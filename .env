# 项目配置使用了dotenv相关的库做支撑，参考如下项目：
# dotenv：https://www.npmjs.com/package/dotenv
# dotenv-defaults：https://www.npmjs.com/package/dotenv-defaults
# dotenv-webpack：https://www.npmjs.com/package/dotenv-webpack

# 模式名称，development|production，构建项目时填production
# NODE_ENV=[模式名称]

# 环境名称，对应package.json中script的--env参数
# APP_ENV=[环境名称]

# 应用部署的基础路径
PUBLIC_PATH=/

# 是否引用外部资源（CDN等）
USE_EXTERNAL_RESOURCES=false

# 是否启用代码映射source-map
USE_SOURCEMAP=true

# 百度地图AccessKey
# 浏览器端
BMAP_WEB_AK=glcuG8GqAjDyWiu9lnGDUISw8QpYuS0G
# 服务端
BMAP_SERVER_AK=5cQRKvynW4aq3cCr4NBsPRxNySV8Hq95

# 代理目标地址
PROXY_TARGET_URL=https://test.wizone.work/std/wibot/webapi
; PROXY_TARGET_URL=http://*************/std/wibot/webapi
