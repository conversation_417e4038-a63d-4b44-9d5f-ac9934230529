# This file is a template, and might need editing before it works on your project.
# see https://docs.gitlab.com/ee/ci/yaml/README.html for all available options

default:
  tags:
    - wibot

variables:
  GIT_CLEAN_FLAGS: -ffdx -e node_modules/ -e dist/

before_script:
  - printf "开始执行脚本，当前路径:" && pwd

after_script:
  - echo "脚本执行完成"

build:
  stage: build
  only:
    refs:
      - std_test
      - std_prod
      - icbc_zj
      - icbc_sz
  script:
    - git log -1
    - echo "开始安装依赖项"
    - npm i
    - echo "安装依赖项完成"
    - echo "开始构建"
    - npm run $CI_COMMIT_REF_NAME;
    - echo "构建完成"
    - ls -l

deploy:pkg:
  stage: deploy
  only:
    refs:
      - std_test
      - std_prod
  environment: $CI_COMMIT_REF_NAME
  script:
    - mv dist web
    - tar zcvf web.tar ./web/
    - sh /data/wiloader/deploy.sh $CI_COMMIT_REF_NAME web ./web.tar

deploy:api:
  stage: deploy
  only:
    refs:
      - std_test
  environment: $CI_COMMIT_REF_NAME
  script:
    - sh /data/front/api.sh web

deploy:icbc:
  stage: deploy
  only:
    refs:
      - icbc_zj
      - icbc_sz
  environment: $CI_COMMIT_REF_NAME
  script:
    - filename="web_$CI_COMMIT_REF_NAME_$(date '+%Y%m%d_%H%M%S')_$CI_COMMIT_BRANCH.tar";
    - cp -r dist web;
    - tar zcvf $filename ./web/;
    - scp $filename "$SERVER":/data;
    - ssh $SERVER "/data/qw_notify.sh /data/$filename file";

notice:
  stage: .post
  only:
    refs:
      - std_test
      - std_prod
      - icbc_zj
      - icbc_sz
  environment: $CI_COMMIT_REF_NAME
  script:
    - if [[ $CI_COMMIT_REF_NAME == icbc_* ]]; then
        ssh $SERVER /data/qw_notify.sh "WEB前端的$CI_COMMIT_REF_NAME包已发，请查收！";
      else
        ssh $SERVER /data/qw_notify.sh "WEB前端$CI_COMMIT_REF_NAME环境更新，以下是最近五条提交日志：\\\\n $(git log --date=format-local:%Y-%m-%d\ %H:%M:%S --pretty=format:'\\> %cd【%h】【%an】：%s \\\\n' -5 | xargs | sed 's/(/\\(/g' | sed 's/)/\\)/g')";
      fi

