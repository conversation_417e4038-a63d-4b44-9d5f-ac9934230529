### ✅ 配置多环境变量

`package.json` 里的 `scripts` 配置 --env 执行不同环境

```javascript
    "dev": "webpack-dev-server --port 12345 --env development",
    "test": "npm run copy && webpack --progress --color --env test",
    "icbc-test": "npm run copy && webpack --progress --color --env icbc-test",
    "production": "npm run copy && webpack --progress --color --env production",
```


---  
### ✅ 搭建启动环境

首先你需要`安装 Node.js`，可以前往官网下载对应安装包，教程url：[https://blog.csdn.net/muzidigbig/article/details/80493880]     
      
  
其次， 你需要在当前项目目录，`执行以下命令`

``` 
 npm install
```

等待安装完成后即可


--- 

此项目为 `React` 项目，官网：[https://react.docschina.org/docs/getting-started.html]
