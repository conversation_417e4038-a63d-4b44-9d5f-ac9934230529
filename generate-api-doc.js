#!/usr/bin/env node

// 引入核心模块与依赖
const fs = require("fs")
const path = require("path")
const babelParser = require("@babel/parser")
const traverse = require("@babel/traverse").default
const glob = require("glob")
// 项目根路径
const projectRoot = process.cwd()
// 路由、api配置文件路径
const routerPath = path.join(projectRoot, "src/components/main/*.jsx")
// 路径别名映射
const aliasMap = {
  "@/": "src/",
  "@comworkbench/": "src/view/comworkbench/",
  "@pharmacy/": "src/view/pharmacy/",
  "@wecom/": "src/view/wecom/",
  "components/": "src/components/",
}
// 忽略扫描 API 的路由路径（如 "@wecom/**/**"）
const ignoreRouteComponentPaths = [
  "@pharmacy/Order/PrescriptionOrder/home",
  "@pharmacy/CustomerManagement/Customer/details",
  "@pharmacy/CustomerManagement/Customer/home",
]
// 忽略扫描 API 的路由路径（如： "/**"）
const ignoreRoutePaths = []
// 忽略输出的 API 路径
const ignoreApiPaths = [
  "/login",
  "/logout",
  "/register",
  "/verifytoken",
  "/comreg",
  "/sysdict/options",
  "/user/getProtectToken",
  "[var:schema]/option",
  "[prop:typeParams.apiUrl]",
  "/wecom/employee/getEmployeeDisableDepartmentIds",
  "/agentSeat/msgVoiceText/isEnable",
]
// 为特定路由页面（如 EmployeeHome）补充的额外接口路径，自动扫描时未能识别的 API 路径
const extraRouteApisMap = {
  "/wecom": [
    "/sysdict/options",
    "/tool/**",
    "/agentSeat/msgVoiceText/isEnable",
  ],
  "/digitalAccountMultiEscrow": [
    "/device/connectToken",
    "/digital_human/login",
    "/device/lock",
  ],
  "/digitalAccountMultiManageEscrow": [
    "/device/connectToken",
    "/digital_human/login",
    "/device/lock",
  ],
}
// REST 操作映射
const restOpMap = {
  getlist: { method: "GET", path: "/page" },
  get: { method: "GET", path: "/get" },
  add: { method: "POST", path: "/add" },
  update: { method: "PUT", path: "/modify" },
  del: { method: "DELETE", path: "/del" },
}

// 根据路径别名和当前目录解析模块的绝对路径
function resolveImportPath(importPath, baseDir) {
  for (const [alias, real] of Object.entries(aliasMap)) {
    if (importPath.startsWith(alias)) {
      return path.resolve(process.cwd(), importPath.replace(alias, real))
    }
  }
  return importPath.startsWith(".") ? path.resolve(baseDir, importPath) : null
}

// 解析模板字符串中的变量表达式，生成所有可能的路径组合
function resolveTemplateLiteral(node, variableMap) {
  if (node.type !== "TemplateLiteral") return null

  let pathCombinations = [""]
  node.quasis.forEach((quasi, index) => {
    const staticPart = quasi.value.cooked || ""
    pathCombinations = pathCombinations.map((path) => path + staticPart)

    if (index < node.expressions.length) {
      const expr = node.expressions[index]
      const exprValues = resolveExpressionValues(expr, variableMap)
      pathCombinations = pathCombinations.flatMap((path) =>
        exprValues.map((value) => path + value)
      )
    }
  })

  return pathCombinations
}

// 递归解析任意表达式 AST 节点的所有可能取值（如变量、运算表达式等）
function resolveExpressionValues(node, variableMap) {
  if (!node) return ["[unknown]"]

  switch (node.type) {
    case "StringLiteral":
      return [node.value]
    case "NumericLiteral":
      return [node.value.toString()]
    case "Identifier":
      const varName = node.name
      const value = variableMap[varName]
      if (value?.type === "ApiUrlValueCollection") {
        return value.values.flatMap((item) =>
          resolveExpressionValues(item.value, variableMap)
        )
      }
      if (value?.type === "StateValueCollection") {
        return value.values.flatMap((item) =>
          resolveExpressionValues(item.apiUrl, variableMap)
        )
      }
      return value
        ? resolveExpressionValues(value, variableMap)
        : [`[var:${varName}]`]
    case "MemberExpression":
    case "OptionalMemberExpression":
      return resolveMemberExpression(node, variableMap)
    case "BinaryExpression":
      return node.operator === "+"
        ? resolveBinaryExpression(node, variableMap)
        : [`[expr:${node.type}]`]
    case "ConditionalExpression":
      return [
        ...resolveExpressionValues(node.consequent, variableMap),
        ...resolveExpressionValues(node.alternate, variableMap),
      ]
    case "TemplateLiteral":
      return resolveTemplateLiteral(node, variableMap)
    case "ObjectExpression":
      return resolveObjectExpression(node, variableMap)
    case "CallExpression":
      return node.callee.name === "restOPCall"
        ? resolveRestOpCall(node, variableMap).map(
            (call) => `${call.url} (${call.method})`
          )
        : [`[expr:${node.type}]`]
    default:
      return [`[expr:${node.type}]`]
  }
}

// 解析对象属性访问表达式（如 this.apiUrl 或 obj.key）
function resolveMemberExpression(node, variableMap) {
  const obj = node.object
  const prop = node.property
  if (obj.type === "ThisExpression" && prop.type === "Identifier") {
    const varName = prop.name
    const value = variableMap[varName]
    return value
      ? resolveExpressionValues(value, variableMap)
      : [`[this.${varName}]`]
  }
  if (obj.type === "Identifier" && prop.type === "Identifier") {
    const objName = obj.name
    const propName = prop.name
    const objValue = variableMap[objName]
    if (objValue) {
      if (objValue.type === "ObjectExpression") {
        const matchedProp = objValue.properties.find(
          (p) => getPropertyKeyName(p) === propName
        )
        if (matchedProp?.value) {
          return resolveExpressionValues(matchedProp.value, variableMap)
        }
      }
      if (
        objValue.type === "StateValueCollection" ||
        objValue.type === "ApiUrlValueCollection"
      ) {
        const allValues = objValue.values
          .map((entry) => {
            if (entry?.type === "ObjectExpression") {
              const matchedProp = entry.properties.find(
                (p) => getPropertyKeyName(p) === propName
              )
              if (matchedProp?.value) {
                return resolveExpressionValues(matchedProp.value, variableMap)
              }
            } else if (entry?.apiUrl && propName === "apiUrl") {
              return resolveExpressionValues(entry.apiUrl, variableMap)
            }
            return null
          })
          .filter(Boolean)
          .flat()
        if (allValues.length > 0) return allValues
      }
    }
    return [`[prop:${objName}.${propName}]`]
  }
  return [`[member:${obj.type}]`]
}

// 解析二元加法表达式（字符串拼接），生成所有可能组合
function resolveBinaryExpression(node, variableMap) {
  const left = resolveExpressionValues(node.left, variableMap)
  const right = resolveExpressionValues(node.right, variableMap)
  return left.flatMap((l) => right.map((r) => l + r))
}

// 解析包含 apiUrl 字段的对象表达式
function resolveObjectExpression(node, variableMap) {
  const apiUrlProp = node.properties.find(
    (prop) => getPropertyKeyName(prop) === "apiUrl"
  )
  return apiUrlProp?.value
    ? resolveExpressionValues(apiUrlProp.value, variableMap)
    : [`[expr:${node.type}]`]
}

// 解析 restOPCall 封装的接口调用，提取最终路径和方法
function resolveRestOpCall(node, variableMap) {
  if (node.arguments.length < 2) return []

  const nameArg = node.arguments[0]
  const opArg = node.arguments[1]

  const nameValues = resolveExpressionValues(nameArg, variableMap)
  const opValues = resolveExpressionValues(opArg, variableMap)

  return nameValues.flatMap((name) =>
    opValues.map((op) => {
      const restInfo = restOpMap[op] || {
        path: `[unknown_op:${op}]`,
        method: "GET",
      }
      const normalizedName = name.toLowerCase()
      return {
        url: normalizedName + restInfo.path,
        method: restInfo.method,
      }
    })
  )
}

// 判断是否是已知的状态设置函数
function isKnownStateSetter(name) {
  const knownSetters = [
    "setTypeParams",
    "typeParams",
    "updateParams",
    "setConfig",
    "updateConfig",
    "setOptions",
    "updateOptions",
  ]
  return (
    knownSetters.includes(name) ||
    name.startsWith("set") ||
    name.startsWith("update")
  )
}

function resolvePathExpression(node, variableMap) {
  return node ? resolveExpressionValues(node, variableMap) : []
}

/**
 * @description: 收集 AST 中所有可能用于生成 API 路径的变量定义，包括
 * - 普通变量声明
 * - 状态管理变量（如 setParams 设置对象）
 * - apiUrl 的 switch 赋值等特殊情况
 * @param {*} ast
 * @return {*}
 */
function collectVariables(ast) {
  const variableMap = {}
  const stateAssignments = {}
  const apiUrlAssignments = {}

  function collectSwitchApiUrls(switchNode) {
    return switchNode.cases.flatMap((switchCase) => {
      const caseValue = switchCase.test
        ? switchCase.test.type === "StringLiteral"
          ? switchCase.test.value
          : "[unknown]"
        : "default"
      return switchCase.consequent.flatMap((stmt) =>
        stmt.type === "ExpressionStatement" &&
        stmt.expression.type === "AssignmentExpression" &&
        stmt.expression.left.type === "Identifier" &&
        stmt.expression.left.name === "apiUrl"
          ? [
              {
                value: stmt.expression.right,
                caseValue: caseValue,
              },
            ]
          : []
      )
    })
  }

  traverse(ast, {
    VariableDeclarator(path) {
      const { node } = path
      if (node.id.type === "Identifier" && node.init) {
        variableMap[node.id.name] = node.init
        if (node.id.name === "apiUrl") {
          apiUrlAssignments.apiUrl = apiUrlAssignments.apiUrl || []
          apiUrlAssignments.apiUrl.push({
            value: node.init,
            context: "initialization",
          })
        }
        if (node.init.type === "ObjectExpression") {
          const hasApiUrl = node.init.properties.some(
            (prop) => getPropertyKeyName(prop) === "apiUrl"
          )
          if (hasApiUrl) {
            stateAssignments[node.id.name] =
              stateAssignments[node.id.name] || []
            stateAssignments[node.id.name].push(node.init)
          }
        }
      }
      if (node.id.type === "ObjectPattern") {
        node.id.properties.forEach((prop) => {
          if (
            prop.type === "ObjectProperty" &&
            prop.key.type === "Identifier" &&
            prop.value.type === "AssignmentPattern"
          ) {
            const varName = prop.key.name
            const defaultValue = prop.value.right
            variableMap[varName] = defaultValue
          }
        })
      }
    },
    AssignmentExpression(path) {
      const { node } = path
      if (node.left && node.right) {
        const varName = getVariableName(node.left)
        if (varName) {
          variableMap[varName] = node.right
          if (varName === "apiUrl") {
            apiUrlAssignments.apiUrl = apiUrlAssignments.apiUrl || []
            apiUrlAssignments.apiUrl.push({
              value: node.right,
              context: "assignment",
            })
          }
          if (node.right.type === "ObjectExpression") {
            const hasApiUrl = node.right.properties.some(
              (prop) => getPropertyKeyName(prop) === "apiUrl"
            )
            if (hasApiUrl) {
              stateAssignments[varName] = stateAssignments[varName] || []
              stateAssignments[varName].push(node.right)
            }
          }
        }
      }
    },
    CallExpression(path) {
      const { node } = path
      if (
        node.callee.type === "Identifier" &&
        node.arguments.length > 0 &&
        node.arguments[0].type === "ObjectExpression"
      ) {
        const objectExpr = node.arguments[0]
        const hasApiUrl = objectExpr.properties.some(
          (prop) => getPropertyKeyName(prop) === "apiUrl"
        )
        if (hasApiUrl) {
          const stateVarName = getStateVarName(node.callee.name, objectExpr)
          stateAssignments[stateVarName] = stateAssignments[stateVarName] || []
          stateAssignments[stateVarName].push(objectExpr)
        }
      }
    },
    SwitchStatement(path) {
      const { node } = path
      const apiUrlValues = collectSwitchApiUrls(node)
      if (apiUrlValues.length > 0) {
        apiUrlAssignments.apiUrl = apiUrlAssignments.apiUrl || []
        apiUrlValues.forEach((valueInfo) => {
          apiUrlAssignments.apiUrl.push({
            value: valueInfo.value,
            context: `switch_case_${valueInfo.caseValue}`,
          })
        })
      }
    },
  })

  Object.keys(stateAssignments).forEach((stateName) => {
    variableMap[stateName] = {
      type: "StateValueCollection",
      values: stateAssignments[stateName],
    }
  })

  Object.keys(apiUrlAssignments).forEach((varName) => {
    variableMap[varName] = {
      type: "ApiUrlValueCollection",
      values: apiUrlAssignments[varName],
    }
  })

  return variableMap
}

// 获取赋值语句左侧的变量名（支持直接变量和 this.xxx 成员形式）
function getVariableName(left) {
  if (left.type === "Identifier") {
    return left.name
  }
  if (
    left.type === "MemberExpression" &&
    left.object.type === "ThisExpression" &&
    left.property.type === "Identifier"
  ) {
    return left.property.name
  }
  return null
}

/**
 * @description: 根据状态设置函数名和对象表达式推断出状态变量名称
 * - 常用于识别如 setTypeParams、setOptionsState 等设置状态的调用
 * @param {*} calleeName
 * @param {*} objectExpr
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/07/23 10:12
 */
function getStateVarName(calleeName, objectExpr) {
  if (calleeName.startsWith("set")) {
    return calleeName.replace(/^set/, "")
  }
  if (calleeName.endsWith("State")) {
    return calleeName.replace(/State$/, "")
  }
  if (calleeName.endsWith("Params")) {
    return calleeName.replace(/Params$/, "")
  }
  return `state_${objectExpr.start}`
}

// 安全获取属性键名
function getPropertyKeyName(prop) {
  if (prop.shorthand) {
    return prop.value.type === "Identifier" ? prop.value.name : "[shorthand]"
  }
  if (prop.method) {
    return prop.key.type === "Identifier"
      ? prop.key.name
      : prop.key.type === "StringLiteral"
      ? prop.key.value
      : "[method]"
  }
  if (prop.computed) {
    return prop.key.type === "StringLiteral"
      ? prop.key.value
      : prop.key.type === "Identifier"
      ? prop.key.name
      : "[computed]"
  }
  return prop.key && prop.key.type === "Identifier"
    ? prop.key.name
    : prop.key && prop.key.type === "StringLiteral"
    ? prop.key.value
    : "[unknown]"
}

// 扫描指定文件中出现的 apiCall、restOPCall 或 apiUrl 设置，提取接口调用信息
// 递归扫描引入的模块和相关文件。
function scanApiCallsFromFile(filePath, visited = new Set()) {
  if (!filePath || !fs.existsSync(filePath) || visited.has(filePath)) return []
  visited.add(filePath)

  const apis = []
  const content = fs.readFileSync(filePath, "utf-8")
  const ast = babelParser.parse(content, {
    sourceType: "module",
    plugins: ["jsx", "decorators-legacy", "classProperties", "dynamicImport"],
  })
  const variableMap = collectVariables(ast)

  traverse(ast, {
    CallExpression(path) {
      const { node } = path
      if (node.callee.name === "apiCall") {
        const urlArg = node.arguments[0]
        const method =
          node.arguments.length > 1 &&
          node.arguments[1].type === "StringLiteral"
            ? node.arguments[1].value
            : "GET"
        const urlValues = resolvePathExpression(urlArg, variableMap)
        urlValues.forEach((url) => {
          const cleanUrl = url.replace(/^\[.*?\] /, "")
          apis.push({ url: cleanUrl, method })
        })
      } else if (node.callee.name === "restOPCall") {
        const restCalls = resolveRestOpCall(node, variableMap)
        apis.push(...restCalls)
      } else if (
        node.callee.type === "Identifier" &&
        isKnownStateSetter(node.callee.name) &&
        node.arguments.length > 0
      ) {
        const arg = node.arguments[0]
        if (arg.type === "ObjectExpression") {
          const apiUrlProp = arg.properties.find(
            (prop) => getPropertyKeyName(prop) === "apiUrl"
          )
          if (apiUrlProp && apiUrlProp.value) {
            const urlValues = resolvePathExpression(
              apiUrlProp.value,
              variableMap
            )
            urlValues.forEach((url) => {
              apis.push({ url, method: "GET" })
            })
          }
        }
      }
    },
    ObjectProperty(path) {
      const { key, value } = path.node
      if (
        key.type === "Identifier" &&
        /Url$/.test(key.name) &&
        value.type === "StringLiteral"
      ) {
        apis.push({ url: value.value, method: "GET" })
      }
    },
    AssignmentExpression(path) {
      const { left, right } = path.node
      if (
        left.type === "Identifier" &&
        /Url$/.test(left.name) &&
        right.type === "StringLiteral"
      ) {
        apis.push({ url: right.value, method: "GET" })
      }
    },
  })

  const dir = path.dirname(filePath)
  const apiCandidates = ["api.js", "api.ts", "api.jsx", "api.tsx"].map((n) =>
    path.join(dir, n)
  )
  for (const f of apiCandidates) {
    if (fs.existsSync(f)) {
      apis.push(...scanApiCallsFromFile(f, visited))
      break
    }
  }

  const importMap = getImportMap(ast, dir)
  for (const absPath of importMap.values()) {
    const candidates = [
      absPath + ".jsx",
      absPath + ".tsx",
      absPath + ".js",
      absPath + ".ts",
      path.join(absPath, "index.jsx"),
      path.join(absPath, "index.tsx"),
      path.join(absPath, "index.js"),
      path.join(absPath, "index.ts"),
      absPath,
    ]
    for (const f of candidates) {
      if (fs.existsSync(f)) {
        const stat = fs.lstatSync(f)
        if (stat.isDirectory()) {
          apis.push(...scanApiCallsFromDir(f, visited))
        } else {
          apis.push(...scanApiCallsFromFile(f, visited))
        }
        break
      }
    }
  }

  return filterEmptyPaths(apis)
}

// 提取模块文件中的所有 import 声明并解析出其绝对路径映射
function getImportMap(ast, dir) {
  const importMap = new Map()
  traverse(ast, {
    ImportDeclaration(p) {
      const local = p.node.specifiers?.[0]?.local?.name
      const value = p.node.source.value
      if (local && value) {
        const absPath = resolveImportPath(value, dir)
        if (absPath) importMap.set(local, absPath)
      }
    },
  })
  return importMap
}

// 递归扫描目录下的所有 JS/TS 文件，收集 API 调用信息
function scanApiCallsFromDir(dirPath, visited = new Set()) {
  if (!fs.existsSync(dirPath)) return []
  return fs.readdirSync(dirPath, { withFileTypes: true }).flatMap((entry) => {
    const full = path.join(dirPath, entry.name)
    return entry.isDirectory()
      ? scanApiCallsFromDir(full, visited)
      : /\.(js|jsx|ts|tsx)$/.test(entry.name)
      ? scanApiCallsFromFile(full, visited)
      : []
  })
}

// 为API路径添加/web前缀
function addWebPrefix(apiPath) {
  return apiPath.startsWith("/web/")
    ? apiPath
    : apiPath.startsWith("/")
    ? `/web${apiPath}`
    : `/web/${apiPath}`
}

// 移除路径中的参数占位符和查询参数
function removePathParams(apiPath) {
  const pathWithoutQuery = apiPath.split("?")[0]
  return pathWithoutQuery.startsWith("/web/")
    ? pathWithoutQuery.replace(/\/\[[^\]]+\]/g, "")
    : pathWithoutQuery.replace(/\[[^\]]+\]/g, "")
}

// 过滤空路径
function filterEmptyPaths(apis) {
  if (!Array.isArray(apis)) return []
  return apis.filter((api) => {
    const cleanUrl = removePathParams(api.url)
    return cleanUrl.trim() !== "" && cleanUrl !== "/"
  })
}

// 获取路径的一级路径
function getFirstLevelPath(routePath) {
  if (routePath === "/" || routePath === "") return "/"
  const parts = routePath.split("/").filter((part) => part !== "")
  return parts.length === 0 ? "/" : `/${parts[0]}`
}

// 将接口按 source 字段进行分组（通常用于按组件或文件归类）
function groupApisBySource(apis) {
  if (!Array.isArray(apis)) return {}
  const apisBySource = {}
  apis.forEach((api) => {
    apisBySource[api.source || "unknown"] =
      apisBySource[api.source || "unknown"] || []
    apisBySource[api.source || "unknown"].push(api)
  })
  return apisBySource
}

// 过滤掉在 ignoreApiPaths 列表中定义的 API 路径（忽略路径参数）
function filterIgnoredApis(apis, normalize) {
  if (!Array.isArray(apis)) return []
  return apis.filter((api) => {
    const urlNorm = normalize(api.url)
    return !ignoreApiPaths.some((black) => normalize(black) === urlNorm)
  })
}

// 对 API 调用进行去重（基于 method + url），并按字典序排序
function getUniqueApis(apis) {
  return Array.from(new Set(apis.map((a) => `${a.method} ${a.url}`))).sort()
}

/**
 * @description: 根据输入参数提取所有符合条件的文件路径（支持目录、通配符等）
 * - 仅处理 .js/.ts/.jsx/.tsx 文件
 * @param {*} inputArgs
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/07/23 10:12
 */
function getInputFiles(inputArgs) {
  const inputFiles = []
  inputArgs.forEach((arg) => {
    const absPath = path.resolve(arg)
    if (fs.existsSync(absPath) && fs.lstatSync(absPath).isDirectory()) {
      const entries = fs.readdirSync(absPath)
      entries.forEach((e) => {
        if (
          e.endsWith(".jsx") ||
          e.endsWith(".js") ||
          e.endsWith(".ts") ||
          e.endsWith(".tsx")
        ) {
          inputFiles.push(path.join(absPath, e))
        }
      })
    } else {
      const matched = glob.sync(arg)
      inputFiles.push(...matched.map((f) => path.resolve(f)))
    }
  })
  return inputFiles
}

/**
 * @description: 从路由配置文件 AST 中提取所有路由 path 和组件路径
 * - 仅识别 routeModules 变量的配置结构
 * @param {*} ast
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/07/23 10:12
 */
function extractRoutes(ast) {
  const routes = []
  traverse(ast, {
    VariableDeclarator(p) {
      const node = p.node
      if (
        node.id.name === "routeModules" &&
        node.init?.type === "ArrayExpression"
      ) {
        node.init.elements.forEach((el) => {
          if (el?.type === "ObjectExpression") {
            const route = {}
            el.properties.forEach((prop) => {
              const key = getPropertyKeyName(prop)
              if (key === "path") {
                let pathValue = prop.value.value
                // 若路径包含 /classHour/，将其替换为 /courseManage/
                if (pathValue.includes("/classHour/")) {
                  pathValue = pathValue.replace("/classHour/", "/courseManage/")
                }
                route.path = pathValue
              }
              if (key === "component") {
                if (
                  prop.value.type === "CallExpression" &&
                  prop.value.callee.name === "require"
                ) {
                  route.component = prop.value.arguments[0].value
                } else if (
                  prop.value.type === "CallExpression" &&
                  prop.value.callee.name === "lazy"
                ) {
                  const fn = prop.value.arguments?.[0]
                  if (
                    fn?.type === "ArrowFunctionExpression" &&
                    fn.body?.type === "CallExpression"
                  ) {
                    const importArg = fn.body.arguments?.[0]
                    if (importArg?.type === "StringLiteral") {
                      route.component = importArg.value
                    }
                  }
                }
              }
              if (key === "meta") {
                const metaInfo = {}
                prop.value.properties.forEach((metaProp) => {
                  const metaKey = getPropertyKeyName(metaProp)
                  if (
                    metaProp.value.type === "StringLiteral" ||
                    metaProp.value.type === "NumericLiteral" ||
                    metaProp.value.type === "BooleanLiteral"
                  ) {
                    metaInfo[metaKey] = metaProp.value.value
                  } else if (metaProp.value.type === "ArrayExpression") {
                    metaInfo[metaKey] = metaProp.value.elements.map((el) =>
                      el.type === "StringLiteral" ? el.value : "[non-string]"
                    )
                  } else {
                    metaInfo[metaKey] = "[unsupported]"
                  }
                })
                route.meta = metaInfo
              }
            })
            if (route.path && route.component && route.meta) routes.push(route)
          }
        })
      }
    },
  })
  return routes
}

// 生成Markdown文档
function generateMarkdown(docsByFile) {
  let md = "# 页面 API 接口文档\n\n"
  const normalize = (str) => str.replace(/^\//, "")
  const firstLevelApis = {}

  for (const fileName of Object.keys(docsByFile)) {
    const routes = docsByFile[fileName]
    for (const routePath of Object.keys(routes)) {
      const apis = routes[routePath]
      if (apis.length === 0) continue
      const firstLevel = routePath
      firstLevelApis[firstLevel] = firstLevelApis[firstLevel] || []
      firstLevelApis[firstLevel].push(...apis)
    }
  }

  const sortedFirstLevels = ["/wecom", ...Object.keys(firstLevelApis).sort()]
  for (const firstLevel of sortedFirstLevels) {
    md += `## 路由：\`${firstLevel}\`\n\n`

    if (firstLevel === "/wecom") {
      const apis = extraRouteApisMap[firstLevel]
      apis.forEach((item) => {
        md += `- \`GET ${item}\`\n`
      })
      md += "\n"
      continue
    }

    const allApis = firstLevelApis[firstLevel]
    const apisBySource = groupApisBySource(allApis)

    for (const source of Object.keys(apisBySource)) {
      const filtered = filterIgnoredApis(apisBySource[source], normalize)
      const unique = getUniqueApis(filtered)

      if (unique.length === 0) {
        md += "- 无扫描到接口调用\n\n"
      } else {
        unique.forEach((item) => {
          md += `- \`${item}\`\n`
        })

        if (extraRouteApisMap[firstLevel]) {
          const apis = extraRouteApisMap[firstLevel]
          apis.forEach((item) => {
            md += `- \`GET ${item}\`\n`
          })
        }
        md += "\n"
      }
    }
  }

  return md
}

// 生成CSV文档
function generateCsv(docsByFile) {
  const csvRows = []
  const normalize = (str) => str.replace(/^\//, "")
  const firstLevelApis = {}

  for (const fileName of Object.keys(docsByFile)) {
    const routes = docsByFile[fileName]
    for (const routePath of Object.keys(routes)) {
      const apis = routes[routePath]
      if (apis.length === 0) continue
      const firstLevel = getFirstLevelPath(routePath)
      firstLevelApis[firstLevel] = firstLevelApis[firstLevel] || []
      firstLevelApis[firstLevel].push(...apis)
    }
  }

  const sortedFirstLevels = ["/wecom", ...Object.keys(firstLevelApis).sort()]
  for (const firstLevel of sortedFirstLevels) {
    if (firstLevel === "/wecom") {
      const apis = extraRouteApisMap[firstLevel].map((item) =>
        addWebPrefix(item)
      )
      csvRows.push(`"${firstLevel.replace(/^\/+/, "")}","${apis.join(",")}"`)
      continue
    }

    const allApis = firstLevelApis[firstLevel]
    const filtered = filterIgnoredApis(allApis, normalize)
    const prefixedApis = filtered.map((api) => ({
      ...api,
      url: addWebPrefix(api.url),
    }))
    const cleanApis = prefixedApis.map((api) => ({
      ...api,
      url: removePathParams(api.url),
    }))
    const nonEmptyApis = filterEmptyPaths(cleanApis)
    let unique = Array.from(
      new Set(
        nonEmptyApis
          .filter((a) => !/\[(var|prop|this|expr|member):/.test(a.url))
          .map((a) => a.url)
      )
    ).sort()
    if (extraRouteApisMap[firstLevel]) {
      const apis = extraRouteApisMap[firstLevel].map((item) =>
        addWebPrefix(item)
      )
      unique = [...unique, ...apis]
    }
    const joinedApis = unique.join(",")
    if (joinedApis) {
      csvRows.push(`"${firstLevel.replace(/^\/+/, "")}","${joinedApis}"`)
    }
  }

  return csvRows.join("\n")
}

// 主程序入口
function main() {
  const formatArg = process.argv.find((arg) => arg.startsWith("--format="))
  const format = formatArg ? formatArg.split("=")[1].toLowerCase() : "md"
  if (!["md", "csv"].includes(format)) {
    console.error("❌ 不支持的格式，可用格式：md, csv")
    process.exit(1)
  }

  let inputArgs = process.argv.slice(2).filter((arg) => !arg.startsWith("--"))
  if (inputArgs.length === 0) {
    inputArgs = [routerPath]
  }

  const inputFiles = getInputFiles(inputArgs)
  if (inputFiles.length === 0) {
    console.error("❌ 没有找到任何 JavaScript 或 TypeScript 文件")
    process.exit(1)
  }

  const docsByFile = {}
  for (const filePath of inputFiles) {
    const fileName = path.basename(filePath)
    docsByFile[fileName] = {}
    const content = fs.readFileSync(filePath, "utf-8")
    const ast = babelParser.parse(content, {
      sourceType: "module",
      plugins: ["jsx", "decorators-legacy", "classProperties", "dynamicImport"],
    })
    const routes = extractRoutes(ast)

    console.log(`📄 ${fileName} 中提取路由：${routes.length} 条`)
    for (const route of routes) {
      if (
        ignoreRouteComponentPaths.includes(route.component) ||
        ignoreRoutePaths.includes(route.path)
      ) {
        console.log(`🔵 忽略组件: ${route.component} (路由: ${route.path})`)
        docsByFile[fileName][route.path] = []
        continue
      }
      const compPath = resolveImportPath(route.component, process.cwd())
      if (!compPath) {
        console.warn(`⚠ 无法解析组件路径: ${route.component}`)
        docsByFile[fileName][route.path] = []
        continue
      }
      const candidates = [
        compPath + ".jsx",
        compPath + ".tsx",
        compPath + ".js",
        compPath + ".ts",
        path.join(compPath, "index.jsx"),
        path.join(compPath, "index.tsx"),
        path.join(compPath, "index.js"),
        path.join(compPath, "index.ts"),
        compPath,
      ]
      let apis = []
      for (const f of candidates) {
        if (fs.existsSync(f)) {
          const stat = fs.lstatSync(f)
          if (stat.isDirectory()) {
            apis = scanApiCallsFromDir(f)
          } else {
            apis = scanApiCallsFromFile(f)
          }
          break
        }
      }
      docsByFile[fileName][route.path] = apis
    }
  }

  const outputFilename = format === "md" ? "api-doc-web.md" : "api-doc-web.csv"
  const content =
    format === "md" ? generateMarkdown(docsByFile) : generateCsv(docsByFile)

  fs.writeFileSync(outputFilename, content, "utf-8")
  console.log(`✅ 已生成：${outputFilename}`)
}

// 运行主程序
main()
