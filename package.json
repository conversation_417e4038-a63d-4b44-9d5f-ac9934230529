{"name": "weebot_web_front", "version": "0.1.0", "private": true, "scripts": {"dev": "webpack-dev-server --host localhost --port 12345 --env development", "dev:tongqi": "webpack-dev-server --host localhost --port 8880 --env development_tongqi", "tongqi": "npm run copy && webpack --progress --color --env tongqi", "copy": "copyfiles -f ./src/favicon.ico ./dist && copyfiles -f ./src/images/* ./dist/images", "prepare": "husky install", "std_test:dev": "npm run copy && webpack --progress --color --env std_test && rm -rf ./web ./web.tar && mv dist web && tar -czvf ./web.tar ./web", "std_test": "npm run copy && webpack --progress --color --env std_test && npm run hash", "std_prod": "npm run copy && webpack --progress --color --env std_prod && npm run hash", "icbc_sz": "npm run copy && webpack --progress --color --env icbc_sz && npm run hash", "icbc_zj": "npm run copy && webpack --progress --color --env icbc_zj && npm run hash", "generate_api_doc_md": "node generate-api-doc.js --format=md", "generate_api_doc_csv": "node generate-api-doc.js --format=csv", "hash": "md5sum ./dist/webpack-hash-manifest.json"}, "dependencies": {"@ant-design/icons": "^4.7.0", "@ant-design/plots": "^1.2.5", "@babel/polyfill": "7.0.0-beta.51", "@emoji-mart/data": "^1.0.5", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "^4.19.5", "array-move": "^3.0.1", "axios": "^0.18.0", "benz-amr-recorder": "^1.1.5", "clipboard": "^2.0.11", "emoji-mart": "5.2.1", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "fr-generator": "^2.8.2", "history": "^4.7.2", "immutability-helper": "^3.1.1", "js-cookie": "^2.2.0", "jsonp": "^0.2.1", "moment": "^2.29.1", "net": "^1.0.2", "qrcode": "^1.4.4", "react": "^17.0.2", "react-bmapgl": "^0.2.13", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.0.4", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0", "react-dom": "^17.0.2", "react-draggable": "^4.4.5", "react-dragger-sort": "^5.0.11", "react-error-boundary": "^4.0.13", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-router-cache-route": "^1.12.11", "react-router-dom": "^4.2.2", "react-sortable-hoc": "^2.0.0", "reflux": "^6.4.1", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.0.0-beta.51", "@babel/parser": "^7.28.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-do-expressions": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.0.0", "@babel/plugin-proposal-pipeline-operator": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-transform-react-jsx-source": "^7.0.0-beta.51", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/preset-env": "^7.0.0-beta.51", "@babel/preset-react": "^7.0.0-beta.51", "@babel/traverse": "^7.28.0", "autoprefixer": "^8.6.3", "babel-eslint": "^10.1.0", "babel-loader": "^8.0.0", "babel-plugin-import": "^1.6.0", "clean-webpack-plugin": "^0.1.17", "copy-webpack-plugin": "^5.0.0", "copyfiles": "^2.3.0", "css-loader": "^0.28.7", "dotenv-defaults": "^5.0.2", "dotenv-webpack": "^8.0.1", "eslint": "^7.32.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-react": "^7.26.1", "file-loader": "^1.1.5", "fs-extra": "^6.0.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "husky": "^7.0.2", "less": "^3.11.3", "less-loader": "^5.0.0", "lint-staged": "^11.2.1", "markdown-loader": "^5.1.0", "moment-locales-webpack-plugin": "^1.2.0", "postcss-flexbugs-fixes": "^3.3.1", "postcss-loader": "^2.1.5", "resolve": "^1.8.1", "style-loader": "^0.19.0", "text-loader": "0.0.1", "thread-loader": "^3.0.4", "url-loader": "^0.6.2", "webpack": "^4.12.0", "webpack-bundle-analyzer": "^4.6.1", "webpack-cli": "^3.1.1", "webpack-dev-server": "^3.1.4", "webpack-hash-manifest-plugin": "^1.0.0"}, "main": "webpack.config.js", "author": "", "license": "ISC", "description": "", "lint-staged": {"src/**/*.{scss,css,less}": ["git add"], "src/**/*.{js,vue,jsx}": ["eslint --fix", "git add"]}}