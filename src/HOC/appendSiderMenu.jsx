//依赖类
import React from 'react';
import AppStore from 'stores/AppStore';
import AppActions from 'actions/AppActions';
import { Layout } from 'antd';


//组件类
import ModuleMenu from 'components/base/ModuleMenu';
import Breadcrumb from 'components/base/Breadcrumb';
import { versionFnMap } from "config";

const { Content, Sider } = Layout;

export default function (WrappedComponent) {
  class appendSiderMenu extends React.Component {
    constructor(props) {
      super(props);
      this.stores = [AppStore];
      this.state = {
        breadCrumb: [],
      }
    }

    render () {
      const { breadCrumb } = this.state;
      const { collapsed, User } = this.stores[0]?.state;

      if (this.props.menuList?.children?.length) {
        return (
          <Layout>
            <Sider
              breakpoint="xl"
              trigger={null}
              collapsible
              onCollapse={(collapsed, type) => {
                AppActions.setCollapsed(collapsed);
              }}
              collapsed={collapsed}
              width={210}
              collapsedWidth={64}
            >
              <ModuleMenu
                collapsed={collapsed}
                onRouteChanges={(breadCrumb) => {
                  if (!versionFnMap.system_ui().system_breadcrumb_wecomFlag) {
                    breadCrumb = breadCrumb.filter(item => item.path != 'wecom')
                  }
                  this.setState({ breadCrumb })
                }}
                menuList={this.props.menuList}
                {...this.props}
              />
            </Sider>

            <Content>
              <Breadcrumb pathSnippets={breadCrumb} />
              <WrappedComponent {...this.props} />
            </Content>
          </Layout>
        )
      } else {
        return (
          <Content>
            <WrappedComponent {...this.props} />
          </Content>
        )
      }
    }
  }

  //这里是为了在 React Developer Tools 中更清楚的展示高阶组件的名称
  //https://reactjs.org/docs/higher-order-components.html#convention-wrap-the-display-name-for-easy-debugging
  function getDisplayName (WrappedComponent) {
    return WrappedComponent.displayName || WrappedComponent.name || 'Component';
  }

  appendSiderMenu.displayName = `appendSiderMenu(${getDisplayName(WrappedComponent)})`;
  return appendSiderMenu;
}
