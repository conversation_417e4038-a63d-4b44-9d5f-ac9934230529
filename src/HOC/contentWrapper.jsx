// 依赖类
import React from 'react';
import Reflux from 'reflux';

// 数据流
import AppStore from 'stores/AppStore';
import AppActions from 'actions/AppActions';

// 组件类
import ErrorBoundary from 'components/base/ErrorBoundary';

export default function (WrappedComponent) {

  class contentWrapper extends Reflux.Component {
    constructor (props) {
      super(props);
      this.stores = [AppStore];
      this.state = {
        errmsg: undefined
      };
    }

    componentWillUnmount () {
      super.componentWillUnmount();
      AppActions.clearError();
    }

    render () {
      const { errmsg } = this.state;
      return (
        <div className="content-card" key={this.state.User?.cid}>
          {
            errmsg
              ? <div className="errorHint">{errmsg}</div>
              : <ErrorBoundary>
                <WrappedComponent {...this.props} />
              </ErrorBoundary>
          }
        </div>
      );
    }

  }


  // 这里是为了在 React Developer Tools 中更清楚的展示高阶组件的名称
  // https://reactjs.org/docs/higher-order-components.html#convention-wrap-the-display-name-for-easy-debugging
  function getDisplayName (WrappedComponent) {
    return WrappedComponent.displayName || WrappedComponent.name || 'Component';
  }

  contentWrapper.displayName = `contentWrapper(${getDisplayName(WrappedComponent)})`;

  return contentWrapper;
}
