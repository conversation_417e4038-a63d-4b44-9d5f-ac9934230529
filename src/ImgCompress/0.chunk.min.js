webpackJsonphtml5ImgCompress([0],{3:function(t,e,a){var r,i;!function(){function a(t){var e=t.naturalWidth,a=t.naturalHeight;if(e*a>1048576){var r=document.createElement("canvas");r.width=r.height=1;var i=r.getContext("2d");return i.drawImage(t,-e+1,0),0===i.getImageData(0,0,1,1).data[3]}return!1}function n(t,e,a){var r=document.createElement("canvas");r.width=1,r.height=a;var i=r.getContext("2d");i.drawImage(t,0,0);for(var n=i.getImageData(0,0,1,a).data,o=0,s=a,c=a;c>o;){var h=n[4*(c-1)+3];0===h?s=c:o=c,c=s+o>>1}var d=c/a;return 0===d?1:d}function o(t,e,a){var r=document.createElement("canvas");return s(t,r,e,a),r.toDataURL("image/jpeg",e.quality||.8)}function s(t,e,r,i){var o=t.naturalWidth,s=t.naturalHeight;if(o+s){var h=r.width,d=r.height,l=e.getContext("2d");l.save(),c(e,l,h,d,r.orientation);var g=a(t);g&&(o/=2,s/=2);var u=1024,m=document.createElement("canvas");m.width=m.height=u;for(var v=m.getContext("2d"),w=i?n(t,o,s):1,b=Math.ceil(u*h/o),f=Math.ceil(u*d/s/w),L=0,I=0;L<s;){for(var p=0,R=0;p<o;)v.clearRect(0,0,u,u),v.drawImage(t,-p,-L),l.drawImage(m,0,0,u,u,R,I,b,f),p+=u,R+=b;L+=u,I+=f}l.restore(),m=v=null}}function c(t,e,a,r,i){switch(i){case 5:case 6:case 7:case 8:t.width=r,t.height=a;break;default:t.width=a,t.height=r}switch(i){case 2:e.translate(a,0),e.scale(-1,1);break;case 3:e.translate(a,r),e.rotate(Math.PI);break;case 4:e.translate(0,r),e.scale(1,-1);break;case 5:e.rotate(.5*Math.PI),e.scale(1,-1);break;case 6:e.rotate(.5*Math.PI),e.translate(0,-r);break;case 7:e.rotate(.5*Math.PI),e.translate(a,-r),e.scale(-1,1);break;case 8:e.rotate(-.5*Math.PI),e.translate(-a,0)}}function h(t){if(window.Blob&&t instanceof Blob){if(!d)throw Error("No createObjectURL function found to create blob url");var e=new Image;e.src=d.createObjectURL(t),this.blob=t,t=e}if(!t.naturalWidth&&!t.naturalHeight){var a=this;t.onload=t.onerror=function(){var t=a.imageLoadListeners;if(t){a.imageLoadListeners=null;for(var e=0,r=t.length;e<r;e++)t[e]()}},this.imageLoadListeners=[]}this.srcImage=t}var d=window.URL&&window.URL.createObjectURL?window.URL:window.webkitURL&&window.webkitURL.createObjectURL?window.webkitURL:null;h.prototype.render=function(t,e,a){if(this.imageLoadListeners){var r=this;return void this.imageLoadListeners.push(function(){r.render(t,e,a)})}e=e||{};var i=this.srcImage.naturalWidth,n=this.srcImage.naturalHeight,c=e.width,h=e.height,l=e.maxWidth,g=e.maxHeight,u=!this.blob||"image/jpeg"===this.blob.type;c&&!h?h=n*c/i<<0:h&&!c?c=i*h/n<<0:(c=i,h=n),l&&c>l&&(c=l,h=n*c/i<<0),g&&h>g&&(h=g,c=i*h/n<<0);var m={width:c,height:h};for(var v in e)m[v]=e[v];var w=t.tagName.toLowerCase();"img"===w?t.src=o(this.srcImage,m,u):"canvas"===w&&s(this.srcImage,t,m,u),"function"==typeof this.onrender&&this.onrender(t),a&&a(),this.blob&&(this.blob=null,d.revokeObjectURL(this.srcImage.src))},r=[],i=function(){return h}.apply(e,r),!(void 0!==i&&(t.exports=i))}()}});