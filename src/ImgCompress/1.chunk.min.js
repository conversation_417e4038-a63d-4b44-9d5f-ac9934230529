webpackJsonphtml5ImgCompress([1],{2:function(r,a,n){function o(r){function a(r){for(var a=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var o=M((a[n]*r+50)/100);o<1?o=1:o>255&&(o=255),b[G[n]]=o}for(var e=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],t=0;t<64;t++){var f=M((e[t]*r+50)/100);f<1?f=1:f>255&&(f=255),j[G[t]]=f}for(var v=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],i=0,c=0;c<8;c++)for(var u=0;u<8;u++)E[i]=1/(b[G[i]]*v[c]*v[u]*8),I[i]=1/(j[G[i]]*v[c]*v[u]*8),i++}function n(r,a){for(var n=0,o=0,e=new Array,t=1;t<=16;t++){for(var f=1;f<=r[t];f++)e[a[o]]=[],e[a[o]][0]=n,e[a[o]][1]=t,o++,n++;n*=2}return e}function o(){d=n(H,K),p=n(P,R),D=n(L,O),T=n(V,W)}function e(){for(var r=1,a=2,n=1;n<=15;n++){for(var o=r;o<a;o++)k[32767+o]=n,_[32767+o]=[],_[32767+o][1]=n,_[32767+o][0]=o;for(var e=-(a-1);e<=-r;e++)k[32767+e]=n,_[32767+e]=[],_[32767+e][1]=n,_[32767+e][0]=a-1+e;r<<=1,a<<=1}}function t(){for(var r=0;r<256;r++)F[r]=19595*r,F[r+256>>0]=38470*r,F[r+512>>0]=7471*r+32768,F[r+768>>0]=-11059*r,F[r+1024>>0]=-21709*r,F[r+1280>>0]=32768*r+8421375,F[r+1536>>0]=-27439*r,F[r+1792>>0]=-5329*r}function f(r){for(var a=r[0],n=r[1]-1;n>=0;)a&1<<n&&(N|=1<<Q),n--,Q--,Q<0&&(255==N?(v(255),v(0)):v(N),Q=7,N=0)}function v(r){J.push(B[r])}function i(r){v(r>>8&255),v(255&r)}function c(r,a){var n,o,e,t,f,v,i,c,u,w=0;const s=8,y=64;for(u=0;u<s;++u){n=r[w],o=r[w+1],e=r[w+2],t=r[w+3],f=r[w+4],v=r[w+5],i=r[w+6],c=r[w+7];var g=n+c,h=n-c,l=o+i,A=o-i,m=e+v,d=e-v,p=t+f,D=t-f,T=g+p,C=g-p,M=l+m,b=l-m;r[w]=T+M,r[w+4]=T-M;var j=.707106781*(b+C);r[w+2]=C+j,r[w+6]=C-j,T=D+d,M=d+A,b=A+h;var E=.382683433*(T-b),I=.5411961*T+E,_=1.306562965*b+E,k=.707106781*M,z=h+k,J=h-k;r[w+5]=J+I,r[w+3]=J-I,r[w+1]=z+_,r[w+7]=z-_,w+=8}for(w=0,u=0;u<s;++u){n=r[w],o=r[w+8],e=r[w+16],t=r[w+24],f=r[w+32],v=r[w+40],i=r[w+48],c=r[w+56];var N=n+c,Q=n-c,S=o+i,U=o-i,q=e+v,B=e-v,F=t+f,G=t-f,H=N+F,K=N-F,L=S+q,O=S-q;r[w]=H+L,r[w+32]=H-L;var P=.707106781*(O+K);r[w+16]=K+P,r[w+48]=K-P,H=G+B,L=B+U,O=U+Q;var R=.382683433*(H-O),V=.5411961*H+R,W=1.306562965*O+R,X=.707106781*L,Y=Q+X,Z=Q-X;r[w+40]=Z+V,r[w+24]=Z-V,r[w+8]=Y+W,r[w+56]=Y-W,w++}var $;for(u=0;u<y;++u)$=r[u]*a[u],x[u]=$>0?$+.5|0:$-.5|0;return x}function u(){i(65504),i(16),v(74),v(70),v(73),v(70),v(0),v(1),v(1),v(0),i(1),i(1),v(0),v(0)}function w(r,a){i(65472),i(17),v(8),i(a),i(r),v(3),v(1),v(17),v(0),v(2),v(17),v(1),v(3),v(17),v(1)}function s(){i(65499),i(132),v(0);for(var r=0;r<64;r++)v(b[r]);v(1);for(var a=0;a<64;a++)v(j[a])}function y(){i(65476),i(418),v(0);for(var r=0;r<16;r++)v(H[r+1]);for(var a=0;a<=11;a++)v(K[a]);v(16);for(var n=0;n<16;n++)v(L[n+1]);for(var o=0;o<=161;o++)v(O[o]);v(1);for(var e=0;e<16;e++)v(P[e+1]);for(var t=0;t<=11;t++)v(R[t]);v(17);for(var f=0;f<16;f++)v(V[f+1]);for(var c=0;c<=161;c++)v(W[c])}function g(){i(65498),i(12),v(3),v(1),v(0),v(2),v(17),v(3),v(17),v(0),v(63),v(0)}function h(r,a,n,o,e){var t,v=e[0],i=e[240];const u=16,w=63,s=64;for(var y=c(r,a),g=0;g<s;++g)z[G[g]]=y[g];var h=z[0]-n;n=z[0],0==h?f(o[0]):(t=32767+h,f(o[k[t]]),f(_[t]));for(var l=63;l>0&&0==z[l];l--);if(0==l)return f(v),n;for(var A,m=1;m<=l;){for(var d=m;0==z[m]&&m<=l;++m);var p=m-d;if(p>=u){A=p>>4;for(var D=1;D<=A;++D)f(i);p&=15}t=32767+z[m],f(e[(p<<4)+k[t]]),f(_[t]),m++}return l!=w&&f(v),n}function l(){for(var r=String.fromCharCode,a=0;a<256;a++)B[a]=r(a)}function A(r){if(r<=0&&(r=1),r>100&&(r=100),C!=r){var n=0;n=r<50?Math.floor(5e3/r):Math.floor(200-2*r),a(n),C=r,console.log("Quality set to: "+r+"%")}}function m(){var a=(new Date).getTime();r||(r=50),l(),o(),e(),t(),A(r);var n=(new Date).getTime()-a;console.log("Initialization "+n+"ms")}var d,p,D,T,C,M=(Math.round,Math.floor),b=new Array(64),j=new Array(64),E=new Array(64),I=new Array(64),_=new Array(65535),k=new Array(65535),x=new Array(64),z=new Array(64),J=[],N=0,Q=7,S=new Array(64),U=new Array(64),q=new Array(64),B=new Array(256),F=new Array(2048),G=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],H=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],K=[0,1,2,3,4,5,6,7,8,9,10,11],L=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],O=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],P=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],R=[0,1,2,3,4,5,6,7,8,9,10,11],V=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],W=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];this.encode=function(r,a,n){var o=(new Date).getTime();a&&A(a),J=new Array,N=0,Q=7,i(65496),u(),s(),w(r.width,r.height),y(),g();var e=0,t=0,v=0;N=0,Q=7,this.encode.displayName="_encode_";for(var c,l,m,C,M,b,j,_,k,x=r.data,z=r.width,B=r.height,G=4*z,H=0;H<B;){for(c=0;c<G;){for(M=G*H+c,b=M,j=-1,_=0,k=0;k<64;k++)_=k>>3,j=4*(7&k),b=M+_*G+j,H+_>=B&&(b-=G*(H+1+_-B)),c+j>=G&&(b-=c+j-G+4),l=x[b++],m=x[b++],C=x[b++],S[k]=(F[l]+F[m+256>>0]+F[C+512>>0]>>16)-128,U[k]=(F[l+768>>0]+F[m+1024>>0]+F[C+1280>>0]>>16)-128,q[k]=(F[l+1280>>0]+F[m+1536>>0]+F[C+1792>>0]>>16)-128;e=h(S,E,e,d,D),t=h(U,I,t,p,T),v=h(q,I,v,p,T),c+=32}H+=8}if(Q>=0){var K=[];K[1]=Q+1,K[0]=(1<<Q+1)-1,f(K)}if(i(65497),n){for(var L=J.length,O=new Uint8Array(L),P=0;P<L;P++)O[P]=J[P].charCodeAt();J=[];var R=(new Date).getTime()-o;return console.log("Encoding time: "+R+"ms"),O}var V="data:image/jpeg;base64,"+btoa(J.join(""));J=[];var R=(new Date).getTime()-o;return console.log("Encoding time: "+R+"ms"),V},m()}var e,t;e=[],t=function(){return o}.apply(a,e),!(void 0!==t&&(r.exports=t))}});