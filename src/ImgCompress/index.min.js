!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.html5ImgCompress=e():t.html5ImgCompress=e()}(this,function(){return function(t){function e(n){if(i[n])return i[n].exports;var o=i[n]={exports:{},id:n,loaded:!1};return t[n].call(o.exports,o,o.exports,e),o.loaded=!0,o.exports}var n=window.webpackJsonphtml5ImgCompress;window.webpackJsonphtml5ImgCompress=function(i,a){for(var r,h,c=0,d=[];c<i.length;c++)h=i[c],o[h]&&d.push.apply(d,o[h]),o[h]=0;for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(t[r]=a[r]);for(n&&n(i,a);d.length;)d.shift().call(null,e)};var i={},o={3:0};return e.e=function(t,n){if(0===o[t])return n.call(null,e);if(void 0!==o[t])o[t].push(n);else{o[t]=[n];var i=document.getElementsByTagName("head")[0],a=document.createElement("script");a.type="text/javascript",a.charset="utf-8",a.async=!0,a.src=e.p+""+t+".chunk.min.js",i.appendChild(a)}},e.m=t,e.c=i,e.p="",e(0)}([function(t,e,n){var i,o;i=[],o=function(){function t(e,n){var i=t.DEFAULTE;this.file=e,this.options={};for(var o in i)this.options[o]=null==n[o]?i[o]:n[o];this.init()}var e=navigator.userAgent,i=~e.indexOf("MicroMessenger"),o=~e.indexOf("Android"),a=function(){var t=navigator.userAgent.match(/(\d)_\d like Mac OS/);return t&&t[1]<=7}(),r=window.URL||window.webkitURL,h=document.scripts,c=h[h.length-1].src;return n.p=c.substr(0,c.lastIndexOf("/")+1),t.prototype={init:function(){r&&File&&document.createElement("canvas").getContext?this.read(this.file):this.options.notSupport()},read:function(t){var e,h,c,d,s,l,p,u,f,g=this,m=new Image,w=r.createObjectURL(t);g.options.before(t)!==!1&&(m.src=w,m.onload=function(){u=function(r){if(s=g.options.quality,e=g.getSize(m,r),h=document.createElement("canvas"),h.width=e.width,h.height=e.height,c=h.getContext("2d"),a)n.e(0,function(e){var n=[e(3)];(function(e){d=new e(m),f={maxWidth:h.width,maxHeight:h.height,orientation:r},~"68".indexOf(r)&&(f.maxWidth=h.height,f.maxHeight=h.width),d.render(h,f),p=h.toDataURL("image/jpeg",s),g.handler("done",h,m,w,p,t)}).apply(null,n)});else{switch(r){case 3:c.rotate(180*Math.PI/180),c.drawImage(m,-h.width,-h.height,h.width,h.height);break;case 6:c.rotate(90*Math.PI/180),c.drawImage(m,0,-h.width,h.height,h.width);break;case 8:c.rotate(270*Math.PI/180),c.drawImage(m,-h.height,0,h.height,h.width);break;default:c.drawImage(m,0,0,h.width,h.height)}o&&i?n.e(1,function(e){var n=[e(2)];(function(e){l=new e,p=l.encode(c.getImageData(0,0,h.width,h.height),100*s),g.handler("done",h,m,w,p,t)}).apply(null,n)}):(p=h.toDataURL("image/jpeg",s),g.handler("done",h,m,w,p,t))}},o?u():n.e(2,function(t){var e=[t(1)];(function(t){t.getData(m,function(){u(t.getTag(this,"Orientation"))})}).apply(null,e)})},m.onerror=function(){g.handler("fail",h,m,w,p,t)})},handler:function(t,e,n,i,o,a){e=null,n=null,r.revokeObjectURL(i),this.options[t](a,o),this.options.complete(a)},getSize:function(t,e){var n,i=this.options,o=i.maxWidth,a=i.maxHeight,r=t.width,h=t.height;return~"68".indexOf(e)&&(r=t.height,h=t.width),n=r/h,o&&a?n>=o/a?r>o&&(h=o/n,r=o):h>a&&(r=a*n,h=a):o?o<r&&(h=o/n,r=o):a&&a<h&&(r=a*n,h=a),{width:r,height:h}}},t.DEFAULTE={maxWidth:1e3,maxHeight:1e3,quality:.6,before:function(){},done:function(){},fail:function(){},complete:function(){},notSupport:function(){}},window.html5ImgCompress=t}.apply(e,i),!(void 0!==o&&(t.exports=o))}])});