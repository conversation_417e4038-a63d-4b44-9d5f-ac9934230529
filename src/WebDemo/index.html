<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>云手机</title>
  <script src="./sdk/jquery.min.js"></script>
  <script src="./sdk/saas-sdk.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      font-size: 12px;
      list-style: none;
    }

    .cloudApp {
      /* position: absolute; */
      /* background: black; */
      width: 282px;
      height: 498px;
      /* z-index: 99999; */
    }

    .loadingbox {
      position: absolute;
      top: 0;
      left: 0;
      background: #e5e5e5;
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 400px;
      /* position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%); */
    }
  </style>
</head>

<body>
  <div class="cloudApp" id="cloudApp"></div>
  <div id="loadingbox" class="loadingbox">
    <span>加载中...</span>
  </div>
</body>

</html>
<script>
  const id = getUrlParam("id");
  const code = getUrlParam("code");
  const token = getUrlParam("token");
  const type = getUrlParam("type");
  const apiPath = getUrlParam("apiPath");
  let timer = null;

  $('.cloudApp').attr('id', code);

  $.ajax({
    method: "GET", // 请求方式
    url: type == 'device' ? `${apiPath}/device/connectToken/${id}` : `${apiPath}/digital_human/login`, // 请求地址
    data: type == 'device' ? {} : { digitalHumanId: id }, // 需要发送的数据
    dataType: "json", // 对请求返回的数据预处理
    headers: {
      'Authorization': 'Bearer ' + token
    },
    success: (res) => { // 请求成功的回调函数
      const { result, retcode, msg } = res;
      if (retcode != 0) {
        $("#loadingbox span").text(msg);
        return false
      }
      let connectToken = result ? (type == 'device' ? JSON.parse(result.value) : JSON.parse(result.digitalH5)) : null;
      if (!connectToken) {
        $("#loadingbox span").text("获取token失败!");
      } else {
        connectToken.masterId = {
          phoneId: connectToken.phoneId,
          domId: code, // 云手机展示dom
        };
        new Cloudplay({
          ...connectToken,
          onSceneChanged: function (sceneId, extraInfo) {
            console.log("sceneId:" + sceneId, extraInfo);
            if (sceneId === "start") {
              $("#loadingbox").hide();
              setDeviceLockState()
              timer = setInterval(() => {
                setDeviceLockState()
              }, 60000);
            } else if (sceneId === "error") {
              clearInterval(timer)
              $("#loadingbox span").text(extraInfo.message);
            }
          },
        })
      }
    },
    error: (err) => { // 请求失败的回调函数
      console.error(err, 'error');
    }
  });

  // 提交设备播流中状态
  function setDeviceLockState () {
    $.ajax({
      method: "POST",
      url: `${apiPath}/device/lock/${code}?duration=${60}`,
      data: {},
      dataType: "json",
      headers: {
        'Authorization': 'Bearer ' + token
      },
      success: (res) => {
        console.log(res, 'success');
      },
      error: (err) => {
        console.error(err, 'error');
        if (err.status == 401) {
          clearInterval(timer);
        }
      }
    });
  }

  function getUrlParam (name) {
    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象
    const r = window.location.search.substr(1).match(reg); // 匹配目标参数
    if (r != null) return unescape(r[2]); return null; // 返回参数值
  }
</script>