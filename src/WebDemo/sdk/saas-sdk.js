!function(e){var t={};function n(r){var o;return(t[r]||(o=t[r]={i:r,l:!1,exports:{}},e[r].call(o.exports,o,o.exports,n),o.l=!0,o)).exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=30)}([function(e,t,n){var r,o=n(60),i=n(19),a=n(61),s=n(62),c=n(63),l=("undefined"!=typeof ArrayBuffer&&(r=n(64)),"undefined"!=typeof navigator&&/Android/i.test(navigator.userAgent)),u="undefined"!=typeof navigator&&/PhantomJS/i.test(navigator.userAgent),p=l||u,d=(t.protocol=3,t.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6}),f=o(d),h={type:"error",data:"parser error"},A=n(65);function g(e,t,n){for(var r=new Array(e.length),o=s(e.length,n),i=0;i<e.length;i++)!function(e,n,o){t(n,function(t,n){r[e]=n,o(t,r)})}(i,e[i],o)}t.encodePacket=function(e,n,r,o){"function"==typeof n&&(o=n,n=!1),"function"==typeof r&&(o=r,r=null);var i=void 0===e.data?void 0:e.data.buffer||e.data;if("undefined"!=typeof ArrayBuffer&&i instanceof ArrayBuffer){var a=e,s=o;if(!(l=n))return t.encodeBase64Packet(a,s);var l=a.data,u=new Uint8Array(l),f=new Uint8Array(1+l.byteLength);f[0]=d[a.type];for(var h=0;h<u.length;h++)f[h+1]=u[h];return s(f.buffer)}if(void 0!==A&&i instanceof A){var g;l=e,s=o;if(!(a=n))return t.encodeBase64Packet(l,s);if(p){var m=l,y=a,v=s;return y?((g=new FileReader).onload=function(){t.encodePacket({type:m.type,data:g.result},y,!0,v)},g.readAsArrayBuffer(m.data)):t.encodeBase64Packet(m,v)}return(a=new Uint8Array(1))[0]=d[l.type],s(a=new A([a.buffer,l.data]))}return i&&i.base64?(n=e,(i=o)(n="b"+t.packets[e.type]+e.data.data)):(i=d[e.type],void 0!==e.data&&(i+=r?c.encode(String(e.data),{strict:!1}):String(e.data)),o(""+i))},t.encodeBase64Packet=function(e,n){var r,o,i="b"+t.packets[e.type];if(void 0!==A&&e.data instanceof A)return(r=new FileReader).onload=function(){var e=r.result.split(",")[1];n(i+e)},r.readAsDataURL(e.data);try{o=String.fromCharCode.apply(null,new Uint8Array(e.data))}catch(t){for(var a=new Uint8Array(e.data),s=new Array(a.length),c=0;c<a.length;c++)s[c]=a[c];o=String.fromCharCode.apply(null,s)}return i+=btoa(o),n(i)},t.decodePacket=function(e,n,r){var o;return void 0===e?h:"string"==typeof e?"b"===e.charAt(0)?t.decodeBase64Packet(e.substr(1),n):r&&!1===(e=function(e){try{e=c.decode(e,{strict:!1})}catch(e){return!1}return e}(e))||(o=e.charAt(0),Number(o)!=o)||!f[o]?h:1<e.length?{type:f[o],data:e.substring(1)}:{type:f[o]}:(o=new Uint8Array(e)[0],r=a(e,1),A&&"blob"===n&&(r=new A([r])),{type:f[o],data:r})},t.decodeBase64Packet=function(e,t){var n,o=f[e.charAt(0)];return r?(n=r.decode(e.substr(1)),{type:o,data:n="blob"===t&&A?new A([n]):n}):{type:o,data:{base64:!0,data:e.substr(1)}}},t.encodePayload=function(e,n,r){"function"==typeof n&&(r=n,n=null);var o=i(e);return n&&o?A&&!p?t.encodePayloadAsBlob(e,r):t.encodePayloadAsArrayBuffer(e,r):e.length?void g(e,function(e,r){t.encodePacket(e,!!o&&n,!1,function(e){r(null,e.length+":"+e)})},function(e,t){return r(t.join(""))}):r("0:")},t.decodePayload=function(e,n,r){if("string"!=typeof e)return t.decodePayloadAsBinary(e,n,r);if("function"==typeof n&&(r=n,n=null),""===e)return r(h,0,1);for(var o,i="",a=0,s=e.length;a<s;a++){var c=e.charAt(a);if(":"!==c)i+=c;else{if(""===i||i!=(o=Number(i)))return r(h,0,1);if(i!=(c=e.substr(a+1,o)).length)return r(h,0,1);if(c.length){if(c=t.decodePacket(c,n,!1),h.type===c.type&&h.data===c.data)return r(h,0,1);if(!1===r(c,a+o,s))return}a+=o,i=""}}return""!==i?r(h,0,1):void 0},t.encodePayloadAsArrayBuffer=function(e,n){if(!e.length)return n(new ArrayBuffer(0));g(e,function(e,n){t.encodePacket(e,!0,!0,function(e){return n(null,e)})},function(e,t){var r=t.reduce(function(e,t){return e+(t="string"==typeof t?t.length:t.byteLength).toString().length+t+2},0),o=new Uint8Array(r),i=0;return t.forEach(function(e){var t="string"==typeof e,n=e;if(t){for(var r=new Uint8Array(e.length),a=0;a<e.length;a++)r[a]=e.charCodeAt(a);n=r.buffer}o[i++]=t?0:1;var s=n.byteLength.toString();for(a=0;a<s.length;a++)o[i++]=parseInt(s[a]);for(o[i++]=255,r=new Uint8Array(n),a=0;a<r.length;a++)o[i++]=r[a]}),n(o.buffer)})},t.encodePayloadAsBlob=function(e,n){g(e,function(e,n){t.encodePacket(e,!0,!0,function(e){var t=new Uint8Array(1);if(t[0]=1,"string"==typeof e){for(var r=new Uint8Array(e.length),o=0;o<e.length;o++)r[o]=e.charCodeAt(o);e=r.buffer,t[0]=0}var i=(e instanceof ArrayBuffer?e.byteLength:e.size).toString(),a=new Uint8Array(i.length+1);for(o=0;o<i.length;o++)a[o]=parseInt(i[o]);a[i.length]=255,A&&(t=new A([t.buffer,a.buffer,e]),n(null,t))})},function(e,t){return n(new A(t))})},t.decodePayloadAsBinary=function(e,n,r){"function"==typeof n&&(r=n,n=null);for(var o=e,i=[];0<o.byteLength;){for(var s=new Uint8Array(o),c=0===s[0],l="",u=1;255!==s[u];u++){if(310<l.length)return r(h,0,1);l+=s[u]}o=a(o,2+l.length),l=parseInt(l);var p=a(o,0,l);if(c)try{p=String.fromCharCode.apply(null,new Uint8Array(p))}catch(e){var d=new Uint8Array(p);for(p="",u=0;u<d.length;u++)p+=String.fromCharCode(d[u])}i.push(p),o=a(o,l)}var f=i.length;i.forEach(function(e,o){r(t.decodePacket(e,n,!0),o,f)})}},function(e,t,n){"use strict";var r={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};r.localCName=r.generateIdentifier(),r.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},r.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(0<t?"m="+e:e).trim()+"\r\n"})},r.getDescription=function(e){return(e=r.splitSections(e))&&e[0]},r.getMediaSections=function(e){return(e=r.splitSections(e)).shift(),e},r.matchPrefix=function(e,t){return r.splitLines(e).filter(function(e){return 0===e.indexOf(t)})},r.parseCandidate=function(e){for(var t=(0===e.indexOf("a=candidate:")?e.substring(12):e.substring(10)).split(" "),n={foundation:t[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},r=8;r<t.length;r+=2)switch(t[r]){case"raddr":n.relatedAddress=t[r+1];break;case"rport":n.relatedPort=parseInt(t[r+1],10);break;case"tcptype":n.tcpType=t[r+1];break;case"ufrag":n.ufrag=t[r+1],n.usernameFragment=t[r+1];break;default:n[t[r]]=t[r+1]}return n},r.writeCandidate=function(e){var t=[],n=(t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port),e.type);return t.push("typ"),t.push(n),"host"!==n&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},r.parseIceOptions=function(e){return e.substr(14).split(" ")},r.parseRtpMap=function(e){e=e.substr(9).split(" ");var t={payloadType:parseInt(e.shift(),10)};e=e[0].split("/");return t.name=e[0],t.clockRate=parseInt(e[1],10),t.channels=3===e.length?parseInt(e[2],10):1,t.numChannels=t.channels,t},r.writeRtpMap=function(e){var t=e.payloadType,n=(void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType),e.channels||e.numChannels||1);return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},r.parseExtmap=function(e){return e=e.substr(9).split(" "),{id:parseInt(e[0],10),direction:0<e[0].indexOf("/")?e[0].split("/")[1]:"sendrecv",uri:e[1]}},r.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},r.parseFmtp=function(e){for(var t,n={},r=e.substr(e.indexOf(" ")+1).split(";"),o=0;o<r.length;o++)n[(t=r[o].trim().split("="))[0].trim()]=t[1];return n},r.writeFmtp=function(e){var t,n="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length&&(t=[],Object.keys(e.parameters).forEach(function(n){e.parameters[n]?t.push(n+"="+e.parameters[n]):t.push(n)}),n+="a=fmtp:"+r+" "+t.join(";")+"\r\n"),n},r.parseRtcpFb=function(e){return{type:(e=e.substr(e.indexOf(" ")+1).split(" ")).shift(),parameter:e.join(" ")}},r.writeRtcpFb=function(e){var t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},r.parseSsrcMedia=function(e){var t=e.indexOf(" "),n={ssrc:parseInt(e.substr(7,t-7),10)},r=e.indexOf(":",t);return-1<r?(n.attribute=e.substr(t+1,r-t-1),n.value=e.substr(r+1)):n.attribute=e.substr(t+1),n},r.parseSsrcGroup=function(e){return{semantics:(e=e.substr(13).split(" ")).shift(),ssrcs:e.map(function(e){return parseInt(e,10)})}},r.getMid=function(e){if(e=r.matchPrefix(e,"a=mid:")[0])return e.substr(6)},r.parseFingerprint=function(e){return{algorithm:(e=e.substr(14).split(" "))[0].toLowerCase(),value:e[1]}},r.getDtlsParameters=function(e,t){return{role:"auto",fingerprints:r.matchPrefix(e+t,"a=fingerprint:").map(r.parseFingerprint)}},r.writeDtlsParameters=function(e,t){var n="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),n},r.parseCryptoLine=function(e){return e=e.substr(9).split(" "),{tag:parseInt(e[0],10),cryptoSuite:e[1],keyParams:e[2],sessionParams:e.slice(3)}},r.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?r.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},r.parseCryptoKeyParams=function(e){return 0!==e.indexOf("inline:")?null:{keyMethod:"inline",keySalt:(e=e.substr(7).split("|"))[0],lifeTime:e[1],mkiValue:e[2]?e[2].split(":")[0]:void 0,mkiLength:e[2]?e[2].split(":")[1]:void 0}},r.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},r.getCryptoParameters=function(e,t){return r.matchPrefix(e+t,"a=crypto:").map(r.parseCryptoLine)},r.getIceParameters=function(e,t){var n=r.matchPrefix(e+t,"a=ice-ufrag:")[0];e=r.matchPrefix(e+t,"a=ice-pwd:")[0];return n&&e?{usernameFragment:n.substr(12),password:e.substr(10)}:null},r.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},r.parseRtpParameters=function(e){for(var t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},n=r.splitLines(e)[0].split(" "),o=3;o<n.length;o++){var i=n[o];if(s=r.matchPrefix(e,"a=rtpmap:"+i+" ")[0]){var a=r.parseRtpMap(s),s=r.matchPrefix(e,"a=fmtp:"+i+" ");switch(a.parameters=s.length?r.parseFmtp(s[0]):{},a.rtcpFeedback=r.matchPrefix(e,"a=rtcp-fb:"+i+" ").map(r.parseRtcpFb),t.codecs.push(a),a.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(a.name.toUpperCase())}}}return r.matchPrefix(e,"a=extmap:").forEach(function(e){t.headerExtensions.push(r.parseExtmap(e))}),t},r.writeRtpDescription=function(e,t){var n="",o=(n=(n=(n=(n+="m="+e+" ")+(0<t.codecs.length?"9":"0")+" UDP/TLS/RTP/SAVPF ")+t.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n")+"c=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\n",t.codecs.forEach(function(e){n=(n=(n+=r.writeRtpMap(e))+r.writeFmtp(e))+r.writeRtcpFb(e)}),0);return t.codecs.forEach(function(e){e.maxptime>o&&(o=e.maxptime)}),0<o&&(n+="a=maxptime:"+o+"\r\n"),n+="a=rtcp-mux\r\n",t.headerExtensions&&t.headerExtensions.forEach(function(e){n+=r.writeExtmap(e)}),n},r.parseRtpEncodingParameters=function(e){var t,n,o=[],i=r.parseRtpParameters(e),a=-1!==i.fecMechanisms.indexOf("RED"),s=-1!==i.fecMechanisms.indexOf("ULPFEC"),c=0<(n=r.matchPrefix(e,"a=ssrc:").map(function(e){return r.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})).length&&n[0].ssrc,l=(0<(n=r.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})})).length&&1<n[0].length&&n[0][0]===c&&(t=n[0][1]),i.codecs.forEach(function(e){"RTX"===e.name.toUpperCase()&&e.parameters.apt&&(e={ssrc:c,codecPayloadType:parseInt(e.parameters.apt,10)},c&&t&&(e.rtx={ssrc:t}),o.push(e),a)&&((e=JSON.parse(JSON.stringify(e))).fec={ssrc:c,mechanism:s?"red+ulpfec":"red"},o.push(e))}),0===o.length&&c&&o.push({ssrc:c}),r.matchPrefix(e,"b="));return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substr(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substr(5),10)*.95-16e3:void 0,o.forEach(function(e){e.maxBitrate=l})),o},r.parseRtcpParameters=function(e){var t={},n=((n=r.matchPrefix(e,"a=ssrc:").map(function(e){return r.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0])&&(t.cname=n.value,t.ssrc=n.ssrc),r.matchPrefix(e,"a=rtcp-rsize"));t.reducedSize=0<n.length,t.compound=0===n.length,n=r.matchPrefix(e,"a=rtcp-mux");return t.mux=0<n.length,t},r.parseMsid=function(e){var t,n=r.matchPrefix(e,"a=msid:");return 1===n.length?{stream:(t=n[0].substr(7).split(" "))[0],track:t[1]}:0<(n=r.matchPrefix(e,"a=ssrc:").map(function(e){return r.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute})).length?{stream:(t=n[0].value.split(" "))[0],track:t[1]}:void 0},r.parseSctpDescription=function(e){var t,n,o=r.parseMLine(e);return 0<(n=(0<(n=r.matchPrefix(e,"a=max-message-size:")).length&&(t=parseInt(n[0].substr(19),10)),isNaN(t)&&(t=65536),r.matchPrefix(e,"a=sctp-port:"))).length?{port:parseInt(n[0].substr(12),10),protocol:o.fmt,maxMessageSize:t}:0<r.matchPrefix(e,"a=sctpmap:").length?(n=r.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" "),{port:parseInt(n[0],10),protocol:n[1],maxMessageSize:t}):void 0},r.writeSctpDescription=function(e,t){var n=[];n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"];return void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},r.generateSessionId=function(){return Math.random().toString().substr(2,21)},r.writeSessionBoilerplate=function(e,t,n){return t=void 0!==t?t:2,"v=0\r\no="+(n||"thisisadapterortc")+" "+(e=e||r.generateSessionId())+" "+t+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},r.writeMediaSection=function(e,t,n,o){return t=r.writeRtpDescription(e.kind,t),t=(t=(t+=r.writeIceParameters(e.iceGatherer.getLocalParameters()))+r.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":"active"))+"a=mid:"+e.mid+"\r\n",e.direction?t+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?t+="a=sendrecv\r\n":e.rtpSender?t+="a=sendonly\r\n":e.rtpReceiver?t+="a=recvonly\r\n":t+="a=inactive\r\n",e.rtpSender&&(t=t+"a="+(n="msid:"+o.id+" "+e.rtpSender.track.id+"\r\n")+"a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+n,e.sendEncodingParameters[0].rtx)&&(t=(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+n)+"a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n"),t+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+r.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+r.localCName+"\r\n"),t},r.getDirection=function(e,t){for(var n=r.splitLines(e),o=0;o<n.length;o++)switch(n[o]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[o].substr(2)}return t?r.getDirection(t):"sendrecv"},r.getKind=function(e){return r.splitLines(e)[0].split(" ")[0].substr(2)},r.isRejected=function(e){return"0"===e.split(" ",2)[1]},r.parseMLine=function(e){return{kind:(e=r.splitLines(e)[0].substr(2).split(" "))[0],port:parseInt(e[1],10),protocol:e[2],fmt:e.slice(3).join(" ")}},r.parseOLine=function(e){return{username:(e=r.matchPrefix(e,"o=")[0].substr(2).split(" "))[0],sessionId:e[1],sessionVersion:parseInt(e[2],10),netType:e[3],addressType:e[4],address:e[5]}},r.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var t=r.splitLines(e),n=0;n<t.length;n++)if(t[n].length<2||"="!==t[n].charAt(1))return!1;return!0},e.exports=r},function(e,t,n){(function(r){t.log=function(){return"object"==typeof console&&console.log&&void 0},t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),this.useColors){var n="color: "+this.color;t.splice(1,0,n,"color: inherit");let e=0,r=0;t[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(e++,"%c"===t)&&(r=e)}),t.splice(r,0,n)}},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.exports=n(46)(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this,n(5))},function(e,t){e.exports=function(e,t){function n(){}n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},function(e,t,n){(function(r){function o(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r?r.env.DEBUG:e}(t=e.exports=n(66)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n,r,o=this.useColors;e[0]=(o?"%c":"")+this.namespace+(o?" %c":" ")+e[0]+(o?"%c ":" ")+"+"+t.humanize(this.diff),o&&(o="color: "+this.color,e.splice(1,0,o,"color: inherit"),e[r=n=0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&(n++,"%c"===e)&&(r=n)}),e.splice(r,0,o))},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=o,t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(o())}).call(this,n(5))},function(e,t){var n,r;e=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return(n=setTimeout)(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}var s,c=[],l=!1,u=-1;function p(){l&&s&&(l=!1,s.length?c=s.concat(c):u=-1,c.length)&&d()}function d(){if(!l){for(var e=a(p),t=(l=!0,c.length);t;){for(s=c,c=[];++u<t;)s&&s[u].run();u=-1,t=c.length}s=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return(r=clearTimeout)(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function h(){}e.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new f(e,t)),1!==c.length||l||a(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},e.title="browser",e.browser=!0,e.env={},e.argv=[],e.version="",e.versions={},e.on=h,e.addListener=h,e.once=h,e.off=h,e.removeListener=h,e.removeAllListeners=h,e.emit=h,e.prependListener=h,e.prependOnceListener=h,e.listeners=function(e){return[]},e.binding=function(e){throw new Error("process.binding is not supported")},e.cwd=function(){return"/"},e.chdir=function(e){throw new Error("process.chdir is not supported")},e.umask=function(){return 0}},function(e,t,n){var r=n(48)("socket.io-parser"),o=n(51),i=n(52),a=n(7),s=n(15);function c(){}t.protocol=4,t.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],t.CONNECT=0,t.DISCONNECT=1,t.EVENT=2,t.ACK=3,t.ERROR=4,t.BINARY_EVENT=5,t.BINARY_ACK=6,t.Encoder=c,t.Decoder=p;var l=t.ERROR+'"encode error"';function u(e){var n=""+e.type;if(t.BINARY_EVENT!==e.type&&t.BINARY_ACK!==e.type||(n+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(n+=e.nsp+","),null!=e.id&&(n+=e.id),null!=e.data){var o=function(e){try{return JSON.stringify(e)}catch(e){return!1}}(e.data);if(!1===o)return l;n+=o}return r("encoded %j as %s",e,n),n}function p(){this.reconstructor=null}function d(e){this.reconPack=e,this.buffers=[]}function f(e){return{type:t.ERROR,data:"parser error: "+e}}c.prototype.encode=function(e,n){var o;r("encoding packet %j",e),t.BINARY_EVENT===e.type||t.BINARY_ACK===e.type?(o=n,i.removeBlobs(e,function(e){var t=u((e=i.deconstructPacket(e)).packet);(e=e.buffers).unshift(t),o(e)})):n([u(e)])},o(p.prototype),p.prototype.add=function(e){var n;if("string"==typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=function(e){var n=0,o={type:Number(e.charAt(0))};if(null==t.types[o.type])return f("unknown packet type "+o.type);if(t.BINARY_EVENT===o.type||t.BINARY_ACK===o.type){for(var i=n+1;"-"!==e.charAt(++n)&&n!=e.length;);var s=e.substring(i,n);if(s!=Number(s)||"-"!==e.charAt(n))throw new Error("Illegal attachments");o.attachments=Number(s)}if("/"===e.charAt(n+1)){for(i=n+1;++n&&","!==(c=e.charAt(n))&&n!==e.length;);o.nsp=e.substring(i,n)}else o.nsp="/";if(""!==(s=e.charAt(n+1))&&Number(s)==s){var c;for(i=n+1;++n;){if(null==(c=e.charAt(n))||Number(c)!=c){--n;break}if(n===e.length)break}o.id=Number(e.substring(i,n+1))}if(e.charAt(++n)){if(!1===(s=function(e){try{return JSON.parse(e)}catch(e){return!1}}(e.substr(n)))||o.type!==t.ERROR&&!a(s))return f("invalid payload");o.data=s}return r("decoded %s as %j",e,o),o}(e),(t.BINARY_EVENT!==n.type&&t.BINARY_ACK!==n.type||(this.reconstructor=new d(n),0===this.reconstructor.reconPack.attachments))&&this.emit("decoded",n)}else{if(!s(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(n=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,this.emit("decoded",n))}},p.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},d.prototype.takeBinaryData=function(e){return this.buffers.push(e),this.buffers.length===this.reconPack.attachments?(e=i.reconstructPacket(this.reconPack,this.buffers),this.finishedReconstruction(),e):null},d.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";(function(e){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var r=n(53),o=n(54),i=n(55);function a(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function s(e,t){if(a()<t)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=c.prototype:(e=null===e?new c(t):e).length=t,e}function c(e,t,n){if(!(c.TYPED_ARRAY_SUPPORT||this instanceof c))return new c(e,t,n);if("number"!=typeof e)return l(this,e,t,n);if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return p(this,e)}function l(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer){var o=e,a=t,l=n;if(a.byteLength,l<0||a.byteLength<l)throw new RangeError("'offset' is out of bounds");if(a.byteLength<l+(r||0))throw new RangeError("'length' is out of bounds");return a=void 0===l&&void 0===r?new Uint8Array(a):void 0===r?new Uint8Array(a,l):new Uint8Array(a,l,r),c.TYPED_ARRAY_SUPPORT?(o=a).__proto__=c.prototype:o=d(o,a),o}if("string"!=typeof t){if(l=e,r=t,c.isBuffer(r))return 0!==(l=s(l,a=0|f(r.length))).length&&r.copy(l,0,0,a),l;if(r){if("undefined"!=typeof ArrayBuffer&&r.buffer instanceof ArrayBuffer||"length"in r)return"number"!=typeof r.length||function(e){return e!=e}(r.length)?s(l,0):d(l,r);if("Buffer"===r.type&&i(r.data))return d(l,r.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}if(o=e,r=t,e=n,c.isEncoding(e="string"==typeof e&&""!==e?e:"utf8"))return(r=(o=s(o,t=0|h(r,e))).write(r,e))!==t?o.slice(0,r):o;throw new TypeError('"encoding" must be a valid string encoding')}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function p(e,t){if(u(t),e=s(e,t<0?0:0|f(t)),!c.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function d(e,t){var n=t.length<0?0:0|f(t.length);e=s(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function f(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function h(e,t){if(c.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;var n=(e="string"!=typeof e?""+e:e).length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return x(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return N(e).length;default:if(r)return x(e).length;t=(""+t).toLowerCase(),r=!0}}function A(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function g(e,t,n,r,o){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):**********<n?n=**********:n<-2147483648&&(n=-2147483648),n=+n,(n=(n=isNaN(n)?o?0:e.length-1:n)<0?e.length+n:n)>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof t&&(t=c.from(t,r)),c.isBuffer(t))return 0===t.length?-1:m(e,t,n,r,o);if("number"==typeof t)return t&=255,c.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?(o?Uint8Array.prototype.indexOf:Uint8Array.prototype.lastIndexOf).call(e,t,n):m(e,[t],n,r,o);throw new TypeError("val must be string, number or Buffer")}function m(e,t,n,r,o){var i=1,a=e.length,s=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a/=i=2,s/=2,n/=2}function c(e,t){return 1===i?e[t]:e.readUInt16BE(t*i)}if(o)for(var l=-1,u=n;u<a;u++)if(c(e,u)===c(t,-1===l?0:u-l)){if(u-(l=-1===l?u:l)+1===s)return l*i}else-1!==l&&(u-=u-l),l=-1;else for(u=n=a<n+s?a-s:n;0<=u;u--){for(var p=!0,d=0;d<s;d++)if(c(e,u+d)!==c(t,d)){p=!1;break}if(p)return u}return-1}function y(e,t,n,r){return j(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function v(e,t,n,r){return j(function(e,t){for(var n,r,o=[],i=0;i<e.length&&!((t-=2)<0);++i)r=(n=e.charCodeAt(i))>>8,o.push(n%256),o.push(r);return o}(t,e.length-n),e,n,r)}function w(e,t,n){n=Math.min(e.length,n);for(var r=[],o=t;o<n;){var i,a,s,c,l=e[o],u=null,p=239<l?4:223<l?3:191<l?2:1;if(o+p<=n)switch(p){case 1:l<128&&(u=l);break;case 2:128==(192&(i=e[o+1]))&&127<(c=(31&l)<<6|63&i)&&(u=c);break;case 3:i=e[o+1],a=e[o+2],128==(192&i)&&128==(192&a)&&2047<(c=(15&l)<<12|(63&i)<<6|63&a)&&(c<55296||57343<c)&&(u=c);break;case 4:i=e[o+1],a=e[o+2],s=e[o+3],128==(192&i)&&128==(192&a)&&128==(192&s)&&65535<(c=(15&l)<<18|(63&i)<<12|(63&a)<<6|63&s)&&c<1114112&&(u=c)}null===u?(u=65533,p=1):65535<u&&(r.push((u-=65536)>>>10&1023|55296),u=56320|1023&u),r.push(u),o+=p}var d=r,f=d.length;if(f<=b)return String.fromCharCode.apply(String,d);for(var h="",A=0;A<f;)h+=String.fromCharCode.apply(String,d.slice(A,A+=b));return h}t.Buffer=c,t.SlowBuffer=function(e){return+e!=e&&(e=0),c.alloc(+e)},t.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=a(),c.poolSize=8192,c._augment=function(e){return e.__proto__=c.prototype,e},c.from=function(e,t,n){return l(null,e,t,n)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!=typeof Symbol)&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0}),c.alloc=function(e,t,n){return r=null,t=t,n=n,u(e=e),e<=0||void 0===t?s(r,e):"string"==typeof n?s(r,e).fill(t,n):s(r,e).fill(t);var r},c.allocUnsafe=function(e){return p(null,e)},c.allocUnsafeSlow=function(e){return p(null,e)},c.isBuffer=function(e){return!(null==e||!e._isBuffer)},c.compare=function(e,t){if(!c.isBuffer(e)||!c.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,o=0,i=Math.min(n,r);o<i;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0},c.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(e,t){if(!i(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return c.alloc(0);if(void 0===t)for(o=t=0;o<e.length;++o)t+=e[o].length;for(var n=c.allocUnsafe(t),r=0,o=0;o<e.length;++o){var a=e[o];if(!c.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,r),r+=a.length}return n},c.byteLength=h,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)A(this,t,t+1);return this},c.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)A(this,t,t+3),A(this,t+1,t+2);return this},c.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)A(this,t,t+7),A(this,t+1,t+6),A(this,t+2,t+5),A(this,t+3,t+4);return this},c.prototype.toString=function(){var e=0|this.length;return 0==e?"":0===arguments.length?w(this,0,e):function(e,t,n){var o,i=!1;if((t=void 0===t||t<0?0:t)>this.length)return"";if((n=void 0===n||n>this.length?this.length:n)<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e=e||"utf8";;)switch(e){case"hex":var a=t,s=n,c=this.length;(!s||s<0||c<s)&&(s=c);for(var l="",u=a=!a||a<0?0:a;u<s;++u)l+=function(e){return e<16?"0"+e.toString(16):e.toString(16)}(this[u]);return l;case"utf8":case"utf-8":return w(this,t,n);case"ascii":c=t;var p=n,d="";p=Math.min(this.length,p);for(var f=c;f<p;++f)d+=String.fromCharCode(127&this[f]);return d;case"latin1":case"binary":a=t;var h=n,A="";h=Math.min(this.length,h);for(var g=a;g<h;++g)A+=String.fromCharCode(this[g]);return A;case"base64":return m=this,o=n,0===(y=t)&&o===m.length?r.fromByteArray(m):r.fromByteArray(m.slice(y,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":for(var m=t,y=n,v=this.slice(m,y),b="",C=0;C<v.length;C+=2)b+=String.fromCharCode(v[C]+256*v[C+1]);return b;default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}.apply(this,arguments)},c.prototype.equals=function(e){if(c.isBuffer(e))return this===e||0===c.compare(this,e);throw new TypeError("Argument must be a Buffer")},c.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return 0<this.length&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n)&&(e+=" ... "),"<Buffer "+e+">"},c.prototype.compare=function(e,t,n,r,o){if(!c.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),(t=void 0===t?0:t)<0||n>e.length||r<0||o>this.length)throw new RangeError("out of range index");if(o<=r&&n<=t)return 0;if(o<=r)return-1;if(n<=t)return 1;if(this===e)return 0;for(var i=(o>>>=0)-(r>>>=0),a=(n>>>=0)-(t>>>=0),s=Math.min(i,a),l=this.slice(r,o),u=e.slice(t,n),p=0;p<s;++p)if(l[p]!==u[p]){i=l[p],a=u[p];break}return i<a?-1:a<i?1:0},c.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},c.prototype.indexOf=function(e,t,n){return g(this,e,t,n,!0)},c.prototype.lastIndexOf=function(e,t,n){return g(this,e,t,n,!1)},c.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-t;if((void 0===n||o<n)&&(n=o),0<e.length&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r=r||"utf8";for(var i,a,s,c=!1;;)switch(r){case"hex":var l=e,u=t,p=n,d=(u=Number(u)||0,this.length-u);if((!p||d<(p=Number(p)))&&(p=d),(d=l.length)%2!=0)throw new TypeError("Invalid hex string");d/2<p&&(p=d/2);for(var f=0;f<p;++f){var h=parseInt(l.substr(2*f,2),16);if(isNaN(h))return f;this[u+f]=h}return f;case"utf8":case"utf-8":return d=t,s=n,j(x(e,(a=this).length-d),a,d,s);case"ascii":return y(this,e,t,n);case"latin1":case"binary":return y(this,e,t,n);case"base64":return a=this,s=t,i=n,j(N(e),a,s,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return v(this,e,t,n);default:if(c)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),c=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var b=4096;function C(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(n<e+t)throw new RangeError("Trying to access beyond buffer length")}function E(e,t,n,r,o,i){if(!c.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(o<t||t<i)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function M(e,t,n,r){t<0&&(t=65535+t+1);for(var o=0,i=Math.min(e.length-n,2);o<i;++o)e[n+o]=(t&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function I(e,t,n,r){t<0&&(t=4294967295+t+1);for(var o=0,i=Math.min(e.length-n,4);o<i;++o)e[n+o]=t>>>8*(r?o:3-o)&255}function T(e,t,n,r){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function k(e,t,n,r,i){return i||T(e,0,n,4),o.write(e,t,n,r,23,4),n+4}function S(e,t,n,r,i){return i||T(e,0,n,8),o.write(e,t,n,r,52,8),n+8}c.prototype.slice=function(e,t){var n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):n<e&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):n<t&&(t=n),t<e&&(t=e),c.TYPED_ARRAY_SUPPORT)(o=this.subarray(e,t)).__proto__=c.prototype;else for(var r=t-e,o=new c(r,void 0),i=0;i<r;++i)o[i]=this[i+e];return o},c.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||C(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return r},c.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||C(e,t,this.length);for(var r=this[e+--t],o=1;0<t&&(o*=256);)r+=this[e+--t]*o;return r},c.prototype.readUInt8=function(e,t){return t||C(e,1,this.length),this[e]},c.prototype.readUInt16LE=function(e,t){return t||C(e,2,this.length),this[e]|this[e+1]<<8},c.prototype.readUInt16BE=function(e,t){return t||C(e,2,this.length),this[e]<<8|this[e+1]},c.prototype.readUInt32LE=function(e,t){return t||C(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},c.prototype.readUInt32BE=function(e,t){return t||C(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},c.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||C(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return(o*=128)<=r&&(r-=Math.pow(2,8*t)),r},c.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||C(e,t,this.length);for(var r=t,o=1,i=this[e+--r];0<r&&(o*=256);)i+=this[e+--r]*o;return(o*=128)<=i&&(i-=Math.pow(2,8*t)),i},c.prototype.readInt8=function(e,t){return t||C(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},c.prototype.readInt16LE=function(e,t){return t||C(e,2,this.length),32768&(t=this[e]|this[e+1]<<8)?4294901760|t:t},c.prototype.readInt16BE=function(e,t){return t||C(e,2,this.length),32768&(t=this[e+1]|this[e]<<8)?4294901760|t:t},c.prototype.readInt32LE=function(e,t){return t||C(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},c.prototype.readInt32BE=function(e,t){return t||C(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},c.prototype.readFloatLE=function(e,t){return t||C(e,4,this.length),o.read(this,e,!0,23,4)},c.prototype.readFloatBE=function(e,t){return t||C(e,4,this.length),o.read(this,e,!1,23,4)},c.prototype.readDoubleLE=function(e,t){return t||C(e,8,this.length),o.read(this,e,!0,52,8)},c.prototype.readDoubleBE=function(e,t){return t||C(e,8,this.length),o.read(this,e,!1,52,8)},c.prototype.writeUIntLE=function(e,t,n,r){e=+e,t|=0,n|=0,r||E(this,e,t,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[t]=255&e;++i<n&&(o*=256);)this[t+i]=e/o&255;return t+n},c.prototype.writeUIntBE=function(e,t,n,r){e=+e,t|=0,n|=0,r||E(this,e,t,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[t+o]=255&e;0<=--o&&(i*=256);)this[t+o]=e/i&255;return t+n},c.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,1,255,0),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},c.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):M(this,e,t,!0),t+2},c.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):M(this,e,t,!1),t+2},c.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):I(this,e,t,!0),t+4},c.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):I(this,e,t,!1),t+4},c.prototype.writeIntLE=function(e,t,n,r){e=+e,t|=0,r||E(this,e,t,n,(r=Math.pow(2,8*n-1))-1,-r);var o=0,i=1,a=0;for(this[t]=255&e;++o<n&&(i*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/i>>0)-a&255;return t+n},c.prototype.writeIntBE=function(e,t,n,r){e=+e,t|=0,r||E(this,e,t,n,(r=Math.pow(2,8*n-1))-1,-r);var o=n-1,i=1,a=0;for(this[t+o]=255&e;0<=--o&&(i*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/i>>0)-a&255;return t+n},c.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,1,127,-128),c.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&(e=e<0?255+e+1:e),t+1},c.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):M(this,e,t,!0),t+2},c.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):M(this,e,t,!1),t+2},c.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):I(this,e,t,!0),t+4},c.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||E(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),c.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):I(this,e,t,!1),t+4},c.prototype.writeFloatLE=function(e,t,n){return k(this,e,t,!0,n)},c.prototype.writeFloatBE=function(e,t,n){return k(this,e,t,!1,n)},c.prototype.writeDoubleLE=function(e,t,n){return S(this,e,t,!0,n)},c.prototype.writeDoubleBE=function(e,t,n){return S(this,e,t,!1,n)},c.prototype.copy=function(e,t,n,r){if(n=n||0,r||0===r||(r=this.length),t>=e.length&&(t=e.length),(r=0<r&&r<n?n:r)===n)return 0;if(0===e.length||0===this.length)return 0;if((t=t||0)<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length);var o,i=(r=e.length-t<r-n?e.length-t+n:r)-n;if(this===e&&n<t&&t<r)for(o=i-1;0<=o;--o)e[o+t]=this[o+n];else if(i<1e3||!c.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)e[o+t]=this[o+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+i),t);return i},c.prototype.fill=function(e,t,n,r){if("string"==typeof e){var o;if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length&&(o=e.charCodeAt(0))<256&&(e=o),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!c.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(!(n<=t))if(t>>>=0,n=void 0===n?this.length:n>>>0,"number"==typeof(e=e||0))for(s=t;s<n;++s)this[s]=e;else for(var i=c.isBuffer(e)?e:x(new c(e,r).toString()),a=i.length,s=0;s<n-t;++s)this[s+t]=i[s%a];return this};var D=/[^+\/0-9A-Za-z-_]/g;function x(e,t){t=t||1/0;for(var n,r=e.length,o=null,i=[],a=0;a<r;++a){if(55295<(n=e.charCodeAt(a))&&n<57344){if(!o){if(56319<n){-1<(t-=3)&&i.push(239,191,189);continue}if(a+1===r){-1<(t-=3)&&i.push(239,191,189);continue}o=n;continue}if(n<56320){-1<(t-=3)&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&-1<(t-=3)&&i.push(239,191,189);if(o=null,n<128){if(--t<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function N(e){return r.toByteArray(function(e){var t;if((e=((t=e).trim?t.trim():t.replace(/^\s+|\s+$/g,"")).replace(D,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function j(e,t,n,r){for(var o=0;o<r&&!(o+n>=t.length||o>=e.length);++o)t[o+n]=e[o];return o}}).call(this,n(14))},function(e,t,n){var r=n(58),o=n(10);e.exports=function(e){var t=e.xdomain,n=e.xscheme;e=e.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!t||r))return new XMLHttpRequest}catch(e){}try{if("undefined"!=typeof XDomainRequest&&!n&&e)return new XDomainRequest}catch(e){}if(!t)try{return new(o[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}}},function(e,t){e.exports="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")()},function(e,t,n){var r=n(0);function o(e){this.path=e.path,this.hostname=e.hostname,this.port=e.port,this.secure=e.secure,this.query=e.query,this.timestampParam=e.timestampParam,this.timestampRequests=e.timestampRequests,this.readyState="",this.agent=e.agent||!1,this.socket=e.socket,this.enablesXDR=e.enablesXDR,this.withCredentials=e.withCredentials,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized,this.forceNode=e.forceNode,this.isReactNative=e.isReactNative,this.extraHeaders=e.extraHeaders,this.localAddress=e.localAddress}n(12)((e.exports=o).prototype),o.prototype.onError=function(e,t){return(e=new Error(e)).type="TransportError",e.description=t,this.emit("error",e),this},o.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},o.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},o.prototype.send=function(e){if("open"!==this.readyState)throw new Error("Transport not open");this.write(e)},o.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},o.prototype.onData=function(e){e=r.decodePacket(e,this.socket.binaryType),this.onPacket(e)},o.prototype.onPacket=function(e){this.emit("packet",e)},o.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},function(e,t,n){function r(e){if(e){var t,n=e;for(t in r.prototype)n[t]=r.prototype[t];return n}}(e.exports=r).prototype.on=r.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},r.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)this._callbacks={};else{var n=this._callbacks["$"+e];if(n)if(1==arguments.length)delete this._callbacks["$"+e];else{for(var r,o=0;o<n.length;o++)if((r=n[o])===t||r.fn===t){n.splice(o,1);break}0===n.length&&delete this._callbacks["$"+e]}}return this},r.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t)}return this},r.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},r.prototype.hasListeners=function(e){return!!this.listeners(e).length}},function(e,t){t.encode=function(e){var t,n="";for(t in e)e.hasOwnProperty(t)&&(n.length&&(n+="&"),n+=encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return n},t.decode=function(e){for(var t={},n=e.split("&"),r=0,o=n.length;r<o;r++){var i=n[r].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}},function(e,t){var n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){(function(t){e.exports=function(e){return n&&t.isBuffer(e)||r&&(e instanceof ArrayBuffer||o(e))};var n="function"==typeof t&&"function"==typeof t.isBuffer,r="function"==typeof ArrayBuffer,o=function(e){return"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer}}).call(this,n(8).Buffer)},function(e,t,n){var r=n(56),o=n(22),i=n(23),a=n(6),s=n(24),c=n(25),l=n(2)("socket.io-client:manager"),u=n(21),p=n(74),d=Object.prototype.hasOwnProperty;function f(e,t){if(!(this instanceof f))return new f(e,t);e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.nsps={},this.subs=[],this.opts=t,this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(t.randomizationFactor||.5),this.backoff=new p({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this.readyState="closed",this.uri=e,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[],e=t.parser||a,this.encoder=new e.Encoder,this.decoder=new e.Decoder,this.autoConnect=!1!==t.autoConnect,this.autoConnect&&this.open()}(e.exports=f).prototype.emitAll=function(){for(var e in this.emit.apply(this,arguments),this.nsps)d.call(this.nsps,e)&&this.nsps[e].emit.apply(this.nsps[e],arguments)},f.prototype.updateSocketIds=function(){for(var e in this.nsps)d.call(this.nsps,e)&&(this.nsps[e].id=this.generateId(e))},f.prototype.generateId=function(e){return("/"===e?"":e+"#")+this.engine.id},i(f.prototype),f.prototype.reconnection=function(e){return arguments.length?(this._reconnection=!!e,this):this._reconnection},f.prototype.reconnectionAttempts=function(e){return arguments.length?(this._reconnectionAttempts=e,this):this._reconnectionAttempts},f.prototype.reconnectionDelay=function(e){return arguments.length?(this._reconnectionDelay=e,this.backoff&&this.backoff.setMin(e),this):this._reconnectionDelay},f.prototype.randomizationFactor=function(e){return arguments.length?(this._randomizationFactor=e,this.backoff&&this.backoff.setJitter(e),this):this._randomizationFactor},f.prototype.reconnectionDelayMax=function(e){return arguments.length?(this._reconnectionDelayMax=e,this.backoff&&this.backoff.setMax(e),this):this._reconnectionDelayMax},f.prototype.timeout=function(e){return arguments.length?(this._timeout=e,this):this._timeout},f.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},f.prototype.open=f.prototype.connect=function(e,t){var n,o,i,a,c,u;return l("readyState %s",this.readyState),~this.readyState.indexOf("open")||(l("opening %s",this.uri),this.engine=r(this.uri,this.opts),n=this.engine,(o=this).readyState="opening",this.skipReconnect=!1,i=s(n,"open",function(){o.onopen(),e&&e()}),a=s(n,"error",function(t){var n;l("connect_error"),o.cleanup(),o.readyState="closed",o.emitAll("connect_error",t),e?((n=new Error("Connection error")).data=t,e(n)):o.maybeReconnectOnOpen()}),!1!==this._timeout&&(c=this._timeout,l("connect attempt will timeout after %d",c),u=setTimeout(function(){l("connect attempt timed out after %d",c),i.destroy(),n.close(),n.emit("error","timeout"),o.emitAll("connect_timeout",c)},c),this.subs.push({destroy:function(){clearTimeout(u)}})),this.subs.push(i),this.subs.push(a)),this},f.prototype.onopen=function(){l("open"),this.cleanup(),this.readyState="open",this.emit("open");var e=this.engine;this.subs.push(s(e,"data",c(this,"ondata"))),this.subs.push(s(e,"ping",c(this,"onping"))),this.subs.push(s(e,"pong",c(this,"onpong"))),this.subs.push(s(e,"error",c(this,"onerror"))),this.subs.push(s(e,"close",c(this,"onclose"))),this.subs.push(s(this.decoder,"decoded",c(this,"ondecoded")))},f.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},f.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},f.prototype.ondata=function(e){this.decoder.add(e)},f.prototype.ondecoded=function(e){this.emit("packet",e)},f.prototype.onerror=function(e){l("error",e),this.emitAll("error",e)},f.prototype.socket=function(e,t){var n,r=this.nsps[e];function i(){~u(n.connecting,r)||n.connecting.push(r)}return r||(r=new o(this,e,t),this.nsps[e]=r,n=this,r.on("connecting",i),r.on("connect",function(){r.id=n.generateId(e)}),this.autoConnect&&i()),r},f.prototype.destroy=function(e){~(e=u(this.connecting,e))&&this.connecting.splice(e,1),this.connecting.length||this.close()},f.prototype.packet=function(e){l("writing packet %j",e);var t=this;e.query&&0===e.type&&(e.nsp+="?"+e.query),t.encoding?t.packetBuffer.push(e):(t.encoding=!0,this.encoder.encode(e,function(n){for(var r=0;r<n.length;r++)t.engine.write(n[r],e.options);t.encoding=!1,t.processPacketQueue()}))},f.prototype.processPacketQueue=function(){var e;0<this.packetBuffer.length&&!this.encoding&&(e=this.packetBuffer.shift(),this.packet(e))},f.prototype.cleanup=function(){l("cleanup");for(var e=this.subs.length,t=0;t<e;t++)this.subs.shift().destroy();this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},f.prototype.close=f.prototype.disconnect=function(){l("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},f.prototype.onclose=function(e){l("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",e),this._reconnection&&!this.skipReconnect&&this.reconnect()},f.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var e,t,n=this;this.backoff.attempts>=this._reconnectionAttempts?(l("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1):(e=this.backoff.duration(),l("will wait %dms before reconnect attempt",e),this.reconnecting=!0,t=setTimeout(function(){n.skipReconnect||(l("attempting reconnect"),n.emitAll("reconnect_attempt",n.backoff.attempts),n.emitAll("reconnecting",n.backoff.attempts),n.skipReconnect)||n.open(function(e){e?(l("reconnect attempt error"),n.reconnecting=!1,n.reconnect(),n.emitAll("reconnect_error",e.data)):(l("reconnect success"),n.onreconnect())})},e),this.subs.push({destroy:function(){clearTimeout(t)}}))},f.prototype.onreconnect=function(){var e=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",e)}},function(e,t,n){var r=n(9),o=n(59),i=n(68);n=n(69);t.polling=function(e){var t,n,a=!1,s=!1,c=!1!==e.jsonp;if("undefined"!=typeof location&&(t="https:"===location.protocol,n=(n=location.port)||(t?443:80),a=e.hostname!==location.hostname||n!==e.port,s=e.secure!==t),e.xdomain=a,e.xscheme=s,"open"in new r(e)&&!e.forceJSONP)return new o(e);if(c)return new i(e);throw new Error("JSONP disabled")},t.websocket=n},function(e,t,n){var r=n(11),o=n(13),i=n(0),a=n(3),s=n(20),c=n(4)("engine.io-client:polling"),l=(e.exports=u,null!=new(n(9))({xdomain:!1}).responseType);function u(e){var t=e&&e.forceBase64;l&&!t||(this.supportsBinary=!1),r.call(this,e)}a(u,r),u.prototype.name="polling",u.prototype.doOpen=function(){this.poll()},u.prototype.pause=function(e){var t,n=this;function r(){c("paused"),n.readyState="paused",e()}this.readyState="pausing",this.polling||!this.writable?(t=0,this.polling&&(c("we are currently polling - waiting to pause"),t++,this.once("pollComplete",function(){c("pre-pause polling complete"),--t||r()})),this.writable||(c("we are currently writing - waiting to pause"),t++,this.once("drain",function(){c("pre-pause writing complete"),--t||r()}))):r()},u.prototype.poll=function(){c("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},u.prototype.onData=function(e){var t=this;c("polling got data %s",e),i.decodePayload(e,this.socket.binaryType,function(e,n,r){if("opening"===t.readyState&&t.onOpen(),"close"===e.type)return t.onClose(),!1;t.onPacket(e)}),"closed"!==this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"===this.readyState?this.poll():c('ignoring poll - transport state "%s"',this.readyState))},u.prototype.doClose=function(){var e=this;function t(){c("writing close packet"),e.write([{type:"close"}])}"open"===this.readyState?(c("transport open - closing"),t()):(c("transport not open - deferring close"),this.once("open",t))},u.prototype.write=function(e){function t(){n.writable=!0,n.emit("drain")}var n=this;this.writable=!1,i.encodePayload(e,this.supportsBinary,function(e){n.doWrite(e,t)})},u.prototype.uri=function(){var e=this.query||{},t=this.secure?"https":"http",n="";return!1!==this.timestampRequests&&(e[this.timestampParam]=s()),this.supportsBinary||e.sid||(e.b64=1),e=o.encode(e),this.port&&("https"==t&&443!==Number(this.port)||"http"==t&&80!==Number(this.port))&&(n=":"+this.port),e.length&&(e="?"+e),t+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+e}},function(e,t,n){(function(t){var r=n(7),o=Object.prototype.toString,i="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===o.call(Blob),a="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===o.call(File);e.exports=function e(n){if(!n||"object"!=typeof n)return!1;if(r(n)){for(var o=0,s=n.length;o<s;o++)if(e(n[o]))return!0;return!1}if("function"==typeof t&&t.isBuffer&&t.isBuffer(n)||"function"==typeof ArrayBuffer&&n instanceof ArrayBuffer||i&&n instanceof Blob||a&&n instanceof File)return!0;if(n.toJSON&&"function"==typeof n.toJSON&&1===arguments.length)return e(n.toJSON(),!0);for(var c in n)if(Object.prototype.hasOwnProperty.call(n,c)&&e(n[c]))return!0;return!1}}).call(this,n(8).Buffer)},function(e,t,n){"use strict";var r,o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),i=64,a={},s=0,c=0;function l(e){for(var t="";t=o[e%i]+t,0<(e=Math.floor(e/i)););return t}function u(){var e=l(+new Date);return e!==r?(s=0,r=e):e+"."+l(s++)}for(;c<i;c++)a[o[c]]=c;u.encode=l,u.decode=function(e){var t=0;for(c=0;c<e.length;c++)t=t*i+a[e.charAt(c)];return t},e.exports=u},function(e,t){var n=[].indexOf;e.exports=function(e,t){if(n)return e.indexOf(t);for(var r=0;r<e.length;++r)if(e[r]===t)return r;return-1}},function(e,t,n){var r=n(6),o=n(23),i=n(72),a=n(24),s=n(25),c=n(2)("socket.io-client:socket"),l=n(73),u=n(19),p=(e.exports=f,{connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1}),d=o.prototype.emit;function f(e,t,n){this.io=e,this.nsp=t,(this.json=this).ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.flags={},n&&n.query&&(this.query=n.query),this.io.autoConnect&&this.open()}o(f.prototype),f.prototype.subEvents=function(){var e;this.subs||(e=this.io,this.subs=[a(e,"open",s(this,"onopen")),a(e,"packet",s(this,"onpacket")),a(e,"close",s(this,"onclose"))])},f.prototype.open=f.prototype.connect=function(){return this.connected||(this.subEvents(),this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting")),this},f.prototype.send=function(){var e=i(arguments);return e.unshift("message"),this.emit.apply(this,e),this},f.prototype.emit=function(e){var t,n;return p.hasOwnProperty(e)?d.apply(this,arguments):(t=i(arguments),(n={type:(void 0!==this.flags.binary?this.flags.binary:u(t))?r.BINARY_EVENT:r.EVENT,data:t,options:{}}).options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof t[t.length-1]&&(c("emitting packet with ack id %d",this.ids),this.acks[this.ids]=t.pop(),n.id=this.ids++),this.connected?this.packet(n):this.sendBuffer.push(n),this.flags={}),this},f.prototype.packet=function(e){e.nsp=this.nsp,this.io.packet(e)},f.prototype.onopen=function(){var e;c("transport is open - connecting"),"/"!==this.nsp&&(this.query?(e="object"==typeof this.query?l.encode(this.query):this.query,c("sending connect packet with query %s",e),this.packet({type:r.CONNECT,query:e})):this.packet({type:r.CONNECT}))},f.prototype.onclose=function(e){c("close (%s)",e),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",e)},f.prototype.onpacket=function(e){var t=e.nsp===this.nsp,n=e.type===r.ERROR&&"/"===e.nsp;if(t||n)switch(e.type){case r.CONNECT:this.onconnect();break;case r.EVENT:case r.BINARY_EVENT:this.onevent(e);break;case r.ACK:case r.BINARY_ACK:this.onack(e);break;case r.DISCONNECT:this.ondisconnect();break;case r.ERROR:this.emit("error",e.data)}},f.prototype.onevent=function(e){var t=e.data||[];c("emitting event %j",t),null!=e.id&&(c("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?d.apply(this,t):this.receiveBuffer.push(t)},f.prototype.ack=function(e){var t=this,n=!1;return function(){var o;n||(n=!0,o=i(arguments),c("sending ack %j",o),t.packet({type:u(o)?r.BINARY_ACK:r.ACK,id:e,data:o}))}},f.prototype.onack=function(e){var t=this.acks[e.id];"function"==typeof t?(c("calling ack %s with %j",e.id,e.data),t.apply(this,e.data),delete this.acks[e.id]):c("bad ack %s",e.id)},f.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},f.prototype.emitBuffered=function(){for(var e=0;e<this.receiveBuffer.length;e++)d.apply(this,this.receiveBuffer[e]);for(this.receiveBuffer=[],e=0;e<this.sendBuffer.length;e++)this.packet(this.sendBuffer[e]);this.sendBuffer=[]},f.prototype.ondisconnect=function(){c("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},f.prototype.destroy=function(){if(this.subs){for(var e=0;e<this.subs.length;e++)this.subs[e].destroy();this.subs=null}this.io.destroy(this)},f.prototype.close=f.prototype.disconnect=function(){return this.connected&&(c("performing disconnect (%s)",this.nsp),this.packet({type:r.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},f.prototype.compress=function(e){return this.flags.compress=e,this},f.prototype.binary=function(e){return this.flags.binary=e,this}},function(e,t,n){function r(e){if(e){var t,n=e;for(t in r.prototype)n[t]=r.prototype[t];return n}}(e.exports=r).prototype.on=r.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},r.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)this._callbacks={};else{var n=this._callbacks["$"+e];if(n)if(1==arguments.length)delete this._callbacks["$"+e];else for(var r,o=0;o<n.length;o++)if((r=n[o])===t||r.fn===t){n.splice(o,1);break}}return this},r.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),n=this._callbacks["$"+e];if(n)for(var r=0,o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t);return this},r.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},r.prototype.hasListeners=function(e){return!!this.listeners(e).length}},function(e,t){e.exports=function(e,t,n){return e.on(t,n),{destroy:function(){e.removeListener(t,n)}}}},function(e,t){var n=[].slice;e.exports=function(e,t){if("function"!=typeof(t="string"==typeof t?e[t]:t))throw new Error("bind() requires a function");var r=n.call(arguments,2);return function(){return t.apply(e,r.concat(n.call(arguments)))}}},function(e,t,n){e.exports=n.p+"e8a0f16c4183c5230f8ec8af5509eeb7.swf"},function(e,t,n){"use strict";var r=n(1);function o(e,t,n,o,i){return t=r.writeRtpDescription(e.kind,t),t=(t=(t+=r.writeIceParameters(e.iceGatherer.getLocalParameters()))+r.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===n?"actpass":i||"active"))+"a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?t+="a=sendrecv\r\n":e.rtpSender?t+="a=sendonly\r\n":e.rtpReceiver?t+="a=recvonly\r\n":t+="a=inactive\r\n",e.rtpSender&&(n=e.rtpSender._initialTrackId||e.rtpSender.track.id,e.rtpSender._initialTrackId=n,t=t+"a="+(i="msid:"+(o?o.id:"-")+" "+n+"\r\n")+"a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+i,e.sendEncodingParameters[0].rtx)&&(t=(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+i)+"a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n"),t+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+r.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(t+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+r.localCName+"\r\n"),t}function i(e,t){function n(e,t){e=parseInt(e,10);for(var n=0;n<t.length;n++)if(t[n].payloadType===e||t[n].preferredPayloadType===e)return t[n]}var r={codecs:[],headerExtensions:[],fecMechanisms:[]};return e.codecs.forEach(function(o){for(var i=0;i<t.codecs.length;i++){var a=t.codecs[i];if(o.name.toLowerCase()===a.name.toLowerCase()&&o.clockRate===a.clockRate&&("rtx"!==o.name.toLowerCase()||!o.parameters||!a.parameters.apt||function(e,t,r,o){return e=n(e.parameters.apt,r),r=n(t.parameters.apt,o),e&&r&&e.name.toLowerCase()===r.name.toLowerCase()}(o,a,e.codecs,t.codecs))){(a=JSON.parse(JSON.stringify(a))).numChannels=Math.min(o.numChannels,a.numChannels),r.codecs.push(a),a.rtcpFeedback=a.rtcpFeedback.filter(function(e){for(var t=0;t<o.rtcpFeedback.length;t++)if(o.rtcpFeedback[t].type===e.type&&o.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}}),e.headerExtensions.forEach(function(e){for(var n=0;n<t.headerExtensions.length;n++){var o=t.headerExtensions[n];if(e.uri===o.uri){r.headerExtensions.push(o);break}}}),r}function a(e,t,n){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(n)}function s(e,t){var n=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return n||e.addRemoteCandidate(t),!n}function c(e,t){return(t=new Error(t)).name=e,t.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],t}e.exports=function(e,t){function n(t,n){n.addTrack(t),n.dispatchEvent(new e.MediaStreamTrackEvent("addtrack",{track:t}))}function l(t,n,r,o){var i=new Event("track");i.track=n,i.receiver=r,i.transceiver={receiver:r},i.streams=o,e.setTimeout(function(){t._dispatchEvent("track",i)})}function u(n){var o,i,a,s=this,l=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){s[e]=l[e].bind(l)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",n=JSON.parse(JSON.stringify(n||{})),this.usingBundle="max-bundle"===n.bundlePolicy,"negotiate"===n.rtcpMuxPolicy)throw c("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(n.rtcpMuxPolicy||(n.rtcpMuxPolicy="require"),n.iceTransportPolicy){case"all":case"relay":break;default:n.iceTransportPolicy="all"}switch(n.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:n.bundlePolicy="balanced"}if(n.iceServers=(o=n.iceServers||[],i=t,a=!1,(o=JSON.parse(JSON.stringify(o))).filter(function(e){var t,n;if(e&&(e.urls||e.url))return n=e.urls||e.url,e.url&&e.urls,n=(n=(t="string"==typeof n)?[n]:n).filter(function(e){return 0!==e.indexOf("turn:")||-1===e.indexOf("transport=udp")||-1!==e.indexOf("turn:[")||a?0===e.indexOf("stun:")&&14393<=i&&-1===e.indexOf("?transport=udp"):a=!0}),delete e.url,e.urls=t?n[0]:n,!!n.length})),this._iceGatherers=[],n.iceCandidatePoolSize)for(var u=n.iceCandidatePoolSize;0<u;u--)this._iceGatherers.push(new e.RTCIceGatherer({iceServers:n.iceServers,gatherPolicy:n.iceTransportPolicy}));else n.iceCandidatePoolSize=0;this._config=n,this.transceivers=[],this._sdpSessionId=r.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1}Object.defineProperty(u.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(u.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),u.prototype.onicecandidate=null,u.prototype.onaddstream=null,u.prototype.ontrack=null,u.prototype.onremovestream=null,u.prototype.onsignalingstatechange=null,u.prototype.oniceconnectionstatechange=null,u.prototype.onconnectionstatechange=null,u.prototype.onicegatheringstatechange=null,u.prototype.onnegotiationneeded=null,u.prototype.ondatachannel=null,u.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},u.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},u.prototype.getConfiguration=function(){return this._config},u.prototype.getLocalStreams=function(){return this.localStreams},u.prototype.getRemoteStreams=function(){return this.remoteStreams},u.prototype._createTransceiver=function(e,t){var n=0<this.transceivers.length;e={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};return this.usingBundle&&n?(e.iceTransport=this.transceivers[0].iceTransport,e.dtlsTransport=this.transceivers[0].dtlsTransport):(n=this._createIceAndDtlsTransports(),e.iceTransport=n.iceTransport,e.dtlsTransport=n.dtlsTransport),t||this.transceivers.push(e),e},u.prototype.addTrack=function(t,n){if(this._isClosed)throw c("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find(function(e){return e.track===t}))throw c("InvalidAccessError","Track already exists.");for(var o=0;o<this.transceivers.length;o++)this.transceivers[o].track||this.transceivers[o].kind!==t.kind||(r=this.transceivers[o]);return r=r||this._createTransceiver(t.kind),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(n)&&this.localStreams.push(n),r.track=t,r.stream=n,r.rtpSender=new e.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},u.prototype.addStream=function(e){var n,r=this;15025<=t?e.getTracks().forEach(function(t){r.addTrack(t,e)}):(n=e.clone(),e.getTracks().forEach(function(e,t){var r=n.getTracks()[t];e.addEventListener("enabled",function(e){r.enabled=e.enabled})}),n.getTracks().forEach(function(e){r.addTrack(e,n)}))},u.prototype.removeTrack=function(t){if(this._isClosed)throw c("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof e.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var n=this.transceivers.find(function(e){return e.rtpSender===t});if(!n)throw c("InvalidAccessError","Sender was not created by this connection.");var r=n.stream;n.rtpSender.stop(),n.rtpSender=null,n.track=null,n.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(r)&&-1<this.localStreams.indexOf(r)&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},u.prototype.removeStream=function(e){var t=this;e.getTracks().forEach(function(e){var n=t.getSenders().find(function(t){return t.track===e});n&&t.removeTrack(n)})},u.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},u.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},u.prototype._createIceGatherer=function(t,n){var r,o=this;return n&&0<t?this.transceivers[0].iceGatherer:this._iceGatherers.length?this._iceGatherers.shift():(r=new e.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy}),Object.defineProperty(r,"state",{value:"new",writable:!0}),this.transceivers[t].bufferedCandidateEvents=[],this.transceivers[t].bufferCandidates=function(e){var n=!e.candidate||0===Object.keys(e.candidate).length;r.state=n?"completed":"gathering",null!==o.transceivers[t].bufferedCandidateEvents&&o.transceivers[t].bufferedCandidateEvents.push(e)},r.addEventListener("localcandidate",this.transceivers[t].bufferCandidates),r)},u.prototype._gather=function(t,n){var o,i=this,a=this.transceivers[n].iceGatherer;a.onlocalcandidate||(o=this.transceivers[n].bufferedCandidateEvents,this.transceivers[n].bufferedCandidateEvents=null,a.removeEventListener("localcandidate",this.transceivers[n].bufferCandidates),a.onlocalcandidate=function(e){var o,s;i.usingBundle&&0<n||((o=new Event("icecandidate")).candidate={sdpMid:t,sdpMLineIndex:n},(s=!(e=e.candidate)||0===Object.keys(e).length)?"new"!==a.state&&"gathering"!==a.state||(a.state="completed"):("new"===a.state&&(a.state="gathering"),e.component=1,e.ufrag=a.getLocalParameters().usernameFragment,e=r.writeCandidate(e),o.candidate=Object.assign(o.candidate,r.parseCandidate(e)),o.candidate.candidate=e,o.candidate.toJSON=function(){return{candidate:o.candidate.candidate,sdpMid:o.candidate.sdpMid,sdpMLineIndex:o.candidate.sdpMLineIndex,usernameFragment:o.candidate.usernameFragment}}),(e=r.getMediaSections(i._localDescription.sdp))[o.candidate.sdpMLineIndex]+=s?"a=end-of-candidates\r\n":"a="+o.candidate.candidate+"\r\n",i._localDescription.sdp=r.getDescription(i._localDescription.sdp)+e.join(""),e=i.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state}),"gathering"!==i.iceGatheringState&&(i.iceGatheringState="gathering",i._emitGatheringStateChange()),s||i._dispatchEvent("icecandidate",o),e&&(i._dispatchEvent("icecandidate",new Event("icecandidate")),i.iceGatheringState="complete",i._emitGatheringStateChange()))},e.setTimeout(function(){o.forEach(function(e){a.onlocalcandidate(e)})},0))},u.prototype._createIceAndDtlsTransports=function(){var t=this,n=new e.RTCIceTransport(null),r=(n.onicestatechange=function(){t._updateIceConnectionState(),t._updateConnectionState()},new e.RTCDtlsTransport(n));return r.ondtlsstatechange=function(){t._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),t._updateConnectionState()},{iceTransport:n,dtlsTransport:r}},u.prototype._disposeIceAndDtlsTransports=function(e){var t;(t=((t=((t=this.transceivers[e].iceGatherer)&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer),this.transceivers[e].iceTransport))&&(delete t.onicestatechange,delete this.transceivers[e].iceTransport),this.transceivers[e].dtlsTransport))&&(delete t.ondtlsstatechange,delete t.onerror,delete this.transceivers[e].dtlsTransport)},u.prototype._transceive=function(e,n,o){var a=i(e.localCapabilities,e.remoteCapabilities);n&&e.rtpSender&&(a.encodings=e.sendEncodingParameters,a.rtcp={cname:r.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(a.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(a)),o&&e.rtpReceiver&&0<a.codecs.length&&("video"===e.kind&&e.recvEncodingParameters&&t<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?a.encodings=e.recvEncodingParameters:a.encodings=[{}],a.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(a.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(a.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(a))},u.prototype.setLocalDescription=function(e){var t,n,o,s=this;return-1===["offer","answer"].indexOf(e.type)?Promise.reject(c("TypeError",'Unsupported type "'+e.type+'"')):!a("setLocalDescription",e.type,s.signalingState)||s._isClosed?Promise.reject(c("InvalidStateError","Can not set local "+e.type+" in state "+s.signalingState)):("offer"===e.type?(t=r.splitSections(e.sdp),n=t.shift(),t.forEach(function(e,t){e=r.parseRtpParameters(e),s.transceivers[t].localCapabilities=e}),s.transceivers.forEach(function(e,t){s._gather(e.mid,t)})):"answer"===e.type&&(n=(t=r.splitSections(s._remoteDescription.sdp)).shift(),o=0<r.matchPrefix(n,"a=ice-lite").length,t.forEach(function(e,t){var a,c=s.transceivers[t],l=c.iceGatherer,u=c.iceTransport,p=c.dtlsTransport,d=c.localCapabilities,f=c.remoteCapabilities;r.isRejected(e)&&0===r.matchPrefix(e,"a=bundle-only").length||c.rejected||(a=r.getIceParameters(e,n),e=r.getDtlsParameters(e,n),o&&(e.role="server"),s.usingBundle&&0!==t||(s._gather(c.mid,t),"new"===u.state&&u.start(l,a,o?"controlling":"controlled"),"new"===p.state&&p.start(e)),t=i(d,f),s._transceive(c,0<t.codecs.length,!1))})),s._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?s._updateSignalingState("have-local-offer"):s._updateSignalingState("stable"),Promise.resolve())},u.prototype.setRemoteDescription=function(o){var u,p,d,f,h,A,g,m=this;return-1===["offer","answer"].indexOf(o.type)?Promise.reject(c("TypeError",'Unsupported type "'+o.type+'"')):!a("setRemoteDescription",o.type,m.signalingState)||m._isClosed?Promise.reject(c("InvalidStateError","Can not set remote "+o.type+" in state "+m.signalingState)):(u={},m.remoteStreams.forEach(function(e){u[e.id]=e}),p=[],d=r.splitSections(o.sdp),f=d.shift(),h=0<r.matchPrefix(f,"a=ice-lite").length,A=0<r.matchPrefix(f,"a=group:BUNDLE ").length,m.usingBundle=A,g=r.matchPrefix(f,"a=ice-options:")[0],m.canTrickleIceCandidates=!!g&&0<=g.substr(14).split(" ").indexOf("trickle"),d.forEach(function(a,c){var l,d,g,y,v,w,b,C,E,M,I,T,k,S=r.splitLines(a),D=r.getKind(a),x=r.isRejected(a)&&0===r.matchPrefix(a,"a=bundle-only").length,N=(S=S[0].substr(2).split(" ")[2],r.getDirection(a,f)),j=r.parseMsid(a),B=r.getMid(a)||r.generateIdentifier();x||"application"===D&&("DTLS/SCTP"===S||"UDP/DTLS/SCTP"===S)?m.transceivers[c]={mid:B,kind:D,protocol:S,rejected:!0}:(!x&&m.transceivers[c]&&m.transceivers[c].rejected&&(m.transceivers[c]=m._createTransceiver(D,!0)),S=r.parseRtpParameters(a),x||(y=r.getIceParameters(a,f),(v=r.getDtlsParameters(a,f)).role="client"),d=r.parseRtpEncodingParameters(a),w=r.parseRtcpParameters(a),b=0<r.matchPrefix(a,"a=end-of-candidates",f).length,a=r.matchPrefix(a,"a=candidate:").map(function(e){return r.parseCandidate(e)}).filter(function(e){return 1===e.component}),("offer"===o.type||"answer"===o.type)&&!x&&A&&0<c&&m.transceivers[c]&&(m._disposeIceAndDtlsTransports(c),m.transceivers[c].iceGatherer=m.transceivers[0].iceGatherer,m.transceivers[c].iceTransport=m.transceivers[0].iceTransport,m.transceivers[c].dtlsTransport=m.transceivers[0].dtlsTransport,m.transceivers[c].rtpSender&&m.transceivers[c].rtpSender.setTransport(m.transceivers[0].dtlsTransport),m.transceivers[c].rtpReceiver)&&m.transceivers[c].rtpReceiver.setTransport(m.transceivers[0].dtlsTransport),"offer"!==o.type||x?"answer"!==o.type||x||(x=(l=m.transceivers[c]).iceGatherer,T=l.iceTransport,C=l.dtlsTransport,k=l.rtpReceiver,E=l.sendEncodingParameters,g=l.localCapabilities,m.transceivers[c].recvEncodingParameters=d,m.transceivers[c].remoteCapabilities=S,m.transceivers[c].rtcpParameters=w,a.length&&"new"===T.state&&(!h&&!b||A&&0!==c?a.forEach(function(e){s(l.iceTransport,e)}):T.setRemoteCandidates(a)),A&&0!==c||("new"===T.state&&T.start(x,y,"controlling"),"new"===C.state&&C.start(v)),!i(l.localCapabilities,l.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&l.sendEncodingParameters[0].rtx&&delete l.sendEncodingParameters[0].rtx,m._transceive(l,"sendrecv"===N||"recvonly"===N,"sendrecv"===N||"sendonly"===N),!k||"sendrecv"!==N&&"sendonly"!==N?delete l.rtpReceiver:(I=k.track,j?(u[j.stream]||(u[j.stream]=new e.MediaStream),n(I,u[j.stream]),p.push([I,k,u[j.stream]])):(u.default||(u.default=new e.MediaStream),n(I,u.default),p.push([I,k,u.default])))):((l=m.transceivers[c]||m._createTransceiver(D)).mid=B,l.iceGatherer||(l.iceGatherer=m._createIceGatherer(c,A)),a.length&&"new"===l.iceTransport.state&&(!b||A&&0!==c?a.forEach(function(e){s(l.iceTransport,e)}):l.iceTransport.setRemoteCandidates(a)),g=e.RTCRtpReceiver.getCapabilities(D),t<15019&&(g.codecs=g.codecs.filter(function(e){return"rtx"!==e.name})),E=l.sendEncodingParameters||[{ssrc:1001*(2*c+2)}],T=!1,"sendrecv"===N||"sendonly"===N?(T=!l.rtpReceiver,k=l.rtpReceiver||new e.RTCRtpReceiver(l.dtlsTransport,D),T&&(I=k.track,(M=j&&"-"===j.stream?M:j?(u[j.stream]||(u[j.stream]=new e.MediaStream,Object.defineProperty(u[j.stream],"id",{get:function(){return j.stream}})),Object.defineProperty(I,"id",{get:function(){return j.track}}),u[j.stream]):(u.default||(u.default=new e.MediaStream),u.default))&&(n(I,M),l.associatedRemoteMediaStreams.push(M)),p.push([I,k,M]))):l.rtpReceiver&&l.rtpReceiver.track&&(l.associatedRemoteMediaStreams.forEach(function(t){var n=t.getTracks().find(function(e){return e.id===l.rtpReceiver.track.id});n&&(n=n,(t=t).removeTrack(n),t.dispatchEvent(new e.MediaStreamTrackEvent("removetrack",{track:n})))}),l.associatedRemoteMediaStreams=[]),l.localCapabilities=g,l.remoteCapabilities=S,l.rtpReceiver=k,l.rtcpParameters=w,l.sendEncodingParameters=E,l.recvEncodingParameters=d,m._transceive(m.transceivers[c],!1,T)))}),void 0===m._dtlsRole&&(m._dtlsRole="offer"===o.type?"active":"passive"),m._remoteDescription={type:o.type,sdp:o.sdp},"offer"===o.type?m._updateSignalingState("have-remote-offer"):m._updateSignalingState("stable"),Object.keys(u).forEach(function(t){var n,r=u[t];r.getTracks().length&&(-1===m.remoteStreams.indexOf(r)&&(m.remoteStreams.push(r),(n=new Event("addstream")).stream=r,e.setTimeout(function(){m._dispatchEvent("addstream",n)})),p.forEach(function(e){var t=e[0],n=e[1];r.id===e[2].id&&l(m,t,n,[r])}))}),p.forEach(function(e){e[2]||l(m,e[0],e[1],[])}),e.setTimeout(function(){m&&m.transceivers&&m.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&0<e.iceTransport.getRemoteCandidates().length&&e.iceTransport.addRemoteCandidate({})})},4e3),Promise.resolve())},u.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},u.prototype._updateSignalingState=function(e){this.signalingState=e,e=new Event("signalingstatechange"),this._dispatchEvent("signalingstatechange",e)},u.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,e.setTimeout(function(){var e;t.needNegotiation&&(t.needNegotiation=!1,e=new Event("negotiationneeded"),t._dispatchEvent("negotiationneeded",e))},0))},u.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",0<t.failed?e="failed":0<t.checking?e="checking":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected?e="connected":0<t.completed&&(e="completed"),e!==this.iceConnectionState&&(this.iceConnectionState=e,e=new Event("iceconnectionstatechange"),this._dispatchEvent("iceconnectionstatechange",e))},u.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",0<t.failed?e="failed":0<t.connecting?e="connecting":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected&&(e="connected"),e!==this.connectionState&&(this.connectionState=e,e=new Event("connectionstatechange"),this._dispatchEvent("connectionstatechange",e))},u.prototype.createOffer=function(){var n=this;if(n._isClosed)return Promise.reject(c("InvalidStateError","Can not call createOffer after close"));var i=n.transceivers.filter(function(e){return"audio"===e.kind}).length,a=n.transceivers.filter(function(e){return"video"===e.kind}).length;if(l=arguments[0]){if(l.mandatory||l.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==l.offerToReceiveAudio&&(i=!0===l.offerToReceiveAudio?1:!1===l.offerToReceiveAudio?0:l.offerToReceiveAudio),void 0!==l.offerToReceiveVideo&&(a=!0===l.offerToReceiveVideo?1:!1===l.offerToReceiveVideo?0:l.offerToReceiveVideo)}for(n.transceivers.forEach(function(e){"audio"===e.kind?--i<0&&(e.wantReceive=!1):"video"===e.kind&&--a<0&&(e.wantReceive=!1)});0<i||0<a;)0<i&&(n._createTransceiver("audio"),i--),0<a&&(n._createTransceiver("video"),a--);var s=r.writeSessionBoilerplate(n._sdpSessionId,n._sdpSessionVersion++),l=(n.transceivers.forEach(function(o,i){var a=o.track,s=o.kind,c=o.mid||r.generateIdentifier();o.mid=c,o.iceGatherer||(o.iceGatherer=n._createIceGatherer(i,n.usingBundle)),c=e.RTCRtpSender.getCapabilities(s),t<15019&&(c.codecs=c.codecs.filter(function(e){return"rtx"!==e.name})),c.codecs.forEach(function(e){"H264"===e.name&&void 0===e.parameters["level-asymmetry-allowed"]&&(e.parameters["level-asymmetry-allowed"]="1"),o.remoteCapabilities&&o.remoteCapabilities.codecs&&o.remoteCapabilities.codecs.forEach(function(t){e.name.toLowerCase()===t.name.toLowerCase()&&e.clockRate===t.clockRate&&(e.preferredPayloadType=t.payloadType)})}),c.headerExtensions.forEach(function(e){(o.remoteCapabilities&&o.remoteCapabilities.headerExtensions||[]).forEach(function(t){e.uri===t.uri&&(e.id=t.id)})}),i=o.sendEncodingParameters||[{ssrc:1001*(2*i+1)}];a&&15019<=t&&"video"===s&&!i[0].rtx&&(i[0].rtx={ssrc:i[0].ssrc+1}),o.wantReceive&&(o.rtpReceiver=new e.RTCRtpReceiver(o.dtlsTransport,s)),o.localCapabilities=c,o.sendEncodingParameters=i}),"max-compat"!==n._config.bundlePolicy&&(s+="a=group:BUNDLE "+n.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),s+="a=ice-options:trickle\r\n",n.transceivers.forEach(function(e,t){s=s+o(e,e.localCapabilities,"offer",e.stream,n._dtlsRole)+"a=rtcp-rsize\r\n",!e.iceGatherer||"new"===n.iceGatheringState||0!==t&&n.usingBundle||(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,s+="a="+r.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(s+="a=end-of-candidates\r\n"))}),new e.RTCSessionDescription({type:"offer",sdp:s}));return Promise.resolve(l)},u.prototype.createAnswer=function(){var n,a,s,l=this;return l._isClosed?Promise.reject(c("InvalidStateError","Can not call createAnswer after close")):"have-remote-offer"!==l.signalingState&&"have-local-pranswer"!==l.signalingState?Promise.reject(c("InvalidStateError","Can not call createAnswer in signalingState "+l.signalingState)):(n=r.writeSessionBoilerplate(l._sdpSessionId,l._sdpSessionVersion++),l.usingBundle&&(n+="a=group:BUNDLE "+l.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),n+="a=ice-options:trickle\r\n",a=r.getMediaSections(l._remoteDescription.sdp).length,l.transceivers.forEach(function(e,r){var s;a<r+1||(e.rejected?("application"===e.kind?"DTLS/SCTP"===e.protocol?n+="m=application 0 DTLS/SCTP 5000\r\n":n+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?n+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(n+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),n+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n"):(e.stream&&("audio"===e.kind?s=e.stream.getAudioTracks()[0]:"video"===e.kind&&(s=e.stream.getVideoTracks()[0]),s)&&15019<=t&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1}),!(r=i(e.localCapabilities,e.remoteCapabilities)).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,n+=o(e,r,"answer",e.stream,l._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(n+="a=rtcp-rsize\r\n")))}),s=new e.RTCSessionDescription({type:"answer",sdp:n}),Promise.resolve(s))},u.prototype.addIceCandidate=function(e){var t,n=this;return e&&void 0===e.sdpMLineIndex&&!e.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(o,i){if(!n._remoteDescription)return i(c("InvalidStateError","Can not add ICE candidate without a remote description"));if(e&&""!==e.candidate){var a=e.sdpMLineIndex;if(e.sdpMid)for(var l=0;l<n.transceivers.length;l++)if(n.transceivers[l].mid===e.sdpMid){a=l;break}var u=n.transceivers[a];if(!u)return i(c("OperationError","Can not add ICE candidate"));if(u.rejected)return o();var p=0<Object.keys(e.candidate).length?r.parseCandidate(e.candidate):{};if("tcp"===p.protocol&&(0===p.port||9===p.port))return o();if(p.component&&1!==p.component)return o();if((0===a||0<a&&u.iceTransport!==n.transceivers[0].iceTransport)&&!s(u.iceTransport,p))return i(c("OperationError","Can not add ICE candidate"));0===(u=e.candidate.trim()).indexOf("a=")&&(u=u.substr(2)),(t=r.getMediaSections(n._remoteDescription.sdp))[a]+="a="+(p.type?u:"end-of-candidates")+"\r\n",n._remoteDescription.sdp=r.getDescription(n._remoteDescription.sdp)+t.join("")}else for(var d=0;d<n.transceivers.length&&(n.transceivers[d].rejected||(n.transceivers[d].iceTransport.addRemoteCandidate({}),(t=r.getMediaSections(n._remoteDescription.sdp))[d]+="a=end-of-candidates\r\n",n._remoteDescription.sdp=r.getDescription(n._remoteDescription.sdp)+t.join(""),!n.usingBundle));d++);o()})},u.prototype.getStats=function(t){if(t&&t instanceof e.MediaStreamTrack){var n=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?n=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(n=e.rtpReceiver)}),n)return n.getStats();throw c("InvalidAccessError","Invalid selector.")}var r=[];return this.transceivers.forEach(function(e){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(t){e[t]&&r.push(e[t].getStats())})}),Promise.all(r).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})},["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(t){var n;(t=e[t])&&t.prototype&&t.prototype.getStats&&(n=t.prototype.getStats,t.prototype.getStats=function(){return n.apply(this).then(function(e){var t=new Map;return Object.keys(e).forEach(function(n){var r;e[n].type={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[(r=e[n]).type]||r.type,t.set(n,e[n])}),t})})});var p=["createOffer","createAnswer"];return p.forEach(function(e){var t=u.prototype[e];u.prototype[e]=function(){var e=arguments;return"function"==typeof e[0]||"function"==typeof e[1]?t.apply(this,[arguments[2]]).then(function(t){"function"==typeof e[0]&&e[0].apply(null,[t])},function(t){"function"==typeof e[1]&&e[1].apply(null,[t])}):t.apply(this,arguments)}}),(p=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var t=u.prototype[e];u.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]||"function"==typeof e[2]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)},function(t){"function"==typeof e[2]&&e[2].apply(null,[t])}):t.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=u.prototype[e];u.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),u}},function(e,t,n){var r=n(44),o=n(6),i=n(16),a=n(2)("socket.io-client"),s=(e.exports=t=c,t.managers={});function c(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n=(e=r(e)).source,o=e.id,c=e.path;c=s[o]&&c in s[o].nsps,c=t.forceNew||t["force new connection"]||!1===t.multiplex||c?(a("ignoring socket cache for %s",n),i(n,t)):(s[o]||(a("new io instance for %s",n),s[o]=i(n,t)),s[o]);return e.query&&!t.query&&(t.query=e.query),c.socket(e.path,t)}t.protocol=o.protocol,t.connect=c,t.Manager=n(16),t.Socket=n(22)},function(e,t,n){e.exports=n.p+"c6ea409c6d44ff9cd017604db56704d3.swf"},function(e,t,n){n(31),n(32),n(76),e.exports=n(75)},function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.swfobject=function(){var e,t,r,o,i,a,s="undefined",c="object",l="Shockwave Flash",u="application/x-shockwave-flash",p="SWFObjectExprInst",d="onreadystatechange",f=window,h=document,A=navigator,g=!1,m=[function(){(g?function(){var e,t=h.getElementsByTagName("body")[0],r=O(c),o=(r.setAttribute("type",u),t.appendChild(r));(o?(e=0,function(){if(n(o.GetVariable)!=s){var i=o.GetVariable("$version");i&&(i=i.split(" ")[1].split(","),M.pv=[parseInt(i[0],10),parseInt(i[1],10),parseInt(i[2],10)])}else if(e<10)return e++,setTimeout(arguments.callee,10);t.removeChild(r),o=null,S()}):S)()}:S)()}],y=[],v=[],w=[],b=!1,C=!1,E=!0,M=function(){var e=n(h.getElementById)!=s&&n(h.getElementsByTagName)!=s&&n(h.createElement)!=s,t=A.userAgent.toLowerCase(),r=A.platform.toLowerCase(),o=/win/.test(r||t),i=(r=/mac/.test(r||t),t=!!/webkit/.test(t)&&parseFloat(t.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")),!1),a=[0,0,0],p=null;if(n(A.plugins)!=s&&n(A.plugins[l])==c)!(p=A.plugins[l].description)||n(A.mimeTypes)!=s&&A.mimeTypes[u]&&!A.mimeTypes[u].enabledPlugin||(i=!(g=!0),p=p.replace(/^.*\s+(\S+\s+\S+$)/,"$1"),a[0]=parseInt(p.replace(/^(.*)\..*$/,"$1"),10),a[1]=parseInt(p.replace(/^.*\.(.*)\s.*$/,"$1"),10),a[2]=/[a-zA-Z]/.test(p)?parseInt(p.replace(/^.*[a-zA-Z]+(.*)$/,"$1"),10):0);else if(n(f.ActiveXObject)!=s)try{var d=new ActiveXObject("ShockwaveFlash.ShockwaveFlash");d&&(p=d.GetVariable("$version"))&&(i=!0,p=p.split(" ")[1].split(","),a=[parseInt(p[0],10),parseInt(p[1],10),parseInt(p[2],10)])}catch(e){}return{w3:e,pv:a,wk:t,ie:i,win:o,mac:r}}();function I(){if(!b){try{var e=h.getElementsByTagName("body")[0].appendChild(O("span"));e.parentNode.removeChild(e)}catch(e){return}b=!0;for(var t=m.length,n=0;n<t;n++)m[n]()}}function T(e){b?e():m[m.length]=e}function k(e){var t,r,o,i;n(f.addEventListener)!=s?f.addEventListener("load",e,!1):n(h.addEventListener)!=s?h.addEventListener("load",e,!1):n(f.attachEvent)!=s?(o="onload",i=e,(r=f).attachEvent(o,i),w[w.length]=[r,o,i]):"function"==typeof f.onload?(t=f.onload,f.onload=function(){t(),e()}):f.onload=e}function S(){var e=y.length;if(0<e)for(var t=0;t<e;t++){var r=y[t].id,o=y[t].callbackFn,i={success:!1,id:r};if(0<M.pv[0]){var a=_(r);if(a)if(!R(y[t].swfVersion)||M.wk&&M.wk<312)if(y[t].expressInstall&&x()){for(var c={},l=(c.data=y[t].expressInstall,c.width=a.getAttribute("width")||"0",c.height=a.getAttribute("height")||"0",a.getAttribute("class")&&(c.styleclass=a.getAttribute("class")),a.getAttribute("align")&&(c.align=a.getAttribute("align")),{}),u=a.getElementsByTagName("param"),p=u.length,d=0;d<p;d++)"movie"!=u[d].getAttribute("name").toLowerCase()&&(l[u[d].getAttribute("name")]=u[d].getAttribute("value"));N(c,l,r,o)}else{c=f=void 0;var f=a;M.ie&&M.win&&4!=f.readyState?(c=O("div"),f.parentNode.insertBefore(c,f),c.parentNode.replaceChild(j(f),c),f.style.display="none",function(){4==f.readyState?f.parentNode.removeChild(f):setTimeout(arguments.callee,10)}()):f.parentNode.replaceChild(j(f),f),o&&o(i)}else Q(r,!0),o&&(i.success=!0,i.ref=D(r),o(i))}else Q(r,!0),o&&((a=D(r))&&n(a.SetVariable)!=s&&(i.success=!0,i.ref=a),o(i))}}function D(e){var t=null;return(e=_(e))&&"OBJECT"==e.nodeName&&(n(e.SetVariable)!=s?t=e:(e=e.getElementsByTagName(c)[0])&&(t=e)),t}function x(){return!C&&R("6.0.65")&&(M.win||M.mac)&&!(M.wk&&M.wk<312)}function N(i,a,c,l){r=l||null,o={success:!(C=!0),id:c};var u=_(c);u&&(t="OBJECT"==u.nodeName?(e=j(u),null):(e=u,c),i.id=p,(n(i.width)==s||!/%$/.test(i.width)&&parseInt(i.width,10)<310)&&(i.width="310"),(n(i.height)==s||!/%$/.test(i.height)&&parseInt(i.height,10)<137)&&(i.height="137"),h.title=h.title.slice(0,47)+" - Flash Player Installation",l=M.ie&&M.win?"ActiveX":"PlugIn",l="MMredirectURL="+f.location.toString().replace(/&/g,"%26")+"&MMplayerType="+l+"&MMdoctitle="+h.title,n(a.flashvars)!=s?a.flashvars+="&"+l:a.flashvars=l,M.ie&&M.win&&4!=u.readyState&&((l=O("div")).setAttribute("id",c+="SWFObjectNew"),u.parentNode.insertBefore(l,u),u.style.display="none",function(){4==u.readyState?u.parentNode.removeChild(u):setTimeout(arguments.callee,10)}()),B(i,a,c))}function j(e){var t=O("div");if(M.win&&M.ie)t.innerHTML=e.innerHTML;else if(e=e.getElementsByTagName(c)[0]){var n=e.childNodes;if(n)for(var r=n.length,o=0;o<r;o++)1==n[o].nodeType&&"PARAM"==n[o].nodeName||8==n[o].nodeType||t.appendChild(n[o].cloneNode(!0))}return t}function B(e,t,r){var o,i,a,l,p,d=_(r);if(!(M.wk&&M.wk<312)&&d)if(n(e.id)==s&&(e.id=r),M.ie&&M.win){var f,h="";for(f in e)e[f]!=Object.prototype[f]&&("data"==f.toLowerCase()?t.movie=e[f]:"styleclass"==f.toLowerCase()?h+=' class="'+e[f]+'"':"classid"!=f.toLowerCase()&&(h+=" "+f+'="'+e[f]+'"'));var A,g="";for(A in t)t[A]!=Object.prototype[A]&&(g+='<param name="'+A+'" value="'+t[A]+'" />');d.outerHTML='<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"'+h+">"+g+"</object>",v[v.length]=e.id,o=_(e.id)}else{var m,y,w=O(c);for(m in w.setAttribute("type",u),e)e[m]!=Object.prototype[m]&&("styleclass"==m.toLowerCase()?w.setAttribute("class",e[m]):"classid"!=m.toLowerCase()&&w.setAttribute(m,e[m]));for(y in t)t[y]!=Object.prototype[y]&&"movie"!=y.toLowerCase()&&(i=w,l=t[a=y],p=void 0,(p=O("param")).setAttribute("name",a),p.setAttribute("value",l),i.appendChild(p));d.parentNode.replaceChild(w,d),o=w}return o}function L(e){var t=_(e);t&&"OBJECT"==t.nodeName&&(M.ie&&M.win?(t.style.display="none",function(){if(4==t.readyState){var n=_(e);if(n){for(var r in n)"function"==typeof n[r]&&(n[r]=null);n.parentNode.removeChild(n)}}else setTimeout(arguments.callee,10)}()):t.parentNode.removeChild(t))}function _(e){var t=null;try{t=h.getElementById(e)}catch(e){}return t}function O(e){return h.createElement(e)}function R(e){var t=M.pv;return(e=e.split("."))[0]=parseInt(e[0],10),e[1]=parseInt(e[1],10)||0,e[2]=parseInt(e[2],10)||0,t[0]>e[0]||t[0]==e[0]&&t[1]>e[1]||t[0]==e[0]&&t[1]==e[1]&&t[2]>=e[2]}function P(e,t,r,o){var l;M.ie&&M.mac||(l=h.getElementsByTagName("head")[0])&&(r=r&&"string"==typeof r?r:"screen",o&&(a=i=null),i&&a==r||((o=O("style")).setAttribute("type","text/css"),o.setAttribute("media",r),i=l.appendChild(o),M.ie&&M.win&&n(h.styleSheets)!=s&&0<h.styleSheets.length&&(i=h.styleSheets[h.styleSheets.length-1]),a=r),M.ie&&M.win?i&&n(i.addRule)==c&&i.addRule(e,t):i&&n(h.createTextNode)!=s&&i.appendChild(h.createTextNode(e+" {"+t+"}")))}function Q(e,t){E&&(t=t?"visible":"hidden",b&&_(e)?_(e).style.visibility=t:P("#"+e,"visibility:"+t))}function F(e){return null!=/[\\\"<>\.;]/.exec(e)&&("undefined"==typeof encodeURIComponent?"undefined":n(encodeURIComponent))!=s?encodeURIComponent(e):e}return M.w3&&((n(h.readyState)!=s&&"complete"==h.readyState||n(h.readyState)==s&&(h.getElementsByTagName("body")[0]||h.body))&&I(),b||(n(h.addEventListener)!=s&&h.addEventListener("DOMContentLoaded",I,!1),M.ie&&M.win&&(h.attachEvent(d,function(){"complete"==h.readyState&&(h.detachEvent(d,arguments.callee),I())}),f==top)&&function(){if(!b){try{h.documentElement.doScroll("left")}catch(e){return setTimeout(arguments.callee,0)}I()}}(),M.wk&&function(){b||(/loaded|complete/.test(h.readyState)?I():setTimeout(arguments.callee,0))}(),k(I))),M.ie&&M.win&&window.attachEvent("onunload",function(){for(var e=w.length,t=0;t<e;t++)w[t][0].detachEvent(w[t][1],w[t][2]);for(var n,r,o=v.length,i=0;i<o;i++)L(v[i]);for(n in M)M[n]=null;for(r in M=null,swfobject)swfobject[r]=null;swfobject=null}),{registerObject:function(e,t,n,r){var o;M.w3&&e&&t?((o={}).id=e,o.swfVersion=t,o.expressInstall=n,o.callbackFn=r,y[y.length]=o,Q(e,!1)):r&&r({success:!1,id:e})},getObjectById:function(e){if(M.w3)return D(e)},embedSWF:function(e,t,r,o,i,a,l,u,p,d){var f={success:!1,id:t};M.w3&&!(M.wk&&M.wk<312)&&e&&t&&r&&o&&i?(Q(t,!1),T(function(){r+="",o+="";var h={};if(p&&n(p)===c)for(var A in p)h[A]=p[A];h.data=e,h.width=r,h.height=o;var g={};if(u&&n(u)===c)for(var m in u)g[m]=u[m];if(l&&n(l)===c)for(var y in l)n(g.flashvars)!=s?g.flashvars+="&"+y+"="+l[y]:g.flashvars=y+"="+l[y];if(R(i)){var v=B(h,g,t);h.id==t&&Q(t,!0),f.success=!0,f.ref=v}else{if(a&&x())return h.data=a,void N(h,g,t,d);Q(t,!0)}d&&d(f)})):d&&d(f)},switchOffAutoHideShow:function(){E=!1},ua:M,getFlashPlayerVersion:function(){return{major:M.pv[0],minor:M.pv[1],release:M.pv[2]}},hasFlashPlayerVersion:R,createSWF:function(e,t,n){if(M.w3)return B(e,t,n)},showExpressInstall:function(e,t,n,r){M.w3&&x()&&N(e,t,n,r)},removeSWF:function(e){M.w3&&L(e)},createCSS:function(e,t,n,r){M.w3&&P(e,t,n,r)},addDomLoadEvent:T,addLoadEvent:k,getQueryParamValue:function(e){var t=h.location.search||h.location.hash;if(t){if(/\?/.test(t)&&(t=t.split("?")[1]),null==e)return F(t);for(var n=t.split("&"),r=0;r<n.length;r++)if(n[r].substring(0,n[r].indexOf("="))==e)return F(n[r].substring(n[r].indexOf("=")+1))}return""},expressInstallCallback:function(){var n;C&&((n=_(p))&&e&&(n.parentNode.replaceChild(e,n),t&&(Q(t,!0),M.ie)&&M.win&&(e.style.display="block"),r)&&r(o),C=!1)}}}()},function(e,t,n){(function(n){var r,o;function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}
/*!

   Flowplayer v6.0.5 (Wednesday, 13. January 2016 09:17AM) | flowplayer.org/license

*/function a(e){return function(){if("object"===i(o)&&"function"==typeof o[e])return o[e].apply(o,Array.prototype.slice.call(arguments))}}"undefined"==typeof console&&(window._console=window.console,window.console=(o=window._console,{log:a("log"),debug:a("debug"),info:a("info"),warn:a("warn"),exception:a("exception"),assert:a("assert"),dir:a("dir"),dirxml:a("dirxml"),trace:a("trace"),group:a("group"),groupCollapsed:a("groupCollapsed"),groupEnd:a("groupEnd"),profile:a("profile"),profileEnd:a("profileEnd"),count:a("count"),clear:a("clear"),time:a("time"),timeEnd:a("timeEnd"),timeStamp:a("timeStamp"),table:a("table"),error:a("error"),memory:a("memory"),markTimeline:a("markTimeline"),timeline:a("timeline"),timelineEnd:a("timelineEnd")}));var s,c,l,u,p,d,f,h,A,g,m,y,v,w,b,C,E,M,I,T,k,S={keyup:null,keydown:null,mousedown:null,mouseup:null,mousemove:null};function D(e,t,n,r){for(var o=n.slice(),a=((n=t).currentTarget=e,n.eventPhase=n.target===n.currentTarget?2:3,n),s=0,c=o.length;s<c&&("object"==(void 0===(handler=o[s])?"undefined":i(handler))&&"function"==typeof handler.handleEvent?handler.handleEvent(a):handler.call(e,a),!a.stoppedImmediatePropagation);s++);return t=!a.stoppedPropagation,r&&t&&e.parentNode?e.parentNode.dispatchEvent(a):!a.defaultPrevented}function x(e,t){return{configurable:!0,get:e,set:t}}function N(e,t,n){var r=g(t||e,n);h(e,"textContent",x(function(){return r.get.call(this)},function(e){r.set.call(this,e)}))}function j(e,t){for(var n=e.length;n--&&e[n]!==t;);return n}function B(){if("BR"===this.tagName)return"\n";for(var e=this.firstChild,t=[];e;)8!==e.nodeType&&7!==e.nodeType&&t.push(e.textContent),e=e.nextSibling;return t.join("")}function L(e){!l&&w.test(document.readyState)&&(l=!l,document.detachEvent(u,L),(e=document.createEvent("Event")).initEvent(p,!0,!0),document.dispatchEvent(e))}function _(e){for(var t;t=this.lastChild;)this.removeChild(t);null!=e&&this.appendChild(document.createTextNode(e))}function O(e,t){return(t=t||s.event).target||(t.target=t.srcElement||t.fromElement||document),t.timeStamp||(t.timeStamp=(new Date).getTime()),t}function R(e){this._=e}function P(){}function Q(e,t){return t.toUpperCase()}
/*! (C) WebReflection Mit Style License */s=this,document.createEvent||(l=!(c=!0),u="onreadystatechange",p="DOMContentLoaded",d="__IE8__"+Math.random(),f=s.Object,h=f.defineProperty||function(e,t,n){e[t]=n.value},A=f.defineProperties||function(e,t){for(var n in t)if(m.call(t,n))try{h(e,n,t[n])}catch(e){s.console}},g=f.getOwnPropertyDescriptor,m=f.prototype.hasOwnProperty,y=s.Element.prototype,k=s.Text.prototype,v=/^[a-z]+$/,w=/loaded|complete/,b={},C=document.createElement("div"),N(s.HTMLCommentElement.prototype,y,"nodeValue"),N(s.HTMLScriptElement.prototype,null,"text"),N(k,null,"nodeValue"),N(s.HTMLTitleElement.prototype,null,"text"),h(s.HTMLStyleElement.prototype,"textContent",(T=g(s.CSSStyleSheet.prototype,"cssText"),x(function(){return T.get.call(this.styleSheet)},function(e){T.set.call(this.styleSheet,e)}))),A(y,{textContent:{get:B,set:_},firstElementChild:{get:function(){for(var e=this.childNodes||[],t=0,n=e.length;t<n;t++)if(1==e[t].nodeType)return e[t]}},lastElementChild:{get:function(){for(var e=this.childNodes||[],t=e.length;t--;)if(1==e[t].nodeType)return e[t]}},previousElementSibling:{get:function(){for(var e=this.previousSibling;e&&1!=e.nodeType;)e=e.previousSibling;return e}},nextElementSibling:{get:function(){for(var e=this.nextSibling;e&&1!=e.nodeType;)e=e.nextSibling;return e}},childElementCount:{get:function(){for(var e=0,t=this.childNodes||[],n=t.length;n--;e+=1==t[n].nodeType);return e}},addEventListener:{value:function(e,t,n){var r,o,i=this,a="on"+e,s=(o=(o=i[d]||h(i,d,{value:{}})[d])[a]||(o[a]={})).h||(o.h=[]);if(!m.call(o,"w")){if(o.w=function(e){return e[d]||D(i,O(0,e),s,!1)},!m.call(b,a))if(v.test(e))try{(r=document.createEventObject())[d]=!0,9!=i.nodeType&&null==i.parentNode&&C.appendChild(i),i.fireEvent(a,r),b[a]=!0}catch(r){for(b[a]=!1;C.hasChildNodes();)C.removeChild(C.firstChild)}else b[a]=!1;(o.n=b[a])&&i.attachEvent(a,o.w)}j(s,t)<0&&s[n?"unshift":"push"](t)}},dispatchEvent:{value:function(e){var t,n=this,r="on"+e.type,o=!!(t=(t=n[d])&&t[r]);return e.target||(e.target=n),o?t.n?n.fireEvent(r,e):D(n,e,t.h,!0):(o=n.parentNode)&&o.dispatchEvent(e),!e.defaultPrevented}},removeEventListener:{value:function(e,t,n){var r;-1<(r=(e=(r=(r=this[d])&&r["on"+e])&&r.h)?j(e,t):-1)&&e.splice(r,1)}}}),A(k,{addEventListener:{value:y.addEventListener},dispatchEvent:{value:y.dispatchEvent},removeEventListener:{value:y.removeEventListener}}),A(s.XMLHttpRequest.prototype,{addEventListener:{value:function(e,t,n){var r,o=this,i="on"+e;j(r=(r=(r=o[d]||h(o,d,{value:{}})[d])[i]||(r[i]={})).h||(r.h=[]),t)<0&&(o[i]||(o[i]=function(){var t=document.createEvent("Event");t.initEvent(e,!0,!0),o.dispatchEvent(t)}),r[n?"unshift":"push"](t))}},dispatchEvent:{value:function(e){var t,n="on"+e.type;return!!(t=(t=this[d])&&t[n])&&(t.n?this.fireEvent(n,e):D(this,e,t.h,!0))}},removeEventListener:{value:y.removeEventListener}}),A(s.Event.prototype,{bubbles:{value:!0,writable:!0},cancelable:{value:!0,writable:!0},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0,this.returnValue=!1)}},stopPropagation:{value:function(){this.stoppedPropagation=!0,this.cancelBubble=!0}},stopImmediatePropagation:{value:function(){this.stoppedImmediatePropagation=!0,this.stopPropagation()}},initEvent:{value:function(e,t,n){this.type=e,this.bubbles=!!t,this.cancelable=!!n,this.bubbles||this.stopPropagation()}}}),A(s.HTMLDocument.prototype,{textContent:{get:function(){return 11===this.nodeType?B.call(this):null},set:function(e){11===this.nodeType&&_.call(this,e)}},addEventListener:{value:function(e,t,n){var r=this;y.addEventListener.call(r,e,t,n),c&&e===p&&!w.test(r.readyState)&&(c=!1,r.attachEvent(u,L),s==top)&&function e(t){try{r.documentElement.doScroll("left"),L()}catch(t){setTimeout(e,50)}}()}},dispatchEvent:{value:y.dispatchEvent},removeEventListener:{value:y.removeEventListener},createEvent:{value:function(e){if("Event"!==e)throw new Error("unsupported "+e);return(e=document.createEventObject()).timeStamp=(new Date).getTime(),e}}}),A(s.Window.prototype,{getComputedStyle:{value:(E=/^(?:[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|))(?!px)[a-z%]+$/,M=/^(top|right|bottom|left)$/,I=/\-([a-z])/g,R.prototype.getPropertyValue=function(e){var t,n,r=(i=this._).style,o=i.currentStyle,i=i.runtimeStyle,a=(o||r)[e=("float"===e?"style-float":e).replace(I,Q)];return E.test(a)&&!M.test(e)&&(t=r.left,(n=i&&i.left)&&(i.left=o.left),r.left="fontSize"===e?"1em":a,a=r.pixelLeft+"px",r.left=t,n)&&(i.left=n),null==a?a:a+""||"auto"},P.prototype.getPropertyValue=function(){return null},function(e,t){return t?new P:new R(e)})},addEventListener:{value:function(e,t,n){var r,o=s;o[e="on"+e]||(o[e]=function(e){return D(o,O(0,e),r,!1)}),j(r=o[e][d]||(o[e][d]=[]),t)<0&&r[n?"unshift":"push"](t)}},dispatchEvent:{value:function(e){var t=s["on"+e.type];return!t||!1!==t.call(s,e)&&!e.defaultPrevented}},removeEventListener:{value:function(e,t,n){-1<(t=(e=(s["on"+e]||f)[d])?j(e,t):-1)&&e.splice(t,1)}}})),k=function(){return function e(t,n,r){function o(a,s){if(!n[a]){if(!t[a]){if(i)return i(a,!0);var c=new Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c}c=n[a]={exports:{}},t[a][0].call(c.exports,function(e){return o(t[a][1][e]||e)},c,c.exports,e,t,n,r)}return n[a].exports}for(var i=!1,a=0;a<r.length;a++)o(r[a]);return o}({1:[function(e,t,n){"use strict";var r=t.exports={},o=e("class-list"),a=window.jQuery,s=e("punycode"),c=e("computed-style");function l(e){return e.replace(/-[a-z]/g,function(e){return e[1].toUpperCase()})}r.noop=function(){},r.identity=function(e){return e},r.removeNode=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},r.find=function(e,t){return a?a(e,t).toArray():(t=t||document,Array.prototype.map.call(t.querySelectorAll(e),function(e){return e}))},r.text=function(e,t){e["innerText"in e?"innerText":"textContent"]=t},r.findDirect=function(e,t){return r.find(e,t).filter(function(e){return e.parentNode===t})},r.hasClass=function(e,t){return o(e).contains(t)},r.isSameDomain=function(e){var t=window.location;e=r.createElement("a",{href:e});return t.hostname===e.hostname&&t.protocol===e.protocol&&t.port===e.port},r.css=function(e,t,n){return"object"===i(t)?Object.keys(t).forEach(function(n){r.css(e,n,t[n])}):void 0!==n?""===n?e?e.style.removeProperty(t):void 0:e?e.style.setProperty(t,n):void 0:e?c(e,t):void 0},r.createElement=function(e,t,n){try{var o,i=document.createElement(e);for(o in t)t.hasOwnProperty(o)&&("css"===o?r.css(i,t[o]):r.attr(i,o,t[o]));return i.innerHTML=n||"",i}catch(o){if(a)return a("<"+e+">"+n+"</"+e+">").attr(t)[0];throw o}},r.toggleClass=function(e,t,n){e&&(e=o(e),void 0===n?e.toggle(t):n?e.add(t):e.remove(t))},r.addClass=function(e,t){return r.toggleClass(e,t,!0)},r.removeClass=function(e,t){return r.toggleClass(e,t,!1)},r.append=function(e,t){return e.appendChild(t),e},r.appendTo=function(e,t){return r.append(t,e),e},r.prepend=function(e,t){e.insertBefore(t,e.firstChild)},r.insertAfter=function(e,t,n){t==r.lastChild(e)&&e.appendChild(n),t=Array.prototype.indexOf.call(e.children,t),e.insertBefore(n,e.children[t+1])},r.html=function(e,t){(e=e.length?e:[e]).forEach(function(e){e.innerHTML=t})},r.attr=function(e,t,n){if(r.hasOwnOrPrototypeProperty(e,t="class"===t?"className":t))try{e[t]=n}catch(r){if(!a)throw r;a(e).attr(t,n)}else!1===n?e.removeAttribute(t):e.setAttribute(t,n);return e},r.prop=function(e,t,n){if(void 0===n)return e&&e[t];e[t]=n},r.offset=function(e){var t=e.getBoundingClientRect();return e.offsetWidth/e.offsetHeight>e.clientWidth/e.clientHeight?{left:100*t.left,right:100*t.right,top:100*t.top,bottom:100*t.bottom,width:100*t.width,height:100*t.height}:t},r.width=function(e,t){return t?e.style.width=(""+t).replace(/px$/,"")+"px":void 0===(t=r.offset(e).width)?e.offsetWidth:t},r.height=function(e,t){return t?e.style.height=(""+t).replace(/px$/,"")+"px":void 0===(t=r.offset(e).height)?e.offsetHeight:t},r.lastChild=function(e){return e.children[e.children.length-1]},r.hasParent=function(e,t){for(var n=e.parentElement;n;){if(r.matches(n,t))return!0;n=n.parentElement}return!1},r.createAbsoluteUrl=function(e){return r.createElement("a",{href:e}).href},r.xhrGet=function(e,t,n){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(4===this.readyState)return 400<=this.status?n():void t(this.responseText)},r.open("get",e,!0),r.send()},r.pick=function(e,t){var n={};return t.forEach(function(t){e.hasOwnProperty(t)&&(n[t]=e[t])}),n},r.hostname=function(e){return s.toUnicode(e||window.location.hostname)},r.browser={webkit:"WebkitAppearance"in document.documentElement.style},r.getPrototype=function(e){return Object.getPrototypeOf?Object.getPrototypeOf(e):e.__proto__},r.hasOwnOrPrototypeProperty=function(e,t){for(var n=e;n;){if(Object.prototype.hasOwnProperty.call(n,t))return!0;n=r.getPrototype(n)}return!1},r.matches=function(e,t){var n=Element.prototype;return(n.matches||n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=0;t[n]&&t[n]!==this;)n++;return!!t[n]}).call(e,t)},void 0!==(t=window.CSSStyleDeclaration.prototype).setAttribute&&(t.setProperty=function(e,t){return this.setAttribute(l(e),String(t))},t.getPropertyValue=function(e){return this.getAttribute(l(e))||null},t.removeProperty=function(e){var t=this.getPropertyValue(e);return this.removeAttribute(l(e)),t})},{"class-list":22,"computed-style":24,punycode:21}],2:[function(e,t,n){"use strict";var r=e("../common");t.exports=function(e,t,n,o){n=n||"opaque";var i="obj"+(""+Math.random()).slice(2,15),a='<object class="fp-engine" id="'+i+'" name="'+i+'" ',s=-1<navigator.userAgent.indexOf("MSIE"),c=(a+=s?'classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">':' data="'+e+'" type="application/x-shockwave-flash">',{width:"100%",height:"100%",hasPriority:!0,allowscriptaccess:"always",allowFullScreenInteractive:"true",wmode:n,quality:"high",flashvars:"",movie:e+(s?"?"+i:""),name:i});"transparent"!==n&&(c.bgcolor=o||"#333333"),Object.keys(t).forEach(function(e){c.flashvars+=e+"="+t[e]+"&"}),Object.keys(c).forEach(function(e){a+='<param name="'+e+'" value="'+c[e]+'"/>'}),a+="</object>",e=r.createElement("div",{},a);return r.find("object",e)},window.attachEvent&&window.attachEvent("onbeforeunload",function(){window.__flash_savedUnloadHandler=window.__flash_unloadHandler=function(){}})},{"../common":1}],3:[function(e,t,n){"use strict";var r,o=e("../flowplayer"),i=e("../common"),a=e("./embed"),s=e("extend-object"),c=e("bean");function l(e){return/^https?:/.test(e)}(r=function(e,t){var n,u,p,d,f,h=e.conf,A=(e.video,window,{engineName:r.engineName,pick:function(t){var n=s({},function(){if(o.support.flashVideo){for(var n,r,i=0;i<t.length;i++)if(r=t[i],/mp4|flv|flash/i.test(r.type)&&(n=r),(n=e.conf.swfHls&&/mpegurl/i.test(r.type)?r:n)&&!/mp4/i.test(n.type))return n;return n}}());if(n)return!n.src||l(n.src)||e.conf.rtmp||n.rtmp||(n.src=i.createAbsoluteUrl(n.src)),n},load:function(r){function m(e){return e.replace(/&amp;/g,"%26").replace(/&/g,"%26").replace(/=/g,"%3D")}function y(){i.removeNode(E)}n=r;var v,w,b,C,E=i.findDirect("video",t)[0]||i.find(".fp-player > video",t)[0],M=r.src,I=l(M),T=(o.support.video&&i.prop(E,"autoplay")&&r.sources.some(function(e){return!!E.canPlayType(e.type)})?c.one(E,"timeupdate",y):y(),r.rtmp||h.rtmp);I||T||(M=i.createAbsoluteUrl(M)),p&&g(r)&&p.data!==h.swfHls&&A.unload(),void 0===r.captureInputEvent&&(r.captureInputEvent=!1),void 0===r.isSplashStream&&(r.isSplashStream=!1),p?(["live","preload","loop","captureInputEvent","customRotate"].forEach(function(e){r.hasOwnProperty(e)&&p.__set(e,r[e])}),Object.keys(r.flashls||{}).forEach(function(e){p.__set("hls_"+e,r.flashls[e])}),!I&&T?p.__set("rtmp",T.url||T):(p.__get("rtmp"),p.__set("rtmp",null)),void 0!==r.frameDelayDetector&&(["durationInSecond","detectIntervalInMili","triggerPlayTimeDelayInMili","frozenTimeInSecond","frameDelayInfoReportIntervelInSecond","isReportFrameDelayDetailInfo","triggerDelayTimeSinceLastCheckInMili","triggerDropFrameRate","startDetectDelayTimeInSecond"].forEach(function(e){r.frameDelayDetector.hasOwnProperty(e)&&p.__set(e,r.frameDelayDetector[e])}),d=d||"hmTriggerDelayEventCallback"+(""+Math.random()).slice(3,15),v=r.frameDelayDetector.callback,window[d]=v,p.__set("triggerCallbackId",d),f=f||"hmTriggerReportFrameDelayCallback"+(""+Math.random()).slice(3,15),w=r.frameDelayDetector.reportCallback,window[f]=w,p.__set("triggerReportFrameDelayCallbackId",f)),p.__get("isSplashStream"),p.__set("isSplashStream",r.isSplashStream),p.__play(M,!0)):(u="fpCallback"+(""+Math.random()).slice(3,15),M=m(M),b={hostname:h.embedded?i.hostname(h.hostname):i.hostname(location.hostname),url:M,captureInputEvent:r.captureInputEvent,customRotate:r.customRotate,isSplashStream:r.isSplashStream,callback:u},t.getAttribute("data-origin")&&(b.origin=t.getAttribute("data-origin")),void 0!==r.frameDelayDetector&&(b.durationInSecond=r.frameDelayDetector.durationInSecond,b.detectIntervalInMili=r.frameDelayDetector.detectIntervalInMili,b.triggerPlayTimeDelayInMili=r.frameDelayDetector.triggerPlayTimeDelayInMili,b.startDetectDelayTimeInSecond=r.frameDelayDetector.startDetectDelayTimeInSecond,b.frozenTimeInSecond=r.frameDelayDetector.frozenTimeInSecond,b.frameDelayInfoReportIntervelInSecond=r.frameDelayDetector.frameDelayInfoReportIntervelInSecond,b.isReportFrameDelayDetailInfo=r.frameDelayDetector.isReportFrameDelayDetailInfo,b.triggerDelayTimeSinceLastCheckInMili=r.frameDelayDetector.triggerDelayTimeSinceLastCheckInMili,b.triggerDropFrameRate=r.frameDelayDetector.triggerDropFrameRate,d="hmTriggerDelayEventCallback"+(""+Math.random()).slice(3,15),b.triggerCallbackId=d,v=r.frameDelayDetector.callback,f="hmTriggerReportFrameDelayCallback"+(""+Math.random()).slice(3,15),b.triggerReportFrameDelayCallbackId=f,w=r.frameDelayDetector.reportCallback),["proxy","key","autoplay","preload","subscribe","live","rotate","loop","debug","splash","poster","rtmpt"].forEach(function(e){h.hasOwnProperty(e)&&(b[e]=h[e]),r.hasOwnProperty(e)&&(b[e]=r[e]),(h.rtmp||{}).hasOwnProperty(e)&&(b[e]=(h.rtmp||{})[e]),(r.rtmp||{}).hasOwnProperty(e)&&(b[e]=(r.rtmp||{})[e])}),h.rtmp&&(b.rtmp=h.rtmp.url||h.rtmp),r.rtmp&&(b.rtmp=r.rtmp.url||r.rtmp),Object.keys(r.flashls||{}).forEach(function(e){var t=r.flashls[e];b["hls_"+e]=t}),void 0!==h.bufferTime&&(b.bufferTime=h.bufferTime),I&&delete b.rtmp,b.rtmp&&(b.rtmp=m(b.rtmp)),0===(T=h.bgcolor||i.css(t,"background-color")||"").indexOf("rgb")?C=function(e){function t(e){return("0"+parseInt(e).toString(16)).slice(-2)}if(e=e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/))return"#"+t(e[1])+t(e[2])+t(e[3])}(T):0===T.indexOf("#")&&(C=7===(M=T).length?M:"#"+M.split("").slice(1).map(function(e){return e+e}).join("")),b.initialVolume=e.volumeLevel,b.debug=window.FLOWPLAYER_LOG,I=g(r)?h.swfHls:h.swf,p=a(I,b,h.wmode,C)[0],T=i.find(".fp-player",t)[0],i.prepend(T,p),setTimeout(function(){try{if(!p.PercentLoaded())return e.trigger("error",[e,{code:7,url:h.swf}])}catch(e){}},5e3),setTimeout(function(){void 0===p.PercentLoaded&&e.trigger("flashdisabled",[e])},2e3),e.off("resume.flashhack").on("resume.flashhack",function(){var t=setTimeout(function(){e.playing&&e.trigger("flashdisabled",[e])},2e3);e.one("progress",function(){clearTimeout(t)})}),p.pollInterval=setInterval(function(){var t;p&&(t=p.__status?p.__status():null)&&(e.playing&&t.time&&t.time!==e.video.time&&e.trigger("progress",[e,t.time]),r.buffer=t.buffer/r.bytes*r.duration,e.trigger("buffer",[e,r.buffer]),!r.buffered)&&0<t.time&&(r.buffered=!0,e.trigger("buffered",[e]))},250),d&&(window[d]=v),f&&(window[f]=w),r.logTag,window[u]=function(t,r,o){var i=n,a=(h.debug&&0===t.indexOf("debug")&&r&&r.length,{type:t});switch(window.FLOWPLAYER_LOG,t){case"ready":r=s(i,r);break;case"click":a.flash=!0;break;case"keydown":return void(S.keydown&&S.keydown(r));case"keyup":return void(S.keyup&&S.keyup(r));case"mousedown":return void(S.mousedown&&S.mousedown(r,o));case"mouseup":return void(S.mouseup&&S.mouseup(r,o));case"mousemove":return void(S.mousemove&&S.mousemove(r,o));case"seek":i.time=r;break;case"status":e.trigger("progress",[e,r.time]),r.buffer<i.bytes&&!i.buffered?(i.buffer=r.buffer/i.bytes*i.duration,e.trigger("buffer",i.buffer)):i.buffered||(i.buffered=!0,e.trigger("buffered"))}"buffered"!=t&&"unload"!==t?setTimeout(function(){e.trigger(a,[e,r])},1):"unload"===t&&e.trigger(a,[e,r])})},speed:i.noop,unload:function(){p&&p.__unload&&p.__unload();try{u&&window[u]&&delete window[u],d&&window[d]&&delete window[d],f&&window[f]&&delete window[f]}catch(e){}i.find("object",t).forEach(i.removeNode),p=0,e.off(".flashengine"),clearInterval(p.pollInterval)},left:function(){return p.__get("hm_left")},top:function(){return p.__get("hm_top")}});function g(e){return/application\/x-mpegurl/i.test(e.type)}return["pause","resume","seek","volume"].forEach(function(t){A[t]=function(n){try{e.ready&&(void 0===n?"pause"==t?p["__"+t](!1):p["__"+t]():p["__"+t](n))}catch(n){if(void 0===p["__"+t])return e.trigger("flashdisabled",[e]);throw n}}}),A}).engineName="flash",r.canPlay=function(e,t){return o.support.flashVideo&&/video\/(mp4|flash|flv)/i.test(e)||o.support.flashVideo&&t.swfHls&&/mpegurl/i.test(e)},o.engines.push(r)},{"../common":1,"../flowplayer":18,"./embed":2,bean:20,"extend-object":26}],4:[function(e,t,n){"use strict";var r,o=e("../flowplayer"),i=e("bean"),a=e("class-list"),s=e("extend-object"),c=e("../common"),l=document.createElement("video"),u={ended:"finish",pause:"pause",play:"resume",progress:"buffer",timeupdate:"progress",volumechange:"volume",ratechange:"speed",seeked:"seek",loadeddata:"ready",error:"error",dataunavailable:"error",webkitendfullscreen:!o.support.inlineVideo&&"unload"};function p(e,t){return t=t||100,Math.round(e*t)/t}function d(e){return/mpegurl/i.test(e)?"application/x-mpegurl":e}function f(e){return/^(video|application)/i.test(e)||(e=d(e)),!!l.canPlayType(e).replace("no","")}function h(e,t,n,o){var i;return void 0===t&&(t=!0),void 0===n&&(n="none"),(o=void 0===o||o)&&r?(r.type=d(e.type),r.src=e.src,c.find("track",r).forEach(c.removeNode),r.removeAttribute("crossorigin"),r):((i=document.createElement("video")).src=e.src,i.type=d(e.type),i.className="fp-engine",i.autoplay=!!t&&"autoplay",i.preload=n,i.setAttribute("x-webkit-airplay","allow"),o&&(r=i),i)}var A=function(e,t){var n,l,d,g=c.findDirect("video",t)[0]||c.find(".fp-player > video",t)[0],m=o.support,y=(c.find("track",g)[0],e.conf);return n={engineName:A.engineName,pick:function(e){var t=function(){if(m.video){if(y.videoTypePreference&&(n=e,r=y.videoTypePreference,n=(n=n.filter(function(e){return e.type===r})).length?n[0]:null))return n;for(var t=0;t<e.length;t++)if(f(e[t].type))return e[t]}var n,r}();if(t)return"string"==typeof t.src&&(t.src=c.createAbsoluteUrl(t.src)),t},load:function(r){var o=!1,A=c.find(".fp-player",t)[0],v=!1;y.splash&&!g?(g=h(r),c.prepend(A,g),o=!0):g?(a(g).add("fp-engine"),c.find("source,track",g).forEach(c.removeNode),e.conf.nativesubtitles||c.attr(g,"crossorigin",!1),v=g.src===r.src):(g=h(r,!!r.autoplay||!!y.autoplay,y.clip.preload||"metadata",!1),c.prepend(A,g),o=!0),m.inlineVideo||c.css(g,{position:"absolute",top:"-9999em"}),i.off(g,"timeupdate",c.noop),i.on(g,"timeupdate",c.noop),c.prop(g,"loop",!(!r.loop&&!y.loop)),void 0!==d&&(g.volume=d),(e.video.src&&r.src!=e.video.src||r.index)&&c.attr(g,"autoplay","autoplay"),g.src=r.src,g.type=r.type,n._listeners=function(n,r,o){var d,h=t.getAttribute("data-flowplayer-instance-id");if(!n.listeners||!n.listeners.hasOwnProperty(h))return(n.listeners||(n.listeners={}))[h]=o,i.on(r,"error",function(t){try{f(t.target.getAttribute("type"))&&e.trigger("error",[e,{code:4,video:s(o,{src:n.src,url:n.src})}])}catch(t){}}),e.on("shutdown",function(){i.off(r)}),d={},Object.keys(u).forEach(function(r){var i,f=u[r];f&&(t.addEventListener(r,i=function(i){var u;if(o=n.listeners[h],i.target&&a(i.target).contains("fp-engine")&&(y.debug&&/progress/.test(f),(e.ready||/ready|error/.test(f))&&f&&c.find("video",t).length))if("unload"===f)e.unload();else{var d=function(){e.trigger(f,[e,u])};switch(f){case"ready":u=s(o,{duration:n.duration,width:n.videoWidth,height:n.videoHeight,url:n.currentSrc,src:n.currentSrc});try{u.seekable=!e.live&&/mpegurl/i.test(o&&o.type||"")&&n.duration||n.seekable&&n.seekable.end(null)}catch(i){}if(l=l||setInterval(function(){try{u.buffer=n.buffered.end(null)}catch(e){}u.buffer&&(p(u.buffer,1e3)<p(u.duration,1e3)&&!u.buffered?e.trigger("buffer",i):u.buffered||(u.buffered=!0,e.trigger("buffer",i).trigger("buffered",i),clearInterval(l),l=0))},250),e.live||u.duration||m.hlsDuration||"loadeddata"!==r)break;return n.addEventListener("durationchange",function e(){u.duration=n.duration;try{u.seekable=n.seekable&&n.seekable.end(null)}catch(e){}d(),n.removeEventListener("durationchange",e),a(t).remove("is-live")}),void n.addEventListener("timeupdate",function r(){e.ready||n.duration||(u.duration=0,a(t).add("is-live"),d()),n.removeEventListener("timeupdate",r)});case"progress":case"seek":if(e.video.duration,0<n.currentTime||e.live)u=Math.max(n.currentTime,0);else if("progress"==f)return;break;case"speed":u=p(n.playbackRate);break;case"volume":u=p(n.volume);break;case"error":try{(u=(i.srcElement||i.originalTarget).error).video=s(o,{src:n.src,url:n.src})}catch(i){return}}d()}},!0),d[r]||(d[r]=[]),d[r].push(i))}),d;n.listeners[h]=o}(g,c.find("source",g).concat(g),r),("none"==y.clip.preload||"mpegurl"==r.type)&&m.zeropreload&&m.dataload||g.load(),(o||v)&&g.load(),g.paused&&(r.autoplay||y.autoplay)&&g.play()},pause:function(){g.pause()},resume:function(){g.play()},speed:function(e){g.playbackRate=e},seek:function(t){try{var n=e.paused;g.currentTime=t,n&&g.pause()}catch(t){}},volume:function(e){d=e,g&&(g.volume=e)},unload:function(){c.find("video.fp-engine",t).forEach(c.removeNode),m.cachedVideoTag||(r=null),l=clearInterval(l);var e=t.getAttribute("data-flowplayer-instance-id");delete g.listeners[e],g=0,n._listeners&&Object.keys(n._listeners).forEach(function(e){n._listeners[e].forEach(function(n){t.removeEventListener(e,n,!0)})})}}};A.canPlay=function(e){return o.support.video&&f(e)},A.engineName="html5",o.engines.push(A)},{"../common":1,"../flowplayer":18,bean:20,"class-list":22,"extend-object":26}],5:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("./resolve").TYPE_RE,i=e("scriptjs"),a=e("bean");r(function(e,t){var n,r,s,c=e.conf.analytics,l=0,u=0;c&&("undefined"==typeof _gat&&i("//google-analytics.com/ga.js"),r=function(){var e=_gat._getTracker(c);return e._setAllowLinker(!0),e},e.bind("load unload",s=function(i,a,s){s=s||e.video,l&&"undefined"!=typeof _gat&&(r()._trackEvent("Video / Seconds played",e.engine.engineName+"/"+s.type,s.title||t.getAttribute("title")||s.src.split("/").slice(-1)[0].replace(o,""),Math.round(l/1e3)),l=0,n)&&(clearTimeout(n),n=null)}).bind("progress",function(){e.seeking||(l+=u?+new Date-u:0,u=+new Date),n=n||setTimeout(function(){n=null,r()._trackEvent("Flowplayer heartbeat","Heartbeat","",0,!0)},6e5)}).bind("pause",function(){u=0}),e.bind("shutdown",function(){a.off(window,"unload",s)}),a.on(window,"unload",s))})},{"../flowplayer":18,"./resolve":13,bean:20,scriptjs:29}],6:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("class-list"),i=e("../common"),a=e("bean");r(function(e,t){var n=/ ?cue\d+ ?/,r=!1;function s(e){t.className=t.className.replace(n," "),0<=e&&o(t).add("cue"+e)}function c(t){var n=e.cuepoints.indexOf(t);s((t=isNaN(t)?t:{time:t}).index=n),e.trigger("cuepoint",[e,t])}var l={},u=-.125;function p(t){return(t=t&&!isNaN(t.time)?t.time:t)<0&&(t=e.video.duration+t),.125*Math.round(t/.125)}e.on("progress",function(e,t,n){if(!r)for(var o=p(n);u<o;)l[u+=.125]&&l[u].forEach(c)}).on("unload",s).on("beforeseek",function(e){setTimeout(function(){e.defaultPrevented||(r=!0)})}).on("seek",function(e,t,n){s(),u=p(n||0)-.125,r=!1,!n&&l[0]&&l[0].forEach(c)}).on("ready",function(t,n,r){u=-.125,r=r.cuepoints||e.conf.cuepoints||[],e.setCuepoints(r)}).on("finish",function(){u=-.125}),e.conf.generate_cuepoints&&e.bind("load",function(){i.find(".fp-cuepoint",t).forEach(i.removeNode)}),e.setCuepoints=function(t){return e.cuepoints=[],l={},t.forEach(e.addCuepoint),e},e.addCuepoint=function(n){e.cuepoints||(e.cuepoints=[]);var r,o,s=p(n);return l[s]||(l[s]=[]),l[s].push(n),e.cuepoints.push(n),e.conf.generate_cuepoints&&!1!==n.visible&&(s=e.video.duration,r=i.find(".fp-timeline",t)[0],i.css(r,"overflow","visible"),(o=n.time||n)<0&&(o=s+o),n=i.createElement("a",{className:"fp-cuepoint fp-cuepoint"+(e.cuepoints.length-1)}),i.css(n,"left",o/s*100+"%"),r.appendChild(n),a.on(n,"mousedown",function(t){t.preventDefault(),t.stopPropagation(),e.seek(o)})),e},e.removeCuepoint=function(t){var n=e.cuepoints.indexOf(t),r=p(t);if(-1!==n&&(e.cuepoints=e.cuepoints.slice(0,n).concat(e.cuepoints.slice(n+1)),-1!==(n=l[r].indexOf(t))))return l[r]=l[r].slice(0,n).concat(l[r].slice(n+1)),e}})},{"../common":1,"../flowplayer":18,bean:20,"class-list":22}],7:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("bean"),i=e("../common"),a=(e("is-object"),e("extend-object")),s=e("class-list"),c=(r(function(e,t){var n,r,s,l;!1!==e.conf.embed&&(e.conf,n=i.find(".fp-ui",t)[0],r=i.createElement("a",{class:"fp-embed",title:"Copy to your site"}),s=i.createElement("div",{class:"fp-embed-code"},"<label>Paste this HTML code on your site to embed.</label><textarea></textarea>"),l=i.find("textarea",s)[0],n.appendChild(r),n.appendChild(s),e.embedCode=function(){var n,r=e.conf.embed||{},o=e.video;return r.iframe?(e.conf.embed.iframe,n=r.width||o.width||i.width(t),o=r.height||o.height||i.height(t),'<iframe src="'+e.conf.embed.iframe+'" allowfullscreen style="width:'+n+";height:"+o+';border:none;"></iframe>'):(n=["ratio","rtmp","live","bufferTime","origin","analytics","key","subscribe","swf","swfHls","embed","adaptiveRatio","logo"],r.playlist&&n.push("playlist"),(o=i.pick(e.conf,n)).logo&&(o.logo=i.createElement("img",{src:o.logo}).src),r.playlist&&e.conf.playlist.length||(o.clip=a({},e.conf.clip,i.pick(e.video,["sources"]))),n='var w=window,d=document,e;w._fpes||(w._fpes=[],w.addEventListener("load",function(){var s=d.createElement("script");s.src="//embed.flowplayer.org/6.0.5/embed.min.js",d.body.appendChild(s)})),e=[].slice.call(d.getElementsByTagName("script"),-1)[0].parentNode,w._fpes.push({e:e,l:"$library",c:$conf});\n'.replace("$conf",JSON.stringify(o)).replace("$library",r.library||""),'<a href="$href">Watch video!\n<script>$script<\/script></a>'.replace("$href",e.conf.origin||window.location.href).replace("$script",n))},c(t,".fp-embed","is-embedding"),o.on(t,"click",".fp-embed-code textarea",function(){l.select()}),o.on(t,"click",".fp-embed",function(){l.textContent=e.embedCode().replace(/(\r\n|\n|\r)/gm,""),l.focus(),l.select()}))}),function(e,t,n){function r(){a.remove(n),o.off(document,".st")}var a=s(e);o.on(e,"click",t||"a",function(e){e.preventDefault(),a.toggle(n),a.contains(n)&&(o.on(document,"keydown.st",function(e){27==e.which&&r()}),o.on(document,"click.st",function(e){i.hasParent(e.target,"."+n)||r()}))})})},{"../common":1,"../flowplayer":18,bean:20,"class-list":22,"extend-object":26,"is-object":28}],8:[function(e,t,n){"use strict";t.exports=function(e,t){function n(e,n,i){function a(c){i&&(t.removeEventListener(s,a),r[e].splice(r[e].indexOf(a),1)),c=[c].concat(o[c.timeStamp+c.type]||[]),n&&n.apply(void 0,c)}var s=e.split(".")[0];t.addEventListener(s,a),r[e]||(r[e]=[]),r[e].push(a)}t=t||document.createElement("div");var r={},o={};e.on=e.bind=function(t,r){return t.split(" ").forEach(function(e){n(e,r)}),e},e.one=function(t,r){return t.split(" ").forEach(function(e){n(e,r,!0)}),e},e.off=e.unbind=function(n){return n.split(" ").forEach(function(e){var n=e.split(".").slice(1),o=e.split(".")[0];Object.keys(r).filter(function(e){var t,r=e.split(".").slice(1);return(!o||0===e.indexOf(o))&&(t=r,0===n.filter(function(e){return-1===t.indexOf(e)}).length)}).forEach(function(e){var n=r[e],o=e.split(".")[0];n.forEach(function(e){t.removeEventListener(o,e),n.splice(n.indexOf(e),1)})})}),e},e.trigger=function(n,r,i){var a;if(n)return r=(r||[]).length?r||[]:[r],a=document.createEvent("Event"),n=n.type||n,a.initEvent(n,!1,!0),Object.defineProperty&&(a.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}),o[a.timeStamp+a.type]=r,t.dispatchEvent(a),i?a:e}},t.exports.EVENTS=["beforeseek","disable","error","finish","fullscreen","fullscreen-exit","load","mute","pause","progress","ready","resume","seek","speed","stop","unload","volume","boot","shutdown"]},{}],9:[function(e,t,n){"use strict";var r,o=e("../flowplayer"),i=e("bean"),a=e("class-list"),s=(e("extend-object"),e("../common")),c=(o.support.browser.mozilla,"fullscreen"),l="fullscreen-exit",u=o.support.fullscreen,p=(e=(document.exitFullscreen,navigator.userAgent.toLowerCase()),/(safari)[ \/]([\w.]+)/.exec(e)&&!/(chrome)[ \/]([\w.]+)/.exec(e));i.on(document,"fullscreenchange.ffscr webkitfullscreenchange.ffscr mozfullscreenchange.ffscr MSFullscreenChange.ffscr",function(e){var t;e=document.webkitCurrentFullScreenElement||document.mozFullScreenElement||document.fullscreenElement||document.msFullscreenElement||e.target;(r||e.parentNode&&e.parentNode.getAttribute("data-flowplayer-instance-id"))&&(t=r||o(e.parentNode),r=e&&!r?t.trigger(c,[e]):(r.trigger(l,[r]),null))}),o(function(e,t){var n,o,i,d,f,h=s.createElement("div",{className:"fp-player"});Array.prototype.map.call(t.children,s.identity).forEach(function(e){s.matches(e,".fp-ratio,script")||h.appendChild(e)}),t.appendChild(h),e.conf.fullscreen&&(n=window,d=a(t),e.isFullscreen=!1,e.fullscreen=function(t){if(!e.disabled)return(t=void 0===t?!e.isFullscreen:t)&&(o=n.scrollY,i=n.scrollX),u?t?["requestFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].forEach(function(e){if("function"==typeof h[e])return h[e](Element.ALLOW_KEYBOARD_INPUT),!p||document.webkitCurrentFullScreenElement||document.mozFullScreenElement||h[e](),!1}):["exitFullscreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].forEach(function(e){if("function"==typeof document[e])return document[e](),!1}):e.trigger(t?c:l,[e]),e},e.on("mousedown.fs",function(){+new Date-f<150&&e.ready&&e.fullscreen(),f=+new Date}),e.on(c,function(n){d.add("is-fullscreen"),u||s.css(t,"position","fixed"),e.isFullscreen=!0}).on(l,function(r){var a;u||"html5"!==e.engine||(a=t.css("opacity")||"",s.css(t,"opacity",0)),u||s.css(t,"position",""),d.remove("is-fullscreen"),u||"html5"!==e.engine||setTimeout(function(){t.css("opacity",a)}),e.isFullscreen=!1,n.scrollTo(i,o)}).on("unload",function(){e.isFullscreen&&e.fullscreen()}),e.on("shutdown",function(){r=null}))})},{"../common":1,"../flowplayer":18,bean:20,"class-list":22,"extend-object":26}],10:[function(e,t,n){"use strict";var r,o,i=e("../flowplayer"),a=e("bean"),s="is-help",c=e("../common"),l=e("class-list");a.on(document,"keydown.fp",function(e){var t=r,n=e.ctrlKey||e.metaKey||e.altKey,i=e.which,a=t&&t.conf,c=o&&l(o);if(t&&a.keyboard&&!t.disabled){if(-1!=[63,187,191].indexOf(i))return c.toggle(s),!1;if(27==i&&c.contains(s))return c.toggle(s),!1;if(!n&&t.ready)if(e.preventDefault(),e.shiftKey)39==i?t.speed(!0):37==i&&t.speed(!1);else{if(i<58&&47<i)return t.seekTo(i-48);switch(i){case 38:case 75:t.volume(t.volumeLevel+.15);break;case 40:case 74:t.volume(t.volumeLevel-.15);break;case 39:case 76:t.seeking=!0,t.seek(!0);break;case 37:case 72:t.seeking=!0,t.seek(!1);break;case 190:t.seekTo();break;case 32:t.toggle();break;case 70:a.fullscreen&&t.fullscreen();break;case 77:t.mute();break;case 81:t.unload()}}}}),i(function(e,t){var n,u;e.conf.keyboard&&(a.on(t,"mouseenter mouseleave",function(n){(r=e.disabled||"mouseover"!=n.type?0:e)&&(o=t)}),n=i.support.video&&"flash"!==e.conf.engine&&document.createElement("video").playbackRate?"<p><em>shift</em> + <em>&#8592;</em><em>&#8594;</em>slower / faster</p>":"",t.appendChild(c.createElement("div",{className:"fp-help"},'         <a class="fp-close"></a>         <div class="fp-help-section fp-help-basics">            <p><em>space</em>play / pause</p>            <p><em>q</em>unload | stop</p>            <p><em>f</em>fullscreen</p>'+n+'         </div>         <div class="fp-help-section">            <p><em>&#8593;</em><em>&#8595;</em>volume</p>            <p><em>m</em>mute</p>         </div>         <div class="fp-help-section">            <p><em>&#8592;</em><em>&#8594;</em>seek</p>            <p><em>&nbsp;. </em>seek to previous            </p><p><em>1</em><em>2</em>&hellip; <em>6</em> seek to 10%, 20% &hellip; 60% </p>         </div>   ')),e.conf.tooltip&&((u=c.find(".fp-ui",t)[0]).setAttribute("title","Hit ? for help"),a.one(t,"mouseout.tip",".fp-ui",function(){u.removeAttribute("title")})),a.on(t,"click",".fp-close",function(){l(t).toggle(s)}),e.bind("shutdown",function(){o==t&&(o=null)}))})},{"../common":1,"../flowplayer":18,bean:20,"class-list":22}],11:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=/IEMobile/.test(window.navigator.userAgent),i=e("class-list"),a=e("../common"),s=e("bean"),c=e("./ui").format,l=window.navigator.userAgent;(r.support.touch||o)&&r(function(e,t){var n,u,p,d,f=/Android/.test(l)&&!/Firefox/.test(l)&&!/Opera/.test(l),h=/Silk/.test(l),A=f?parseFloat(/Android\ (\d\.\d)/.exec(l)[1],10):0,g=i(t),m=(f&&!o&&(!/Chrome/.test(l)&&A<4&&(n=e.load,e.load=function(t,r){var o=n.apply(e,arguments);return e.trigger("ready",[e,e.video]),o}),p=0,d=function(e){u=setInterval(function(){e.video.time=++p,e.trigger("progress",[e,p])},1e3)},e.bind("ready pause unload",function(){u&&(clearInterval(u),u=null)}),e.bind("ready",function(){p=0}),e.bind("resume",function(t,n){if(n.live)return p?d(n):void e.one("progress",function(e,t,n){0===n&&d(t)})})),r.support.volume||(g.add("no-volume"),g.add("no-mute")),g.add("is-touch"),e.sliders&&e.sliders.timeline&&e.sliders.timeline.disableAnimation(),r.support.inlineVideo&&!e.conf.native_fullscreen||(e.conf.nativesubtitles=!0),!1);s.on(t,"touchmove",function(){m=!0}),s.on(t,"touchend click",function(t){m?m=!1:e.playing&&!g.contains("is-mouseover")?(g.add("is-mouseover"),g.remove("is-mouseout"),t.preventDefault(),t.stopPropagation()):e.playing||e.splash||!g.contains("is-mouseout")||g.contains("is-mouseover")||setTimeout(function(){e.playing||e.splash||e.resume()},400)}),e.conf.native_fullscreen&&"function"==typeof document.createElement("video").webkitEnterFullScreen&&(e.fullscreen=function(){var e=a.find("video.fp-engine",t)[0];e.webkitEnterFullScreen(),s.one(e,"webkitendfullscreen",function(){a.prop(e,"controls",!0),a.prop(e,"controls",!1)})}),(f||h)&&e.bind("ready",function(){var n=a.find("video.fp-engine",t)[0];s.one(n,"canplay",function(){n.play()}),n.play(),e.bind("progress.dur",function(){var r=n.duration;1!==r&&(e.video.duration=r,a.find(".fp-duration",t)[0].innerHTML=c(r),e.unbind("progress.dur"))})})})},{"../common":1,"../flowplayer":18,"./ui":17,bean:20,"class-list":22}],12:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("extend-object"),i=e("bean"),a=e("class-list"),s=e("../common"),c=e("./resolve"),l=new c,u=window.jQuery,p=/^#/;r(function(e,t){var n=o({active:"is-active",advance:!0,query:".fp-playlist a"},e.conf),r=n.active,d=a(t);function f(){return s.find(n.query,h())}function h(){if(!p.test(n.query))return t}function A(){return s.find(n.query+"."+r,h())}function g(){var n,r=s.find(".fp-playlist",t)[0];r||(r=s.createElement("div",{className:"fp-playlist"}),(n=s.find(".fp-next,.fp-prev",t)).length?n[0].parentElement.insertBefore(r,n[0]):s.insertAfter(t,s.find("video",t)[0],r)),r.innerHTML="",e.conf.playlist[0].length&&(e.conf.playlist=e.conf.playlist.map(function(e){var t;return"string"==typeof e?{sources:[{type:"m3u8"===(t=e.split(c.TYPE_RE)[1]).toLowerCase()?"application/x-mpegurl":"video/"+t,src:e}]}:{sources:e.map(function(e){var t={};return Object.keys(e).forEach(function(n){t.type=/mpegurl/i.test(n)?"application/x-mpegurl":"video/"+n,t.src=e[n]}),t})}})),e.conf.playlist.forEach(function(e,t){e=e.sources[0].src,r.appendChild(s.createElement("a",{href:e,"data-index":t}))})}e.play=function(t){if(void 0===t)return e.resume();if("number"!=typeof t||e.conf.playlist[t]){if("number"!=typeof t)return e.load.apply(null,arguments);var n=o({index:t},e.conf.playlist[t]);if(t===e.video.index)return e.load(n,function(){e.resume()});e.off("resume.fromfirst"),e.load(n,function(){e.video.index=t})}return e},e.next=function(t){return t&&t.preventDefault(),-1!=(t=e.video.index)&&(t=t===e.conf.playlist.length-1?0:t+1,e.play(t)),e},e.prev=function(t){return t&&t.preventDefault(),-1!=(t=e.video.index)&&(t=0===t?e.conf.playlist.length-1:t-1,e.play(t)),e},e.setPlaylist=function(t){return e.conf.playlist=t,delete e.video.index,g(),e},e.addPlaylistItem=function(t){return e.setPlaylist(e.conf.playlist.concat([t]))},e.removePlaylistItem=function(t){var n=e.conf.playlist;return e.setPlaylist(n.slice(0,t).concat(n.slice(t+1)))},i.on(t,"click",".fp-next",e.next),i.on(t,"click",".fp-prev",e.prev),n.advance&&e.off("finish.pl").on("finish.pl",function(e,t){if(t.video.loop)return t.seek(0,function(){t.resume()});var r=0<=t.video.index?t.video.index+1:void 0;r<t.conf.playlist.length||n.loop?(r=r===t.conf.playlist.length?0:r,d.remove("is-finished"),setTimeout(function(){t.play(r)})):1<t.conf.playlist.length&&t.one("resume.fromfirst",function(){return t.play(0),!1})});var m=!1;e.conf.playlist.length&&(m=!0,g(),e.conf.clip&&e.conf.clip.sources.length||(e.conf.clip=e.conf.playlist[e.conf.startIndex||0])),f().length&&!m&&(e.conf.playlist=[],delete e.conf.startIndex,f().forEach(function(t){var n=t.href;t.setAttribute("data-index",e.conf.playlist.length),n=l.resolve(n,e.conf.clip.sources);u&&o(n,u(t).data()),e.conf.playlist.push(n)})),i.on(p.test(n.query)?document:t,"click",n.query,function(t){t.preventDefault(),t=t.currentTarget,-1!=(t=Number(t.getAttribute("data-index")))&&e.play(t)}),e.on("load",function(o,i,c){var l,u,p,f,g;e.conf.playlist.length&&(u=(l=A()[0])&&l.getAttribute("data-index"),g=c.index=void 0!==(g=c).index?g.index:void 0!==e.video.index?e.video.index:e.conf.startIndex||0,p=s.find(n.query+'[data-index="'+g+'"]',h())[0],f=g==e.conf.playlist.length-1,l&&a(l).remove(r),p&&a(p).add(r),d.remove("video"+u),d.add("video"+g),s.toggleClass(t,"last-video",f),c.index=i.video.index=g,c.is_last=i.video.is_last=f)}).on("unload.pl",function(){e.conf.playlist.length&&(A().forEach(function(e){a(e).toggle(r)}),e.conf.playlist.forEach(function(e,t){d.remove("video"+t)}))}),e.conf.playlist.length&&(e.conf.loop=!1)})},{"../common":1,"../flowplayer":18,"./resolve":13,bean:20,"class-list":22,"extend-object":26}],13:[function(e,t,n){"use strict";var r=/\.(\w{3,4})(\?.*)?$/i,o=e("extend-object");function i(e){var t=e.attr("src"),n=e.attr("type")||"",i=t.split(r)[1];n=n.toLowerCase();return o(e.data(),{src:t,suffix:i||n,type:n||i})}t.exports=function(){this.sourcesFromVideoTag=function(e,t){var n=[];return t("source",e).each(function(){n.push(i(t(this)))}),!n.length&&e.length&&n.push(i(e)),n},this.resolve=function(e,t){return e?("string"==typeof e&&((e={src:e,sources:[]}).sources=(t||[]).map(function(t){var n=t.src.split(r)[1];return{type:t.type,src:e.src.replace(r,"."+n+"$2")}})),e=e instanceof Array?{sources:e.map(function(e){return e.type&&e.src?e:Object.keys(e).reduce(function(t,n){return o(t,{type:/mpegurl/i.test(t=n)?"application/x-mpegurl":"video/"+t,src:e[n]})},{})})}:e):{sources:t}}},t.exports.TYPE_RE=r},{"extend-object":26}],14:[function(e,t,n){"use strict";var r=e("class-list"),o=e("bean"),i=e("../common");t.exports=function(e,t){function n(){u=i.offset(e),p=i.width(e),d=i.height(e),h=f?d:p,g=function(e){return Math.max(0,Math.min(h,f?(1-e)*d:e*p))}(A)}function a(t){l||t==b.value||A&&!(t<A)||(o.fire(e,"slide",[t]),b.value=t)}function s(e){!(n=e.pageX||e.clientX)&&e.originalEvent&&e.originalEvent.touches&&e.originalEvent.touches.length&&(n=e.originalEvent.touches[0].pageX);e=f?e.pageY-u.top:n-u.left;var n=Math.max(0,Math.min(g||h,e))/h;return f&&(n=1-n),c(n=t?1-n:n,0)}function c(e,t){void 0===t&&(t=0),1<e&&(e=1);var n=Math.round(1e3*e)/10+"%";return(!A||e<=A)&&(v.remove("animated"),w?v.remove("animated"):(v.add("animated"),i.css(m,"transition-duration",(t||0)+"ms")),i.css(m,"width",n)),e}/iPad/.test(navigator.userAgent)&&/CriOS/.test(navigator.userAgent);var l,u,p,d,f,h,A,g,m=i.lastChild(e),y=r(e),v=r(m),w=!1,b={max:function(e){A=e},disable:function(e){l=e},slide:function(e,t,r){n(),r&&a(e),c(e,t)},disableAnimation:function(t,n){w=!1!==t,i.toggleClass(e,"no-animation",!!n)}};return n(),o.on(e,"mousedown.sld touchstart",function(e){var t,r,i;e.preventDefault(),l||(r=a,100,t=function(){i||(r.apply(this,arguments),i=1,setTimeout(function(){i=0},100))},n(),b.dragging=!0,y.add("is-dragging"),a(s(e)),o.on(document,"mousemove.sld touchmove.sld",function(e){e.preventDefault(),t(s(e))}),o.one(document,"mouseup touchend",function(){b.dragging=!1,y.remove("is-dragging"),o.off(document,"mousemove.sld touchmove.sld")}))}),b}},{"../common":1,bean:20,"class-list":22}],15:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("../common"),i=e("bean"),a=e("class-list");r.defaults.subtitleParser=function(e){var t=/^(([0-9]{2}:){1,2}[0-9]{2}[,.][0-9]{3}) --\> (([0-9]{2}:){1,2}[0-9]{2}[,.][0-9]{3})(.*)/;function n(e){return 2==(e=e.split(":")).length&&e.unshift(0),60*e[0]*60+60*e[1]+parseFloat(e[2].replace(",","."))}for(var r,o,i,a=[],s=0,c=e.split("\n"),l=c.length;s<l;s++)if(o=t.exec(c[s])){for(r=c[s-1],i="<p>"+c[++s]+"</p><br/>";"string"==typeof c[++s]&&c[s].trim()&&s<c.length;)i+="<p>"+c[s]+"</p><br/>";r={title:r,startTime:n(o[1]),endTime:n(o[3]),text:i},a.push(r)}return a},r(function(e,t){function n(e){o.toggleClass(o.find("li.active",t)[0],"active"),o.toggleClass(o.find('li[data-subtitle-index="'+e+'"]',t)[0],"active")}var s,c,l,u=a(t);i.on(t,"click",".fp-menu",function(e){a(void 0).toggle("dropdown-open")}),i.on(t,"click",".fp-menu li[data-subtitle-index]",function(t){if("-1"===(t=t.target.getAttribute("data-subtitle-index")))return e.disableSubtitles();e.loadSubtitles(t)}),e.on("ready",function(n,i,c){var p,d,f=i.conf;if(r.support.subtitles&&f.nativesubtitles&&"html5"==i.engine.engineName)return p=function(e){var n=o.find("video",t)[0].textTracks;n.length&&(n[0].mode=e)},c.subtitles&&c.subtitles.length?(d=o.find("video.fp-engine",t)[0],c.subtitles.some(function(e){return!o.isSameDomain(e.src)})&&o.attr(d,"crossorigin","anonymous"),d.textTracks.addEventListener("addtrack",function(){p("disabled"),p("showing")}),void c.subtitles.forEach(function(e){d.appendChild(o.createElement("track",{kind:"subtitles",srclang:e.srclang||"en",label:e.label||"en",src:e.src,default:e.default}))})):void 0;i.subtitles=[],f=o.find(".fp-player",t)[0],l=(l=o.find(".fp-subtitle",t)[0])||o.appendTo(o.createElement("div",{class:"fp-subtitle"}),f),Array.prototype.forEach.call(l.children,o.removeNode),s=a(l),o.find(".fp-menu",t).forEach(o.removeNode),u.remove("has-menu"),e.disableSubtitles(),c.subtitles&&c.subtitles.length&&(u.add("has-menu"),f=c.subtitles.filter(function(e){return e.default})[0])&&i.loadSubtitles(c.subtitles.indexOf(f))}),e.bind("cuepoint",function(e,t,n){n.subtitle?(c=n.index,o.html(l,n.subtitle.text),s.add("fp-active")):n.subtitleEnd&&(s.remove("fp-active"),c=n.index)}),e.bind("seek",function(t,n,r){c&&e.cuepoints[c]&&e.cuepoints[c].time>r&&(s.remove("fp-active"),c=null),(e.cuepoints||[]).forEach(function(t){var n=t.subtitle;n&&c!=t.index?r>=t.time&&(!n.endTime||r<=n.endTime)&&e.trigger("cuepoint",[e,t]):t.subtitleEnd&&r>=t.time&&t.index==c+1&&e.trigger("cuepoint",[e,t])})}),e.disableSubtitles=function(){return e.subtitles=[],(e.cuepoints||[]).forEach(function(t){(t.subtitle||t.subtitleEnd)&&e.removeCuepoint(t)}),l&&Array.prototype.forEach.call(l.children,o.removeNode),n(-1),e},e.loadSubtitles=function(t){e.disableSubtitles();var r=e.video.subtitles[t].src;if(r)return n(t),o.xhrGet(r,function(t){e.conf.subtitleParser(t).forEach(function(t){var n={time:t.startTime,subtitle:t,visible:!1};e.subtitles.push(t),e.addCuepoint(n),e.addCuepoint({time:t.endTime,subtitleEnd:t.title,visible:!1}),0!==t.startTime||e.video.time||e.trigger("cuepoint",[e,n])})},function(){return e.trigger("error",{code:8,url:r}),!1}),e}})},{"../common":1,"../flowplayer":18,bean:20,"class-list":22}],16:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=(e=e("extend-object"),{}),i=navigator.userAgent.toLowerCase(),a=((i=/(chrome)[ \/]([\w.]+)/.exec(i)||/(safari)[ \/]([\w.]+)/.exec(i)||/(webkit)[ \/]([\w.]+)/.exec(i)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(i)||/(msie) ([\w.]+)/.exec(i)||i.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(i)||[])[1]&&(o[i[1]]=!0,o.version=i[2]||"0"),(i=document.createElement("video")).loop=!0,i.autoplay=!0,i.preload=!0,navigator.userAgent),s=o.msie||/Trident\/7/.test(a),c=/iPad|MeeGo/.test(a)&&!/CriOS/.test(a),l=/iPad/.test(a)&&/CriOS/.test(a),u=/iP(hone|od)/i.test(a)&&!/iPad/.test(a)&&!/IEMobile/i.test(a),p=/Android/.test(a)&&!/Firefox/.test(a),d=/Android/.test(a)&&/Firefox/.test(a),f=/Silk/.test(a),h=/IEMobile/.test(a),A=h?parseFloat(/Windows\ Phone\ (\d+\.\d+)/.exec(a)[1],10):0,g=h?parseFloat(/IEMobile\/(\d+\.\d+)/.exec(a)[1],10):0,m=(c&&function(e){(e=/Version\/(\d\.\d)/.exec(e))&&1<e.length&&parseFloat(e[1],10)}(a),p?parseFloat(/Android\ (\d\.\d)/.exec(a)[1],10):0);a=e(r.support,{browser:o,subtitles:!!i.addTextTrack,fullscreen:"function"==typeof document.webkitCancelFullScreen&&!/Mac OS X 10_5.+Version\/5\.0\.\d Safari/.test(a)||document.mozFullScreenEnabled||"function"==typeof document.exitFullscreen||"function"==typeof document.msExitFullscreen,inlineBlock:!(s&&o.version<8),touch:"ontouchstart"in window,dataload:!c&&!u&&!h,zeropreload:!s&&!p,volume:!(c||p||u||f||l),cachedVideoTag:!(c||u||l||h),firstframe:!(u||c||p||f||l||h||d),inlineVideo:!u&&(!h||8.1<=A&&11<=g)&&(!p||3<=m),hlsDuration:!p&&(!o.safari||c||u||l),seekable:!c&&!l});try{var y=navigator.plugins["Shockwave Flash"],v=s?new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version"):y.description;s||y[0].enabledPlugin?((v=v.split(/\D+/)).length&&!v[0]&&(v=v.slice(1)),a.flashVideo=9<v[0]||9==v[0]&&115<=v[3]):a.flashVideo=!1}catch(e){}try{a.video=!!i.canPlayType,a.video&&i.canPlayType("video/mp4")}catch(e){a.video=!1}a.animation=function(){for(var e=["","Webkit","Moz","O","ms","Khtml"],t=document.createElement("p"),n=0;n<e.length;n++)if(void 0!==t.style[e[n]+"AnimationName"])return!0}()},{"../flowplayer":18,"extend-object":26}],17:[function(e,t,n){"use strict";var r=e("../flowplayer"),o=e("../common"),i=e("class-list"),a=e("bean"),s=e("./slider");function c(e){return 10<=(e=parseInt(e,10))?e:"0"+e}function l(e){e=e||0;var t=Math.floor(e/3600),n=Math.floor(e/60);return e-=60*n,1<=t?t+":"+c(n-=60*t)+":"+c(e):c(n)+":"+c(e)}r(function(e,t){var n,c=e.conf,u=r.support,p=i(t),d=(o.find(".fp-ratio,.fp-ui",t).forEach(o.removeNode),p.add("flowplayer"),t.appendChild(o.createElement("div",{className:"fp-ratio"})),o.createElement("div",{className:"fp-ui"},'         <div class="waiting"><em></em><em></em><em></em></div>         <a class="fullscreen"></a>         <a class="unload"></a>         <p class="speed"></p>         <div class="controls">            <a class="play"></a>            <div class="timeline">               <div class="buffer"></div>               <div class="progress"></div>            </div>            <div class="timeline-tooltip fp-tooltip"></div>            <div class="volume">               <a class="mute"></a>               <div class="volumeslider">                  <div class="volumelevel"></div>               </div>            </div>         </div>         <div class="time">            <em class="elapsed"></em>            <em class="remaining"></em>            <em class="duration"></em>         </div>         <div class="message"><h2></h2><p></p></div>'.replace(/class="/g,'class="fp-')));function f(e){return o.find(".fp-"+e,t)[0]}t.appendChild(d),f("progress");var h=f("buffer"),A=f("elapsed"),g=f("remaining"),m=f("waiting"),y=f("ratio"),v=f("speed"),w=i(v),b=f("duration"),C=f("controls"),E=f("timeline-tooltip"),M=o.css(y,"padding-top"),I=f("timeline"),T=s(I,e.rtl),k=(f("volume"),f("fullscreen")),S=f("volumeslider"),D=s(S,e.rtl),x=p.contains("fixed-controls")||p.contains("no-toggle");function N(e){o.css(y,"padding-top",100*e+"%"),u.inlineBlock||o.height(o.find("object",t)[0],o.height(t))}function j(e){e?(p.add("is-mouseover"),p.remove("is-mouseout")):(p.add("is-mouseout"),p.remove("is-mouseover"))}T.disableAnimation(p.contains("is-touch")),e.sliders=e.sliders||{},e.sliders.timeline=T,e.sliders.volume=D,u.animation||o.html(m,"<p>loading &hellip;</p>"),c.ratio&&N(c.ratio);try{c.fullscreen||o.removeNode(k)}catch(f){o.removeNode(k)}e.on("ready",function(e,n,r){var i=n.video.duration;T.disable(n.disabled||!i),c.adaptiveRatio&&!isNaN(r.height/r.width)&&N(r.height/r.width),o.html([b,g],l(i)),o.toggleClass(t,"is-long",3600<=i),D.slide(n.volumeLevel),"flash"===n.engine.engineName?T.disableAnimation(!0,!0):T.disableAnimation(!1),o.find(".fp-title",d).forEach(o.removeNode),r.title&&o.prepend(d,o.createElement("div",{className:"fp-title"},r.title))}).on("unload",function(){M||c.splash||o.css(y,"paddingTop",""),T.slide(0)}).on("buffer",function(){var t=e.video,n=t.buffer/t.duration;!t.seekable&&u.seekable&&T.max(n),n<1?o.css(h,"width",100*n+"%"):o.css(h,"width","100%")}).on("speed",function(e,t,n){o.text(v,n+"x"),w.add("fp-hilite"),setTimeout(function(){w.remove("fp-hilite")},1e3)}).on("buffered",function(){o.css(h,"width","100%"),T.max(1)}).on("progress",function(){var t=e.video.time,n=e.video.duration;T.dragging||T.slide(t/n,e.seeking?0:250),o.html(A,l(t)),o.html(g,"-"+l(n-t))}).on("finish resume seek",function(e){o.toggleClass(t,"is-finished","finish"==e.type)}).on("stop",function(){o.html(A,l(0)),T.slide(0,100)}).on("finish",function(){o.html(A,l(e.video.duration)),T.slide(1,100),p.remove("is-seeking")}).on("beforeseek",function(){}).on("volume",function(){D.slide(e.volumeLevel)}).on("disable",function(){var n=e.disabled;T.disable(n),D.disable(n),o.toggleClass(t,"is-disabled",e.disabled)}).on("mute",function(e,n,r){o.toggleClass(t,"is-muted",r)}).on("error",function(e,t,n){return!0}),a.on(t,"mouseenter mouseleave",function(r){var o;x||(j(r="mouseover"==r.type),r?(e.on("pause.x volume.x",r=function(){j(!0),o=new Date}),a.on(t,"mousemove.x",r),n=setInterval(function(){new Date-o>c.mouseoutTimeout&&(j(!1),o=new Date)},100)):(a.off(t,"mousemove.x"),e.off("pause.x volume.x"),clearInterval(n)))}),a.on(t,"mouseleave",function(){(T.dragging||D.dragging)&&(p.add("is-mouseover"),p.remove("is-mouseout"))}),a.on(t,"mousemove",".fp-timeline",function(t){var n=((t=t.pageX||t.clientX)-o.offset(I).left)/o.width(I),r=n*e.video.duration;n<0||(o.html(E,l(r)),o.css(E,"left",t-o.offset(C).left-o.width(E)/2+"px"))}),a.on(t,"contextmenu",function(e){var n,r=o.offset(o.find(".fp-player",t)[0]),i=window,s=e.clientX-(r.left+i.scrollX);r=e.clientY-(r.top+i.scrollY);p.contains("is-flash-disabled")||(n=o.find(".fp-context-menu",t)[0])&&(e.preventDefault(),o.css(n,{left:s+"px",top:r+"px",display:"block"}),a.on(t,"click",".fp-context-menu",function(e){e.stopPropagation()}),a.on(document,"click.outsidemenu",function(e){o.css(n,"display","none"),a.off(document,"click.outsidemenu")}))}),e.on("flashdisabled",function(){p.add("is-flash-disabled"),e.one("ready progress",function(){p.remove("is-flash-disabled"),o.find(".fp-flash-disabled",t).forEach(o.removeNode)}),t.appendChild(o.createElement("div",{className:"fp-flash-disabled"},"Adobe Flash is disabled for this page, click player area to enable"))}),c.poster&&o.css(t,"background-image","url("+c.poster+")");m=o.css(t,"background-color"),k="none"!=o.css(t,"background-image")||m&&"rgba(0, 0, 0, 0)"!=m&&"transparent"!=m;var B=("string"==typeof c.splash&&o.css(t,"background-image","url('"+c.splash+"')"),!k&&e.forcedSplash&&o.css(t,"background-color","#555"),a.on(t,"click",".fp-toggle, .fp-play",function(){e.disabled||e.toggle()}),a.on(t,"click",".fp-mute",function(){e.mute()}),a.on(t,"click",".fp-fullscreen",function(){e.fullscreen()}),a.on(t,"click",".fp-unload",function(){e.unload()}),a.on(I,"slide",function(t){e.seeking=!0,e.seek(t*e.video.duration)}),a.on(S,"slide",function(t){e.volume(t)}),f("time"));a.on(t,"click",".fp-time",function(){i(B).toggle("is-inverted")}),j(x),e.on("shutdown",function(){a.off(I),a.off(S)})}),t.exports.format=l},{"../common":1,"../flowplayer":18,"./slider":14,bean:20,"class-list":22}],18:[function(e,t,n){"use strict";var r=e("extend-object"),o=e("is-function"),a=e("class-list"),s=e("bean"),c=e("./common"),l=e("./ext/events"),u=[],p=[],d=(window.navigator.userAgent,window.onbeforeunload),f=!(window.onbeforeunload=function(e){if(u.forEach(function(e){e.conf.splash?e.unload():e.bind("error",function(){c.find(".flowplayer.is-error .fp-message").forEach(c.removeNode)})}),d)return d(e)});try{"object"==i(window.localStorage)&&(window.localStorage.flowplayerTestStorage="test",f=!0)}catch(e){}var h,A=/Safari/.exec(navigator.userAgent)&&!/Chrome/.exec(navigator.userAgent),g=(g=/(\d+\.\d+) Safari/.exec(navigator.userAgent))?Number(g[1]):100,m=(window.flowplayer=t.exports=function(e,t,n){return o(e)?p.push(e):"number"==typeof e||void 0===e?u[e||0]:e.nodeType?null!==e.getAttribute("data-flowplayer-instance-id")?u[e.getAttribute("data-flowplayer-instance-id")]:t?v(e,t,n):void 0:e.jquery?flowplayer(e[0],t,n):"string"==typeof e?(e=c.find(e)[0])&&flowplayer(e,t,n):void 0},r(flowplayer,{version:"6.0.5",engines:[],conf:{},set:function(e,t){"string"==typeof e?flowplayer.conf[e]=t:r(flowplayer.conf,e)},support:{},defaults:{debug:!!f&&!!localStorage.flowplayerDebug,disabled:!1,fullscreen:window==window.top,keyboard:!0,ratio:9/16,adaptiveRatio:!1,rtmp:0,proxy:"best",splash:!1,live:!1,swf:"//releases.flowplayer.org/6.0.5/flowplayer.swf",swfHls:"//releases.flowplayer.org/6.0.5/flowplayerhls.swf",speeds:[.25,.5,1,1.5,2],tooltip:!0,mouseoutTimeout:5e3,volume:f?"true"==localStorage.muted?0:!isNaN(localStorage.volume)&&localStorage.volume||1:1,errors:["","Video loading aborted","Network error","Video not properly encoded","Video file not found","Unsupported video","Skin not found","SWF file not found","Subtitles not found","Invalid RTMP URL","Unsupported video format. Try installing Adobe Flash."],errorUrls:["","","","","","","","","","","http://get.adobe.com/flashplayer/"],playlist:[],hlsFix:A&&g<8},bean:s,common:c,extend:r}),0),y=e("./ext/resolve");function v(e,t,n){t&&t.embed&&(t.embed=r({},flowplayer.defaults.embed,t.embed));var i,d,h=e,A=a(h),g=r({},flowplayer.defaults,flowplayer.conf,t),v={},w=new y;A.add("is-loading");try{v=f?window.localStorage:v}catch(e){}var b=((e=h.currentStyle&&"rtl"===h.currentStyle.direction||window.getComputedStyle&&null!==window.getComputedStyle(h,null)&&"rtl"===window.getComputedStyle(h,null).getPropertyValue("direction"))&&A.add("is-rtl"),{conf:g,currentSpeed:1,volumeLevel:100,video:{},disabled:!1,finished:!1,loading:!1,muted:!1,paused:!1,playing:!1,ready:!1,splash:!1,rtl:e,load:function(e,t){if(b.error||b.loading)return b.error,b.loading,!1;b.video={},b.finished=!1,e=e||g.clip,e=r({},w.resolve(e,g.clip.sources)),(b.playing||b.engine)&&(e.autoplay=!0);var n=C(e);if(!n)return b.trigger("error",[b,{code:flowplayer.support.flashVideo?5:10}]);if(n.engineName)return b.engine&&n.engineName===b.engine.engineName||(b.ready=!1,b.engine&&(b.engine.unload(),b.conf.autoplay=!0),d=b.engine=n(b,h),b.one("ready",function(){d.volume(b.volumeLevel)})),r(e,d.pick(e.sources.filter(function(e){return!e.engine||e.engine===d.engineName}))),e.src&&(b.trigger("load",[b,e,d],!0).defaultPrevented?b.loading=!1:(d.load(e),(t=o(e)?e:t)&&b.one("ready",t))),b;throw new Error("engineName property of factory should be exposed")},pause:function(e,t){return b.ready&&!b.loading&&(d.pause(t),b.one("pause",e)),b},resume:function(){return b.ready&&b.paused&&(d.resume(),b.finished)&&(b.trigger("resume",[b]),b.finished=!1),b},toggle:function(){return b.ready?b.paused?b.resume():b.pause():b.load()},seek:function(e,t){var n;return b.ready&&!b.live&&("boolean"==typeof e&&(n=.1*b.video.duration,e=b.video.time+(e?n:-n)),e=i=Math.min(Math.max(e,0),b.video.duration-.1).toFixed(1),b.trigger("beforeseek",[b,e],!0).defaultPrevented?(b.seeking=!1,c.toggleClass(h,"is-seeking",b.seeking)):(d.seek(e),o(t)&&b.one("seek",t))),b},seekTo:function(e,t){return e=void 0===e?i:.1*b.video.duration*e,b.seek(e,t)},mute:function(e,t){return void 0===e&&(e=!b.muted),t||(v.muted=b.muted=e,v.volume=(isNaN(v.volume)?g:v).volume),b.volume(e?0:v.volume,!0),b.trigger("mute",[b,e]),b},volume:function(e,t){return b.ready&&(e=Math.min(Math.max(e,0),1),t||(v.volume=e),d.volume(e)),b},speed:function(e,t){return b.ready&&("boolean"==typeof e&&(e=g.speeds[g.speeds.indexOf(b.currentSpeed)+(e?1:-1)]||b.currentSpeed),d.speed(e),t)&&h.one("speed",t),b},stop:function(e,t){return(t||b.ready)&&(b.pause(void 0,e),b.seek(0,function(){b.trigger("stop",[b])})),b.error=null,b.loading=!1,b},unload:function(){return A.contains("is-embedding")||(g.splash?(b.trigger("unload",[b]),d&&(d.unload(),b.engine=d=0)):b.stop()),b},shutdown:function(){b.unload(),b.trigger("shutdown",[b]),s.off(h),delete u[h.getAttribute("data-flowplayer-instance-id")],h.removeAttribute("data-flowplayer-instance-id")},disable:function(e){return(e=void 0===e?!b.disabled:e)!=b.disabled&&(b.disabled=e,b.trigger("disable",e)),b},left:function(){var e;return d.left&&(e=d.left())?parseInt(e,10):0},top:function(){var e;return d.top&&(e=d.top())?parseInt(e,10):0}}),C=(b.conf=r(b.conf,g),l(b),function(e){var t,n=flowplayer.engines;if(g.engine){var r=n.filter(function(e){return e.engineName===g.engine})[0];if(r&&e.sources.some(function(e){return(!e.engine||e.engine===r.engineName)&&r.canPlay(e.type,b.conf)}))return r}return g.enginePreference&&(n=flowplayer.engines.filter(function(e){return-1<g.enginePreference.indexOf(e.engineName)}).sort(function(e,t){return g.enginePreference.indexOf(e.engineName)-g.enginePreference.indexOf(t.engineName)})),e.sources.some(function(e){var r=n.filter(function(t){return(!e.engine||e.engine===t.engineName)&&t.canPlay(e.type,b.conf)}).shift();return r&&(t=r),!!r}),t});return h.getAttribute("data-flowplayer-instance-id")||(h.setAttribute("data-flowplayer-instance-id",m++),b.on("boot",function(){!g.splash&&!A.contains("is-splash")&&flowplayer.support.firstframe||(b.forcedSplash=!g.splash&&!A.contains("is-splash"),b.splash=g.autoplay=!0,g.splash||(g.splash=!0),A.add("is-splash")),g.splash&&c.find("video",h).forEach(c.removeNode),(g.live||A.contains("is-live"))&&(b.live=g.live=!0,A.add("is-live")),p.forEach(function(e){e(b,h)}),u.push(b),g.splash?b.unload():b.load(),g.disabled&&b.disable(),b.one("ready",n)}).on("load",function(e,t,n){g.splash&&c.find(".flowplayer.is-ready,.flowplayer.is-loading").forEach(function(e){(e=e.getAttribute("data-flowplayer-instance-id"))!==h.getAttribute("data-flowplayer-instance-id")&&(e=u[Number(e)])&&e.conf.splash&&e.unload()}),A.add("is-loading"),t.loading=!0,void 0!==n.live&&(c.toggleClass(h,"is-live",n.live),t.live=n.live)}).on("ready",function(e,t,n){n.time=0,t.video=n,A.remove("is-loading"),t.loading=!1,t.muted?t.mute(!0,!0):t.volume(t.volumeLevel),t=t.conf.hlsFix&&/mpegurl/i.exec(n.type),c.toggleClass(h,"hls-fix",!!t)}).on("resume unload",function(e){A.remove("is-loading"),b.loading=!1}).on("ready unload",function(e){e="ready"==e.type,c.toggleClass(h,"is-splash",!e),c.toggleClass(h,"is-ready",e),b.ready=e,b.splash=!e}).on("progress",function(e,t,n){t.video.time=n}).on("speed",function(e,t,n){t.currentSpeed=n}).on("volume",function(e,t,n){t.volumeLevel=Math.round(100*n)/100,t.muted?n&&t.mute(!1):v.volume=n}).on("beforeseek seek",function(e){b.seeking="beforeseek"==e.type,c.toggleClass(h,"is-seeking",b.seeking)}).on("ready pause pauseInLastFrame resume unload finish stop",function(e,t,n){b.paused=/pause|finish|unload|stop/.test(e.type),b.paused=b.paused||"ready"===e.type&&!g.autoplay&&!b.playing,b.playing=!b.paused,"pauseInLastFrame"!=e.type&&(c.toggleClass(h,"is-paused",b.paused),c.toggleClass(h,"is-playing",b.playing))}).on("finish",function(e){b.finished=!0}).on("error",function(){})),b.trigger("boot",[b,h]),b}void 0!==window.jQuery&&((h=window.jQuery)(function(){"function"==typeof h.fn.flowplayer&&h('.flowplayer:has(video,script[type="application/json"])').flowplayer()}),h.fn.flowplayer=function(e,t){return this.each(function(){o(e="string"==typeof e?{swf:e}:e)&&(t=e,e={});var n,i,a,s=h(this),c=(c=s.find('script[type="application/json"]')).length?JSON.parse(c.text()):(n=s.find("video")).length?(i=n.data()||{},a={},h.each(["autoplay","loop","preload","poster"],function(e,t){var r=n.attr(t);void 0!==r&&-1!==["autoplay","poster"].indexOf(t)?a[t]=r||!0:void 0!==r&&(i[t]=r||!0)}),i.subtitles=n.find("track").map(function(){var e=h(this);return{src:e.attr("src"),kind:e.attr("kind"),label:e.attr("label"),srclang:e.attr("srclang"),default:e.prop("default")}}).get(),i.sources=(new y).sourcesFromVideoTag(n,h),r(a,{clip:i})):{},u=v(this,h.extend({},e||{},c,s.data()),t);l.EVENTS.forEach(function(e){u.on(e+".jquery",function(e){s.trigger.call(s,e.type,e.detail&&e.detail.args)})}),s.data("flowplayer",u)})})},{"./common":1,"./ext/events":8,"./ext/resolve":13,bean:20,"class-list":22,"extend-object":26,"is-function":27}],19:[function(e,t,n){e("es5-shim"),t.exports=e("./flowplayer"),e("./ext/support"),e("./engine/embed"),e("./engine/html5"),e("./engine/flash"),e("./ext/ui"),e("./ext/keyboard"),e("./ext/playlist"),e("./ext/cuepoint"),e("./ext/subtitle"),e("./ext/analytics"),e("./ext/embed"),e("./ext/fullscreen"),e("./ext/mobile")},{"./engine/embed":2,"./engine/flash":3,"./engine/html5":4,"./ext/analytics":5,"./ext/cuepoint":6,"./ext/embed":7,"./ext/fullscreen":9,"./ext/keyboard":10,"./ext/mobile":11,"./ext/playlist":12,"./ext/subtitle":15,"./ext/support":16,"./ext/ui":17,"./flowplayer":18,"es5-shim":25}],20:[function(e,t,n){
/*!
        * Bean - copyright (c) Jacob Thornton 2011-2012
        * https://github.com/fat/bean
        * MIT license
        */
var r;r=function(e,t){function n(e,t){return x||t||e!==S&&e!==E?e:D}function r(e){l=arguments.length?e:S.querySelectorAll?function(e,t){return t.querySelectorAll(e)}:function(){throw new Error("Bean: No selector engine installed")}}function o(e,t){if(x||!t||!e||e.propertyName=="_on"+t){var n=U.get(this,t||e.type,null,!1),r=n.length,o=0;for(e=new Q(e,this,!0),t&&(e.type=t);o<r&&!e.isImmediatePropagationStopped();o++)n[o].removed||n[o].handler.call(this,e)}}function a(e,t){function n(t,n){for(var r,o=_(e)?l(e,n):e;t&&t!==n;t=t.parentNode)for(r=o.length;r--;)if(o[r]===t)return t}function r(e){var r=n(e.target,this);r&&t.apply(r,arguments)}return r.__beanDel={ft:n,selector:e},r}function s(e,t,n){var r,o,i,a=_(t);if(a&&0<t.indexOf(" "))for(i=(t=L(t)).length;i--;)s(e,t[i],n);else if((o=a&&t.replace(T,""))&&P[o]&&(o=P[o].base),!t||a)a=(a=a&&t.replace(I,""))&&L(a,"."),G(e,o,n,a);else if(O(t))G(e,null,t);else for(r in t)t.hasOwnProperty(r)&&s(e,r,t[r]);return e}function c(e,t,n,r){var o,l,u,p,d,f,h;if(void 0!==n||"object"!=i(t)){for(r=O(n)?(d=B.call(arguments,3),o=n):(o=r,d=B.call(arguments,4),a(n,o)),u=L(t),this===j&&(r=z(s,e,t,r,o)),p=u.length;p--;)h=U.put(f=new F(e,u[p].replace(T,""),r,o,L(u[p].replace(I,""),"."),d,!1)),f[N]&&h&&Y(e,f.eventType,!0,f.customType);return e}for(l in t)t.hasOwnProperty(l)&&c.call(this,e,l,t[l])}e=e||"bean",t=t||this;var l,u,p,d,f,h,A,g,m,y,v,w,b,C,E=window,M=t[e],I=/[^\.]*(?=\..*)\.|.*/,T=/\..*/,k="addEventListener",S=document||{},D=S.documentElement||{},x=D[k],N=x?k:"attachEvent",j={},B=Array.prototype.slice,L=function(e,t){return e.split(t||" ")},_=function(e){return"string"==typeof e},O=function(e){return"function"==typeof e},R=function(e,t,n){for(n=0;n<t.length;n++)t[n]&&(e[t[n]]=1);return e}({},L("click dblclick mouseup mousedown contextmenu mousewheel mousemultiwheel DOMMouseScroll mouseover mouseout mousemove selectstart selectend keydown keypress keyup orientationchange focus blur change reset select submit load unload beforeunload resize move DOMContentLoaded readystatechange message error abort scroll "+(x?"show input invalid touchstart touchmove touchend touchcancel gesturestart gesturechange gestureend textinput readystatechange pageshow pagehide popstate hashchange offline online afterprint beforeprint dragstart dragenter dragover dragleave drag drop dragend loadstart progress suspend emptied stalled loadmetadata loadeddata canplay canplaythrough playing waiting seeking seeked ended durationchange timeupdate play pause ratechange volumechange cuechange checking noupdate downloading cached updateready obsolete ":""))),P=(C="compareDocumentPosition"in D?function(e,t){return t.compareDocumentPosition&&16==(16&t.compareDocumentPosition(e))}:"contains"in D?function(e,t){return(t=9===t.nodeType||t===window?D:t)!==e&&t.contains(e)}:function(e,t){for(;e=e.parentNode;)if(e===t)return 1;return 0},{mouseenter:{base:"mouseover",condition:Z},mouseleave:{base:"mouseout",condition:Z},mousewheel:{base:/Firefox/.test(navigator.userAgent)?"DOMMouseScroll":"mousewheel"}}),Q=(p=L("altKey attrChange attrName bubbles cancelable ctrlKey currentTarget detail eventPhase getModifierState isTrusted metaKey relatedNode relatedTarget shiftKey srcElement target timeStamp type view which propertyName"),d=p.concat(L("button buttons clientX clientY dataTransfer fromElement offsetX offsetY pageX pageY screenX screenY toElement")),f=d.concat(L("wheelDelta wheelDeltaX wheelDeltaY wheelDeltaZ axis")),h=p.concat(L("char charCode key keyCode keyIdentifier keyLocation location")),A=p.concat(L("data")),g=p.concat(L("touches targetTouches changedTouches scale rotation")),m=p.concat(L("data origin source")),y=p.concat(L("state")),v=/over|out/,w=[{reg:/key/i,fix:function(e,t){return t.keyCode=e.keyCode||e.which,h}},{reg:/click|mouse(?!(.*wheel|scroll))|menu|drag|drop/i,fix:function(e,t,n){return t.rightClick=3===e.which||2===e.button,t.pos={x:0,y:0},e.pageX||e.pageY?(t.clientX=e.pageX,t.clientY=e.pageY):(e.clientX||e.clientY)&&(t.clientX=e.clientX+S.body.scrollLeft+D.scrollLeft,t.clientY=e.clientY+S.body.scrollTop+D.scrollTop),v.test(n)&&(t.relatedTarget=e.relatedTarget||e[("mouseover"==n?"from":"to")+"Element"]),d}},{reg:/mouse.*(wheel|scroll)/i,fix:function(){return f}},{reg:/^text/i,fix:function(){return A}},{reg:/^touch|^gesture/i,fix:function(){return g}},{reg:/^message$/i,fix:function(){return m}},{reg:/^popstate$/i,fix:function(){return y}},{reg:/.*/,fix:function(){return p}}],b={},X.prototype.preventDefault=function(){this.originalEvent.preventDefault?this.originalEvent.preventDefault():this.originalEvent.returnValue=!1},X.prototype.stopPropagation=function(){this.originalEvent.stopPropagation?this.originalEvent.stopPropagation():this.originalEvent.cancelBubble=!0},X.prototype.stop=function(){this.preventDefault(),this.stopPropagation(),this.stopped=!0},X.prototype.stopImmediatePropagation=function(){this.originalEvent.stopImmediatePropagation&&this.originalEvent.stopImmediatePropagation(),this.isImmediatePropagationStopped=function(){return!0}},X.prototype.isImmediatePropagationStopped=function(){return this.originalEvent.isImmediatePropagationStopped&&this.originalEvent.isImmediatePropagationStopped()},X.prototype.clone=function(e){var t=new X(this,this.element,this.isNative);return t.currentTarget=e,t},X),F=(K.prototype.inNamespaces=function(e){var t,n,r=0;if(!e)return!0;if(!this.namespaces)return!1;for(t=e.length;t--;)for(n=this.namespaces.length;n--;)e[t]==this.namespaces[n]&&r++;return e.length===r},K.prototype.matches=function(e,t,n){return!(this.element!==e||t&&this.original!==t||n&&this.handler!==n)},K),U=(u={},{has:function(e,t,n,r){var o,i=u[(r?"r":"$")+t];if(i)for(o=i.length;o--;)if(!i[o].root&&i[o].matches(e,n,null))return!0;return!1},get:function(e,t,n,r){var o=[];return J(e,t,n,null,r,function(e){return o.push(e)}),o},put:function(e){var t=!e.root&&!this.has(e.element,e.type,null,!1),n=(e.root?"r":"$")+e.type;return(u[n]||(u[n]=[])).push(e),t},del:function(e){J(e.element,e.type,null,e.handler,e.root,function(e,t,n){return t.splice(n,1),e.removed=!0,0===t.length&&delete u[(e.root?"r":"$")+e.type],!1})},entries:function(){var e,t=[];for(e in u)"$"==e.charAt(0)&&(t=t.concat(u[e]));return t}}),Y=x?function(e,t,n){e[n?k:"removeEventListener"](t,o,!1)}:function(e,t,n,r){var i;n?(U.put(i=new F(e,r||t,function(t){o.call(e,t,r)},o,null,null,!0)),r&&null==e["_on"+r]&&(e["_on"+r]=0),i.target.attachEvent("on"+i.eventType,i.handler)):(i=U.get(e,r||t,o,!0)[0])&&(i.target.detachEvent("on"+i.eventType,i.handler),U.del(i))},z=function(e,t,n,r,o){return function(){r.apply(this,arguments),e(t,n,o)}},G=function(e,t,n,r){t=t&&t.replace(T,"");for(var o=U.get(e,t,null,!1),i={},a=0,s=o.length;a<s;a++)n&&o[a].original!==n||!o[a].inNamespaces(r)||(U.del(o[a]),!i[o[a].eventType]&&o[a][N]&&(i[o[a].eventType]={t:o[a].eventType,c:o[a].type}));for(a in i)U.has(e,i[a].t,null,!1)||Y(e,i[a].t,!1,i[a].c)},W=x?function(e,t,n){var r=S.createEvent(e?"HTMLEvents":"UIEvents");r[e?"initEvent":"initUIEvent"](t,!0,!0,E,1),n.dispatchEvent(r)}:function(e,t,r){r=n(r,e),e?r.fireEvent("on"+t,S.createEventObject()):r["_on"+t]++},H={on:c,add:function(e,t,n,r){return c.apply(null,_(n)?[e,n,t,r].concat(3<arguments.length?B.call(arguments,5):[]):B.call(arguments))},one:function(){return c.apply(j,arguments)},off:s,remove:s,clone:function(e,t,n){for(var r,o,i=U.get(t,n,null,!1),a=i.length,s=0;s<a;s++)i[s].original&&(r=[e,i[s].type],(o=i[s].handler.__beanDel)&&r.push(o.selector),r.push(i[s].original),c.apply(null,r));return e},fire:function(e,t,n){for(var r,o,i,a,s=L(t),c=s.length;c--;)if(t=s[c].replace(T,""),(i=(i=s[c].replace(I,""))&&L(i,"."))||n||!e[N])for(a=U.get(e,t,null,!1),n=[!1].concat(n),r=0,o=a.length;r<o;r++)a[r].inNamespaces(i)&&a[r].handler.apply(e,n);else W(R[t],t,e);return e},Event:Q,setSelectorEngine:r,noConflict:function(){return t[e]=M,this}};function J(e,t,n,r,o,i){var a=o?"r":"$";if(t&&"*"!=t){var s,c=0,l=u[a+t],p="*"==e;if(l)for(s=l.length;c<s;c++)if((p||l[c].matches(e,n,r))&&!i(l[c],l,c,t))return}else for(var d in u)d.charAt(0)==a&&J(e,d.substr(1),n,r,o,i)}function V(e,t,n,r){function o(n,o){return t.apply(e,r?B.call(o,n?0:1).concat(r):o)}function i(n,r){return t.__beanDel?t.__beanDel.ft(n.target,e):r}var a=n?function(e){var t=i(e,this);if(n.apply(t,arguments))return e&&(e.currentTarget=t),o(e,arguments)}:function(e){return t.__beanDel&&(e=e.clone(i(e))),o(e,arguments)};return a.__beanDel=t.__beanDel,a}function K(e,t,r,o,i,a,s){var c=P[t];"unload"==t&&(r=z(G,e,t,r,o)),c&&(c.condition&&(r=V(e,r,c.condition,a)),t=c.base||t),this.isNative=c=R[t]&&!!e[N],this.customType=!x&&!c&&t,this.element=e,this.type=t,this.original=o,this.namespaces=i,this.eventType=x||c?t:"propertychange",this.target=n(e,c),this[N]=!!this.target[N],this.root=s,this.handler=V(e,r,null,a)}function X(e,t,n){if(arguments.length&&(e=e||((t.ownerDocument||t.document||t).parentWindow||E).event,this.originalEvent=e,this.isNative=n,this.isBean=!0,e)){var r,o,i,a,s,c=e.type,l=e.target||e.srcElement;if(this.target=l&&3===l.nodeType?l.parentNode:l,n){if(!(s=b[c]))for(r=0,o=w.length;r<o;r++)if(w[r].reg.test(c)){b[c]=s=w[r].fix;break}for(r=(a=s(e,this,c)).length;r--;)!((i=a[r])in this)&&i in e&&(this[i]=e[i])}}}function Z(e){return(e=e.relatedTarget)?e!==this&&"xul"!==e.prefix&&!/document/.test(this.toString())&&!C(e,this):null==e}return E.attachEvent&&E.attachEvent("onunload",function e(){var t,n=U.entries();for(t in n)n[t].type&&"unload"!==n[t].type&&s(n[t].element,n[t].type);E.detachEvent("onunload",e),E.CollectGarbage&&E.CollectGarbage()}),r(),H},void 0!==t&&t.exports?t.exports=r():this.bean=r()},{}],21:[function(e,t,r){(function(e){var n=this,o="object"==i(r)&&r,a="object"==i(t)&&t&&t.exports==o&&t;(e="object"==i(e)&&e).global!==e&&e.window!==e||(n=e);var s,c,l=**********,u=36,p=26,d=38,f=700,h=/^xn--/,A=/[^ -~]/,g=/\x2E|\u3002|\uFF0E|\uFF61/g,m={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=u-1,v=Math.floor,w=String.fromCharCode;function b(e){throw RangeError(m[e])}function C(e,t){for(var n=e.length;n--;)e[n]=t(e[n]);return e}function E(e,t){return C(e.split(g),t).join(".")}function M(e){for(var t,n,r=[],o=0,i=e.length;o<i;)55296<=(t=e.charCodeAt(o++))&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function I(e){return C(e,function(e){var t="";return 65535<e&&(t+=w((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+w(e)}).join("")}function T(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function k(e,t,n){var r=0;for(e=n?v(e/f):e>>1,e+=v(e/t);y*p>>1<e;r+=u)e=v(e/y);return v(r+(y+1)*e/(e+d))}function S(e){var t,n,r,o,i,a,s,c=[],d=e.length,f=0,h=128,A=72,g=e.lastIndexOf("-");for(g<0&&(g=0),n=0;n<g;++n)128<=e.charCodeAt(n)&&b("not-basic"),c.push(e.charCodeAt(n));for(r=0<g?g+1:0;r<d;){for(o=f,i=1,a=u;d<=r&&b("invalid-input"),s=e.charCodeAt(r++),(u<=(s=s-48<10?s-22:s-65<26?s-65:s-97<26?s-97:u)||s>v((l-f)/i))&&b("overflow"),f+=s*i,!(s<(s=a<=A?1:A+p<=a?p:a-A));a+=u)i>v(l/(s=u-s))&&b("overflow"),i*=s;A=k(f-o,t=c.length+1,0==o),v(f/t)>l-h&&b("overflow"),h+=v(f/t),f%=t,c.splice(f++,0,h)}return I(c)}function D(e){for(var t,n,r,o,i,a,s,c,d,f,h=[],A=(e=M(e)).length,g=128,m=72,y=t=0;y<A;++y)(s=e[y])<128&&h.push(w(s));for(n=r=h.length,r&&h.push("-");n<A;){for(o=l,y=0;y<A;++y)g<=(s=e[y])&&s<o&&(o=s);for(o-g>v((l-t)/(c=n+1))&&b("overflow"),t+=(o-g)*c,g=o,y=0;y<A;++y)if((s=e[y])<g&&++t>l&&b("overflow"),s==g){for(i=t,a=u;!(i<(d=a<=m?1:m+p<=a?p:a-m));a+=u)h.push(w(T(d+(f=i-d)%(d=u-d),0))),i=v(f/d);h.push(w(T(i,0))),m=k(t,c,n==r),t=0,++n}++t,++g}return h.join("")}if(s={version:"1.2.4",ucs2:{decode:M,encode:I},decode:S,encode:D,toASCII:function(e){return E(e,function(e){return A.test(e)?"xn--"+D(e):e})},toUnicode:function(e){return E(e,function(e){return h.test(e)?S(e.slice(4).toLowerCase()):e})}},o&&!o.nodeType)if(a)a.exports=s;else for(c in s)s.hasOwnProperty(c)&&(o[c]=s[c]);else n.punycode=s}).call(this,void 0!==n?n:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],22:[function(e,t,n){var r=e("indexof");function o(e){return!!e}t.exports=function(e){var t=e.classList;if(t)return t;var n={add:i,remove:a,contains:s,toggle:function(e){return s(e)?(a(e),!1):(i(e),!0)},toString:function(){return e.className},length:0,item:function(e){return c()[e]||null}};return n;function i(e){var t=c();-1<r(t,e)||(t.push(e),l(t))}function a(e){var t=c();-1!==(e=r(t,e))&&(t.splice(e,1),l(t))}function s(e){return-1<r(c(),e)}function c(){for(var t=e.className.split(" "),n=o,r=[],i=0;i<t.length;i++)n(t[i])&&r.push(t[i]);return r}function l(t){var r=t.length;e.className=t.join(" "),n.length=r;for(var o=0;o<t.length;o++)n[o]=t[o];delete t[r]}}},{indexof:23}],23:[function(e,t,n){var r=[].indexOf;t.exports=function(e,t){if(r)return e.indexOf(t);for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}},{}],24:[function(e,t,n){t.exports=function(e,t,n,r){if(r=(n=window.getComputedStyle)?n(e):e.currentStyle)return r[t.replace(/-(\w)/gi,function(e,t){return t.toUpperCase()})]}},{}],25:[function(e,t,n){!function(e,r){"use strict";"object"===i(n)?t.exports=r():e.returnExports=r()}(this,function(){function e(e){var t=i(e);return null===e||"object"!==t&&"function"!==t}function t(){}function n(e){var t=!0,n=!0;return e&&(e.call("foo",function(e,n,r){"object"!==i(r)&&(t=!1)}),e.call([1],function(){"use strict";n="string"==typeof this},"x")),!!e&&t&&n}var r,o,a=Array,s=a.prototype,c=Object,l=c.prototype,u=Function.prototype,p=String,d=p.prototype,f=Number,h=f.prototype,A=s.slice,g=s.splice,m=s.push,y=s.unshift,v=s.concat,w=u.call,b=u.apply,C=Math.max,E=Math.min,M=l.toString,I="function"==typeof Symbol&&"symbol"===i(Symbol.toStringTag),T=Function.prototype.toString,k=function(e){if("function"!=typeof e)return!1;if(!I)return"[object Function]"===(t=M.call(e))||"[object GeneratorFunction]"===t;var t=e;try{return T.call(t),!0}catch(e){return!1}},S=RegExp.prototype.exec,D=function(e){if("object"!==i(e))return!1;if(!I)return"[object RegExp]"===M.call(e);try{return S.call(e),!0}catch(e){return!1}},x=String.prototype.valueOf,N=function(e){if("string"==typeof e)return!0;if("object"!==i(e))return!1;if(!I)return"[object String]"===M.call(e);try{return x.call(e),!0}catch(e){return!1}},j=c.defineProperty&&function(){try{var e,t={};for(e in c.defineProperty(t,"x",{enumerable:!1,value:t}),t)return!1;return t.x===t}catch(e){return!1}}(),B=(r=l.hasOwnProperty,o=j?function(e,t,n,r){!r&&t in e||c.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(e,t,n,r){!r&&t in e||(e[t]=n)},function(e,t,n){for(var i in t)r.call(t,i)&&o(e,i,t[i],n)}),L=f.isNaN||function(e){return e!=e},_=function(e){return L(e=+e)?e=0:0!==e&&e!==1/0&&e!==-1/0&&(e=(0<e||-1)*Math.floor(Math.abs(e))),e},O=function(e){if(null==e)throw new TypeError("can't convert "+e+" to object");return c(e)},R=function(e){return e>>>0},P=(B(u,{bind:function(e){var n=this;if(!k(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var r,o=A.call(arguments,1),i=C(0,n.length-o.length),a=[],s=0;s<i;s++)m.call(a,"$"+s);return r=Function("binder","return function ("+a.join(",")+"){ return binder.apply(this, arguments); }")(function(){var t;return this instanceof r?(t=n.apply(this,v.call(o,A.call(arguments))),c(t)===t?t:this):n.apply(e,v.call(o,A.call(arguments)))}),n.prototype&&(t.prototype=n.prototype,r.prototype=new t,t.prototype=null),r}}),w.bind(l.hasOwnProperty)),Q=w.bind(l.toString),F=w.bind(A),U=b.bind(A),Y=w.bind(d.slice),z=w.bind(d.split),G=w.bind(d.indexOf),W=w.bind(m),H=w.bind(l.propertyIsEnumerable),J=w.bind(s.sort),V=a.isArray||function(e){return"[object Array]"===Q(e)},K=(u=1!==[].unshift(0),"a"!==(b=(B(s,{unshift:function(){return y.apply(this,arguments),this.length}},u),B(a,{isArray:V}),c("a")))[0]||!(0 in b));B(s,{forEach:function(e){var t,n=O(this),r=K&&N(this)?z(this,""):n,o=-1,i=R(r.length);if(1<arguments.length&&(t=arguments[1]),!k(e))throw new TypeError("Array.prototype.forEach callback must be a function");for(;++o<i;)o in r&&(void 0===t?e(r[o],o,n):e.call(t,r[o],o,n))}},!n(s.forEach)),B(s,{map:function(e){var t,n=O(this),r=K&&N(this)?z(this,""):n,o=R(r.length),i=a(o);if(1<arguments.length&&(t=arguments[1]),!k(e))throw new TypeError("Array.prototype.map callback must be a function");for(var s=0;s<o;s++)s in r&&(i[s]=void 0===t?e(r[s],s,n):e.call(t,r[s],s,n));return i}},!n(s.map)),B(s,{filter:function(e){var t,n,r=O(this),o=K&&N(this)?z(this,""):r,i=R(o.length),a=[];if(1<arguments.length&&(n=arguments[1]),!k(e))throw new TypeError("Array.prototype.filter callback must be a function");for(var s=0;s<i;s++)s in o&&(t=o[s],void 0===n?e(t,s,r):e.call(n,t,s,r))&&W(a,t);return a}},!n(s.filter)),B(s,{every:function(e){var t,n=O(this),r=K&&N(this)?z(this,""):n,o=R(r.length);if(1<arguments.length&&(t=arguments[1]),!k(e))throw new TypeError("Array.prototype.every callback must be a function");for(var i=0;i<o;i++)if(i in r&&!(void 0===t?e(r[i],i,n):e.call(t,r[i],i,n)))return!1;return!0}},!n(s.every)),B(s,{some:function(e){var t,n=O(this),r=K&&N(this)?z(this,""):n,o=R(r.length);if(1<arguments.length&&(t=arguments[1]),!k(e))throw new TypeError("Array.prototype.some callback must be a function");for(var i=0;i<o;i++)if(i in r&&(void 0===t?e(r[i],i,n):e.call(t,r[i],i,n)))return!0;return!1}},!n(s.some)),l=!1,s.reduce&&(l="object"===i(s.reduce.call("es5",function(e,t,n,r){return r}))),B(s,{reduce:function(e){var t=O(this),n=K&&N(this)?z(this,""):t,r=R(n.length);if(!k(e))throw new TypeError("Array.prototype.reduce callback must be a function");if(0===r&&1===arguments.length)throw new TypeError("reduce of empty array with no initial value");var o,i=0;if(2<=arguments.length)o=arguments[1];else for(;;){if(i in n){o=n[i++];break}if(++i>=r)throw new TypeError("reduce of empty array with no initial value")}for(;i<r;i++)i in n&&(o=e(o,n[i],i,t));return o}},!l),w=!1,s.reduceRight&&(w="object"===i(s.reduceRight.call("es5",function(e,t,n,r){return r}))),B(s,{reduceRight:function(e){var t,n=O(this),r=K&&N(this)?z(this,""):n,o=R(r.length);if(!k(e))throw new TypeError("Array.prototype.reduceRight callback must be a function");if(0===o&&1===arguments.length)throw new TypeError("reduceRight of empty array with no initial value");var i=o-1;if(2<=arguments.length)t=arguments[1];else for(;;){if(i in r){t=r[i--];break}if(--i<0)throw new TypeError("reduceRight of empty array with no initial value")}if(!(i<0))for(;i in r&&(t=e(t,r[i],i,n)),i--;);return t}},!w),u=s.indexOf&&-1!==[0,1].indexOf(1,2),B(s,{indexOf:function(e){var t=K&&N(this)?z(this,""):O(this),n=R(t.length);if(0!==n){var r=0;for(r=0<=(r=1<arguments.length?_(arguments[1]):r)?r:C(0,n+r);r<n;r++)if(r in t&&t[r]===e)return r}return-1}},u),b=s.lastIndexOf&&-1!==[0,1].lastIndexOf(0,-3),B(s,{lastIndexOf:function(e){var t=K&&N(this)?z(this,""):O(this),n=R(t.length);if(0!==n){var r=n-1;for(r=0<=(r=1<arguments.length?E(r,_(arguments[1])):r)?r:n-Math.abs(r);0<=r;r--)if(r in t&&e===t[r])return r}return-1}},b),w=(l=[1,2]).splice(),u=2===l.length&&V(w)&&0===w.length,B(s,{splice:function(e,t){return 0===arguments.length?[]:g.apply(this,arguments)}},!u),b={},s.splice.call(b,0,0,1),l=1===b.length,B(s,{splice:function(e,t){var n;return 0===arguments.length?[]:(n=arguments,this.length=C(_(this.length),0),0<arguments.length&&"number"!=typeof t&&((n=F(arguments)).length<2?W(n,this.length-e):n[1]=_(t)),g.apply(this,n))}},!l),(w=new a(1e5))[8]="x",w.splice(1,1),u=7===w.indexOf("x");(b=[])[256]="a",b.splice(257,0,"b"),B(s,{splice:function(e,t){for(var n,r=O(this),o=[],i=R(r.length),a=_(e),s=a<0?C(i+a,0):E(a,i),c=E(C(_(t),0),i-s),l=0;l<c;)n=p(s+l),P(r,n)&&(o[l]=r[n]),l+=1;var u,d=F(arguments,2),f=d.length;if(f<c){for(l=s;l<i-c;)n=p(l+c),u=p(l+f),P(r,n)?r[u]=r[n]:delete r[u],l+=1;for(l=i;i-c+f<l;)delete r[l-1],--l}else if(c<f)for(l=i-c;s<l;)n=p(l+c-1),u=p(l+f-1),P(r,n)?r[u]=r[n]:delete r[u],--l;l=s;for(var h=0;h<d.length;++h)r[l]=d[h],l+=1;return r.length=i-c+f,o}},!(u&&"a"===b[256]));var X=s.join;try{ee="1,2,3"!==Array.prototype.join.call("123",",")}catch(n){ee=!0}function Z(e){for(var t=O(this),n=R(t.length),r=0;r<arguments.length;)t[n+r]=arguments[r],r+=1;return t.length=n+r,n+r}function q(e){var t=e.constructor;return t&&t.prototype===e}function $(e){return"[object Arguments]"===Q(e)}ee&&B(s,{join:function(e){return e=void 0===e?",":e,X.call(N(this)?z(this,""):this,e)}},ee);(l="1,2"!==[1,2].join(void 0))&&B(s,{join:function(e){return X.call(this,void 0===e?",":e)}},l),w={},u=1!==Array.prototype.push.call(w,void 0)||1!==w.length||void 0!==w[0]||!P(w,0);var ee=(B(s,{push:function(e){return(V(this)?m:Z).apply(this,arguments)}},u),1!==(b=[]).push(void 0)||1!==b.length||void 0!==b[0]||!P(b,0)),te=(l=(B(s,{push:Z},ee),B(s,{slice:function(e,t){var n=N(this)?z(this,""):this;return U(n,arguments)}},K),function(){try{return[1,2].sort(null),[1,2].sort({}),!0}catch(e){}return!1}()),w=function(){try{return[1,2].sort(/a/),!1}catch(e){}return!0}(),u=function(){try{return[1,2].sort(void 0),!0}catch(e){}return!1}(),B(s,{sort:function(e){if(void 0===e)return J(this);if(k(e))return J(this,e);throw new TypeError("Array.prototype.sort callback must be a function")}},l||!u||!w),!{toString:null}.propertyIsEnumerable("toString")),ne=function(){}.propertyIsEnumerable("prototype"),re=!P("x","0"),oe={$window:!0,$console:!0,$parent:!0,$self:!0,$frame:!0,$frames:!0,$frameElement:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$external:!0},ie=function(){if("undefined"!=typeof window)for(var e in window)try{!oe["$"+e]&&P(window,e)&&null!==window[e]&&"object"===i(window[e])&&q(window[e])}catch(e){return!0}return!1}(),ae=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],se=ae.length,ce=$(arguments)?$:function(e){return null!==e&&"object"===i(e)&&"number"==typeof e.length&&0<=e.length&&!V(e)&&k(e.callee)},le=(b=(B(c,{keys:function(e){var t=k(e),n=ce(e),r=null!==e&&"object"===i(e),o=r&&N(e);if(!r&&!t&&!n)throw new TypeError("Object.keys called on a non-object");var a=[],s=ne&&t;if(o&&re||n)for(var c=0;c<e.length;++c)W(a,p(c));if(!n)for(var l in e)s&&"prototype"===l||!P(e,l)||W(a,p(l));if(te)for(var u=function(e){if("undefined"==typeof window||!ie)return q(e);try{return q(e)}catch(e){return!1}}(e),d=0;d<se;d++){var f=ae[d];u&&"constructor"===f||!P(e,f)||W(a,f)}return a}}),c.keys&&function(){return 2===c.keys(arguments).length}(1,2)),ee=c.keys&&function(){var e=c.keys(arguments);return 1!==arguments.length||1!==e.length||1!==e[0]}(1),c.keys),ue=(B(c,{keys:function(e){return ce(e)?le(F(e)):le(e)}},!b||ee),-621987552e5);s=Date.prototype.toISOString&&-1===new Date(ue).toISOString().indexOf("-000001"),l=Date.prototype.toISOString&&"1969-12-31T23:59:59.999Z"!==new Date(-1).toISOString();B(Date.prototype,{toISOString:function(){var e,t,n,r,o;if(!isFinite(this))throw new RangeError("Date.prototype.toISOString called on non-finite value.");for(r=this.getUTCFullYear(),o=this.getUTCMonth(),r+=Math.floor(o/12),e=[1+(o%12+12)%12,this.getUTCDate(),this.getUTCHours(),this.getUTCMinutes(),this.getUTCSeconds()],r=(r<0?"-":9999<r?"+":"")+Y("00000"+Math.abs(r),0<=r&&r<=9999?-4:-6),t=e.length;t--;)(n=e[t])<10&&(e[t]="0"+n);return r+"-"+F(e,0,2).join("-")+"T"+F(e,2).join(":")+"."+Y("000"+this.getUTCMilliseconds(),-3)+"Z"}},s||l),!function(){try{return Date.prototype.toJSON&&null===new Date(NaN).toJSON()&&-1!==new Date(ue).toJSON().indexOf("-000001")&&Date.prototype.toJSON.call({toISOString:function(){return!0}})}catch(e){return!1}}()&&(Date.prototype.toJSON=function(t){var n=c(this),r=function(t){var n,r;if(e(t))return t;if(r=t.valueOf,k(r)&&e(n=r.call(t)))return n;if(r=t.toString,k(r)&&e(n=r.call(t)))return n;throw new TypeError}(n);if("number"==typeof r&&!isFinite(r))return null;if(r=n.toISOString,k(r))return r.call(n);throw new TypeError("toISOString property is not callable")});u=1e15===Date.parse("+033658-09-27T01:46:40.000Z"),w=!isNaN(Date.parse("2012-04-04T24:00:00.500Z"))||!isNaN(Date.parse("2012-11-31T23:59:59.000Z"))||!isNaN(Date.parse("2012-12-31T23:59:60.000Z")),!isNaN(Date.parse("2000-01-01T00:00:00.000Z"))&&!w&&u||(pe=Math.pow(2,31)-1,de=L(new Date(1970,0,1,0,0,0,pe+1).getTime()),Date=function(t){function n(r,o,i,a,s,c,l){var u,d,f=arguments.length,h=this instanceof t?(u=c,d=l,de&&7<=f&&pe<l&&(h=Math.floor(l/pe)*pe,u+=h=Math.floor(h/1e3),d-=1e3*h),1===f&&p(r)===r?new t(n.parse(r)):7<=f?new t(r,o,i,a,s,u,d):6<=f?new t(r,o,i,a,s,u):5<=f?new t(r,o,i,a,s):4<=f?new t(r,o,i,a):3<=f?new t(r,o,i):2<=f?new t(r,o):1<=f?new t(r):new t):t.apply(this,arguments);return e(h)||B(h,{constructor:n},!0),h}function r(e,t){var n=1<t?1:0;return a[t]+Math.floor((e-1969+n)/4)-Math.floor((e-1901+n)/100)+Math.floor((e-1601+n)/400)+365*(e-1970)}var o,i=new RegExp("^(\\d{4}|[+-]\\d{6})(?:-(\\d{2})(?:-(\\d{2})(?:T(\\d{2}):(\\d{2})(?::(\\d{2})(?:(\\.\\d{1,}))?)?(Z|(?:([-+])(\\d{2}):(\\d{2})))?)?)?)?$"),a=[0,31,59,90,120,151,181,212,243,273,304,334,365];for(o in t)P(t,o)&&(n[o]=t[o]);return B(n,{now:t.now,UTC:t.UTC},!0),n.prototype=t.prototype,B(n.prototype,{constructor:n},!0),B(n,{parse:function(e){var n,o,a,s,c,l,u,p,d,h,A=i.exec(e);return A?(u=f(A[1]),p=f(A[2]||1)-1,h=f(A[3]||1)-1,d=f(A[4]||0),n=f(A[5]||0),o=f(A[6]||0),a=Math.floor(1e3*f(A[7]||0)),s=Boolean(A[4]&&!A[8]),c="-"===A[9]?1:-1,l=f(A[10]||0),A=f(A[11]||0),d<(0<n||0<o||0<a?24:25)&&n<60&&o<60&&a<1e3&&-1<p&&p<12&&l<24&&A<60&&-1<h&&h<r(u,1+p)-r(u,p)&&(u=1e3*(60*((u=60*(24*(r(u,p)+h)+d+l*c))+n+A*c)+o)+a,s&&(p=u,h=0,de&&pe<p&&(d=Math.floor(p/pe)*pe,h+=d=Math.floor(d/1e3),p-=1e3*d),u=f(new t(1970,0,1,0,0,h,p))),-864e13<=u)&&u<=864e13?u:NaN):t.parse.apply(this,arguments)}}),n}(Date)),Date.now||(Date.now=function(){return(new Date).getTime()}),b=h.toFixed&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0));var pe,de,fe,he,Ae={base:1e7,size:6,data:[0,0,0,0,0,0],multiply:function(e,t){for(var n=-1,r=t;++n<Ae.size;)r+=e*Ae.data[n],Ae.data[n]=r%Ae.base,r=Math.floor(r/Ae.base)},divide:function(e){for(var t=Ae.size,n=0;0<=--t;)n+=Ae.data[t],Ae.data[t]=Math.floor(n/e),n=n%e*Ae.base},numToString:function(){for(var e,t=Ae.size,n="";0<=--t;)""===n&&0!==t&&0===Ae.data[t]||(e=p(Ae.data[t]),""===n?n=e:n+=Y("0000000",0,7-e.length)+e);return n},pow:function e(t,n,r){return 0===n?r:n%2==1?e(t,n-1,r*t):e(t*t,n/2,r)},log:function(e){for(var t=0,n=e;4096<=n;)t+=12,n/=4096;for(;2<=n;)t+=1,n/=2;return t}},ge=(ee=(B(h,{toFixed:function(e){var t,n,r,o,i;e=f(e);if((e=L(e)?0:Math.floor(e))<0||20<e)throw new RangeError("Number.toFixed called with invalid number of decimals");if(i=f(this),L(i))return"NaN";if(i<=-1e21||1e21<=i)return p(i);if(t="",i<0&&(t="-",i=-i),n="0",1e-21<i)if(i=(r=Ae.log(i*Ae.pow(2,69,1))-69)<0?i*Ae.pow(2,-r,1):i/Ae.pow(2,r,1),i*=4503599627370496,0<(r=52-r)){for(Ae.multiply(0,i),o=e;7<=o;)Ae.multiply(1e7,0),o-=7;for(Ae.multiply(Ae.pow(10,o,1),0),o=r-1;23<=o;)Ae.divide(1<<23),o-=23;Ae.divide(1<<o),Ae.multiply(1,1),Ae.divide(2),n=Ae.numToString()}else Ae.multiply(0,i),Ae.multiply(1<<-r,0),n=Ae.numToString()+Y("0.00000000000000000000",2,2+e);return 0<e?(i=n.length)<=e?t+Y("0.0000000000000000000",0,e-i+2)+n:t+Y(n,0,i-e)+"."+Y(n,i-e):t+n}},b),function(){try{return"1"===1..toPrecision(void 0)}catch(e){return!0}}()),h.toPrecision),me=(B(h,{toPrecision:function(e){return void 0===e?ge.call(this):ge.call(this,e)}},ee),2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||1<".".split(/()()/).length?(fe=void 0===/()??/.exec("")[1],he=Math.pow(2,32)-1,d.split=function(e,t){var n=String(this);if(void 0===e&&0===t)return[];if(!D(e))return z(this,e,t);for(var r,o,i,a=[],s=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),c=0,l=new RegExp(e.source,s+"g"),u=(fe||(r=new RegExp("^"+l.source+"$(?!\\s)",s)),void 0===t?he:R(t)),p=l.exec(n);p&&!(c<(o=p.index+p[0].length)&&(W(a,Y(n,c,p.index)),!fe&&1<p.length&&p[0].replace(r,function(){for(var e=1;e<arguments.length-2;e++)void 0===arguments[e]&&(p[e]=void 0)}),1<p.length&&p.index<n.length&&m.apply(a,F(p,1)),i=p[0].length,c=o,u<=a.length));)l.lastIndex===p.index&&l.lastIndex++,p=l.exec(n);return c===n.length?!i&&l.test("")||W(a,""):W(a,Y(n,c)),u<a.length?Y(a,0,u):a}):"0".split(void 0,0).length&&(d.split=function(e,t){return void 0===e&&0===t?[]:z(this,e,t)}),d.replace);ye=[],"x".replace(/x(.)?/g,function(e,t){W(ye,t)}),1===ye.length&&void 0===ye[0]||(d.replace=function(e,t){var n=k(t),r=D(e)&&/\)[*?]/.test(e.source);return n&&r?me.call(this,e,function(n){var r=arguments.length,o=e.lastIndex,i=(e.lastIndex=0,e.exec(n)||[]);return e.lastIndex=o,W(i,arguments[r-2],arguments[r-1]),t.apply(this,i)}):me.call(this,e,t)});var ye,ve,we,be=d.substr,Ce=(s="".substr&&"b"!=="0b".substr(-1),w="["+(l=(B(d,{substr:function(e,t){var n=e;return e<0&&(n=C(this.length+e,0)),be.call(this,n,t)}},s),"\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"))+"]",new RegExp("^"+w+w+"*")),Ee=new RegExp(w+w+"*$"),Me=(u=d.trim&&(l.trim()||!"​".trim()),b=(B(d,{trim:function(){if(null==this)throw new TypeError("can't convert "+this+" to object");return p(this).replace(Ce,"").replace(Ee,"")}},u),d.lastIndexOf&&-1!=="abcあい".lastIndexOf("あい",2)),B(d,{lastIndexOf:function(e){if(null==this)throw new TypeError("can't convert "+this+" to object");for(var t=p(this),n=p(e),r=1<arguments.length?f(arguments[1]):NaN,o=(r=L(r)?1/0:_(r),E(C(r,0),t.length)),i=n.length,a=o+i;0<a;){a=C(0,a-i);var s=G(Y(t,a,o+i),n);if(-1!==s)return a+s}return-1}},b),d.lastIndexOf);B(d,{lastIndexOf:function(e){return Me.apply(this,arguments)}},1!==d.lastIndexOf.length),8===parseInt(l+"08")&&22===parseInt(l+"0x16")||(parseInt=(ve=parseInt,we=/^[\-+]?0[xX]/,function(e,t){return e=p(e).trim(),t=f(t)||(we.test(e)?16:10),ve(e,t)})),"RangeError: test"!==String(new RangeError("test"))&&(Error.prototype.toString=function(){if(null==this)throw new TypeError("can't convert "+this+" to object");var e=this.name,t=(void 0===e?e="Error":"string"!=typeof e&&(e=p(e)),this.message);return void 0===t?t="":"string"!=typeof t&&(t=p(t)),e?t?e+": "+t:e:t}),j&&((h=function(e,t){var n;H(e,t)&&((n=Object.getOwnPropertyDescriptor(e,t)).enumerable=!1,Object.defineProperty(e,t,n))})(Error.prototype,"message"),""!==Error.prototype.message&&(Error.prototype.message=""),h(Error.prototype,"name")),"/a/gim"!==String(/a/gim)&&(RegExp.prototype.toString=function(){var e="/"+this.source+"/";return this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),e})})},{}],26:[function(e,t,n){var r=[],o=r.forEach,i=r.slice;t.exports=function(e){return o.call(i.call(arguments,1),function(t){if(t)for(var n in t)e[n]=t[n]}),e}},{}],27:[function(e,t,n){t.exports=function(e){var t=r.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)};var r=Object.prototype.toString},{}],28:[function(e,t,n){"use strict";t.exports=function(e){return"object"===i(e)&&null!==e}},{}],29:[function(e,t,n){
/*!
        * $script.js JS loader & dependency manager
        * https://github.com/ded/script.js
        * (c) Dustin Diaz 2014 | License MIT
        */
!function(e){void 0!==t&&t.exports?t.exports=e():this.$script=e()}(function(){var e,t,n=document,r=n.getElementsByTagName("head")[0],o=!1,i="readyState",a="onreadystatechange",s={},c={},l={};function u(e,t){for(var n=0,r=e.length;n<r;++n)if(!t(e[n]))return o;return 1}function p(e,t){u(e,function(e){return!t(e)})}function d(t,n,r){t=t.push?t:[t];var o=n&&n.call,i=o?n:r,a=o?t.join(""):n,h=t.length;function A(e){return e.call?e():s[e]}function g(){if(!--h)for(var e in s[a]=1,i&&i(),c)u(e.split("|"),A)&&(p(c[e],A),c[e]=[])}return setTimeout(function(){p(t,function t(n,r){return null===n?(g(),0):(r||/^https?:\/\//.test(n)||!e||(n=-1===n.indexOf(".js")?e+n+".js":e+n),l[n]?2==l[n]?g():setTimeout(function(){t(n,!0)},0):(l[n]=1,void f(n,g)))})},0),d}function f(e,o){var s,c=n.createElement("script");c.onload=c.onerror=c[a]=function(){c[i]&&!/^c|loade/.test(c[i])||s||(c.onload=c[a]=null,s=1,l[e]=2,o())},c.async=1,c.src=t?e+(-1===e.indexOf("?")?"?":"&")+t:e,r.insertBefore(c,r.lastChild)}return d.get=f,d.order=function(e,t,n){!function r(o){o=e.shift(),e.length?d(o,r):d(o,t,n)}()},d.path=function(t){e=t},d.urlArgs=function(e){t=e},d.ready=function(e,t,n){e=e.push?e:[e];var r=[];return p(e,function(e){s[e]||r.push(e)}),u(e,function(e){return s[e]})?t():(e=e.join("|"),c[e]=c[e]||[],c[e].push(t),n&&n(r)),d},d.done=function(e){d([null],e)},d})},{}]},{},[19])(19)},"object"==i(t)&&void 0!==e?e.exports=k():void 0!==(r="function"==typeof(A=k)?A.apply(t,[]):A)&&(e.exports=r)}).call(this,n(14))},function(e,t,n){var r=n(34),o=("string"==typeof r&&(r=[[e.i,r,""]]),{hmr:!0});o.transform=void 0,o.insertInto=void 0,n(42)(r,o),r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(35)(!1);var r=(s=n(36))(n(37)),o=s(n(38)+"?#iefixyg5dv7"),i=s(n(39)),a=s(n(40)),s=s(n(41)+"#fpicons");t.push([e.i,"@font-face {\n\tfont-family: 'fpicons';\n\tsrc: url("+r+");\n\tsrc: url("+o+") format('embedded-opentype'),\n\t\turl("+i+") format('woff'), url("+a+") format('truetype'),\n\t\turl("+s+') format(\'svg\');\n\tfont-weight: normal;\n\tfont-style: normal;\n}\n\n[class^="fp-i-"],\n[class*=" fp-i-"] {\n\tfont-family: \'fpicons\';\n\tspeak: none;\n\tfont-style: normal;\n\tfont-weight: normal;\n\tfont-variant: normal;\n\ttext-transform: none;\n\tline-height: 1;\n\n\t/* Better Font Rendering =========== */\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n}\n.flowplayer {\n\tposition: relative;\n\twidth: 100%;\n\tcounter-increment: flowplayer;\n\tbackground-size: contain;\n\tbackground-repeat: no-repeat;\n\tbackground-position: center center;\n\tdisplay: inline-block;\n}\n.flowplayer * {\n\tfont-weight: inherit;\n\tfont-family: inherit;\n\tfont-style: inherit;\n\ttext-decoration: inherit;\n\tfont-size: 100%;\n\tpadding: 0;\n\tborder: 0;\n\tmargin: 0;\n\tlist-style-type: none;\n}\n.flowplayer a:focus {\n\toutline: 0;\n}\n.flowplayer video {\n\twidth: 100%;\n}\n.flowplayer.is-ipad video {\n\t-webkit-transform: translateX(-2048px);\n}\n.is-ready.flowplayer.is-ipad video {\n\t-webkit-transform: translateX(0);\n}\n.flowplayer .fp-player {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n}\n.flowplayer .fp-engine,\n.flowplayer .fp-ui,\n.flowplayer .fp-message {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tcursor: default;\n\tz-index: 1;\n}\n.flowplayer .fp-ui {\n\tz-index: 1;\n\t-moz-user-select: none;\n\tuser-select: none;\n}\n.flowplayer .fp-message {\n\tdisplay: none;\n\ttext-align: center;\n\tcursor: default;\n\twidth: 100%;\n}\n.flowplayer .fp-title {\n\tline-height: 30px;\n\tfont-weight: normal;\n\tfont-family: \'myriad pro\', Helvetica, Arial, sans-serif;\n\tfont-size: 11px;\n\tcursor: default;\n\tcolor: #fff;\n\twidth: auto;\n\tmax-width: 50%;\n\twhite-space: nowrap;\n\ttext-overflow: ellipsis;\n\toverflow: hidden;\n\tfloat: left;\n\tpadding: 0 20px;\n}\n.is-rtl.flowplayer .fp-title {\n\tfloat: right;\n}\n.no-background.flowplayer .fp-play,\n.no-background.flowplayer .fp-brand {\n\tbackground-color: transparent !important;\n\tbackground-image: -webkit-gradient(linear, left top, left bottom, from(transparent), to(transparent)) !important;\n\tbackground-image: -webkit-linear-gradient(top, transparent, transparent) !important;\n\tbackground-image: -moz-linear-gradient(top, transparent, transparent) !important;\n\tbackground-image: -o-linear-gradient(top, transparent, transparent) !important;\n\tbackground-image: linear-gradient(to bottom, transparent, transparent) !important;\n\ttext-shadow: 0 0 1px #000;\n}\n.flowplayer .fp-buffer {\n\tbackground-color: #eee;\n}\n.flowplayer .fp-waiting {\n\tdisplay: none;\n\tmargin: 0 auto;\n\ttext-align: center;\n}\n.flowplayer .fp-waiting em {\n\twidth: 1em;\n\theight: 1em;\n\t-webkit-border-radius: 1em;\n\t-moz-border-radius: 1em;\n\tborder-radius: 1em;\n\tbackground-color: rgba(255, 255, 255, 0.8);\n\tdisplay: inline-block;\n\t-webkit-animation: pulse .6s infinite;\n\t-moz-animation: pulse .6s infinite;\n\tanimation: pulse .6s infinite;\n\tmargin: .3em;\n\topacity: 0;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n}\n.flowplayer .fp-waiting em:nth-child(1) {\n\t-webkit-animation-delay: .3s;\n\t-moz-animation-delay: .3s;\n\tanimation-delay: .3s;\n}\n.flowplayer .fp-waiting em:nth-child(2) {\n\t-webkit-animation-delay: .45s;\n\t-moz-animation-delay: .45s;\n\tanimation-delay: .45s;\n}\n.flowplayer .fp-waiting em:nth-child(3) {\n\t-webkit-animation-delay: .6s;\n\t-moz-animation-delay: .6s;\n\tanimation-delay: .6s;\n}\n.flowplayer .fp-waiting p {\n\tcolor: #ccc;\n\tfont-weight: bold;\n}\n.flowplayer .fp-speed {\n\tfont-size: 30px;\n\tbackground-color: #333;\n\tbackground-color: rgba(51, 51, 51, 0.8);\n\tcolor: #eee;\n\tmargin: 0 auto;\n\ttext-align: center;\n\twidth: 120px;\n\tpadding: .1em 0 0;\n\topacity: 0;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t-webkit-transition: opacity .5s;\n\t-moz-transition: opacity .5s;\n\ttransition: opacity .5s;\n}\n.flowplayer .fp-speed.fp-hilite {\n\topacity: 1;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n}\n.flowplayer .fp-help {\n\tposition: absolute;\n\ttop: 0;\n\tleft: -9999em;\n\tz-index: 100;\n\tbackground-color: #333;\n\tbackground-color: rgba(51, 51, 51, 0.9);\n\twidth: 100%;\n\theight: 100%;\n\topacity: 0;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t-webkit-transition: opacity .2s;\n\t-moz-transition: opacity .2s;\n\ttransition: opacity .2s;\n\ttext-align: center;\n}\n.is-help.flowplayer .fp-help {\n\tleft: 0;\n\topacity: 1;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n}\n.flowplayer .fp-help .fp-help-section {\n\tmargin: 3%;\n\tdirection: ltr;\n}\n.flowplayer .fp-help .fp-help-basics {\n\tmargin-top: 6%;\n}\n.flowplayer .fp-help p {\n\tcolor: #eee;\n\tmargin: .5em 0;\n\tfont-size: 14px;\n\tline-height: 1.5;\n\tdisplay: inline-block;\n\tmargin: 1% 2%;\n}\n.flowplayer .fp-help em {\n\tbackground: #eee;\n\t-webkit-border-radius: .3em;\n\t-moz-border-radius: .3em;\n\tborder-radius: .3em;\n\tmargin-right: .4em;\n\tpadding: .3em .6em;\n\tcolor: #333;\n}\n.flowplayer .fp-help small {\n\tfont-size: 90%;\n\tcolor: #aaa;\n}\n.flowplayer .fp-help .fp-close {\n\tdisplay: block;\n}\n@media (max-width: 600px) {\n\t.flowplayer .fp-help p {\n\t\tfont-size: 9px;\n\t}\n}\n.flowplayer .fp-dropdown {\n\tposition: absolute;\n\ttop: 5px;\n\twidth: 100px;\n\tbackground-color: #000 !important;\n\t-webkit-border-radius: 3px;\n\t-moz-border-radius: 3px;\n\tborder-radius: 3px;\n\tbox-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tmargin: 0 !important;\n\tlist-style-type: none !important;\n}\n.flowplayer .fp-dropdown:before {\n\tcontent: \'\';\n\tdisplay: block;\n\tposition: absolute;\n\ttop: -5px;\n\tleft: calc(50% - 5px);\n\twidth: 0;\n\theight: 0;\n\tborder-left: 5px solid transparent;\n\tborder-right: 5px solid transparent;\n\tborder-bottom: 5px solid rgba(51, 51, 51, 0.9);\n}\n.flowplayer .fp-dropdown li {\n\tpadding: 10px !important;\n\tmargin: 0 !important;\n\tcolor: #fff !important;\n\tfont-size: 11px !important;\n\tlist-style-type: none !important;\n}\n.flowplayer .fp-dropdown li.active {\n\tbackground-color: #00a7c8 !important;\n\tcursor: default !important;\n}\n.flowplayer .fp-dropdown.fp-dropup {\n\tbottom: 20px;\n\ttop: auto;\n}\n.flowplayer .fp-dropdown.fp-dropup:before {\n\ttop: auto;\n\tbottom: -5px;\n\tborder-bottom: none;\n\tborder-top: 5px solid rgba(51, 51, 51, 0.9);\n}\n.flowplayer .fp-tooltip {\n\tbackground-color: #000;\n\tcolor: #fff;\n\tdisplay: none;\n\tposition: absolute;\n\tpadding: 5px;\n}\n.flowplayer .fp-tooltip:before {\n\tcontent: \'\';\n\tdisplay: block;\n\tposition: absolute;\n\tbottom: -5px;\n\twidth: 0;\n\theight: 0;\n\tleft: calc(50% - 5px);\n\tborder-left: 5px solid transparent;\n\tborder-right: 5px solid transparent;\n\tborder-top: 5px solid #000;\n}\n.flowplayer .fp-subtitle {\n\tposition: absolute;\n\tbottom: 40px;\n\tleft: -99999em;\n\tz-index: 10;\n\ttext-align: center;\n\twidth: 100%;\n\topacity: 0;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t-webkit-transition: opacity .3s;\n\t-moz-transition: opacity .3s;\n\ttransition: opacity .3s;\n}\n.flowplayer .fp-subtitle p {\n\tdisplay: inline;\n\tbackground-color: #333;\n\tbackground-color: rgba(51, 51, 51, 0.9);\n\tcolor: #eee;\n\tpadding: .1em .4em;\n\tfont-size: 16px;\n\tline-height: 1.6;\n}\n.flowplayer .fp-subtitle p:after {\n\tcontent: \'\';\n\tclear: both;\n}\n.flowplayer .fp-subtitle p b {\n\tfont-weight: bold;\n}\n.flowplayer .fp-subtitle p i {\n\tfont-style: italic;\n}\n.flowplayer .fp-subtitle p u {\n\ttext-decoration: underline;\n}\n.flowplayer .fp-subtitle.fp-active {\n\tleft: 0;\n\topacity: 1;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n}\n.flowplayer .fp-fullscreen,\n.flowplayer .fp-unload,\n.flowplayer .fp-mute,\n.flowplayer .fp-embed,\n.flowplayer .fp-close,\n.flowplayer .fp-play,\n.flowplayer .fp-menu {\n\tfont-family: \'fpicons\' !important;\n\tcolor: #fff !important;\n\tfont-size: 15px !important;\n\ttext-align: center !important;\n\tline-height: 30px !important;\n\ttext-decoration: none !important;\n}\n.is-rtl.flowplayer .fp-fullscreen,\n.is-rtl.flowplayer .fp-unload,\n.is-rtl.flowplayer .fp-mute,\n.is-rtl.flowplayer .fp-embed,\n.is-rtl.flowplayer .fp-close,\n.is-rtl.flowplayer .fp-play,\n.is-rtl.flowplayer .fp-menu {\n\t-webkit-transform: scale(-1, 1);\n\t-moz-transform: scale(-1, 1);\n\ttransform: scale(-1, 1);\n}\n.is-rtl.flowplayer .fp-menu {\n\t-webkit-transform: none;\n\t-moz-transform: none;\n\ttransform: none;\n}\n.flowplayer .fp-fullscreen:before {\n\tcontent: "\\e602";\n}\n.flowplayer .fp-unload:before,\n.flowplayer .fp-close:before {\n\tcontent: "\\e600";\n}\n.flowplayer .fp-mute:before {\n\tcontent: "\\e606";\n}\n.flowplayer .fp-embed:before {\n\tcontent: "\\e603";\n}\n.flowplayer .fp-play:before {\n\tcontent: "\\e608";\n}\n.flowplayer .fp-menu:before {\n\tcontent: "\\e604";\n}\n.flowplayer .fp-flash-disabled {\n\tbackground: #333;\n\twidth: 390px;\n\tmargin: 0 auto;\n\tposition: absolute;\n\tbottom: 0;\n\tcolor: #fff;\n}\n.is-splash.flowplayer .fp-ui,\n.is-paused.flowplayer .fp-ui {\n\tbackground-color: #292929;\n}\n/* .is-rtl.is-splash.flowplayer .fp-ui,.is-rtl.is-paused.flowplayer .fp-ui{background:url(img/.png)} */\n.is-fullscreen.flowplayer .fp-ui {\n\tbackground-size: auto;\n}\n.flowplayer .fp-brand {\n\tcolor: #fff !important;\n\tposition: absolute;\n\tright: 115px;\n\tfont-weight: normal !important;\n\tfont-family: \'myriad pro\', Helvetica, Arial, sans-serif !important;\n\ttext-decoration: none !important;\n\tline-height: 15px !important;\n\tfont-size: 11px !important;\n\theight: 15px;\n\twidth: 55px;\n\tbottom: 9px;\n\tbox-sizing: border-box;\n\ttext-align: center;\n\tpadding: 1px;\n\twhite-space: nowrap;\n\ttext-overflow: ellipsis;\n\toverflow: hidden;\n}\n.has-menu.flowplayer .fp-brand {\n\tright: 152px;\n}\n.is-rtl.flowplayer .fp-brand {\n\tright: auto;\n\tleft: 125px;\n}\n.has-menu.is-rtl.flowplayer .fp-brand {\n\tleft: 152px;\n}\n.no-brand.flowplayer .fp-brand {\n\tdisplay: none;\n}\n.no-mute.flowplayer .fp-brand {\n\tright: 95px;\n}\n.flowplayer .fp-logo {\n\tposition: absolute;\n\ttop: auto;\n\tleft: 15px;\n\tbottom: 40px;\n\tcursor: pointer;\n\tdisplay: none;\n\tz-index: 100;\n}\n.flowplayer .fp-logo img {\n\twidth: 100%;\n}\n.is-embedded.flowplayer .fp-logo {\n\tdisplay: block;\n}\n.fixed-controls.flowplayer .fp-logo {\n\tbottom: 15px;\n}\n.flowplayer .fp-fullscreen,\n.flowplayer .fp-unload,\n.flowplayer .fp-close {\n\tposition: absolute;\n\ttop: 10px;\n\tleft: auto;\n\tright: 10px;\n\tdisplay: block;\n\twidth: 30px;\n\theight: 23px;\n\ttext-align: center;\n\tcursor: pointer;\n\theight: 30px;\n\twidth: 30px;\n}\n.is-rtl.flowplayer .fp-fullscreen,\n.is-rtl.flowplayer .fp-unload,\n.is-rtl.flowplayer .fp-close {\n\tright: auto;\n\tleft: 10px;\n}\n.flowplayer .fp-unload,\n.flowplayer .fp-close {\n\tdisplay: none;\n}\n.flowplayer .fp-play {\n\tdisplay: none;\n\theight: 30px !important;\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\ttext-align: center;\n}\n.is-rtl.flowplayer .fp-play {\n\tleft: auto;\n\tright: 0;\n}\n.is-playing.flowplayer .fp-play:before {\n\tcontent: "\\e607";\n}\n.flowplayer .fp-menu {\n\tdisplay: none;\n\tposition: absolute;\n\tbottom: 0;\n\tz-index: 11;\n\tright: 10px;\n}\n.is-rtl.flowplayer .fp-menu {\n\tright: auto;\n\tleft: 10px;\n}\n.has-menu.flowplayer .fp-menu {\n\tdisplay: block;\n}\n.flowplayer .fp-menu .fp-dropdown {\n\tz-index: 12;\n\tdisplay: none;\n\tleft: -42.5px;\n\tline-height: auto;\n\twidth: 149px;\n\t-webkit-transform: none;\n\t-moz-transform: none;\n\ttransform: none;\n}\n.is-rtl.flowplayer .fp-menu .fp-dropdown {\n\tleft: -10px;\n}\n.flowplayer .fp-menu.dropdown-open .fp-dropdown {\n\tdisplay: block;\n}\n.flowplayer.is-ready.is-closeable .fp-unload {\n\tdisplay: block;\n}\n.flowplayer.is-ready.is-closeable .fp-embed {\n\tright: 90px;\n}\n.flowplayer.is-ready.is-closeable .fp-fullscreen {\n\tright: 50px;\n}\n.flowplayer.is-ready.is-closeable.is-rtl .fp-embed {\n\tright: auto;\n\tleft: 90px;\n}\n.flowplayer.is-ready.is-closeable.is-rtl .fp-fullscreen {\n\tright: auto;\n\tleft: 50px;\n}\n.flowplayer.is-fullscreen .fp-fullscreen {\n\tdisplay: block !important;\n}\n.flowplayer.is-fullscreen .fp-fullscreen:before {\n\tcontent: "\\e601";\n}\n.flowplayer .fp-progress.animated {\n\ttransition-timing-function: linear;\n\ttransition-property: width, height;\n}\n.flowplayer.is-touch .fp-progress {\n\t-webkit-transition: width .2s linear;\n\t-moz-transition: width .2s linear;\n\ttransition: width .2s linear;\n\tbox-sizing: border-box;\n}\n.flowplayer .fp-mute {\n\tposition: relative;\n\twidth: 30px;\n\theight: 30px;\n\tfloat: left;\n\ttop: -12px;\n\tcursor: pointer;\n}\n.is-rtl.flowplayer .fp-mute {\n\tfloat: right;\n}\n.no-mute.flowplayer .fp-mute {\n\tdisplay: none;\n}\n.flowplayer .fp-volumeslider {\n\twidth: 75px;\n\theight: 6px;\n\tcursor: col-resize;\n\tfloat: left;\n}\n.is-rtl.flowplayer .fp-volumeslider {\n\tfloat: right;\n}\n.flowplayer .fp-volumelevel {\n\theight: 100%;\n}\n.flowplayer .fp-remaining {\n\tdisplay: none;\n}\n.flowplayer.is-splash,\n.flowplayer.is-poster {\n\tcursor: pointer;\n}\n.flowplayer.is-poster .fp-engine {\n\ttop: -9999em;\n}\n/*.flowplayer.is-loading .fp-waiting{display:block}*/\n.flowplayer.is-loading .fp-ui {\n\tbackground-position: -9999em;\n}\n.flowplayer.is-loading video.fp-engine {\n\tposition: absolute;\n\ttop: -9999em;\n}\n.flowplayer.is-playing {\n\tbackground-image: none !important;\n\tbackground-color: #333;\n}\n.flowplayer.is-playing.hls-fix.is-finished .fp-engine {\n\tposition: absolute;\n\ttop: -9999em;\n}\n.flowplayer.is-fullscreen {\n\ttop: 0 !important;\n\tleft: 0 !important;\n\tborder: 0 !important;\n\tmargin: 0 !important;\n\twidth: 100% !important;\n\theight: 100% !important;\n\tmax-width: 100% !important;\n\tz-index: 99999 !important;\n\t-webkit-box-shadow: 0 !important;\n\t-moz-box-shadow: 0 !important;\n\tbox-shadow: 0 !important;\n\tbackground-image: none !important;\n\tbackground-color: #333;\n}\n.is-rtl.flowplayer.is-fullscreen {\n\tleft: auto !important;\n\tright: 0 !important;\n}\n.flowplayer.is-fullscreen .fp-player {\n\tbackground-color: #333;\n}\n.flowplayer.is-error {\n\tborder: 1px solid #909090;\n\tbackground: #fdfdfd !important;\n}\n.flowplayer.is-error h2 {\n\tfont-weight: bold;\n\tfont-size: large;\n\tmargin-top: 10%;\n}\n.flowplayer.is-error .fp-message {\n\tdisplay: block;\n}\n.flowplayer.is-ready.is-muted .fp-mute {\n\topacity: .7;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);\n}\n.flowplayer.is-ready.is-muted .fp-mute:before {\n\tcontent: "\\e605";\n}\n.flowplayer.is-mouseout .fp-title {\n\toverflow: hidden;\n}\n.flowplayer.is-disabled .fp-progress {\n\tbackground-color: #999;\n}\n.flowplayer.is-flash-disabled {\n\tbackground-color: #333;\n}\n.flowplayer.is-flash-disabled object.fp-engine {\n\tz-index: 1;\n}\n.flowplayer.is-flash-disabled .fp-flash-disabled {\n\tdisplay: none;\n\tz-index: 2;\n}\n.flowplayer .fp-embed {\n\tposition: absolute;\n\ttop: 10px;\n\tleft: auto;\n\tright: 50px;\n\tdisplay: block;\n\twidth: 30px;\n\theight: 30px;\n\ttext-align: center;\n}\n.is-rtl.flowplayer .fp-embed {\n\tright: auto;\n\tleft: 50px;\n}\n.flowplayer .fp-embed-code {\n\tposition: absolute;\n\tdisplay: none;\n\ttop: 10px;\n\tright: 67px;\n\tbackground-color: #333;\n\tpadding: 3px 5px;\n\t-webkit-border-radius: 3px;\n\t-moz-border-radius: 3px;\n\tborder-radius: 3px;\n\t-webkit-box-shadow: 0 0 3px #ccc;\n\t-moz-box-shadow: 0 0 3px #ccc;\n\tbox-shadow: 0 0 3px #ccc;\n\tfont-size: 12px;\n}\n.is-closeable.flowplayer .fp-embed-code {\n\tright: 99px;\n}\n.flowplayer .fp-embed-code:before {\n\tcontent: \'\';\n\twidth: 0;\n\theight: 0;\n\tposition: absolute;\n\ttop: 2px;\n\tright: -10px;\n\tborder: 5px solid transparent;\n\tborder-left-color: #333;\n}\n.is-rtl.flowplayer .fp-embed-code {\n\tright: auto;\n\tleft: 67px;\n}\n.is-rtl.flowplayer .fp-embed-code:before {\n\tright: auto;\n\tleft: -10px;\n\tborder-left-color: transparent;\n\tborder-right-color: #333;\n}\n.flowplayer .fp-embed-code textarea {\n\twidth: 400px;\n\theight: 16px;\n\tfont-family: monaco, "courier new", verdana;\n\tcolor: #777;\n\twhite-space: nowrap;\n\tresize: none;\n\toverflow: hidden;\n\tborder: 0;\n\toutline: 0;\n\tbackground-color: transparent;\n\tcolor: #ccc;\n}\n.flowplayer .fp-embed-code label {\n\tdisplay: block;\n\tcolor: #999;\n}\n.flowplayer.is-embedding .fp-embed,\n.flowplayer.is-embedding .fp-embed-code {\n\tdisplay: block;\n\topacity: 1;\n\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n}\n@-moz-keyframes pulse {\n\t0% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n@-webkit-keyframes pulse {\n\t0% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n@-o-keyframes pulse {\n\t0% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n@-ms-keyframes pulse {\n\t0% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n@keyframes pulse {\n\t0% {\n\t\topacity: 0;\n\t}\n\t100% {\n\t\topacity: 1;\n\t}\n}\n.flowplayer .fp-title {\n\tposition: absolute;\n\ttop: 10px;\n\tleft: 10px;\n}\n.is-rtl.flowplayer .fp-title {\n\tleft: auto;\n\tright: 10px;\n}\n.flowplayer .fp-title,\n.flowplayer .fp-unload,\n.flowplayer .fp-fullscreen,\n.flowplayer .fp-embed {\n\t-webkit-border-radius: 3px;\n\t-moz-border-radius: 3px;\n\tborder-radius: 3px;\n}\n.flowplayer .fp-embed-code {\n\tright: 85px;\n}\n.is-closeable.flowplayer .fp-embed-code {\n\tright: 125px;\n}\n.is-rtl.flowplayer .fp-embed-code {\n\tright: auto;\n\tleft: 85px;\n}\n.flowplayer.is-mouseout .fp-menu {\n\tdisplay: none;\n}\n@-moz-keyframes functional-controls-hide {\n\t0% {\n\t\topacity: 0;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t}\n\t100% {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tleft: 0;\n\t\topacity: 1;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n\t}\n}\n@-webkit-keyframes functional-controls-hide {\n\t0% {\n\t\topacity: 0;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t}\n\t100% {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tleft: 0;\n\t\topacity: 1;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n\t}\n}\n@-o-keyframes functional-controls-hide {\n\t0% {\n\t\topacity: 0;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t}\n\t100% {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tleft: 0;\n\t\topacity: 1;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n\t}\n}\n@-ms-keyframes functional-controls-hide {\n\t0% {\n\t\topacity: 0;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t}\n\t100% {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tleft: 0;\n\t\topacity: 1;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n\t}\n}\n@keyframes functional-controls-hide {\n\t0% {\n\t\topacity: 0;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);\n\t}\n\t100% {\n\t\tbottom: 0;\n\t\tright: 0;\n\t\tleft: 0;\n\t\topacity: 1;\n\t\tfilter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);\n\t}\n}\n.rotate_90 {\n\ttransform: rotate(90deg);\n  transform-origin: center;\n  object-fit: contain;\n}\n.flowplayer .saas-revolve {\n\tmax-height: 200px;\n\tposition: absolute;\n\tmargin: auto;\n\tfont-size: 14px;\n\tline-height: 30px;\n\tcursor: default;\n\tz-index: 4;\n\tcolor: #ffffff;\n\ttop: auto;\n\tleft: 0;\n\tbottom: 50px;\n\tright: 0;\n}\n.flowplayer .saas-revolve li {\n\ttext-align: center;\n\tdisplay: none;\n\tword-break: break-all;\n\tword-wrap: break-word;\n}\n.flowplayer .saas-revolve ul > :first-child {\n\tdisplay: block;\n}\n.flowplayer .curtain {\n\tposition: absolute;\n\tdisplay: block;\n\tmargin: 0 auto;\n\ttext-align: center;\n\tbackground-color: #000000;\n\tz-index: 1;\n\ttop: 0px;\n\twidth: 100%;\n\theight: 100%;\n}\n.flowplayer .curtain .waiting {\n\tpadding-top: 15%;\n}\n.flowplayer .curtain .notice {\n\tfont-size: 26px;\n\tcolor: #ffffff;\n}\n\n.flowplayer .fp-controls,\n.flowplayer .fp-time {\n\tdisplay: none;\n}\n\n.flowplayer .fp-fullscreen {\n\tdisplay: none !important;\n}\n.flowplayer .fp-ui{\n\tdisplay: none !important;\n}\n',""])},function(e,t,n){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n=function(e,t){var n=e[1]||"",r=e[3];return r?t&&"function"==typeof btoa?(e=function(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}(r),t=r.sources.map(function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"}),[n].concat(t).concat([e]).join("\n")):[n].join("\n"):n}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];null!=i&&(r[i]=!0)}for(o=0;o<e.length;o++){var a=e[o];null!=a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){"use strict";e.exports=function(e,t){return"string"==typeof e&&(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),/["'() \t\n]/.test(e)||t)?'"'+e.replace(/"/g,'\\"').replace(/\n/g,"\\n")+'"':e}},function(e,t){e.exports="data:application/vnd.ms-fontobject;base64,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"},function(e,t){e.exports="data:application/vnd.ms-fontobject;base64,OAwAAJQLAAABAAIAAAAAAAAAAAAAAAAAAAABAJABAAAAAExQAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAspSWMgAAAAAAAAAAAAAAAAAAAAAAAA4AZgBwAGkAYwBvAG4AcwAAAA4AUgBlAGcAdQBsAGEAcgAAABYAVgBlAHIAcwBpAG8AbgAgADEALgAwAAAADgBmAHAAaQBjAG8AbgBzAAAAAAAAAQAAAAsAgAADADBPUy8yDxIDNgAAALwAAABgY21hcBpVzGMAAAEcAAAATGdhc3AAAAAQAAABaAAAAAhnbHlmuUHFCQAAAXAAAAfYaGVhZAZxTPYAAAlIAAAANmhoZWEIngTSAAAJgAAAACRobXR4OGYFhwAACaQAAABEbG9jYQ9sEXYAAAnoAAAAJG1heHAAGABoAAAKDAAAACBuYW1lTCmp3AAACiwAAAFFcG9zdAADAAAAAAt0AAAAIAADBAABkAAFAAACmQLMAAAAjwKZAswAAAHrADMBCQAAAAAAAAAAAAAAAAAAAAEQAAAAAAAAAAAAAAAAAAAAAEAAAOYMA8D/wABAA8AAQAAAAAEAAAAAAAAAAAAAACAAAAAAAAIAAAADAAAAFAADAAEAAAAUAAQAOAAAAAoACAACAAIAAQAg5gz//f//AAAAAAAg5gD//f//AAH/4xoEAAMAAQAAAAAAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAQAAAAAAAAAAAAIAADc5AQAAAAACAIUAOAN7Ay4AEAAhAAAlBwYiJwEmND8BNjIXAR4BBwMXFhQHAQYiLwEmNDcBNjIXA3s2CBcH/WYICDYIFwcClQsCCDg1CAj9bAgXCDUICAKUCBcIcTYICAKUCBcINgcH/WsIFwcCvTYHFwj9ZgcHNggXBwKaCAgAAAAABgAX/8oD6QOcABAAIQAyAEMAVABlAAAFETQ2OwEyFhURFAYrAQYmNQEhMhYdARQGIyEiJj0BJjYzEwE2Mh8BFhQHAQYiLwEuATcBERQGKwEiJjURNDY7ATYWFQEhIiY9ATQ2MyEyFh0BFgYjAwEGIi8BJjQ3ATYyHwEeAQcBig4JOwoNDQo7CQ7+pAGuCg0NCv5SCg0CDgsNATEHFAYpBwf+0ggUBikHAQYCOw4JOwoNDQo7CQ4BXP5SCg0NCgGuCg0CDgsN/s8HFAYpBwcBLggUBikHAQYfAa4KDQ0K/lIJDgIOCwHFDQo7CQ4OCTsKDf6VATEHBykIFAb+zQgIKQgUCANK/lIKDQ0KAa4KDQINDP47DQo7Cg0NCjsKDQFs/tEHBykIFAYBMAgIKQcVBwAAAAAGAD3/7gPDA3gAEAAhADIAQwBUAGUAABMRFAYrASImNRE0NjsBMhYVASEiJj0BNDYzITIWHQEUBiMDAQYiLwEmNDcBNjIfARYUByURNDY7ATIWFREUBisBIiY1ASEyFh0BFAYjIQYmPQE0NjMTATYyHwEWFAcBBiIvASY0N6YNCjsJDg4JOwgPAV3+UQkODgkBrwkODgkQ/tAIFAYpBwcBMQgTBikICAFnDQo7CQ4OCTsID/6jAa8JDg4J/lEJDg4JEAEwCBQGKQcH/s8IEwYpCAgBtv5SCg0NCgGuCQ4OCf44Dgk7Cg0NCjsJDgFu/tAICCgIFAYBMAgIKQcUBlUBrgkODgn+UgoNDQoBxw0KOwkOAhAJOQsO/pIBMQgIKQgUBf7PCAgpCBMGAAACACQARQTcAxcAGQAzAAAlIiYnAS4BNTQ2NwE2FhcWBgcJAR4BBw4BIyEiJicmNjcJAS4BNz4BFwEeARUUBgcBDgEjAf0JEwf+YQwLDQoBoRMyEA8JE/6fAV4TCQ8IGRABBg4ZCg8JEwFe/qITCQ8PMBUBnwwLDQr+XwgSB0UFCAExBxoPEBcKATAQCRMUMRD/AP7+EDAVCQ4MCxMyEAECAQMPMBUTCQ/+zwcaDxAXCv7NBgcAAAAAAwBAAE8DwAMaABAAIQAyAAABFRQGIyEiJj0BNDYzIR4BFREVFAYjISImPQE0NjMhMhYVERUUBiMhIiY9ATQ2MyEyFhUDwAsJ/KgHDQsJA1gHDQsJ/KgHDQsJA1gHDQsJ/KgHDQsJA1gHDQMFQggNCwpCCA0CCwj+z0IIDQsKQggNDQj+0kIIDQsKQggNDQgABAAx/+4EygN4ABAAHQAuAD8AAAEhIiY9ATQ2MyEyFh0BFAYjEwEmNDcBNhYVERQGJwEHBiInASY0PwE2MhcBFgYHAxcWFAcBBiIvASY0NwE2MhcBlP7aGSQkGQEmGSUjG9L+eQYGAYcKHRsMAmIuBhQI/nYGBi4GFAcBjQYBBywuBgb+cwUUCC4GBgGNCBEIAQMkGeYZJSUZ5hsi/usBtgYQBgG4DAwP/JIOCgsBBS4GBgGNBhQHLgYG/nMHEggBri4GEwj+cwYGLgYUBwGNBgYAAAACALj/7gMUA3gAEAAdAAABISImPQE0NjMhMhYdARQGIxMBJjQ3ATYWFREUBicCHP7aGSUlGQEmGSUjG9L+dgYGAYoKHBoMAQMkGeYZJSUZ5hsi/usBtgYQBgG4DAwP/JIOCgsAAgDSABIDLgNXABAAIQAAJSMiJjURNDY7ATIWFREUBiMhIyImNRE0NjsBMhYVEQ4BIwMNbg4UFA5uDRQUDf5Ubg0UFA1uDhQCFAwSFA0DAw0UFA38/Q8SFA0DAw0UFA38/Q8SAAEAeAAFA2EDYQAMAAATNDYXARYUBwEGJjUReDkpAocpKf15KTkDKS4hF/6LGEIX/ooXIS4C7AAAAQAR/8UD7wOiABQAAAUiLgI1ND4CMzIeAhUUDgIjAgBmtYZOToe0Zma0h05OhrVmO02HtGZntIZOToa0Z2a0h00AAAACAD3/8APDA3cAFAApAAAFIi4CNTQ+AjMyHgIVFA4CIxEiDgIVFB4CMzI+AjU0LgIjAgBdpXpHR3qlXV2lekdHeqVdVpZxQUFxl1VVl3FBQXGWVhBHe6RdXaV6SEh6pV1dpHtHA2FBcJdWVZdwQUFwl1VWl3BBAAABAZMBFwKiAlAADAAAATQ2HwEWFA8BBiY9AQGTJBnSGhrSGSQCLR0VD3kPKg55DxUd8wAABAA2AAgEMwNfABAAIQAyAEMAAAEhIiY1MTQ2MyEyFhUxDgEjByEiJjUxNDYzITIWFTEUBiMRISImNTE0NjMhMhYVMRQGIxchIiY1MTQ2MyEyFhUxDgEjA/38bBUeHhUDlxUeAh4Wvf3mFR4eFQIaFR4eFf3mFR4eFQIaFR4eFb38bBUeHhUDlxUeAh4WAQMeFRUeHhUVHvseFRUeHhUVHgLwHhYVHh4VFh77HxUVHh4VFR8AAQAAAAEAADKWlLJfDzz1AAsEAAAAAADROwRJAAAAANE7BEkAAP/FBNwDogAAAAgAAgAAAAAAAAABAAADwP/AAAAFAAAAAAAE3AABAAAAAAAAAAAAAAAAAAAAEQAAAAAAAAAAAAAAAAIAAAAEAACFBAAAFwQAAD0FAAAkBAAAQAUAADEEAAC4BAAA0gQAAHgEAAARBAAAPQQAAZMEZgA2AAAAAAAKABQAHgBcAPYBjAHoAjACmgLMAv4DGgM8A3gDkgPsAAEAAAARAGYABgAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAOAK4AAQAAAAAAAQAOAAAAAQAAAAAAAgAOAEcAAQAAAAAAAwAOACQAAQAAAAAABAAOAFUAAQAAAAAABQAWAA4AAQAAAAAABgAHADIAAQAAAAAACgA0AGMAAwABBAkAAQAOAAAAAwABBAkAAgAOAEcAAwABBAkAAwAOACQAAwABBAkABAAOAFUAAwABBAkABQAWAA4AAwABBAkABgAOADkAAwABBAkACgA0AGMAZgBwAGkAYwBvAG4AcwBWAGUAcgBzAGkAbwBuACAAMQAuADAAZgBwAGkAYwBvAG4Ac2ZwaWNvbnMAZgBwAGkAYwBvAG4AcwBSAGUAZwB1AGwAYQByAGYAcABpAGMAbwBuAHMARgBvAG4AdAAgAGcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAASQBjAG8ATQBvAG8AbgAuAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="},function(e,t){e.exports="data:font/woff;base64,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"},function(e,t){e.exports="data:font/ttf;base64,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"},function(e,t){e.exports="data:image/svg+xml;base64,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"},function(e,t,n){var r,o,i,a={},s=(r=function(){return window&&document&&document.all&&!window.atob},function(){return o=void 0===o?r.apply(this,arguments):o}),c=(i={},function(e,t){if("function"==typeof e)return e();if(void 0===i[e]){if(t=function(e,t){return(t||document).querySelector(e)}.call(this,e,t),window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}i[e]=t}return i[e]}),l=null,u=0,p=[],d=n(43);function f(e,t){for(var n=0;n<e.length;n++){var r=e[n],o=a[r.id];if(o){o.refs++;for(var i=0;i<o.parts.length;i++)o.parts[i](r.parts[i]);for(;i<r.parts.length;i++)o.parts.push(v(r.parts[i],t))}else{var s=[];for(i=0;i<r.parts.length;i++)s.push(v(r.parts[i],t));a[r.id]={id:r.id,refs:1,parts:s}}}}function h(e,t){for(var n=[],r={},o=0;o<e.length;o++){var i=e[o],a=t.base?i[0]+t.base:i[0];i={css:i[1],media:i[2],sourceMap:i[3]};r[a]?r[a].parts.push(i):n.push(r[a]={id:a,parts:[i]})}return n}function A(e,t){var n=c(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=p[p.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),p.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");r=c(e.insertAt.before,n),n.insertBefore(t,r)}}function g(e){null!==e.parentNode&&(e.parentNode.removeChild(e),0<=(e=p.indexOf(e)))&&p.splice(e,1)}function m(e){var t,r=document.createElement("style");return void 0===e.attrs.type&&(e.attrs.type="text/css"),void 0===e.attrs.nonce&&(t=n.nc)&&(e.attrs.nonce=t),y(r,e.attrs),A(e,r),r}function y(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function v(e,t){var n,r,o,i,a;if(t.transform&&e.css){if(!(i="function"==typeof t.transform?t.transform(e.css):t.transform.default(e.css)))return function(){};e.css=i}return o=t.singleton?(i=u++,n=l=l||m(t),r=C.bind(null,n,i,!1),C.bind(null,n,i,!0)):e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(i=t,a=document.createElement("link"),void 0===i.attrs.type&&(i.attrs.type="text/css"),i.attrs.rel="stylesheet",y(a,i.attrs),A(i,a),r=function(e,t,n){var r=n.css,o=(n=n.sourceMap,void 0===t.convertToAbsoluteUrls&&n);(t.convertToAbsoluteUrls||o)&&(r=d(r)),n&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */"),t=new Blob([r],{type:"text/css"}),o=e.href,e.href=URL.createObjectURL(t),o&&URL.revokeObjectURL(o)}.bind(null,n=a,t),function(){g(n),n.href&&URL.revokeObjectURL(n.href)}):(n=m(t),r=function(e,t){var n=t.css;if((t=t.media)&&e.setAttribute("media",t),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}.bind(null,n),function(){g(n)}),r(e),function(t){t?t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap||r(e=t):o()}}e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=s()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=h(e,t);return f(n,t),function(e){for(var r=[],o=0;o<n.length;o++){var i=n[o];(s=a[i.id]).refs--,r.push(s)}e&&f(h(e,t),t);var s;for(o=0;o<r.length;o++)if(0===(s=r[o]).refs){for(var c=0;c<s.parts.length;c++)s.parts[c]();delete a[s.id]}}},w=[];var w,b=function(e,t){return w[e]=t,w.filter(Boolean).join("\n")};function C(e,t,n,r){n=n?"":r.css;e.styleSheet?e.styleSheet.cssText=b(t,n):(r=document.createTextNode(n),(n=e.childNodes)[t]&&e.removeChild(n[t]),n.length?e.insertBefore(r,n[t]):e.appendChild(r))}},function(e,t){e.exports=function(e){var t,n,r="undefined"!=typeof window&&window.location;if(r)return e&&"string"==typeof e?(t=r.protocol+"//"+r.host,n=t+r.pathname.replace(/\/[^\/]*$/,"/"),e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,r){return r=r.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t}),/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(r)?e:(e=0===r.indexOf("//")?r:0===r.indexOf("/")?t+r:n+r.replace(/^\.\//,""),"url("+JSON.stringify(e)+")")})):e;throw new Error("fixUrls requires window.location")}},function(e,t,n){var r=n(45),o=n(2)("socket.io-client:url");e.exports=function(e,t){var n=e;return t=t||"undefined"!=typeof location&&location,null==e&&(e=t.protocol+"//"+t.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?t.protocol+e:t.host+e),/^(https?|wss?):\/\//.test(e)||(o("protocol-less url %s",e),e=void 0!==t?t.protocol+"//"+e:"https://"+e),o("parse %s",e),n=r(e)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/",e=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host,n.id=n.protocol+"://"+e+":"+n.port,n.href=n.protocol+"://"+e+(t&&t.port===n.port?"":":"+n.port),n}},function(e,t){var n=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,r=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];e.exports=function(e){for(var t=e,o=e.indexOf("["),i=e.indexOf("]"),a=(-1!=o&&-1!=i&&(e=e.substring(0,o)+e.substring(o,i).replace(/:/g,";")+e.substring(i,e.length)),n.exec(e||"")),s={},c=14;c--;)s[r[c]]=a[c]||"";return-1!=o&&-1!=i&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s}},function(e,t,n){e.exports=function(e){function t(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return r.colors[Math.abs(t)%r.colors.length]}function r(e){let n;function a(...e){if(a.enabled){const i=a;var t=Number(new Date),o=t-(n||t);i.diff=o,i.prev=n,i.curr=t,n=t,e[0]=r.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{var o;return"%%"!==t&&(s++,"function"==typeof(n=r.formatters[n]))&&(o=e[s],t=n.call(i,o),e.splice(s,1),s--),t}),r.formatArgs.call(i,e),(i.log||r.log).apply(i,e)}}return a.namespace=e,a.enabled=r.enabled(e),a.useColors=r.useColors(),a.color=t(e),a.destroy=o,a.extend=i,"function"==typeof r.init&&r.init(a),r.instances.push(a),a}function o(){var e=r.instances.indexOf(this);return-1!==e&&(r.instances.splice(e,1),!0)}function i(e,t){return(t=r(this.namespace+(void 0===t?":":t)+e)).log=this.log,t}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return((r.debug=r).default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},r.disable=function(){var e=[...r.names.map(a),...r.skips.map(a).map(e=>"-"+e)].join(",");return r.enable(""),e},r.enable=function(e){let t;r.save(e),r.names=[],r.skips=[];var n=("string"==typeof e?e:"").split(/[\s,]+/),o=n.length;for(t=0;t<o;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")));for(t=0;t<r.instances.length;t++){var i=r.instances[t];i.enabled=r.enabled(i.namespace)}},r.enabled=function(e){if("*"===e[e.length-1])return!0;let t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1},r.humanize=n(47),Object.keys(e).forEach(t=>{r[t]=e[t]}),r.instances=[],r.names=[],r.skips=[],r.formatters={},r.selectColor=t,r.enable(r.load()),r}},function(e,t){var n=864e5;function r(e,t,n,r){return t=1.5*n<=t,Math.round(e/n)+" "+r+(t?"s":"")}e.exports=function(e,t){t=t||{};var o=typeof e;if(!("string"==o&&0<e.length)){if("number"==o&&isFinite(e))return(t.long?function(e){var t=Math.abs(e);return n<=t?r(e,t,n,"day"):36e5<=t?r(e,t,36e5,"hour"):6e4<=t?r(e,t,6e4,"minute"):1e3<=t?r(e,t,1e3,"second"):e+" ms"}:function(e){var t=Math.abs(e);return n<=t?Math.round(e/n)+"d":36e5<=t?Math.round(e/36e5)+"h":6e4<=t?Math.round(e/6e4)+"m":1e3<=t?Math.round(e/1e3)+"s":e+"ms"})(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}if(o=e,!(100<(o=String(o)).length)&&(o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(o))){var i=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return 6048e5*i;case"days":case"day":case"d":return i*n;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*i;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*i;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*i;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}},function(e,t,n){(function(r){function o(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r?r.env.DEBUG:e}(t=e.exports=n(49)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n,r,o=this.useColors;e[0]=(o?"%c":"")+this.namespace+(o?" %c":" ")+e[0]+(o?"%c ":" ")+"+"+t.humanize(this.diff),o&&(o="color: "+this.color,e.splice(1,0,o,"color: inherit"),e[r=n=0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&(n++,"%c"===e)&&(r=n)}),e.splice(r,0,o))},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=o,t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(o())}).call(this,n(5))},function(e,t,n){function r(e){var n;function r(){if(r.enabled){for(var e=r,o=+new Date,i=o-(n||o),a=(e.diff=i,e.prev=n,e.curr=o,n=o,new Array(arguments.length)),s=0;s<a.length;s++)a[s]=arguments[s];a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");var c=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,function(n,r){var o;return"%%"!==n&&(c++,"function"==typeof(r=t.formatters[r]))&&(o=a[c],n=r.call(e,o),a.splice(c,1),c--),n}),t.formatArgs.call(e,a),(r.log||t.log||void 0).apply(e,a)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),r.destroy=o,"function"==typeof t.init&&t.init(r),t.instances.push(r),r}function o(){var e=t.instances.indexOf(this);return-1!==e&&(t.instances.splice(e,1),!0)}(t=e.exports=r.debug=r.default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];var n,r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")));for(n=0;n<t.instances.length;n++){var i=t.instances[n];i.enabled=t.enabled(i.namespace)}},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(50),t.instances=[],t.names=[],t.skips=[],t.formatters={}},function(e,t){function n(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var r=typeof e;if(!("string"==r&&0<e.length)){if("number"==r&&!1===isNaN(e))return t.long?n(r=e,864e5,"day")||n(r,36e5,"hour")||n(r,6e4,"minute")||n(r,1e3,"second")||r+" ms":864e5<=(t=e)?Math.round(t/864e5)+"d":36e5<=t?Math.round(t/36e5)+"h":6e4<=t?Math.round(t/6e4)+"m":1e3<=t?Math.round(t/1e3)+"s":t+"ms";throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}if(r=e,!(100<(r=String(r)).length)&&(r=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(r))){var o=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*o;case"days":case"day":case"d":return 864e5*o;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*o;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*o;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*o;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return o;default:return}}}},function(e,t,n){function r(e){if(e){var t,n=e;for(t in r.prototype)n[t]=r.prototype[t];return n}}(e.exports=r).prototype.on=r.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},r.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)this._callbacks={};else{var n=this._callbacks["$"+e];if(n)if(1==arguments.length)delete this._callbacks["$"+e];else{for(var r,o=0;o<n.length;o++)if((r=n[o])===t||r.fn===t){n.splice(o,1);break}0===n.length&&delete this._callbacks["$"+e]}}return this},r.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t)}return this},r.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},r.prototype.hasListeners=function(e){return!!this.listeners(e).length}},function(e,t,n){var r=n(7),o=n(15),i=(n=Object.prototype.toString,"function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===n.call(Blob)),a="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===n.call(File);t.deconstructPacket=function(e){var t=[],n=e.data;return e.data=function e(t,n){if(!t)return t;var i;if(o(t))return i={_placeholder:!0,num:n.length},n.push(t),i;if(r(t)){for(var a=new Array(t.length),s=0;s<t.length;s++)a[s]=e(t[s],n);return a}if("object"==typeof t&&!(t instanceof Date)){var c;a={};for(c in t)a[c]=e(t[c],n);return a}return t}(n,t),e.attachments=t.length,{packet:e,buffers:t}},t.reconstructPacket=function(e,t){return e.data=function e(t,n){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&0<=t.num&&t.num<n.length)return n[t.num];throw new Error("illegal attachments")}if(r(t))for(var o=0;o<t.length;o++)t[o]=e(t[o],n);else if("object"==typeof t)for(var i in t)t[i]=e(t[i],n);return t}(e.data,t),e.attachments=void 0,e},t.removeBlobs=function(e,t){var n=0,s=e;!function e(c,l,u){if(c)if(i&&c instanceof Blob||a&&c instanceof File){n++;var p=new FileReader;p.onload=function(){u?u[l]=this.result:s=this.result,--n||t(s)},p.readAsArrayBuffer(c)}else if(r(c))for(var d=0;d<c.length;d++)e(c[d],d,c);else if("object"==typeof c&&!o(c))for(var f in c)e(c[f],f,c)}(s),n||t(s)}},function(e,t,n){"use strict";t.byteLength=function(e){return 3*((e=l(e))[0]+(e=e[1]))/4-e},t.toByteArray=function(e){for(var t,n=(r=l(e))[0],r=r[1],a=new i(3*(n+r)/4-r),s=0,c=0<r?n-4:n,u=0;u<c;u+=4)t=o[e.charCodeAt(u)]<<18|o[e.charCodeAt(u+1)]<<12|o[e.charCodeAt(u+2)]<<6|o[e.charCodeAt(u+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;return 2===r&&(t=o[e.charCodeAt(u)]<<2|o[e.charCodeAt(u+1)]>>4,a[s++]=255&t),1===r&&(t=o[e.charCodeAt(u)]<<10|o[e.charCodeAt(u+1)]<<4|o[e.charCodeAt(u+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t),a},t.fromByteArray=function(e){for(var t,n=e.length,o=n%3,i=[],a=0,s=n-o;a<s;a+=16383)i.push(function(e,t,n){for(var o,i=[],a=t;a<n;a+=3)o=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(function(e){return r[e>>18&63]+r[e>>12&63]+r[e>>6&63]+r[63&e]}(o));return i.join("")}(e,a,s<a+16383?s:a+16383));return 1==o?(t=e[n-1],i.push(r[t>>2]+r[t<<4&63]+"==")):2==o&&(t=(e[n-2]<<8)+e[n-1],i.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+"=")),i.join("")};for(var r=[],o=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,c=a.length;s<c;++s)r[s]=a[s],o[a.charCodeAt(s)]=s;function l(e){var t=e.length;if(0<t%4)throw new Error("Invalid string. Length must be a multiple of 4");return t=(e=-1===(e=e.indexOf("="))?t:e)===t?0:4-e%4,[e,t]}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,r,o){var i,a,s=8*o-r-1,c=(1<<s)-1,l=c>>1,u=-7,p=n?o-1:0,d=n?-1:1;o=e[t+p];for(p+=d,i=o&(1<<-u)-1,o>>=-u,u+=s;0<u;i=256*i+e[t+p],p+=d,u-=8);for(a=i&(1<<-u)-1,i>>=-u,u+=r;0<u;a=256*a+e[t+p],p+=d,u-=8);if(0===i)i=1-l;else{if(i===c)return a?NaN:1/0*(o?-1:1);a+=Math.pow(2,r),i-=l}return(o?-1:1)*a*Math.pow(2,i-r)},t.write=function(e,t,n,r,o,i){var a,s,c=8*i-o-1,l=(1<<c)-1,u=l>>1,p=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:i-1,f=r?1:-1;i=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=l):(a=Math.floor(Math.log(t)/Math.LN2),t*(r=Math.pow(2,-a))<1&&(a--,r*=2),2<=(t+=1<=a+u?p/r:p*Math.pow(2,1-u))*r&&(a++,r/=2),l<=a+u?(s=0,a=l):1<=a+u?(s=(t*r-1)*Math.pow(2,o),a+=u):(s=t*Math.pow(2,u-1)*Math.pow(2,o),a=0));8<=o;e[n+d]=255&s,d+=f,s/=256,o-=8);for(a=a<<o|s,c+=o;0<c;e[n+d]=255&a,d+=f,a/=256,c-=8);e[n+d-f]|=128*i}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){e.exports=n(57),e.exports.parser=n(0)},function(e,t,n){var r=n(17),o=n(12),i=n(4)("engine.io-client:socket"),a=n(21),s=n(0),c=n(71),l=n(13);function u(e,t){if(!(this instanceof u))return new u(e,t);t=t||{},e&&"object"==typeof e&&(t=e,e=null),e?(e=c(e),t.hostname=e.host,t.secure="https"===e.protocol||"wss"===e.protocol,t.port=e.port,e.query&&(t.query=e.query)):t.host&&(t.hostname=c(t.host).host),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.agent=t.agent||!1,this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?443:80),this.query=t.query||{},"string"==typeof this.query&&(this.query=l.decode(this.query)),this.upgrade=!1!==t.upgrade,this.path=(t.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!t.forceJSONP,this.jsonp=!1!==t.jsonp,this.forceBase64=!!t.forceBase64,this.enablesXDR=!!t.enablesXDR,this.withCredentials=!1!==t.withCredentials,this.timestampParam=t.timestampParam||"t",this.timestampRequests=t.timestampRequests,this.transports=t.transports||["polling","websocket"],this.transportOptions=t.transportOptions||{},this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=t.policyPort||843,this.rememberUpgrade=t.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=t.onlyBinaryUpgrades,this.perMessageDeflate=!1!==t.perMessageDeflate&&(t.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=t.pfx||null,this.key=t.key||null,this.passphrase=t.passphrase||null,this.cert=t.cert||null,this.ca=t.ca||null,this.ciphers=t.ciphers||null,this.rejectUnauthorized=void 0===t.rejectUnauthorized||t.rejectUnauthorized,this.forceNode=!!t.forceNode,this.isReactNative="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),("undefined"==typeof self||this.isReactNative)&&(t.extraHeaders&&0<Object.keys(t.extraHeaders).length&&(this.extraHeaders=t.extraHeaders),t.localAddress)&&(this.localAddress=t.localAddress),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,this.open()}(e.exports=u).priorWebsocketSuccess=!1,o(u.prototype),u.protocol=s.protocol,(u.Socket=u).Transport=n(11),u.transports=n(17),u.parser=n(0),u.prototype.createTransport=function(e){i('creating transport "%s"',e);var t=function(e){var t,n={};for(t in e)e.hasOwnProperty(t)&&(n[t]=e[t]);return n}(this.query),n=(t.EIO=s.protocol,t.transport=e,this.transportOptions[e]||{});return this.id&&(t.sid=this.id),new r[e]({query:t,socket:this,agent:n.agent||this.agent,hostname:n.hostname||this.hostname,port:n.port||this.port,secure:n.secure||this.secure,path:n.path||this.path,forceJSONP:n.forceJSONP||this.forceJSONP,jsonp:n.jsonp||this.jsonp,forceBase64:n.forceBase64||this.forceBase64,enablesXDR:n.enablesXDR||this.enablesXDR,withCredentials:n.withCredentials||this.withCredentials,timestampRequests:n.timestampRequests||this.timestampRequests,timestampParam:n.timestampParam||this.timestampParam,policyPort:n.policyPort||this.policyPort,pfx:n.pfx||this.pfx,key:n.key||this.key,passphrase:n.passphrase||this.passphrase,cert:n.cert||this.cert,ca:n.ca||this.ca,ciphers:n.ciphers||this.ciphers,rejectUnauthorized:n.rejectUnauthorized||this.rejectUnauthorized,perMessageDeflate:n.perMessageDeflate||this.perMessageDeflate,extraHeaders:n.extraHeaders||this.extraHeaders,forceNode:n.forceNode||this.forceNode,localAddress:n.localAddress||this.localAddress,requestTimeout:n.requestTimeout||this.requestTimeout,protocols:n.protocols||void 0,isReactNative:this.isReactNative})},u.prototype.open=function(){var e,t;if(this.rememberUpgrade&&u.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))e="websocket";else{if(0===this.transports.length)return t=this,void setTimeout(function(){t.emit("error","No transports available")},0);e=this.transports[0]}this.readyState="opening";try{e=this.createTransport(e)}catch(e){return this.transports.shift(),void this.open()}e.open(),this.setTransport(e)},u.prototype.setTransport=function(e){i("setting transport %s",e.name);var t=this;this.transport&&(i("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),(this.transport=e).on("drain",function(){t.onDrain()}).on("packet",function(e){t.onPacket(e)}).on("error",function(e){t.onError(e)}).on("close",function(){t.onClose("transport close")})},u.prototype.probe=function(e){i('probing transport "%s"',e);var t=this.createTransport(e,{probe:1}),n=!1,r=this;function o(){var o;r.onlyBinaryUpgrades&&(o=!this.supportsBinary&&r.transport.supportsBinary,n=n||o),n||(i('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",function(o){n||("pong"===o.type&&"probe"===o.data?(i('probe transport "%s" pong',e),r.upgrading=!0,r.emit("upgrading",t),t&&(u.priorWebsocketSuccess="websocket"===t.name,i('pausing current transport "%s"',r.transport.name),r.transport.pause(function(){n||"closed"!==r.readyState&&(i("changing transport and sending upgrade packet"),d(),r.setTransport(t),t.send([{type:"upgrade"}]),r.emit("upgrade",t),t=null,r.upgrading=!1,r.flush())}))):(i('probe transport "%s" failed',e),(o=new Error("probe error")).transport=t.name,r.emit("upgradeError",o)))}))}function a(){n||(n=!0,d(),t.close(),t=null)}function s(n){var o=new Error("probe error: "+n);o.transport=t.name,a(),i('probe transport "%s" failed because of error: %s',e,n),r.emit("upgradeError",o)}function c(){s("transport closed")}function l(){s("socket closed")}function p(e){t&&e.name!==t.name&&(i('"%s" works - aborting "%s"',e.name,t.name),a())}function d(){t.removeListener("open",o),t.removeListener("error",s),t.removeListener("close",c),r.removeListener("close",l),r.removeListener("upgrading",p)}u.priorWebsocketSuccess=!1,t.once("open",o),t.once("error",s),t.once("close",c),this.once("close",l),this.once("upgrading",p),t.open()},u.prototype.onOpen=function(){if(i("socket open"),this.readyState="open",u.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause){i("starting upgrade probes");for(var e=0,t=this.upgrades.length;e<t;e++)this.probe(this.upgrades[e])}},u.prototype.onPacket=function(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(i('socket receive: type "%s", data "%s"',e.type,e.data),this.emit("packet",e),this.emit("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var t=new Error("server error");t.code=e.data,this.onError(t);break;case"message":this.emit("data",e.data),this.emit("message",e.data)}else i('packet received with socket readyState "%s"',this.readyState)},u.prototype.onHandshake=function(e){this.emit("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},u.prototype.onHeartbeat=function(e){clearTimeout(this.pingTimeoutTimer);var t=this;t.pingTimeoutTimer=setTimeout(function(){"closed"!==t.readyState&&t.onClose("ping timeout")},e||t.pingInterval+t.pingTimeout)},u.prototype.setPing=function(){var e=this;clearTimeout(e.pingIntervalTimer),e.pingIntervalTimer=setTimeout(function(){i("writing ping packet - expecting pong within %sms",e.pingTimeout),e.ping(),e.onHeartbeat(e.pingTimeout)},e.pingInterval)},u.prototype.ping=function(){var e=this;this.sendPacket("ping",function(){e.emit("ping")})},u.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),(this.prevBufferLen=0)===this.writeBuffer.length?this.emit("drain"):this.flush()},u.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(i("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},u.prototype.write=u.prototype.send=function(e,t,n){return this.sendPacket("message",e,t,n),this},u.prototype.sendPacket=function(e,t,n,r){"function"==typeof t&&(r=t,t=void 0),"function"==typeof n&&(r=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState&&((n=n||{}).compress=!1!==n.compress,this.emit("packetCreate",e={type:e,data:t,options:n}),this.writeBuffer.push(e),r&&this.once("flush",r),this.flush())},u.prototype.close=function(){var e;function t(){e.onClose("forced close"),i("socket closing - telling transport to close"),e.transport.close()}function n(){e.removeListener("upgrade",n),e.removeListener("upgradeError",n),t()}function r(){e.once("upgrade",n),e.once("upgradeError",n)}return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",(e=this).writeBuffer.length?this.once("drain",function(){(this.upgrading?r:t)()}):(this.upgrading?r:t)()),this},u.prototype.onError=function(e){i("socket error %j",e),u.priorWebsocketSuccess=!1,this.emit("error",e),this.onClose("transport error",e)},u.prototype.onClose=function(e,t){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(i('socket close with reason: "%s"',e),clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",e,t),this.writeBuffer=[],this.prevBufferLen=0)},u.prototype.filterUpgrades=function(e){for(var t=[],n=0,r=e.length;n<r;n++)~a(this.transports,e[n])&&t.push(e[n]);return t}},function(e,t){try{e.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){e.exports=!1}},function(e,t,n){var r=n(9),o=n(18),i=n(12),a=n(3),s=n(4)("engine.io-client:polling-xhr");n=n(10);function c(){}function l(e){var t,n;o.call(this,e),this.requestTimeout=e.requestTimeout,this.extraHeaders=e.extraHeaders,"undefined"!=typeof location&&(t="https:"===location.protocol,n=(n=location.port)||(t?443:80),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||n!==e.port,this.xs=e.secure!==t)}function u(e){this.method=e.method||"GET",this.uri=e.uri,this.xd=!!e.xd,this.xs=!!e.xs,this.async=!1!==e.async,this.data=void 0!==e.data?e.data:null,this.agent=e.agent,this.isBinary=e.isBinary,this.supportsBinary=e.supportsBinary,this.enablesXDR=e.enablesXDR,this.withCredentials=e.withCredentials,this.requestTimeout=e.requestTimeout,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized,this.extraHeaders=e.extraHeaders,this.create()}function p(){for(var e in u.requests)u.requests.hasOwnProperty(e)&&u.requests[e].abort()}e.exports=l,e.exports.Request=u,a(l,o),l.prototype.supportsBinary=!0,l.prototype.request=function(e){return(e=e||{}).uri=this.uri(),e.xd=this.xd,e.xs=this.xs,e.agent=this.agent||!1,e.supportsBinary=this.supportsBinary,e.enablesXDR=this.enablesXDR,e.withCredentials=this.withCredentials,e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,e.requestTimeout=this.requestTimeout,e.extraHeaders=this.extraHeaders,new u(e)},l.prototype.doWrite=function(e,t){e=this.request({method:"POST",data:e,isBinary:"string"!=typeof e&&void 0!==e});var n=this;e.on("success",t),e.on("error",function(e){n.onError("xhr post error",e)}),this.sendXhr=e},l.prototype.doPoll=function(){s("xhr poll");var e=this.request(),t=this;e.on("data",function(e){t.onData(e)}),e.on("error",function(e){t.onError("xhr poll error",e)}),this.pollXhr=e},i(u.prototype),u.prototype.create=function(){var e={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR},t=(e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,this.xhr=new r(e)),n=this;try{s("xhr open %s: %s",this.method,this.uri),t.open(this.method,this.uri,this.async);try{if(this.extraHeaders)for(var o in t.setDisableHeaderCheck&&t.setDisableHeaderCheck(!0),this.extraHeaders)this.extraHeaders.hasOwnProperty(o)&&t.setRequestHeader(o,this.extraHeaders[o])}catch(e){}if("POST"===this.method)try{this.isBinary?t.setRequestHeader("Content-type","application/octet-stream"):t.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{t.setRequestHeader("Accept","*/*")}catch(e){}"withCredentials"in t&&(t.withCredentials=this.withCredentials),this.requestTimeout&&(t.timeout=this.requestTimeout),this.hasXDR()?(t.onload=function(){n.onLoad()},t.onerror=function(){n.onError(t.responseText)}):t.onreadystatechange=function(){if(2===t.readyState)try{var e=t.getResponseHeader("Content-Type");(n.supportsBinary&&"application/octet-stream"===e||"application/octet-stream; charset=UTF-8"===e)&&(t.responseType="arraybuffer")}catch(e){}4===t.readyState&&(200===t.status||1223===t.status?n.onLoad():setTimeout(function(){n.onError("number"==typeof t.status?t.status:0)},0))},s("xhr data %s",this.data),t.send(this.data)}catch(e){return void setTimeout(function(){n.onError(e)},0)}"undefined"!=typeof document&&(this.index=u.requestsCount++,u.requests[this.index]=this)},u.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},u.prototype.onData=function(e){this.emit("data",e),this.onSuccess()},u.prototype.onError=function(e){this.emit("error",e),this.cleanup(!0)},u.prototype.cleanup=function(e){if(void 0!==this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=c:this.xhr.onreadystatechange=c,e)try{this.xhr.abort()}catch(e){}"undefined"!=typeof document&&delete u.requests[this.index],this.xhr=null}},u.prototype.onLoad=function(){var e,t;try{try{t=this.xhr.getResponseHeader("Content-Type")}catch(e){}e=("application/octet-stream"===t||"application/octet-stream; charset=UTF-8"===t)&&this.xhr.response||this.xhr.responseText}catch(e){this.onError(e)}null!=e&&this.onData(e)},u.prototype.hasXDR=function(){return"undefined"!=typeof XDomainRequest&&!this.xs&&this.enablesXDR},u.prototype.abort=function(){this.cleanup()},u.requestsCount=0,u.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",p):"function"==typeof addEventListener&&(e="onpagehide"in n?"pagehide":"unload",addEventListener(e,p,!1)))},function(e,t){e.exports=Object.keys||function(e){var t,n=[],r=Object.prototype.hasOwnProperty;for(t in e)r.call(e,t)&&n.push(t);return n}},function(e,t){e.exports=function(e,t,n){var r=e.byteLength;if(t=t||0,n=n||r,e.slice)return e.slice(t,n);if(t<0&&(t+=r),n<0&&(n+=r),r<n&&(n=r),r<=t||n<=t||0===r)return new ArrayBuffer(0);for(var o=new Uint8Array(e),i=new Uint8Array(n-t),a=t,s=0;a<n;a++,s++)i[s]=o[a];return i.buffer}},function(e,t){function n(){}e.exports=function(e,t,r){var o=!1;return r=r||n,0===(i.count=e)?t():i;function i(e,n){if(i.count<=0)throw new Error("after called too many times");--i.count,e?(o=!0,t(e),t=r):0!==i.count||o||t(null,n)}}},function(e,t){
/*! https://mths.be/utf8js v2.1.2 by @mathias */
var n,r,o,i=String.fromCharCode;function a(e){for(var t,n,r=[],o=0,i=e.length;o<i;)55296<=(t=e.charCodeAt(o++))&&t<=56319&&o<i?56320==(64512&(n=e.charCodeAt(o++)))?r.push(((1023&t)<<10)+(1023&n)+65536):(r.push(t),o--):r.push(t);return r}function s(e,t){if(!(55296<=e&&e<=57343))return 1;if(t)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function c(e,t){return i(e>>t&63|128)}function l(){if(r<=o)throw Error("Invalid byte index");var e=255&n[o];if(o++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}e.exports={version:"2.1.2",encode:function(e,t){for(var n,r,o,l=!1!==(t=t||{}).strict,u=a(e),p=u.length,d=-1,f="";++d<p;)f+=(r=l,o=void 0,0==(4294967168&(n=n=u[d]))?i(n):(o="",0==(4294965248&n)?o=i(n>>6&31|192):0==(4294901760&n)?(s(n,r)||(n=65533),o=i(n>>12&15|224),o+=c(n,6)):0==(4292870144&n)&&(o=i(n>>18&7|240),o=(o+=c(n,12))+c(n,6)),o+i(63&n|128)));return f},decode:function(e,t){for(var c,u=!1!==(t=t||{}).strict,p=(n=a(e),r=n.length,o=0,[]);!1!==(c=function(e){var t,i;if(r<o)throw Error("Invalid byte index");if(o==r)return!1;if(t=255&n[o],o++,0==(128&t))return t;if(192==(224&t)){if(128<=(i=(31&t)<<6|l()))return i;throw Error("Invalid continuation byte")}if(224==(240&t)){if(2048<=(i=(15&t)<<12|l()<<6|l()))return s(i,e)?i:65533;throw Error("Invalid continuation byte")}if(240==(248&t)&&65536<=(i=(7&t)<<18|l()<<12|l()<<6|l())&&i<=1114111)return i;throw Error("Invalid UTF-8 detected")}(u));)p.push(c);for(var d,f=p,h=f.length,A=-1,g="";++A<h;)65535<(d=f[A])&&(g+=i((d-=65536)>>>10&1023|55296),d=56320|1023&d),g+=i(d);return g}}},function(e,t){!function(e){"use strict";t.encode=function(t){for(var n=new Uint8Array(t),r=n.length,o="",i=0;i<r;i+=3)o=(o=(o=(o+=e[n[i]>>2])+e[(3&n[i])<<4|n[i+1]>>4])+e[(15&n[i+1])<<2|n[i+2]>>6])+e[63&n[i+2]];return r%3==2?o=o.substring(0,o.length-1)+"=":r%3==1&&(o=o.substring(0,o.length-2)+"=="),o},t.decode=function(t){for(var n,r,o,i,a=.75*t.length,s=t.length,c=0,l=(a=("="===t[t.length-1]&&(a--,"="===t[t.length-2])&&a--,new ArrayBuffer(a)),new Uint8Array(a)),u=0;u<s;u+=4)n=e.indexOf(t[u]),r=e.indexOf(t[u+1]),o=e.indexOf(t[u+2]),i=e.indexOf(t[u+3]),l[c++]=n<<2|r>>4,l[c++]=(15&r)<<4|o>>2,l[c++]=(3&o)<<6|63&i;return a}}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/")},function(e,t){var n="undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder&&MozBlobBuilder,r=function(){try{return 2===new Blob(["hi"]).size}catch(e){return!1}}(),o=r&&function(){try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(e){return!1}}(),i=n&&n.prototype.append&&n.prototype.getBlob;function a(e){return e.map(function(e){var t,n;return e.buffer instanceof ArrayBuffer?(t=e.buffer,e.byteLength!==t.byteLength&&((n=new Uint8Array(e.byteLength)).set(new Uint8Array(t,e.byteOffset,e.byteLength)),t=n.buffer),t):e})}function s(e,t){t=t||{};var r=new n;return a(e).forEach(function(e){r.append(e)}),t.type?r.getBlob(t.type):r.getBlob()}function c(e,t){return new Blob(a(e),t||{})}"undefined"!=typeof Blob&&(s.prototype=Blob.prototype,c.prototype=Blob.prototype),e.exports=r?o?Blob:c:i?s:void 0},function(e,t,n){function r(e){var n;function r(){if(r.enabled){for(var e=r,o=+new Date,i=o-(n||o),a=(e.diff=i,e.prev=n,e.curr=o,n=o,new Array(arguments.length)),s=0;s<a.length;s++)a[s]=arguments[s];a[0]=t.coerce(a[0]),"string"!=typeof a[0]&&a.unshift("%O");var c=0;a[0]=a[0].replace(/%([a-zA-Z%])/g,function(n,r){var o;return"%%"!==n&&(c++,"function"==typeof(r=t.formatters[r]))&&(o=a[c],n=r.call(e,o),a.splice(c,1),c--),n}),t.formatArgs.call(e,a),(r.log||t.log||void 0).apply(e,a)}}return r.namespace=e,r.enabled=t.enabled(e),r.useColors=t.useColors(),r.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),r.destroy=o,"function"==typeof t.init&&t.init(r),t.instances.push(r),r}function o(){var e=t.instances.indexOf(this);return-1!==e&&(t.instances.splice(e,1),!0)}(t=e.exports=r.debug=r.default=r).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];var n,r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")));for(n=0;n<t.instances.length;n++){var i=t.instances[n];i.enabled=t.enabled(i.namespace)}},t.enabled=function(e){if("*"===e[e.length-1])return!0;var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(67),t.instances=[],t.names=[],t.skips=[],t.formatters={}},function(e,t){function n(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var r=typeof e;if(!("string"==r&&0<e.length)){if("number"==r&&!1===isNaN(e))return t.long?n(r=e,864e5,"day")||n(r,36e5,"hour")||n(r,6e4,"minute")||n(r,1e3,"second")||r+" ms":864e5<=(t=e)?Math.round(t/864e5)+"d":36e5<=t?Math.round(t/36e5)+"h":6e4<=t?Math.round(t/6e4)+"m":1e3<=t?Math.round(t/1e3)+"s":t+"ms";throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}if(r=e,!(100<(r=String(r)).length)&&(r=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(r))){var o=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*o;case"days":case"day":case"d":return 864e5*o;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*o;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*o;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*o;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return o;default:return}}}},function(e,t,n){var r,o=n(18),i=n(3),a=n(10),s=(e.exports=u,/\n/g),c=/\\n/g;function l(){}function u(e){o.call(this,e),this.query=this.query||{},r=r||(a.___eio=a.___eio||[]),this.index=r.length;var t=this;r.push(function(e){t.onData(e)}),this.query.j=this.index,"function"==typeof addEventListener&&addEventListener("beforeunload",function(){t.script&&(t.script.onerror=l)},!1)}i(u,o),u.prototype.supportsBinary=!1,u.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),o.prototype.doClose.call(this)},u.prototype.doPoll=function(){var e=this,t=document.createElement("script"),n=(this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),t.async=!0,t.src=this.uri(),t.onerror=function(t){e.onError("jsonp poll error",t)},document.getElementsByTagName("script")[0]);n?n.parentNode.insertBefore(t,n):(document.head||document.body).appendChild(t),this.script=t,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout(function(){var e=document.createElement("iframe");document.body.appendChild(e),document.body.removeChild(e)},100)},u.prototype.doWrite=function(e,t){var n,r,o,i,a=this;function l(){u(),t()}function u(){if(a.iframe)try{a.form.removeChild(a.iframe)}catch(e){a.onError("jsonp polling iframe removal error",e)}try{var e='<iframe src="javascript:0" name="'+a.iframeId+'">';i=document.createElement(e)}catch(e){(i=document.createElement("iframe")).name=a.iframeId,i.src="javascript:0"}i.id=a.iframeId,a.form.appendChild(i),a.iframe=i}this.form||(n=document.createElement("form"),r=document.createElement("textarea"),o=this.iframeId="eio_iframe_"+this.index,n.className="socketio",n.style.position="absolute",n.style.top="-1000px",n.style.left="-1000px",n.target=o,n.method="POST",n.setAttribute("accept-charset","utf-8"),r.name="d",n.appendChild(r),document.body.appendChild(n),this.form=n,this.area=r),this.form.action=this.uri(),u(),e=e.replace(c,"\\\n"),this.area.value=e.replace(s,"\\n");try{this.form.submit()}catch(e){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"===a.iframe.readyState&&l()}:this.iframe.onload=l}},function(e,t,n){(function(t){var r,o,i=n(11),a=n(0),s=n(13),c=n(3),l=n(20),u=n(4)("engine.io-client:websocket");if("undefined"!=typeof WebSocket?r=WebSocket:"undefined"!=typeof self&&(r=self.WebSocket||self.MozWebSocket),"undefined"==typeof window)try{o=n(70)}catch(c){}var p=r||o;function d(e){e&&e.forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=e.perMessageDeflate,this.usingBrowserWebSocket=r&&!e.forceNode,this.protocols=e.protocols,this.usingBrowserWebSocket||(p=o),i.call(this,e)}c(e.exports=d,i),d.prototype.name="websocket",d.prototype.supportsBinary=!0,d.prototype.doOpen=function(){if(this.check()){var e=this.uri(),t=this.protocols,n={};this.isReactNative||(n.agent=this.agent,n.perMessageDeflate=this.perMessageDeflate,n.pfx=this.pfx,n.key=this.key,n.passphrase=this.passphrase,n.cert=this.cert,n.ca=this.ca,n.ciphers=this.ciphers,n.rejectUnauthorized=this.rejectUnauthorized),this.extraHeaders&&(n.headers=this.extraHeaders),this.localAddress&&(n.localAddress=this.localAddress);try{this.ws=this.usingBrowserWebSocket&&!this.isReactNative?t?new p(e,t):new p(e):new p(e,t,n)}catch(e){return this.emit("error",e)}void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},d.prototype.addEventListeners=function(){var e=this;this.ws.onopen=function(){e.onOpen()},this.ws.onclose=function(){e.onClose()},this.ws.onmessage=function(t){e.onData(t.data)},this.ws.onerror=function(t){e.onError("websocket error",t)}},d.prototype.write=function(e){for(var n=this,r=(this.writable=!1,e.length),o=0,i=r;o<i;o++)!function(e){a.encodePacket(e,n.supportsBinary,function(o){var i;n.usingBrowserWebSocket||(i={},e.options&&(i.compress=e.options.compress),n.perMessageDeflate&&("string"==typeof o?t.byteLength(o):o.length)<n.perMessageDeflate.threshold&&(i.compress=!1));try{n.usingBrowserWebSocket?n.ws.send(o):n.ws.send(o,i)}catch(o){u("websocket closed before onclose event")}--r||(n.emit("flush"),setTimeout(function(){n.writable=!0,n.emit("drain")},0))})}(e[o])},d.prototype.onClose=function(){i.prototype.onClose.call(this)},d.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},d.prototype.uri=function(){var e=this.query||{},t=this.secure?"wss":"ws",n="";return this.port&&("wss"==t&&443!==Number(this.port)||"ws"==t&&80!==Number(this.port))&&(n=":"+this.port),this.timestampRequests&&(e[this.timestampParam]=l()),this.supportsBinary||(e.b64=1),(e=s.encode(e)).length&&(e="?"+e),t+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+e},d.prototype.check=function(){return!(!p||"__initialize"in p&&this.name===d.prototype.name)}}).call(this,n(8).Buffer)},function(e,t){},function(e,t){var n=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,r=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];e.exports=function(e){for(var t,o=e,i=e.indexOf("["),a=e.indexOf("]"),s=(-1!=i&&-1!=a&&(e=e.substring(0,i)+e.substring(i,a).replace(/:/g,";")+e.substring(a,e.length)),n.exec(e||"")),c={},l=14;l--;)c[r[l]]=s[l]||"";return-1!=i&&-1!=a&&(c.source=o,c.host=c.host.substring(1,c.host.length-1).replace(/;/g,":"),c.authority=c.authority.replace("[","").replace("]","").replace(/;/g,":"),c.ipv6uri=!0),c.pathNames=function(e){var t=e.replace(/\/{2,9}/g,"/").split("/");return"/"!=e.substr(0,1)&&0!==e.length||t.splice(0,1),"/"==e.substr(e.length-1,1)&&t.splice(t.length-1,1),t}(c.path),c.queryKey=(t={},c.query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,n,r){n&&(t[n]=r)}),t),c}},function(e,t){e.exports=function(e,t){for(var n=[],r=(t=t||0)||0;r<e.length;r++)n[r-t]=e[r];return n}},function(e,t){t.encode=function(e){var t,n="";for(t in e)e.hasOwnProperty(t)&&(n.length&&(n+="&"),n+=encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return n},t.decode=function(e){for(var t={},n=e.split("&"),r=0,o=n.length;r<o;r++){var i=n[r].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}},function(e,t){function n(e){this.ms=(e=e||{}).min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=0<e.jitter&&e.jitter<=1?e.jitter:0,this.attempts=0}(e.exports=n).prototype.duration=function(){var e,t,n=this.ms*Math.pow(this.factor,this.attempts++);return this.jitter&&(e=Math.random(),t=Math.floor(e*this.jitter*n),n=0==(1&Math.floor(10*e))?n-t:n+t),0|Math.min(n,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(e){this.ms=e},n.prototype.setMax=function(e){this.max=e},n.prototype.setJitter=function(e){this.jitter=e}},function(e,t,n){var r,o;r=window.WEB_SOCKET_LOGGER?WEB_SOCKET_LOGGER:window.console&&window.console.log&&window.console.error?window.console:{log:function(){},error:function(){}},o=swfobject.getFlashPlayerVersion(),r.log("[WebSocket] v360.01 start defer init: "+o.major),window.NativeWebSocket=window.WebSocket||window.MozWebSocket,"file:"==location.protocol&&r.error("WARNING: web-socket-js doesn't work in file:///... URL unless you set Flash Security Settings properly. Open the page via Web server i.e. http://..."),window.WebSocket=function(e,t,n,r,o,i){var a=this;a.__id=WebSocket.__nextId++,(WebSocket.__instances[a.__id]=a).readyState=WebSocket.CONNECTING,a.bufferedAmount=0,a.__events={},t?"string"==typeof t&&(t=[t]):t=[],a.__createTask=setTimeout(function(){WebSocket.__addTask(function(){a.__createTask=null;try{WebSocket.__flash.create(a.__id,e,t,n||null,r||0,o||null,i||!1)}catch(s){a.__createTask=setTimeout(function(){WebSocket.__flash.create(a.__id,e,t,n||null,r||0,o||null,i||!1)},500)}})},0)},WebSocket.prototype.send=function(e){if(this.readyState==WebSocket.CONNECTING)throw"INVALID_STATE_ERR: Web Socket connection has not been established";return(e=WebSocket.__flash.send(this.__id,encodeURIComponent(e)))<0||(this.bufferedAmount+=e,!1)},WebSocket.prototype.close=function(){this.__createTask?(clearTimeout(this.__createTask),this.__createTask=null,this.readyState=WebSocket.CLOSED):this.readyState!=WebSocket.CLOSED&&this.readyState!=WebSocket.CLOSING&&(this.readyState=WebSocket.CLOSING,WebSocket.__flash.close(this.__id))},WebSocket.prototype.id=function(){return this.__id},WebSocket.prototype.addEventListener=function(e,t,n){e in this.__events||(this.__events[e]=[]),this.__events[e].push(t)},WebSocket.prototype.removeEventListener=function(e,t,n){if(e in this.__events)for(var r=this.__events[e],o=r.length-1;0<=o;--o)if(r[o]===t){r.splice(o,1);break}},WebSocket.prototype.dispatchEvent=function(e){for(var t=this.__events[e.type]||[],n=0;n<t.length;++n)t[n](e);var r=this["on"+e.type];r&&r.apply(this,[e])},WebSocket.prototype.__handleEvent=function(e){if("readyState"in e&&(this.readyState=e.readyState),"protocol"in e&&(this.protocol=e.protocol),"open"==e.type||"error"==e.type)n=this.__createSimpleEvent(e.type);else if("close"==e.type)(n=this.__createSimpleEvent("close")).wasClean=!!e.wasClean,n.code=e.code,n.reason=e.reason;else{if("message"!=e.type)throw"unknown event type: "+e.type;var t=decodeURIComponent(e.message),n=this.__createMessageEvent("message",t)}n.id=e.id,this.dispatchEvent(n)},WebSocket.prototype.__createSimpleEvent=function(e){var t;return document.createEvent&&window.Event?((t=document.createEvent("Event")).initEvent(e,!1,!1),t):{type:e,bubbles:!1,cancelable:!1}},WebSocket.prototype.__createMessageEvent=function(e,t){var n;return function(){try{return 1<navigator.userAgent.toLowerCase().indexOf("edge")}catch(e){}}()||!window.MessageEvent||"function"!=typeof MessageEvent||window.opera?document.createEvent&&window.MessageEvent&&!window.opera?((n=document.createEvent("MessageEvent")).initMessageEvent("message",!1,!1,t,null,null,window,null),n):{type:e,data:t,bubbles:!1,cancelable:!1}:new MessageEvent("message",{view:window,bubbles:!1,cancelable:!1,data:t})},WebSocket.CONNECTING=0,WebSocket.OPEN=1,WebSocket.CLOSING=2,WebSocket.CLOSED=3,WebSocket.__isFlashImplementation=!0,WebSocket.__initialized=!1,WebSocket.__flash=null,WebSocket.__instances={},WebSocket.__tasks=[],WebSocket.__nextId=0,WebSocket.loadFlashPolicyFile=function(e){WebSocket.__addTask(function(){WebSocket.__flash.loadManualPolicyFile(e)})},WebSocket.setPolicyPort=function(e){WebSocket.__addTask(function(){WebSocket.__flash.setPolicyPort(e)})},WebSocket.__initialize=function(){var e,t,n;WebSocket.__initialized||(WebSocket.__initialized=!0,WebSocket.__swfLocation&&(window.WEB_SOCKET_SWF_LOCATION=WebSocket.__swfLocation),window.WEB_SOCKET_SWF_LOCATION?(window.WEB_SOCKET_SUPPRESS_CROSS_DOMAIN_SWF_ERROR||WEB_SOCKET_SWF_LOCATION.match(/(^|\/)WebSocketMainInsecure\.swf(\?.*)?$/)||!WEB_SOCKET_SWF_LOCATION.match(/^\w+:\/\/([^\/]+)/)||(t=RegExp.$1,location.host!=t&&r.error("[WebSocket] You must host HTML and WebSocketMain.swf in the same host ('"+location.host+"' != '"+t+"'). See also 'How to host HTML file and SWF file in different domains' section in README.md. If you use WebSocketMainInsecure.swf, you can suppress this message by WEB_SOCKET_SUPPRESS_CROSS_DOMAIN_SWF_ERROR = true;")),(t=document.createElement("div")).id="webSocketContainer",t.style.position="absolute",WebSocket.__isFlashLite()?(t.style.left="0px",t.style.top="0px"):(t.style.left="-100px",t.style.top="-100px",t.style.zIndex=-1,t.style.opacity=0),(e=document.createElement("div")).id="webSocketFlash",t.appendChild(e),document.body.appendChild(t),t=e="1px",n="window",function(){var e,t,n,r,o=navigator.userAgent.toLowerCase();if(1<o.indexOf("chrome")&&(e=name,n="",r=0,(t=o.match(/msie ([\d.]+)/))?a("Trident",i(t[1])):(t=o.match(/firefox\/([\d.]+)/))?a("Gecko",i(t[1])):(t=o.match(/chrome\/([\d.]+)/))?a("WebKit",i(t[1])):(t=o.match(/opera.([\d.]+)/))?a("Presto",i(t[1])):(t=o.match(/version\/([\d.]+).*safari/))&&a("WebKit",i(t[1])),"59"<=("kernel"==e?n:r)))return 1;function i(e){return e}function a(e,t){n=e,r=t}}()&&(t=e="600px",n="transparent"),swfobject.embedSWF(WEB_SOCKET_SWF_LOCATION,"webSocketFlash",e,t,"11.4",null,null,{hasPriority:!0,swliveconnect:!0,allowScriptAccess:"always",wmode:n},null,function(e){e.success||r.error("[flash socket] swfobject.embedSWF failed")})):r.error("[WebSocket] set WEB_SOCKET_SWF_LOCATION to location of WebSocketMain.swf"))},WebSocket.__isFlashInitialized=!1,WebSocket.isInitialized=function(){return WebSocket.__isFlashInitialized},WebSocket.__onFlashInitialized=function(){r.log("[flash socket] __onFlashInitialized is finished"),WebSocket.__isFlashInitialized=!0,setTimeout(function(){WebSocket.__flash=document.getElementById("webSocketFlash"),WebSocket.__flash.setCallerUrl(location.href);var e=!!window.WEB_SOCKET_DEBUG;WebSocket.__flash.setDebug(e);for(var t=0;t<WebSocket.__tasks.length;++t)WebSocket.__tasks[t]();WebSocket.__tasks=[]},500)},WebSocket.__onFlashEvent=function(){return setTimeout(function(){try{for(var e=WebSocket.__flash.receiveEvents(),t=0;t<e.length;++t)WebSocket.__instances[e[t].webSocketId].__handleEvent(e[t])}catch(e){r.error(e)}},0),!0},WebSocket.__log=function(e){r.log(decodeURIComponent(e))},WebSocket.__error=function(e){r.error(decodeURIComponent(e))},WebSocket.__addTask=function(e){WebSocket.__flash?e():WebSocket.__tasks.push(e)},WebSocket.__isFlashLite=function(){var e;return!!(window.navigator&&window.navigator.mimeTypes&&(e=window.navigator.mimeTypes["application/x-shockwave-flash"])&&e.enabledPlugin&&e.enabledPlugin.filename&&e.enabledPlugin.filename.match(/flashlite/i))},window.isWebSocketModuleInitFunCall=!1,window.WebSocketModuleInit=function(e){var t=swfobject.getFlashPlayerVersion();r.log("[flash socket] module init: "+t.major+", isWebSocketModuleInitFunCall: "+window.isWebSocketModuleInitFunCall),e&&swfobject.getFlashPlayerVersion().major<11.4?r.error("[flash socket] Flash Player >= 11.4 is required."):window.isWebSocketModuleInitFunCall||(window.isWebSocketModuleInitFunCall=!0,WebSocket.__initialize())}},function(e,t,n){"use strict";n.r(t);var r={},o=(n.r(r),n.d(r,"shimGetUserMedia",function(){return bt}),n.d(r,"shimGetDisplayMedia",function(){return Ct}),n.d(r,"shimMediaStream",function(){return Et}),n.d(r,"shimOnTrack",function(){return Mt}),n.d(r,"shimGetSendersWithDtmf",function(){return It}),n.d(r,"shimGetStats",function(){return Tt}),n.d(r,"shimSenderReceiverGetStats",function(){return kt}),n.d(r,"shimAddTrackRemoveTrackWithNative",function(){return St}),n.d(r,"shimAddTrackRemoveTrack",function(){return Dt}),n.d(r,"shimPeerConnection",function(){return xt}),n.d(r,"fixNegotiationNeeded",function(){return Nt}),{}),i=(n.r(o),n.d(o,"shimGetUserMedia",function(){return Bt}),n.d(o,"shimGetDisplayMedia",function(){return Lt}),n.d(o,"shimPeerConnection",function(){return _t}),n.d(o,"shimReplaceTrack",function(){return Ot}),{}),a=(n.r(i),n.d(i,"shimGetUserMedia",function(){return Rt}),n.d(i,"shimGetDisplayMedia",function(){return Pt}),n.d(i,"shimOnTrack",function(){return Qt}),n.d(i,"shimPeerConnection",function(){return Ft}),n.d(i,"shimSenderGetStats",function(){return Ut}),n.d(i,"shimReceiverGetStats",function(){return Yt}),n.d(i,"shimRemoveStream",function(){return zt}),n.d(i,"shimRTCDataChannel",function(){return Gt}),n.d(i,"shimAddTransceiver",function(){return Wt}),n.d(i,"shimGetParameters",function(){return Ht}),n.d(i,"shimCreateOffer",function(){return Jt}),n.d(i,"shimCreateAnswer",function(){return Vt}),{}),s=(n.r(a),n.d(a,"shimLocalStreamsAPI",function(){return Kt}),n.d(a,"shimRemoteStreamsAPI",function(){return Xt}),n.d(a,"shimCallbacksAPI",function(){return Zt}),n.d(a,"shimGetUserMedia",function(){return qt}),n.d(a,"shimConstraints",function(){return $t}),n.d(a,"shimRTCIceServerUrls",function(){return en}),n.d(a,"shimTrackEventTransceiver",function(){return tn}),n.d(a,"shimCreateOfferLegacy",function(){return nn}),n.d(a,"shimAudioContext",function(){return rn}),{});function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}n.r(s),n.d(s,"shimRTCIceCandidate",function(){return an}),n.d(s,"shimMaxMessageSize",function(){return sn}),n.d(s,"shimSendThrowTypeError",function(){return cn}),n.d(s,"shimConnectionState",function(){return ln}),n.d(s,"removeExtmapAllowMixed",function(){return un}),n.d(s,"shimAddIceCandidateNullOrEmpty",function(){return pn});var l=function(){function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function")}var t,n;return t=e,(n=[{key:"isIE8",value:function(){var e=window.navigator.userAgent.toLowerCase();return!(e.indexOf("msie")<0)&&/msie 8\.0/i.test(e)}},{key:"isIE9",value:function(){var e=window.navigator.userAgent.toLowerCase();return!(e.indexOf("msie")<0)&&"9."==e.match(/9./i)}},{key:"isIE10",value:function(){return!(window.navigator.userAgent.toLowerCase().indexOf("msie")<0)&&/MSIE\s+10.0/i.test(navigator.userAgent)&&function(){return void 0===this}()}},{key:"isFirefox",value:function(){return"undefined"!=typeof InstallTrigger}},{key:"_isChrome",value:function(){return 1<navigator.userAgent.toLowerCase().indexOf("chrome")}},{key:"_isEdge",value:function(){return 1<navigator.userAgent.toLowerCase().indexOf("edge")}},{key:"_isMimeType",value:function(e,t){try{var n,r=navigator.mimeTypes;for(n in r)if(r[n][e]==t)return!0}catch(e){}return!1}},{key:"_is360Browser",value:function(){var e=_isMimeType("type","application/vnd.chromium.remoting-viewer");if(!e)try{window.navigator.webkitPersistentStorage||(IS_360RotateIE9=e=!0)}catch(e){}if(!e)try{!window.navigator.mimeTypes[40]&&window.navigator.mimeTypes.length||(IS_360RotateIE9=e=!0)}catch(e){}return e}}])&&c(t.prototype,n),e}();function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function d(e){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){return(f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var h=function(){var e=n,t=l;if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");function n(){if(this instanceof n)return function(e,t){if(!t||"object"!==u(t)&&"function"!=typeof t){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t}(this,d(n).call(this));throw new TypeError("Cannot call a class as a function")}return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t),e=n,(t=[{key:"isDataType",value:function(e){return(e=Object.prototype.toString.call(e)).substring(8,e.length-1).toLowerCase()}},{key:"hasMasterId",value:function(e,t){return t.some(function(t){return t.phoneId==e})}},{key:"getPhoneIds",value:function(e){if(void 0!==e){for(var t=[],n=0;n<e.length;n++){if("string"!=this.isDataType(e[n].phoneId)||!e[n].phoneId||isNaN(e[n].phoneId)){t=!1;break}t.push(e[n].phoneId)}return t}}},{key:"getDomIds",value:function(e){for(var t=[],n=0;n<e.length;n++){if(!e[n].domId){t=!1;break}t.push(e[n].domId)}return t}},{key:"getSlaveIdsValue",value:function(e,t,n){return(e=e.filter(function(e){return e.phoneId==t||e.domId==t})).length&&e[0][n]||!1}},{key:"emptyDom",value:function(e){return!!(e=document.getElementById(e))&&(e.innerHTML&&(e.innerHTML=""),e)}},{key:"filterMaterId",value:function(e,t){return t.filter(function(t){return e!=t.phoneId})}},{key:"getStyle",value:function(e,t){return(e.currentStyle||getComputedStyle(e,!1))[t]}},{key:"findInArrRreduction",value:function(e,t){if("object"==this.isDataType(t))for(var n=0;n<e.length;n++)if(e[n].phoneId==t.phoneId&&e[n].domId==t.domId)return!0;return!1}},{key:"findInArrIncrement",value:function(e,t){if("object"!=this.isDataType(t))return!1;for(var n=0;n<e.length;n++)if(e[n].phoneId==t.phoneId||e[n].domId==t.domId)return!1;return!0}},{key:"arrPush",value:function(e,t,n,r){for(var o=0;o<e.length;o++)if(e[o].domId==t){e[o][n]=r;break}}},{key:"addStyleCss",value:function(e,t){for(var n in t)e.style[n]=t[n]}},{key:"addSetAttribute",value:function(e,t){for(var n in t)e.setAttribute(n,t[n])}},{key:"createInputElem",value:function(e){var t=document.createElement("div"),n=document.createElement("textarea");return this.addStyleCss(t,{position:"absolute",left:"0",top:"0",zIndex:"2",width:"200px",height:"1px",backgroundColor:"transparent",opacity:"1"}),this.addSetAttribute(n,{class:"inputTextarea",dataUtem:e,autocomplete:"off",onkeydown:"this.onkeyup();",onkeyup:"this.size=(this.value.length>20?this.value.length:20);"}),this.addStyleCss(n,{display:"none",width:"100%",height:"100%",border:"none",color:"transparent",fontSize:"12px",background:"transparent",outline:"none"}),t.insertBefore(n,t.childNodes[0]),t}},{key:"flashCheck",value:function(){var e=navigator.plugins["Shockwave Flash"];return!!e&&parseInt(e.description.split(" ")[2],10)}},{key:"getBrowser",value:function(e){var t,n=navigator.userAgent.toLowerCase(),r="",o=0;function i(e){return e}function a(e,t){r=e,o=t}return(t=n.match(/msie ([\d.]+)/))?a("Trident",i(t[1])):(t=n.match(/firefox\/([\d.]+)/))?a("Gecko",i(t[1])):(t=n.match(/chrome\/([\d.]+)/))?a("WebKit",i(t[1])):(t=n.match(/opera.([\d.]+)/))?a("Presto",i(t[1])):(t=n.match(/version\/([\d.]+).*safari/))&&a("WebKit",i(t[1])),"kernel"==e?r:o}},{key:"detectOS",value:function(){var e=navigator.userAgent,t="Mac68K"==navigator.platform||"MacPPC"==navigator.platform||"Macintosh"==navigator.platform||"MacIntel"==navigator.platform;if(t)return"Mac";if("X11"==navigator.platform&&!n&&!t)return"Unix";if(-1<String(navigator.platform).indexOf("Linux"))return"Linux";var n="Win32"==navigator.platform||"Windows"==navigator.platform;if(n){if(-1<e.indexOf("Windows NT 5.0")||-1<e.indexOf("Windows 2000"))return"Win2000";if(-1<e.indexOf("Windows NT 5.1")||-1<e.indexOf("Windows XP"))return"WinXP";if(-1<e.indexOf("Windows NT 5.2")||-1<e.indexOf("Windows 2003"))return"Win2003";if(-1<e.indexOf("Windows NT 6.0")||-1<e.indexOf("Windows Vista"))return"WinVista";if(-1<e.indexOf("Windows NT 6.1")||-1<e.indexOf("Windows 7"))return"Win7";if(-1<e.indexOf("Windows NT 6.2")||-1<e.indexOf("Windows 8"))return"Win8";if(-1<e.indexOf("Windows NT 10.0")||e.indexOf("Windows NT 6.4")||-1<e.indexOf("Windows 10"))return"Win10"}return"other"}},{key:"getBasePath",value:function(e){var t="",n=document.getElementsByTagName("script");if(n&&0<n.length)for(var r in n)try{if(n[r].src){var o=n[r].src,i=o.indexOf(e);if(0<i){t=o.slice(0,i);break}}}catch(e){}return t}},{key:"hasOwnProperty",value:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},{key:"setSessionStore",value:function(e,t){hasOwnProperty(sessionStorage,"CLOUDPLAY")||sessionStorage.setItem("CLOUDPLAY","");var n={};return(n=sessionStorage.CLOUDPLAY?JSON.parse(sessionStorage.CLOUDPLAY):n)[e]=t,sessionStorage.CLOUDPLAY=JSON.stringify(n),!0}},{key:"removeSessionStore",value:function(e){var t;hasOwnProperty(sessionStorage,"CLOUDPLAY")&&sessionStorage.CLOUDPLAY&&(t=JSON.parse(sessionStorage.CLOUDPLAY),e)&&(delete t[e],sessionStorage.CLOUDPLAY=JSON.stringify(t))}},{key:"getSessionStore",value:function(e){if(!hasOwnProperty(sessionStorage,"CLOUDPLAY")||!sessionStorage.CLOUDPLAY)return{};try{var t=JSON.parse(sessionStorage.CLOUDPLAY);return e?t[e]:t}catch(e){return __hmLogError("call getSessionStore got exception: "+e.toString(),CP_ERROR_MAP.get_session_storage_error,e.toString()),{}}}},{key:"isWebsocketObject",value:function(e){try{return e&&(e instanceof WebSocket||e instanceof window.NativeWebSocket)}catch(t){return e}}},{key:"websocket_send",value:function(e,t,n){(e=e.getWsObj())&&e.readyState==WebSocket.OPEN&&e.send(t,n)}},{key:"dummyFunction",value:function(){}},{key:"websocket_close",value:function(e,t){(e=e.getWsObj())&&(e.onopen=this.dummyFunction,e.onmessage=this.dummyFunction,e.onclose=this.dummyFunction,t)&&e.close()}},{key:"mixTips",value:function(e,t){for(var n=0;n<t.length;n++)e=e.replace("|",t[n]);return e.replace(/\n/g,"<br />")}},{key:"indexBy",value:function(e,t,n){if(!e||!t)return{};var r={};for(o in e){var o,i=e[o];i&&i.hasOwnProperty(t)&&(r[o=i[t]]=n?i[n]||"":i)}return r}},{key:"getUrlQueryString",value:function(e){return null==(e=window.location.search.match(new RegExp("[?&]"+e+"=([^&]+)","i")))||e.length<1?"":e[1]}},{key:"isFlashNoCache",value:function(){try{return 1==this.getUrlQueryString("flashnocache").trim()}catch(e){return!1}}},{key:"alert",value:function(e){try{_walert.call(window,e)}catch(t){window.alert(e)}}},{key:"formatMS",value:function(e){var t,n="";return 3600<(e=e/1e3)&&((t=Math.floor(e/3600))&&(n+=t+"小时"),e%=3600),60<e&&((t=Math.floor(e/60))&&(n+=t+"分钟"),e%=60),(e=Math.ceil(e))&&(n+=e+"秒"),n}},{key:"isSupportRTC",value:function(){var e=navigator.userAgent.toLowerCase();return/chrome\//gi.test(e)?54<=parseFloat(e.match(/chrome\/(\d+)/i)[1]):/firefox\//gi.test(e)?46<=parseFloat(e.match(/firefox\/(\d+)/i)[1]):!!/safari\//gi.test(e)&&13<=parseFloat(e.match(/version\/(\d+)/i)[1])}},{key:"json2url",value:function(e){var t,n=[];for(t in e)n.push("".concat(t,"=").concat(e[t]));return n.join("&")}},{key:"getRandomString",value:function(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:16,t="abcdefghijklmnopqrstuvwxyz0123456789",n=t.charAt(Math.floor(26*Math.random())),r=0;r<e-1;++r)n+=t.charAt(Math.floor(Math.random()*t.length));return n}},{key:"ArrayCutApart",value:function(e,t){for(var n=[],r=0;r<e.length;r+=t)n.push(e.slice(r,r+t));return n}}])&&p(e.prototype,t),n}();function A(e){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function g(e){return(g=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function m(e,t){return(m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var y=1,v=2,w=3,b=new(function(){var e=n,t=h;if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");function n(){if(this instanceof n)return function(e,t){if(!t||"object"!==A(t)&&"function"!=typeof t){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t}(this,g(n).call(this));throw new TypeError("Cannot call a class as a function")}return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&m(e,t),n}());function C(e){if(e.method=e.method||"POST",e.timeout=e.timeout||15e3,e.contentType=e.contentType||"application/x-www-form-urlencoded",b.isIE8()||b.isIE9()){var t=e;try{var n=new XDomainRequest;/^\d+$/.test(t.timeout)&&(n.timeout=t.timeout),n.ontimeout=function(){t.done(!1,{code:y,message:"timeout"})},n.onload=function(){t.done(!0,n.responseText)},n.onprogress=function(){},n.onerror=function(){t.done(!1,{code:v,message:"onerror: "+n.responseText})},n.open(t.method,t.url),n.send(t.data)}catch(n){t.done(!1,{code:w,message:"Exception: "+n.toString()})}}else try{var r=window.XMLHttpRequest?new window.XMLHttpRequest:window.ActiveXObject?new ActiveXObject("Microsoft.XMLHTTP"):null;"GET"===e.method?(r.open("GET",e.url,!0),r.send()):(r.open("POST",e.url,!0),r.setRequestHeader("Content-type",e.contentType),r.send(e.data)),r.onreadystatechange=function(){4===this.readyState&&200<=this.status&&this.status<300?(e.done(!0,r.responseText),e.timer&&clearTimeout(e.timer)):4===this.readyState&&(e.done(!1,{code:4,message:"invalid status: "+this.status}),e.timer)&&clearTimeout(e.timer)},e.timeout&&(e.timer=setTimeout(function(){r.abort()},e.timeout))}catch(n){e.done(!1,{code:w,message:"Exception: "+n.toString()})}}var E=function e(t,n){return new e.fn.init(t,n)},M=window.document,I=[],T=I.concat,k=I.filter,S=I.slice,D={},x={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},N=/^\s*<(\w+|!)[^>]*>/,j=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,B=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,L=/^(?:body|html)$/i,_=["val","css","html","text","data","width","height","offset"],O=(t=M.createElement("table"),M.createElement("tr")),R={tr:M.createElement("tbody"),tbody:t,thead:t,tfoot:t,td:O,th:O,"*":M.createElement("div")},P=/^[\w-]*$/,Q={},F=Q.toString,U=M.createElement("div"),Y=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},z=M.documentElement.contains?function(e,t){return e!==t&&e.contains(t)}:function(e,t){for(;t=t&&t.parentNode;)if(t===e)return!0;return!1};function G(e){return null==e?String(e):Q[F.call(e)]||"object"}function W(e){return"function"==G(e)}function H(e){return null!=e&&e==e.window}function J(e){return null!=e&&e.nodeType==e.DOCUMENT_NODE}function V(e){return"object"==G(e)}function K(e){return V(e)&&!H(e)&&Object.getPrototypeOf(e)==Object.prototype}function X(e){var t=!!e&&"length"in e&&e.length,n=G(e);return"function"!=n&&!H(e)&&("array"==n||0===t||"number"==typeof t&&0<t&&t-1 in e)}function Z(e){return e.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function q(e,t){return"number"!=typeof t||x[Z(e)]?t:t+"px"}function $(e){return e.replace(/-+(.)?/g,function(e,t){return t?t.toUpperCase():""})}function ee(e){return e in D?D[e]:D[e]=new RegExp("(^|\\s)"+e+"(\\s|$)")}function te(e){return e instanceof E}function ne(e,t){return null==t?E(e):E(e).filter(t)}function re(e,t,n,r){return W(t)?t.call(e,n,r):t}function oe(e,t,n){null==n?e.removeAttribute(t):e.setAttribute(t,n)}function ie(e,t){var n=e.className||"",r=n&&void 0!==n.baseVal;if(void 0===t)return r?n.baseVal:n;r?n.baseVal=t:e.className=t}function ae(e,t,n,r){var o,i,a=E.map(t,function(e){var t=[];return"array"==(o=G(e))?(e.forEach(function(e){return void 0!==e.nodeType?t.push(e):te(e)?t=t.concat(e.get()):void(t=t.concat(E.fragment(e)))}),t):"object"==o||null==e?e:E.fragment(e)}),s=1<e.length;return a.length<1?e:e.each(function(e,t){i=r?t:t.parentNode;var o=z(M.documentElement,i);a.forEach(function(e){if(s)e=e.cloneNode(!0);else if(!i)return E(e).remove();n.call(t,e),o&&se(e,function(e){var t;null==e.nodeName||"SCRIPT"!==e.nodeName.toUpperCase()||e.type&&"text/javascript"!==e.type||e.src||(t=e.ownerDocument?e.ownerDocument.defaultView:window).eval.call(t,e.innerHTML)})})})}E.fn=E.prototype={constuctor:E,length:0,forEach:I.forEach,reduce:I.reduce,push:I.push,sort:I.sort,splice:I.splice,indexOf:I.indexOf,init:function(e,t){var n,r;if(!e)return this;if("string"==typeof e)if("<"==(e=e.trim())[0]&&N.test(e))n=E.fragment(e,RegExp.$1,t),e=null;else{if(void 0!==t)return E(t).find(e);n=E.qsa(M,e)}else{if(W(e))return E(M).ready(e);if(te(e))return e;if(Y(e))r=e,n=k.call(r,function(e){return null!=e});else if(V(e))n=[e],e=null;else{if(void 0!==t)return E(t).find(e);n=E.qsa(M,e)}}return E.makeArray(n,e,this)},concat:function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=te(e=arguments[n])?e.toArray():e;return T.apply(te(this)?this.toArray():this,t)},pluck:function(e){return E.map(this,function(t){return t[e]})},toArray:function(){return this.get()},get:function(e){return void 0===e?S.call(this):this[0<=e?e:e+this.length]},size:function(){return this.length},each:function(e){return I.every.call(this,function(t,n){return!1!==e.call(t,n,t)}),this},map:function(e){return E(E.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return E(S.apply(this,arguments))},first:function(){var e=this[0];return e&&!V(e)?e:E(e)},last:function(){var e=this[this.length-1];return e&&!V(e)?e:E(e)},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)}},E.extend=E.fn.extend=function(){var e,t,n,r,o,i=arguments[0]||{},a=1,s=arguments.length,c=!1;for("boolean"==typeof i&&(c=i,i=arguments[a]||{},a++),"object"==typeof i||W(i)||(i={}),a===s&&(i=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)o=i[t],i!==(n=e[t])&&(c&&n&&(K(n)||(r=Y(n)))?(o=r?(r=!1,o&&Y(o)?o:[]):o&&K(o)?o:{},i[t]=E.extend(c,o,n)):void 0!==n&&(i[t]=n));return i},E.extend({makeArray:function(e,t,n){for(var r=e?e.length:0,o=0;o<r;o++)n[o]=e[o];return n.length=r,n.selector=t||"",n},qsa:function(e,t){var n,r="#"==t[0],o=!r&&"."==t[0],i=r||o?t.slice(1):t,a=P.test(i);return e.getElementById&&a&&r?(n=e.getElementById(i))?[n]:[]:1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType?[]:S.call(a&&!r&&e.getElementsByClassName?o?e.getElementsByClassName(i):e.getElementsByTagName(t):e.querySelectorAll(t))},fragment:function(e,t,n){var r,o,i;return(r=j.test(e)?E(M.createElement(RegExp.$1)):r)||(e.replace&&(e=e.replace(B,"<$1></$2>")),void 0===t&&(t=N.test(e)&&RegExp.$1),(i=R[t=t in R?t:"*"]).innerHTML=""+e,r=E.each(S.call(i.childNodes),function(){i.removeChild(this)})),K(n)&&(o=E(r),E.each(n,function(e,t){-1<_.indexOf(e)?o[e](t):o.attr(e,t)})),r},matches:function(e,t){var n,r;return!(!t||!e||1!==e.nodeType)&&((n=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.matchesSelector)?n.call(e,t):((r=!(n=e.parentNode))&&(n=U).appendChild(e),n=~E.qsa(n,t).indexOf(e),r&&U.removeChild(e),n))},each:function(e,t){var n,r;if(X(e)){for(n=0;n<e.length;n++)if(!1===t.call(e[n],n,e[n]))return e}else for(r in e)if(!1===t.call(e[r],r,e[r]))return e;return e},map:function(e,t){var n,r,o,i,a=[];if(X(e))for(r=0;r<e.length;r++)null!=(n=t(e[r],r))&&a.push(n);else for(o in e)null!=(n=t(e[o],o))&&a.push(n);return 0<(i=a).length?E.fn.concat.apply([],i):i}}),E.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){Q["[object "+t+"]"]=t.toLowerCase()}),E.fn.init.prototype=E.fn;var se=function(e,t){t(e);for(var n=0,r=e.childNodes.length;n<r;n++)se(e.childNodes[n],t)},ce=1;function le(e){return e._zid||(e._zid=ce++)}function ue(e){return"string"==typeof e}function pe(){return!0}function de(){return!1}var fe={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};function he(e,t){if(t||!e.isDefaultPrevented){t=t||e,E.each(fe,function(n,r){var o=t[n];e[n]=function(){return this[r]=pe,o&&o.apply(t,arguments)},e[r]=de});try{e.timeStamp||(e.timeStamp=Date.now())}catch(e){}(void 0!==t.defaultPrevented?t.defaultPrevented:"returnValue"in t?!1===t.returnValue:t.getPreventDefault&&t.getPreventDefault())&&(e.isDefaultPrevented=pe)}return e}var Ae={},ge="onfocusin"in window,me={focus:"focusin",blur:"focusout"},ye={mouseenter:"mouseover",mouseleave:"mouseout"},ve=/^([A-Z]|returnValue$|layer[XY]$|webkitMovement[XY]$)/;function we(e){return{e:(e=(""+e).split("."))[0],ns:e.slice(1).sort().join(" ")}}function be(e,t){return e.del&&!ge&&e.e in me||!!t}function Ce(e){return ye[e]||ge&&me[e]||e}function Ee(e,t,n,r,o){var i=le(e);(t||"").split(/\s/).forEach(function(t){(function(e,t,n,r){var o,i;return(t=we(t)).ns&&(i=t.ns,o=new RegExp("(?:^| )"+i.replace(" "," .* ?")+"(?: |$)")),(Ae[le(e)]||[]).filter(function(e){return e&&(!t.e||e.e==t.e)&&(!t.ns||o.test(e.ns))&&(!n||le(e.fn)===le(n))&&(!r||e.sel==r)})})(e,t,n,r).forEach(function(t){delete Ae[i][t.i],"removeEventListener"in e&&e.removeEventListener(Ce(t.e),t.proxy,be(t,o))})})}function Me(e,t){ue(e)||(e=(t=e).type);var n=M.createEvent(Te[e]||"Events"),r=!0;if(t)for(var o in t)"bubbles"==o?r=!!t[o]:n[o]=t[o];return n.initEvent(e,r,!0),he(n)}var Ie,Te={click:"MouseEvents",mousedown:"MouseEvents",mouseup:"MouseEvents",mousemove:"MouseEvents"},ke="",Se=M.createElement("div");t=Se.style.transitionProperty;function De(e){return Ie?Ie+e:e.toLowerCase()}void 0===Se.style.transform&&E.each({Webkit:"webkit",Moz:"",O:"o"},function(e,t){if(void 0!==Se.style[e+"TransitionProperty"])return ke="-"+e.toLowerCase()+"-",Ie=t,!1}),Se=null,E.fx={off:void 0===Ie&&void 0===t,speeds:{_default:400,fast:200,slow:600},cssPrefix:ke,transitionEnd:De("TransitionEnd"),animationEnd:De("AnimationEnd")};var xe,Ne,je,Be,Le,_e,Oe,Re,Pe,Qe=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i,Fe={};xe=ke+"transform",Fe[Ne=ke+"transition-property"]=Fe[je=ke+"transition-duration"]=Fe[Le=ke+"transition-delay"]=Fe[Be=ke+"transition-timing-function"]=Fe[_e=ke+"animation-name"]=Fe[Oe=ke+"animation-duration"]=Fe[Pe=ke+"animation-delay"]=Fe[Re=ke+"animation-timing-function"]="";var Ue=function(){return this.css("display","none")};function Ye(e,t,n,r){return function(e,t,n,r,o){return"function"!=typeof t||o||(o=t,t=void 0),n={opacity:n},r&&(n.scale=r,e.css(E.fx.cssPrefix+"transform-origin","0 0")),e.animate(n,t,null,o)}(e,t,0,n,function(){Ue.call(E(this)),r&&r.call(this)})}O={css:function(e,t){if(arguments.length<2){var n,r,o=this[0];if("string"==typeof e)return o?o.style[$(e)]||getComputedStyle(o,"").getPropertyValue(e):void 0;if(Y(e))return o?(n={},r=getComputedStyle(o,""),E.each(e,function(e,t){n[t]=o.style[$(t)]||r.getPropertyValue(t)}),n):void 0}var i="";if("string"==G(e))t||0===t?i=Z(e)+":"+q(e,t):this.each(function(){this.style.removeProperty(Z(e))});else for(var a in e)e[a]||0===e[a]?i+=Z(a)+":"+q(a,e[a])+";":this.each(function(){this.style.removeProperty(Z(a))});return this.each(function(){this.style.cssText+=";"+i})},attr:function(e,t){var n;return"string"!=typeof e||1 in arguments?this.each(function(n){if(1===this.nodeType)if(V(e))for(var r in e)oe(this,r,e[r]);else oe(this,e,re(this,t,n,this.getAttribute(e)))}):0 in this&&1==this[0].nodeType&&null!=(n=this[0].getAttribute(e))?n:void 0},remove:function(){return this.each(function(){null!=this.parentNode&&this.parentNode.removeChild(this)})},append:function(){return ae(this,arguments,function(e){this.insertBefore(e,null)},!0)},removeClass:function(e){var t=[];return this.each(function(n){if("className"in this){if(void 0===e)return ie(this,"");t=ie(this),re(this,e,n,t).split(/\s+/g).forEach(function(e){t=t.replace(ee(e)," ")}),ie(this,t.trim())}})},addClass:function(e){var t=[];return e?this.each(function(n){var r;"className"in this&&(t=[],r=ie(this),re(this,e,n,r).split(/\s+/g).forEach(function(e){E(this).hasClass(e)||t.push(e)},this),t.length)&&ie(this,r+(r?" ":"")+t.join(" "))}):this},prepend:function(){return ae(this,arguments,function(e){this.insertBefore(e,this.firstChild)},!0)},after:function(){return ae(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!1)},before:function(){return ae(this,arguments,function(e){this.parentNode.insertBefore(e,this)},!1)},on:function(e,t,n,r,o){var i,a,s=this;return e&&!ue(e)?(E.each(e,function(e,r){s.on(e,t,n,r,o)}),s):(ue(t)||W(r)||!1===r||(r=n,n=t,t=void 0),void 0!==r&&!1!==n||(r=n,n=void 0),!1===r&&(r=de),s.each(function(s,c){var l,u,p,d,f,h,A,g;o&&(i=function(e){return Ee(c,e.type,r),r.apply(this,arguments)}),u=e,p=r,d=n,h=(a=(f=t)?function(e){var n=E(e.target).closest(t,c).get(0);if(n&&n!==c)return e=E.extend(function(e){var t,n={originalEvent:e};for(t in e)ve.test(t)||void 0===e[t]||(n[t]=e[t]);return he(n,e)}(e),{currentTarget:n,liveFired:c}),(i||r).apply(n,[e].concat(S.call(arguments,1)))}:a)||i,A=le(l=c),g=Ae[A]||(Ae[A]=[]),u.split(/\s/).forEach(function(e){if("ready"==e)return E(M).ready(p);var t=we(e),n=(t.fn=p,t.sel=f,t.e in ye&&(p=function(e){if(!(e=e.relatedTarget)||e!==this&&!z(this,e))return t.fn.apply(this,arguments)}),(t.del=h)||p);t.proxy=function(e){var t;if(!(e=he(e)).isImmediatePropagationStopped())return e.data=d,!1===(t=n.apply(l,null==e._args?[e]:[e].concat(e._args)))&&(e.preventDefault(),e.stopPropagation()),t},t.i=g.length,g.push(t),"addEventListener"in l&&l.addEventListener(Ce(t.e),t.proxy,be(t,void 0))})}))},off:function(e,t,n){var r=this;return e&&!ue(e)?(E.each(e,function(e,n){r.off(e,t,n)}),r):(ue(t)||W(n)||!1===n||(n=t,t=void 0),!1===n&&(n=de),r.each(function(){Ee(this,e,n,t)}))},trigger:function(e){this[0].dispatchEvent(new Me(e))},val:function(e){return 0 in arguments?(null==e&&(e=""),this.each(function(t){this.value=re(this,e,t,this.value)})):this[0]&&(this[0].multiple?E(this[0]).find("option").filter(function(){return this.selected}).pluck("value"):this[0].value)},find:function(e){var t=this;return e?"object"==typeof e?E(e).filter(function(){var e=this;return I.some.call(t,function(t){return z(t,e)})}):1==this.length?E(E.qsa(this[0],e)):this.map(function(){return E.qsa(this,e)}):E()},height:function(e){if(!e)return this[0].offsetHeight;this.css("height",e)},width:function(e){if(!e)return this[0].offsetWidth;this.css("width",e)},offset:function(e){var t;return e?this.each(function(t){var n=E(this),r=(t=re(this,e,t,n.offset()),n.offsetParent().offset());t={top:t.top-r.top,left:t.left-r.left};"static"==n.css("position")&&(t.position="relative"),n.css(t)}):this.length?M.documentElement===this[0]||z(M.documentElement,this[0])?{left:(t=this[0].getBoundingClientRect()).left+window.pageXOffset,top:t.top+window.pageYOffset,width:Math.round(t.width),height:Math.round(t.height)}:{top:0,left:0}:null},scrollTop:function(e){var t;if(this.length)return t="scrollTop"in this[0],void 0===e?t?this[0].scrollTop:(H(this[0])?this[0]:this[0].defaultView).pageYOffset:this.each(t?function(){this.scrollTop=e}:function(){this.scrollTo(this.scrollX,e)})},hasClass:function(e){return!!e&&I.some.call(this,function(e){return this.test(ie(e))},ee(e))},isShow:function(){return"block"===this.css("display")},show:function(e,t){return function(){return this.each(function(){"none"==this.style.display&&(this.style.display=""),"none"==getComputedStyle(this,"").getPropertyValue("display")&&(this.style.display=defaultDisplay(this.nodeName))})}.call(this),void 0===e?e=0:this.css("opacity",0),We(this,e,1,t)},animate:function(e,t,n,r,o){return W(t)&&(r=t,t=n=void 0),W(n)&&(r=n,n=void 0),K(t)&&(n=t.easing,r=t.complete,o=t.delay,t=t.duration),t=t&&("number"==typeof t?t:E.fx.speeds[t]||E.fx.speeds._default)/1e3,o=o&&parseFloat(o)/1e3,this.anim(e,t,n,r,o)},anim:function(e,t,n,r,o){var i,a,s,c={},l="",u=this,p=E.fx.transitionEnd,d=!1;if(void 0===t&&(t=E.fx.speeds._default/1e3),void 0===o&&(o=0),E.fx.off&&(t=0),"string"==typeof e)c[_e]=e,c[Oe]=t+"s",c[Pe]=o+"s",c[Re]=n||"linear",p=E.fx.animationEnd;else{for(i in a=[],e)Qe.test(i)?l+=i+"("+e[i]+") ":(c[i]=e[i],a.push(i.replace(/([A-Z])/g,"-$1").toLowerCase()));l&&(c[xe]=l,a.push(xe)),0<t&&"object"==typeof e&&(c[Ne]=a.join(", "),c[je]=t+"s",c[Le]=o+"s",c[Be]=n||"linear")}return s=function(e){if(void 0!==e){if(e.target!==e.currentTarget)return;E(e.target).off(p,s)}else E(this).off(p,s);d=!0,E(this).css(Fe),r&&r.call(this)},0<t&&(this.on(p,s),setTimeout(function(){d||s.call(u)},1e3*(t+o)+25)),this.size()&&this.get(0).clientLeft,this.css(c),t<=0&&setTimeout(function(){u.each(function(){s.call(this)})},0),this},hide:function(e,t){return void 0===e?Ge.call(this):function(e,t,n){return We(e,t,0,function(){Ge.call(E(this)),n&&n.call(this)})}(this,e,t)},closest:function(e,t){var n=[],r="object"==typeof e&&E(e);return this.each(function(o,i){for(;i&&!(r?0<=r.indexOf(i):E.matches(i,e));)i=i!==t&&!J(i)&&i.parentNode;i&&n.indexOf(i)<0&&n.push(i)}),E(n)},parent:function(e){return ne((t=this.pluck("parentNode"),k.call(t,function(e,n){return t.indexOf(e)==n})),e);var t},parents:function(e){for(var t=[],n=this;0<n.length;)n=E.map(n,function(e){if((e=e.parentNode)&&!J(e)&&t.indexOf(e)<0)return t.push(e),e});return ne(t,e)},Event:Me,removeAttr:function(e){return this.each(function(){1===this.nodeType&&e.split(" ").forEach(function(e){oe(this,e)},this)})},blur:function(){this[0]&&this[0].blur()},focus:function(){this[0].focus()},html:function(e){return 0 in arguments?this.each(function(t){var n=this.innerHTML;E(this).empty().append(re(this,e,t,n))}):0 in this?this[0].innerHTML:null},empty:function(){return this.each(function(){this.innerHTML=""})},filter:function(e){return W(e)?this.not(this.not(e)):E(k.call(this,function(t){return E.matches(t,e)}))},text:function(e){return 0 in arguments?this.each(function(t){t=re(this,e,t,this.textContent),this.textContent=null==t?"":""+t}):0 in this?this.pluck("textContent").join(""):null},fadeOut:function(e,t){return Ye(this,e,null,t)},index:function(e){return e?this.indexOf(E(e)[0]):this.parent().children().indexOf(this[0])},position:function(){var e,t,n,r;if(this.length)return e=this[0],n=this.offsetParent(),r=L.test(n[0].nodeName)?{top:0,left:0}:n.offset(),"fixed"===E(e).css("position")?t=e.getBoundingClientRect():(t=this.offset(),r.top+=parseFloat(E(n[0]).css("border-top-width"))||0,r.left+=parseFloat(E(n[0]).css("border-left-width"))||0),{top:t.top-r.top-parseFloat(E(e).css("margin-top"))||0,left:t.left-r.left-parseFloat(E(e).css("margin-left"))||0}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||M.body;e&&!L.test(e.nodeName)&&"static"==E(e).css("position");)e=e.offsetParent;return e})}};var ze=(E.extend({isArray:Y,trim:function(e){return null==e?"":String.prototype.trim.call(e)}}),E.fn.extend(O),E);function Ge(){return this.css("display","none")}function We(e,t,n,r){return"function"!=typeof t||r||(r=t,t=void 0),e.animate({opacity:n},t,null,r)}function He(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Je=new h,Ve=Symbol("connect"),Ke=Symbol("open"),Xe=Symbol("close"),Ze=Symbol("message"),qe=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.uri=t.url,this.opened=t.opened||{},this.closed=t.closed||{},this.message=t.message||{},this.error=t.error||{},this.reconnect=t.reconnected||{},this.type=t.type,this.elem=t.elem,this.oWs=null,this.oSaveWs=null,this.retry_time=0,this.retpyLimit=4,this.reconnecting=!1,this.reconnectTimer=null,this.heartbeatTimer=null,this[Ve]()}var t,n;return t=e,(n=[{key:"getWsObj",value:function(){return this.oWs}},{key:"stop",value:function(){clearInterval(this.heartbeatTimer),clearTimeout(this.reconnectTimer),this.heartbeatTimer=null,this.reconnectTimer=null,this.reconnecting=!0,this.oWs.onclose=null}},{key:Ve,value:function(){this.retry_time>this.retpyLimit?(!this.reconnecting&&this.reconnect&&this.reconnect(this.elem),this.stop()):(this.oWs=new NativeWebSocket(this.uri),this.oSaveWs=this.oWs,this.oWs.onopen=this[Ke].bind(this),this.oWs.onclose=this[Xe].bind(this),"canvas"==this.type&&(this.oWs.binaryType="arraybuffer"),this.oWs.onmessage=this[Ze].bind(this))}},{key:Ke,value:function(){var e=this;if(this.opened(),this.oWs!=this.oSaveWs)throw new Error("socket onopen socket check error");0<this.retry_time&&(this.retry_time=0),this.reconnecting=!0,this.heartbeatTimer=setInterval(function(){"input"==e.type||"agentLink"==e.type?Je.websocket_send(e,"ping:".concat(Date.now())):Je.websocket_send(e,"")},"input"==this.type?6e4:1e4)}},{key:Xe,value:function(e){var t=this;if(this.oWs!=this.oSaveWs)throw new Error("onclose socket check error");this.closed(e,this.elem),clearInterval(this.heartbeatTimer),this.heartbeatTimer=null,this.reconnecting?(this.reconnecting=!1,setTimeout(function(){t.oWs&&t.oWs.readyState==WebSocket.OPEN||!t.reconnecting&&t.reconnect&&t.reconnect(t.elem)},3e3)):this.reconnectTimer=setTimeout(function(){t.retry_time+=1,t[Ve]()},1e3+2e3*this.retry_time)}},{key:Ze,value:function(e){if(this.oWs!=this.oSaveWs)throw new Error("onmessage socket check error");this.message(e.data)}}])&&He(t.prototype,n),e}();function $e(e){return($e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function tt(e){return(tt=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function nt(e,t){return(nt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var rt=new(function(){var e=n,t=h;if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");function n(){if(this instanceof n)return function(e,t){if(!t||"object"!==$e(t)&&"function"!=typeof t){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t}(this,tt(n).call(this));throw new TypeError("Cannot call a class as a function")}return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&nt(e,t),e=n,(t=[{key:"getElemCoordinate",value:function(e){var t=(e=e.getBoundingClientRect()).top-document.documentElement.clientTop+document.documentElement.scrollTop,n=e.bottom,r=e.left-document.documentElement.clientLeft+document.documentElement.scrollLeft,o=e.right;return{top:t,right:o,bottom:n,left:r,width:e.width||o-r,height:e.height||n-t}}}])&&et(e.prototype,t),n}());function ot(e){return(ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function it(e){return(it=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function at(e,t){return(at=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}t=function(){var e=n,t=h;if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");function n(){if(this instanceof n)return function(e,t){if(!t||"object"!==ot(t)&&"function"!=typeof t){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t}(this,it(n).call(this));throw new TypeError("Cannot call a class as a function")}return e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&at(e,t),n}(),O=n(26);var st=n.n(O);function ct(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}n(33);var lt=new t,ut=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.elem=t.elem,this.options=t,this.width=t.width,this.height=t.height,this.error=t.error||{},this.success=t.success||{},this.rotate=!0,this.oElem=document.getElementById(this.elem),this.createDom(),this.video_player=null,this.audio_player=null,this.basePath=lt.getBasePath("saas-sdk.js"),this.createPlayer()}var t,n;return t=e,(n=[{key:"createDom",value:function(){var e;(e=((e=document.createElement("div")).setAttribute("id","playerElem"),lt.addStyleCss(e,{width:"100%",height:"100%"}),this.oElem.insertBefore(e,this.oElem.childNodes[0]),document.createElement("div"))).setAttribute("id","playerElem2"),e.setAttribute("style","display:block;width:400px; height: 400px;position:absolute; z-index:-1;left:0; top:0;opacity:0.005;"),this.oElem.appendChild(e)}},{key:"config",value:function(){return{autoplay:!0,live:!0,keyboard:!1,swf:"".concat(this.basePath,"/").concat(st.a,"?version=").concat(lt.isFlashNoCache()?(new Date).getTime():encodeURI(window.SDK_VERSION)),seedable:!1,embed:!1,debug:!1,fullscreen:!0,rotate:!1}}},{key:"createPlayer",value:function(){var e,t,n=this,r=this.config();Object.assign(flowplayer.conf,r,this.options),this.video_player&&(this.video_player.stop(!0),this.video_player.load({sources:[{type:"video/flash",src:flowplayer.conf.videoUrl,customRotate:flowplayer.conf.rotate}]})),this.video_player||(this.video_player=flowplayer("#playerElem",{keyboard:!1,clip:{provider:"rtmp",sources:[{type:"video/flash",src:flowplayer.conf.videoUrl,customRotate:flowplayer.conf.rotate}]}})),this.audio_player&&(this.audio_player.stop(),this.audio_player.load({sources:[{type:"video/flash",src:flowplayer.conf.audioUrl}]})),this.audio_player||(this.audio_player=flowplayer("#playerElem2",{rtmp:"",ratio:.001,clip:{provider:"rtmp",sources:[{type:"video/flash",src:flowplayer.conf.audioUrl}]}})),this.video_player.off("error").on("error",function(e,t,r){n.error(n.elem)}),this.audio_player.off("error").on("error",function(e,t,r){n.error(n.elem)}),this.success(),this.rotate&&((r=this.oElem.getElementsByClassName("fp-engine")[0]).classList.add("rotate_90"),e=this.oElem.getElementsByClassName("fp-player")[0],t=lt.getStyle(e,"width"),e=lt.getStyle(e,"height"),r.style.width=e,r.style.height=t,t=(parseFloat(t)-parseFloat(e))/2,r.style.left="".concat(t,"px"),r.style.top="".concat(Math.abs(t),"px"))}},{key:"startPlay",value:function(){createPlayer(player_wrap,option)}},{key:"stopPlay",value:function(e){if(e){if(audio_player&&lt.hasOwnProperty(audio_player,"mute"))try{audio_player.mute(!0)}catch(e){}audio_player&&lt.hasOwnProperty(audio_player,"stop")&&audio_player.stop(!1,!0),audio_player=null,video_player&&lt.hasOwnProperty(video_player,"stop")&&video_player.stop(),video_player=null}else video_player&&lt.hasOwnProperty(video_player,"stop")&&video_player.stop(!0),audio_player&&lt.hasOwnProperty(audio_player,"stop")&&audio_player.stop();input_ping_interval&&(clearInterval(input_ping_interval),input_ping_interval=null),lt.isWebsocketObject(input_socket)&&(lt.websocket_close(input_socket,!0),input_socket=void 0)}}])&&ct(t.prototype,n),e}();let pt=!0,dt=!0;function ft(e,t,n){return(e=e.match(t))&&e.length>=n&&parseInt(e[n],10)}function ht(e,t,n){if(e.RTCPeerConnection){const r=(e=e.RTCPeerConnection.prototype).addEventListener,o=(e.addEventListener=function(e,o){var i;return e!==t?r.apply(this,arguments):(i=(e=>{(e=n(e))&&(o.handleEvent?o.handleEvent(e):o(e))}),this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(o,i),r.apply(this,[e,i]))},e.removeEventListener);e.removeEventListener=function(e,n){var r;return e===t&&this._eventMap&&this._eventMap[t]&&this._eventMap[t].has(n)?(r=this._eventMap[t].get(n),this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,r])):o.apply(this,arguments)},Object.defineProperty(e,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}}function At(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(pt=e)?"adapter.js logging disabled":"adapter.js logging enabled"}function gt(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(dt=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function mt(){}function yt(e){return"[object Object]"===Object.prototype.toString.call(e)}function vt(e,t,n){const r=n?"outbound-rtp":"inbound-rtp",o=new Map;if(null!==t){const n=[];e.forEach(e=>{"track"===e.type&&e.trackIdentifier===t.id&&n.push(e)}),n.forEach(t=>{e.forEach(n=>{n.type===r&&n.trackId===t.id&&function e(t,n,r){n&&!r.has(n.id)&&(r.set(n.id,n),Object.keys(n).forEach(o=>{o.endsWith("Id")?e(t,t.get(n[o]),r):o.endsWith("Ids")&&n[o].forEach(n=>{e(t,t.get(n),r)})}))}(e,n,o)})})}return o}const wt=mt;function bt(e,t){const n=e&&e.navigator;if(n.mediaDevices){const e=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach(n=>{if("require"!==n&&"advanced"!==n&&"mediaSource"!==n){const r="object"==typeof e[n]?e[n]:{ideal:e[n]},o=(void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact),function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t});if(void 0!==r.ideal){t.optional=t.optional||[];let e={};"number"==typeof r.ideal?(e[o("min",n)]=r.ideal,t.optional.push(e),(e={})[o("max",n)]=r.ideal):e[o("",n)]=r.ideal,t.optional.push(e)}void 0!==r.exact&&"number"!=typeof r.exact?(t.mandatory=t.mandatory||{},t.mandatory[o("",n)]=r.exact):["min","max"].forEach(e=>{void 0!==r[e]&&(t.mandatory=t.mandatory||{},t.mandatory[o(e,n)]=r[e])})}}),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},r=function(r,o){if(!(61<=t.version)){if((r=JSON.parse(JSON.stringify(r)))&&"object"==typeof r.audio&&((i=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])})((r=JSON.parse(JSON.stringify(r))).audio,"autoGainControl","googAutoGainControl"),i(r.audio,"noiseSuppression","googNoiseSuppression"),r.audio=e(r.audio)),r&&"object"==typeof r.video){let a=r.video.facingMode;a=a&&("object"==typeof a?a:{ideal:a});var i=t.version<66;if(a&&("user"===a.exact||"environment"===a.exact||"user"===a.ideal||"environment"===a.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||i)){let t;if(delete r.video.facingMode,"environment"===a.exact||"environment"===a.ideal?t=["back","rear"]:"user"!==a.exact&&"user"!==a.ideal||(t=["front"]),t)return n.mediaDevices.enumerateDevices().then(n=>{let i=(n=n.filter(e=>"videoinput"===e.kind)).find(e=>t.some(t=>e.label.toLowerCase().includes(t)));return(i=!i&&n.length&&t.includes("back")?n[n.length-1]:i)&&(r.video.deviceId=a.exact?{exact:i.deviceId}:{ideal:i.deviceId}),r.video=e(r.video),wt("chrome: "+JSON.stringify(r)),o(r)})}r.video=e(r.video)}wt("chrome: "+JSON.stringify(r))}return o(r)},o=function(e){return 64<=t.version?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(n.getUserMedia=function(e,t,i){r(e,e=>{n.webkitGetUserMedia(e,t,e=>{i&&i(o(e))})})}.bind(n),n.mediaDevices.getUserMedia){const e=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(t){return r(t,t=>e(t).then(e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(e=>{e.stop()}),new DOMException("","NotFoundError");return e},e=>Promise.reject(o(e))))}}}}function Ct(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&"function"==typeof t&&(e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then(t=>{var r=n.video&&n.video.width,o=n.video&&n.video.height,i=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:i||3}},r&&(n.video.mandatory.maxWidth=r),o&&(n.video.mandatory.maxHeight=o),e.navigator.mediaDevices.getUserMedia(n)})})}function Et(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Mt(e){if("object"!=typeof e||!e.RTCPeerConnection||"ontrack"in e.RTCPeerConnection.prototype)ht(e,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e));else{Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=(t=>{t.stream.addEventListener("addtrack",n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.track.id):{track:n.track};var o=new Event("track");o.track=n.track,o.receiver=r,o.transceiver={receiver:r},o.streams=[t.stream],this.dispatchEvent(o)}),t.stream.getTracks().forEach(n=>{let r;r=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find(e=>e.track&&e.track.id===n.id):{track:n};var o=new Event("track");o.track=n,o.receiver=r,o.transceiver={receiver:r},o.streams=[t.stream],this.dispatchEvent(o)})}),this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}}function It(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack,r=(e.RTCPeerConnection.prototype.addTrack=function(e,r){let o=n.apply(this,arguments);return o||(o=t(this,e),this._senders.push(o)),o},e.RTCPeerConnection.prototype.removeTrack);e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments),-1!==(e=this._senders.indexOf(e))&&this._senders.splice(e,1)}}const n=e.RTCPeerConnection.prototype.addStream,r=(e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach(e=>{this._senders.push(t(this,e))})},e.RTCPeerConnection.prototype.removeStream);e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],r.apply(this,[e]),e.getTracks().forEach(e=>{var t=this._senders.find(t=>t.track===e);t&&this._senders.splice(this._senders.indexOf(t),1)})}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Tt(e){if(e.RTCPeerConnection){const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,r]=arguments;if(0<arguments.length&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const o=function(e){const t={};return e.result().forEach(e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach(t=>{n[t]=e.stat(t)}),t[n.id]=n}),t},i=function(e){return new Map(Object.keys(e).map(t=>[t,e[t]]))};return 2<=arguments.length?t.apply(this,[function(e){n(i(o(e)))},e]):new Promise((e,n)=>{t.apply(this,[function(t){e(i(o(t)))},n])}).then(n,r)}}}function kt(e){if("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver){if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders,n=(t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),e.RTCPeerConnection.prototype.addTrack);n&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>vt(t,e.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),ht(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then(t=>vt(t,e.track,!1))}}if("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype){const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(0<arguments.length&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,r;return this.getSenders().forEach(n=>{n.track===e&&(t?r=!0:t=n)}),this.getReceivers().forEach(t=>(t.track===e&&(n?r=!0:n=t),t.track===e)),r||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}}}function St(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(e=>this._shimmedLocalStreams[e][0])};const t=e.RTCPeerConnection.prototype.addTrack,n=(e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var r=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(r)&&this._shimmedLocalStreams[n.id].push(r):this._shimmedLocalStreams[n.id]=[n,r],r},e.RTCPeerConnection.prototype.addStream),r=(e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")});const t=this.getSenders();n.apply(this,arguments);var r=this.getSenders().filter(e=>-1===t.indexOf(e));this._shimmedLocalStreams[e.id]=[e].concat(r)},e.RTCPeerConnection.prototype.removeStream),o=(e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],r.apply(this,arguments)},e.RTCPeerConnection.prototype.removeTrack);e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach(t=>{var n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]}),o.apply(this,arguments)}}function Dt(e,t){if(e.RTCPeerConnection){if(e.RTCPeerConnection.prototype.addTrack&&65<=t.version)return St(e);const r=e.RTCPeerConnection.prototype.getLocalStreams,o=(e.RTCPeerConnection.prototype.getLocalStreams=function(){var e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(e=>this._reverseStreams[e.id])},e.RTCPeerConnection.prototype.addStream),i=(e.RTCPeerConnection.prototype.addStream=function(t){var n;this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach(e=>{if(this.getSenders().find(t=>t.track===e))throw new DOMException("Track already exists.","InvalidAccessError")}),this._reverseStreams[t.id]||(n=new e.MediaStream(t.getTracks()),this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n),o.apply(this,[t])},e.RTCPeerConnection.prototype.removeStream),a=(e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},i.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[(this._streams[e.id]||e).id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var r;if(1!==(r=[].slice.call(arguments,1)).length||!r[0].getTracks().find(e=>e===t))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(e=>e.track===t))throw new DOMException("Track already exists.","InvalidAccessError");return this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},(r=this._streams[n.id])?(r.addTrack(t),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))})):(r=new e.MediaStream([t]),this._streams[n.id]=r,this._reverseStreams[r.id]=n,this.addStream(r)),this.getSenders().find(e=>e.track===t)},["createOffer","createAnswer"].forEach(function(t){const r=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?r.apply(this,[t=>{t=n(this,t),e[0].apply(null,[t])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):r.apply(this,arguments).then(e=>n(this,e))}}[t]}),e.RTCPeerConnection.prototype.setLocalDescription),s=(e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type&&(arguments[0]=function(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{t=e._reverseStreams[t];var r=e._streams[t.id];n=n.replace(new RegExp(t.id,"g"),r.id)}),new RTCSessionDescription({type:t.type,sdp:n})}(this,arguments[0])),a.apply(this,arguments)},Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription"));function n(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach(t=>{t=e._reverseStreams[t];var r=e._streams[t.id];n=n.replace(new RegExp(r.id,"g"),t.id)}),new RTCSessionDescription({type:t.type,sdp:n})}Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){var e=s.get.apply(this);return""===e.type?e:n(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach(n=>{this._streams[n].getTracks().find(t=>e.track===t)&&(t=this._streams[n])}),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}}function xt(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}}[t]})}function Nt(e,t){ht(e,"negotiationneeded",e=>{var n=e.target;if(!(t.version<72||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e})}O=n(27);var jt=n.n(O);function Bt(e){const t=(e=e&&e.navigator).mediaDevices.getUserMedia.bind(e.mediaDevices);e.mediaDevices.getUserMedia=function(e){return t(e).catch(e=>Promise.reject({name:{PermissionDeniedError:"NotAllowedError"}[(e=e).name]||e.name,message:e.message,constraint:e.constraint,toString(){return this.name}}))}}function Lt(e){"getDisplayMedia"in e.navigator&&(!e.navigator.mediaDevices||e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||(e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)))}function _t(e,t){if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){const t=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set(e){t.set.call(this,e);var n=new Event("enabled");n.enabled=e,this.dispatchEvent(n)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}}),e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);const n=jt()(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=function(e){let t=!1;return(e=JSON.parse(JSON.stringify(e))).filter(e=>{if(e&&(e.urls||e.url)){let r=e.urls||e.url;e.url&&e.urls;var n="string"==typeof r;return r=(r=n?[r]:r).filter(e=>0!==e.indexOf("stun:")&&((e=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp"))&&!t?t=!0:e&&!t)),delete e.url,e.urls=n?r[0]:r,!!r.length}})}(e.iceServers,t.version),e.iceServers),new n(e)},e.RTCPeerConnection.prototype=n.prototype}function Ot(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)}function Rt(e,t){const n=e&&e.navigator;if(e=e&&e.MediaStreamTrack,n.getUserMedia=function(e,t,r){n.mediaDevices.getUserMedia(e).then(t,r)},!(55<t.version&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},r=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(e){return"object"==typeof e&&"object"==typeof e.audio&&(e=JSON.parse(JSON.stringify(e)),t(e.audio,"autoGainControl","mozAutoGainControl"),t(e.audio,"noiseSuppression","mozNoiseSuppression")),r(e)},e&&e.prototype.getSettings){const n=e.prototype.getSettings;e.prototype.getSettings=function(){var e=n.apply(this,arguments);return t(e,"mozAutoGainControl","autoGainControl"),t(e,"mozNoiseSuppression","noiseSuppression"),e}}if(e&&e.prototype.applyConstraints){const n=e.prototype.applyConstraints;e.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"==typeof e&&(e=JSON.parse(JSON.stringify(e)),t(e,"autoGainControl","mozAutoGainControl"),t(e,"noiseSuppression","mozNoiseSuppression")),n.apply(this,[e])}}}}function Pt(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){return n&&n.video?(!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)):((n=new DOMException("getDisplayMedia without video constraints is undefined")).name="NotFoundError",n.code=8,Promise.reject(n))})}function Qt(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Ft(e,t){if("object"==typeof e&&(e.RTCPeerConnection||e.mozRTCPeerConnection)){!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){const n=e.RTCPeerConnection.prototype[t];e.RTCPeerConnection.prototype[t]={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}}[t]});const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},r=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,o,i]=arguments;return r.apply(this,[e||null]).then(e=>{if(t.version<53&&!o)try{e.forEach(e=>{e.type=n[e.type]||e.type})}catch(t){if("TypeError"!==t.name)throw t;e.forEach((t,r)=>{e.set(r,Object.assign({},t,{type:n[t.type]||t.type}))})}return e}).then(o,i)}}}function Ut(e){if("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders,n=(t&&(e.RTCPeerConnection.prototype.getSenders=function(){var e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),e.RTCPeerConnection.prototype.addTrack);n&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}}function Yt(e){if("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&!(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){var e=t.apply(this,[]);return e.forEach(e=>e._pc=this),e}),ht(e,"track",e=>(e.receiver._pc=e.srcElement,e)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}}function zt(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this.getSenders().forEach(t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)})})}function Gt(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function Wt(e){if("object"==typeof e&&e.RTCPeerConnection){const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];var e=arguments[1],n=e&&"sendEncodings"in e,r=(n&&e.sendEncodings.forEach(e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(1<=parseFloat(e.scaleResolutionDownBy)))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(0<=parseFloat(e.maxFramerate)))throw new RangeError("max_framerate must be >= 0.0")}),t.apply(this,arguments));if(n){const t=r.sender;"encodings"in(n=t.getParameters())&&(1!==n.encodings.length||0!==Object.keys(n.encodings[0]).length)||(n.encodings=e.sendEncodings,t.sendEncodings=e.sendEncodings,this.setParametersPromises.push(t.setParameters(n).then(()=>{delete t.sendEncodings}).catch(()=>{delete t.sendEncodings})))}return r})}}function Ht(e){if("object"==typeof e&&e.RTCRtpSender){const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){var e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}}function Jt(e){if("object"==typeof e&&e.RTCPeerConnection){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}}function Vt(e){if("object"==typeof e&&e.RTCPeerConnection){const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}}function Kt(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach(n=>t.call(this,n,e)),e.getVideoTracks().forEach(n=>t.call(this,n,e))},e.RTCPeerConnection.prototype.addTrack=function(e,...n){return n&&n.forEach(e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);var t=this._localStreams.indexOf(e);if(-1!==t){this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach(e=>{n.includes(e.track)&&this.removeTrack(e)})}})}}function Xt(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams||[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=(e=>{e.streams.forEach(e=>{var t;this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e)||(this._remoteStreams.push(e),(t=new Event("addstream")).stream=e,this.dispatchEvent(t))})}))}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach(t=>{var n;e._remoteStreams||(e._remoteStreams=[]),0<=e._remoteStreams.indexOf(t)||(e._remoteStreams.push(t),(n=new Event("addstream")).stream=t,e.dispatchEvent(n))})}),t.apply(e,arguments)}}}function Zt(e){if("object"==typeof e&&e.RTCPeerConnection){const t=(e=e.RTCPeerConnection.prototype).createOffer,n=e.createAnswer,r=e.setLocalDescription,o=e.setRemoteDescription,i=e.addIceCandidate;e.createOffer=function(e,n){var r=2<=arguments.length?arguments[2]:e;r=t.apply(this,[r]);return n?(r.then(e,n),Promise.resolve()):r},e.createAnswer=function(e,t){var r=2<=arguments.length?arguments[2]:e;r=n.apply(this,[r]);return t?(r.then(e,t),Promise.resolve()):r},e.setLocalDescription=function(e,t,n){return e=r.apply(this,[e]),n?(e.then(t,n),Promise.resolve()):e},e.setRemoteDescription=function(e,t,n){return e=o.apply(this,[e]),n?(e.then(t,n),Promise.resolve()):e},e.addIceCandidate=function(e,t,n){return e=i.apply(this,[e]),n?(e.then(t,n),Promise.resolve()):e}}}function qt(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const n=(e=t.mediaDevices).getUserMedia.bind(e);t.mediaDevices.getUserMedia=(e=>n($t(e)))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,r){t.mediaDevices.getUserMedia(e).then(n,r)}.bind(t))}function $t(e){return e&&void 0!==e.video?Object.assign({},e,{video:function e(t){return yt(t)?Object.keys(t).reduce(function(n,r){var o=(i=yt(t[r]))?e(t[r]):t[r],i=i&&!Object.keys(o).length;return void 0===o||i?n:Object.assign(n,{[r]:o})},{}):t}(e.video)}):e}function en(e){if(e.RTCPeerConnection){const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){var r=[];for(let t=0;t<e.iceServers.length;t++){let n=e.iceServers[t];!n.hasOwnProperty("urls")&&n.hasOwnProperty("url")?((n=JSON.parse(JSON.stringify(n))).urls=n.url,delete n.url,r.push(n)):r.push(e.iceServers[t])}e.iceServers=r}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}}function tn(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function nn(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){var n;return e&&(void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio),n=this.getTransceivers().find(e=>"audio"===e.receiver.track.kind),!1===e.offerToReceiveAudio&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveAudio||n||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo),n=this.getTransceivers().find(e=>"video"===e.receiver.track.kind),!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video")),t.apply(this,arguments)}}function rn(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}O=n(1);var on=n.n(O);function an(e){if(e.RTCIceCandidate&&!(e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)){const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){var n=new t(e),r=on.a.parseCandidate(e.candidate);const o=Object.assign(n,r);return o.toJSON=function(){return{candidate:o.candidate,sdpMid:o.sdpMid,sdpMLineIndex:o.sdpMLineIndex,usernameFragment:o.usernameFragment}},o}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,ht(e,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t))}}function sn(e,t){if(e.RTCPeerConnection){"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var e;if(this._sctp=null,"chrome"===t.browser&&76<=t.version&&"plan-b"===(e=this.getConfiguration().sdpSemantics)&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0}),(e=arguments[0])&&e.sdp&&((e=on.a.splitSections(e.sdp)).shift(),e.some(e=>(e=on.a.parseMLine(e))&&"application"===e.kind&&-1!==e.protocol.indexOf("SCTP")))){var r=function(e){let n=65536;return"firefox"===t.browser?t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637:n}(o=null===(o=(o=arguments[0]).sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/))||o.length<2||(o=parseInt(o[1],10))!=o?-1:o),o=function(e,n){let r=65536;return"firefox"===t.browser&&57===t.version&&(r=65535),0<(e=on.a.matchPrefix(e.sdp,"a=max-message-size:")).length?r=parseInt(e[0].substr(19),10):"firefox"===t.browser&&-1!==n&&(r=2147483637),r}(arguments[0],o);let e;e=0===r&&0===o?Number.POSITIVE_INFINITY:0===r||0===o?Math.max(r,o):Math.min(r,o),r={},Object.defineProperty(r,"maxMessageSize",{get:()=>e}),this._sctp=r}return n.apply(this,arguments)}}}function cn(e){if(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype){const n=e.RTCPeerConnection.prototype.createDataChannel;function t(e,t){const n=e.send;e.send=function(){var r=(r=arguments[0]).length||r.size||r.byteLength;if("open"===e.readyState&&t.sctp&&r>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}e.RTCPeerConnection.prototype.createDataChannel=function(){var e=n.apply(this,arguments);return t(e,this),e},ht(e,"datachannel",e=>(t(e.channel,e.target),e))}}function ln(e){if(e.RTCPeerConnection&&!("connectionState"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=(e=>{var t,n=e.target;return n._lastConnectionState!==n.connectionState&&(n._lastConnectionState=n.connectionState,t=new Event("connectionstatechange",e),n.dispatchEvent(t)),e}),this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}})}}function un(e,t){if(e.RTCPeerConnection&&!("chrome"===t.browser&&71<=t.version||"safari"===t.browser&&605<=t.version)){const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(n){var r;return n&&n.sdp&&-1!==n.sdp.indexOf("\na=extmap-allow-mixed")&&(r=n.sdp.split("\n").filter(e=>"a=extmap-allow-mixed"!==e.trim()).join("\n"),e.RTCSessionDescription&&n instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:n.type,sdp:r}):n.sdp=r),t.apply(this,arguments)}}}function pn(e,t){if(e.RTCPeerConnection&&e.RTCPeerConnection.prototype){const n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}}var dn=function({window:e},t={shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0}){var n=mt,c=function(e){var t={browser:null,version:null};if(void 0!==e&&e.navigator){var n=e.navigator;if(n.mozGetUserMedia)t.browser="firefox",t.version=ft(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)t.browser="chrome",t.version=ft(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(n.mediaDevices&&n.userAgent.match(/Edge\/(\d+).(\d+)$/))t.browser="edge",t.version=ft(n.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=ft(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}}else t.browser="Not a browser.";return t}(e),l={browserDetails:c,commonShim:s,extractVersion:ft,disableLog:At,disableWarnings:gt};switch(c.browser){case"chrome":if(!t.shimChrome)return n("Chrome shim is not included in this adapter release."),l;if(null===c.version)return n("Chrome shim can not determine version, not shimming."),l;n("adapter.js shimming chrome."),l.browserShim=r,pn(e,c),bt(e,c),Et(e),xt(e,c),Mt(e),Dt(e,c),It(e),Tt(e),kt(e),Nt(e,c),an(e),ln(e),sn(e,c),cn(e),un(e,c);break;case"firefox":if(!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),l;n("adapter.js shimming firefox."),l.browserShim=i,pn(e,c),Rt(e,c),Ft(e,c),Qt(e),zt(e),Ut(e),Yt(e),Gt(e),Wt(e),Ht(e),Jt(e),Vt(e),an(e),ln(e),sn(e,c),cn(e);break;case"edge":if(!t.shimEdge)return n("MS edge shim is not included in this adapter release."),l;n("adapter.js shimming edge."),l.browserShim=o,Bt(e),Lt(e),_t(e,c),Ot(e),sn(e,c),cn(e);break;case"safari":if(!t.shimSafari)return n("Safari shim is not included in this adapter release."),l;n("adapter.js shimming safari."),l.browserShim=a,pn(e,c),en(e),nn(e),Zt(e),Kt(e),Xt(e),tn(e),qt(e),rn(e),an(e),sn(e,c),cn(e),un(e,c);break;default:n("Unsupported browser!")}return l}({window:"undefined"==typeof window?void 0:window}),fn=(O=n(28),n.n(O));function hn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var An=new t,gn=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this._io=null,this._pc=null,this._roomId=null,this._heartTimer=null,this._statusForDisToCon=null,this._initSuccessStatus=null,this._webRTCStatsJSON=null,this._remoteVideoStream=null,this._remoteAudioStream=null,this.oVideoLabel=null,this.oAudioLabel=null,this.audioPlayStatus=null,this.rotate=!0,this._webRTCStatsJSON={},this.remoteCandidatePayload=null,this.remoteOfferPayload=null,this.callback=t}var t,n;return t=e,(n=[{key:"create",value:function(e){var t,n=this,r=(e=document.getElementById(e),Math.floor((new Date).getTime()+1e8*Math.random())),o="canvas".concat(r).concat(dn.browserDetails.version||0),i="video".concat(r);r="audio".concat(r);document.querySelector(".HMplayerBox")||(t=document.createElement("div"),An.addStyleCss(t,{position:"relative",width:"100%",height:"100%"}),t.setAttribute("class","HMplayerBox"),e.appendChild(t)),document.querySelector(".HMplayerBox").innerHTML='\n            <canvas id="'.concat(o,'" poster="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7" style="width: 100%; height: 100%; position:absolute; left: 0; top: 0; z-index: 10;"></canvas>\n            <video id="').concat(i,'" muted autoplay playsinline webkit-playsinline x5-playsinline x5-video-player-type="h5" x5-video-orientation="landscape" x-webkit-airplay="allow" preload style="width: 100%; height: 100%;"></video>\n            <audio id="').concat(r,'" playsinline webkit-playsinline style="position:absolute; z-index:-1; width:50px; height:10px;"></audio>\n        '),this.rotate&&((t=document.querySelector(".HMplayerBox")).classList.add("rotate_90"),o=An.getStyle(e,"width"),e=An.getStyle(e,"height"),t.style.width=e,t.style.height=o,o=(parseFloat(o)-parseFloat(e))/2,t.style.left="".concat(o,"px"),t.style.top="".concat(Math.abs(o),"px")),this.oVideoLabel=document.getElementById(i),this.oAudioLabel=document.getElementById(r),this.oVideoLabel.addEventListener("playing",function(e){n.callback("webrtc_decode_first_frame_end","playing")}),this.oVideoLabel.addEventListener("loadstart",function(e){n.callback("webrtc_decode_first_frame_end","loadstart")}),this.oVideoLabel.addEventListener("loadedmetadata",function(e){n.callback("webrtc_decode_first_frame_end","loadedmetadata")}),this.oVideoLabel.addEventListener("loadeddata",function(e){n.callback("rtc_connect_vibf","loadeddata")}),this.oVideoLabel.addEventListener("canplay",function(e){n.callback("webrtc_decode_first_frame_end","canplay")})}},{key:"connect",value:function(e,t){var n=e.roomId,r=(e=e.signalUrl,void 0===(r=t.timeout)?2e3:r),o=void 0===(o=t.reconnectionDelayMax)?1e3:o;t=void 0===(t=t.reconnectionAttempts)?5:t;this._roomId=n,this._addPeerConnect(),this.callback("begin_connect",e),this._io&&this._io.connected?this._socketSendMessage("join"):this._socketConnect(e,{timeout:r,reconnectionDelayMax:o,reconnectionAttempts:t})}},{key:"checkOfferAndCandidate",value:function(){this.remoteCandidatePayload&&this.remoteOfferPayload&&this._remoteOffer(this.remoteOfferPayload)}},{key:"_socketConnect",value:function(e,t){var n=this;t.transports=["websocket"],this._io=fn()(e,t),this._io.on("connect",function(){n.callback("connect");var e=n._pc&&n._pc.connectionState?n._pc.connectionState:n._pc&&n._pc.iceConnectionState?n._pc.iceConnectionState:"";n._pc&&"connected"!=e&&n._socketSendMessage("join"),n._heartTimer&&clearInterval(n._heartTimer),n._heartTimer=setInterval(function(){clearInterval(n._heartTimer),n._socketSendMessage("heartbeat",{time:(new Date).getTime()})},1e4)}),this._io.on("onMessage",function(e){"offer"===e.type?(n.remoteOfferPayload=e.payload,n.checkOfferAndCandidate()):"candidate"===e.type&&(n.remoteCandidatePayload=e.payload,n.checkOfferAndCandidate())}),this._io.on("disconnect",function(){n.remoteCandidatePayload=null,n.remoteOfferPayload=null,n.callback("disconnect")}),this._io.on("reconnectfailed",function(){n.callback("reconnectFailed")}),this._io.on("reconnect",function(){n.callback("reconnectSuccess")}),this._io.on("reconnecterror",function(){n.callback("reconnectError")}),this._io.on("connecttimeout",function(){n.callback("connectTimeout")})}},{key:"_socketSendMessage",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};"join"==e?this.callback("send_join"):"answer"==e&&this.callback("send_answer"),this._io.emit(e,{to:this._roomId,type:e,payload:t})}},{key:"_remoteOffer",value:function(e){var t=this;this.callback("receive_offer",JSON.stringify(e)),"new"!==this._pc.iceConnectionState&&/firefox/gi.test(navigator.userAgent)?(this._addPeerConnect(),this._socketSendMessage("join")):this._pc.setRemoteDescription(new RTCSessionDescription({type:e.type,sdp:e.sdp})).then(function(){t.remoteOfferPayload=null,t.callback("setRemoteDescription_success"),t._creatAnswer()}).catch(function(e){e="error_name:".concat(e.name,"; message:").concat(e.message),t.callback("setRemoteDescription_failed",e)})}},{key:"_removeNoneed",value:function(e,t){for(var n=t,r=function(t){t=/\d+/gi.exec(e[t]);for(var r=new RegExp("(rtpmap|rtcp-fb|fmtp):"+t),o=(n=n.filter(function(e){return!r.test(e)}),new RegExp("apt="+t)),i=n.filter(function(e){return o.test(e)}),a=0;a<i.length;a++)!function(e){e=/\d+/gi.exec(i[e]);var t=new RegExp("(rtpmap|fmtp):"+e);n=n.filter(function(e){return!t.test(e)})}(a)},o=0;o<e.length;o++)r(o);return n}},{key:"_creatAnswer",value:function(){var e=this;this._pc.createAnswer().then(function(t){t.sdp=t.sdp.replace("useinbandfec=1","useinbandfec=1;stereo=1"),e._pc.setLocalDescription(t).then(function(){e._remoteCandidate(e.remoteCandidatePayload),e.callback("setLocalDescription_success",t.sdp.split("\r\n").join(",")),e._socketSendMessage("answer",t)}).catch(function(t){t="error_name:".concat(t.name,"; message:").concat(t.message),e.callback("setLocalDescription_failed",t)})}).catch(function(t){t="error_name:".concat(t.name,"; message:").concat(t.message),e.callback("createAnswer_failed",t)})}},{key:"_remoteCandidate",value:function(e){var t=this;this.callback("receive_candidate",JSON.stringify(e)),this._pc.addIceCandidate(new RTCIceCandidate({sdpMLineIndex:e.label,sdpMid:e.id,candidate:e.candidate})).then(function(){t.callback("addIceCandidate_success")}).catch(function(e){e="error_name:".concat(e.name,"; message:").concat(e.message),t.callback("addIceCandidate_failed",e)})}},{key:"_addPeerConnect",value:function(){this._pc&&(this._pc.close(),this._pc.ontrack=null,this._pc.onicecandidate=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null);try{var e=window.RTCPeerConnection||window.mozRTCPeerConnection||window.webkitRTCPeerConnection;this._pc=new e(null)}catch(t){throw e={code:0,status:{socket:0,video:0,audio:0},errorCode:"HMWRTCSDK010"},this.callback("rtc_create_error",e),t}this._pcOnchangeManage()}},{key:"_pcOnchangeManage",value:function(){var e=this;this._pc.ontrack=function(t){"audio"==t.track.kind?(e.oAudioLabel.srcObject=t.streams[0],e._remoteAudioStream=t.streams[0]):(e.oVideoLabel.srcObject=t.streams[0],e._remoteVideoStream=t.streams[0]),e.callback("ontrack",t.track.kind),e._pc.id=(new Date).getTime()},this._pc.onicecandidate=function(t){t=t.candidate&&t.candidate.candidate,e.callback("onIceCandidate_success",t)},this._pc.onconnectionstatechange=function(){e.callback("connectionState",e._pc.connectionState),/firefox/gi.test(navigator.userAgent)||/opera/gi.test(navigator.userAgent)||e._connectionStateChange(e._pc.connectionState)},this._pc.oniceconnectionstatechange=function(){e.callback("iceConnectionState",e._pc.iceConnectionState),(/firefox/gi.test(navigator.userAgent)||/opera/gi.test(navigator.userAgent))&&e._connectionStateChange(e._pc.iceConnectionState)},this._pc.onicegatheringstatechange=function(){},this._pc.onsignalingstatechange=function(e){},this._pc.ondatachannel=function(t){e.callback("rtc_data_channel",t)}}},{key:"_connectionStateChange",value:function(e){switch(e){case"connected":this._successCallbackToSDK();break;case"disconnected":this._statusForDisToCon=!0;break;case"failed":this._statusForDisToCon=!1,this._failedCallbackToSDK()}}},{key:"_successCallbackToSDK",value:function(){this.callback("peer_connect_success"),this._statusForDisToCon?(this._statusForDisToCon=!1,this.callback("self_connect_success")):this.callback("other_connect_success")}},{key:"_failedCallbackToSDK",value:function(){var e={code:0,status:{socket:this._io.connected?1:0,video:0,audio:0},errorCode:this._io.connected?"HMWRTCSDK003":"HMWRTCSDK001,HMWRTCSDK003"};this.callback("rtc_connect_result",e),this._io.close()}},{key:"disconnect",value:function(){this._remoteVideoStream&&this._remoteVideoStream.getTracks().forEach(function(e){e.stop()}),this._remoteAudioStream&&this._remoteAudioStream.getTracks().forEach(function(e){e.stop()}),this.oAudioLabel&&(this.oAudioLabel.srcObject=null),this.oVideoLabel&&(this.oVideoLabel.srcObject=null),this.remoteCandidatePayload=null,this.remoteOfferPayload=null,this._pc&&(this._pc.close(),this._pc.ontrack=null,this._pc.onicecandidate=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null),this._io&&this._io.close(),this._heartTimer&&clearInterval(this._heartTimer)}},{key:"enableAudio",value:function(e){e?this.oAudioLabel.play():this.oAudioLabel.pause()}}])&&hn(t.prototype,n),e}();function mn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var yn=new h,vn=12903,wn=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.accessKeyId=t.accessKeyId,this.loggerUrl=t.countlyUrl,this.appKey=t.appKey,this.cache=[],this.cid=t.masterId,this.pkgName=t.packageName}var t,n;return t=e,(n=[{key:"send",value:function(e,t){var n=Date.now(),r=(n=String(Math.floor(n)),(new Date).getHours()),o=(new Date).getDay();e={key:"HMCPWebEvent",count:1,timestamp:n,hour:r,dow:o,segmentation:{key:3,timeStamp:n,eventID:e,cloudID:this.cid||"",eventData:t||"",accessKeyId:this.accessKeyId,networkType:"WIFI",netStatus:"WIFI",extension:{sdkVersion:SDK_VERSION,pkgName:this.pkgName}}},t=[];this.loggerUrl?(this.cache.length?(t=[].concat(function(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}(this.cache),[e]),this.cache=[]):t=[e],t="events="+JSON.stringify(t)+"&app_key="+encodeURIComponent(this.appKey)+"&device_id="+encodeURIComponent("31234567")+"&sdk_name="+encodeURIComponent("saas_group_web")+"&sdk_version="+encodeURIComponent(SDK_VERSION)+"&hour="+r+"&dow="+o+"&timestamp="+n,r=yn.json2url({c:e.segmentation.cloudID||"",e:e.segmentation.eventID,t:e.segmentation.cloudID||"",s:e.segmentation.key,u:e.segmentation.cloudID||""}),C({url:"".concat(this.loggerUrl,"?").concat(r),data:t,method:"POST",contentType:"application/x-www-form-urlencoded",timeout:10,done:function(e){}})):this.cache.push(e)}}])&&mn(t.prototype,n),e}();function bn(e,t){return{phoneId:e=0<arguments.length&&void 0!==e?e:"",message:t=1<arguments.length&&void 0!==t?t:"设备停止"}}function Cn(e,t){return{phoneId:e=0<arguments.length&&void 0!==e?e:"",message:t=1<arguments.length&&void 0!==t?t:"设备重连中"}}function En(e,t,n){return{phoneId:e=0<arguments.length&&void 0!==e?e:"",message:t=1<arguments.length&&void 0!==t?t:"",code:n=2<arguments.length&&void 0!==n?n:""}}var Mn="https://saas-rel.haimawan.com",In={getPhoneInfo:101,refresh:102,reduction:103,increment:104,reconnect:105},Tn=(O=n(29),n.n(O));function kn(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function Sn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var Dn=new h,xn=Symbol("connect"),Nn=Symbol("authAjax"),jn=Symbol("refresh"),Bn=Symbol("getPhoneInfo"),Ln=Symbol("saasSocket"),_n=Symbol("saasSocketMsg"),On=Symbol("screenSocket"),Rn=(Symbol("canvasSocket"),Symbol("drawImg"),Symbol("flashPlayer")),Pn=Symbol("inputSocket");t=function(){function e(t){var n=this;if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");if(this.basePath=Dn.getBasePath("saas-sdk.js"),window.WEB_SOCKET_SWF_LOCATION="".concat(this.basePath,"/").concat(Tn.a,"?version=").concat(Dn.isFlashNoCache()?(new Date).getTime():encodeURI(window.SDK_VERSION)),window.WEB_SOCKET_DEBUG=!1,this.Logger=new wn({accessKeyId:t.accessKeyId,token:t.token,masterId:t.masterId,countlyUrl:"https://countly.haimawan.com/i",appKey:"9c0edb3f80d8d9adc71bb544b6dc87743340e829",packageName:t.packageName}),"object"!=Dn.isDataType(t)||"array"!=Dn.isDataType(t.slaveIds))throw new Error("Cloudplay 实例错误");if("function"!=Dn.isDataType(t.onSceneChanged))throw new Error("onSceneChanged 参数错误");this.Maximum=t.Maximum,this.ObjectLength=4,this.slaveIdsMaxNumber=100,this.phoneIds=[t.masterId.phoneId],this.imgPhoneList=t.imgPhoneList||[];var r=[];this.imgPhoneList.map(function(e){e.list.map(function(e,t){r.push(e)})}),this.imgDomList=r,this.slaveIds=[],this.accessKeyId=t.accessKeyId||"",this.masterId=t.masterId||"",this.token=t.token||"",this.sceneChanged=t.onSceneChanged,this.userInfo=t.userInfo||{},this.cap="cap:".concat(t.cap),this.player=null,this.streameType="WEBRTC",this.volumeState=!1,this.sendWSMessageCallback={intent:Dn.dummyFunction,clipboard:Dn.dummyFunction},this.gInputSocket=null,this.isStop=!1,this.gSpeed=t.capTime||1e3,this.LoopImgTimer=null,this.wsMessageList={},this.wsMessageTimer=null,this.agentList=[],(t.slaveIds||[]).map(function(e){Dn.ArrayCutApart(e.list,n.Maximum).map(function(t){var r={ws:null,idcId:e.idcId,list:t};t.map(function(e){r[e]={readyState:0}}),n.agentList.push(r)}),n.slaveIds=n.slaveIds.concat(e.list)}),"string"!=Dn.isDataType(this.accessKeyId)||""==this.accessKeyId||/[^a-z0-9]/gi.test(this.accessKeyId)?this.sceneChanged("error",En("","accessKeyId参数错误","1000000001")):"string"!=Dn.isDataType(this.masterId.phoneId)||""==this.masterId.phoneId||/[^0-9]/gi.test(this.masterId.phoneId)?this.sceneChanged("error",En("","masterId参数错误","1000000002")):(this.slaveIds.push(this.masterId.phoneId),this.agentInfo={agentUrl:t.agentUrl,timestamp:t.timestamp,sign:t.sign},t.agentUrl&&t.timestamp&&t.sign?"string"!=Dn.isDataType(this.userInfo.userId)||"string"!=Dn.isDataType(this.userInfo.userToken)||/[^a-z0-9]/gi.test(this.userInfo.userId)||/[^a-z0-9]/gi.test(this.userInfo.userToken)||""==this.userInfo.userId||""==this.userInfo.userToken||32<this.userInfo.userId.length||32<this.userInfo.userToken.length?this.sceneChanged("error",En("","userInfo中的userId、userToken必须是小于32位的字符串","1000000007")):"string"==Dn.isDataType(t.cap)&&/^0\.[1-9][0-9]$|^1\.00$/.test(t.cap)?(Dn.isSupportRTC()||this.flashcheck())&&("string"!=Dn.isDataType(this.token)||""==this.token?this.sceneChanged("error",En("","token参数错误","1000000010")):(window.SDK_VERSION="hotfix-1.8.4-f03a82d6",this.phoneType=null,this.newSlaveIds=[],this.bOk=!0,this.rotate=!0,this.count=1,this.networkState=!1,this.oSaasWs=null,this.oScreenWs=null,this.keyboard={menu:{down:"keyDown:16777301",up:"keyUp:16777301"},backspace:{down:"keyDown:158",up:"keyUp:158"},home:{down:"keyDown:36",up:"keyUp:36"},delete:{down:"keyDown:8",up:"keyUp:8"},volumeUp:{down:"keyDown:115",up:"keyUp:115"},volumeDown:{down:"keyDown:114",up:"keyUp:114"}},this[Bn](),this.councetAgentLink(),this.networkListening(),this.checkInitWebsocketNum=0,document.addEventListener("click",function(e){ze(".inputTextarea").size()&&ze(".inputTextarea").blur()}))):this.sceneChanged("error",En("","cap取值范围必须是0.10-0.50之间","1000000008")):this.sceneChanged("error",En("","签名或代理连接错误","1000000030")))}var t,n;return t=e,(n=[{key:"domListening",value:function(){var e=this;setTimeout(function(){e.imgDomList.map(function(t){var n=document.getElementById(t);n&&(ze(n).find(".inputTextarea").length||n.appendChild(Dn.createInputElem()),e.handelMouseListening(t,function(n,r){t==ze(r.target.parentElement).attr("id")&&("WEBRTC"==e.streameType&&e.player&&!e.volumeState&&(e.volumeState=!0,e.player.enableAudio(!0)),e.SyncOperation(n,t))}))})},50)}},{key:"addCloudPhones",value:function(e){var t=this,n=0,r=(e.forEach(function(e){n+=e.list.length}),this.agentList.forEach(function(r){var o=Object.getOwnPropertyNames(r).length-t.ObjectLength,i=[];o<=t.Maximum&&e.forEach(function(e){r.idcId===e.idcId&&e.list.forEach(function(o,a){r[o]?e.list[a]=null:!o||Object.getOwnPropertyNames(r).length-t.ObjectLength>t.Maximum||(r[o]={getImg:!1,readyState:0},r.list.includes(o)||r.list.push(o),i.push(o),e.list[a]=null,--n)})}),r.ws.oWs.readyState===WebSocket.OPEN&&i.length&&(r.ws&&Dn.websocket_send(r.ws,JSON.stringify({cmd:"Connect",trace:Dn.getRandomString(),data:{idcId:r.idcId,addPhones:i}})),t.slaveIds=t.slaveIds.concat(i))}),0<n&&e.forEach(function(e){var n=t.removeEmpty(e.list);Dn.ArrayCutApart(n,t.Maximum).forEach(function(n){var r={ws:t.agentLink({list:n,idcId:e.idcId}),idcId:e.idcId,list:n};t.slaveIds=t.slaveIds.concat(n),n.forEach(function(e){r[e]={getImg:!1,readyState:0}}),t.agentList.push(r)})}),new Set(this.slaveIds));this.slaveIds=Array.from(r)}},{key:"removeCloudPhones",value:function(e){var t=this;e.forEach(function(e){e.list.forEach(function(e){t.agentList.forEach(function(n){var r=[];n[e]&&n[e].readyState&&(0<=t.slaveIds.indexOf(e)&&t.slaveIds.splice(t.slaveIds.indexOf(e),1),r.push(e)),r.length&&n.ws&&Dn.websocket_send(n.ws,JSON.stringify({cmd:"Connect",trace:Dn.getRandomString(),data:{idcId:n.idcId,rmPhones:r}}))})})})}},{key:"reconnectCloudPhones",value:function(e){this.addCloudPhones(e)}},{key:"resetImgPhoneList",value:function(e,t){var n=[];e.map(function(e){e.list.map(function(e,t){n.push(e)})}),t&&(this.cap="cap:".concat(t)),this.imgDomList=n,this.imgPhoneList=e,this.LoopgetImg(),this.domListening()}},{key:"setCapInterval",value:function(e){this.gSpeed=e,this.LoopgetImg()}},{key:"slaveSwitchMaster",value:function(e){this.token=e.token,this.masterId.phoneId=e.slavePhoneId,this.masterId.domId=e.domId,this.phoneIds=[e.slavePhoneId],this.oSaasWs&&(this.oSaasWs.stop(),this.oSaasWs.oWs.close()),this.oScreenWs&&(this.oScreenWs.stop(),this.oScreenWs.oWs.close()),this.gInputSocket&&(this.gInputSocket.stop(),this.gInputSocket.oWs.close()),this.player&&(this.player.disconnect(),this.player=null),this.removeCloudPhones([{list:[e.slavePhoneId],idcId:e.slaveIdcId}]),this.addCloudPhones([{list:[e.masterPhoneId],idcId:e.masterIdcId}]),this.slaveIds.push(e.slavePhoneId),this[Bn]()}},{key:"resetPlayer",value:function(e){var t,n;"WEBRTC"==this.streameType&&(t=document.querySelector(".HMplayerBox"),e=document.getElementById(e),t)&&e&&(n=Dn.getStyle(e,"width"),e=Dn.getStyle(e,"height"),t.style.width=e,t.style.height=n,n=(parseFloat(n)-parseFloat(e))/2,t.style.left="".concat(n,"px"),t.style.top="".concat(Math.abs(n),"px"))}},{key:"councetAgentLink",value:function(){var e=this;this.agentList.map(function(t){t.ws=e.agentLink(t)})}},{key:"agentLink",value:function(e){var t=this,n=null,r="".concat(this.agentInfo.agentUrl,"?bid=").concat(this.accessKeyId,"&timestamp=").concat(this.agentInfo.timestamp,"&sign=").concat(this.agentInfo.sign);return n=new qe({url:r,type:"agentLink",opened:function(){n&&Dn.websocket_send(n,JSON.stringify({cmd:"Connect",trace:Dn.getRandomString(),data:{idcId:e.idcId,addPhones:e.list}})),t.LoopgetImg(),t.domListening()},closed:function(){t.networkState},message:function(e){try{e=JSON.parse(e),t.AgentLinkMessage(e)}catch(e){}},error:function(e){},reconnected:function(e){t.isStop||t.networkState||(t.sceneChanged("error",En("","系统内部错误","999999999")),t.quit())}})}},{key:"AgentLinkMessage",value:function(e){switch(e.cmd){case"Connect":this.sceneChanged("connectState",e.data),"Add"==e.data.type&&e.data.success?this.AddPhoneState(e.data):this.RemovePhoneState(e.data);break;case"Screenshot":e.data.success&&e.data.img&&this.dragBase64Img(e.data.phoneId,e.data.img);break;case"Notify":if("Disconnect"==e.data.notifyType)e.data.success=!1,this.sceneChanged("connectState",e.data),this.RemovePhoneState(e.data);else if("InputSocketText"==e.data.notifyType&&0<=this.imgDomList.indexOf(e.data.phoneId+"")&&e.data.body){if(/^startim/gi.test(e.data.body))return void this.IMInput("start",e.data.phoneId);if(/^stopim/gi.test(msg))return void this.IMInput("stop",e.data.phoneId)}}}},{key:"SyncOperation",value:function(e,t){var n=this;t&&t!=this.masterId.phoneId||Dn.websocket_send(this.gInputSocket,e),this.agentList.map(function(r){t&&t!=n.masterId.phoneId?r[t]&&r.ws&&Dn.websocket_send(r.ws,JSON.stringify({cmd:"Input",trace:Dn.getRandomString(),data:{type:2,target:[t],body:e}})):r.ws&&Dn.websocket_send(r.ws,JSON.stringify({cmd:"Input",trace:Dn.getRandomString(),data:{type:1,body:e}}))})}},{key:"LoopgetImg",value:function(e){for(var t=this,n=0;n<this.imgDomList.length;n++){var r,o,i,a=this.imgDomList[n];(a=document.getElementById(a))&&!a.querySelector("canvas")&&(r=document.createElement("canvas"),a.appendChild(r),r=a.querySelector("canvas"),ze(r).attr({width:1280,height:720})),a&&(r=a.querySelector("canvas"),o=parseFloat(Dn.getStyle(a,"height")),a=parseFloat(Dn.getStyle(a,"width")),i=(parseFloat(o)-parseFloat(a))/2,ze(r).css({position:"relative",width:"".concat(o,"px"),height:"".concat(a,"px"),left:"-".concat(i,"px"),top:"".concat(i,"px"),transform:"rotate(90deg)"}))}clearInterval(this.LoopImgTimer),this.agentList.map(function(e,n){var r=[];e.getImgList=r,t.imgPhoneList.map(function(t){e.idcId==t.idcId&&t.list.map(function(t,n){e[t]&&t&&r.push(t)})}),e.getImgList=r}),this.AgentGetImg(),this.LoopImgTimer=setInterval(function(){t.AgentGetImg()},this.gSpeed)}},{key:"AgentGetImg",value:function(){var e=this;this.agentList.map(function(t,n){t.getImgList.length&&t.ws&&t.ws.oWs.readyState===WebSocket.OPEN&&t.ws&&Dn.websocket_send(t.ws,JSON.stringify({cmd:"Screenshot",trace:Dn.getRandomString(),data:{target:t.getImgList,body:e.cap}}))})}},{key:"AddPhoneState",value:function(e){this.agentList.map(function(t,n){t[e.phoneId]&&(t[e.phoneId].readyState=1)})}},{key:"RemovePhoneState",value:function(e){var t=this;this.agentList.map(function(n,r){n[e.phoneId]&&(0<=t.slaveIds.indexOf(e.phoneId)&&t.slaveIds.splice(t.slaveIds.indexOf(e.phoneId),1),t.agentList[r].getImgList=n.getImgList.filter(function(t){return t!=e.phoneId}),t.agentList[r].list=n.list.filter(function(t){return t!=e.phoneId}),delete t.agentList[r][e.phoneId])}),this.agentList=this.agentList.filter(function(e){return!(e=Object.getOwnPropertyNames(e).length<=4)})}},{key:"dragBase64Img",value:function(e,t){var n,r,o=document.getElementById(e).querySelector("canvas");o&&(n=o.getContext("2d"),(r=new Image).src="data:image/png;base64,"+t,r.onload=function(){n.drawImage(r,0,0,o.width,o.height)})}},{key:"removeEmpty",value:function(e){var t=[];return e.map(function(e){e&&t.push(e)}),t}},{key:"flashcheck",value:function(){return Dn.flashCheck()?Dn.flashCheck()&&Dn.flashCheck()<11?(this.sceneChanged("error",En("","flash版本低于11,请升级flash","1000000010")),!1):(this.initFlashSocket(),!0):(this.sceneChanged("error",En("","flash已被禁用,请启用flash插件","1000000009")),!1)}},{key:"networkListening",value:function(e){function t(){r.isStop||setTimeout(function(){r.isStop=!1,r[Bn](),r.councetAgentLink()},2e3)}function n(){r.isStop||(r.sceneChanged("offline",function(e){return{message:e=0<arguments.length&&void 0!==e?e:"网络中断"}}()),r.networkState=!0,r.stopSDK("offline"))}var r=this;e?(window.removeEventListener("online",t),window.removeEventListener("offline",n)):(window.removeEventListener("online",t),window.removeEventListener("offline",n),window.addEventListener("online",t),window.addEventListener("offline",n))}},{key:Ln,value:function(e,t){var n=this;e="".concat(e,"/websocket?did=31234567&cid=").concat(this.masterId.phoneId,"&uid=").concat(this.userInfo.userId,"&appId=").concat(this.accessKeyId,"&sign=").concat(t);this.Logger.send(12088,{saasUrl:encodeURIComponent(e)}),this.oSaasWs=new qe({url:e,type:"saas",opened:function(){n.Logger.send(12089),n[xn]()},closed:function(){n.networkState||n.sceneChanged("reconnecting",Cn())},message:function(e){n[_n](e),n.Logger.send(12054,window.btoa(JSON.stringify(e)))},error:function(e){},reconnected:function(e){n.isStop||n.networkState||n.sceneChanged("error",En("","系统内部错误","999999999"))}})}},{key:On,value:function(e){var t=this;this.Logger.send(12401,{screenUrl:encodeURIComponent(e)}),this.oScreenWs=new qe({url:e,type:"screen",opened:function(){t.Logger.send(12402)},closed:function(){t.networkState||(t.Logger.send(12403),t.sceneChanged("reconnecting",Cn()))},message:function(e){var n;"clipboard"===(e=JSON.parse(e)).type&&(Dn.hasOwnProperty(e,"code")?((n=0==e.code)?t.Logger.send(12902,"mid: "+e.mid):t.Logger.send(vn,"mid: "+e.mid+" code: "+e.code),delete t.wsMessageList[e.mid],"{}"==JSON.stringify(t.wsMessageList)&&(clearInterval(t.wsMessageTimer),t.wsMessageTimer=null),t.sendWSMessageCallback[e.type](e.mid,n,e.message)):(t.sceneChanged("wsmessage",e),t.Logger.send(12904,"type:clipboard, mid:"+e.mid)))},error:function(e){},reconnected:function(e){t.isStop||t.networkState||t.sceneChanged("error",En("","系统内部错误","999999999"))}})}},{key:_n,value:function(e){var t,n=this,r=JSON.parse(e);if(0===r.ack&&2===r.type)switch((t=JSON.parse(r.payload)).operation){case 5:var o=t.data,i=this.masterId.domId;if(Dn.emptyDom(this.masterId.domId)){if(r.to==this.masterId.phoneId){if("WEBRTC"==this.streameType&&o.webRtcInfo)this.volumeState=!1,this.player=new gn(function(e,t){n._playListenWEBRTC(e,t),"rtc_connect_vibf"==e&&n.startInput(o.inputUrl,i)}),this.player.create(i),this.player.connect({roomId:o.webRtcInfo.roomId,signalUrl:o.webRtcInfo.signalUrl},{});else{if(!this.flashcheck())return;var a=o.videoUrl,s=o.audioUrl,c=o.inputUrl;this[Rn](a,s,c,i)}this[On](o.screenUrl)}}else this.sceneChanged("error",En(r.to,"".concat(i," 节点没有找到"),"130106002"));break;case 1:case 2:case 3:case 4:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 21:break;case 22:this.quit()}}},{key:"arrayBufferToBase64",value:function(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=new Uint8Array(e),r=n.length,o="",i=0;i<r;i+=3)o=(o=(o=(o+=t[n[i]>>2])+t[(3&n[i])<<4|n[i+1]>>4])+t[(15&n[i+1])<<2|n[i+2]>>6])+t[63&n[i+2]];return r%3==2?o=o.substring(0,o.length-1)+"=":r%3==1&&(o=o.substring(0,o.length-2)+"=="),o}},{key:Rn,value:function(e,t,n,r){var o=this,i=Dn.emptyDom(r),a=Dn.getStyle(i,"width");i=Dn.getStyle(i,"height");new ut({elem:r,width:a,height:i,videoUrl:e,audioUrl:t,error:function(e){var t=Dn.getSlaveIdsValue(o.slaveIds,e,"phoneId");(t=(o.sceneChanged("stop",bn(t)),Dn.emptyDom(e),Dn.getSlaveIdsValue(o.slaveIds,e,"oInputSocket")))&&t.stop(),o[jn]()},success:function(){o.startInput(n,r)}})}},{key:"startInput",value:function(e,t){var n=this;this[Pn](e,t,function(){n.sceneChanged("start",function(e,t){return{phoneId:e=0<arguments.length&&void 0!==e?e:"",message:t=1<arguments.length&&void 0!==t?t:"设备已启用"}}(n.masterId.phoneId)),n.handelMouseListening(t,function(e,r){r&&t!=ze(r.target.parentElement.parentElement).attr("id")&&t!=ze(r.target.parentElement.parentElement.parentElement).attr("id")||("WEBRTC"==n.streameType&&n.player&&!n.volumeState&&(n.volumeState=!0,n.player.enableAudio(!0)),n.SyncOperation(e))})})}},{key:"handelMouseListening",value:function(e,t){function n(e,t,n,r){(isNaN(t)||t<0)&&(t=0),(isNaN(n)||n<0)&&(n=0),e="mouse:".concat(e,":").concat(t.toFixed(4),",").concat(n.toFixed(4)),o(e,r)}var r,o,i,a;r=e,o=t,e=(e=document.getElementById(r)).getElementsByTagName("canvas")[0]||e.getElementsByClassName("fp-engine")[0]||e,a=i=0,e&&(e.onmousedown=function(e){var t=parseFloat(rt.getStyle(document.getElementById(r),"width")),o=parseFloat(rt.getStyle(document.getElementById(r),"height"));return window.gHMWElement=r||null,i=e.offsetX/o,a=e.offsetY/t,n(2,i,a,e),n(1,i,a,e),this.onmousemove=function(e){i=e.offsetX/o,a=e.offsetY/t,e.offsetY>t-10||e.offsetY<10||e.offsetX>o-10||e.offsetX<10?(this.onmousemove=null,n(2,i,a,e)):n(3,i,a,e)},this.onmouseup=function(e){this.onmousemove=null,this.onmouseup=null,this.releaseCapture&&this.releaseCapture(),n(2,i,a,e)},this.setCapture&&this.setCapture(),!1})}},{key:Pn,value:function(e,t,n){var r=this;this.gInputSocket=new qe({url:e,elem:t,type:"input",opened:function(){r.networkState=!1,document.getElementById(t).appendChild(Dn.createInputElem()),n()},closed:function(e,t){clearInterval(r[t]),r.networkState||r.sceneChanged("reconnecting",Cn(r.masterId.phoneId))},message:function(e){/^pong/gi.test(e)||(/^startim/gi.test(e)?r.IMInput("start",r.masterId.phoneId):/^stopim/gi.test(e)&&r.IMInput("stop",r.masterId.phoneId))},error:function(e){},reconnected:function(e){r.isStop||r.networkState||(r.sceneChanged("error",En(r.masterId.phoneId,"链接失败,请手动刷新","999999999")),r.quit())}})}},{key:"IMInput",value:function(e,t){var n,r,o;window.gHMWElement&&(t=t||this.masterId.domId,window.gHMWElement==t)&&(ze(".inputTextarea").size()&&(ze(".inputTextarea").hide(),ze(".inputTextarea").blur()),n=document.getElementById(window.gHMWElement).getElementsByTagName("textarea")[0],r=!0,o=this,"stop"==e?n&&n.blur():"start"==e&&(n&&(n.style.display="block"),n.focus(),n.removeEventListener("compositionstart",e=function(e){(r=!0)&&o.groupSendMsg(t,this)}),n.addEventListener("compositionstart",function(){r=!1}),n.removeEventListener("compositionend",e),n.addEventListener("compositionend",e),n.oninput=function(){r&&o.groupSendMsg(t,this)},n.onkeydown=function(e){8==e.keyCode&&o.functionkey(t,o.keyboard.delete.down,o.keyboard.delete.up)}))}},{key:"groupSendMsg",value:function(e,t){var n=t.value;this.SyncOperation("textIM:".concat(n),e),t.value=""}},{key:"closeSocket",value:function(e){e.oInputSocket&&(e.oInputSocket.stop(),e.oInputSocket.oWs.close()),e.oCanvasSocket&&(e.oCanvasSocket.stop(),e.oCanvasSocket.oWs.close()),this.sceneChanged("stop",bn(e.phoneId))}},{key:Bn,value:function(){var e=this,t="".concat(Mn,"/phone/").concat(this.masterId.phoneId),n={accessKeyId:this.accessKeyId,token:this.token,updateClientIp:!1,clientIp:""};this[Nn](t,n,"getPhoneInfo",function(t){e.phoneType=t.phoneType,e[Ln](t.accessUrl,t.signature)})}},{key:jn,value:function(e,t){var n=this,r="".concat(Mn,"/phone/").concat(this.masterId.phoneId,"/group/refresh"),o=["WEBRTC","RTMP"];Dn.isSupportRTC()||(o=["RTMP"]),o={accessKeyId:this.accessKeyId,token:this.token,phoneType:this.phoneType,streamingTypes:o,userAgent:navigator.userAgent,phoneIds:this.phoneIds,clientIp:"",clientType:3};this.Logger.send(12120,JSON.stringify(o)),this[Nn](r,o,"refresh",function(e){n.streameType=e.streamType,n.Logger.send(12121,JSON.stringify(e))})}},{key:xn,value:function(){var e=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"connect",n=1<arguments.length?arguments[1]:void 0,r="".concat(Mn,"/phone/").concat(this.masterId.phoneId,"/group/connect"),o={accessKeyId:this.accessKeyId,token:this.token,phoneIds:this.phoneIds};this[Nn](r,o,t,function(r){e[jn](t,n)})}},{key:Nn,value:function(e,t,n,r){var o=this;C({url:e,data:JSON.stringify(t),contentType:"text/plain",done:function(e,i){var a=t.phoneIds?t.phoneIds.toString():o.masterId.phoneId;e?0==(e=JSON.parse(i)).code?r(e):(i=e.msg,e="120".concat(e.code,"002"),o.sceneChanged("error",En(a,i,e))):(i="120".concat(In[n],"002"),o.sceneChanged("error",En(a,"".concat(n,"请求失败"),i)))}})}},{key:"increment",value:function(e){if("array"!=Dn.isDataType(e))this.sceneChanged("error",En("","increment参数类型错误","1000000015"));else{var t=this.uniq(e);if([].concat(kn(t),kn(this.slaveIds)).length>this.slaveIdsMaxNumber)this.sceneChanged("error",En("","群控数量最多不能超过".concat(this.slaveIdsMaxNumber,"个"),"1000000004"));else{for(var n=0;n<t.length;n++)-1==Dn.getDomIds(this.slaveIds).indexOf(t[n].domId)&&-1==Dn.getPhoneIds(this.slaveIds).indexOf(t[n].phoneId)||(this.sceneChanged("error",En("","phoneId或domId已被占用","1000000018")),t.splice(n,1),n--);t.length&&this[xn]("increment",t)}}}},{key:"uniq",value:function(e){for(var t=[],n=0;n<e.length;n++)-1==Dn.getDomIds(t).indexOf(e[n].domId)&&-1==Dn.getPhoneIds(t).indexOf(e[n].phoneId)&&t.push(e[n]);return t}},{key:"reduction",value:function(e){var t=this,n=e;if("quit"!=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:"")){if("array"!=Dn.isDataType(n))return void this.sceneChanged("error",En("","reduction参数类型错误","1000000015"));e=Dn.getDomIds(n);var r=Dn.getPhoneIds(n);if(!e||!r)return void this.sceneChanged("error",En("","缺少phoneId或domId","1000000006"));for(var o=0;o<n.length;o++)-1==Dn.getDomIds(this.slaveIds).indexOf(n[o].domId)||-1==Dn.getPhoneIds(this.slaveIds).indexOf(n[o].phoneId)?(this.sceneChanged("error",En("","phoneId或domId不存在","1000000016")),n.splice(o,1),o--):n[o].phoneId==this.masterId.phoneId&&(this.sceneChanged("error",En("","主控设备不能移除","1000000020")),n.splice(o,1),o--)}n.length&&(e="".concat(Mn,"/phone/").concat(this.masterId.phoneId,"/group/subtract"),r={accessKeyId:this.accessKeyId,token:this.token,phoneIds:Dn.getPhoneIds(n)},this[Nn](e,r,"reduction",function(e){for(var r=0;r<t.slaveIds.length;r++)Dn.findInArrRreduction(n,t.slaveIds[r])&&(Dn.emptyDom(t.slaveIds[r].domId),t[t.slaveIds[r].domId]&&clearInterval(t[t.slaveIds[r].domId]),t.closeSocket(t.slaveIds[r]),t.slaveIds.splice(r,1),r--)}))}},{key:"reconnect",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,reconnect参数错误","1000000014")):(this.sceneChanged("stop",bn(e)),this.phoneIds=[],this.phoneIds=[e],this[xn]("reconnect"))}},{key:"home",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,home参数错误","1000000013")):this.functionkey(e,this.keyboard.home.down,this.keyboard.home.up)}},{key:"backspace",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,backspace参数错误","1000000012")):this.functionkey(e,this.keyboard.backspace.down,this.keyboard.backspace.up,!0)}},{key:"menu",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,menu参数错误","1000000011")):this.functionkey(e,this.keyboard.menu.down,this.keyboard.menu.up)}},{key:"volumeUp",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,menu参数错误","1000000011")):this.functionkey(e,this.keyboard.volumeUp.down,this.keyboard.volumeUp.up)}},{key:"volumeDown",value:function(e){this.slaveIds.indexOf(e)<0||"string"!=Dn.isDataType(e)?this.sceneChanged("error",En("","phoneId没有找到,menu参数错误","1000000011")):this.functionkey(e,this.keyboard.volumeDown.down,this.keyboard.volumeDown.up)}},{key:"functionkey",value:function(e,t,n,r){var o=this;e==this.masterId.phoneId&&(e=""),this.SyncOperation(t,e),r?setTimeout(function(){o.SyncOperation(n,e)},70):this.SyncOperation(n,e)}},{key:"refreshImg",value:function(e){var t=this;"string"!=Dn.isDataType(e)||e<500||/[^0-9]/gi.test(e)?this.sceneChanged("error",En("","refreshImg时间必须大于500ms","1000000019")):this.slaveIds.forEach(function(n){t[n.domId]&&clearInterval(t[n.domId]),n.oCanvasSocket&&(t[n.domId]=setInterval(function(){Dn.websocket_send(n.oCanvasSocket,t.cap)},parseInt(e)))})}},{key:"quit",value:function(){this.networkState=!0,this.stopSDK(),this.networkListening("remove")}},{key:"stopSDK",value:function(e){"offline"!=e&&(this.isStop=!0),this.oSaasWs&&(this.oSaasWs.stop(),this.oSaasWs.oWs.close()),this.oScreenWs&&(this.oScreenWs.stop(),this.oScreenWs.oWs.close()),this.gInputSocket&&(this.gInputSocket.stop(),this.gInputSocket.oWs.close()),this.player&&(this.player.disconnect(),this.player=null),this.agentList.map(function(e){e.ws&&(e.ws.stop(),e.ws.oWs.close())})}},{key:"initFlashSocket",value:function(){var e=this;this.initWebsocketModule(12e3,function(t){!t&&e.checkInitWebsocketNum<1?(e.checkInitWebsocketNum++,window.isWebSocketModuleInitFunCall=!1,e.initFlashSocket()):e.checkInitWebsocketNum=0})}},{key:"sendWSMessage",value:function(e){var t=this;if(!Dn.hasOwnProperty(e,"type")||!Dn.hasOwnProperty(e,"data"))return e&&e.callback&&e.callback(e.mid,!1,"缺少data或type字段"),this.Logger.send(vn,"mid: ".concat(e.mid," type or data missed")),!1;if("function"==typeof e.callback&&(this.sendWSMessageCallback[e.type]=e.callback),!Dn.hasOwnProperty(e,"mid"))return this.sendWSMessageCallback[e.type]("",!1,"缺少mid字段"),this.Logger.send(vn,"mid missed"),!1;if(!this.oScreenWs)return this.sendWSMessageCallback[e.type](e.mid,!1,"socket未链接"),this.Logger.send(vn,"mid: ".concat(e.mid," ws not connected")),!1;var n=e.type;if(["clipboard","gps","intent"].indexOf(n)<0)return this.sendWSMessageCallback[e.type](e.mid,!1,"type字段无效"),this.Logger.send(vn,"mid: ".concat(e.mid," invalid message type: ").concat(n)),!1;if("clipboard"!==n)return!1;this.Logger.send(12901,"mid: ".concat(e.mid));var r=e.data;if(!(r instanceof Array))return this.sendWSMessageCallback[e.type](e.mid,!1,"data字段无效"),this.Logger.send(vn,"mid: ".concat(e.mid," type: ").concat(n," invalid message data")),!1;for(var o=0;o<r.length;o++){var i=r[o];if(!Dn.hasOwnProperty(i,"itemType")||!Dn.hasOwnProperty(i,"itemData"))return this.sendWSMessageCallback[e.type](e.mid,!1,"data字段无效"),this.Logger.send(vn,"mid: ".concat(e.mid," type: ").concat(n," invalid message item")),!1;if(1024<i.itemData.length)return this.sendWSMessageCallback[e.type](e.mid,!1,"data字段无效"),this.Logger.send(vn,"mid: ".concat(e.mid," type: ").concat(n," item length exceeded")),!1}var a={type:n,cid:this.masterId.phoneId,bid:this.accessKeyId,mid:e.mid,data:{code:"paste",dataType:0,data:r}};return this.wsMessageList[a.mid]=Date.now(),this.wsMessageTimer||(this.wsMessageTimer=setInterval(function(){for(var r in t.wsMessageList)1e4<Date.now()-t.wsMessageList[r]&&(t.sendWSMessageCallback[e.type](r,!1,"发送超时"),delete t.wsMessageList[r],t.Logger.send(vn,"mid: ".concat(e.mid," type: ").concat(n," timeout")));"{}"==JSON.stringify(t.wsMessageList)&&(clearInterval(t.wsMessageTimer),t.wsMessageTimer=null)},1e3)),this.oScreenWs.oWs.send(JSON.stringify(a))}},{key:"initWebsocketModule",value:function(e,t){try{window.WebSocketModuleInit(!1),setTimeout(function(){try{WebSocket.isInitialized()?t&&t(!0):t&&t(!1)}catch(e){t&&t(!1)}},e)}catch(e){t&&t(!1)}}},{key:"_playListenWEBRTC",value:function(e,t){switch(e){case"begin_connect":this.Logger.send(13215,encodeURIComponent(t));break;case"send_join":this.Logger.send(13221,t);break;case"receive_offer":this.Logger.send(13222,t);break;case"receive_candidate":this.Logger.send(13225,t);break;case"setRemoteDescription_success":this.Logger.send(13223,t);break;case"setRemoteDescription_failed":this.Logger.send(13233,t);break;case"setLocalDescription_success":this.Logger.send(13236,t);break;case"send_answer":this.Logger.send(13224,t);break;case"setLocalDescription_failed":this.Logger.send(13237,t);break;case"createAnswer_failed":this.Logger.send(13235,t);break;case"addIceCandidate_success":this.Logger.send(13238);break;case"addIceCandidate_failed":this.Logger.send(13239,t);break;case"onIceCandidate_success":this.Logger.send(13226,t);break;case"ontrack":this.Logger.send(13240,t);break;case"connectionState":this.Logger.send(13252,"peer_connect_status: ".concat(t));break;case"iceConnectionState":this.Logger.send(13252,"peer_iceConnect_status: ".concat(t));break;case"peer_connect_success":this.Logger.send(13216);break;case"self_connect_success":this.Logger.send(13250);break;case"other_connect_success":this.Logger.send(13251);break;case"webrtc_decode_first_frame_end":this.Logger.send(13249,t);break;case"rtc_connect_vibf":this.Logger.send(12045,t);break;case"rtc_connect_result":this.Logger.send(13252,"rtc_connect_result: ".concat(t)),1!=t.code&&(this.phoneIds=[this.masterId.phoneId],this[jn]());break;case"webrtc_receive_audio":this.Logger.send(13253);break;case"rtc_data_channel":break;case"rtc_create_error":this.Logger.send(13217,JSON.stringify(t));break;case"connect":this.Logger.send(13241,t);break;case"disconnect":this.Logger.send(13242,t);break;case"reconnectFailed":this.Logger.send(13245,"signalServer_reconn_failed");break;case"reconnectSuccess":this.Logger.send(13245,"signalServer_reconnect_success");break;case"reconnectError":this.Logger.send(13245,"signalServer_reconn_error");break;case"connectTimeout":this.Logger.send(13220,"signalServer_timeout")}}}])&&Sn(t.prototype,n),e}();window.Cloudplay=t}]);