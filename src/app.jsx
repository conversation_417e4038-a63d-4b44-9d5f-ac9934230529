import React from 'react';
import ReactDom from 'react-dom';
import { Router, Route, Switch, Redirect } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import <PERSON><PERSON> from 'js-cookie';
import history from 'common/history';
import './assets/iconfont/iconfont';
import 'moment/locale/zh-cn';

// 路由页面
import App from 'components/App';

const components = [
  {
    path: "/login",
    component: require('./view/auth/Login/home'),
  },
  {
    path: "/authorize",
    component: require('./view/auth/Authorize/home'),
  },
  {
    path: "/companyRegister",
    component: require('./view/auth/CompanyRegister/home'),
  },
  {
    path: "/register",
    component: require('./view/auth/Register/home'),
  },
  {
    path: "/forgetPassword",
    component: require('./view/auth/ForgetPassword/home'),
  }
]
if (module.hot) {
  module.hot.accept();
}
ReactDom.render(
  <ConfigProvider locale={zhCN} >
    <Router history={history}>
      <Switch>
        {
          components.map((item, index) => <Route key={index} path={item.path} render={(props) => (
            // 路由重定向-登录情况下无法前往login
            Cookie.get('weebot_cloud_token') ? <Redirect to="/" /> : <item.component.default {...props} />
          )} />)
        }
        <Route component={App} />
      </Switch>
    </Router>
  </ConfigProvider>
  , document.querySelector('#root'));
