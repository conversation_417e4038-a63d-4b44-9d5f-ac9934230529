/**
 * @description: 对象重组数组
 * @param {Object} obj
 * @return {Array}
 * @Author: <PERSON>hong<PERSON>unW<PERSON>
 * @Date: 2022/04/24 09:43
 */
export function ObjResetArr(obj, aKey) {
  let arr = [];
  for (let [key, value] of Object.entries(obj)) {
    arr.push({
      [aKey]: key,
      ...value,
    });
  }
  return arr;
}

/**
 * @description: 遍历数组并记录最后一个满足条件的元素的索引。
 * @param {*} arr
 * @param {*} conditionFn
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/04/18 10:37
 */
export function getLastSuccessfulConditionIndex(arr = [], conditionFn) {
  let lastSuccessfulIndex = -1;
  for (let i = 0; i < arr.length; i++) {
    if (conditionFn(arr[i])) {
      lastSuccessfulIndex = i;
    }
  }
  return lastSuccessfulIndex;
}
