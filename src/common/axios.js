import { notification, message } from 'antd';
import React from 'react';
import axios from 'axios';
import './axios.less';

let instance = axios.create();

instance.interceptors.response.use(function (response) {
  // Do something with response data
  return response;
}, function (error) {
  // console.log(error.request);
  // console.log(error.config);
  // console.log(error.response);
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const warningNotLen = document.getElementsByClassName('ant-notification-notice').length === 0;
    const warningMsgLen = document.getElementsByClassName('ant-message-notice-content').length === 0;
    console.log(error.response, warningNotLen, warningMsgLen, 'error.response');
    if (process.env.NODE_ENV == 'development') {
      if (warningNotLen) {
        if (error.response.status === 401) {
          notification.open({
            className: 'warning',
            message: '响应异常：账号状态已过期，请重新登录！',
            // message: `响应异常：${error.response.status} (${error.response.statusText})`,
            // description: (
            //   <div>
            //     <div>{`api请求路由：${error.config.url}`}</div>
            //     <div>{error.response.data.msg}</div>
            //   </div>
            // )
          });
        } else {
          notification.open({
            className: 'warning',
            message: `响应异常：${error.response.status} (${error.response.statusText})`,
            description: (
              <div>
                <div>{`api请求路由：${error.config.url}`}</div>
                <div>{error.response.data.msg}</div>
              </div>
            )
          });
        }
      }
    } else {
      // if (warningMsgLen) {
      //   message.error(error.response.data.msg);
      // }
    }
  } else if (error.request && process.env.NODE_ENV == 'development') {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    notification.open({
      className: 'error',
      message: '服务器未响应',
      description: (
        <div>{`api请求路由：${error.config.url}`}</div>
      )
    });
  } else if (process.env.NODE_ENV == 'development') {
    // Something happened in setting up the request that triggered an Error
    notification.open({
      className: 'error',
      message: '请求发送异常',
      description: (
        <div>
          <div>{`api请求路由：${error.config.url}`}</div>
          <div>{`描述：${error.msg}`}</div>
          <div>{error.stack}</div>
        </div>
      )
    });
  }
  // Do something with response error
  return Promise.reject(error);
});

export default instance;
