/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/02/28 15:26
 * @LastEditTime: 2024/02/28 15:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/common/clipboard.js
 * @Description: '复制到粘贴板'
 */

import Clipboard from 'clipboard';
import { message } from 'antd';

export function copyToClipboard(text, event, tip = '复制成功！') {
  return new Promise((resolve, reject) => {
    const clipboard = new Clipboard(event.target, {
      text: () => text,
    });
    clipboard.on('success', () => {
      resolve();
      message.success(tip);
      clipboard.destroy();
    });
    clipboard.on('error', (err) => {
      reject(err);
      message.error('该浏览器不支持自动复制');
      clipboard.destroy();
    });
    clipboard.onClick(event);
  });
}
