import moment from 'moment';

/**
 * @description: 计算两个日期之间的天数
 * @param {开始日期 yyyy-MM-dd} dateString1
 * @param {结束日期 yyyy-MM-dd} dateString2
 * @return {number} 如果日期相同 返回一天 开始日期大于结束日期，返回0
 * @Author: <PERSON>hongJun<PERSON>ei
 * @Date: 2021/07/29 11:31
 */
export function getDaysBetween(dateString1, dateString2) {
  let startDate = Date.parse(dateString1);
  let endDate = Date.parse(dateString2);
  if (startDate > endDate) {
    return 0;
  }
  if (startDate == endDate) {
    return 1;
  }
  let days = (endDate - startDate) / (1 * 24 * 60 * 60 * 1000) + 1;
  return days;
}

export function ts2str(ts, fmt) {
  if (!ts) {
    return '';
  }
  let date = new Date(ts);
  let o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return fmt;
}

/**
 * @description: 日期转时间戳
 * @param {*}
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/07/22 09:34
 */
export function timeStamp(date) {
  return new Date(ts2str(date, 'yyyy-MM-dd hh:mm:ss').replace(/-/g, '/')).getTime();
}

export function doHandleMonth(month) {
  let m = month;
  if (month.toString().length == 1) {
    m = '0' + month;
  }
  return m;
}

export function getDay(day) {
  let today = new Date();
  let targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
  today.setTime(targetday_milliseconds); // 注意，这行是关键代码
  let tYear = today.getFullYear();
  let tMonth = today.getMonth();
  let tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + '-' + tMonth + '-' + tDate;
}

// 排除日期返回为null情况
export function getExcludeNullDate(date) {
  if (date) {
    return moment(date).format('YYYY-MM-DD');
  } else {
    return '';
  }
}

// 计算指定今天之前的天数
export function computeBeforeToday(day) {
  const end = new Date(new Date()); // 今天作为结束日期
  const today = new Date(new Date());
  today.setDate(new Date().getDate() - day + 1); // 计算后的作为开始日期
  const begin = today;
  return [begin, end];
}

// 数字转星期
export function getWeekday(value, prefix = '周') {
  let day = '';
  switch (value) {
    case 1:
      day = '一';
      break;
    case 2:
      day = '二';
      break;
    case 3:
      day = '三';
      break;
    case 4:
      day = '四';
      break;
    case 5:
      day = '五';
      break;
    case 6:
      day = '六';
      break;
    case 7:
      day = '日';
      break;
  }
  return prefix + day;
}

// 获取本周/本月的日期
export function getDayWeekMonthDate(value) {
  const now = new Date(); // 当前日期
  const nowDayOfWeek = now.getDay(); // 今天本周的第几天
  const nowDay = now.getDate(); // 当前日
  const nowMonth = now.getMonth(); // 当前月
  let nowYear = now.getYear(); // 当前年
  nowYear += nowYear < 2000 ? 1900 : 0;
  let startTime = '';
  let endTime = '';
  if (value == 'week') {
    const day = nowDayOfWeek || 7;
    startTime = formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 1 - day));
    endTime = formatDate(new Date(now.getFullYear(), nowMonth, nowDay + 7 - day));
  } else if (value == 'month') {
    let monthStartDate = new Date(nowYear, nowMonth, 1);
    let monthEndDate = new Date(nowYear, nowMonth + 1, 1);
    let days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24); // 获取本月的天数
    startTime = formatDate(new Date(nowYear, nowMonth, 1));
    endTime = formatDate(new Date(nowYear, nowMonth, days));
  }
  return [startTime, endTime];
}
// 日期格式化
function formatDate(date) {
  let myyear = date.getFullYear();
  let mymonth = date.getMonth() + 1;
  let myweekday = date.getDate();

  if (mymonth < 10) {
    mymonth = '0' + mymonth;
  }
  if (myweekday < 10) {
    myweekday = '0' + myweekday;
  }
  return myyear + '-' + mymonth + '-' + myweekday;
}

// 禁用当前以及当前日期之后的日期
export const disabledAfterDate = (current) => current >= moment().subtract(1, 'day');

// 只能选择当前日期之后的日期
export const disabledDate = (current) => current < moment().subtract(1, 'day');

// 只能选择当前时间之后的时间点
export const disabledTime = (date) => {
  let currentDay = moment().date(); // 当下的时间
  let currentHours = moment().hours();
  let currentMinutes = moment().minutes(); // 设置的时间
  let settingHours = moment(date).hours();
  let settingDay = moment(date).date();
  if (date && settingDay === currentDay && settingHours === currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 设置为当天现在这小时，禁用该小时，该分钟之前的时间
      disabledMinutes: () => range(0, currentMinutes - 1),
    };
  } else if (date && settingDay === currentDay && settingHours > currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 设置为当天现在这小时之后，只禁用当天该小时之前的时间
    };
  } else if (date && settingDay === currentDay && settingHours < currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 若先设置了的小时小于当前的，再设置日期为当天，需要禁用当天现在这小时之前的时间和所有的分
      disabledMinutes: () => range(0, 59),
    };
  } else if (date && settingDay > currentDay) {
    return {
      disabledHours: () => [], // 设置为当天之后的日期，则不应有任何时间分钟的限制
      disabledMinutes: () => [],
    };
  }
};

const range = (start, end) => {
  const result = [];
  for (let i = start; i <= end; i++) {
    result.push(i);
  }
  return result;
};

/**
 * @description: 获取两个日期之间的所有日期
 * @param {开始日期 yyyy-MM-dd} dateString1
 * @param {结束日期 yyyy-MM-dd} dateString2
 * @return {Array} 所有日期数组
 * @Author: Lixiaoyan
 * @Date: 2022/12/23 14:46
 */
export function getdiffdate(dateString1, dateString2) {
  //初始化日期列表，数组
  let diffdate = new Array();
  let i = 0;
  //开始日期小于等于结束日期,并循环
  while (dateString1 <= dateString2) {
    diffdate[i] = dateString1;

    //获取开始日期时间戳
    let stime_ts = new Date(dateString1).getTime();
    //增加一天时间戳后的日期
    let next_date = stime_ts + 24 * 60 * 60 * 1000;

    //拼接年月日，这里的月份会返回（0-11），所以要+1
    const next_dates_y = new Date(next_date).getFullYear() + '-';
    const next_dates_m =
      new Date(next_date).getMonth() + 1 < 10
        ? '0' + (new Date(next_date).getMonth() + 1) + '-'
        : new Date(next_date).getMonth() + 1 + '-';
    const next_dates_d =
      new Date(next_date).getDate() < 10
        ? '0' + new Date(next_date).getDate()
        : new Date(next_date).getDate();

    dateString1 = next_dates_y + next_dates_m + next_dates_d;

    //增加数组key
    i++;
  }
  return diffdate;
}

/**
 * @description: 今天时间显示以及今天之前显示日期
 * @param {日期 yyyy-MM-dd} date
 * @return {String} 日期
 * @Author: Lixiaoyan
 * @Date: 2023/5/12 17：00
 */
export function getTimeOrDate(date) {
  let result = '';
  if (moment(date).format('YYYY-MM-DD') == moment().format('YYYY-MM-DD')) {
    result = moment(date).format('HH:mm');
  } else if (moment(date).format('YYYY') != moment().format('YYYY')) {
    result = moment(date).format('YYYY-MM-DD');
  } else {
    result = moment(date).format('MM-DD');
  }
  return result;
}

/**
 * @description: 获取会话今天时间显示以及今天之前显示日期
 * @param {日期 yyyy-MM-dd} date
 * @return {String} 日期
 * @Author: Lixiaoyan
 * @Date: 2023/5/12 17：00
 */
export function getChatTimeOrDate(date) {
  let result = '';
  if (moment(date).format('YYYY-MM-DD') == moment().format('YYYY-MM-DD')) {
    result = moment(date).format('HH:mm');
  } else if (moment(date).format('YYYY') != moment().format('YYYY')) {
    result = moment(date).format('YYYY年MM月DD日');
  } else {
    result = moment(date).format('MM月DD日 HH:mm');
  }
  return result;
}

/**
 * @description: 获取导出文档显示日期
 * @param {开始日期 yyyy-MM-dd} startDate
 * @param {结束日期 yyyy-MM-dd} endDate
 * @return {String} 日期
 * @Author: Lixiaoyan
 * @Date: 2023/5/12 17：00
 */
export function getExportFileDate(startDate, endDate) {
  let result = '';
  if (moment(startDate).format('YYYY-MM-DD') == moment(endDate).format('YYYY-MM-DD')) {
    result = moment(startDate).format('YYYY-MM-DD');
  } else {
    result = moment(startDate).format('YYYY-MM-DD') + '至' + moment(endDate).format('YYYY-MM-DD');
  }
  return result;
}

/**
 * @description: 秒数转格式化时间
 * @param {*} second
 * @param {*} format
 * @param {*} airFlag
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/07/24 15:03
 */
export function secondsToTime(second, format = 'HH:mm:ss', airFlag = false) {
  if (airFlag && !second) {
    return '';
  }
  return moment.utc(second * 1000).format(format);
}
