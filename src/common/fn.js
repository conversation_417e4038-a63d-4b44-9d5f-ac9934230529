/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/02/28 09:43
 * @LastEditTime: 2024/06/19 11:22
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/common/fn.js
 * @Description: '防抖、节流、单例函数'
 */

import { useCallback, useRef } from 'react';

/**
 * @description: 防抖
 * @param {function} fn
 * @param {number} delay
 * @return {*}
 * @Author: Janaeiw
 */
export function debounce(fn, delay = 500) {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}

/**
 * @description: hooks防抖
 * @param {function} fn
 * @param {number} delay
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/06/19 09:37
 */
export function useDebounce(fn, delay = 500) {
  const timerRef = useRef(null);

  const debouncedFunc = useCallback(
    (...args) => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      timerRef.current = setTimeout(() => {
        fn(...args);
      }, delay);
    },
    [fn, delay],
  );

  return debouncedFunc;
}

/**
 * @description: 节流
 * @param {function} fn
 * @param {number} delay
 * @return {*}
 * @Author: Janaeiw
 */
export function throttle(fn, delay = 500) {
  let timer = null;
  return function () {
    if (timer) {
      return;
    }
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}

/**
 * @description: hooks节流
 * @param {function} fn
 * @param {number} delay
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/06/19 10:06
 */
export function useThrottle(fn, delay = 500) {
  const lastCallTimeRef = useRef(0);

  const throttledFunc = useCallback(
    (...args) => {
      const now = new Date().getTime();
      if (now - lastCallTimeRef.current >= delay) {
        lastCallTimeRef.current = now;
        fn(...args);
      }
    },
    [fn, delay],
  );

  return throttledFunc;
}

/**
 * @description: 单例函数（只执行一次）
 * @param {function} fn
 * @return {*}
 * @Author: Janaeiw
 */
export function once(fn) {
  let done = false;
  return function () {
    if (!done) {
      done = true;
      fn.apply(this, arguments);
    }
  };
}
