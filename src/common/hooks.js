import { useRef } from 'react';
import {useDidCache, useDidRecover} from "react-router-cache-route";

/**
 * @description: 解决闭包数据不改变问题
 * @param {*} value
 * @return {*}
 * @Author: Lixiaoyan
 * @Date: 2022/08/26 17:10
 */
export function useLatest (value) {
  const ref = useRef(value);
  ref.current = value;
  return ref;
}


/**
 * @description: 使用页面缓存生命周期
 * @param {*} onShow
 * @param {*} onHide
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/03/19 10:18
 */
export function usePageCacheLifeCycle({onShow, onHide}) {
  useDidRecover(() => {
    onShow && onShow();
  });

  useDidCache(() => {
    onHide && onHide();
  });
}
