/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/19 16:51
 * @LastEditTime: 2024/07/11 15:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/common/image.js
 * @Description: '图片'
 */

import { message, Upload } from 'antd';

/**
 * @description: 压缩图片
 * @param {Object} file 文件流数据
 * @param {Object} options 压缩配置
 *  @param {Number} options.width 压缩图片的宽度
 * @param {Number} options.height 压缩图片的高度
 * @param {Number} options.quality 压缩后图片的清晰度，取值0-1，值越小，所绘制出的图像越模糊
 * @return {Promise} then(base64)
 * @Author: ZhongJunWei
 * @Date: 2021/08/12 15:55
 */
export function compressImage(file, options) {
  const read = new FileReader();
  read.readAsDataURL(file);
  return new Promise(function (resolve, reject) {
    read.onload = function (e) {
      const img = new Image();
      img.src = e.target.result;
      img.onload = function () {
        // 规则:2M以内不压缩
        const isLt2M = file.size / 1024 / 1024 > 20;

        // 默认按比例压缩-压缩质量的一半
        const width = isLt2M ? options.width || img.width / 2 : options.width || img.width;
        const height = isLt2M ? options.height || img.height / 2 : options.height || img.height;
        const quality = options.quality || 0.92; // 0.92为默认压缩质量

        // 生成canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 创建属性节点
        canvas.setAttribute('width', width);
        canvas.setAttribute('height', height);
        ctx.drawImage(this, 0, 0, width, height);
        const base64 = canvas.toDataURL(file['type'], quality);

        // 回调函数返回file的值（将base64编码转成file）
        // files = dataURLtoFile(base64) // 如果后台接收类型为base64的话这一步可以省略
        // 回调函数返回file的值（将base64转为二进制）
        // let fileBinary = dataURLtoBlob(base64)
        resolve(base64);
      };
    };
  });
}

/**
 * @description: 将base64转为二进制
 * @param {string} dataurl
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/08/12 15:57
 */
export function dataURLtoBlob(dataurl) {
  let arr = dataurl.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

/**
 * @description: base64转码（将base64编码转回file文件）
 * @param {string} dataurl
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/08/12 15:57
 */
export function dataURLtoFile(dataurl) {
  let arr = dataurl.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], { type: mime });
}

/**
 * @description: base64转码（将base64编码转回file文件）
 * @param {string} urlData
 * @param {string} fileName
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/08/12 15:57
 */
export function base64ToFile(urlData, fileName) {
  let arr = urlData.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bytes = atob(arr[1]); // 解码base64
  let n = bytes.length;
  let ia = new Uint8Array(n);
  while (n--) {
    ia[n] = bytes.charCodeAt(n);
  }

  return new File([ia], fileName, { type: mime });
}

/**
 * @description: wibot的filePath图片拼接
 * @param {string} filePath
 * @param {string} size
 * @return {url}
 * @Author: ZhongJunWei
 * @Date: 2021/08/12 15:59
 */
export function getWibotFileUrl(filePath, size = '0x0') {
  if (!filePath) {
    return '';
  }
  let src = filePath;
  const origin =
    process.env.NODE_ENV == 'development' ? 'https://test.wizone.work' : window.location.origin;
  const prefix = origin + '/minio/imageproxy/public/0x0/wecom-marketing-test/';
  if (
    src &&
    !src.startsWith('http://') &&
    !src.startsWith('https://') &&
    !src.startsWith('data:image')
  ) {
    src = prefix + src;
  }
  return src;
}

/**
 * @description: 图片上传前验证
 * @param {*} file
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/08/27 11:03
 */
export function beforeUpload(file, files, params = {}) {
  const { size = 2, width = null, height = null } = params;
  const filter = new Promise(function (resolve, reject) {
    if (!width && !height) {
      resolve(true);
    }
    const read = new FileReader();
    read.readAsDataURL(file);
    read.onload = function (e) {
      const img = new Image();
      img.src = e.target.result;
      img.onload = async function () {
        // console.log(width, height, img.width, img.height);
        if (img.width > width || img.height > height) {
          resolve(false);
        } else {
          resolve(true);
        }
      };
    };
  });
  return filter.then((res) => {
    if (!res) {
      message.error(`您只能上传最大尺寸${width} x ${height}图片！`);
    }
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('您只能上传JPEG/JPG/PNG格式图片！ ');
    }

    const isLtM = file.size / 1024 / 1024 < size;
    if (!isLtM) {
      message.error(`图片不能大于${size}MB!`);
    }
    return (res && isJpgOrPng && isLtM) || Upload.LIST_IGNORE;
  });
}

/**
 * @description: 视频上传前验证
 * @param {Object} file 文件流数据
 * @param {Number} size 文件限制大小
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2022/03/30 16:40
 */
export function videoBeforeUpload(file, size = 10) {
  const isVideo = file.type.includes('video/');
  if (!isVideo) {
    message.error('请上传视频文件!');
  }
  const isLtM = file.size / 1024 / 1024 < size;
  if (!isLtM) {
    message.error(`视频不能大于${size}MB!`);
  }
  return (isVideo && isLtM) || Upload.LIST_IGNORE;
}

export function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

/**
 * @description: 获取视频第一帧作为封面
 * @param {Object} params
 * @return {String}
 * @Author: ZhongJunWei
 * @Date: 2022/06/13 11:02
 */
export function getVideoBase64(params = {}) {
  return new Promise(function (resolve, reject) {
    const { url, currentTime = 1 } = params;
    let dataURL = '';
    const video = document.createElement('video');
    video.setAttribute('crossOrigin', 'anonymous'); // 处理跨域
    video.setAttribute('src', url);
    video.setAttribute('width', 400);
    video.setAttribute('height', 240);
    video.currentTime = currentTime;
    video.addEventListener('loadeddata', function () {
      // console.log(video.videoHeight);
      // console.log(video.videoWidth);
      // console.log(video.duration);
      const canvas = document.createElement('canvas');
      const Vwidth = video.width; // canvas的尺寸和图片一样
      const Vheight = video.height;
      canvas.width = Vwidth;
      canvas.height = Vheight;
      canvas.getContext('2d').drawImage(video, 0, 0, Vwidth, Vheight); // 绘制canvas
      dataURL = canvas.toDataURL('image/jpeg'); // 转换为base64
      resolve(dataURL);
    });
  });
}

/**
 * @description: 获取视频文件流的总时长
 * @param {*} file
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/07/11 15:21
 */
export function getVideoDuration(file) {
  return new Promise(function (resolve, reject) {
    // 创建一个video元素
    const video = document.createElement('video');
    video.preload = 'metadata';

    // 设置视频文件源为对象URL
    video.src = URL.createObjectURL(file);

    // 监听loadedmetadata事件，当视频的元数据已加载时触发
    video.addEventListener('loadedmetadata', function () {
      // 当视频的元数据已加载后，释放对象URL资源
      URL.revokeObjectURL(video.src);

      // 获取视频时长，单位为秒
      const duration = parseInt(video.duration);

      // 调用回调函数并传递视频时长
      resolve(duration);
    });
  });
}

/**
 * @description: 截取资源域名
 * @param {String} fileUrl
 * @return {String} fileUrl
 * @Author: Janaeiw
 * @Date: 2023/04/11 09:47
 */
export function interceptResourceDomain(fileUrl = '') {
  if (!fileUrl || process.env.NODE_ENV == 'development') {
    return fileUrl;
  }
  let arrUrl = fileUrl.split('//');
  let start = arrUrl[1].indexOf('/');
  let relUrl = arrUrl[1].substring(start);
  // if (relUrl.indexOf('?') != -1) {
  //   relUrl = relUrl.split('?')[0];
  // }
  return relUrl;
}

/**
 * @description: 读取图片参数
 * @param {*} file
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/12/12 11:42
 */
export async function lesenImageParams(file) {
  let img = new Image(),
    title,
    src,
    size,
    base,
    width,
    height;

  if (typeof file === 'object') {
    title = '前';
    size = file.size;
    src = URL.createObjectURL(file);
    base = 1024;
  } else {
    title = '后';
    size = file.length;
    src = file;
    base = 1333;
  }

  file = null; // 处理完后记得清缓存

  img.src = src;
  const params = new Promise(function (resolve, reject) {
    img.onload = function () {
      resolve({
        width: this.width,
        height: this.height,
      });
    };
  });

  await params.then((res) => {
    width = res.width;
    height = res.height;
  });

  return {
    title,
    src,
    size,
    base,
    width,
    height,
  };
}

/**
 * @description: 读取图片后缀
 * @param {*} url
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/12/12 16:19
 */
export function lesenImageSuffix(url) {
  // 从 URL 中提取文件名
  const filename = url.substring(url.lastIndexOf('/') + 1);
  // 使用正则表达式匹配文件名中的后缀
  const extension = filename.match(/\.(jpg|jpeg|png)$/i);
  if (extension) {
    // 返回匹配到的后缀（不包括点）
    return extension[0].substring(1).toLowerCase();
  }
  // 如果没有匹配到后缀，则返回空字符串或 null，取决于您的需求
  return '';
}
