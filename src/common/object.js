/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/09/27 10:57
 * @LastEditTime: 2024/03/29 18:25
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/common/object.js
 * @Description: ''
 */

/**
 * @description: 需要拼接的参数对象-转地址栏参数
 * @param {Object} obj
 * @return {String}
 * @Author: ZhongJunWei
 * @Date: 2021/12/16 11:03
 */
export function obj2qs(obj) {
  if (!obj && !Object.keys(obj).length) {
    return '';
  } else {
    let arr = [];
    for (let key in obj) {
      arr.push(key + '=' + obj[key]);
    }
    return arr.join('&');
  }
}

/**
 * @description: 需要转换的地址栏参数-转数组
 * @param {String} url
 * @return {Object}
 * @Author: ZhongJunWei
 * @Date: 2021/12/16 11:03
 */
export function qs2obj(url) {
  let qs = url.split('?')[1];
  let arr = [];
  let res = {};
  if (!qs) {
    // return res;
  } else {
    arr = qs.split('&');
    for (let i = 0, len = arr.length; i < len; i++) {
      let key = arr[i].split('=')[0];
      let val = arr[i].split('=')[1];
      res[key] = decodeURIComponent(val);
    }
  }
  return res;
}

/**
 * @description: 对象判重
 * @param {Object} obj1
 * @param {Object} obj2
 * @return {Boolean}
 * @Author: Janaeiw
 * @Date: 2024/03/29 18:22
 */
export function areObjsEqual(obj1, obj2) {
  if (JSON.stringify(obj1) === JSON.stringify(obj2)) {
    return true;
  }
  return false;
}
