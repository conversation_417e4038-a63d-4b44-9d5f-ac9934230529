import QrCode from 'qrcode';

/**
 * @description: 链接转二维码
 * @param {object} params
 * @return {string}
 * @Author: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @Date: 2021/08/16 16:39
 */
export function QrCodeBase(params = {}) {
  const { url } = params;
  let qrcode = null;
  QrCode.toDataURL(url, (err, base64) => {
    if (err) {
      throw err;
    }
    qrcode = base64;
  });
  return qrcode;
}
