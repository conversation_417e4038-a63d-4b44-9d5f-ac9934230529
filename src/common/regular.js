/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/01 09:19
 * @LastEditTime: 2024/11/22 14:23
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/common/regular.js
 * @Description: '正则'
 */

/**
 * @description: 移除输入框为空
 * @param {*} e
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/07/23 16:57
 */
export function removeInputEmpty(e) {
  // return e.target.value.replace(/\s+/g, "") //移除全部空格
  return e.target.value.replace(/(^\s*)|(\s*$)/g, ''); // 移除左右空格
}

/**
 * @description: 阿拉伯数字全部转换为大写-专门用于转周期
 * @param {string} n
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/08/26 17:11
 */
export function num(n) {
  // if (!/(^[1-9]\d*$)/) {
  //   return '非法数字';
  // }
  let uppercase = '千百亿千百十万千百十个';
  let nLength = n.length;
  let newStr = '';
  if (uppercase.length - nLength < 0) {
    return '数字过长';
  }
  uppercase = uppercase.substr(uppercase.length - nLength);
  for (let i = 0; i < nLength; i++) {
    newStr += '零一二三四五六日八九'.charAt(n[i]) + uppercase.charAt(i);
  }
  newStr = newStr.substr(0, newStr.length - 1);
  return newStr;
}

/**
 * @description: 获取括号外的字符
 * @param {string} val
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/09/02 13:44
 */
export function bracketsOut(val) {
  return val.replace(/(.+)[(（]\d+[)）]/g, '$1');
}

/**
 * @description: 获取括号内的子字符
 * @param {string} val
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/09/02 14:12
 */
export function bracketsIns(val) {
  return val.match(/\(([^)]*)\)/)[1];
}

/**
 * @description: 获取中文括号内的子字符
 * @param {string} val
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/09/02 14:12
 */
export function bracketsChsIns(val) {
  return val.match(/（([^)]*)）/)[1];
}

/**
 * @description: 解决Form内使用Upload报错
 * @param {*} e
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/10/13 09:37
 */
export function normFile(e) {
  if (Array.isArray(e)) {
    return e;
  }
  return e && e.fileList;
}

/**
 * http链接
 * @param {string} value
 * @returns {Boolean}
 */
export function validURL(value) {
  const reg = /(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/;
  return reg.test(value);
}

/**
 * @description: 百分比计算
 * @param {number} num
 * @param {number} total
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2022/11/08 14:47
 */
export function GetPercent(num, total) {
  num = parseFloat(num);
  total = parseFloat(total);
  if (isNaN(num) || isNaN(total)) {
    return 0;
  }
  return total <= 0 ? '0%' : Math.round((num / total) * 10000) / 100.0 + '%';
}

/**
 * @description: 去除所有空格
 * @param {*} value
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/05/18 09:48
 */
export function trimAll(value) {
  return value.replace(/\s+/g, '');
}

/**
 * @description: 编码文件链接中的中文
 * @param {*} url
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/05/18 09:48
 */
export function unescapeFile(url) {
  const reg = /[\u4e00-\u9fa5]/g;
  if (url.match(reg)) {
    const texts = url.match(reg).join('');
    const index = url.indexOf(texts);
    return url.substring(index, url.length);
  } else {
    const index = url.lastIndexOf('/');
    return url.substring(index + 1, url.length);
  }
}

/**
 * @description: 判断文件类型
 * @param {*} url
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/05/22 10:04
 */
export function checkSuffix(url) {
  const imageRegex = /\.(jpg|png|gif|bmp|jpeg|psd)$/;
  const videoRegex = /\.(avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv|mp4)$/;
  if (imageRegex.test(url.toLowerCase())) {
    return 'IMAGE';
  } else if (videoRegex.test(url.toLowerCase())) {
    return 'VIDEO';
  } else {
    return 'FILE';
  }
}

/**
 * @description: 移除输入框xss字符
 * @param {String} value
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/07/23 16:57
 */
export function removeInputXss(value) {
  return value.replace(/[<>"'&]/g, '');
}

/**
 * @description: 替换空格-\n-><br/>
 * @param {String} text
 * @return {String}
 * @Author: ZhongJunWei
 * @Date: 2022/07/01 15:05
 */
export function replaceSpace(text) {
  return text.replace(/\n/g, '<br/>');
}

/**
 * @description: <br/>替换成换行符
 * @param {*} text
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2022/07/22 16:23
 */
export function replaceBr(text) {
  return text.replace(/<br\s*\/?>/gi, '\r\n');
}

/**
 * @description: 验证文本是否全是空格
 * @param {string} text
 * @return {Boolean}
 * @Author: ZhongJunWei
 * @Date: 2022/07/01 15:09
 */
export function validateAllSpace(text) {
  if (text.split(' ').join('').length == 0) {
    return true;
  }
  return false;
}

/**
 * @description: 判断wangeditor富文本内容是否为空
 * @param {*} str
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/06/26 17:11
 */
export function editorIsEmpty(str) {
  let repStr = str
    .replace(/<[^<p>]+>/g, '') // 将所有<p>标签 replace ''
    .replace(/<[</p>$]+>/g, '') // 将所有</p>标签 replace ''
    .replace(/&nbsp;/gi, '') // 将所有 空格 replace ''
    .replace(/<[^<br/>]+>/g, ''); // 将所有 换行符 replace ''
  if (repStr == '') {
    return true;
  }
  let regu = '^[ ]+$';
  let re = new RegExp(regu);
  return re.test(repStr);
}

export function isNull(str) {
  if (str == '') {
    return true;
  }
  let regu = '^[ ]+$';
  let re = new RegExp(regu);
  return re.test(str);
}

/**
 * @description: 中文名字脱敏
 * @param {string} name
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/04/17 14:13
 */
export function maskChineseName(name) {
  if (!name) {
    return false;
  }
  if (name.length === 2) {
    return name[0] + '*';
  } else {
    const middleLength = name.length - 2;
    const firstChar = name.charAt(0);
    const lastChar = name.charAt(name.length - 1);
    const middleChars = '*'.repeat(middleLength);
    return `${firstChar}${middleChars}${lastChar}`;
  }
}

/**
 * @description: 搜索匹配高亮
 * @param {string} text
 * @param {Array} keywords
 * @return {string}
 * @Author: Janaeiw
 * @Date: 2024/10/15 11:42
 */
export function highLightText(text, keywords = []) {
  // 统一处理非数组类型参数
  if (typeof keywords === 'string') {
    keywords = keywords.split(',');
  } else if (!Array.isArray(keywords)) {
    keywords = [];
  }

  // 过滤空值并转义正则特殊字符
  const escapedKeywords = keywords
      .filter(Boolean)
      .map(keyword => keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
      .filter(keyword => keyword.length > 0);

  if (escapedKeywords.length === 0) return text;

  try {
    const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'gi');
    return text.replace(regex, '<span style="color:#ff5500">$1</span>');
  } catch {
    return text;
  }
}
/**
 * @description: 协议转换
 * @param {*} protocol
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/11/22 14:17
 */
export function transferProtocol(protocol = location.protocol) {
  const protocols = {
    'https:': 'wss:',
    'http:': 'ws:',
  };
  return protocols[protocol] || protocol; // 未匹配到时返回原协议
}
