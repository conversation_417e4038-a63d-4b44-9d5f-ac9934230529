/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/07/19 15:50
 * @LastEditTime: 2024/05/24 14:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/common/scroll.js
 * @Description: '操作滚动条'
 */

/**
 * @description: 滚动条保持最底部
 * @param {any} calssName
 * @return {*}
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/07/01 14:46
 */
export function scrollToBottom(calssName) {
  let container = document.querySelector(`.${calssName}`);
  if (!container) {
    return false;
  }
  setTimeout(() => {
    container.scrollTop = container.scrollHeight;
  }, 1000)
  // const observable = new MutationObserver(() => {
  //   container.scrollTo({ top: container.scrollHeight, behavior: 'auto'})
  // })
  // observable.observe(container, { childList: false, subtree: true })
}
