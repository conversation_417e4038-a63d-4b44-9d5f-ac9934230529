/**
 * @description: 获取localStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:11
 */
export function localGetItem(key) {
  return localStorage.getItem(key)
}

/**
 * @description: 设置localStorage
 * @param {*} key
 * @param {*} value
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:11
 */
export function localSetItem(key, value) {
  return localStorage.setItem(key, value)
}

/**
 * @description: 删除localStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:11
 */
export function localRemoveItem(key) {
  localStorage.removeItem(key)
}

/**
 * @description: 清空localStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/04/24 09:43
 */
export function localClear(key) {
  localStorage.clear(key)
}

/**
 * @description: 获取sessionStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/04/24 09:45
 */
export function sessionGetItem(key) {
  return sessionStorage.getItem(key)
}

/**
 * @description: 设置sessionStorage
 * @param {*} key
 * @param {*} value
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/04/24 09:45
 */
export function sessionSetItem(key, value) {
  return sessionStorage.setItem(key, value)
}

/**
 * @description: 删除sessionStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/04/24 09:45
 */
export function sessionRemoveItem(key) {
  sessionStorage.removeItem(key)
}

/**
 * @description: 清空sessionStorage
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/04/24 09:45
 */
export function sessionClear(key) {
  sessionStorage.clear(key)
}

/**
 * @description: 获取 document.cookie
 * @param {*} key
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 11:23
 */
export function getCookie(key) {
  const cookieStr = document.cookie
  if (!cookieStr) return null

  const cookies = cookieStr.split("; ")
  for (const item of cookies) {
    const [k, v] = item.split("=")
    if (decodeURIComponent(k) === key) {
      return decodeURIComponent(v)
    }
  }

  return null
}

/**
 * @description: 设置cookie
 * document.cookie="name=value;expires=time;path=path"
 * @param {*} name
 * @param {*} value
 * @param {*} time
 * @param {*} path
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:08
 */
export function setCookie(name, value, time = 7, path = "/") {
  if (typeof name !== "string" || typeof value !== "string") return

  const days = Number(time)
  const expiresDate = new Date()
  expiresDate.setDate(expiresDate.getDate() + (isNaN(days) ? 7 : days))

  const cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(
    value
  )};expires=${expiresDate.toUTCString()};path=${path}`
  document.cookie = cookieString
}

/**
 * @description:删除cookie 设置失效时间为之前的时间 如果cookie设置了路径,删除需要同步路径才可删除
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:09
 */
export function removeCookie(key, path) {
  setCookie(key, "", -1, path)
}

/**
 * @description: 清空cookie 只能删除当前域名路径内的cookie
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2023/03/06 10:09
 */
export function clearCookie() {
  let arr = document.cookie.split("; ")
  for (let i = 0; i < arr.length; i++) {
    let arrItem = arr[i].split("=")
    setCookie(arrItem[0], "", -1)
  }
}
