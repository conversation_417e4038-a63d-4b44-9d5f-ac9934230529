/**
 * @description:递归树形数组-重组字段
 * @param {*} arr
 * @param {*} keyMap
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2024/09/11 11:33
 */
export function renameKeysInArray(arr, keyMap) {
  // 递归函数，用于处理每个对象或数组
  function renameKeys(obj) {
    if (Array.isArray(obj)) {
      return obj.map((item) => renameKeys(item)); // 遇到数组，递归遍历数组
    } else if (typeof obj === 'object' && obj !== null) {
      let newObj = {};
      for (let key in obj) {
        // 使用新 key 替换旧 key，如果 keyMap 中存在该 key，否则保持原 key
        const newKey = keyMap[key] || key;
        // 递归处理嵌套的对象或数组
        newObj[newKey] = renameKeys(obj[key]);
      }
      return newObj;
    } else {
      // 原始值不需要递归，直接返回
      return obj;
    }
  }

  return renameKeys(arr);
}


const handleToggle = item => {
  item.options = item.options?.map(item => {
    return {
      isChecked: !item.isChecked,
      ...item
    }
  })
  console.log(`[item]: `, item)
}
export function recursionTagKeyTreeDataTag(treeData) {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children) {
      // 内部调用即递归
      tmp.children = tmp.options = recursionTagKeyTreeDataTag(tmp.children);
    }
    obj = {
      parentid: tmp.parentid,
      title: tmp.title,
      label: tmp.title,
      key: tmp.title,
      value: tmp.title,
      // disabled: tmp.children && tmp.children.length > 0,
      children: tmp.children,
      options: tmp.children,
    };
    newTreeData.push(obj);
  });
  return newTreeData;
}

export function recursionTagKeyTreeData(treeData) {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children) {
      // 内部调用即递归
      tmp.children = recursionTagKeyTreeData(tmp.children);
      obj = {
        title: tmp.title,
        key: tmp.key,
        value: tmp.key,
        children: tmp.children,
      };
    }
    newTreeData.push(obj);
  });
  return newTreeData;
}

// 树根据children是否有值控制是否可选
export function recursionTagKeyTreeDataDisabled(treeData) {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children) {
      // 内部调用即递归
      tmp.children = recursionTagKeyTreeDataDisabled(tmp.children);
    }

    obj = {
      parentid: tmp.parentid,
      title: tmp.title,
      key: tmp.key,
      value: tmp.key,
      children: tmp.children,
      disabled: !tmp.parentid && !tmp.children.length > 0,
    };
    newTreeData.push(obj);
  });
  return newTreeData;
}

export function recursionKeyEmployeeOption(treeData) {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children && tmp.children.length > 0) {
      // 内部调用即递归
      tmp.children = recursionKeyEmployeeOption(tmp.children);
    }
    obj = {
      name: tmp.name,
      key: tmp.id,
      children: tmp.children && tmp.children.length > 0 ? tmp.children : null,
    };
    newTreeData.push(obj);
  });
  return newTreeData;
}

// 扁平化多维数组->转一维
export function flatten(array = [], property = 'children') {
  if (array.length === 0) {
    return [];
  }
  const flattenArray = [];
  function loop(array) {
    array.forEach((item) => {
      flattenArray.push(item);
      if (item[property] && item[property].length > 0) {
        loop(item[property]);
      }
    });
  }
  loop(array);
  return flattenArray;
}

// 场景类型树递归方法
export const sceneRecursionTreeData = (treeData) => {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children && tmp.children.length > 0) {
      // 内部调用即递归
      tmp.children = sceneRecursionTreeData(tmp.children);
    }
    obj = {
      ...tmp,
      title: tmp.title,
      key: tmp.key,
      value: tmp.key,
      disabled: !tmp.parentid,
      children: tmp.children,
      parentName: tmp.parentName,
    };
    newTreeData.push(obj);
  });
  return newTreeData;
};

// 员工树部门禁选以及数据权限禁选
export const recursionOrgOptionDepData = (treeData, disableList = []) => {
  const newTreeData = [];
  let obj = {};
  treeData.forEach((item) => {
    const tmp = { ...item };
    if (tmp.children && tmp.children.length > 0) {
      // 内部调用即递归
      tmp.children = recursionOrgOptionDepData(tmp.children, disableList);
    }

    obj = {
      ...tmp,
      disabled: tmp.type == 'dep' || disableList.includes(tmp.id),
    };
    newTreeData.push(obj);
  });
  return newTreeData;
};


export function addChecked (data) {
  data.forEach((item) => {
    item.checked = false;
    if (item.options && item.options.length > 0) {
      addChecked(item.options)
    }
  });
  return data
}

/**
 * 递归查找树形结构中指定名称的节点
 * @param treeData
 * @param name
 * @param property
 * @returns {*}
 */
export function findTreeItemByName (treeData = [], name = '', property = '') {
  function _find (node, name) {
    if (node[property] === name) { return node }

    if (node.children && node.children.length) {
      for (const child of node.children) {
        const result = _find(child, name)
        if (result) { return result }
      }
    }
    return null
  }

  return _find({ children: treeData }, name)
}
