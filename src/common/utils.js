import axios from 'common/axios';
import Cookie from 'js-cookie';
import history from 'common/history';
import AppStore from 'stores/AppStore';
import Reflux from 'reflux';
import { message, Button } from 'antd';
import { getApiUrl } from '../config';

export function isObjEqual(a, b) {
  if (a == null && b != null) {
    return false;
  }
  if (a != null && b == null) {
    return false;
  }
  if (a == null && b == null) {
    return true;
  }

  let aProps = Object.getOwnPropertyNames(a);
  let bProps = Object.getOwnPropertyNames(b);
  if (aProps.length != bProps.length) {
    return false;
  }
  for (let i = 0; i < aProps.length; i++) {
    let propName = aProps[i];

    if (a[propName] !== b[propName]) {
      return false;
    }
  }
  return true;
}

export function genUUID(len, radix) {
  let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  let uuid = [],
    i;
  radix = radix || chars.length;

  if (len) {
    for (i = 0; i < len; i++) {
      uuid[i] = chars[0 | (Math.random() * radix)];
    }
  } else {
    let r;
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join('');
}

export function genKey(len) {
  let key = [];
  let chars = '0123456789abcdef'.split('');
  for (let i = 0; i < len; i++) {
    key[i] = chars[0 | (Math.random() * chars.length)];
  }
  return key.join('');
}

export function polygonToGeoJson(scale, polygon = []) {
  const geoJson = {
    type: 'FeatureCollection',
    features: [],
  };

  polygon.forEach((item, index) => {
    geoJson.features.push({
      type: 'Feature',
      properties: {
        name: item.link?.label,
      },
      geometry: {
        type: 'Polygon',
        coordinates: [item.coord.map((vertex) => [vertex[0] / scale, 1 - vertex[1]])],
      },
    });
  });
  return geoJson;
}

// 保留n位小数后格式化输出
export function formatValue(value, n = 0) {
  if (typeof value !== 'number') {
    return '--';
  }
  if (n < 0) {
    n = 0;
  }
  if (typeof value === 'string') {
    value = parseFloat(value);
  }
  // return parseFloat(value.toFixed(n)).toLocaleString();
  return parseFloat(value.toFixed(n));
}

export function ts2str(ts, fmt) {
  // author: meizz
  if (!ts) {
    return '';
  }
  let date = new Date(ts);
  let o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return fmt;
}

export function flatten(input) {
  let array = [];
  let group = input;
  for (let i = 0; i < group.length; i++) {
    if (group[i] instanceof Array) {
      array = array.concat(this.flatten(group[i]));
    } else {
      array = array.concat(group[i]);
    }
  }
  return array;
}
let warningFlag = true;
export function apiCall(
  url,
  method,
  data,
  callback = null,
  download = {
    isExit: false,
    title: '',
  },
) {
  let accessToken = Cookie.get('weebot_cloud_token');
  let params = {};

  if (data) {
    if (method == 'GET' || method == 'DELETE') {
      let keys = Object.keys(data);
      keys.forEach((key) => {
        if (data[key] instanceof Object) {
          params[key] = JSON.stringify(data[key]);
        } else {
          params[key] = data[key];
        }
      });
    }
  }

  const whiteIgnore = [
    "/verifytoken",
    "/sysdict/options",
    "/dept/option",
    "appemployee/page",
    "/employee/option",
    "/employee/org_option",
    "/employee/company_and_employee_option",
    "/user/getProtectToken",
    "user/page",
    "user/changestatus",
    "user/getUserVOById",
    "user/add",
    "user/modify",
    "user/del",
    "menu/getMenusList",
    "menu/getH5MenuList",
    "/role/option",
    "role/option",
    "/role/getListByQuery",
    "role/page",
    "/role/modify",
    "role/get",
    "role/add",
    "role/modify",
    "role/del",
    "/role/export",
    "/employee_tag/page",
    "/employee_tag/add",
    "/employee_tag/modify",
    "/employee_tag/del",
    "/employee_tag/option",
    "/employee/change_tag",
    "/employee_tag/export",
    "/employee/getCurrentEmployeeDepartmentIdList",
    "/employee_tag_menu/page",
    "/employee_tag_menu/add",
    "/employee_tag_menu/del",
    "/employee_tag_menu/modify",
    "/employee_tag_menu/get",
    "/employee_tag_menu/getOptions",
    "/employee/updateEmployeeState",
    "/wxcp/sync",
    "/changepwd",
    "/appemployee/addOrModify",
    "/appemployee/get_dto",
    "/appemployee/getManualEmployeeIdList",
    "/appemployee/import/batch",
    "/appemployee/import/failList",
    "/appemployee/del",
    "/appemployee/import/update/telephone",
    "/frontend/insert",
    "/menu/getMenuList",
    "/role/page",
    "/role/del",
    "/role/get",
    "/role/add",
    "/role/modify",
    "/notifyMessage",
    "/notifyMessageEmployee/last",
    "/notifyMessageEmployee/read",
    "/notifyMessageEmployee",
    "/notifyMessageEmployee/readAll"
  ]

  const AppSteUser = AppStore.state.User;
  let baseURL = getApiUrl();

  const urlSplit = url.split('?')[0];
  if (AppSteUser && AppSteUser.kitMapList && whiteIgnore.indexOf(urlSplit) <= -1) {
    const kitItem = AppSteUser.kitMapList.find((item) => location.pathname.includes(item.kitKey));
    if (kitItem) {
      const { kitUrl, kitKey } = kitItem;
      if (kitKey == 'syswb' || kitKey == 'comwb') {
        baseURL = baseURL;
      } else {
        baseURL = `${baseURL}/${kitKey}`;
      }
    }
  }

  return new Promise((resolve, reject) => {
    axios({
      baseURL: baseURL,
      method: method,
      contentType: 'application/json;charset=UTF-8',
      headers: {
        Authorization: 'Bearer ' + accessToken,
      },
      url: url,
      data: data,
      params: params,
      responseType: download.isExit ? 'blob' : null,
    })
      .then((response) => {
        let result = response.data;
        if (download.isExit) {
          let reader = new FileReader();
          reader.readAsText(result);
          reader.onload = function (res) {
            try {
              let resData = JSON.parse(res.target.result); // 解析对象成功，说明是json数据
              reject(resData);
              message.error(resData.msg);
            } catch {
              // 如果是下载请求，前端模拟一个a标签直接下载
              let aTag = document.createElement('a'); // 生成一个A标签
              aTag.href = window.URL.createObjectURL(new Blob([response.data]));
              aTag.setAttribute('download', download.title);
              document.body.appendChild(aTag);
              aTag.click();
              URL.revokeObjectURL(aTag.href);
              document.body.removeChild(aTag);
              resolve();
            }
          };
          return;
        }

        if (result.retcode == -2 || result.retcode == 10002) {
          // 清空登录用户，记录最后一个位置，然后跳转到登录页

          Reflux.setGlobalState({ AppStore: { User: null } });
          const lastLocation = sessionStorage.getItem('weebot_cloud_web_lastLocation');
          if (!lastLocation) {
            sessionStorage.setItem(
              'weebot_cloud_web_lastLocation',
              location.pathname + location.search,
            );
          }
          history.push('/login');
        }

        if (result.retcode == 0) {
          if (callback) {
            callback?.success(result.result);
          } else {
            resolve(result.result);
          }
        } else {
          if (callback) {
            callback.fail?.(result);
          } else {
            reject(result);
          }
          message.error({
            content: (
              <span>
                {result.msg}
                {result.retcode == 22001 || result.retcode == 22002 ? (
                  <Button
                    style={{ marginLeft: '10px' }}
                    onClick={() => {
                      location.reload();
                    }}
                  >
                    刷新
                  </Button>
                ) : (
                  ''
                )}
              </span>
            ),
          });
        }
      })
      .catch((e) => {
        let error = e.response?.data ?? null;
        const status = e.response?.status;

        if (error || status) {
          if (error.retcode == -2 || error.retcode == 10002 || status == 401) {
            const lastLocation = sessionStorage.getItem('weebot_cloud_web_lastLocation');
            if (!warningFlag) {
              // 让token过期报错只提示一次
              Reflux.setGlobalState({ AppStore: { User: null } });
              if (!lastLocation) {
                sessionStorage.setItem(
                  'weebot_cloud_web_lastLocation',
                  location.pathname + location.search,
                );
              }
              // api错误码主动清除token
              Cookie.remove('weebot_cloud_token');
              history.push('/login');
              return false;
            }
            warningFlag = false;
            Reflux.setGlobalState({ AppStore: { User: null } });
            if (!lastLocation) {
              sessionStorage.setItem(
                'weebot_cloud_web_lastLocation',
                location.pathname + location.search,
              );
            }
            // api错误码主动清除token
            Cookie.remove('weebot_cloud_token');
            history.push('/login');
          }
        }

        if (status == 401) {
          error = {
            retcode: error.retcode ?? -1,
            msg: '登录过期，请重新登录。',
            error: e,
          };
        } else {
          error = {
            retcode: error.retcode ?? -1,
            msg: error.msg ?? 'HTTP通讯错误',
            error: e,
          };
        }

        if (callback) {
          callback.fail?.(error);
        } else {
          reject(error);
        }
        message.error(error.msg);
      });
  });
}

export function restOPCall(name, op, params) {
  let tag = name.toLowerCase();
  switch (op) {
    case 'getlist':
      return apiCall(tag + '/page', 'GET', params);
    case 'get':
      return apiCall(tag + '/get', 'GET', params);
    case 'add':
      return apiCall(tag + '/add', 'POST', params);
    case 'update':
      return apiCall(tag + '/modify', 'PUT', params);
    case 'del':
      return apiCall(tag + '/del', 'DELETE', params);
  }
}

export function getDictValue(dataset, key) {
  let match = AppStore.state.g_sysdict[dataset].find((item, index) => item[0] == key + '');
  if (match) {
    return match[1];
  } else {
    return null;
  }
}

export function compareArray(arr1, arr2) {
  if (arr1 == arr2) {
    return true;
  }
  if (arr1 == null || arr2 == null) {
    return false;
  }
  if (arr1.length != arr2.length) {
    return false;
  }

  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] != arr2[i]) {
      return false;
    }
  }
  return true;
}

export function openNewTab(url) {
  window.open((process.env.PUBLIC_PATH + url).replaceAll('//', '/'), '_blank');
}
