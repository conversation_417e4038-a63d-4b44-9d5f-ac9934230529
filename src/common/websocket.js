/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/14 09:50
 * @LastEditTime: 2022/03/15 11:26
 * @LastEditors: ZhongJunWei
 * @FilePath: \weebot_cloud_webfront\src\common\websocket.js
 * @Description: 'WebSocket即时通讯'
 */

let websocket, lockReconnect = false;

export function createWebSocket (url) {
  websocket = new WebSocket(url);
  console.log(websocket, 'websocket'); // 查看websocket当前状态
  websocket.onopen = function (event) {
    console.log('连接上 websocket 服务端了');
    heartCheck.reset().start();
  };
  websocket.onerror = function () {
    reconnect(url);
  };
  websocket.onclose = function (e) {
    console.log('websocket 断开: ' + e.code + ' ' + e.reason + ' ' + e.wasClean);
  };
  websocket.onmessage = function (event) {
    lockReconnect = true;
    // event 为服务端传输的消息，在这里可以处理
    console.log(event, 'onmessage');
  };
}

let heartCheck = {
  timeout: 60000, // 60秒
  timeoutObj: null,
  reset: function () {
    clearInterval(this.timeoutObj);
    return this;
  },
  start: function () {
    this.timeoutObj = setInterval(function () {
      // 这里发送一个心跳，后端收到后，返回一个心跳消息，
      // onmessage拿到返回的心跳就说明连接正常
      websocket.send('HeartBeat');
    }, this.timeout);
  }
};

export function reconnect (url) {
  if (lockReconnect) {return;}
  // 没连接上会一直重连，设置延迟避免请求过多
  let timer = setTimeout(function () {
    createWebSocket(url);
    lockReconnect = false;
    clearTimeout(timer);
  }, 4000);
}

// 关闭连接
export function closeWebSocket () {
  websocket && websocket.close();
}
