/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/23 16:03
 * @LastEditTime: 2023/04/19 11:28
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/common/xlsx.js
 * @Description: '导入导出'
 */
import Excel from 'exceljs';
import moment from 'moment';
import { saveAs } from 'file-saver';
import { getDictValue } from 'common/utils';

/**
 * @description: 导出表格
 * @param {Array} sheetArray 多表格数组
 * @param {Object} config 配置 {fileName:文件名}
 * @return {.xlsx}
 * @Author: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @Date: 2021/09/07 16:34
 */
export function exportExcelFile(sheetArray, config = {}) {
  // 写入文件
  let workbook = new Excel.Workbook();
  // 循环表格Sheet
  sheetArray.forEach((item, index) => {
    let worksheet = workbook.addWorksheet(`Sheet${index + 1}`);
    // 冻结首行
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    worksheet.columns = item.headers
      .filter((header) => header.dataIndex)
      .map((header) => {
        const width = header.width && header.width.replace('px', '');
        return {
          header: header.title,
          key: header.dataIndex,
          width: isNaN(width) ? 20 : width / 8,
          style: {
            alignment: {
              horizontal: header.align || 'left',
              vertical: 'middle',
              shrinkToFit: true,
              wrapText: true,
            },
          },
        };
      });
    worksheet.addRows(item.data);
    worksheet.columns.forEach((col) => {
      worksheet.getColumn(col.key).eachCell({ includeEmpty: true }, function (cell, rowNumber) {
        if (rowNumber == 1) {
          // 过滤首行
          return;
        }
        let newVal = cellValueFormat(col.key, cell.value);
        if (newVal !== undefined) {
          cell.value = newVal;
        } else {
          cell.value = '-';
        }
      });
    });
    // 首行加粗
    worksheet.getRow(1).font = { bold: true };
  });

  workbook.xlsx
    .writeBuffer()
    .then(function (buffer) {
      saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${config.fileName}`);
    })
    .catch((err) => {
      console.log(err);
    });
}

/**
 * @description: 单元格值格式化
 * @param {key} dataIndex
 * @param {value} value
 * @return {*}
 * @Author: ZhongJunWei
 * @Date: 2021/09/07 16:51
 */
export function cellValueFormat(dataIndex, value, cell) {
  if (value == null || value == undefined || value.length == 0) {
    return;
  }

  let newVal = value;
  switch (dataIndex) {
    case 'userTagList':
      let tags = [['业务标签'], ['自定义标签'], ['朋友圈标签']];
      value.forEach((item) => tags[item.tagType - 1].push(item.tagName));
      newVal = tags
        .filter((item) => item.length > 1)
        .map((item) => item.join('、').replace('、', '：'))
        .join('；\n');
      break;
    case 'birthday':
      newVal = moment(value).format('YYYY-MM-DD');
      break;
    case 'gender':
      newVal = getDictValue('SEX', value);
      break;
    case 'customerType':
      newVal = getDictValue('CUSTOMERTYPE', value);
      break;
    case 'groupName':
      newVal = value.replaceAll(',', '、');
      break;
    case 'parentType':
      newVal = getDictValue('OPERATION_PARENT_TYPE', value);
      break;
    case 'actType':
      newVal = getDictValue('ACTTYPE', value);
      break;
    case 'actState':
      newVal = getDictValue('ACTSTATE', value);
      break;
    case 'state':
      newVal = getDictValue('COMMODITYSTATUS', value);
      break;
    case 'status':
      newVal = getDictValue('USERSTATUS', value);
      break;
    case 'wechatBinding':
      newVal = getDictValue('WECHATBINDING', value);
      break;
    case 'operationType':
      newVal = getDictValue('OPERATION_TYPE', value);
      break;
  }
  return newVal;
}
