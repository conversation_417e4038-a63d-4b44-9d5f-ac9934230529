/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/07/03 17:15
 * @LastEditTime: 2025/08/19 14:59
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/common/xss.js
 * @Description: 'xss 自定义过滤规则'
 */

import xss from "xss"

const allowTags = [
  // 结构类
  "body",
  "html",
  "div",
  "span",
  "p",
  "br",
  "hr",
  "pre",
  "blockquote",

  // 标题类
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",

  // 文本样式
  "b",
  "strong",
  "i",
  "em",
  "u",
  "ins",
  "s",
  "del",
  "small",
  "sub",
  "sup",
  "mark",
  "code",
  "font",
  "q",
  "cite",
  "abbr",
  "time",

  // 列表
  "ul",
  "ol",
  "li",

  // 表格
  "table",
  "thead",
  "tbody",
  "tr",
  "td",
  "th",

  // 媒体
  "img",
  "video",
  "audio",
  "source",
  "track",
  "svg",
  "figure",
  "figcaption",

  // 交互
  "details",
  "summary",

  // 链接
  "a",
]
const whiteList = {}
allowTags.forEach((tag) => {
  whiteList[tag] = ["style"]
})

whiteList.a.push("href", "target", "rel")
whiteList.img.push("src", "alt", "data-href")
whiteList.video.push("src", "controls", "poster", "width", "height")
whiteList.source.push("src", "type")

const XSS = new xss.FilterXSS({
  whiteList,
  css: false, // 禁用内建 CSS 过滤器
  // 保留原始包装结构 （默认被 xss 过滤了 data-*）
  onTagAttr(tag, name, value, isWhiteAttr) {
    if (name.startsWith("data-")) {
      return `${name}="${xss.escapeAttrValue(value)}"`
    }
  },
  safeAttrValue(tag, name, value) {
    if (name === "style") {
      const clean = value
        .split(";")
        .map((rule) => rule.trim())
        .filter((rule) => {
          return (
            rule && !/expression|javascript:|url\(/i.test(rule) // 过滤掉危险内容
          )
        })
        .join("; ")
      return clean + (clean ? ";" : "")
    }
    // 其他属性继续默认处理
    return xss.safeAttrValue(tag, name, value)
  },
})

export function filterXSS(html) {
  return XSS.process(html)
}
