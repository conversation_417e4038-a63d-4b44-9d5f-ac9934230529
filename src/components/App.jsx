// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Route, Switch } from 'react-router-dom';
import {
  <PERSON><PERSON>, Button, Col, Drawer, Form, Image, Input, Layout, Menu, message, Modal, notification, Popover, Result, Row, Spin
} from "antd"
import {
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import history from 'common/history';
import Cookie from 'js-cookie';
import Icon from 'components/base/Icon';
import GeneralSelect from 'components/select/GeneralSelect';
import moment from 'moment';
import watermark from 'common/watermark';
// 资源类
import './App.less';
import less from 'less';
// 数据流
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';
import NavMenu from 'components/base/NavMenu';
import SysWorkBench from 'components/main/SysWorkBench';
import ComWorkBench from 'components/main/ComWorkBench';
import AccountCenter from 'components/main/AccountCenter';
import Wecom from 'components/main/Wecom';
import Pharmacy from 'components/main/Pharmacy';
import { genNavMenu, genSideMenu } from 'components/main/menu';
import { apiCall, genUUID } from 'common/utils';
import { QrCodeBase } from 'common/qrcode';
import {
  MobileOutlined,
  BgColorsOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { Themes, ThemesColor } from '../theme/variables.js';
import { removeInputEmpty } from 'common/regular';
import { ErrorBoundary } from "react-error-boundary";
import { versionFnMap } from "config";
import {FileHOC} from 'components/FileHOC/FileHOC';
import NotifyCenter from "components/NotifyCenter/home"
const { Header } = Layout;
const FormItem = Form.Item;

class App extends Reflux.Component {
  changePasswordForm = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.state = {
      popvisible: false,
      loading: false,
      popCodevisible: false,
      drawerVisible: false,
      colorCheckedIdx: 0,
      jwtToken: '',
      changePasswordVisible: false,
      changePasswordConfirmLoading: false,
      unreadCount: 0
    }
  }

  componentWillMount () {
    super.componentWillMount();
    AppActions.verifyLogin(() => {
      // 更新水印
      this.setWaterMark()
    });
    if (!this.state.g_sysdict) {
      AppActions.getSysDictOptions();
    }
    this.getProtectToken();

    let mark = true;
    document.addEventListener('click', () => {
      if (mark) {
        mark = false;
        let audio = document.createElement('audio');
        audio.src = 'audio/msgTip.mp3'
        //循环播放，如果不需要可注释
        audio.loop = "loop"
        audio.autoplay = "autoplay"
        audio.muted = "muted"
        document.body.appendChild(audio);
        let timer = setTimeout(() => {
          document.body.removeChild(audio)
          clearTimeout(timer)
        }, 1000);
      }
    });
  }

  componentDidMount () {
    let timer = setTimeout(() => {
      const indx = localStorage.getItem('themesIndex');
      this.handleThemColor(indx);
      clearTimeout(timer);
    }, 1000);
    // 添加水印
    this.setWaterMark()
    setInterval(() => {
      this.getLastMsg()
    }, 5000)
  }

  componentWillReceiveProps () {
    AppActions.verifyLogin(() => {
      // 更新水印
      this.setWaterMark()
    })
  }

  // 设置水印
  setWaterMark = () => {
    let userName = sessionStorage.getItem('weebot_cloud_web_username');
    (localStorage.getItem('watermark') != 'no' && userName) && watermark.set(userName + ' ' + moment().format('YYYY-MM-DD HH:mm:ss'));
  }

  renderRoute = (roleid) => {
    const { User } = this.state;
    return (
      <ErrorBoundary
        onError={(error, info) => {
          const { message, stack } = error;
          const data = {
            error: `${message}\n${stack}` // 将错误消息和堆栈跟踪转换为字符串
          }
          apiCall("/frontend/insert", "POST", data)
        }}
        fallbackRender={({ error, resetErrorBoundary }) =>
          <Result
            className='ErrorBoundary'
            status="500"
            title="发生错误"
            subTitle="页面渲染发生错误，请联系管理人员"
            extra={<Button type="primary" onClick={() => {
              resetErrorBoundary()
            }}>重新加载</Button>}
          />
        }
      >
        <Switch>
          <Route
            path={'/syswb'}
            render={(props) => (
              <SysWorkBench
                menuList={genSideMenu(roleid, 'syswb', User.menus)}
                {...props}
              />
            )}
          />
          <Route
            path={'/comwb'}
            render={(props) => (
              <ComWorkBench
                menuList={genSideMenu(roleid, 'comwb', User.menus)}
                {...props}
              />
            )}
          />
          <Route
            path={'/acccenter'}
            render={(props) => (
              <AccountCenter
                menuList={genSideMenu(roleid, 'acccenter', User.menus)}
                {...props}
              />
            )}
          />
          <Route
            path={'/wecom'}
            render={(props) => (
              <Wecom
                menuList={genSideMenu(roleid, 'wecom', User.menus)}
                {...props}
              />
            )}
          />
          <Route
            path={'/pharmacy'}
            render={(props) => (
              <Pharmacy
                menuList={genSideMenu(roleid, 'pharmacy', User.menus)}
                {...props}
              />
            )}
          />
        </Switch>
      </ErrorBoundary>
    );
  };

  onClickUserinfo = ({ item, key }) => {
    this.setState({ popvisible: false });
    switch (key) {
      case 'logout':
        AppActions.logout();
        this.handleThemColor(0);
        break;
      case 'userinfo':
        history.push('/acccenter/userinfo');
        break;
      case 'message':
        history.push('/acccenter/ticket');
        break;
      case 'ticket':
        history.push('/acccenter/ticket');
        break;
      case 'changePassword':
        this.setState({
          changePasswordVisible: true,
        });
        break;
    }
  };

  handleVisibleChange = (visible) => {
    this.setState({ popvisible: visible });
  };

  handleCodeChange = (visible) => {
    this.setState({ popCodevisible: visible });
  };

  handleClickCode = () => {
    this.setState({ popCodevisible: false });
  };

  handleCompanyChange = (companyId) => {
    const { User } = this.state;
    if (User?.activeCompanyId == companyId) {
      return;
    }
    this.setState({ loading: true });
    apiCall('/switchcom', 'PUT', { companyId: companyId }).then((retdata) => {
      let user = {
        ...retdata,
        menus: retdata.menuList,
        moduleVersionMap: retdata.moduleVersionMap || {},
      };
      AppActions.setState({ User: user });
      // location.reload();
      this.setState({ loading: false });
    });
  };

  // 设置整体主题颜色
  handleSetColor = () => {
    this.setState({
      drawerVisible: true,
    });
  };
  onDrawerClose = () => {
    this.setState({
      drawerVisible: false,
    });
  };
  handleThemColor = (index) => {
    // 调用 `less.modifyVars` 方法来改变变量值
    less.modifyVars(Themes[index]).then((res) => {
      localStorage.setItem('themesIndex', index);
    });
    this.setState({
      colorCheckedIdx: index,
    });
  };

  // 获取当前用户加密的token
  getProtectToken () {
    if (!Cookie.get('weebot_cloud_token')) {
      return false;
    }
    apiCall('/user/getProtectToken', 'GET')
      .then((res) => {
        this.setState({
          jwtToken: res,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  handleRead = (current) => {
    apiCall("/notifyMessageEmployee/read", "POST", [current.id]).then(_ => {
      notification.close(current.id)
    })
  }

  getLastMsg = () => {
    apiCall("/notifyMessageEmployee/last", "GET").then(res => {
      // this.setState({ unreadCount: res.unreadCount })
      if (!res.id) {
        return
      }
      let title = ""
      switch (res.type) {
        case "AGENT_HANDLE_LIMIT":
          title = "坐席单人处理量抵达上限"
          break
      }
      notification.open({
        key: res.id,
        message: title,
        description: `${res.createTime}, ${res.content}`,
        btn: (<Button type="primary" onClick={() => this.handleRead(res)}>
            已读
          </Button>)
      })
    })
  }
  handleClickNotify = () => {
    this.setState({openNotify: true})
  }


  render () {
    const {
      User,
      g_sysdict,
      popCodevisible,
      popvisible,
      loading,
      drawerVisible,
      colorCheckedIdx,
      jwtToken,
      changePasswordVisible,
      changePasswordConfirmLoading,
    } = this.state;
    const { collapsed } = this.stores[0]?.state;

    if (User == undefined) {
      return null;
    }

    if (!g_sysdict) {
      return null;
    }

    const popoverContent = (
      <Menu
        selectable={false}
        onClick={this.onClickUserinfo}
        style={{ border: 'unset', textAlign: 'center' }}
      >
        {/* <Menu.Item key="userinfo">账号安全</Menu.Item> */}
        {/* <Menu.Item key="message">我的消息</Menu.Item> */}
        {/* <Menu.Item key="ticket">我的工单</Menu.Item> */}
        <Menu.ItemGroup title={User.account}>
          <Menu.Item key="changePassword">
            修改密码
          </Menu.Item>
          <Menu.Item key="logout">退出</Menu.Item>
        </Menu.ItemGroup>
      </Menu>
    );

    const image = QrCodeBase({
      url: `${location.origin}/m/employee/login?token=${jwtToken}`,
    });

    const popoverCordContent = (
      <div style={{ width: "130px" }}>
        <FileHOC src={image}>
          {(url) => (
            <Image
              width={130}
              src={url}
              preview={url}
              onClick={this.handleClickCode}
            ></Image>
          )}
        </FileHOC>
        <div>微信扫码访问移动端</div>
      </div>
    );

    const themeColor = ThemesColor.map((item, index) => (
      <div
        className="theme-color"
        key={index}
        style={{ backgroundColor: item, textAlign: 'center' }}
        onClick={() => {
          this.handleThemColor(index);
        }}
      >
        {colorCheckedIdx == index && (
          <CheckOutlined style={{ color: '#fff' }} />
        )}
      </div>
    ));

    const changePasswordModal = (
      <Modal
        visible={changePasswordVisible}
        width={400}
        title="修改密码"
        destroyOnClose
        confirmLoading={changePasswordConfirmLoading}
        onOk={() => {
          this.changePasswordForm.current.validateFields().then((formData) => {
            const { oldPwd, newPwd, repeatNewPwd } = formData;
            this.setState({
              changePasswordConfirmLoading: true,
            });
            const data = {
              oldPwd:
                genUUID(8) + window.btoa(oldPwd).split('')
                  .reverse()
                  .join(''),
              newPwd:
                genUUID(8) + window.btoa(newPwd).split('')
                  .reverse()
                  .join(''),
              repeatNewPwd:
                genUUID(8) +
                window.btoa(repeatNewPwd).split('')
                  .reverse()
                  .join(''),
            };
            apiCall('/changepwd', 'POST', data)
              .then((res) => {
                message.success('修改成功！');
                this.setState({
                  changePasswordVisible: false,
                  changePasswordConfirmLoading: false,
                });
              })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                this.setState({
                  changePasswordConfirmLoading: false,
                });
              });
          });
        }}
        onCancel={(e) => {
          e.preventDefault();
          e.stopPropagation();
          this.setState({
            changePasswordVisible: false,
            changePasswordConfirmLoading: false,
          });
        }}
      >
        <Form
          labelCol={{ span: 7 }}
          wrapperCol={{ span: 17 }}
          ref={this.changePasswordForm}
        >
          <FormItem
            name="oldPwd"
            label="原密码"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入原密码' }]}
          >
            <Input.Password
              maxLength={16}
              placeholder="请输入原密码"
              allowClear
            />
          </FormItem>

          <FormItem
            name="newPwd"
            label="新密码"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入新密码' }]}
          >
            <Input.Password
              maxLength={16}
              placeholder="请输入新密码"
              allowClear
            />
          </FormItem>

          <FormItem
            name="repeatNewPwd"
            label="重复新密码"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请确认新密码' }]}
          >
            <Input.Password
              maxLength={16}
              placeholder="请确认新密码"
              allowClear
            />
          </FormItem>
        </Form>
      </Modal>
    );

    return (
      <Layout>
        <Header>
          <Row justice="space-between">
            <Col span={14}>
              <div id="logo" style={{fontSize: '24px'}}>
                {versionFnMap.system_ui().system_title}
              </div>
              {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
                className: 'trigger',
                onClick: () => {
                  AppActions.setCollapsed(!collapsed);
                },
              })}
              <NavMenu
                menuList={genNavMenu(User.roleid, User.menus.map(item => ({
                  ...item,
                  hidden: !versionFnMap.system_ui().system_navMenu_wecomFlag
                })))}
                {...this.props}
              />
            </Col>
            <Col span={10}>
              <div className="upright" mode="horizontal">
                <Popover
                  placement="bottom"
                  content={popoverCordContent}
                  visible={popCodevisible}
                  onVisibleChange={this.handleCodeChange}
                >
                  {location.pathname.includes("/mktsuite") ? (<div
                      style={{
                        cursor: "pointer", display: "flex", flexDirection: "column", marginRight: "15px"
                      }}
                    >
                      <MobileOutlined
                        style={{ fontSize: "20px", verticalAlign: "middle" }}
                      />
                      <span style={{ fontSize: "12px", lineHeight: "15px" }}>
                        移动端
                      </span>
                    </div>) : ("")}
                </Popover>
                <Badge count={this.state.unreadCount} offset={[-8, -6]} size="small" onClick={() => this.handleClickNotify()}>
                  <BellOutlined style={{
                    fontSize: "20px", verticalAlign: "middle", cursor: "pointer", color: "#fff"
                  }} />
                </Badge>

                <BgColorsOutlined
                  style={{
                    marginLeft: "15px",
                    marginRight: "15px",
                    fontSize: "20px",
                    verticalAlign: "middle",
                    cursor: "pointer"
                  }}
                  onClick={this.handleSetColor}
                />
                {/* 营销套件展示 系统管理员显示切换;企业管理员或企业普通用户，就显示名称就可以了 */}
                {User.roleType == 1 ? (
                  <span style={{ marginRight: '15px' }}>
                    切换{versionFnMap.system_ui().system_companyFlag}
                    <GeneralSelect
                      width={250}
                      schema="company"
                      value={
                        User?.activeCompanyId ? User.activeCompanyId + '' : ''
                      }
                      onChange={this.handleCompanyChange}
                    />
                  </span>
                ) : (
                  <span style={{ marginRight: '15px' }}>
                    {versionFnMap.system_ui().system_companyFlag}
                    {User.companyName}
                  </span>
                )}
                <Popover
                  placement="bottomLeft"
                  content={popoverContent}
                  visible={popvisible}
                  onVisibleChange={this.handleVisibleChange}
                >
                  <span className="userinfo">
                    <Icon iconid="user-circle" size={24} />
                    &nbsp;
                    <span>{User.userName}</span>
                  </span>
                </Popover>
              </div>
            </Col>
          </Row>
        </Header>

        {loading ? (
          <Spin
            tip="切换企业中..."
            size="large"
            style={{ backgroundColor: 'white' }}
          >
            <div style={{ height: '500px', width: '100%' }} />
          </Spin>
        ) : (
          this.renderRoute(User.roleid)
        )}
        <Drawer title="消息中心" width={1000} placement="right" open={this.state.openNotify} onClose={() => this.setState({ openNotify: false })}>
          {
            this.state.openNotify && <NotifyCenter />
          }
        </Drawer>

        <Drawer
          title="主题色设置"
          placement="right"
          onClose={this.onDrawerClose}
          visible={drawerVisible}
        >
          {themeColor}
        </Drawer>

        {changePasswordModal}
      </Layout>
    );
  }
}

export default App;
