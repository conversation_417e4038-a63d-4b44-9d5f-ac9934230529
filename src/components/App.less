@import "~antd/dist/antd.less"; // 引入官方提供的 less 样式入口文件
@import "~style/animate.less";
@import "~style/editorView.less"; // wangEditor 5 内容预览样式
@import "~@wangeditor/editor/dist/css/style.css"; // wangEditor 5 富文本样式

//整体布局
#root {
  > .ant-layout {
    //sider收缩时固定menu,无滚动条,sider未收缩时固定menu,有滚动条
    .ant-layout-sider .ant-menu-root.module-menu {
      z-index: 20;
      height: calc(~"100vh - 64px");
      left: 0px;
      top: 64px;
    }

    .ant-layout-sider:not(.ant-layout-sider-collapsed)
      .ant-menu-root.module-menu {
      width: 210px;
      height: calc(~"100vh - 64px");
      overflow-y: auto;
      overflow-x: hidden;

      /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
      &::-webkit-scrollbar {
        width: 6px;
      }

      /*定义滚动条的轨道颜色、内阴影及圆角*/
      &::-webkit-scrollbar-track {
        border-radius: 6px;
      }

      /*定义滑块颜色、内阴影及圆角*/
      &::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: rgba(144, 147, 153, 0.3);
      }
    }

    .ant-layout-sider-collapsed .min-hidden {
      opacity: 0;
    }
  }

  .ant-layout-header {
    padding: 0 0px;
    background: #ffffff;

    #logo {
      float: left;
      width: 209px;
      text-align: center;
    }

    .trigger {
      display: inline-block;
      float: left;
      padding: 0 24px;
      font-size: 18px;
      line-height: 64px;
      cursor: pointer;
      transition: color 0.3s;
    }

    .ant-menu {
      border: unset;
    }

    .upright {
      margin-right: 24px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      white-space: nowrap;
    }

    //用户信息
    .userinfo {
      // float: right;

      img {
        vertical-align: middle;
        width: 40px;
        height: 40px;

        & + span {
          display: inline-block;
          width: 100px;
          max-width: 100px;
          text-overflow: ellipsis;
          overflow: hidden;
          vertical-align: top;
          margin-left: 10px;
          white-space: nowrap;
        }
      }
    }
  }

  .ant-layout-content {
    // min-width: 960px;
    padding: 10px;
    height: calc(~"100vh - 64px");
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  //让type为card的Tab组件边框更明显
  .ant-tabs.ant-tabs-card > .ant-tabs-bar {
    border-color: #666;

    .ant-tabs-tab {
      border-color: #666;
    }
  }
}

.ant-spin-nested-loading > div > .ant-spin {
  //取消max-height上限，使得spin位于中间
  max-height: none;
}

.userinfo-popover-content {
  width: 150px;

  .mini-card {
    border: 1px solid;
    border-radius: 5px;
    padding-top: 10px;
    padding-left: 5px;
  }

  p {
    line-height: 1em;
  }

  .ant-divider {
    margin: 1em 0px 0px;
  }

  .ant-menu-item-divider {
    margin-top: 1em;
    background-color: #ffffff;
  }
}

//表格最后一行操作按钮间距
.ant-table td a:not(:last-child) {
  margin-right: 16px;
}

//Spin居中
.ant-spin {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 自定义添加Table空值字符
.ant-table-cell:empty:after,
.ant-table-cell span:empty:after {
  content: "-";
}

// 解决Table复选框有值问题
.ant-table-cell .ant-checkbox-inner::after {
  content: "" !important;
}

// 删除input自动填充背景色
input:-webkit-autofill {
  background-color: transparent;
  transition: background-color 50000s ease-in-out 0s;
}

.pre-wrap {
  .phone-box {
    .link-card {
      width: 198px;
    }
  }
}

.ant-tooltip {
  .ant-tooltip-inner {
    white-space: pre-line;
  }
}

.ant-select-dropdown {
  .ant-select-tree {
    .ant-select-tree-title {
      word-break: break-all;
    }
  }
}

// 禁用元素对鼠标事件做出反应。
.nonePointer {
  pointer-events: none !important;
}

// 表格选中
.l-table-row-active {
  td {
    background-color: #dcf4ff !important;

    &:hover {
      background-color: #dcf4ff !important;
    }
  }
}

// 文字高亮
.Text-Highlight {
  color: #ff5500;
}

// 可视化卡片
.ant-chartCard {
  .ant-card-body {
    min-height: 448px;
    position: relative;

    .ant-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

// 自定义滚动条
.custom-scrollbar {
  overflow-y: auto;

  div {
    overflow: unset;
  }

  /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /*定义滚动条的轨道颜色、内阴影及圆角*/
  &::-webkit-scrollbar-track {
    border-radius: 6px;
  }

  /*定义滑块颜色、内阴影及圆角*/
  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: #c0c0c0;

    &:hover {
      background-color: #7c7c7c;
    }
  }
}

// 错误边界
.ant-layout:has(> .ErrorBoundary) {
  position: relative;
  height: 100vh;

  .ErrorBoundary {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

// 优化upload-icon样式
.ant-upload {
  .anticon {
    margin: 0 auto;
  }
}

// 表单动态减少按钮样式
.dynamic-delete-button {
  position: relative;
  top: 4px;
  margin: 0 8px;
  color: #999 !important;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.dynamic-delete-button:hover {
  color: #777 !important;
}

.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.ant-typography-ellipsis-multiple-line,
.ant-typography-ellipsis-single-line {
  overflow: hidden !important;
}
