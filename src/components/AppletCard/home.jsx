/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/28 11:40
 * @LastEditTime: 2025/05/06 17:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/AppletCard/home.jsx
 * @Description: '小程序卡片'
 */

import { Image, Typography } from "antd"
import { FileHOC } from "components/FileHOC/FileHOC"
import React from "react"
import "./home.less"

const { Paragraph } = Typography

const AppletCard = (props) => {
  const { data = {}, key = "1", mask = null, onClick } = props
  data.image =
    typeof data.fileId == "object" ? data.fileId[0] : data.fileId || data.image

  return (
    <div
      className="applet-card"
      key={key}
      onClick={() => {
        onClick?.()
      }}
    >
      <Paragraph ellipsis={{ rows: 1 }}>
        {data.miniProgram?.title || data.title}
      </Paragraph>

      <FileHOC src={data.miniProgram?.fileId || data.image || "error"}>
        {(url) => <Image preview={false} src={url} />}
      </FileHOC>
      <div className="footer">
        <img src="images/applet.png" />
        小程序
      </div>
      {mask}
    </div>
  )
}

export default AppletCard
