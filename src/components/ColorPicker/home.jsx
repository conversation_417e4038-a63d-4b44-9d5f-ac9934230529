/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/20 18:02
 * @LastEditTime: 2022/12/27 14:47
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\components\ColorPicker\home.jsx
 * @Description: '颜色选择器'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { SketchPicker } from 'react-color';
import './home.less';

const ColorPicker = forwardRef((props, ref) => {
  const { onChangeColor, handleClosePicker } = props;

  useImperativeHandle(ref, () => ({
    handleSetColor,
  }))

  const [color, setColor] = useState("#aabbcc");

  useEffect(() => { }, []);

  const onClosePicker = () => {
    handleClosePicker();
  };

  const handleSetColor = (c) => {
    setColor(c);
  }

  return (
    <div className='ColorPicker'>
      {/* <CloseOutlined className='colorPicker-close' onClick={onClosePicker} /> */}
      <SketchPicker color={color} onChangeComplete={(c) => {
        setColor(c.hex);
      }}
        styles={{
          picker: {
            width: '200px',
            padding: '10px 10px 0px',
            boxSizing: 'initial',
            background: 'rgb(255, 255, 255)',
            borderRadius: '4px',
            boxShadow: 'unset'
          }
        }}
      />
      <div className='pickerBtns'>
        <Button type="primary" onClick={onClosePicker} style={{ marginRight: '20px' }}>取消</Button>
        <Button type="primary" onClick={() => {
          onChangeColor(color);
        }}>确定 </Button>
      </div>
    </div>
  );
});

export default ColorPicker;
