import React, { useEffect, useRef, useState } from "react"
import {apiCall} from "common/utils";
import { <PERSON>ton, Collapse, Drawer, Empty, Space, Spin } from "antd"
import TypeMessage from "../../view/wecom/DigitalHuman/AgentSeat/comps/TypeMessage/home";
import {withRouter} from "react-router-dom";
import './index.less'
import { CaretRightOutlined } from "@ant-design/icons"

const { Panel } = Collapse

const TypeMessageDrawer = (props) => {
    const {
        visible = false,
        title = '标题',
        placement = 'right',
        onCancel,
        id = null,
        footer = true,
        actions,
    } = props.params;
    const [loading, setLoading] = useState(false);
    const [nowTitle, setNowTitle] = useState('标题');
    const [messageList, setMessageList] = useState([]);
    const [hitList, setHitList] = useState([]);
    const [msgId, setMsgId] = useState("");
    const [qualityIdList, setQualityIdList] = useState([]);
    const [qualityId, setQualityId] = useState(null);

    const [conversationId, setConversationId] = useState(null)
    const [currentId, setCurrentId] = useState(null)
    // 存储语音对象
    const [amrObj, setAmrObj] = useState(null);

    const [dialogIdList, setDialogIdList] = useState([])

    const [currentConversation, setCurrentConversation] = useState(null)


    const getIdList = () => {
        apiCall('/riskControl/riskControlLog/getRiskControlLogIdList', 'GET', {}).then(res => {
            setDialogIdList(res)
            // if (!currentId) {
            //     setCurrentId(res.find(i => i === id))
            // }
        })
    }


    useEffect(async () => {
        if (!id) {
          return
        }
        const data = await primaryIdToConversationId(id)
        setCurrentId(id)
        setConversationId(data?.conversationId)
        await getListMsg(data?.conversationId)

        getMsgDateByConversionId(data?.conversationId)
    }, [id])

    useEffect(async () => {
        getIdList()
        setNowTitle(title)
        const data = await primaryIdToConversationId(currentId)
        if (!data) {
          return
        }
        actions.change(data)
        setConversationId(data.conversationId)
        getMsgDateByConversionId(data.conversationId)
        await getListMsg(data.conversationId)
    }, [currentId])

    useEffect(() => {
        if (currentConversation) {

            actions.change(currentConversation)
            onLocate(currentConversation)
        }
    }, [currentConversation])


    // useEffect(async () => {
    //
    //     if (currentId) {
    //         getIdList()
    //         setNowTitle(title)
    //         const data = await primaryIdToConversationId(currentId)
    //         actions.change(data)
    //         await getListMsg(data.conversationId)
    //     } else {
    //         setLoading(false);
    //         setMessageList([])
    //         setHitList([])
    //         setMsgId("")
    //     }
    // }, [visible, currentId]);


    const primaryIdToConversationId = async (id) => {
        if (!id) {
            return null
        }
        const data = await apiCall(`/riskControl/riskControlLog/${id}`, 'GET', {})

        //setMsgId()
        setCurrentConversation(data)
        return data
    }

    const getListMsg = async (conversationId) => {
        setLoading(true)
        setMessageList([])
        const data = {
            conversationId,
        };
        await apiCall("/agentSeat/msg/listMsg", "GET", data)
            .then((res) => {
                setMessageList(res)
            })
            .catch((err) => {

            })
            .finally(() => {
                setTimeout(() => {
                    setLoading(false)
                }, 1000)
            });
    };

    // 获取质检结果

    // 获取对话列表
    // 消息定位
    const onLocate = (item) => {
        const {msgId} = item;
        setTimeout(() => {
            setMsgId(msgId)
            if (!msgId) {
                setTimeout(() => {
                  document.querySelector(`#dialogPanel${conversationId}`)?.scrollIntoView()
                }, 2000)
            }
        }, 1000)
    }

    useEffect(() => {
        if (msgId) {
            // 初始化
            let locateBoxList1 = document.querySelectorAll(".wi-messages-wrap");
            let locateBoxList2 = document.querySelectorAll(".wi-type-system");
            for (let index = 0; index < locateBoxList1.length; index++) {
                const item = locateBoxList1[index];
                item.style.background = 'unset'
            }
            for (let index = 0; index < locateBoxList2.length; index++) {
                const item = locateBoxList2[index];
                item.style.background = 'unset'
            }
            let locateBox = document.querySelector(`.msgId-${msgId}`);
            if (!locateBox) {
                setTimeout(() => {
                    locateBox = document.querySelector(`.msgId-${msgId}`);
                    if (locateBox) {
                        locateBox.style.background = '#f5cb97';
                        document.querySelector(".Conversation-Drawer-Container .ant-drawer-body").scrollTop = locateBox.offsetTop - locateBox.clientHeight - 200;
                    }
                }, 1500)
            } else {
                locateBox.style.background = '#f5cb97';
                document.querySelector(".Conversation-Drawer-Container .ant-drawer-body").scrollTop = locateBox.offsetTop - locateBox.clientHeight - 200;
            }
        }
    }, [msgId]);

    const prevDisabled = () => {
        return dialogIdList[0] === currentId
    }

    const nextDisabled = () => {
        return dialogIdList[dialogIdList.length - 1] === currentId
    }
    const [msgLoading, setMsgLoading] = useState(false)
    const [dialogList, setDialogList] = useState([])
    const [dialogActiveKey, setDialogActiveKey] = useState([])
    const historyMsg = useRef([])

  function getMsgDateByConversionId (conversationId) {
    const data = {
      conversationId: conversationId || props.params.conversationId,
    }
    apiCall("/agentSeat/msg/listConversationBase", "GET", data).then((res) => {
      setDialogList(res)
      setDialogActiveKey([data.conversationId])
      // isFirst.current = true
    }).finally(_ => {
      setTimeout(() => {
        // isFirst.current = false
      }, 5000)
    })
  }
    // 获取某个对话的消息列表
    const fetchSessionMessage = (params = {}) => {

      const { conversationId } = params
      const data = {
        conversationId: conversationId,
      }
      const messageList = dialogList.find(
        (c) => c.conversationId.toString() === conversationId.toString(),
      )?.messageList
      if (messageList?.length) {
        return
      }
      setMsgLoading(true)
      apiCall("/agentSeat/msg/listMsg", "GET", data)
        .then((res) => {
          const newDialogList = JSON.parse(JSON.stringify(dialogList))
          newDialogList.forEach((item) => {
            if (String(item.conversationId) === String(conversationId)) {
              item.messageList = res
            }
          })
          setDialogList(newDialogList)
        })
        .finally((_) => {
          setMsgLoading(false)
          // if (isFirst.current) {
          //   isFirst.current = false
          //   setTimeout(() => {
          //     // scrollToBottom(".ant-drawer-body")
          //   }, 1000)
          // }
        })
    }
    useEffect(() => {
      if (!dialogActiveKey?.length) {
        return
      }
      fetchSessionMessage({
        conversationId: dialogActiveKey[dialogActiveKey.length - 1],
      })
    }, [dialogActiveKey])

    const footerDom = <Space>
        <Button type="primary" loading={loading} disabled={prevDisabled()} onClick={() => {
            const i = dialogIdList.findIndex(value => value === currentId)
            const id = dialogIdList[i - 1] || null;
            setCurrentId(id)
            actions.pre?.(currentConversation)
        }}>上一条</Button>
        <Button type="primary" loading={loading} disabled={nextDisabled()} onClick={() => {
            const i = dialogIdList.findIndex(value => value === currentId)
            const id = dialogIdList[i + 1] || null;
            setCurrentId(id)
            actions.next?.(currentConversation)
        }}>下一条</Button>
    </Space>

    return (
        <Drawer
            className='Conversation-Drawer-Container'
            title={nowTitle}
            placement={placement}
            width={props.width || 550}
            open={visible}
            destroyOnClose
            mask={false}
            push={{distance: 378}}
            onClose={() => {
                onCancel?.();
            }}
            footer={footer && footerDom}
        >
            <Spin spinning={loading}>
              <Collapse
                className="wi-messages-dialog-list"
                expandIcon={({ isActive }) => (
                  <CaretRightOutlined rotate={isActive ? 90 : 0} />
                )}
                ghost
                destroyInactivePanel
                defaultActiveKey={dialogActiveKey}
                activeKey={dialogActiveKey}
                onChange={(key) => {
                  historyMsg.current.push(...key)
                  setDialogActiveKey(key)
                }}
              >
                {dialogList.map((item, index) => (
                  <Panel
                    id={`dialogPanel${item.conversationId}`}
                    header={`对话 ${item.time}`}
                    key={item.conversationId}
                  >
                    <Spin spinning={msgLoading} />
                    {item.messageList?.length ? (
                      <>
                        {item.messageList.map((atem, andex) => (
                          <TypeMessage
                            key={andex}
                            item={atem}
                            preview={true}
                            onTogether={null}
                            amrObj={amrObj}
                            onVoice={(data) => {
                              setAmrObj({ ...data })
                            }}
                          />
                        ))}
                      </>
                    ) : (
                      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                  </Panel>
                ))}
              </Collapse>
                {props.params.children?.(currentConversation)}
            </Spin>
        </Drawer>
    );
};

export default withRouter(TypeMessageDrawer);
