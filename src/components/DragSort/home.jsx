/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/15 15:38
 * @LastEditTime: 2023/02/03 16:03
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\components\DragSort\home.jsx
 * @Description: '拖拽排序组件'
 */
import React, { useRef, memo } from 'react';
import { useDrop, useDrag } from 'react-dnd';
import './home.less';
// 拖拽排序
const DragSort = (props) => {
  const { id, index, changePosition, children } = props;
  const ref = useRef(null);
  const type = 'DragDropBox';

  // // 因为没有定义收集函数，所以返回值数组第一项不要
  // const [{ isOver, dropClassName }, drop] = useDrop({
  //   accept: type, // 只对useDrag的type的值为DragDropBox时才做出反应
  //   collect: (monitor) => {
  //     const { index: dragIndex } = monitor.getItem() || {};
  //     if (dragIndex === index) {
  //       return {};
  //     }

  //     return {
  //       isOver: monitor.canDrop(),
  //       dropClassName: 'drop-over-downward',
  //     };
  //   },
  //   hover: (item, monitor) => {
  //     // 这里用节流可能会导致拖动排序不灵敏
  //     if (!ref.current) {return;}
  //     const dragIndex = item.index;
  //     const hoverIndex = index;
  //     if (dragIndex === hoverIndex) {return;} // 如果回到自己的坑，那就什么都不做
  //     changePosition(dragIndex, hoverIndex); // 调用传入的方法完成交换
  //     item.index = hoverIndex; // 将当前当前移动到Box的index赋值给当前拖动的box，不然会出现两个盒子疯狂抖动！
  //   },
  // });

  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      changePosition(item.index, index);
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type,
    item: {
      id,
      index,
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(), // css样式需要
    }),
  });
  drag(drop(ref));

  return (
    // ref 这样处理可以使得这个组件既可以被拖动也可以接受拖动
    <div
      ref={ref}
      // className={`tag-item ${isOver ? dropClassName : ''}`}
      className='tag-item'
      key={index}
    >
      {children}
    </div>
  );
};
export default memo(DragSort);
