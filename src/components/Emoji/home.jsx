/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/05 16:32
 * @LastEditTime: 2023/06/21 15:01
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\components\Emoji\home.jsx
 * @Description: 'Emoji组件'
 */

import React, { useEffect } from "react";
import { CloseOutlined } from "@ant-design/icons";
import data from "@emoji-mart/data";
// import Picker from "@emoji-mart/react";
import { Picker } from "emoji-mart";
import i18nZh from "common/emoji-mart-i18n-zh.json";
import "./home.less";

const Emoji = (props) => {
  const { handleEmojiSelect, handleCloseEmoji, navPosition = "top" } = props;

  useEffect(() => {
    const pickerOptions = {
      data,
      locale: "zh",
      i18n: i18nZh,
      previewPosition: "none",
      categories: ["frequent", "people"],
      searchPosition: "none",
      emojiSize: 18,
      navPosition,
      onEmojiSelect: (selected) => {
        console.log(selected, "selected");
        handleEmojiSelect(selected);
      },
    };
    const picker = new Picker(pickerOptions);
    document.querySelector(".EmojiPickerWrap").appendChild(picker);
  }, []);

  const onCloseEmoji = () => {
    handleCloseEmoji();
  };

  const onEmojiSelect = (e) => {
    handleEmojiSelect(e);
  };

  return (
    <div className="EmojiPickerWrap">
      <CloseOutlined className="emojiPicker-close" onClick={onCloseEmoji} />
      {/* <Picker
        data={data}
        i18n={i18nZh}
        categories={["frequent", "people"]}
        previewPosition="none"
        searchPosition="none"
        locale="zh"
        emojiSize={18}
        onEmojiSelect={onEmojiSelect}
        navPosition={navPosition}
      /> */}
    </div>
  );
};

export default Emoji;
