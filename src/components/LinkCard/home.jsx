/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/29 15:42
 * @LastEditTime: 2025/07/28 10:39
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/LinkCard/home.jsx
 * @Description: '链接卡片'
 */

import { Row, Col, Image, Typography } from "antd"
import {FileHOC} from 'components/FileHOC/FileHOC';
import "./home.less"

const { Paragraph } = Typography;

const LinkCard = (props) => {
  const { overStyle = {}, key = '1', data = {}, isLink = true, mask = null, onClick } = props;
  data.imageUrl =
    data.images?.[0] ||
    data.fileId?.[0] ||
    data.shareImage ||
    data.imageUrl ||
    data.image ||
    ""

  return (
    <div
      className="link-card"
      key={key}
      onClick={() => {
        if (isLink && data.url) {
          window.open(data.url, "_blank")
          return false
        }
        onClick?.()
      }}
      style={isLink ? { cursor: "pointer", ...overStyle } : { ...overStyle }}
    >
      <Paragraph strong ellipsis={{ rows: 2 }}>
        {data.title}
      </Paragraph>
      <Row justify="space-between">
        <Col span={16}>
          <Paragraph
            style={{ fontSize: "12px", lineHeight: "18px" }}
            ellipsis={{ rows: 3 }}
          >
            {data.description}
          </Paragraph>
        </Col>
        <Col>
          <FileHOC src={data.imageUrl}>
            {(url) => (
              <Image
                width={54}
                src={url}
                fallback="images/fallbackImg.png"
                preview={false}
              />
            )}
          </FileHOC>
        </Col>
      </Row>
      {mask}
    </div>
  )
};

export default LinkCard;
