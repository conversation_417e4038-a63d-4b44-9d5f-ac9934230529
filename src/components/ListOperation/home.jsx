import { EllipsisOutlined } from "@ant-design/icons";
import { Dropdown, Menu } from "antd";
import React from "react";

/**
 * 动态操作列
 * 参数：
 *   opts：操作数组，数组的每个元素为一个操作的信息，包括字段onClick（操作点击事件）、name（操作名称）
 *   maxDisplay：最大显示的操作数量，默认为3个
 */
const ListOperation = (props) => {
  const { opts = [], maxDisplay = 3 } = props;
  // let maxDisplay = props.maxDisplay && props.maxDisplay >= 1 ? props.maxDisplay : 3;

  let result;
  if (opts.length == 0) {
    // 没有操作时，不显示任何东西
    result = <>-</>;
  } else if (opts.length <= maxDisplay) {
    // 当有较少操作时，直接显示这些操作
    result = (
      <>
        {opts.map((opt, index) => {
          return <a key={index} onClick={opt.onClick}>{opt.name}</a>;
        })}
      </>
    );
  } else {
    // 当有较多操作时，除了显示前几个操作外，还会显示省略号，省略号包括剩余的其他操作
    result = (
      <>
        {opts.slice(0, maxDisplay - 1).map((opt, index) => {
          return <a key={index} onClick={opt.onClick}>{opt.name}</a>;
        })}
        <Dropdown
          overlay={
            <Menu selectable={false}>
              {opts.slice(maxDisplay - 1).map((opt) => {
                return (
                  <Menu.Item key={opt.name} onClick={opt.onClick}>
                    {opt.name}
                  </Menu.Item>
                );
              })}
            </Menu>
          }
        >
          <a>
            <EllipsisOutlined style={{ fontSize: "24px" }} />
          </a>
        </Dropdown>
      </>
    );
  }

  return result;
};

export default ListOperation;
