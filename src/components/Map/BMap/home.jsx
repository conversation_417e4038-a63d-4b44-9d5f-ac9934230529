/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 11:32
 * @LastEditTime: 2023/02/16 10:44
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\components\Map\BMap\home.jsx
 * @Description: '百度地图'
 */

import React, { useState, useEffect } from 'react';
import { Select, Spin } from 'antd';
import Map from 'react-bmapgl/dist/Map/Map';
import MapApiLoaderHOC from 'react-bmapgl/dist/Map/MapApiLoaderHOC';
import Marker from 'react-bmapgl/dist/Overlay/Marker';
import InfoWindow from 'react-bmapgl/dist/Overlay/InfoWindow';
import NavigationControl from 'react-bmapgl/dist/Control/NavigationControl';
import JsonP from 'jsonp';

import './home.less';

const { Option } = Select;

const BMap = (props) => {
  const {
    isInit = null,
    value = {},
    onChange,
  } = props;
  const [loading, setLoading] = useState(false);
  const [center, setCenter] = useState({});
  const [addressText, setAddressText] = useState('');
  const [addressOptions, setAddressOptions] = useState([]);
  const [region, setRegion] = useState('');
  const [selectValue, setSelectValue] = useState(null);
  const [searchValue, setSearchValue] = useState(null);

  useEffect(() => {
    getLocationIp();
    return () => { };
  }, []);

  useEffect(() => {
    if (isInit) {
      // getGeolocation();
    } else if (isInit == false) {
      setCenter(new window.BMapGL.Point(value.lng, value.lat));
      setAddressText(value.address);
    }
  }, [isInit]);

  useEffect(() => {
    if (center) {
      onChange?.({
        ...center,
        address: addressText,
      });
    }
  }, [center, addressText]);

  // 普通IP定位-Web服务API
  const getLocationIp = () => {
    const params = new URLSearchParams({
      ak: process.env.BMAP_SERVER_AK,
      ip: '',
      coor: 'bd09ll',
    });
    JsonP('https://api.map.baidu.com/location/ip?' + params, (err, res) => {
      console.log(res, 'getLocationIp-普通IP定位');
      const { content = null } = res;
      if (content) {
        setRegion(content.address_detail.city);
        setCenter(new window.BMapGL.Point(content.point.x, content.point.y));
        setAddressText(content.address);
      } else {
        getLocalCity();
      }
    });
  };

  // IP定位-根据用户IP 返回城市级别的定位结果
  const getLocalCity = () => {
    const myCity = new window.BMapGL.LocalCity();
    myCity.get((res) => {
      setRegion(res.name);
    });
  };

  // 浏览器定位 - 优先调用浏览器H5定位接口
  const getGeolocation = () => {
    setLoading(true);
    const geolocation = new window.BMapGL.Geolocation();
    geolocation.enableSDKLocation();
    geolocation.getCurrentPosition((res) => {
      console.log(res, 'getGeolocation-浏览器定位');
      const { point, address } = res;
      setCenter(point);
      setAddressText(address.province + address.city + address.district + address.street + address.street_number);
      setLoading(false);
    });
  };

  // 正地址解析服务
  const geocoderPoint = (e) => {
    setLoading(true);
    const { item } = e;
    const geoc = new window.BMapGL.Geocoder();
    const address = item.value.city + item.value.district + item.value.business;
    geoc.getPoint(address, (point) => {
      console.log(item, point, 'geocoderPoint-正地址解析服务');
      setCenter(point);
      setAddressText(address);
      setLoading(false);
    });
  };

  // 逆地址解析服务
  const geocoderLocation = (e) => {
    setLoading(true);
    setSelectValue(null);
    const { latlng } = e;
    const geoc = new window.BMapGL.Geocoder();
    geoc.getLocation(latlng, (res) => {
      console.log(latlng, res, 'geocoderLocation-逆地址解析服务');
      setCenter(latlng);
      setAddressText(res.address);
      setLoading(false);
    });
  };

  // 逆地址解析服务-Web服务API
  const reverse_geocoding = (e) => {
    const { latlng } = e;
    const params = new URLSearchParams({
      ak: process.env.BMAP_SERVER_AK,
      output: 'json',
      coordtype: 'wgs84ll',
      location: `${latlng.lat},${latlng.lng}`,
    });
    JsonP('https://api.map.baidu.com/reverse_geocoding/v3/?' + params, (err, res) => {
      console.log(latlng, res.result, 'reverse_geocoding');
      const { formatted_address } = res.result;
      setCenter(latlng);
      setAddressText(formatted_address);
      setLoading(false);
    });
  };

  // 搜索地址
  const onSearchAddress = (value) => {
    if (value) {
      setSearchValue(value);
      let timer = setTimeout(() => {
        const data = {
          keyword: value
        };
        keyWordSearch(data).then((res) => {
          setAddressOptions([...res]);
        });
        clearTimeout(timer);
      }, 500);
    } else {
      setSearchValue('');
    }
  };

  // 选中 option地址
  const onChangeAddress = (value) => {
    const select = addressOptions.find((item) => item.uid == value);
    const { province, city, district, name, location } = select;
    const selectContent = province + city + district + name;
    setCenter(location);
    setAddressText(selectContent);
    setSearchValue(selectContent);
    setSelectValue(value);
  };

  // 关键词输入提示-Web服务API
  const keyWordSearch = (data) => {
    const { keyword } = data;
    return new Promise(function (resolve, reject) {
      const params = new URLSearchParams({
        query: keyword,
        region: region,
        city_limit: false,
        output: 'json',
        ak: process.env.BMAP_SERVER_AK,
      });
      JsonP('https://api.map.baidu.com/place/v2/suggestion?' + params, (err, res) => {
        resolve(res.result);
      });
    });
  };

  return (
    <div className='BMap-Container'>
      <Spin spinning={loading}>
        <Select
          showSearch
          value={selectValue}
          searchValue={searchValue}
          placeholder={'搜索地址'}
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={onSearchAddress}
          onChange={onChangeAddress}
          notFoundContent={null}
        >
          {
            addressOptions.map((item, index) => <Option key={item.uid}>{item.title}<span style={{ color: '#b9b9b9', fontSize: '12px' }}>{item.province}-{item.city}-{item.district}-{item.name}</span></Option>)
          }
        </Select>
        <Map
          className="BMap"
          center={center}
          style={{ height: 300 }}
          enableDoubleClickZoom
          enableScrollWheelZoom
          zoom={16}
          onClick={(e) => {geocoderLocation(e);}}
        >
          <NavigationControl />
          <Marker position={center} />
          <InfoWindow position={center} text={addressText} />
        </Map>
      </Spin>
    </div>
  );
};

export default MapApiLoaderHOC({ ak: process.env.BMAP_WEB_AK })(BMap);
