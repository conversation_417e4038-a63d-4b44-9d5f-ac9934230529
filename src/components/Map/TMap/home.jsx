/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/27 09:20
 * @LastEditTime: 2022/12/29 16:50
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\components\Map\TMap\home.jsx
 * @Description: '腾讯地图'
 */

import React, { useState, useEffect, memo, forwardRef, useImperativeHandle } from 'react';
import { Select } from 'antd';
import JsonP from 'jsonp'

import './home.less';

const { Option } = Select;
const key = '7JTBZ-PAMKF-MVOJN-NVENG-YNXAH-G5FC5'

let initLatlng = null;
let map = null;
let marker = null;
let infoWindow = null;

const TMap = forwardRef((props, ref) => {
  // const { centerLatLng } = props;
  const [latLng, setLatLng] = useState({});
  const [addressOptions, setAddressOptions] = useState([]);
  const [searchValue, setSearchValue] = useState(null);

  useImperativeHandle(ref, () => ({
    initTMapLoader,
    destroyMap,
    getLatLng,
  }))

  useEffect(() => { }, []);

  const initTMapLoader = (params) => {
    return new Promise(function (resolve, reject) {
      loadScript().then(res => {
        initMap(params).then(() => {
          resolve()
        }).catch(err => {
          reject()
        })
      }).catch(err => {
        reject()
      })
    })
  }

  // 异步创建TMap方法
  const loadScript = () => {
    return new Promise(function (resolve, reject) {
      window.initMap = function () {
        resolve();
      };
      let script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `https://map.qq.com/api/gljs?v=1.exp&callback=initMap&key=${key}`; // JavaScript API GL initMap
      // script.src = `https://map.qq.com/api/js?v=2.exp&callback=initMap&key=${key}`; // JavaScript API initMapV2
      script.onerror = reject;
      document.head.appendChild(script);
    });
  };

  // 初始化地图V1
  const initMap = (params) => {
    return new Promise(function (resolve, reject) {
      IPLocation().then(res => {
        const { location } = res;
        // 初始化经纬度，最开始的点
        initLatlng = params ? new TMap.LatLng(params.lat, params.lng) : new TMap.LatLng(location.lat, location.lng);
        setLatLng(params ? params : location)
        console.log(initLatlng, location, 'initLatlng----');
        // 初始化地图，绑定dom
        map = new TMap.Map('TMap', { // 设置地图属性
          zoom: 16,
          center: initLatlng,
        });
        // 初始化marker图层
        marker = new TMap.MultiMarker({
          map: map,
          styles: {
            // 点标记样式
            marker: new TMap.MarkerStyle({
              width: 20, // 样式宽
              height: 30, // 样式高
              anchor: { x: 10, y: 30 }, // 描点位置
            }),
          },
          geometries: [
            // 点标记数据数组
            {
              // 标记位置(纬度，经度，高度)
              position: initLatlng,
              id: 'marker',
            },
          ],
        });
        positionDescribe({
          ...initLatlng,
          get_poi: 1
        }).then(res => {
          const { province, city, district } = res.address_component
          // 初始化infoWindow
          infoWindow = new TMap.InfoWindow({
            map: map,
            position: initLatlng,
            offset: { x: 0, y: -32 }, // 设置信息窗相对position偏移像素
            content: province + city + district + res.pois[0].title
          });
        })
        // 点击marker本身
        marker.on("click", function (event) {
          console.log(event, '点击marker本身');
        })
        // 点击地图修改marker和infoWindow
        map.on("click", function (event) {
          console.log(event, '点击地图修改marker和infoWindow');
          //设置infoWindow
          positionDescribe({
            ...event.latLng,
            get_poi: 1
          }).then(res => {
            if (marker) {
              setLatLng(event.latLng)
              marker.setGeometries([
                {
                  position: event.latLng,
                  id: "marker",
                }
              ])
              const { province, city, district } = res.address_component
              infoWindow.open(); // 打开信息窗
              infoWindow.setPosition(event.latLng); // 设置信息窗位置
              infoWindow.setContent(province + city + district + res.pois[0].title); // 设置信息窗内容
            }
          })
        })
        resolve()
      })
    });
  }

  // 初始化地图V2
  const initMapV2 = () => {
    return new Promise(function (resolve, reject) {
      const params = new URLSearchParams({
        output: "jsonp",
        key: key,
      })
      JsonP("https://apis.map.qq.com/ws/location/v1/ip?" + params, (err, res) => {
        const { location } = res.result
        // 初始化经纬度，最开始的点
        initLatlng = new qq.maps.LatLng(location.lat, location.lng);
        // 创建地图，绑定dom
        map = new qq.maps.Map('TMap', { // 设置地图属性
          zoom: 16,
          center: initLatlng,
        });
        // 现实已经存在的点
        marker = new qq.maps.Marker({
          map: map,
          animation: qq.maps.MarkerAnimation.DROP,
          position: initLatlng,
        });
        // 添加监听事件
        new qq.maps.event.addListener(map, 'click', function (event) {
          if (!marker) {
            marker = new qq.maps.Marker({
              position: event.latLng,
              map: map
            });
            return;
          }
          marker.setPosition(event.latLng);
        });
        resolve()
      });
    });
  }

  // IP定位
  const IPLocation = () => {
    return new Promise(function (resolve, reject) {
      const params = new URLSearchParams({
        output: "jsonp",
        key: key,
      })
      JsonP("https://apis.map.qq.com/ws/location/v1/ip?" + params, (err, res) => {
        resolve(res.result)
      });
    })
  }

  // 逆地址解析（坐标位置描述）
  const positionDescribe = (data) => {
    const { lat, lng, get_poi = 0 } = data;
    return new Promise(function (resolve, reject) {
      const params = new URLSearchParams({
        location: `${lat},${lng}`,
        get_poi: get_poi,
        output: "jsonp",
        key: key,
      })
      JsonP("https://apis.map.qq.com/ws/geocoder/v1/?" + params, (err, res) => {
        resolve(res.result)
      })
    })
  }

  // 关键词输入提示
  const keyWordSearch = (data) => {
    const { keyword } = data;
    return new Promise(function (resolve, reject) {
      const params = new URLSearchParams({
        keyword: keyword,
        output: "jsonp",
        key: key,
      })
      JsonP("https://apis.map.qq.com/ws/place/v1/suggestion/?" + params, (err, res) => {
        resolve(res.data)
      })
    })
  }

  // 销毁地图
  const destroyMap = () => {
    map && map.destroy();
  }

  // 搜索地址
  const onSearchAddress = (value) => {
    if (value) {
      setSearchValue(value)
      let timer = setTimeout(() => {
        const data = {
          keyword: value
        }
        keyWordSearch(data).then(res => {
          setAddressOptions([...res])
        })
        clearTimeout(timer)
      }, 500);
    } else {
      console.log('清空');
      setSearchValue('')
    }
  };

  // 选择地址
  const onChangeAddress = (value) => {
    const select = addressOptions.find(item => item.id == value)
    const { province, city, district, title, location } = select;
    if (marker) {
      const selectLatLng = new TMap.LatLng(location.lat, location.lng);
      const selectContent = province + city + district + title;
      map.setCenter(selectLatLng)
      marker.setGeometries([
        {
          position: selectLatLng,
          id: "marker",
        }
      ])
      infoWindow.open(); // 打开信息窗
      infoWindow.setPosition(selectLatLng); // 设置信息窗位置
      infoWindow.setContent(selectContent); // 设置信息窗内容
      let timer = setTimeout(() => {
        setSearchValue(selectContent)
        clearTimeout(timer)
      }, 100);
      setLatLng(location)
    }
  };

  // 抛出经纬度值
  const getLatLng = () => {
    return latLng
  }

  const jsonPDemo = () => {
    const params = new URLSearchParams({
      output: "jsonp",
      key: "ICXBZ-T7MCW-FEWRT-RQDGB-6KVKO-YSBOV"
    })
    JsonP("http://apis.map.qq.com/ws/location/v1/ip?" + params, (err, res) => {
      console.log(res.result)
    });
  }

  return (
    <div className='TMap-container'>
      <div id="TMap" />
      <Select
        showSearch
        searchValue={searchValue}
        placeholder={'搜索地址'}
        defaultActiveFirstOption={false}
        showArrow={false}
        filterOption={false}
        onSearch={onSearchAddress}
        onChange={onChangeAddress}
        notFoundContent={null}
      >
        {
          addressOptions.map((item, index) => <Option key={item.id}>{item.title} <span style={{ color: "#b9b9b9", fontSize: "12px" }}>{item.province}-{item.city}-{item.district}</span></Option>)
        }
      </Select>
    </div>
  );
});

export default memo(TMap);
