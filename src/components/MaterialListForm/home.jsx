/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/08/26 11:27
 * @LastEditTime: 2024/10/21 17:04
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/MaterialListForm/home.jsx
 * @Description: '素材列表表单'
 */

import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import {
  Button,
  Form,
  Input,
  message,
  InputNumber,
  Tag,
  Dropdown,
  Menu,
} from "antd";
import { apiCall } from "common/utils";
import {
  PlusOutlined,
  CloseCircleOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { normFile, trimAll } from "common/regular";
import MaterialModal from "components/Modal/MaterialModal/home";
import LiveCodeModal from "components/Modal/LiveCodeModal/home";
import AppletCard from "components/AppletCard/home";
import { QrCodeBase } from "common/qrcode";
import DndSortable, { arrayMove } from "react-dragger-sort";
import WibotUpload from "components/WibotUpload/home";
import LinkCard from "components/LinkCard/home";
import "./home.less";
import { FileHOC } from "../FileHOC/FileHOC";

const FormItem = Form.Item;
const { TextArea } = Input;
const { CheckableTag } = Tag;

const MaterialListForm = forwardRef((props, ref) => {
  const {
    formRef = null,
    isNickname = true,
    materialAmount = 0, //已选择的资源数
    isDisabled = false, //是否禁用
    codeName = "", //添加自动拉群名称
    labelCol = 3,
    // OnlyOneCopyWriter = false,//是否只允许添加一条文案
    needScriptFlag = false, //是否需要选择有参考话术的资源时把参考话术也添加为资源素材且只允许添加一条文案
    previewList = [], //上级部门或员工素材预览内容
    materialTabList = null, //选择素材弹窗配置Tab
    tabType = null, // 素材类型
    inserteFlag = false, //是否允许在每个素材下插入素材
    intervalFlag = false, //是否在每个素材下显示发送时间间隔
    limitNineFlag = true, //是否限制素材数量为9条
    maxLength = 9, // 素材数量上限，默认为9
    isCopyWriterDisabled = false, // 是否文案禁用
    isOpenTag = false, // 是否开启标签
    isSort = false, // 是否开启拖拽排序
    isShowProduct = false, // 是否在有关联产品的网页文章、海报、图片资源下显示产品码
    compress = true, // 是否对图片进行质量优化
  } = props.params;
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  });
  const [liveCodeModalParams, setLiveCodeModalParams] = useState({
    visible: false,
  });
  const [msgList, setMsgList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [copyWriterFlag, seCopyWriterFlag] = useState(false);
  const [menuList, setMenuList] = useState([
    "copyWriter",
    "image",
    "material",
    "liveCode",
  ]);
  const [isRetrieve, setIsRetrieve] = useState(false);

  useImperativeHandle(ref, () => ({
    getInitMsgList,
    getModifyMsgList,
    getCopyWriterValidate, // needScriptFlag情况下保存校验资源文案字数
    setRetrieveFun,
  }));

  useEffect(() => {
    const { menuList } = props.params;
    menuList && setMenuList(menuList);
  }, []);

  useEffect(() => {
    props.callback({
      data: getModifyMsgList(),
    });
    if (needScriptFlag && msgList.some((item) => item.type == "copyWriter")) {
      seCopyWriterFlag(true);
    } else {
      seCopyWriterFlag(false);
    }
  }, [msgList]);

  const getInitMsgList = (messages) => {
    const newMsgList = messages?.map((item, index) => {
      let msg = "";
      switch (item.type) {
        case "copyWriter":
          msg = item.content || item.copyWriter || "";
          break;
        case "Text":
          msg = item.content;
          break;
        case "Article":
          msg = item.url || item.title;
          break;
        case "pageArticle":
          msg = (item.fileId && item.fileId[0]) || item.title;
          break;
        case "Picture":
          msg = (item.fileId && item.fileId[0]) || "";
          break;
        case "Poster":
          msg = (item.fileId && item.fileId[0]) || "";
          break;
        case "QrCode":
          msg = item.url;
          break;
        case "Video":
          msg = (item.fileId && item.fileId[0]) || "";
          break;
        case "MINI_PROGRAM":
          msg = (item.fileId && item.fileId[0]) || item.title;
          item.miniProgram = {
            ...item,
          };
          break;
        case "FORM":
          msg = (item.fileId && item.fileId[0]) || item.title;
          item.imageUrl = item.fileId && item.fileId[0];
          break;
        case "Product":
          msg = (item.fileId && item.fileId[0]) || item.title;
          item.imageUrl = item.fileId && item.fileId[0];
          break;
        default:
          msg = item.type;
          break;
      }
      return msg;
    });
    formRef?.setFieldsValue({
      msgList: [...newMsgList],
    });
    const newItemList = messages;
    newItemList?.forEach((item) => {
      if (item.type == "QrCode") {
        item.imageUrl = QrCodeBase({ url: item.url });
      } else if (item.type == "Video") {
        item.videoUrl = item.fileId ? item.fileId[0] : "";
      } else if (item.fileId && Array.isArray(item.fileId)) {
        item.imageUrl = item.fileId ? item.fileId[0] : "";
        item.image = item.fileId ? item.fileId[0] : "";
      } else if (item.images && Array.isArray(item.images)) {
        item.imageUrl = item.images ? item.images[0] : "";
        item.image = item.images ? item.images[0] : "";
      }
    });
    setMsgList([...newItemList] ?? []);
  };

  // 添加对应素材列表
  const handleAddMsg = (type, inserteIdx) => {
    const newMsgList = [...msgList];
    if (previewList.length > 0) {
      const list = [...previewList, ...newMsgList];
      const newArr = list.filter((item) => item.type != "copyWriter");
      const copyWriterFlag = list.some((item) => item.type == "copyWriter");
      if (
        copyWriterFlag
          ? newArr.length + 1 >= maxLength
          : newArr.length >= maxLength
      ) {
        message.warning(
          `当前欢迎语累计已超过${maxLength}条，超出部分不支持预览！`
        );
      }
    }
    if (limitNineFlag && !needScriptFlag && newMsgList.length >= maxLength) {
      message.warning(`素材不能超过${maxLength}条！`);
      return;
    }
    if (limitNineFlag && needScriptFlag && newMsgList.length >= maxLength + 1) {
      message.warning(`素材不能超过${maxLength}条！`);
      return;
    }
    if (type == "material") {
      setMaterialModalParams({
        visible: true,
        tabType: (isRetrieve && "copyWriter") || tabType || null,
        tabList: (isRetrieve && ["copyWriter"]) || materialTabList || null,
        materialAmount: materialAmount,
        needScriptFlag: needScriptFlag,
        limitNineFlag: limitNineFlag,
        maxLength: maxLength,
        onSubmit: (data) => {
          setMaterialModalParams({ visible: false });

          let msgListField = formRef.getFieldValue("msgList") ?? [];

          //需要话术文案合并且选择的资源包含话术
          if (needScriptFlag && data.some((item) => item.script)) {
            let script = "";
            data.forEach((item) => {
              script += (item.script || "") + (item.script ? "\n" : "");
            });
            // console.log(script, "scriptscript");

            //已选的资源中已存在文案且选择的资源非文案类型
            if (newMsgList.some((item) => item.type == "copyWriter")) {
              if (data.every((item) => item.type != "copyWriter")) {
                newMsgList.forEach((item, index) => {
                  if (item.type == "copyWriter") {
                    if (data.isResourceSet) {
                      const allCopyWriter = data.filter(i => i.type === 'copyWriter').map(i => i.content).join('\n')
                      item.content = item.content + allCopyWriter
                    }else {
                      item.content = item.content + script;
                    }

                    msgListField[index] = item.content;
                  }
                });
                message.success("该资源的参考话术已添加到文案中！");
              }
            } else {
              //已选的资源中未添加文案且选择的资源非文案
              if (data.some((item) => item.type != "copyWriter")) {
                msgListField.push(script);
                newMsgList.push({
                  id: null,
                  content: script,
                  type: "copyWriter",
                });
                message.success("该资源的参考话术已添加到文案中！");
              }
            }
          }

          const msgArr = [];
          data.forEach((item) => {
            let msg = "";
            switch (item.type) {
              case "copyWriter":
                msg = item.copyWriter;
                item.content = msg;
                break;
              case "pageArticle":
                msg = (item.images && item.images[0]) || "";
                item.imageUrl = msg;
                item.image = msg;
                item.fileId = item.images;
                break;
              case "Article":
                msg = (item.images && item.images[0]) || "";
                item.imageUrl = msg;
                item.image = msg;
                item.fileId = item.images;
                break;
              case "Video":
                msg = (item.videos && item.videos[0]) || "";
                item.videoUrl = msg;
                item.videos = item.videos || [];
                item.fileId = item.videos;
                break;
              case "MINI_PROGRAM":
                msg = (item.miniProgram && item.miniProgram.fileId) || "";
                item.fileId = item.miniProgram && [item.miniProgram.fileId];
                break;
              case "Poster":
                msg = (item.fileId && item.fileId[0]) || "";
                item.imageUrl = msg;
                item.fileId = item.fileId;
                break;
              case "QrCode":
                msg = (item.fileId && item.fileId[0]) || "";
                item.imageUrl = msg;
                item.fileId = item.fileId;
                break;
              case "Picture":
                msg = (item.fileId && item.fileId[0]) || "";
                item.imageUrl = msg;
                item.fileId = item.fileId;
                break;
              case "FORM":
                item.image = item.shareImage || item.image;
                item.title = item.shareTitle;
                item.description = item.shareDescription;
                msg = item.image || "";
                item.imageUrl = msg;
                break;
              case "Product":
                msg = (item.images && item.images[0]) || "";
                item.imageUrl = msg;
                break;
            }
            item.isMaterial = true;
            msgArr.push(msg);
          });

          //需要话术文案合并且选择的资源包含文案类型
          if (
            needScriptFlag &&
            data.some((item) => item.type == "copyWriter")
          ) {
            let scriptCopyWriter = "";
            const filterArr = data.filter((item) => item.type != "copyWriter");
            const filterTypeArr = filterArr.map((item) => item.type);
            data.forEach((item) => {
              scriptCopyWriter +=
                (item.script || "") +
                (item.script ? "\n" : "") +
                (item.copyWriter || "") +
                (item.copyWriter ? "\n" : "");
            });
            //已选择的资源中已存在文案
            if (newMsgList.some((item) => item.type == "copyWriter")) {
              newMsgList.forEach((item, index) => {
                if (item.type == "copyWriter") {
                  if (data.isResourceSet) {
                    const allCopyWriter = data.filter(i => i.type === 'copyWriter').map(i => i.content).join('\n')
                    console.log(`[allCopyWriter]: `, allCopyWriter)
                    item.content = item.content + allCopyWriter
                  }else {
                    item.content = item.content + scriptCopyWriter;
                  }

                  msgListField[index] = item.content;
                }
              });
              if (data.some((item) => item.script)) {
                message.success("该资源的文案及参考话术已一同添加到文案中！");
              }
            }
            formRef.setFieldsValue({
              msgList: msgListField.concat(filterTypeArr),
            });
            setMsgList(newMsgList.concat(filterArr));
          } else {
            if (typeof inserteIdx == "number") {
              let intervalListField =
                formRef.getFieldValue("intervalList") ?? [];
              intervalListField.splice(inserteIdx + 1, 0, null);
              newMsgList.splice(inserteIdx + 1, 0, ...data);
              msgListField.splice(inserteIdx + 1, 0, ...msgArr);
              formRef.setFieldsValue({
                msgList: msgListField,
                intervalList: intervalListField,
              });
              setMsgList(newMsgList);
            } else {
              formRef.setFieldsValue({
                msgList: msgListField.concat(msgArr),
              });
              setMsgList(newMsgList.concat(data));
            }
          }
        },
        onCancel: () => {
          setMaterialModalParams({ visible: false });
        },
      });
    } else if (type == "code") {
      setLiveCodeModalParams({
        visible: true,
        onSubmit: (data) => {
          setLiveCodeModalParams({ visible: false });
          let msgListField = formRef.getFieldValue("msgList") ?? [];
          msgListField.push(data.url);
          formRef.setFieldsValue({
            msgList: msgListField,
          });
          newMsgList.push(data);
          setMsgList(newMsgList);
        },
        onCancel: () => {
          setLiveCodeModalParams({ visible: false });
        },
      });
    } else if (type == "copyWriter") {
      let msgListField = formRef.getFieldValue("msgList") ?? [];
      if (typeof inserteIdx == "number") {
        let intervalListField = formRef.getFieldValue("intervalList") ?? [];
        intervalListField.splice(inserteIdx + 1, 0, null);
        newMsgList.splice(inserteIdx + 1, 0, {
          type: type,
          content: "",
        });
        msgListField.splice(inserteIdx + 1, 0, "");
        formRef.setFieldsValue({
          msgList: msgListField,
          intervalList: intervalListField,
        });
      } else {
        newMsgList.push({
          type: type,
          content: "",
        });
        msgListField.push("");
        formRef.setFieldsValue({
          msgList: msgListField,
        });
      }
      setMsgList(newMsgList);
    } else if (type == "Picture") {
      let msgListField = formRef.getFieldValue("msgList") ?? [];
      if (typeof inserteIdx == "number") {
        let intervalListField = formRef.getFieldValue("intervalList") ?? [];
        intervalListField.splice(inserteIdx + 1, 0, null);
        newMsgList.splice(inserteIdx + 1, 0, {
          type: type,
          imageUrl: "",
        });
        msgListField.splice(inserteIdx + 1, 0, "");
        formRef.setFieldsValue({
          msgList: msgListField,
          intervalList: intervalListField,
        });
      } else {
        newMsgList.push({
          type: type,
          imageUrl: "",
        });
        msgListField.push("");
        formRef.setFieldsValue({
          msgList: msgListField,
        });
      }
      setMsgList(newMsgList);
    }
  };

  // 输入框光标处手动添加内容
  const handleAddFocusContent = async (value, index) => {
    const insertHTML = "#客户昵称#";
    const inputIndex = document.getElementById(`msgInput${index}`); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) return;
    let newMsgList = [...msgList];
    let msgListField = formRef.getFieldValue("msgList") ?? [];
    newMsgList[index].content =
      value.substring(0, startPos) + insertHTML + value.substring(endPos);
    msgListField[index] =
      value.substring(0, startPos) + insertHTML + value.substring(endPos);
    formRef.setFieldsValue({
      msgList: msgListField,
    });
    setMsgList([...newMsgList]);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  // 消息列表-删除选中
  const handleDelMsg = (order) => {
    const formMsgList = [...msgList];
    const fltMsgList = formMsgList.filter((el, idx) => idx !== order);
    const fieldMsgList = formRef.getFieldValue("msgList");
    const newFieldMsgList = fieldMsgList.filter((el, idx) => idx !== order);
    if (intervalFlag) {
      const intervalListField = formRef.getFieldValue("intervalList") ?? [];
      const newIntervalListField = intervalListField.filter(
        (el, idx) => idx !== order
      );
      formRef.setFieldsValue({
        msgList: newFieldMsgList,
        intervalList: newIntervalListField,
      });
    } else {
      formRef.setFieldsValue({
        msgList: newFieldMsgList,
      });
    }
    setMsgList(fltMsgList);
  };

  //产品码开启关闭
  const handleProductTagChange = async (data, checked) => {
    const { type, id, showProduct } = data;
    const newMsgList = JSON.parse(JSON.stringify(msgList));
    if (type == "POSTER_TOOL" || type == "Picture" || type == "Poster") {
      props.params.changeLoading(true);
      await apiCall("/info/infoResource/getPic", "GET", {
        id,
        original: showProduct,
      })
        .then((res) => {
          const { images } = res;
          data.imageUrl = images ? images[0] : "";
          data.showProduct = !data.showProduct;
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          props.params.changeLoading(false);
        });
    }
    newMsgList.forEach((item) => {
      if (item.id == data.id) {
        if (
          item.type == "POSTER_TOOL" ||
          item.type == "Picture" ||
          item.type == "Poster"
        ) {
          item.imageUrl = data.imageUrl;
          item.image = data.imageUrl;
          item.fileId = [data.imageUrl];
          item.showProduct = data.showProduct;
        } else {
          item.showProduct = !data.showProduct;
        }
      }
    });
    setMsgList(newMsgList);
  };

  // 获取素材列表处理后的数据
  const getModifyMsgList = () => {
    return msgList.map((item, index) => {
      let msg = {};
      switch (item.type) {
        case "copyWriter":
          msg = {
            ...item,
            content: item.content.trim(),
          };
          break;
        case "pageArticle":
          if (item.title) {
            msg = {
              ...item,
              image: item.fileId && item.fileId[0],
              url: item.transitUrl || item.url,
            };
          } else {
            msg = {
              ...item,
              url: item.transitUrl || item.url,
            };
          }
          break;
        case "Picture":
          msg = {
            ...item,
          };
          break;
        case "Article":
          msg = {
            ...item,
            image: item.fileId && item.fileId[0],
            url: item.transitUrl || item.url,
          };
          break;
        case "Video":
          msg = {
            ...item,
            title: item.script?.substring(0, 30) || "",
            url: item.transitUrl || item.url,
          };
          break;
        case "Poster":
          msg = {
            ...item,
            image: item.imageUrl,
          };
          break;
        case "GROUP": //新增群活码
          msg = {
            ...item,
            image: item.imageUrl,
            type: "QrCode",
          };
          break;
        case "QrCode": //编辑群活码
          msg = {
            ...item,
            image: item.imageUrl,
            type: "QrCode",
          };
          break;
        case "MINI_PROGRAM": //小程序
          msg = {
            ...item,
            ...item.miniProgram,
            url: item.miniProgram?.page || item.miniProgram?.url,
            fileId: item?.fileId,
          };
          break;
        case "FORM": //表单
          delete item.extraSetting;
          delete item.jsonContent;
          delete item.commitActionContent;
          msg = {
            ...item,
            fileId: item.image ? [item.image] : item.fileId,
            url: item.transitUrl || item.url,
          };
          break;
        case "Product":
          msg = {
            ...item,
            fileId: item.images ? item.images : item.fileId,
            url: item.transitUrl || item.url,
          };
          break;
        default:
          msg = {
            ...item,
          };
          break;
      }
      return msg;
    });
  };

  const getCopyWriterValidate = () => {
    let flag = false;
    for (let i = 0; i < msgList.length; i++) {
      if (
        msgList[i].type == "copyWriter" &&
        trimAll(msgList[i].content).length > (needScriptFlag ? 1200 : 1000)
      ) {
        message.error("文案超长不允许保存！");
        flag = true;
      }
    }
    return flag;
  };

  // 关于设置挽回语的操作
  const setRetrieveFun = (params = {}) => {
    const { specialFlag, menuList } = params;
    if (menuList && menuList.length) {
      setMenuList(menuList);
    } else {
      setMenuList(["copyWriter", "image", "material", "liveCode"]);
    }
    setIsRetrieve(specialFlag);
  };

  return (
    <div className="MaterialListForm">
      <FormItem
        extra={props.params.extra || ""}
        style={{ marginBottom: "0px" }}
      >
        <div className="btn-flex">
          {menuList?.map((item, index) => (
            <div key={index}>
              {item == "copyWriter" && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  disabled={isDisabled || copyWriterFlag}
                  onClick={() => handleAddMsg("copyWriter")}
                >
                  文案
                </Button>
              )}

              {item == "image" && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  disabled={isDisabled}
                  onClick={() => handleAddMsg("Picture")}
                >
                  图片
                </Button>
              )}

              {item == "material" && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  disabled={isDisabled}
                  onClick={() => handleAddMsg("material")}
                >
                  选择素材
                </Button>
              )}

              {item == "liveCode" && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  disabled={isDisabled}
                  onClick={() => handleAddMsg("code")}
                >
                  添加群活码
                </Button>
              )}
            </div>
          ))}
        </div>
      </FormItem>

      <div className="msgList">
        <DndSortable
          onUpdate={(params) => {
            const { from, to } = params;
            if (intervalFlag) {
              formRef.setFieldsValue({
                msgList: arrayMove(
                  formRef.getFieldValue("msgList"),
                  from.index,
                  to.index
                ),
                intervalList: arrayMove(
                  formRef.getFieldValue("intervalList"),
                  from.index,
                  to.index
                ),
              });
            } else {
              formRef.setFieldsValue({
                msgList: arrayMove(
                  formRef.getFieldValue("msgList"),
                  from.index,
                  to.index
                ),
              });
            }
            setMsgList(arrayMove(msgList, from.index, to.index));
          }}
          options={{
            handle: ".sortBox",
            hiddenFrom: true,
          }}
        >
          {msgList?.map((item, index) => (
            <div key={index} className="template">
              {intervalFlag && (
                <FormItem
                  label="发送间隔"
                  labelCol={{ span: labelCol }}
                  name={["intervalList", index]}
                >
                  <InputNumber min={0} addonAfter="秒" />
                </FormItem>
              )}

              <div style={{ display: "flex" }}>
                {needScriptFlag && item.type != "copyWriter" && (
                  <span className="title">
                    {needScriptFlag ? index : index + 1}
                  </span>
                )}

                {isSort && (
                  <MenuOutlined
                    className="sortBox"
                    style={{
                      fontSize: "20px",
                      color: "#d9d9d9",
                      marginRight: "10px",
                      cursor: "move",
                    }}
                  />
                )}

                {item.type == "copyWriter" || item.type == "Text" ? (
                  <FormItem
                    name={["msgList", index]}
                    label={!needScriptFlag && "文案"}
                    labelCol={{ span: labelCol }}
                    className="formItem_noMargin"
                    rules={[
                      {
                        required: !needScriptFlag,
                        message: `请输入${needScriptFlag ? 1200 : 1000
                          }字以内的文案`,
                      },
                    ]}
                    extra={
                      <>
                        {isNickname && (
                          <Button
                            className="add-content"
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() =>
                              handleAddFocusContent(item.content, index)
                            }
                          >
                            客户昵称
                          </Button>
                        )}
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                      </>
                    }
                    initialValue={item.content}
                  >
                    <TextArea
                      id={`msgInput${index}`}
                      showCount
                      maxLength={needScriptFlag ? 1200 : 1000}
                      autoSize={{ minRows: 4, maxRows: 6 }}
                      disabled={isDisabled || (isCopyWriterDisabled && item.id)} // 开启禁用文案并且有id才禁用
                      onChange={(e) => {
                        let newMsgList = msgList;
                        newMsgList[index].content = e.target.value;
                        setMsgList([...newMsgList]);
                      }}
                      placeholder={`请输入${needScriptFlag ? 1200 : 1000
                        }字以内的文案`}
                      allowClear
                      className={`${trimAll(item.content).length >
                        (needScriptFlag ? 1200 : 1000) && "textAreaAffix"
                        }`}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "pageArticle" ? (
                  <FormItem
                    label="文章"
                    className="formItem_noMargin"
                    labelCol={{ span: labelCol }}
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                        {isShowProduct && item.productId && (
                          <CheckableTag
                            checked={item.showProduct}
                            onChange={(checked) =>
                              handleProductTagChange(item, checked)
                            }
                            className="CheckableTag"
                          >
                            产品码
                          </CheckableTag>
                        )}
                      </>
                    }
                  >
                    <LinkCard
                      isLink={false}
                      data={{
                        ...item,
                      }}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "Picture" ? (
                  <FormItem
                    name={["msgList", index]}
                    className="formItem_noMargin"
                    getValueFromEvent={normFile}
                    label="图片"
                    labelCol={{ span: labelCol }}
                    rules={[{ required: true, message: "请上传图片" }]}
                    extra={
                      <>
                        <div>大小限制为10M，最多上传1张</div>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                        {isShowProduct && item.productId && (
                          <CheckableTag
                            checked={item.showProduct}
                            onChange={(checked) =>
                              handleProductTagChange(item, checked)
                            }
                            className="CheckableTag"
                          >
                            产品码
                          </CheckableTag>
                        )}
                      </>
                    }
                    onClick={(e) => {
                      e.preventDefault(); // 阻止浏览器默认行为
                      e.stopPropagation(); // 阻止事件冒泡
                    }}
                  >
                    <WibotUpload
                      fileList={item.imageUrl}
                      deletable={false}
                      onDone={(params) => {
                        const { fileUrl, fileId } = params;
                        let newMsgList = [...msgList];
                        newMsgList[index].imageUrl = fileUrl;
                        newMsgList[index].image = fileUrl;
                        newMsgList[index].fileId = [fileUrl];
                        newMsgList[index].picType = "creat";
                        setMsgList([...newMsgList]);

                        let msgListField =
                          formRef.getFieldValue("msgList") ?? [];
                        msgListField[index] = fileUrl;
                        formRef.setFieldsValue({
                          msgList: msgListField,
                        });
                      }}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "Article" ||
                  item.type == "SURVEY" ||
                  item.type == "Product" ? (
                  <FormItem
                    label={
                      item.type == "Article"
                        ? "推文"
                        : item.type == "Product"
                          ? "产品"
                          : "问卷"
                    }
                    labelCol={{ span: labelCol }}
                    className="formItem_noMargin"
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                      </>
                    }
                  >
                    <LinkCard
                      isLink={false}
                      data={{
                        ...item,
                      }}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "FORM" || item.type == "RESOURCE_COLLECTION" ? (
                  <FormItem
                    label={item.type == "FORM" ? "问卷" : "活动集锦"}
                    labelCol={{ span: labelCol }}
                    className="formItem_noMargin"
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                      </>
                    }
                  >
                    <LinkCard
                      isLink={false}
                      data={{
                        ...item,
                      }}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "Video" ? (
                  <FormItem
                    name={["msgList", index]}
                    label="视频"
                    labelCol={{ span: labelCol }}
                    className="formItem_noMargin"
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                      </>
                    }
                  >
                    <FileHOC src={item?.videoUrl}>
                      {(url) => (
                        <video
                          controls
                          src={url}
                          style={{ width: "102px", height: "102px" }}
                        />
                      )}
                    </FileHOC>
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "Poster" ||
                  item.type == "QrCode" ||
                  item.isCode ? (
                  <FormItem
                    name={["msgList", index]}
                    className="formItem_noMargin"
                    getValueFromEvent={normFile}
                    label={
                      item.type == "QrCode" || item.isCode ? "群活码" : "海报"
                    }
                    labelCol={{ span: labelCol }}
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                        {isShowProduct && item.productId && (
                          <CheckableTag
                            checked={item.showProduct}
                            onChange={(checked) =>
                              handleProductTagChange(item, checked)
                            }
                            className="CheckableTag"
                          >
                            产品码
                          </CheckableTag>
                        )}
                      </>
                    }
                  >
                    <WibotUpload
                      fileList={item.imageUrl}
                      deletable={false}
                      compress={compress}
                      onDone={(params) => {
                        const { fileUrl, fileId } = params;
                        let newMsgList = [...msgList];
                        newMsgList[index].imageUrl = fileUrl;
                        newMsgList[index].fileId = [fileUrl];
                        setMsgList([...newMsgList]);

                        let msgListField =
                          formRef.getFieldValue("msgList") ?? [];
                        msgListField[index] = fileUrl;
                        formRef.setFieldsValue({
                          msgList: msgListField,
                        });
                      }}
                    />
                  </FormItem>
                ) : (
                  ""
                )}

                {item.type == "MINI_PROGRAM" ? (
                  <FormItem
                    label="小程序"
                    labelCol={{ span: labelCol }}
                    className="formItem_noMargin"
                    extra={
                      <>
                        {isOpenTag && (
                          <>
                            {item.internalResourcesFlag && (
                              <Tag color="green">内部资源</Tag>
                            )}
                            {item.resourceStatus &&
                              item.resourceStatus != "Added" && (
                                <Tag color="red">已下架</Tag>
                              )}
                          </>
                        )}
                      </>
                    }
                  >
                    <AppletCard data={item.miniProgram || {}} />
                  </FormItem>
                ) : (
                  ""
                )}

                {((needScriptFlag &&
                  item.type != "copyWriter" &&
                  !isDisabled) ||
                  !needScriptFlag) && (
                    <CloseCircleOutlined
                      className={`tagClose ${inserteFlag ? "tagCloseInserte" : ""
                        }`}
                      onClick={() => handleDelMsg(index)}
                    />
                  )}
              </div>

              {inserteFlag && (
                <Dropdown
                  arrow
                  overlay={
                    <Menu>
                      <Menu.Item>
                        <a onClick={() => handleAddMsg("copyWriter", index)}>
                          文案
                        </a>
                      </Menu.Item>
                      <Menu.Item>
                        <a onClick={() => handleAddMsg("Picture", index)}>
                          图片
                        </a>
                      </Menu.Item>
                      <Menu.Item>
                        <a onClick={() => handleAddMsg("material", index)}>
                          素材
                        </a>
                      </Menu.Item>
                    </Menu>
                  }
                >
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    disabled={isDisabled}
                    className="insertionBtn"
                  >
                    插入内容
                  </Button>
                </Dropdown>
              )}
            </div>
          ))}
        </DndSortable>
      </div>

      <MaterialModal params={materialModalParams} />
      <LiveCodeModal params={liveCodeModalParams} />
    </div>
  );
});

export default MaterialListForm;
