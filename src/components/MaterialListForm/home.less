.MaterialListForm {

  .ant-form-item-control-input-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .ant-input-textarea {
      width: 100%;
    }
  }

  .btn-flex {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;

    button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }


  .msgList {

    .template {
      position: relative;
      border-top: 1px solid #f1f2f4;
      padding: 20px 0;
      display: flex;
      flex-direction: column;

      &:first-child {
        border: unset;
      }

      .ant-row {
        margin-bottom: unset;
        flex: 0.8;
        margin-top: -4px;
      }

      .title {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        margin-right: 10px;
      }

      .textAreaAffix {
        &::after {
          color: #ff4d4f;
        }
      }

      .tagClose {
        cursor: pointer;
        font-size: 20px;
        color: #d9d9d9;
        margin-left: 20px;

        &.tagCloseInserte {
          position: absolute;
          right: 0;
          top: 20px;
          z-index: 99;
        }
      }

      .formItem_noMargin {
        flex: 1;
        margin-bottom: 0px;
      }

      .ant-input-textarea {
        width: 320px;
      }

      .insertionBtn {
        position: absolute;
        right: 0px;
        bottom: 20px;

        z-index: 99;
      }

      .CheckableTag {
        background: #f5f5f5;

        &:hover {
          color: #f24e41;
        }

        &.ant-tag-checkable-checked {
          background: #f24e41;

          &:hover {
            color: #fff;
          }
        }
      }
    }

    .picture-card {
      width: 200px;
      max-height: 200px;
      display: flex;
      flex-wrap: wrap;
      overflow: hidden;

      .ant-image {
        width: 60px;
        height: 60px;
        margin: 0 6px 6px 0;

        img {
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}