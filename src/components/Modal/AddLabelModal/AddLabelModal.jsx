/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/19 10:00
 * @LastEditTime: 2024/10/21 17:04
 * @LastEditors: Janaeiw
 * @Description: '打标签(新增标签)共用组件'
 */

import React, { memo, useEffect, useState } from "react";
import { Modal, Tree, Input, Divider, Spin, Empty } from "antd";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils";
import "./AddLabelModal.less";

const { Search } = Input;

const AddLabelModal = (props) => {
  const {
    visible,
    selectedData = [],
    // treeData,
    onSelect,
    handleSubmit,
    onCancel,
    confirmLoading,
  } = props;
  const [tagTreeData, setTagTreeData] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    if (visible) {
      getTagCategoryTreeTwo();
    } else {
      setAutoExpandParent(true);
      setCheckedKeys([]);
      setExpandedKeys([]);
      setSearchValue("");
    }
  }, [visible]);

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const treeData = recursionTagKeyTreeDataTag(res);
        setTagTreeData(
          treeData.map((item, index) => ({
            ...item,
            checkable: false,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 将树形节点改为一维数组
  const generateList = (data, dataList) => {
    for (let i = 0; i < data.length; i++) {
      const nodeItem = data[i];
      const { key, title } = nodeItem;
      dataList.push({ key, title });
      if (nodeItem.children) {
        generateList(nodeItem.children, dataList);
      }
    }
    return dataList;
  };

  const loopFindParents = (title, treeData, property = "children") => {
    if (treeData.length == 0 || !title) {
      return treeData;
    }
    const filterArr = [];

    // let filter = treeData.filter(function (item) {
    //   return filterObj(item, title);
    // });
    // console.log(filter, 'filterObj');
    // return filter

    treeData.forEach((item, index) => {
      if (item.title.includes(title)) {
        filterArr.push(item);
      }
      if (item.children && item.children.length > 0) {
        item.children.forEach((atem, andex) => {
          if (atem.title.includes(title)) {
            item.children = item.children.filter((btem) =>
              btem.title.includes(title)
            );
            filterArr.indexOf(item) < 0 && filterArr.push(item);
          }
        });
      }
    });
    return filterArr;
  };

  const filterObj = (item, title) => {
    // item.children = item.children.filter((btem) => btem.title.includes(title));
    if (item.title.indexOf(title) > -1) {
      return true;
    }
    if (item.hasOwnProperty("children")) {
      item.children = item.children.filter((child) => {
        if (child.hasOwnProperty("type")) {
          return child.title.indexOf(title) > -1;
        } else if (child.hasOwnProperty("children")) {
          return filterObj(child, title);
        }
      });
      if (item.children.length > 0) {
        return true;
      }
    } else {
      return item.title.indexOf(title) > -1;
    }
  };

  const onChange = (e) => {
    let { value } = e.target;
    const newValue = String(value).trim();
    const dataList = generateList(tagTreeData, []);
    let expandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(newValue) > -1) {
          return getParentKey(item.key, tagTreeData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(true);
    setSearchValue(newValue);
    setData(loopFindParents(newValue, JSON.parse(JSON.stringify(tagTreeData))));
  };

  // antd tree树形匹配方法
  const getParentKey = (key, tree) => {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  // 树节点展开/收缩
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  // 处理搜索时树形数据高亮
  const loopSearchValue = (treeData) =>
    treeData.map((item) => {
      const index = item.title.indexOf(searchValue);
      const beforeStr = item.title.substr(0, index);
      const afterStr = item.title.substr(index + searchValue.length);
      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: "#f50" }}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      if (item.children) {
        return {
          ...item,
          title,
          key: item.key,
          children: loopSearchValue(item.children),
        };
      }
      return {
        ...item,
        title,
        key: item.key,
      };
    });

  // 点击选择节点
  const onCheck = (val, data) => {
    if (data && data.checkedNodes && data.checkedNodes.length) {
      let checkedNodes = [...data.checkedNodes];
      setCheckedKeys(checkedNodes.map((item) => item.key));
    } else {
      setCheckedKeys([]);
    }
  };

  return (
    <Modal
      className="AddLabelModal"
      visible={visible}
      title="打标签"
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onOk={() => {
        handleSubmit(checkedKeys);
      }}
      onCancel={() => {
        onCancel();
      }}
      confirmLoading={confirmLoading}
    >
      <Spin spinning={loading}>
        <div className="modal-wrap">
          {selectedData.length > 0 ? (
            <div className="modal-customers">
              {selectedData.map((item, index) => (
                <p key={index.toString()}>{item.name}</p>
              ))}
            </div>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ flex: 1 }} />
          )}
          <Divider
            type="vertical"
            style={{ height: "auto", borderLeftColor: "#d9d9d9" }}
          />
          <div className="modal-labelTree">
            <Search
              style={{ borderRadius: "6px", marginBottom: 10 }}
              placeholder=""
              onChange={onChange}
            />
            <Tree
              checkable
              selectable={false}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onCheck={onCheck}
              treeData={loopSearchValue(tagTreeData)}
              height={300}
            />
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default memo(AddLabelModal);
