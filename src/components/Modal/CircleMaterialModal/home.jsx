/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/31 14:04
 * @LastEditTime: 2024/10/21 17:04
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/Modal/CircleMaterialModal/home.jsx
 * @Description: '朋友圈素材'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Input, Pagination, Spin, Card, Typography, Image, Empty, Tabs, Row, Col, Select, message } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import {FileHOC} from 'components/FileHOC/FileHOC';
import './home.less';

const FormItem = Form.Item;
const { Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const CircleMaterialModal = (props) => {
  const { visible, imageNum, onSubmit, onCancel } = props;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});
  const [tabType, setTabType] = useState('Poster');

  useEffect(() => {
    if (visible) {
      fetchList();
    }
  }, [visible]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formForm.validateFields().then((formData) => {
      const { pagination, query, type } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      console.log(type, formData, 'llll');
      if (type == 'Poster' || type == 'Video') {
        delete formData.resourceTypes;
      }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        resourceTypes: type || tabType,
        resourceStatus: 'Added',
        ...query,
        ...formData,
      };

      apiCall('/info/infoResource', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    if (tabType == 'Poster' && dataSource[index].images.length + imageNum > 9) {
      message.error('超过可选数量！');
      return false;
    }
    setSelect({ ...dataSource[index] });
  };


  const onChangeTabs = (type) => {
    fetchList({ type, pagination: { current: 1, pageSize: 8 } });
    setTabType(type);
    formForm.resetFields();
  };

  const onOk = () => {
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    setTabType('Poster');
    formForm.resetFields();
    onSubmit?.(select);
  };

  const handleCancel = () => {
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    setTabType('Poster');
    formForm.resetFields();
    onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择朋友圈素材"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={handleCancel}
      onOk={onOk}
      className="CircleMaterialModal"
    >
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          <Tabs accessKey={tabType} destroyInactiveTabPane onChange={onChangeTabs} tabBarExtraContent={<div style={{ display: 'flex' }}>
            <Form layout={'inline'} form={formForm}>
              {
                tabType == 'Article' && <FormItem name="resourceTypes" initialValue="Article">
                  <Select
                    defaultValue="Article"
                    allowClear
                    style={{ width: '100px' }}
                  >
                    <Option value="Article">推文</Option>
                    <Option value="pageArticle">网页文章</Option>
                  </Select>
                </FormItem>
              }
              <FormItem name="searchStr" getValueFromEvent={(e) => removeInputEmpty(e)}>
                <Input placeholder="请输入关键字" allowClear />
              </FormItem>
              <div className="flex flex-space-between">
                <div>
                  <Button type="primary" onClick={() => handleQuery()} style={{ marginRight: '10px' }}>
                    查询
                  </Button>
                  <Button onClick={() => handleReset()}>重置筛选</Button>
                </div>
              </div>
            </Form>
          </div>}>
            <TabPane tab="文本/图片" key='Poster'>
              <p>内容来自于资源中心的【图片】，文案为该资源的参考话术</p>
              {
                dataSource.length > 0 ? dataSource.map((item, index) => (
                  <div className="picture-card" key={index} onClick={() => handleSelectScript(index)}>
                    <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: '5px' }}>{item.script}</Paragraph>
                    <div className={item.images && item.images.length > 1 ? 'picture-card_img' : ''}>
                      {
                        item.images?.map((atem, andex) => (
                         <FileHOC src={atem}>
                          {(url) => (
                             <Image src={url} preview={false} key={andex} />
                          )}
                         </FileHOC>
                        ))
                      }
                    </div>
                    {
                      item.id == select.id ? <div className="mask">
                        <CheckCircleTwoTone />
                      </div> : ''
                    }
                  </div>
                )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </TabPane>
            <TabPane tab="文本/视频" key='Video'>
              <p>内容来自于资源中心的【视频】，文案为该资源的参考话术</p>
              {
                dataSource.length > 0 ? dataSource.map((item, index) => (
                  <div className="picture-card" key={index} onClick={() => handleSelectScript(index)}>
                    <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: '5px' }}>{item.script}</Paragraph>
                    {
                      item.videos?.length ? <FileHOC src={item.videos[0]}>
                        {(url) => (
                          <video src={url} />
                        )}
                      </FileHOC> : ''
                    }
                    {
                      item.id == select.id ? <div className="mask">
                        <CheckCircleTwoTone />
                      </div> : ''
                    }
                  </div>
                )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </TabPane>
            <TabPane tab="文本/链接" key='Article'>
              <p>内容来自于资源中心的【推文/网页文章】，文案为该资源的参考话术</p>
              {
                dataSource.length > 0 ? dataSource.map((item, index) => (
                  <div className="link-card" key={index} onClick={() => handleSelectScript(index)}>
                    <Paragraph strong ellipsis={{ rows: 2 }}>{item.title}</Paragraph>
                    <Row justify="space-between">
                      <Col span={17}>
                        <Paragraph ellipsis={{ rows: 3 }}>
                          {item.description}
                        </Paragraph>
                      </Col>
                      <Col>
                        <FileHOC src={item.images && item.images[0] || 'error'}>
                          {(url) => (
                            <Image
                            width={54}
                            src={url}
                            fallback="images/fallbackImg.png"
                            preview={false}
                          />
                          )}
                        </FileHOC>
                      </Col>
                    </Row>
                    {
                      item.id == select.id ? <div className="mask">
                        <CheckCircleTwoTone />
                      </div> : ''
                    }
                  </div>
                )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </TabPane>
          </Tabs>

          <Pagination
            {...paginations}
            onChange={onChangePagination}
          />
        </Card>
      </Spin>
    </Modal>
  );
};

export default CircleMaterialModal;
