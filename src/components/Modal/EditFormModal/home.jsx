/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/07 17:40
 * @LastEditTime: 2023/11/09 14:11
 * @LastEditors: Janaeiw
 * @fileId: \weebot_cloud_webfront\src\components\Modal\EditFormModal\home.jsx
 * @Description: ''
 */

import React, { useState, useEffect, memo } from 'react';
import { Form, Modal, Input, Upload } from 'antd';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import { normFile } from 'common/regular';
import { apiCall } from 'common/utils';
import WibotUploadImage from 'components/WibotUploadImage/home';

const FormItem = Form.Item;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
};

const EditFormModal = (props) => {
  const { visible, data, onOk, onCancel } = props.params;
  const [form] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  useEffect(() => {
    if (visible) {
      if (data) {
        const { title, description, image, url } = data;
        form.setFieldsValue({
          title,
          description,
          fileId: image ? [image] : [],
          url
        });
        setImageUrl(image);
      }
    }
  }, [visible]);

  const customRequest = (config) => {
    const File = config.file;
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    apiCall('/file/image', 'POST', data).then((res) => {
      const { fileId, fileUrl } = res;
      setImageUrl(fileUrl);
      // setResourceId(fileId);
      form.setFieldsValue({
        fileId: [fileId]
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setUploadLoading(false);
      });
  };

  const onChangeUpload = (info) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
  };

  const handleResetUpload = (e) => {
    e.preventDefault();// 阻止浏览器默认行为
    e.stopPropagation();// 阻止事件冒泡
    setImageUrl('');
    // setResourceId('');
    form.setFieldsValue({
      fileId: ''
    });
  };

  const handleOk = () => {
    form.validateFields().then((formData) => {
      onOk?.({ ...formData, image: imageUrl });
      form.resetFields();
      setImageUrl('');
      // setResourceId('');
    });
  };

  const handleCancel = () => {
    form.resetFields();
    setImageUrl('');
    onCancel?.();
  };

  return (
    <Modal
      title="编辑标题、描述、封面"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确认"
      cancelText="取消"
    >
      <Form {...layout} form={form}>
        <FormItem name="url" label="链接" rules={[{ required: true, message: '请输入带有【http://或https://】链接', type: 'url' }]}>
          <Input placeholder="请输入带有【http://或https://】链接" allowClear />
        </FormItem>
        <FormItem name="title" label="链接标题" rules={[{ required: true, message: '请输入链接标题' }]}>
          <TextArea maxLength={30} placeholder="请输入（30字内）" allowClear />
        </FormItem>
        <FormItem name="description" label="链接描述" rules={[{ required: true, message: '请输入链接描述' }]}>
          <TextArea maxLength={60} placeholder="请输入链接描述（60字内）" allowClear />
        </FormItem>
        <FormItem name="fileId" valuePropName="fileList" getValueFromEvent={normFile} label="链接封面" rules={[{ required: true, message: '请上传图片' }]}
          extra="建议尺寸500*500px，大小限制为2M，最多上传1张" >
          <Upload
            name="file"
            customRequest={customRequest}
            listType="picture-card"

            showUploadList={false}
            beforeUpload={beforeUpload}
            onChange={onChangeUpload}
          >
            <WibotUploadImage
              imageUrl={imageUrl}
              loading={uploadLoading}
              onClose={handleResetUpload}
            />
          </Upload>
        </FormItem>
      </Form>
    </Modal>
  );
};

export default memo(EditFormModal);
