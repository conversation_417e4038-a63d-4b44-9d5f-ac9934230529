/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/06 09:34
 * @LastEditTime: 2022/10/25 11:24
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\components\Modal\JsonConfigModal\home.jsx
 * @Description: '助理管理-JsonFormModal'
 */

import React from 'react';
import Reflux from 'reflux';
import { Modal, Form, Input } from 'antd';
import ReactJson from 'react-json-view';

const FormItem = Form.Item;
const { TextArea } = Input;

export default class JsonConfigModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      confirmLoading: false,
      mockJson: {},
    };
  }

  componentDidMount () {
    this.props.onRefJsonConfigFn(this);
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps);
  }

  didUpdate = async (prevProps) => {
    const { ts, visible, config } = this.props.params;
    // 时间戳有更新, 属于外面传入
    if (ts != prevProps.params.ts) {
      // 重置状态数据
      this.setState({
        visible,
        mockJson: config ? JSON.parse(config) : {}
      });
      let timer = setTimeout(() => {
        this.formRef.current.setFieldsValue({
          config: config || '{}'
        });
        clearTimeout(timer);
      }, 100);
    }
  }

  onChangeJsonVal = (e) => {
    e.persist();
    this.setState({
      mockJson: JSON.parse(e.target.value)
    });
  }

  handleEdit = (params) => {
    const { updated_src } = params;
    this.setState({ mockJson: updated_src });
    this.formRef.current.setFieldsValue({
      config: JSON.stringify(updated_src)
    });
  }

  handleAdd = (params) => {
    const { updated_src } = params;
    this.setState({ mockJson: updated_src });
    this.formRef.current.setFieldsValue({
      config: JSON.stringify(updated_src)
    });
  }

  handleDelete = (params) => {
    const { updated_src } = params;
    this.setState({ mockJson: updated_src });
    this.formRef.current.setFieldsValue({
      config: JSON.stringify(updated_src)
    });
  }

  onSubmit = async () => {
    try {
      await this.formRef.current.validateFields().then((formData) => {
        this.setState({
          confirmLoading: true
        });
        this.props.params?.onSubmit?.(formData);
      });
    } catch (err) {
      console.log(err);
      this.setState({
        confirmLoading: false
      });
    }
  }

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
      mockJson: {},
    });
  }

  render () {
    const { visible, confirmLoading, mockJson } = this.state;

    return (
      <Modal
        visible={visible}
        title="更新配置"
        maskClosable={false}
        afterClose={null}
        onOk={this.onSubmit}
        onCancel={this.onCancel}
        destroyOnClose
        confirmLoading={confirmLoading}
        width={700}
      >
        <Form layout="horizontal" ref={this.formRef}
          labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} >
          <FormItem label="配置文本域" name="config" initialValue={'{}'}>
            <TextArea
              autoSize={{ minRows: 2, maxRows: 6 }}
              onChange={this.onChangeJsonVal}
            />
          </FormItem>
          <FormItem label="配置视图" >
            <ReactJson
              src={mockJson}
              onEdit={this.handleEdit}
              onAdd={this.handleAdd}
              onDelete={this.handleDelete}
              name={false}
              displayObjectSize={false}
              displayDataTypes={false}
            />
          </FormItem>
        </Form>
      </Modal>
    );
  }
}
