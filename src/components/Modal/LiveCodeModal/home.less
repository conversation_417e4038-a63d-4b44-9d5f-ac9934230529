.LiveCodeModal {
  .ant-card {
    .link-card {
      margin: 0 34px 10px;
      cursor: pointer;
    }

    .picture-card {
      width: 150px;
      height: 228px;
      position: relative;
      display: inline-block;
      margin: 0 36px 10px;
      padding: 10px;
      border: 1px solid #ccc;
      box-shadow: 1px 2px 3px 0px #ccc;
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      vertical-align: middle;
      overflow: hidden;

      div {
        margin: 0;
      }

      video {
        width: 100%;
        height: 170px;
      }

      .ant-image {
        width: 100%;
        height: 170px;
        margin-bottom: 5px;

        .ant-image-img {
          height: 100%;
          object-fit: contain;
        }
      }
    }

    &.select-item {
      overflow: hidden;
    }

    .mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1000;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      -webkit-overflow-scrolling: touch;

      .anticon {
        font-size: 40px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .ant-typography {
      font-size: 12px;
      line-height: 18px;
    }

    .ant-pagination {
      text-align: right;
    }
  }
}