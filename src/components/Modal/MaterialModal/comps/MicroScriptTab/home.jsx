/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/01/08 16:21
 * @LastEditTime: 2023/04/19 11:39
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\components\Modal\MaterialModal\comps\MicroScriptTab\home.jsx
 * @Description: '素材库对话框'
 */

import React from 'react';
import { Typography, Image, Row, Col } from 'antd';
import { CheckCircleTwoTone, PlayCircleOutlined } from '@ant-design/icons';
const { Paragraph } = Typography;
import {FileHOC} from 'components/FileHOC/FileHOC';
const MicroScriptTab = (props) => {
  const {
    item = {},
    index,
    multiple = true,
    selectData,
    handleSelectScript,
  } = props;

  return (
    <div className="MicroScriptItem">
      {(item.type == 'Article' ||
        item.type == 'pageArticle' ||
        item.type == 'FORM' ||
        item.type == 'Product' ||
        item.type == 'SURVEY') && (
        <div
          className="link-card"
          key={index}
          onClick={() => handleSelectScript(item)}
        >
          <Paragraph strong ellipsis={{ rows: 2 }}>
            {item.title}
          </Paragraph>
          <Row justify="space-between">
            <Col span={17}>
              <Paragraph ellipsis={{ rows: 3 }}>{item.description}</Paragraph>
            </Col>
            <Col>
              <FileHOC src={(item.fileId && item.fileId[0]) || 'error'}>
                {(url) => (
                  <Image
                  width={54}
                  src={url}
                  fallback="images/fallbackImg.png"
                  preview={false}
                />
                )}
              </FileHOC>
            </Col>
          </Row>
          {multiple ? (
            selectData.some((i) => i.id == item.id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ''
            )
          ) : item.id == selectData.id ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ''
          )}
          {/* {selectData.some((i) => i.id == item.id) ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ""
          )} */}
        </div>
      )}
      {(item.type == 'Poster' ||
        item.type == 'Picture' ||
        item.type == 'QrCode') && (
        <div
          className="media-card"
          key={index}
          onClick={() => handleSelectScript(item)}
        >
          <FileHOC src={item.fileId && item.fileId[0]}>
            {(url) => (
              <Image src={url} preview={false} />
            )}
          </FileHOC>
          <Paragraph ellipsis={{ rows: 2 }}>{item.name}</Paragraph>
          {multiple ? (
            selectData.some((i) => i.id == item.id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ''
            )
          ) : item.id == selectData.id ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ''
          )}
        </div>
      )}
      {item.type == 'Video' && (
        <div
          className="media-card"
          key={index}
          onClick={() => handleSelectScript(item)}
        >
          {item.fileId?.length ? <FileHOC src={item.fileId[0]}>
            {(url) => (
              <video src={url} />
            )}
          </FileHOC> : ''}
          <PlayCircleOutlined className="media-play" />
          {multiple ? (
            selectData.some((i) => i.id == item.id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ''
            )
          ) : item.id == selectData.id ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ''
          )}
        </div>
      )}
      {item.type == 'MINI_PROGRAM' && (
        <div
          className="applet-card"
          key={index}
          onClick={() => handleSelectScript(item)}
        >
          <div className="applet-card_top">
            <Paragraph
              ellipsis={{ rows: 1 }}
              className="noMargin"
              style={{ minHeight: '0px', textAlign: 'left' }}
            >
              {item.title}
            </Paragraph>
            <FileHOC src={(item.fileId && item.fileId[0]) || "error"}>
              {(url) => (
                <Image
                  width="100%"
                  height="100%"
                  src={url}
                  fallback="images/fallbackImg.png"
                  preview={false}
                />
              )}
            </FileHOC>
          </div>
          <div className="applet-card_bottom">
            <img src="images/applet.png" />
            <span style={{ color: '#7F7F7F' }}>小程序</span>
          </div>
          {multiple ? (
            selectData.some((i) => i.id == item.id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ''
            )
          ) : item.id == selectData.id ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ''
          )}
        </div>
      )}
      {item.type == 'copyWriter' && (
        <div
          className="link-card"
          key={index}
          onClick={() => handleSelectScript(item)}
        >
          <Paragraph ellipsis={{ rows: 3 }}>{item.content}</Paragraph>
          {multiple ? (
            selectData.some((i) => i.id == item.id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ''
            )
          ) : item.id == selectData.id ? (
            <div className="mask">
              <CheckCircleTwoTone />
            </div>
          ) : (
            ''
          )}
        </div>
      )}
    </div>
  );
};

export default MicroScriptTab;
