/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/01/08 16:21
 * @LastEditTime: 2024/11/14 17:03
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/Modal/MaterialModal/home.jsx
 * @Description: '素材库对话框'
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Button,
  Form,
  Input,
  Pagination,
  Spin,
  Card,
  Typography,
  Tabs,
  Image,
  Row,
  Col,
  Empty,
  message,
  Cascader,
  Popover,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import MicroScriptTab from './comps/MicroScriptTab/home';

import './home.less';
import {isV1} from "config";
import {FileHOC} from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Paragraph, Title, Text } = Typography;

const MaterialModal = (props) => {
  const {
    placeholder = null,
    multiple = true, // 是否多选
    materialAmount = 0, // 已选择的资源数
    needScriptFlag = false, // 是否需要选择有参考话术的资源时把参考话术也添加合并为资源素材
    limitNineFlag = true, // 是否限制素材数量为9
    maxLength = 9, // 素材数量上限，默认为9
    isOnlyPicMultiple = false, // 对于比如群发朋友圈选择素材要求图片海报类型多选其他的类型单选的情况
    okText = '确定', // 弹窗确认按钮文案
  } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});
  const [selectPicture, setSelectPicture] = useState(null);
  const [selectPicArr, setSelectPicArr] = useState([]);
  const [selectArr, setSelectArr] = useState([]);
  const [options, setOptions] = useState([]);
  const [buttonDisabled, setButtonDisabled] = useState(true);
  const [tabType, setTabType] = useState('Article');
  const [tabList, setTabList] = useState([
    'Article',
    'pageArticle',
    'Video',
    'copyWriter',
    'MINI_PROGRAM',
    'Picture',
    'Poster',
    'FORM',
    'Product',
    // "MicroScript", // 微化本
    // "ResourceSet", // 资源集
  ]);

  useEffect(async () => {
    const { visible, tabType, tabList } = props.params;
    if (visible) {
      setVisible(visible);
      let timer = setTimeout(async () => {
        // tabType取缓存
        const type = localStorage.getItem('materialModalTabType');
        console.log(type, 'typetype');
        if (tabType) {
          setTabType(tabType);
        } else {
          setTabType(type);
        }
        tabList && setTabList(tabList);
        if (type == 'MicroScript') {
          // 素材类型为微话本
          const cascaderList = localStorage.getItem('materialModalCascader'); // 级联树上一次选择的缓存
          const arrKeys = cascaderList ? JSON.parse(cascaderList) : null;
          microScriptCascade(arrKeys);
        } else {
          fetchList({
            type: tabType || null,
            pagination: { current: 1, pageSize: 8 },
          });
        }
        clearTimeout(timer);
      }, 300);
    }
  }, [props.params.visible]);

  useEffect(() => {
    if (JSON.stringify(select) != '{}') {
      setButtonDisabled(false);
    } else if (selectPicArr.length > 0) {
      setButtonDisabled(false);
    } else if (selectArr.length > 0) {
      setButtonDisabled(false);
    } else {
      setButtonDisabled(true);
    }
  }, [select, selectPicArr, selectArr]);

  useEffect(() => {
    if (tabType == 'MicroScript') {
      const cascaderList = localStorage.getItem('materialModalCascader'); // 级联树上一次选择的缓存
      const arrKeys = cascaderList ? JSON.parse(cascaderList) : null;
      microScriptCascade(arrKeys);
    }
    // 监听tabType变化存缓存
    localStorage.setItem('materialModalTabType', tabType);

    // 对于比如群发朋友圈选择素材要求图片海报类型多选其他的类型单选的情况
    if (isOnlyPicMultiple && (tabType == 'Picture' || tabType == 'Poster')) {
      props.params.multiple = true;
    } else if (isOnlyPicMultiple) {
      props.params.multiple = false;
    }
  }, [tabType]);

  const microScriptCascade = (arrKeys) => {
    setLoading(true);
    apiCall('/info/microScript/cascade/tree', 'GET')
      .then((res) => {
        const TreeData = recursionCascaderTitleKey(res);
        setOptions(TreeData);
        const microScriptId = TreeData[0]?.children[0]?.children[0]?.value;
        console.log(arrKeys, 'arrKeysarrKeys');
        formRef.current.setFieldsValue({
          microScriptItemId: arrKeys
            ? arrKeys
            : [
              TreeData[0].value,
              TreeData[0]?.children[0].value,
              microScriptId,
            ],
        });
        if (arrKeys && arrKeys.length == 3) {
          // 级联树缓存存在并且有第三级
          fetchList({
            type: tabType,
            pagination: { current: 1, pageSize: 8 },
            microScriptItemId: arrKeys[2],
          });
        } else if (!arrKeys) {
          // 级联树缓存不存在
          fetchList({
            type: tabType,
            pagination: { current: 1, pageSize: 8 },
            microScriptItemId: microScriptId,
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const recursionCascaderTitleKey = (treeData) => {
    const newTreeData = [];
    let obj = {};
    treeData.forEach((item) => {
      const tmp = { ...item };
      if (tmp.children) {
        // 内部调用即递归
        tmp.children = recursionCascaderTitleKey(tmp.children);
      }
      obj = {
        label: tmp.title,
        key: tmp.key,
        value: tmp.key,
        children: tmp.children,
      };
      newTreeData.push(obj);
    });
    return newTreeData;
  }

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      setDataSource([]);
      formData.typeId = formData.typeId?.join(',') || null;
      const { pagination, type, microScriptItemId } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const resourceTypes = type || tabType;
      if (resourceTypes != 'MicroScript') {
        delete formData.microScriptItemId;
      }
      let data = {};
      let apiUrl = '/info/infoResource';
      switch (resourceTypes) {
        case 'Poster':
          data = {
            current: pageInfo.current,
            size: pageInfo.pageSize,
            ...formData,
            resourceTypes: 'POSTER_TOOL',
            resourceStatus: 'Added',
          };
          apiUrl = '/info/infoResource';
          break;
        case 'FORM':
          data = {
            current: pageInfo.current,
            size: pageInfo.pageSize,
            type: 'FORM',
            state: 'COLLECTING',
            ...formData,
          };
          apiUrl = '/base/form';
          break;
        case 'MicroScript':
          data = {
            current: pageInfo.current,
            size: pageInfo.pageSize,
            microScriptItemId,
          };
          apiUrl = '/info/microScript/filter';
          break;
        case 'ResourceSet':
          data = {
            current: pageInfo.current,
            size: pageInfo.pageSize,
            needList: true,
            name: formData.searchStr,
            ...formData,
          };
          apiUrl = '/info/resourceSet';
          break;
        default:
          data = {
            current: pageInfo.current,
            size: pageInfo.pageSize,
            resourceTypes: resourceTypes,
            resourceStatus: 'Added',
            subType: resourceTypes == 'Product' ? 'FACE' : null,
            ...formData,
          };
          break;
      }
      apiCall(apiUrl, 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          if (resourceTypes == 'Poster') {
            setDataSource(
              records.map((item) => ({
                ...item,
                type: 'Poster',
                fileId: item.images,
              }))
            );
          } else {
            setDataSource(records);
          }
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: [8, 20, 50, 100],
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
          setDataSource([]);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTabs = (type) => {
    if (type != 'MicroScript') {
      fetchList({ type, pagination: { current: 1, pageSize: 8 } });
    }
    setDataSource([]);
    setTabType(type);
    setSelect({});
    setSelectArr([]);
    setSelectPicture(null);
    setSelectPicArr([]);
  };

  // 微话本级联选择
  const onChangeCascader = (value) => {
    localStorage.setItem('materialModalCascader', JSON.stringify(value));
  };

  const handleQuery = () => {
    const microScriptId = formRef.current.getFieldValue('microScriptItemId');
    if (tabType == 'MicroScript') {
      if (microScriptId && microScriptId.length >= 3) {
        fetchList({ microScriptItemId: microScriptId[2] });
      } else {
        message.error('请选择话术组！');
      }
      return false;
    }
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  // 单选素材
  const handleSelectScript = (data) => {
    setSelect({ ...data });
  };

  // 多选素材
  const handleSelectScriptArr = (data) => {
    const arr = JSON.parse(JSON.stringify(selectArr));
    if (arr.some((i) => i.id == data.id)) {
      const newArr = arr.filter((i) => i.id != data.id);
      setSelectArr(newArr);
    } else {
      const amount = selectArr.length + materialAmount;
      if (limitNineFlag) {
        if (needScriptFlag && amount > maxLength) {
          message.error('超过可选数量！');
          return false;
        } else if (!needScriptFlag && amount > (maxLength - 1)) {
          message.error('超过可选数量！');
          return false;
        }
      }
      arr.push(data);
      setSelectArr(arr);
    }
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    setSelectArr([]);
    setSelectPicture(null);
    setSelectPicArr([]);
    formRef.current.resetFields();
    if (tabType == 'Picture') {
      props.params?.onSubmit?.(
        multiple
          ? selectArr.map((item) => ({
            ...item,
            fileId: item.images,
          }))
          : {
            ...select,
            fileId: select.images,
          }
      );
    } else if (tabType == 'ResourceSet') {
      let newSelectArr = [];
      selectArr?.forEach((item) => {
        item.resourceList.forEach((atem) => {
          if (atem.type == 'Picture') {
            newSelectArr.push({
              ...atem,
              fileId: atem.images,
            });
          } else if (atem.type == 'POSTER_TOOL') {
            newSelectArr.push({
              ...atem,
              fileId: atem.images,
              type: 'Poster',
            });
          } else {
            newSelectArr.push({
              isResourceSet: true,
              ...atem,
            });
          }
        });
      });
      // 处理资源集选择素材过多，只获取（总素材+已选素材中含有的文本素材-当前已选素材）条数。其中如果有文本，按需补充相应素材
      const isCopyWriter = newSelectArr.some(
        (item) => item.type == 'copyWriter'
      );
      newSelectArr = newSelectArr.filter(
        (item, index) =>
          index <
          (needScriptFlag ? 10 : 9) + (isCopyWriter ? 1 : 0) - materialAmount
      );
      newSelectArr.isResourceSet = true;
      console.log(newSelectArr, select, 'newSelectArr-newSelectArr');
      props.params?.onSubmit?.(multiple ? newSelectArr : select);
    } else {
      if (tabType == 'MicroScript' && multiple) {
        selectArr.forEach((item) => {
          switch (item.type) {
            case 'copyWriter':
              item.copyWriter = item.content;
              break;
            case 'MINI_PROGRAM':
              item.miniProgram = {
                ...item,
                fileId: item.fileId[0],
              };
              break;
            case 'pageArticle':
              item.images = item.fileId;
              break;
            case 'Article':
              item.images = item.fileId;
              break;
            case 'Video':
              item.videos = item.fileId;
              break;
            case 'FORM':
              item.image = item.fileId && item.fileId[0];
              break;
            case 'Product':
              item.images = item.fileId;
              break;
          }
          item.script = item.script ? item.script : '';
        });
      }
      props.params?.onSubmit?.(multiple ? selectArr : select);
    }
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    setSelectArr([]);
    setTabList([
      'Article',
      'pageArticle',
      'Video',
      'copyWriter',
      'MINI_PROGRAM',
      'Poster',
      'Picture',
      'FORM',
      'Product',
    ]);
    setSelectPicture(null);
    setSelectPicArr([]);
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="MaterialModal"
      visible={visible}
      width={970}
      title="选择素材"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      okText={okText}
      okButtonProps={{ disabled: buttonDisabled }}
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          {tabType == 'MicroScript' ? (
            <FormItem name="microScriptItemId">
              <Cascader
                options={options}
                onChange={onChangeCascader}
                changeOnSelect
                placeholder="请选择话术"
              />
            </FormItem>
          ) : tabType == 'ResourceSet' ? (
            <FormItem
              name="searchStr"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input
                placeholder={placeholder ? placeholder : '请输入名称'}
                allowClear
              />
            </FormItem>
          ) : (
            <FormItem
              name="searchStr"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input
                placeholder={placeholder ? placeholder : '请输入关键字'}
                allowClear
              />
            </FormItem>
          )}
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          <Tabs
            activeKey={tabType}
            destroyInactiveTabPane
            onChange={onChangeTabs}
          >
            {tabList &&
              tabList.map((item, index) => {
                let content = '';
                switch (item) {
                  case 'Article':
                    content = (
                      <TabPane tab={isV1() ? "推文" : "链接"} key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="link-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <Paragraph strong ellipsis={{ rows: 2 }}>
                                  {atem.title}
                                </Paragraph>
                                <Row justify="space-between">
                                  <Col span={17}>
                                    <Paragraph ellipsis={{ rows: 3 }}>
                                      {atem.description}
                                    </Paragraph>
                                  </Col>
                                  <Col>
                                    <FileHOC src={(atem.images && atem.images[0]) || 'error'}>
                                      {(url) => (
                                        <Image
                                          width={54}
                                          src={url}
                                          fallback="images/fallbackImg.png"
                                          preview={false}
                                        />
                                      )}
                                    </FileHOC>
                                  </Col>
                                </Row>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'pageArticle':
                    content = (
                      <TabPane tab={isV1() ? "网页文章" : "雷达海报"} key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="link-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <Paragraph strong ellipsis={{ rows: 2 }}>
                                  {atem.title}
                                </Paragraph>
                                <Row justify="space-between">
                                  <Col span={17}>
                                    <Paragraph ellipsis={{ rows: 3 }}>
                                      {atem.description}
                                    </Paragraph>
                                  </Col>
                                  <Col>
                                    <FileHOC src={(atem.images && atem.images[0]) || 'error'}>
                                      {(url) => (
                                        <Image
                                          width={54}
                                          src={url}
                                          fallback="images/fallbackImg.png"
                                          preview={false}
                                        />
                                      )}
                                    </FileHOC>
                                  </Col>
                                </Row>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'Video':
                    content = (
                      <TabPane tab="视频" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="media-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                {atem.videos?.length ? (
                                  <FileHOC src={atem.videos[0]}>
                                    {(url) => (
                                      <video src={url} />
                                    )}
                                  </FileHOC>
                                ) : (
                                  ''
                                )}
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'copyWriter':
                    content = (
                      <TabPane tab="文案" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="link-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <Paragraph ellipsis={{ rows: 3 }}>
                                  {atem.copyWriter}
                                </Paragraph>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'MINI_PROGRAM':
                    content = (
                      <TabPane tab="小程序" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="applet-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <div className="applet-card_top">
                                  <Paragraph
                                    ellipsis={{ rows: 1 }}
                                    className="noMargin"
                                    style={{ minHeight: '0px' }}
                                  >
                                    {atem.miniProgram?.title}
                                  </Paragraph>
                                  <FileHOC src={atem.miniProgram?.fileId || 'error'}>
                                    {(url) => (
                                      <Image
                                      width="100%"
                                      height={120}
                                      src={url}
                                      fallback="images/fallbackImg.png"
                                      style={{ objectFit: 'cover' }}
                                      preview={false}
                                    />
                                    )}
                                  </FileHOC>
                                </div>
                                <div className="applet-card_bottom">
                                  <img src="images/applet.png" />
                                  <span style={{ color: '#7F7F7F' }}>小程序</span>
                                </div>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'Poster':
                    content = (
                      <TabPane tab="海报" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="media-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <div
                                  onClick={(e) => {
                                    e.preventDefault(); // 阻止浏览器默认行为
                                    e.stopPropagation(); // 阻止事件冒泡
                                  }}
                                >
                                  <FileHOC src={atem.fileId}>
                                    {(url) => (
                                      <Image src={url} />
                                    )}
                                  </FileHOC>
                                </div>
                                <Paragraph ellipsis={{ rows: 2 }}>
                                  {atem.posterName}
                                </Paragraph>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'Picture':
                    content = (
                      <TabPane tab="图片" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="media-card"
                                key={andex}
                                onClick={(e) => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <div
                                  onClick={(e) => {
                                    e.preventDefault(); // 阻止浏览器默认行为
                                    e.stopPropagation(); // 阻止事件冒泡
                                  }}
                                >
                                  {atem?.images?.length && (
                                    <FileHOC src={atem.images[0]}>
                                      {(url) => (
                                        <Image src={url} />
                                      )}
                                    </FileHOC>
                                  )}
                                </div>
                                <Paragraph ellipsis={{ rows: 2 }}>
                                  {atem.title}
                                </Paragraph>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'FORM':
                    content = (
                      <TabPane tab="问卷" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <div
                              className="link-card"
                              key={andex}
                              onClick={() => {
                                multiple
                                  ? handleSelectScriptArr(atem)
                                  : handleSelectScript(atem);
                              }}
                            >
                              <Paragraph strong ellipsis={{ rows: 2 }}>
                                {atem.shareTitle}
                              </Paragraph>
                              <Row justify="space-between">
                                <Col span={17}>
                                  <Paragraph ellipsis={{ rows: 3 }}>
                                    {atem.shareDescription}
                                  </Paragraph>
                                </Col>
                                <Col>
                                  <FileHOC src={atem.shareImage || 'error'}>
                                    {(url) => (
                                      <Image
                                      width={54}
                                      src={url}
                                      fallback="images/fallbackImg.png"
                                      preview={false}
                                    />
                                    )}
                                  </FileHOC>
                                </Col>
                              </Row>
                              {multiple ? (
                                selectArr.some((i) => i.id == atem.id) ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )
                              ) : atem.id == select.id ? (
                                <div className="mask">
                                  <CheckCircleTwoTone />
                                </div>
                              ) : (
                                ''
                              )}
                            </div>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'Product':
                    content = (
                      <TabPane tab="产品" key={item}>
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <Popover placement="topLeft" getPopupContainer={(triggerNode) => triggerNode.parentNode} content={<>
                              <p>推荐理由：{atem.reason}</p>
                              <p style={{ margin: "0px" }}>参考话术：{atem.script}</p>
                            </>}>
                              <div
                                className="link-card"
                                key={andex}
                                onClick={() => {
                                  multiple
                                    ? handleSelectScriptArr(atem)
                                    : handleSelectScript(atem);
                                }}
                              >
                                <Paragraph strong ellipsis={{ rows: 2 }}>
                                  {atem.title}
                                </Paragraph>
                                <Row justify="space-between">
                                  <Col span={17}>
                                    <Paragraph ellipsis={{ rows: 3 }}>
                                      {atem.description}
                                    </Paragraph>
                                  </Col>
                                  <Col>
                                    <FileHOC src={(atem.images && atem.images[0]) || 'error'}>
                                      {(url) => (
                                        <Image
                                        width={54}
                                        src={url}
                                        fallback="images/fallbackImg.png"
                                        preview={false}
                                      />
                                      )}
                                    </FileHOC>
                                  </Col>
                                </Row>
                                {multiple ? (
                                  selectArr.some((i) => i.id == atem.id) ? (
                                    <div className="mask">
                                      <CheckCircleTwoTone />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                ) : atem.id == select.id ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )}
                              </div>
                            </Popover>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'MicroScript':
                    content = (
                      <TabPane
                        tab="微话本"
                        key={item}
                        className="MicroScriptTab"
                      >
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <MicroScriptTab
                              key={andex}
                              item={atem}
                              index={andex}
                              selectData={multiple ? selectArr : select}
                              handleSelectScript={
                                multiple
                                  ? handleSelectScriptArr
                                  : handleSelectScript
                              }
                            />
                          ))
                        ) : (
                          <Empty
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            style={{ width: '100%' }}
                          />
                        )}
                      </TabPane>
                    );
                    break;
                  case 'ResourceSet':
                    content = (
                      <TabPane
                        tab="资源集"
                        key={item}
                        style={
                          dataSource.length > 0 && {
                            display: 'flex',
                            flexWrap: 'wrap',
                            alignItems: 'flex-start',
                          }
                        }
                      >
                        {dataSource.length > 0 ? (
                          dataSource.map((atem, andex) => (
                            <div
                              className="resourceSet-card"
                              key={andex}
                              onClick={() => {
                                multiple
                                  ? handleSelectScriptArr(atem)
                                  : handleSelectScript(atem);
                              }}
                            >
                              <Title level={4} ellipsis={{ rows: 2 }}>
                                {atem.name}
                              </Title>
                              <Text type="secondary">{atem.groupName}</Text>
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                }}
                              >
                                {atem.typeCount?.Article && (
                                  <Text>推文 {atem.typeCount?.Article} 个</Text>
                                )}
                                {atem.typeCount?.pageArticle && (
                                  <Text>
                                    网页文章 {atem.typeCount?.pageArticle} 个
                                  </Text>
                                )}
                                {atem.typeCount?.copyWriter && (
                                  <Text>
                                    文案 {atem.typeCount?.copyWriter} 个
                                  </Text>
                                )}
                                {atem.typeCount?.POSTER_TOOL && (
                                  <Text>
                                    海报 {atem.typeCount?.POSTER_TOOL} 个
                                  </Text>
                                )}
                                {atem.typeCount?.Picture && (
                                  <Text>图片 {atem.typeCount?.Picture} 个</Text>
                                )}
                                {atem.typeCount?.Video && (
                                  <Text>视频 {atem.typeCount?.Video} 个</Text>
                                )}
                                {atem.typeCount?.MINI_PROGRAM && (
                                  <Text>
                                    小程序 {atem.typeCount?.MINI_PROGRAM} 个
                                  </Text>
                                )}
                                {atem.typeCount?.Product && (
                                  <Text>产品 {atem.typeCount?.Product} 个</Text>
                                )}
                              </div>
                              {multiple ? (
                                selectArr.some((i) => i.id == atem.id) ? (
                                  <div className="mask">
                                    <CheckCircleTwoTone />
                                  </div>
                                ) : (
                                  ''
                                )
                              ) : atem.id == select.id ? (
                                <div className="mask">
                                  <CheckCircleTwoTone />
                                </div>
                              ) : (
                                ''
                              )}
                            </div>
                          ))
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </TabPane>
                    );
                    break;
                }
                return content;
              })}
          </Tabs>
          {!selectPicture && (
            <Pagination {...paginations} onChange={onChangePagination} />
          )}
        </Card>
      </Spin>
    </Modal>
  );
};

export default MaterialModal;
