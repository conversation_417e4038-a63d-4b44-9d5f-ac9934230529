.MaterialModal {
  .ant-card {
    .link-card {
      margin: 0 34px 10px;
      min-height: 112px;
      cursor: pointer;
    }

    .media-card {
      width: 150px;
      height: 228px;
      position: relative;
      display: inline-block;
      margin: 0 36px 10px;
      padding: 10px;
      border: 1px solid #ccc;
      box-shadow: 1px 2px 3px 0px #ccc;
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      vertical-align: middle;
      overflow: hidden;

      div {
        margin: 0;
      }

      video {
        width: 100%;
        height: 170px;
      }

      .ant-image {
        width: 100%;
        height: 170px;
        margin-bottom: 5px;

        .ant-image-img {
          height: 100%;
          object-fit: contain;
        }
      }

      .picture-card_img {
        height: 195px;

        .ant-image {
          width: 60px;
          height: 60px;
          margin-right: 5px;
        }
      }
    }

    .picture-card {
      width: 220px;
      height: 255px;
      position: relative;
      display: inline-block;
      margin: 0 36px 10px;
      padding: 10px;
      border: 1px solid #ccc;
      box-shadow: 1px 2px 3px 0px #ccc;
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      vertical-align: middle;
      overflow: hidden;

      div {
        margin: 0;
      }

      .ant-image {
        width: 100%;
        height: 170px;
        margin-bottom: 5px;

        .ant-image-img {
          height: 100%;
          object-fit: contain;
        }
      }

      .picture-card_img {
        height: 195px;

        .ant-image {
          width: 60px;
          height: 60px;
          margin-right: 5px;
        }
      }

      .picture-card_one {
        height: 195px;
      }
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .anticon-close {
        font-size: 20px;
        cursor: pointer;
      }
    }

    .selectPicture {
      .pictureItem {
        position: relative;
        width: 150px;
        height: 100%;
        display: inline-block;
        margin-right: 20px;
        margin-bottom: 10px;
      }

      .ant-image {
        width: 100%;
        height: 150px;
        cursor: pointer;

        .ant-image-img {
          height: 100%;
          object-fit: cover;
        }
      }
    }

    &.select-item {
      overflow: hidden;
    }

    .mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1000;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      -webkit-overflow-scrolling: touch;

      .anticon {
        font-size: 40px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .ant-typography {
      font-size: 12px;
      line-height: 18px;
      min-height: 36px;
    }

    .ant-pagination {
      text-align: right;
    }

    .applet-card {
      cursor: pointer;
      position: relative;
      width: 185px;
      height: auto;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin: 0 34px 10px;
      overflow: hidden;
      display: inline-block;
      box-sizing: border-box;

      .applet-card_top {
        padding: 5px 10px 0px 10px;

        .ant-image {
          margin: 5px 0;
        }
      }

      .applet-card_bottom {
        display: flex;
        align-items: center;
        font-size: 12px;
        border-top: 1px solid #f2f2f2;
        padding: 0 10px;
        line-height: 25px;

        img {
          width: 13px;
          height: 13px;
        }
      }

      .noMargin {
        margin-bottom: 0px;
      }
    }

    .resourceSet-card {
      position: relative;
      display: inline-block;
      width: 230px;
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 6px;
      text-align: left;
      white-space: break-spaces;
      overflow: hidden;
      margin: 0 34px 10px;
      min-height: 112px;
      cursor: pointer;

      .ant-typography {
        min-height: auto;
        margin: 0;
      }
    }

    .ant-popover-content {
      max-width: 500px;
    }
  }

  .MicroScriptTab {
    display: flex;
    flex-wrap: wrap;

    //   column-count: 3;
    // column-gap: 10px;
    .MicroScriptItem {
      display: inline-block;
      width: 230px;
      margin: 0 34px 10px;
      text-align: center;

      .link-card {
        height: 120px;
        margin: 0px;
      }

      .media-card {
        width: 120px;
        height: 120px;

        video {
          width: 100%;
          height: 100%;
        }

        .ant-image {
          width: 100%;
          height: 100%;
        }

        .media-play {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .applet-card {
        width: 120px;
        height: 120px;
        margin: 0px;
      }
    }
  }

  .ant-modal-body {
    .ant-tabs-content-holder {
      margin-bottom: 10px;
      max-height: 500px;
      overflow-y: scroll;
    }
  }
}