/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/22 11:31
 * @LastEditTime: 2023/11/30 15:22
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/components/Modal/OperateModal/index.jsx
 * @Description: '操作对话框'
 */

import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { Modal } from 'antd';

const OperateModal = forwardRef((props, ref) => {
  const { visible, title = '删除确认', content = '是否确认删除该数据？', onSubmit, onCancel } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false)

  useImperativeHandle(ref, () => ({
    onStopLoading,
  }));

  useEffect(() => {
    if (!visible) {
      setConfirmLoading(false)
    }
  }, [visible]);

  const onStopLoading = () => {
    setConfirmLoading(false)
  }

  return (
    <Modal
      visible={visible}
      title={title}
      destroyOnClose
      width={450}
      confirmLoading={confirmLoading}
      onOk={() => {
        setConfirmLoading(true)
        onSubmit?.();
      }}
      onCancel={() => {
        setConfirmLoading(false);
        onCancel?.();
      }}
    >
      {content}
    </Modal>
  );
});

export default OperateModal;
