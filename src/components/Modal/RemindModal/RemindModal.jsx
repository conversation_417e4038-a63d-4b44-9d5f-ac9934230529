/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/24 9:00
 * @LastEditTime: 2022/10/25 11:24
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description: '操作详情弹窗'
 */

import React, { memo } from 'react';
import { Modal, Button } from 'antd';

const RemindModal = (props) => {
  const {
    visible,
    title,
    content,
    handleSubmit,
    onCancel,
    footer = null
  } = props;

  return (
    <Modal
      visible={visible}
      title={title}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onOk={handleSubmit}
      onCancel={onCancel}
      footer={footer ? footer : [
        <Button key="close" type="primary" onClick={onCancel}>
          取消
        </Button>,
        <Button key="sure" type="primary" onClick={handleSubmit}>
          确认
        </Button>,
      ]}
    // confirmLoading={confirmLoading}
    >
      {content}
    </Modal>
  );
};
export default memo(RemindModal);