
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2024/10/21 17:05
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/Modal/ScriptModal/home.jsx
 * @Description: '话术库对话框'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Form, Input, Pagination, Spin, Card, Typography, Image, Row, Col, Empty, TreeSelect } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import {FileHOC} from 'components/FileHOC/FileHOC';
import './home.less';

const FormItem = Form.Item;
const { Paragraph } = Typography;
const { SHOW_PARENT } = TreeSelect;

const ScriptModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [typeOptions, setTypeOptions] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      let timer = setTimeout(() => {
        getInfoTypeOptions();
        fetchList();
        clearTimeout(timer);
      }, 300);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      formData.typeId = formData.typeId?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall('/info/script/page', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: [8, 20, 50, 100],
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getInfoTypeOptions = () => {
    const data = {
      type: 'Script'
    };
    apiCall('/info/informationType/option', 'GET', data).then((res) => {
      setTypeOptions([
        {
          title: '全选',
          value: '',
          key: '',
          children: res.map((item) => ({
            title: item.name,
            value: item.id,
            key: item.id,
          })
          )
        }
      ]);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    // 选择后返回顶部
    document.getElementsByClassName('item')[index].scrollTop = 0;
    setSelect({ ...dataSource[index] });
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setSelect({});
    setDataSource([]);
    setTypeOptions([]);
    setPaginations({ current: 1, pageSize: 8 });
    formRef.current.resetFields();
    props.params?.onSubmit?.(select);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setSelect({});
    setDataSource([]);
    setTypeOptions([]);
    setPaginations({ current: 1, pageSize: 8 });
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择话术"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="ScriptModal"
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="title" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="话术标题" allowClear />
          </FormItem>
          <FormItem name="typeId" >
            <TreeSelect
              placeholder='话术类型'
              treeData={typeOptions}
              treeCheckable
              treeDefaultExpandedKeys={['']}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp='title'
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          {
            dataSource.length
              ? <>
                <ul className='list-item'>
                  {
                    dataSource?.map((item, index) =>
                      <li key={index} className={item.id == select.id ? 'item select-item' : 'item'} onClick={() => handleSelectScript(index)}>
                        <Paragraph className='title' ellipsis={{ tooltip: true }}>{item.title}</Paragraph>
                        <div style={{ padding: '10px' }}>
                          {
                            item.materialLibraryList?.length ? item.materialLibraryList.map((atem, andex) =>
                              <div key={andex}>
                                {
                                  atem.type == 'Text' ? <Paragraph key={andex} className='describe' ellipsis={{ rows: 3, tooltip: true }}>{atem.content}</Paragraph> : ''
                                }
                                {
                                  atem.type == 'Picture' ? atem.fileId?.map((btem, bndex) => <FileHOC src={btem}>
                                    {(url) => (
                                      <Image key={bndex} style={{ margin: '10px 0' }} src={url} preview={false}/>
                                    )}
                                  </FileHOC>) : ''
                                }
                                {
                                  atem.type == 'Video' ? atem.fileId?.map((btem, bndex) => <FileHOC src={btem}>
                                    {(url) => (
                                      <video key={bndex} style={{ margin: '10px 0' }} src={url}/>
                                    )}
                                  </FileHOC>) : ''
                                }
                                {
                                  atem.type == 'Article' || atem.type == 'LINK_CARD'
                                    ? atem.title ? <div className="link-card" key={andex}>
                                      <Paragraph strong ellipsis={{ rows: 2, tooltip: true }}>{atem.title}</Paragraph>
                                      <Row justify="space-between">
                                        <Col span={17}>
                                          <Paragraph style={{ fontSize: '12px', lineHeight: '18px' }} ellipsis={{ rows: 3, tooltip: true }}>
                                            {atem.description}
                                          </Paragraph>
                                        </Col>
                                        <Col>
                                          <FileHOC src={atem.fileId && atem.fileId[0] || 'error'}>
                                            {(url) => (
                                              <Image
                                              width={54}
                                              src={url}
                                              fallback="images/fallbackImg.png"
                                              preview={false}
                                            />
                                            )}
                                          </FileHOC>
                                        </Col>
                                      </Row>
                                    </ div> : <Paragraph key={andex} className='describe' ellipsis={{ rows: 3, tooltip: true }}>{atem.url}</Paragraph> : ''
                                }
                              </div>
                            ) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                          }
                          {
                            item.id == select.id ? <div className="mask">
                              <CheckCircleTwoTone />
                            </div> : ''
                          }
                        </div>
                      </li>
                    )
                  }
                </ul>
                <Pagination
                  {...paginations}
                  onChange={onChangePagination}
                />
              </> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </Card>
      </Spin>
    </Modal>
  );
};

export default ScriptModal;
