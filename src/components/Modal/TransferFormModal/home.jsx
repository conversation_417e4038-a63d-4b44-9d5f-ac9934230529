/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/21 11:39
 * @LastEditTime: 2023/10/09 16:06
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/Modal/TransferFormModal/home.jsx
 * @Description: ''
 */

import React, { memo, useEffect, useState } from 'react';
import { Modal, Input, Spin, Tree, Row, Col, Avatar, Image, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import './home.less';
import {FileHOC} from 'components/FileHOC/FileHOC';
const { Search } = Input;

const TransferFormModal = (props) => {
  const {
    visible,
    title,
    isSingle = false,
    dataSource,
    empData,
    onSubmit,
    onCancel,
  } = props;

  const [loading, setLoading] = useState(true);
  const [targetKeys, setTargetKeys] = useState([]);
  const [treeSelectedKeys, setTreeSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [treeSelectData, setTreeSelectData] = useState([]);
  const [allNodeDataSource, setAllNodeDataSource] = useState([]);
  const [singleSelectKey, setSingleSelectKey] = useState([]);
  const [singleSelectData, setSingleSelectData] = useState(null);
  const [data, setData] = useState([]);
  const [checkedNodes, setCheckedNodes] = useState({
    checked: true
  });

  useEffect(() => {
    if (dataSource && dataSource.length >= 0) {
      const newAllDataSource = getinitData(dataSource);
      const newFilterAllDataSource = newAllDataSource.filter((item) => !item.children);
      setAllNodeDataSource(newFilterAllDataSource);
      setTreeSelectedKeys(empData?.map((item) => item.key) ?? []);
      setExpandedKeys(newFilterAllDataSource?.map((item) => item.key) ?? []);
      // setTargetKeys(empData?.map((item) => item.key));//已经被选择过的key设置禁选
      if (isSingle) {
        setSingleSelectKey(empData?.map((item) => item.key) ?? []);
      }
      setData(dataSource);
      setLoading(false);
    }
  }, [dataSource]);

  const getinitData = (dataSource) => {
    const transferDataSource = [];
    function flatten (list = []) {
      list?.forEach((item) => {
        transferDataSource.push(item);
        flatten(item.children);
      });
    }
    flatten(dataSource);
    return transferDataSource;
  };

  // 初始化树数据
  const transferDataSource = [];
  function flatten (list = []) {
    list?.forEach((item) => {
      transferDataSource.push(item);
      flatten(item.children);
    });
  }
  flatten(dataSource);

  useEffect(() => {
    const newTransferDataSource = transferDataSource.filter((item) => !item.children);
    if (treeSelectedKeys.length > 0) {
      let Arr = [];
      if (checkedNodes?.key) {
        if (checkedNodes?.checked) {
          Arr = newTransferDataSource.filter((item, index) => item.key == checkedNodes.key);
          setTreeSelectData([...treeSelectData, ...Arr]);
        } else {
          Arr = treeSelectData.filter((item, index) => item.key != checkedNodes.key);
          setTreeSelectData(Arr);
        }
        setCheckedNodes({ checked: true });
        return false;
      }
      Arr = newTransferDataSource.filter((item, index) => {
        if (treeSelectedKeys.indexOf(item.key) > -1) {
          return item;
        }
      });
      setTreeSelectData(Arr);
    }
  }, [treeSelectedKeys]);

  useEffect(() => {
    if (singleSelectKey.length > 0) {
      const newTransferDataSource = transferDataSource.filter((item) => !item.children);
      const obj = newTransferDataSource.find((item, index) => singleSelectKey.indexOf(item.key) > -1);
      setSingleSelectData(obj);
    }
  }, [singleSelectKey]);

  // 包装需要返回的树的数据格式
  const generateTree = (treeNodes = [], checkedKeys = []) => (
    treeNodes?.map(({ children, ...props }) => {
      const index = props.name?.indexOf(searchValue);
      let name = '';
      if (index > -1) {
        const beforeStr = props.name.substr(0, index);
        const afterStr = props.name.substr(index + searchValue.length);
        name = <div style={{ lineHeight: '15px' }}>
          {beforeStr}
          <span style={{ color: '#f50' }}>{searchValue}</span>
          {afterStr}
        </div>;
      } else {
        name = <div style={{ lineHeight: '15px' }}>{props.name}</div>;
      }
      return (
        {
          ...props,
          name,
          // disabled: checkedKeys.includes(props.key),
          children: generateTree(children, checkedKeys),
        }
      );
    })
  );

  // 将树形节点改为一维数组
  const generateList = (data, dataList) => {
    for (let i = 0; i < data.length; i++) {
      const nodeItem = data[i];
      const { key, name } = nodeItem;
      dataList.push({ key, name, });
      if (nodeItem.children) {
        generateList(nodeItem.children, dataList);
      }
    }
    return dataList;
  };
  // antd tree树形匹配方法
  const getParentKey = (key, tree) => {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  const loopFindParents = (title, treeData, property = 'children') => {
    if (treeData.length == 0 || !title) {
      return treeData;
    }
    const filterArr = [];
    treeData.forEach((item, index) => {
      if (item.name.includes(title)) {
        filterArr.push(item);
      }
      if (item.children && item.children.length > 0) {
        item.children.forEach((atem, andex) => {
          if (atem.name.includes(title)) {
            item.children = item.children.filter((btem) => btem.name.includes(title));
            filterArr.indexOf(item) < 0 && filterArr.push(item);
          }
        });
      }
    });
    return filterArr;
  };

  // 搜索框发生改变
  const onSearchChange = (e) => {
    let { value } = e.target;
    const newValue = String(value).trim();
    const dataList = generateList(dataSource, []);
    let expandedKeys = dataList.map((item) => {
      if (item.name?.indexOf(newValue) > -1) {
        return getParentKey(item.key, dataSource);
      }
      return null;
    }).filter((item, i, self) => item && self.indexOf(item) === i);
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(true);
    setSearchValue(newValue);
    setData(loopFindParents(newValue, JSON.parse(JSON.stringify(dataSource))));
  };

  // 树节点展开/收缩
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  // 点击选择节点
  const onCheck = (val, data) => {
    if (data && data.checkedNodes && data.checkedNodes.length) {
      let checkedNodes = [...data.checkedNodes];
      console.log(checkedNodes, 'checkedNodes');
      const newCheckedNodes = checkedNodes.filter((item) => !item.children || !(item.children.length > 0));
      setTreeSelectedKeys(
        newCheckedNodes.map((item) => item.key)
      );
      setCheckedNodes({
        ...data.node,
        checked: data.checked
      });
      // setTreeSelectData(checkedNodes);
    } else {
      setTreeSelectedKeys([]);
      setTreeSelectData([]);
    }
  };

  // 单选选择节点
  const onSelect = (selectedKeys, data) => {
    let node = data.node;
    if (!node.children) {
      setSingleSelectKey(selectedKeys);
    }
  };

  // 全选
  const onCheckboxChange = () => {
    const newTransferDataSource = transferDataSource.filter((item) => !item.children);
    const allKeys = newTransferDataSource.filter((item) => !item.disabled)?.map((i) => i.key);
    setTreeSelectedKeys(allKeys);
  };

  // 取消全选
  const onCancelChoose = () => {
    setTreeSelectedKeys([]);
    setTreeSelectData([]);
  };

  const onCloseItem = (key, value = null) => {
    if (value == 'single') {
      setSingleSelectKey([]);
      setSingleSelectData(null);
      return;
    }
    const newTreeSelectedKeys = treeSelectedKeys.filter((item) => item !== key);
    const newtreeSelectData = treeSelectData.filter((item) => item.key !== key);
    setTreeSelectedKeys([...newTreeSelectedKeys]);
    setTreeSelectData([...newtreeSelectData]);
  };

  const onOk = () => {
    if (isSingle) {
      onSubmit([singleSelectData]);
      // return;
    } else {
      onSubmit(treeSelectData);
    }
    setTreeSelectData([]);
    setTreeSelectedKeys([]);
    setSingleSelectKey([]);
    setSingleSelectData(null);
    setAutoExpandParent(true);
    setExpandedKeys([]);
    setSearchValue('');
  };
  const onModalCancel = () => {
    onCancel();
    setTreeSelectData([]);
    setTreeSelectedKeys([]);
    setSingleSelectKey([]);
    setSingleSelectData(null);
    setAutoExpandParent(true);
    setExpandedKeys([]);
    setSearchValue('');
  };

  const renderHtml = (nodeData) => (
    <Row gutter={16}>
      {
        nodeData.avatar && <Col span={6}>
          <Avatar shape="square" size={30} src={<FileHOC src={nodeData.avatar}>
            {(url) => (
              <Image src={url} preview={false} />
            )}
          </FileHOC>} />
        </Col>
      }
      <Col span={18}>
        <div style={{ lineHeight: '15px' }}>{nodeData.name}</div>
        <div style={{ color: !nodeData.children?.length > 0 ? '#AAAAAA' : '', fontSize: !nodeData.children?.length > 0 ? '12px' : '' }}>{nodeData.dep}</div>
      </Col>
    </Row>
  );
  const renderFooter = (props, { direction }) => {
    if (direction === 'left') {
      return (
        <Button size="small" style={{ float: 'left', margin: 5 }} onClick={onCheckboxChange}>
          全部员工
        </Button>
      );
    }
    return (
      <Button size="small" style={{ float: 'right', margin: 5 }} onClick={onCancelChoose}>
        清空选择
      </Button>
    );
  };

  return (
    <Modal
      visible={visible}
      title={title || '选择员工'}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onOk={onOk}
      onCancel={onModalCancel}
      className="transferFormModal"
    >
      <Spin spinning={loading}>
        <div className='emp-modal-wrap'>
          <div className='emp-tree-left'>
            {/* <Input
              suffix={<SearchOutlined style={{ cursor: 'pointer', color: '#d9d9d9' }} />}
              style={{ width: '200px', borderRadius: '5px', margin: '10px 0px' }}
              onChange={onSearchChange}
              className="modal-labelTree-search"
            /> */}
            <Search style={{ borderRadius: '6px', padding: '10px 10px 10px 0' }} placeholder="" onChange={onSearchChange} />
            <Tree
              height={300}
              blockNode
              selectable={isSingle}
              checkable={!isSingle}
              defaultExpandAll
              titleRender={renderHtml}
              checkedKeys={treeSelectedKeys}
              treeData={generateTree(data, targetKeys)}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              onCheck={onCheck}
              onSelect={onSelect}
              className="modal-empTree-tree"
            />
            {/* {
              !isSingle ? <div style={{ float: 'left', margin: 5 }}>
                <Button size="small" onClick={onCheckboxChange}>
                  选择全部
                </Button>
                <span>（{allNodeDataSource.length}）</span>
              </div> : ''
            } */}
          </div>
          {
            !isSingle ? <div className='emp-tree-right' style={{ flex: 1, padding: '10px' }}>
              {
                treeSelectData.map((item, index) => (
                  <Row gutter={16} key={index} style={{ position: 'relative' }}>
                    {
                      item.avatar && <Col span={6}>
                        <Avatar shape="square" size={30} src={<FileHOC src={item.avatar}>
                          {(url) => (
                            <Image src={url} preview={false} />
                          )}
                        </FileHOC>} />
                      </Col>
                    }
                    <Col span={18} style={{ padding: '10px' }}>
                      <div style={{ lineHeight: '15px' }}>{item.name}</div>
                      <div style={{ color: !item.children?.length > 0 ? '#AAAAAA' : '', fontSize: !item.children?.length > 0 ? '12px' : '' }}>{item.dep}</div>
                    </Col>
                    <CloseOutlined onClick={() => { onCloseItem(item.key); }} style={{
                      position: 'absolute',
                      right: '46px',
                      top: '10px',
                    }} />
                  </Row>
                ))
              }
              <div style={{ position: 'absolute', right: 0, bottom: 0, margin: 5 }}>
                <Button size="small" onClick={onCancelChoose}>
                  清空选择
                </Button>
                <span>（{treeSelectData.length}）</span>
              </div>
            </div>
              : <div className='emp-tree-right' style={{ flex: 1, padding: '10px' }}>
                {
                  singleSelectData && <Row gutter={16} style={{ position: 'relative' }}>
                    {
                      singleSelectData.avatar && <Col span={6}>
                        <Avatar shape="square" size={30} src={<FileHOC src={singleSelectData.avatar}>
                          {(url) => (
                            <Image src={url} preview={false} />
                          )}
                        </FileHOC>} />
                      </Col>
                    }
                    <Col span={18} style={{ padding: '10px' }}>
                      <div style={{ lineHeight: '15px' }}>{singleSelectData.name}</div>
                      <div style={{ color: !singleSelectData.children?.length > 0 ? '#AAAAAA' : '', fontSize: !singleSelectData.children?.length > 0 ? '12px' : '' }}>{singleSelectData.dep}</div>
                    </Col>
                    <CloseOutlined onClick={() => { onCloseItem(singleSelectData.key, 'single'); }} style={{
                      position: 'absolute',
                      right: '46px',
                      top: '10px',
                    }} />
                  </Row>
                }
              </div>
          }
        </div>
      </Spin>
    </Modal>
  );
};

export default memo(TransferFormModal);
