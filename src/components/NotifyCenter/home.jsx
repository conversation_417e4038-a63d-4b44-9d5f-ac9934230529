import FilterBar from "components/FilterBar/FilterBar"
import { <PERSON><PERSON>, Button, Card, Descriptions, Form, Modal, notification, Select, Table, Tag, Tooltip } from "antd"
import React, { Fragment, useEffect, useState } from "react"
import { useForm } from "antd/es/form/Form"
import moment from "moment"
import { apiCall } from "common/utils"
import { ellipsis } from "@wecom/DigitalHuman/group/utils"

const NotifyCenter = () => {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const columns = [
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: "220px",
      align: "center",
      render(value, record, index) {
        let text = ''
        switch (value) {
          case "AGENT_HANDLE_LIMIT":
            text = "坐席单人处理量抵达上限"
            break;
          default:
            text = "其他"
            break;
        }
        return (
          <>
            {record.readFlag ? '' : <Badge style={{ marginRight: 8 }} dot />}
            {text}
          </>
        )
      }

    },
    {
      title: "消息内容",
      dataIndex: "content",
      key: "content",
      align: "center",
      render(value, record, index) {
        return (
          <>
            <Tooltip title={value}>
              <span>{ellipsis(value, 15)}</span>
            </Tooltip>
          </>
        )
      }
    },
    {
      title: "时间",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      width: "220px",
      sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime()
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "center",
      width: "100px",
      render(value, record, index) {
        return <a onClick={() => handleDetail(record)}>详情</a>
      }
    },
  ]
  const [filterForm] = useForm()
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRow, setCurrentRow] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  };



  const handleDetail = (record) => {
    setIsModalOpen(true)
    switch (record.type) {
      case "AGENT_HANDLE_LIMIT":
        record.typeStr = "坐席单人处理量抵达上限"
        break
    }
    setCurrentRow(record)
    handleReadIds([record.id])
  }

  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleQuery = () => {
    fetchList()
  }
  const handleReset = () => {
    filterForm.resetFields()
    fetchList()
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination, sorter });
  };

  useEffect(() => {
    fetchList()
  }, [])
  const fetchList = async (params = {}) => {
    const { pagination, query, sorter } = params;
    setLoading(true);
    filterForm.validateFields().then(formData => {
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const ORDER = {
        ascend: 'asc',
        descend: 'desc'
      }
      let orderStr = sorter?.order ? `${sorter?.field}:${ORDER[sorter?.order]}`:  ''
      apiCall("/notifyMessageEmployee", "GET", {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: formData.type,
        readFlag: formData.readFlag,
        orderStr
      }).then(res => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      }).finally(() => {
        setLoading(false);
      })
    })

  };

  const handleAllRead = () => {
    apiCall("/notifyMessageEmployee/readAll", "POST").then(res => {
      fetchList()
    })
  }

  const handleReadRows = () => {
    handleReadIds(rowSelection.selectedRowKeys)
  }

  const handleReadIds = async (ids = []) => {
    apiCall("/notifyMessageEmployee/read", "POST", ids).then(_ => {
      fetchList()
    })
  }

  return (
    <Fragment>
      <FilterBar>
        <Form layout={"inline"} form={filterForm}>
          <Form.Item name="readFlag">
            <Select
              allowClear
              placeholder="类型"
            >
              <Select.Option value={""}>全部</Select.Option>
              <Select.Option value={true}>已读</Select.Option>
              <Select.Option value={false}>未读</Select.Option>
            </Select>
          </Form.Item>


          <Form.Item name="type">
            <Select
              allowClear
              placeholder="类型"
            >
              <Select.Option value={""}>全部</Select.Option>
              <Select.Option value={"AGENT_HANDLE_LIMIT"}>坐席单人处理量抵达上限</Select.Option>
            </Select>
          </Form.Item>


        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" disabled={rowSelection.selectedRowKeys.length === 0} onClick={() => handleReadRows()}>
              标记已读
            </Button>
            <Button onClick={() => handleAllRead()}>全部已读</Button>
          </div>
        </div>
      </FilterBar>
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={paginations}
        onChange={onChangeTable}
        rowSelection={rowSelection}
      />
      <Modal title="详情" open={isModalOpen} onOk={handleOk} onCancel={handleOk} footer={[
        <Button key="submit" type="primary" onClick={handleOk}>
          确定
        </Button>
      ]}>
        <Descriptions title="" column={1} labelStyle={{ width: "80px" }}>
          <Descriptions.Item label="触发时间">{currentRow.createTime}</Descriptions.Item>
          <Descriptions.Item label="类   型">{currentRow.typeStr}</Descriptions.Item>
          {/*<Descriptions.Item label="通知用户">{currentRow.employeeNameList?.join(',')}</Descriptions.Item>*/}
          <Descriptions.Item label="发送内容">{currentRow.content}</Descriptions.Item>
        </Descriptions>
      </Modal>
    </Fragment>
  )
}

export default NotifyCenter;
