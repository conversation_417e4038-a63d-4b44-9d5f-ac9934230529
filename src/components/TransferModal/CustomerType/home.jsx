/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/11/19 09:50
 * @LastEditTime: 2024/10/21 17:07
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/TransferModal/CustomerType/home.jsx
 * @Description: '客户类型(客户/客户群-树状格式)穿梭对话框'
 */

import React, { useEffect, useState } from 'react';
import { Modal, Input, Spin, Tree, Avatar, Button, Empty } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import './home.less';

const { Search } = Input;

const TransferModal = (props) => {
  const {
    visible = false, // 是否显示弹窗,默认隐藏
    multiple = false, // 是否开启多选,默认单选
    type, // 客户/客户群 类型 ['customer','group']
    checkList, // 已选列表
    paramsInfo = {}, // 接口相关信息
  } = props;
  const [loading, setLoading] = useState(false);
  const [typeParams, setTypeParams] = useState(null);
  const [treeListData, setTreeListData] = useState([]);
  const [treeCheckKeys, setTreeCheckKeys] = useState([]);
  const [treeCheckList, setTreeCheckList] = useState([]);

  useEffect(() => {
    console.log(type, 'useEffect-type');
    if (type == 'customer') {
      setTypeParams({
        title: '客户',
        placeholder: '请输入客户名称/员工名称',
        apiUrl: '/employee/customer_employee',
        params: {},
        fieldNames: { title: 'name', key: 'key' },
      });
    } else if (type == 'group') {
      setTypeParams({
        title: '客户群',
        placeholder: '请输入群名称/群主名称',
        apiUrl: '/group/popup',
        params: {},
        fieldNames: { title: 'name', key: 'id' },
      });
    } else if (type == 'other') {
      setTypeParams({
        ...paramsInfo,
      });
    }
  }, [type]);

  useEffect(() => {
    console.log(checkList, 'useEffect-checkList');
    if (checkList && checkList.length) {
      setTreeCheckList(checkList);
    }
  }, [checkList]);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { query } = params;
    const data = {
      paged: false,
      ...typeParams.params,
      ...query,
    };
    apiCall(typeParams?.apiUrl, 'GET', data)
      .then((res) => {
        console.log(`result [res]: `, res)
        setTreeListData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 点击Tree单选框(切换选中)
  const onSelect = (selectedKeys, info) => {
    console.log('onSelect', selectedKeys, info);
    setTreeCheckList([info.node]);
  };

  // 点击Tree复选框(选中/反选)
  const onCheck = (checkedKeys, info) => {
    console.log('onCheck', checkedKeys, info);
    const newTreeCheckList = treeCheckList;
    if (info.checked) {
      newTreeCheckList.push(info.node);
      setTreeCheckList([...newTreeCheckList]);
    } else {
      // console.log(newTreeCheckList.indexOf(info.node));
      // console.log(newTreeCheckList.findIndex((item) => (item.key || item.id) == info.node.key));
      newTreeCheckList.splice(
        newTreeCheckList.findIndex(
          (item) => (item.key || item.id) == info.node.key
        ),
        1
      );
      setTreeCheckList([...newTreeCheckList]);
    }
  };

  // 关闭单个TreeCheck
  const onCloseTreeCheckItem = (item) => {
    const newTreeCheckList = treeCheckList;
    newTreeCheckList.splice(newTreeCheckList.indexOf(item), 1);
    setTreeCheckList([...newTreeCheckList]);
  };

  // 清空全部
  const onClearAll = () => {
    setTreeCheckList([]);
  };

  // 确认-改变数据
  const onOk = () => {
    setTreeListData([]);
    setTreeCheckList([]);
    props?.onSubmit?.(treeCheckList);
  };

  // 取消-不改变数据
  const onCancel = () => {
    setTreeListData([]);
    setTreeCheckList([]);
    props?.onCancel?.();
  };

  const titleRenderHtml = (nodeData, isClose = false) => {
    if (type == 'customer' || type == 'other') {
      // 客户
      return (
        <div
          className={
            (multiple && 'ant-tree-title-nodeData single') ||
            'ant-tree-title-nodeData'
          }
          key={nodeData.key || nodeData.id}
        >
          <Avatar size={30} src={nodeData.avatar} />
          <div className="ant-tree-title-nodeData-left">
            <div>
              {nodeData.name}
              <span
                style={
                  nodeData.type == 1
                    ? { color: '#07c160' }
                    : { color: '#f59a23' }
                }
              >
                {nodeData.companyName}
              </span>
            </div>
            <span>{nodeData.realName}</span>
          </div>
          <div className="ant-tree-title-nodeData-center">
            {nodeData.relevanceEmployee}
          </div>
          {isClose && !multiple && (
            <CloseOutlined
              className="ant-tree-title-nodeData-close"
              onClick={() => {
                onCloseTreeCheckItem(nodeData);
              }}
            />
          )}
        </div>
      );
    } else if (type == 'group') {
      // 客户群
      return (
        <div
          className={
            (multiple && 'ant-tree-title-nodeData single') ||
            'ant-tree-title-nodeData'
          }
          key={nodeData.id}
        >
          <div className="ant-tree-title-nodeData-left">
            <div>{nodeData.name}</div>
          </div>
          <div className="ant-tree-title-nodeData-center">
            {nodeData.leaderName}
          </div>
          {isClose && !multiple && (
            <CloseOutlined
              className="ant-tree-title-nodeData-close"
              onClick={() => {
                onCloseTreeCheckItem(nodeData);
              }}
            />
          )}
        </div>
      );
    }
  };

  return (
    <Modal
      width={660}
      className="Customer-TransferModal-Container"
      visible={visible}
      title={'选择' + typeParams?.title}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <div className="modal-tree-wrap">
          <div className="tree-left">
            <Search
              placeholder={typeParams?.placeholder}
              allowClear
              onSearch={(value) => {
                if (!value) {
                  return false;
                }
                fetchList({
                  query: {
                    keyWord: value,
                  },
                });
              }}
            />
            {(treeListData.length && (
              <Tree
                height={358}
                blockNode
                selectable={multiple}
                checkable={!multiple}
                titleRender={titleRenderHtml}
                checkedKeys={treeCheckList?.map((item) => item.key || item.id)}
                selectedKeys={treeCheckList?.map((item) => item.key || item.id)}
                treeData={treeListData}
                onSelect={onSelect}
                onCheck={onCheck}
                fieldNames={typeParams?.fieldNames}
              />
            )) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
          </div>
          <div className="tree-right">
            <p>已选{typeParams?.title}</p>
            <div className="tree-right-scroll-box">
              {/* {
                treeCheckList.length && treeCheckList?.map((item, index) => (titleRenderHtml(item, true))) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              } */}
              {(treeCheckList.length && (
                <Tree
                  height={330}
                  selectable={false}
                  titleRender={(nodeData) => titleRenderHtml(nodeData, true)}
                  treeData={treeCheckList}
                  fieldNames={typeParams?.fieldNames}
                />
              )) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </div>
            {!multiple && (
              <div className="tree-right-all-close">
                <Button size="small" onClick={() => onClearAll()}>
                  清空选择
                </Button>
                <span>（已选{treeCheckList?.length}个）</span>
              </div>
            )}
          </div>
        </div>
      </Spin>
    </Modal>
  );
};

export default TransferModal;
