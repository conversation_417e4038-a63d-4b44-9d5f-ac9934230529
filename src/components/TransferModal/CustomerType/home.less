.Customer-TransferModal-Container {
  .ant-modal-body {
    .modal-tree-wrap {
      height: 400px;
      display: flex;
      flex-direction: row;

      .tree-left {
        flex: 1;
        border: unset;
        border-right: 1px solid #e5e5e5;
        padding: 0 10px 0 0;

        .ant-input-search {
          padding: 0 0 10px 0;
        }
      }

      .tree-right {
        border-left: 1px solid #e5e5e5;
        padding: 0 0 0 10px;
        flex: 1;
        flex-direction: column;
        display: flex;

        .tree-right-scroll-box {
          flex: 1;

          .ant-tree {

            .ant-tree-treenode,
            .ant-tree-node-content-wrapper {
              width: 100%;
            }
          }
        }


        .tree-right-all-close {
          text-align: right;
        }
      }

      .ant-tree {
        .ant-tree-treenode {
          padding: 0;
        }

        .ant-tree-switcher {
          display: none;
        }
      }

      .ant-tree-title-nodeData {
        display: flex;
        font-size: 13px;
        align-items: center;
        margin-bottom: 8px;
        min-height: 24px;
        line-height: 24px;

        &.single {
          margin: 4px 0;
        }

        .ant-tree-title-nodeData-left {
          margin-left: 8px;
          flex: 1;
          line-height: 15px;
        }

        .ant-tree-title-nodeData-center {
          max-width: 150px;
          margin-right: 8px;
        }

        .ant-tree-title-nodeData-close {
          margin-right: 8px;
        }
      }
    }
  }
}