/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/11/19 09:50
 * @LastEditTime: 2025/06/17 14:33
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/TransferModal/EmployeeType/home.jsx
 * @Description: '员工类型(树状格式)穿梭对话框'
 */

import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react"
import { Button, Empty, Input, Modal, Spin, Tabs, Tag, Tree } from "antd"
import { CloseOutlined, PlusOutlined } from "@ant-design/icons"
import { apiCall } from "common/utils"
import "./home.less"
// 1. 初始化组件时：通过接口获取组织架构数据，将树的数据平铺，展示已选员工或部门的标签
// 2. 打开选择器时：展示选择器的弹窗，左侧是组织架构树（默认显示两级，并勾选已选员工或部门）和标签列表，右侧是已选选项
// 3. 点击搜索时：替换左侧树组件为普通列表，并展示匹配项
// 4. 点击确定时：关闭选择器的弹窗，展示已选选项
// 5. 选择器的模式取值：
//    ['dep','emp','tag']|[]：组织架构和标签选择器。显示部门、员工、标签
//    ['dep','emp']：组织架构选择器。显示部门、员工，不显示标签
//    ['dep','tag']：部门和标签选择器。显示部门、标签，不显示员工
//    ['dep']：部门选择器。显示部门，不显示员工、标签
//    ['emp','tag']：员工和标签选择器。显示部门、员工、标签，但是部门不能选
//    ['emp']：员工选择器。显示部门、员工，不显示标签，但是部门不能选
//    ['tag']：标签选择器。显示标签，不显示部门、员工
// 6. 其他属性解释
//    title：选择器标题、唤起选择器的按钮的值
//    value：选中的ID数组，如果multiple=false，则只取第一个有效元素
//    exclude：排除的ID数组（隐藏的ID数组）
//    disable：禁用的ID数组，禁用之后选项依旧显示，只能不能选中
//    onlyEmployee：只选员工，等同mode=['emp']
//    onlyDepartment：只选部门，等同mode=['dep']
//    onlyTag：只选标签，等同mode=['tag']
//    mode：选择器的模式，请看第五点
//    multiple：多选
//    readonly：只读，该状态下，唤起选择器的按钮不可点击，并且已选中的选项也不能被删除
//    onChange：选择器选项变化事件，第一个参数是选中的ID数组，第二个参数是选中的选项对象数组{id, name}
//    needDisableDepFlag:是否需要根据数据权限禁用部门，默认需要
//    needExcludeDepFlag:是否需要根据数据权限过滤部门，默认需要
//    customHTML:是否自定义dom
//    btnShowFlag:是否显示隐藏操作按钮，默认显示

const TransferModal = forwardRef((props, ref) => {
  const {
    title = "请选择",
    value = [],
    exclude = [],
    disable = [],
    onlyEmployee = false,
    onlyDepartment = false,
    onlyTag = false,
    mode = [], // 选择器的模式
    multiple = true,
    readonly = false,
    needDisableDepFlag = true,
    needExcludeDepFlag = false,
    customHTML = null, // 是否自定义dom
    btnShowFlag = true,
    onChange,
  } = props
  if (mode.length == 0) {
    if (onlyEmployee) {
      mode.push("emp")
    } else if (onlyDepartment) {
      mode.push("dep")
    } else if (onlyTag) {
      mode.push("tag")
    } else {
      mode.push("emp")
      mode.push("dep")
      mode.push("tag")
    }
  }
  const modes = [
    "dep,emp,tag",
    "dep,emp",
    "dep,tag",
    "dep",
    "emp,tag",
    "emp",
    "tag",
  ]
  const searchPlaceholders = [
    "搜索部门 / 员工 / 标签",
    "搜索部门 / 员工",
    "搜索部门 / 标签",
    "搜索部门",
    "搜索员工 / 标签",
    "搜索员工",
    "搜索标签",
  ]
  const modeIndex = modes.indexOf(mode.sort().join())
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  const [orgTree, setOrgTree] = useState([]) // 组织架构树，用于渲染组织架构树组件
  const [tags, setTags] = useState([]) // 员工标签数组
  const [optionMap, setOptionMap] = useState(null) // 选项map，key是选项ID，value是部门或员工或员工标签，用于根据ID快速检索选项
  const [selectedIds, setSelectedIds] = useState([]) // 已选的ID数组
  const [selectedKeys, setSelectedKeys] = useState([]) // 已选的key数组（一个员工可能在组织架构树的多个部门节点中，这里使用key来保证选中其中一个员工节点后便同时选中该员工在整棵组织架构树中的节点）
  const [searchOptions, setSearchOptions] = useState([]) // 可被搜索的选项
  const [searchValue, setSearchValue] = useState(null) // 搜索框的值
  const [matchedOptions, setMatchedOptions] = useState([]) // 搜索匹配到的选项
  const [activeTab, setActiveTab] = useState("1")
  const [useDisable, setUseDisable] = useState([])

  useImperativeHandle(ref, () => ({
    changeVisible,
  }))

  /**
   * 初始化渲染页面所需的数据
   */
  useEffect(async () => {
    needDisableDepFlag && (await getEmployeeDisableDepartmentIds())
    setUseDisable(disable)
    needExcludeDepFlag && (await getManualEmployeeIds())
    await (async function fetchData() {
      setLoading(true)
      let orgTree = [{}]
      let tags = []
      let optionMap = new Map()

      if (modeIndex != 6) {
        orgTree = (await apiCall("/employee/org_option", "GET", null)) || [{}]
        expandOrgTree(orgTree, optionMap)
      }
      if (modeIndex != 1 && modeIndex != 3 && modeIndex != 5) {
        tags = (await apiCall("/employee_tag/option", "GET", null)) || []
        tags.forEach((tag) => {
          tag.id = "tag" + tag.id
          tag.type = "tag"
          optionMap.set(tag.id, {
            id: tag.id,
            name: tag.name,
            type: "tag",
          })
        })
      }
      setOptionMap(optionMap)
      setSearchOptions([...optionMap.values()])
      setOrgTree(orgTree)
      setTags(tags)
      setLoading(false)
    })()
    return () => {
      setOptionMap(null)
      setSearchOptions([])
      setOrgTree([])
      setTags([])
      setUseDisable([])
    }
  }, [])

  useEffect(() => {
    if (!optionMap) {
      return
    }
    setSelectedIds(excludeInvalidId(value))
  }, [optionMap])

  useEffect(() => {
    if (visible) {
      setSelectedIds(excludeInvalidId(value))
    } else {
      setSelectedIds([])
      setSearchValue(null)
      setMatchedOptions([])
    }
  }, [visible])

  useEffect(() => {
    if (!optionMap) {
      return
    }
    setSelectedKeys(
      selectedIds.flatMap((selectedId) => {
        let option = optionMap.get(selectedId)
        return option.keys || option.key
      })
    )
  }, [selectedIds])

  useEffect(() => {
    if (!searchValue) {
      return
    }

    let matchedOptions = searchOptions
      .filter(
        (option) =>
          option.name.includes(searchValue) ||
          new RegExp(searchValue, "i").test(option.userId)
      )
      .sort((a, b) => (a > b ? 1 : a == b ? 0 : -1))

    setMatchedOptions(matchedOptions)
  }, [searchValue])

  // 员工没有权限的部门编号列表
  const getEmployeeDisableDepartmentIds = async () => {
    const apiUrl = `${
      location.pathname.includes("pharmacy") ? "/wecom" : ""
    }/employee/getEmployeeDisableDepartmentIds`
    await apiCall(apiUrl, "GET")
      .then((res) => {
        res.forEach((item) => {
          disable.push(item)
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 不同的场景来展示数据来源不同的员工
  const getManualEmployeeIds = async () => {
    await apiCall("/appemployee/getManualEmployeeIdList", "GET")
      .then((res) => {
        res.forEach((item) => {
          exclude.push(item)
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const excludeInvalidId = function (ids) {
    // 根据传入的value过滤和组件配置过滤选中的ID列表
    let result = ids.filter(
      (id) =>
        // 选中的ID必选在选项中
        optionMap.has(id) &&
        // 选中的ID不能在隐藏的列表中
        !exclude.includes(id)
    )
    if (!multiple && result.length > 1) {
      result = [result[0]]
    }
    return result
  }

  /**
   * 递归展开组织架构树，并把所有节点存入optionMap中
   */
  const expandOrgTree = async function (orgNodes, optionMap) {
    for (let i = 0; i < orgNodes.length; i++) {
      let node = orgNodes[i]
      if (
        // 如果在隐藏节点的列表中包含当前节点，则在树中去除该节点
        (exclude.length > 0 && exclude.includes(node.id)) ||
        // 如果是部门和标签选择器或部门选择器，则不显示员工节点
        ((modeIndex == 2 || modeIndex == 3) && node.type == "emp")
      ) {
        // 删除指定索引的节点
        orgNodes.splice(i, 1)
        i--
        continue
      }
      if ((modeIndex == 4 || modeIndex == 5) && node.type == "dep") {
        // 如果是员工和标签选择器或员工选择器，则部门节点不能选，不能被搜索
        node.disabled = true
      } else {
        // 被禁用的节点，可以被搜索，不能被选中
        if (disable.length > 0 && disable.includes(node.id)) {
          node.disabled = true
        }
        let item = optionMap.get(node.id)
        if (item) {
          item.keys.push(node.key)
          item.parentName += ", " + node.parentName
        } else {
          optionMap.set(node.id, {
            id: node.id,
            name: node.name,
            type: node.type,
            keys: [node.key],
            parentName: node.parentName,
            userId: node.userId,
          })
        }
      }

      if (node.children && node.children.length > 0) {
        expandOrgTree(node.children, optionMap)
      }
    }
  }

  /**
   * 触发当前组件的变化事件
   */
  const triggerChange = function (changeValue) {
    setSelectedIds(changeValue)
    onChange?.(
      changeValue,
      changeValue.map((id) => {
        let option = optionMap.get(id)
        return { id: option.id, name: option.name }
      })
    )
  }

  const onSelect = function (_, e) {
    let option = e.option || e.node
    if (multiple) {
      if (e.selected) {
        setSelectedIds([...selectedIds, option.id])
      } else {
        setSelectedIds(
          selectedIds.filter((selectedId) => selectedId != option.id)
        )
      }
    } else {
      if (e.selected) {
        setSelectedIds([option.id])
      } else {
        setSelectedIds([])
      }
    }
  }

  /**
   * 控制弹窗显示隐藏
   */
  const changeVisible = (value) => {
    setVisible(value)
  }

  return (
    <>
      {customHTML ? (
        customHTML
      ) : (
        <>
          {btnShowFlag ? (
            <Button
              disabled={optionMap == null || readonly}
              type="primary"
              onClick={() => setVisible(true)}
            >
              {optionMap == null ? <Spin /> : <PlusOutlined />}
              {title}
            </Button>
          ) : (
            ""
          )}
          <ConfirmOptionList
            value={value}
            optionMap={optionMap}
            triggerChange={triggerChange}
            readonly={readonly}
            btnShowFlag={btnShowFlag}
          />
        </>
      )}
      <Modal
        width={660}
        className="Employee-TransferModal-Container"
        visible={visible}
        title={title}
        centered
        maskClosable={false}
        onCancel={() => {
          setVisible(false)
        }}
        onOk={() => {
          triggerChange(selectedIds)
          setVisible(false)
        }}
      >
        <Spin spinning={loading}>
          <div className="modal-tree-wrap">
            <div className="tree-left">
              <Input
                placeholder={searchPlaceholders[modeIndex]}
                allowClear
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
              {searchValue && searchValue.trim().length > 0 ? (
                <MatchedOptionList
                  matchedOptions={matchedOptions}
                  selectedIds={selectedIds}
                  onSelect={onSelect}
                  disable={useDisable}
                />
              ) : modeIndex == 1 || modeIndex == 3 || modeIndex == 5 ? (
                <OrgTree
                  onSelect={onSelect}
                  selectedKeys={selectedKeys}
                  orgTree={orgTree}
                />
              ) : modeIndex == 6 ? (
                <TagList
                  tags={tags}
                  selectedIds={selectedIds}
                  onSelect={onSelect}
                  disable={useDisable}
                />
              ) : (
                <Tabs
                  defaultActiveKey={activeTab}
                  activeKey={activeTab}
                  onChange={(activeKey) => setActiveTab(activeKey)}
                >
                  <Tabs.TabPane tab="组织架构" key="1">
                    <OrgTree
                      onSelect={onSelect}
                      selectedKeys={selectedKeys}
                      orgTree={orgTree}
                    />
                  </Tabs.TabPane>
                  <Tabs.TabPane tab="员工标签" key="2">
                    <TagList
                      tags={tags}
                      selectedIds={selectedIds}
                      onSelect={onSelect}
                      disable={useDisable}
                    />
                  </Tabs.TabPane>
                </Tabs>
              )}
            </div>
            <div className="tree-right">
              <p>已选列表</p>
              <SelectedOptionList
                selectedIds={selectedIds}
                optionMap={optionMap}
                multiple={multiple}
                onSelect={onSelect}
              />
              <div className="tree-right-all-close">
                <Button size="small" onClick={() => setSelectedIds([])}>
                  清空选择
                </Button>
                <span>（已选{selectedIds?.length}项）</span>
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
    </>
  )
})

/**
 * 组织架构树
 */
const OrgTree = ({ onSelect, selectedKeys, orgTree }) => (
  <Tree
    rootClassName="ant-tree-dep"
    height={306}
    blockNode
    checkStrictly
    multiple
    onSelect={onSelect}
    selectedKeys={selectedKeys}
    defaultExpandedKeys={[orgTree[0]?.key]}
    treeData={orgTree}
    fieldNames={{
      title: "name",
      key: "key",
      children: "children",
    }}
    titleRender={(nodeData) => {
      let title = `${nodeData.name}${
        nodeData.userId ? `(${nodeData.userId})` : ""
      }`
      return <span title={title}>{title}</span>
    }}
  />
)

/**
 * 已选中的选项列表组件
 */
const SelectedOptionList = function ({
  selectedIds,
  optionMap,
  multiple,
  onSelect,
}) {
  return (
    <div className="tree-right-scroll-box">
      {(selectedIds &&
        selectedIds.length > 0 &&
        selectedIds.map((id) => {
          let option = optionMap.get(id)
          let title = `${option.name}${
            option.userId ? `(${option.userId})` : ""
          }`
          return (
            <div className="ant-tree-title-nodeData" key={option.id}>
              <div className="ant-tree-title-nodeData-left" title={title}>
                {title}
              </div>
              <div className="ant-tree-title-nodeData-center">
                {option.parentName || ""}
              </div>
              {multiple && (
                <CloseOutlined
                  className="ant-tree-title-nodeData-close"
                  onClick={() =>
                    onSelect(null, { option: option, selected: false })
                  }
                />
              )}
            </div>
          )
        })) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    </div>
  )
}

/**
 * 搜索匹配到的选项列表
 */
const MatchedOptionList = function ({
  matchedOptions,
  selectedIds,
  onSelect,
  disable,
}) {
  return (
    <div className="tree-right-scroll-box">
      {(matchedOptions &&
        matchedOptions.length > 0 &&
        matchedOptions.map((option) => {
          let selected = selectedIds.includes(option.id)
          let title = `${option.name}${
            option.userId ? `(${option.userId})` : ""
          }`
          return (
            <div
              className={
                selected
                  ? "ant-tree-title-nodeData selected"
                  : "ant-tree-title-nodeData"
              }
              key={option.id}
              onClick={() => {
                if (!disable.includes(option.id)) {
                  onSelect(null, { option: option, selected: !selected })
                }
              }}
            >
              <div
                className="ant-tree-title-nodeData-left"
                disabled={disable.includes(option.id)}
                style={{ color: disable.includes(option.id) ? "#bfbfbf" : "" }}
                title={title}
              >
                {title}
                {option.type == "dep"
                  ? "（部门）"
                  : option.type == "tag"
                  ? "（标签）"
                  : ""}
              </div>
              <div className="ant-tree-title-nodeData-center">
                {option.parentName || ""}
              </div>
            </div>
          )
        })) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    </div>
  )
}

/**
 * 员工标签列表
 */
const TagList = function ({ tags, selectedIds, onSelect, disable }) {
  return (
    <div className="tree-right-scroll-box" style={{ height: "308px" }}>
      {(tags &&
        tags.length > 0 &&
        tags.map((tag) => {
          let selected = selectedIds.includes(tag.id)
          return (
            <div
              className={
                selected
                  ? "ant-tree-title-nodeData selected"
                  : "ant-tree-title-nodeData"
              }
              key={tag.id}
              onClick={() => {
                if (!disable.includes(tag.id)) {
                  onSelect(null, { option: tag, selected: !selected })
                }
              }}
            >
              <div
                className="ant-tree-title-nodeData-left"
                disabled={disable.includes(tag.id)}
                style={{ color: disable.includes(tag.id) ? "#bfbfbf" : "" }}
                title={tag.name}
              >
                {tag.name}
              </div>
            </div>
          )
        })) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    </div>
  )
}

/**
 * 选择器确认后的选项列表
 */
const ConfirmOptionList = function ({
  value,
  optionMap,
  triggerChange,
  readonly,
  btnShowFlag,
}) {
  return (
    <div>
      {optionMap &&
        value.map((vid) => {
          let option = optionMap.get(vid)
          return (
            (option && (
              <Tag
                closable={!readonly}
                style={{ marginTop: btnShowFlag ? "8px" : " 0 8px 8px 0" }}
                key={vid}
                onClose={(e) => {
                  e.preventDefault()
                  triggerChange(value.filter((id) => vid != id))
                }}
              >
                {option.name}
              </Tag>
            )) ||
            ""
          )
        })}
    </div>
  )
}

export default TransferModal
