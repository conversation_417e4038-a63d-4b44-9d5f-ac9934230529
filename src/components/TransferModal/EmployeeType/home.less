.Employee-TransferModal-Container {
  .ant-modal-body {
    .modal-tree-wrap {
      height: 400px;
      display: flex;
      flex-direction: row;

      .tree-left {
        flex: 1;
        border: unset;
        border-right: 1px solid #e5e5e5;
        padding: 0 10px 0 0;

        .ant-tree-title-nodeData {
          cursor: pointer;
          margin-bottom: unset !important;

          .ant-tree-title-nodeData-center {
            margin-right: unset !important;
          }
        }

        .ant-tree,
        .tree-right-scroll-box {
          margin-top: 10px;
        }

        .ant-tabs .ant-tree,
        .ant-tabs .tree-right-scroll-box {
          margin-top: 0px;
        }
      }

      .tree-right {
        border-left: 1px solid #e5e5e5;
        padding: 0 0 0 10px;
        flex: 1;
        flex-direction: column;
        display: flex;

        .tree-right-all-close {
          text-align: right;
        }
      }

      .ant-tree {
        .ant-tree-treenode {
          padding: 0;
        }
      }

      .ant-tree-dep {
        .ant-tree-treenode {
          .ant-tree-node-content-wrapper {
            display: flex;

            .ant-tree-title {
              display: flex;
              flex: auto;

              span {
                flex: auto;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 1px;
              }
            }
          }
        }
      }

      .ant-tree-title-nodeData {
        display: flex;
        font-size: 13px;
        align-items: center;
        margin-bottom: 8px;
        padding: 0 4px;
        min-height: 24px;
        line-height: 24px;
        border-radius: 2px;

        .ant-tree-title-nodeData-left {
          flex: auto;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 1px;
        }

        .ant-tree-title-nodeData-center {
          max-width: 150px;
          margin-right: 8px;
        }

        .ant-tree-title-nodeData-close {
          margin-right: 8px;
        }
      }
    }

    .tree-right-scroll-box {
      height: 358px;
      overflow-y: auto;

      /* 设置滚动条的样式 */
      &::-webkit-scrollbar {
        width: 8px;
      }

      /* 滚动槽 */
      &::-webkit-scrollbar-track {
        border-radius: 10px;
      }

      /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}