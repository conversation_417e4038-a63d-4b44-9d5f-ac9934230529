/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/11/04 10:27
 * @LastEditTime: 2024/11/04 11:16
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotAvatar/home.jsx
 * @Description: 'Avatar + Image，自定义头像，默认二维码兜底'
 */

import React, { useEffect } from 'react';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Image } from 'antd';
import { QrCodeBase } from 'common/qrcode';
import {FileHOC} from 'components/FileHOC/FileHOC';
const WibotAvatar = (props) => {
  const { src } = props;

  useEffect(() => { }, []);

  return (
    <Avatar
      {...props}
      style={{ backgroundColor: "#c5c5c5" }}
      src={props.src ? <FileHOC src={props.src}>
        {(url) => (
          <Image src={url} fallback={QrCodeBase({ url: props.src })} />
        )}
      </FileHOC> : <UserOutlined />}
    />
  );
};

export default WibotAvatar;
