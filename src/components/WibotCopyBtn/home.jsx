/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/20 14:12
 * @LastEditTime: 2023/11/23 10:09
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotCopyBtn/home.jsx
 * @Description: '复制按钮'
 */

import React, { useEffect } from 'react';
import { CopyToClipboard } from "react-copy-to-clipboard";
import { message } from 'antd';

const WibotCopyBtn = (props) => {
  const { title = '复制', text = "" } = props;

  useEffect(() => { }, []);

  return (
    <CopyToClipboard
      text={text}
      onCopy={(value) => {
        if (value) {
          message.success("复制成功！");
        } else {
          message.fail("复制失败！");
        }
      }}
    >
      <a>{title}</a>
    </CopyToClipboard>
  );
};

export default WibotCopyBtn;
