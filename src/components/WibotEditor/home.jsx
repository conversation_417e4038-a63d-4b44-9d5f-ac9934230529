/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/06/25 15:53
 * @LastEditTime: 2025/07/22 12:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/WibotEditor/home.jsx
 * @Description: 'wangEditor 5'
 * @Description: 'https://www.wangeditor.com/'
 */

import { Editor, Toolbar } from "@wangeditor/editor-for-react"
import { Spin, message } from "antd"
import { base64ToFile, compressImage } from "common/image"
import { apiCall } from "common/utils"
import { filterXSS } from "common/xss"
import { formatURL } from "config"
import { forwardRef, useEffect, useImperativeHandle, useState } from "react"

const WibotEditor = forwardRef((props, ref) => {
  const { onChangeHtml } = props
  useImperativeHandle(ref, () => ({
    getHtml,
    getText,
    setHtml,
    insertHtml,
    insertText,
    setText,
    disable,
    clear,
  }))
  const [editor, setEditor] = useState(null)
  const [show, setShow] = useState(false)
  const [loading, setLoading] = useState(false)
  const [toolbarConfig, setToolbarConfig] = useState({})
  const [editorConfig, setEditorConfig] = useState({})
  const [mode, setMode] = useState("default") // or 'simple'
  const [defaultHtml, setDefaultHtml] = useState("")
  const [defaultText, setDefaultText] = useState("")

  // 初始化配置
  useEffect(() => {
    const { toolbarConfig, editorConfig, defaultHtml, defaultText, mode } =
      props
    // 工具栏配置
    toolbarConfig && setToolbarConfig(toolbarConfig)
    // 编辑器配置
    const defaultEditorConfig = {
      placeholder: "请输入内容...",
      autoFocus: false,
      MENU_CONF: {
        uploadImage: {
          customUpload(file, insertFn) {
            setLoading(true)
            const File = file
            const options = {}
            compressImage(File, options).then((result) => {
              const formData = new FormData()
              formData.append("file", base64ToFile(result, File.name))
              const data = formData
              apiCall("/file/image", "POST", data)
                .then((res) => {
                  const { fileUrl } = res
                  let newFileUlr = formatURL(fileUrl)
                  insertFn(newFileUlr, "", newFileUlr)
                })
                .catch((err) => {
                  console.log(err)
                })
                .finally(() => {
                  setLoading(false)
                })
            })
          },
        },
        uploadVideo: {
          customUpload(file, insertFn) {
            if (file.size > 10 * 1024 * 1024) {
              message.error("视频大小不能超过10M")
              return false
            }
            setLoading(true)
            const File = file
            const formData = new FormData()
            formData.append("file", File)
            const data = formData
            apiCall("/file/video", "POST", data)
              .then((res) => {
                const { fileUrl } = res
                     let newFileUlr = formatURL(fileUrl)
                     insertFn(newFileUlr, newFileUlr)
              })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
                setLoading(false)
              })
          },
        },
      },
    }
    setEditorConfig({
      ...defaultEditorConfig,
      ...editorConfig,
    })
    defaultHtml && setDefaultHtml(defaultHtml) // 默认html
    defaultText && setDefaultText(defaultText) // 默认text
    mode && setMode(mode) // 默认text
  }, [props])

  // 初始化完成显示富文本
  useEffect(() => {
    if (editorConfig && !show) {
      setShow(true)
    }
  }, [editorConfig])

  // 富文本初始化完成
  const onCreated = (editor) => {
    // console.log('onCreated', editor);
    setEditor(editor)
  }

  // 及时销毁 editor ，重要！
  useEffect(() => {
    // 富文本加载完成设置默认html/默认text
    if (editor) {
      defaultHtml && setHtml(defaultHtml)
      defaultText && setText(defaultText)
    }
    return () => {
      if (editor == null) {
        return
      }
      editor.destroy()
      setEditor(null)
    }
  }, [editor])

  // 监听富文本的变化
  const onChange = (editor) => {
    // console.log('onChange', editor);
  }

  // 获取html
  const getHtml = () => editor.getHtml()

  // 获取text
  const getText = () => editor.getText()

  // 设置html-重置
  const setHtml = (html) => {
    editor.setHtml(filterXSS(html))
  }

  // 设置text转html-重置
  const setText = (text) => {
    const html = text
      .split(/\n/)
      .map((line) => `<p>${line}</p>`)
      .join("\n")
    editor.setHtml(filterXSS(html))
  }

  // 追加html
  const insertHtml = (html) => {
    editor.focus() // 必要
    editor.dangerouslyInsertHtml(filterXSS(html))
  }

  // 追加text
  const insertText = (text) => {
    editor.focus() // 必要
    editor.insertText(text)
  }

  // 禁用编辑器，设置为只读
  const disable = () => {
    editor.disable()
  }

  // 清空编辑器内容
  const clear = () => {
    editor.clear()
  }

  return (
    <>
      {show && (
        <Spin spinning={loading}>
          <div className="WibotEditor-Container">
            <div
              className="body"
              style={{ border: "1px solid #ccc", zIndex: 100 }}
            >
              <Toolbar
                editor={editor}
                defaultConfig={toolbarConfig}
                mode={mode}
                style={{ borderBottom: "1px solid #ccc" }}
              />
              <Editor
                defaultConfig={editorConfig}
                onCreated={(editor) => onCreated(editor)}
                onChange={(editor) => {
                  onChange(editor)
                  onChangeHtml(editor.getHtml())
                }}
                mode={mode}
                style={{ height: "300px", overflowY: "hidden" }}
              />
            </div>
          </div>
        </Spin>
      )}
    </>
  )
})

export default WibotEditor
