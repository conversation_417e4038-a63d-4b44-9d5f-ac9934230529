/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/07/01 11:04
 * @LastEditTime: 2025/07/18 16:48
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotEditorView/home.jsx
 * @Description: 'wangEditor 5 预览模式'
 */

import { filterXSS } from "common/xss"

const Template = (props) => {
  const { className = "", style = {}, html = "", ...rest } = props

  return (
    <div
      className={`WibotEditor-View-Container editor-content-view ${className}`}
      style={style}
      dangerouslySetInnerHTML={{ __html: filterXSS(html) }}
      {...rest}
    />
  )
}

export default Template
