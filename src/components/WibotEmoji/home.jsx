/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/12/25 14:13
 * @LastEditTime: 2023/12/25 17:22
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/components/WibotEmoji/home.jsx
 * @Description: 'Tooltip + Popover + Emoji'
 */

import React, { useEffect, useState } from "react";
import { Tooltip, Popover } from 'antd';
import { SmileOutlined } from "@ant-design/icons";
import data from "@emoji-mart/data";
import { Picker } from "emoji-mart";
import i18nZh from "common/emoji-mart-i18n-zh.json";
import "./home.less";
import {versionFnMap} from "config";

const WibotEmoji = (props) => {
  const { iconClassName = "", iconStyle, locale = "zh", previewPosition = "none", categories = ["frequent", "people"], searchPosition = "none", emojiSize = 18, navPosition = 'top', onEmojiSelect } = props;
  const [clicked, setClicked] = useState(false);
  const [hovered, setHovered] = useState(false);

  useEffect(() => {
    if (clicked) {
      const pickerOptions = {
        data,
        locale: locale,
        i18n: i18nZh,
        previewPosition: previewPosition,
        categories,
        searchPosition: searchPosition,
        emojiSize: emojiSize,
        navPosition: navPosition,
        onEmojiSelect: (selected) => {
          console.log(`[selected]: `, selected)
          hide()
          onEmojiSelect?.(selected);
        },
      };
      if (versionFnMap.system_ui().excludeEmoji) {
        const emojis = {
          "grinning": {
            "id": "grinning",
            "name": "Grinning Face",
            "emoticons": [
              ":D"
            ],
            "keywords": [
              "smile",
              "happy",
              "joy",
              ":D",
              "grin"
            ],
            "skins": [
              {
                "unified": "1f600",
                "native": "😀",
                "x": 32,
                "y": 21
              }
            ],
            "version": 1
          },
          "smiley": {
            "id": "smiley",
            "name": "Grinning Face with Big Eyes",
            "emoticons": [
              ":)",
              "=)",
              "=-)"
            ],
            "keywords": [
              "smiley",
              "happy",
              "joy",
              "haha",
              ":D",
              ":)",
              "smile",
              "funny"
            ],
            "skins": [
              {
                "unified": "1f603",
                "native": "😃",
                "x": 32,
                "y": 24
              }
            ],
            "version": 1
          },
          "smile": {
            "id": "smile",
            "name": "Grinning Face with Smiling Eyes",
            "emoticons": [
              ":)",
              "C:",
              "c:",
              ":D",
              ":-D"
            ],
            "keywords": [
              "smile",
              "happy",
              "joy",
              "funny",
              "haha",
              "laugh",
              "like",
              ":D",
              ":)"
            ],
            "skins": [
              {
                "unified": "1f604",
                "native": "😄",
                "x": 32,
                "y": 25
              }
            ],
            "version": 1
          },
          "grin": {
            "id": "grin",
            "name": "Beaming Face with Smiling Eyes",
            "keywords": [
              "grin",
              "happy",
              "smile",
              "joy",
              "kawaii"
            ],
            "skins": [
              {
                "unified": "1f601",
                "native": "😁",
                "x": 32,
                "y": 22
              }
            ],
            "version": 1
          },
          "laughing": {
            "id": "laughing",
            "name": "Grinning Squinting Face",
            "emoticons": [
              ":>",
              ":->"
            ],
            "keywords": [
              "laughing",
              "satisfied",
              "happy",
              "joy",
              "lol",
              "haha",
              "glad",
              "XD",
              "laugh"
            ],
            "skins": [
              {
                "unified": "1f606",
                "native": "😆",
                "x": 32,
                "y": 27
              }
            ],
            "version": 1
          },
          "rolling_on_the_floor_laughing": {
            "id": "rolling_on_the_floor_laughing",
            "name": "Rolling on the Floor Laughing",
            "keywords": [
              "face",
              "lol",
              "haha",
              "rofl"
            ],
            "skins": [
              {
                "unified": "1f923",
                "native": "🤣",
                "x": 40,
                "y": 17
              }
            ],
            "version": 35
          },
          "joy": {
            "id": "joy",
            "name": "Face with Tears of Joy",
            "keywords": [
              "cry",
              "weep",
              "happy",
              "happytears",
              "haha"
            ],
            "skins": [
              {
                "unified": "1f602",
                "native": "😂",
                "x": 32,
                "y": 23
              }
            ],
            "version": 1
          },
          "wink": {
            "id": "wink",
            "name": "Winking Face",
            "emoticons": [
              ";)",
              ";-)"
            ],
            "keywords": [
              "wink",
              "happy",
              "mischievous",
              "secret",
              ";)",
              "smile",
              "eye"
            ],
            "skins": [
              {
                "unified": "1f609",
                "native": "😉",
                "x": 32,
                "y": 30
              }
            ],
            "version": 1
          },
          "blush": {
            "id": "blush",
            "name": "Smiling Face with Smiling Eyes",
            "emoticons": [
              ":)"
            ],
            "keywords": [
              "blush",
              "smile",
              "happy",
              "flushed",
              "crush",
              "embarrassed",
              "shy",
              "joy"
            ],
            "skins": [
              {
                "unified": "1f60a",
                "native": "😊",
                "x": 32,
                "y": 31
              }
            ],
            "version": 1
          },
          "innocent": {
            "id": "innocent",
            "name": "Smiling Face with Halo",
            "keywords": [
              "innocent",
              "angel",
              "heaven"
            ],
            "skins": [
              {
                "unified": "1f607",
                "native": "😇",
                "x": 32,
                "y": 28
              }
            ],
            "version": 1
          },
          "smiling_face_with_3_hearts": {
            "id": "smiling_face_with_3_hearts",
            "name": "Smiling Face with Hearts",
            "keywords": [
              "3",
              "love",
              "like",
              "affection",
              "valentines",
              "infatuation",
              "crush",
              "adore"
            ],
            "skins": [
              {
                "unified": "1f970",
                "native": "🥰",
                "x": 43,
                "y": 60
              }
            ],
            "version": 115
          },
          "heart_eyes": {
            "id": "heart_eyes",
            "name": "Smiling Face with Heart-Eyes",
            "keywords": [
              "heart",
              "eyes",
              "love",
              "like",
              "affection",
              "valentines",
              "infatuation",
              "crush"
            ],
            "skins": [
              {
                "unified": "1f60d",
                "native": "😍",
                "x": 32,
                "y": 34
              }
            ],
            "version": 1
          },
          "star-struck": {
            "id": "star-struck",
            "name": "Star-Struck",
            "keywords": [
              "star",
              "struck",
              "grinning",
              "face",
              "with",
              "eyes",
              "smile",
              "starry"
            ],
            "skins": [
              {
                "unified": "1f929",
                "native": "🤩",
                "x": 40,
                "y": 40
              }
            ],
            "version": 55
          },
          "kissing_heart": {
            "id": "kissing_heart",
            "name": "Face Blowing a Kiss",
            "emoticons": [
              ":*",
              ":-*"
            ],
            "keywords": [
              "kissing",
              "heart",
              "love",
              "like",
              "affection",
              "valentines",
              "infatuation"
            ],
            "skins": [
              {
                "unified": "1f618",
                "native": "😘",
                "x": 32,
                "y": 45
              }
            ],
            "version": 1
          },
          "kissing": {
            "id": "kissing",
            "name": "Kissing Face",
            "keywords": [
              "love",
              "like",
              "3",
              "valentines",
              "infatuation",
              "kiss"
            ],
            "skins": [
              {
                "unified": "1f617",
                "native": "😗",
                "x": 32,
                "y": 44
              }
            ],
            "version": 1
          },
          "relaxed": {
            "id": "relaxed",
            "name": "Smiling Face",
            "keywords": [
              "relaxed",
              "blush",
              "massage",
              "happiness"
            ],
            "skins": [
              {
                "unified": "263a-fe0f",
                "native": "☺️",
                "x": 57,
                "y": 35
              }
            ],
            "version": 1
          },
          "kissing_closed_eyes": {
            "id": "kissing_closed_eyes",
            "name": "Kissing Face with Closed Eyes",
            "keywords": [
              "love",
              "like",
              "affection",
              "valentines",
              "infatuation",
              "kiss"
            ],
            "skins": [
              {
                "unified": "1f61a",
                "native": "😚",
                "x": 32,
                "y": 47
              }
            ],
            "version": 1
          },
          "kissing_smiling_eyes": {
            "id": "kissing_smiling_eyes",
            "name": "Kissing Face with Smiling Eyes",
            "keywords": [
              "affection",
              "valentines",
              "infatuation",
              "kiss"
            ],
            "skins": [
              {
                "unified": "1f619",
                "native": "😙",
                "x": 32,
                "y": 46
              }
            ],
            "version": 1
          },
          "yum": {
            "id": "yum",
            "name": "Face Savoring Food",
            "keywords": [
              "yum",
              "happy",
              "joy",
              "tongue",
              "smile",
              "silly",
              "yummy",
              "nom",
              "delicious",
              "savouring"
            ],
            "skins": [
              {
                "unified": "1f60b",
                "native": "😋",
                "x": 32,
                "y": 32
              }
            ],
            "version": 1
          },
          "stuck_out_tongue": {
            "id": "stuck_out_tongue",
            "name": "Face with Tongue",
            "emoticons": [
              ":p",
              ":-p",
              ":P",
              ":-P",
              ":b",
              ":-b"
            ],
            "keywords": [
              "stuck",
              "out",
              "prank",
              "childish",
              "playful",
              "mischievous",
              "smile"
            ],
            "skins": [
              {
                "unified": "1f61b",
                "native": "😛",
                "x": 32,
                "y": 48
              }
            ],
            "version": 1
          },
          "stuck_out_tongue_winking_eye": {
            "id": "stuck_out_tongue_winking_eye",
            "name": "Winking Face with Tongue",
            "emoticons": [
              ";p",
              ";-p",
              ";b",
              ";-b",
              ";P",
              ";-P"
            ],
            "keywords": [
              "stuck",
              "out",
              "eye",
              "prank",
              "childish",
              "playful",
              "mischievous",
              "smile",
              "wink"
            ],
            "skins": [
              {
                "unified": "1f61c",
                "native": "😜",
                "x": 32,
                "y": 49
              }
            ],
            "version": 1
          },
          "zany_face": {
            "id": "zany_face",
            "name": "Zany Face",
            "keywords": [
              "grinning",
              "with",
              "one",
              "large",
              "and",
              "small",
              "eye",
              "goofy",
              "crazy"
            ],
            "skins": [
              {
                "unified": "1f92a",
                "native": "🤪",
                "x": 40,
                "y": 41
              }
            ],
            "version": 55
          },
          "stuck_out_tongue_closed_eyes": {
            "id": "stuck_out_tongue_closed_eyes",
            "name": "Squinting Face with Tongue",
            "keywords": [
              "stuck",
              "out",
              "closed",
              "eyes",
              "prank",
              "playful",
              "mischievous",
              "smile"
            ],
            "skins": [
              {
                "unified": "1f61d",
                "native": "😝",
                "x": 32,
                "y": 50
              }
            ],
            "version": 1
          },
          "hugging_face": {
            "id": "hugging_face",
            "name": "Hugging Face",
            "keywords": [
              "smile",
              "hug"
            ],
            "skins": [
              {
                "unified": "1f917",
                "native": "🤗",
                "x": 39,
                "y": 6
              }
            ],
            "version": 1
          },
          "face_with_hand_over_mouth": {
            "id": "face_with_hand_over_mouth",
            "name": "Face with Hand over Mouth",
            "keywords": [
              "smiling",
              "eyes",
              "and",
              "covering",
              "whoops",
              "shock",
              "surprise"
            ],
            "skins": [
              {
                "unified": "1f92d",
                "native": "🤭",
                "x": 40,
                "y": 44
              }
            ],
            "version": 55
          },
          "face_with_open_eyes_and_hand_over_mouth": {
            "id": "face_with_open_eyes_and_hand_over_mouth",
            "name": "Face with Open Eyes and Hand over Mouth",
            "keywords": [
              "silence",
              "secret",
              "shock",
              "surprise"
            ],
            "skins": [
              {
                "unified": "1fae2",
                "native": "🫢",
                "x": 55,
                "y": 32
              }
            ],
            "version": 145
          },
          "shushing_face": {
            "id": "shushing_face",
            "name": "Shushing Face",
            "keywords": [
              "with",
              "finger",
              "covering",
              "closed",
              "lips",
              "quiet",
              "shhh"
            ],
            "skins": [
              {
                "unified": "1f92b",
                "native": "🤫",
                "x": 40,
                "y": 42
              }
            ],
            "version": 55
          },
          "thinking_face": {
            "id": "thinking_face",
            "name": "Thinking Face",
            "keywords": [
              "hmmm",
              "think",
              "consider"
            ],
            "skins": [
              {
                "unified": "1f914",
                "native": "🤔",
                "x": 39,
                "y": 3
              }
            ],
            "version": 1
          },
          "saluting_face": {
            "id": "saluting_face",
            "name": "Saluting Face",
            "keywords": [
              "respect",
              "salute"
            ],
            "skins": [
              {
                "unified": "1fae1",
                "native": "🫡",
                "x": 55,
                "y": 31
              }
            ],
            "version": 145
          },
          "grimacing": {
            "id": "grimacing",
            "name": "Grimacing Face",
            "keywords": [
              "grimace",
              "teeth"
            ],
            "skins": [
              {
                "unified": "1f62c",
                "native": "😬",
                "x": 33,
                "y": 4
              }
            ],
            "version": 1
          },
          "lying_face": {
            "id": "lying_face",
            "name": "Lying Face",
            "keywords": [
              "lie",
              "pinocchio"
            ],
            "skins": [
              {
                "unified": "1f925",
                "native": "🤥",
                "x": 40,
                "y": 19
              }
            ],
            "version": 35
          },
          "relieved": {
            "id": "relieved",
            "name": "Relieved Face",
            "keywords": [
              "relaxed",
              "phew",
              "massage",
              "happiness"
            ],
            "skins": [
              {
                "unified": "1f60c",
                "native": "😌",
                "x": 32,
                "y": 33
              }
            ],
            "version": 1
          },
          "pensive": {
            "id": "pensive",
            "name": "Pensive Face",
            "keywords": [
              "sad",
              "depressed",
              "upset"
            ],
            "skins": [
              {
                "unified": "1f614",
                "native": "😔",
                "x": 32,
                "y": 41
              }
            ],
            "version": 1
          },
          "sleepy": {
            "id": "sleepy",
            "name": "Sleepy Face",
            "keywords": [
              "tired",
              "rest",
              "nap"
            ],
            "skins": [
              {
                "unified": "1f62a",
                "native": "😪",
                "x": 33,
                "y": 2
              }
            ],
            "version": 1
          },
          "drooling_face": {
            "id": "drooling_face",
            "name": "Drooling Face",
            "keywords": [],
            "skins": [
              {
                "unified": "1f924",
                "native": "🤤",
                "x": 40,
                "y": 18
              }
            ],
            "version": 35
          },
          "sleeping": {
            "id": "sleeping",
            "name": "Sleeping Face",
            "keywords": [
              "tired",
              "sleepy",
              "night",
              "zzz"
            ],
            "skins": [
              {
                "unified": "1f634",
                "native": "😴",
                "x": 33,
                "y": 13
              }
            ],
            "version": 1
          },
          "mask": {
            "id": "mask",
            "name": "Face with Medical Mask",
            "keywords": [
              "sick",
              "ill",
              "disease",
              "covid"
            ],
            "skins": [
              {
                "unified": "1f637",
                "native": "😷",
                "x": 33,
                "y": 18
              }
            ],
            "version": 1
          },
          "sneezing_face": {
            "id": "sneezing_face",
            "name": "Sneezing Face",
            "keywords": [
              "gesundheit",
              "sneeze",
              "sick",
              "allergy"
            ],
            "skins": [
              {
                "unified": "1f927",
                "native": "🤧",
                "x": 40,
                "y": 38
              }
            ],
            "version": 35
          },
          "exploding_head": {
            "id": "exploding_head",
            "name": "Exploding Head",
            "keywords": [
              "shocked",
              "face",
              "with",
              "mind",
              "blown"
            ],
            "skins": [
              {
                "unified": "1f92f",
                "native": "🤯",
                "x": 40,
                "y": 46
              }
            ],
            "version": 55
          },
          "face_with_cowboy_hat": {
            "id": "face_with_cowboy_hat",
            "name": "Cowboy Hat Face",
            "keywords": [
              "with",
              "cowgirl"
            ],
            "skins": [
              {
                "unified": "1f920",
                "native": "🤠",
                "x": 40,
                "y": 14
              }
            ],
            "version": 35
          },
          "partying_face": {
            "id": "partying_face",
            "name": "Partying Face",
            "keywords": [
              "celebration",
              "woohoo"
            ],
            "skins": [
              {
                "unified": "1f973",
                "native": "🥳",
                "x": 44,
                "y": 2
              }
            ],
            "version": 115
          },
          "disguised_face": {
            "id": "disguised_face",
            "name": "Disguised Face",
            "keywords": [
              "pretent",
              "brows",
              "glasses",
              "moustache"
            ],
            "skins": [
              {
                "unified": "1f978",
                "native": "🥸",
                "x": 44,
                "y": 12
              }
            ],
            "version": 135
          },
          "sunglasses": {
            "id": "sunglasses",
            "name": "Smiling Face with Sunglasses",
            "emoticons": [
              "8)"
            ],
            "keywords": [
              "cool",
              "smile",
              "summer",
              "beach",
              "sunglass"
            ],
            "skins": [
              {
                "unified": "1f60e",
                "native": "😎",
                "x": 32,
                "y": 35
              }
            ],
            "version": 1
          },
          "nerd_face": {
            "id": "nerd_face",
            "name": "Nerd Face",
            "keywords": [
              "nerdy",
              "geek",
              "dork"
            ],
            "skins": [
              {
                "unified": "1f913",
                "native": "🤓",
                "x": 39,
                "y": 2
              }
            ],
            "version": 1
          },
          "face_with_monocle": {
            "id": "face_with_monocle",
            "name": "Face with Monocle",
            "keywords": [
              "stuffy",
              "wealthy"
            ],
            "skins": [
              {
                "unified": "1f9d0",
                "native": "🧐",
                "x": 47,
                "y": 13
              }
            ],
            "version": 55
          },
          "open_mouth": {
            "id": "open_mouth",
            "name": "Face with Open Mouth",
            "emoticons": [
              ":o",
              ":-o",
              ":O",
              ":-O"
            ],
            "keywords": [
              "surprise",
              "impressed",
              "wow",
              "whoa",
              ":O"
            ],
            "skins": [
              {
                "unified": "1f62e",
                "native": "😮",
                "x": 33,
                "y": 7
              }
            ],
            "version": 1
          },
          "hushed": {
            "id": "hushed",
            "name": "Hushed Face",
            "keywords": [
              "woo",
              "shh"
            ],
            "skins": [
              {
                "unified": "1f62f",
                "native": "😯",
                "x": 33,
                "y": 8
              }
            ],
            "version": 1
          },
          "astonished": {
            "id": "astonished",
            "name": "Astonished Face",
            "keywords": [
              "xox",
              "surprised",
              "poisoned"
            ],
            "skins": [
              {
                "unified": "1f632",
                "native": "😲",
                "x": 33,
                "y": 11
              }
            ],
            "version": 1
          },
          "flushed": {
            "id": "flushed",
            "name": "Flushed Face",
            "keywords": [
              "blush",
              "shy",
              "flattered"
            ],
            "skins": [
              {
                "unified": "1f633",
                "native": "😳",
                "x": 33,
                "y": 12
              }
            ],
            "version": 1
          },
          "pleading_face": {
            "id": "pleading_face",
            "name": "Pleading Face",
            "keywords": [
              "begging",
              "mercy",
              "cry",
              "tears",
              "sad",
              "grievance"
            ],
            "skins": [
              {
                "unified": "1f97a",
                "native": "🥺",
                "x": 44,
                "y": 14
              }
            ],
            "version": 115
          },
          "face_holding_back_tears": {
            "id": "face_holding_back_tears",
            "name": "Face Holding Back Tears",
            "keywords": [
              "touched",
              "gratitude",
              "cry"
            ],
            "skins": [
              {
                "unified": "1f979",
                "native": "🥹",
                "x": 44,
                "y": 13
              }
            ],
            "version": 145
          },
          "sob": {
            "id": "sob",
            "name": "Loudly Crying Face",
            "emoticons": [
              ":'("
            ],
            "keywords": [
              "sob",
              "cry",
              "tears",
              "sad",
              "upset",
              "depressed"
            ],
            "skins": [
              {
                "unified": "1f62d",
                "native": "😭",
                "x": 33,
                "y": 5
              }
            ],
            "version": 1
          },
          "scream": {
            "id": "scream",
            "name": "Face Screaming in Fear",
            "keywords": [
              "scream",
              "munch",
              "scared",
              "omg"
            ],
            "skins": [
              {
                "unified": "1f631",
                "native": "😱",
                "x": 33,
                "y": 10
              }
            ],
            "version": 1
          },
          "clap": {
            "id": "clap",
            "name": "Clapping Hands",
            "keywords": [
              "clap",
              "praise",
              "applause",
              "congrats",
              "yay"
            ],
            "skins": [
              {
                "unified": "1f44f",
                "native": "👏",
                "x": 13,
                "y": 2
              },
              {
                "unified": "1f44f-1f3fb",
                "native": "👏🏻",
                "x": 13,
                "y": 3
              },
              {
                "unified": "1f44f-1f3fc",
                "native": "👏🏼",
                "x": 13,
                "y": 4
              },
              {
                "unified": "1f44f-1f3fd",
                "native": "👏🏽",
                "x": 13,
                "y": 5
              },
              {
                "unified": "1f44f-1f3fe",
                "native": "👏🏾",
                "x": 13,
                "y": 6
              },
              {
                "unified": "1f44f-1f3ff",
                "native": "👏🏿",
                "x": 13,
                "y": 7
              }
            ],
            "version": 1
          },
          "raised_hands": {
            "id": "raised_hands",
            "name": "Raising Hands",
            "keywords": [
              "raised",
              "gesture",
              "hooray",
              "yea",
              "celebration"
            ],
            "skins": [
              {
                "unified": "1f64c",
                "native": "🙌",
                "x": 34,
                "y": 46
              },
              {
                "unified": "1f64c-1f3fb",
                "native": "🙌🏻",
                "x": 34,
                "y": 47
              },
              {
                "unified": "1f64c-1f3fc",
                "native": "🙌🏼",
                "x": 34,
                "y": 48
              },
              {
                "unified": "1f64c-1f3fd",
                "native": "🙌🏽",
                "x": 34,
                "y": 49
              },
              {
                "unified": "1f64c-1f3fe",
                "native": "🙌🏾",
                "x": 34,
                "y": 50
              },
              {
                "unified": "1f64c-1f3ff",
                "native": "🙌🏿",
                "x": 34,
                "y": 51
              }
            ],
            "version": 1
          },
          "heart_hands": {
            "id": "heart_hands",
            "name": "Heart Hands",
            "keywords": [
              "love",
              "appreciation",
              "support"
            ],
            "skins": [
              {
                "unified": "1faf6",
                "native": "🫶",
                "x": 56,
                "y": 14
              },
              {
                "unified": "1faf6-1f3fb",
                "native": "🫶🏻",
                "x": 56,
                "y": 15
              },
              {
                "unified": "1faf6-1f3fc",
                "native": "🫶🏼",
                "x": 56,
                "y": 16
              },
              {
                "unified": "1faf6-1f3fd",
                "native": "🫶🏽",
                "x": 56,
                "y": 17
              },
              {
                "unified": "1faf6-1f3fe",
                "native": "🫶🏾",
                "x": 56,
                "y": 18
              },
              {
                "unified": "1faf6-1f3ff",
                "native": "🫶🏿",
                "x": 56,
                "y": 19
              }
            ],
            "version": 145
          },
          "handshake": {
            "id": "handshake",
            "name": "Handshake",
            "keywords": [
              "agreement",
              "shake"
            ],
            "skins": [
              {
                "unified": "1f91d",
                "native": "🤝",
                "x": 39,
                "y": 37
              },
              {
                "unified": "1f91d-1f3fb",
                "native": "🤝🏻",
                "x": 39,
                "y": 38
              },
              {
                "unified": "1f91d-1f3fc",
                "native": "🤝🏼",
                "x": 39,
                "y": 39
              },
              {
                "unified": "1f91d-1f3fd",
                "native": "🤝🏽",
                "x": 39,
                "y": 40
              },
              {
                "unified": "1f91d-1f3fe",
                "native": "🤝🏾",
                "x": 39,
                "y": 41
              },
              {
                "unified": "1f91d-1f3ff",
                "native": "🤝🏿",
                "x": 39,
                "y": 42
              }
            ],
            "version": 35
          },
          "pray": {
            "id": "pray",
            "name": "Folded Hands",
            "keywords": [
              "pray",
              "please",
              "hope",
              "wish",
              "namaste",
              "highfive",
              "high",
              "five",
              "thank",
              "you",
              "thanks",
              "appreciate"
            ],
            "skins": [
              {
                "unified": "1f64f",
                "native": "🙏",
                "x": 35,
                "y": 27
              },
              {
                "unified": "1f64f-1f3fb",
                "native": "🙏🏻",
                "x": 35,
                "y": 28
              },
              {
                "unified": "1f64f-1f3fc",
                "native": "🙏🏼",
                "x": 35,
                "y": 29
              },
              {
                "unified": "1f64f-1f3fd",
                "native": "🙏🏽",
                "x": 35,
                "y": 30
              },
              {
                "unified": "1f64f-1f3fe",
                "native": "🙏🏾",
                "x": 35,
                "y": 31
              },
              {
                "unified": "1f64f-1f3ff",
                "native": "🙏🏿",
                "x": 35,
                "y": 32
              }
            ],
            "version": 1
          },
          "wave": {
            "id": "wave",
            "name": "Waving Hand",
            "keywords": [
              "wave",
              "hands",
              "gesture",
              "goodbye",
              "solong",
              "farewell",
              "hello",
              "hi",
              "palm"
            ],
            "skins": [
              {
                "unified": "1f44b",
                "native": "👋",
                "x": 12,
                "y": 39
              },
              {
                "unified": "1f44b-1f3fb",
                "native": "👋🏻",
                "x": 12,
                "y": 40
              },
              {
                "unified": "1f44b-1f3fc",
                "native": "👋🏼",
                "x": 12,
                "y": 41
              },
              {
                "unified": "1f44b-1f3fd",
                "native": "👋🏽",
                "x": 12,
                "y": 42
              },
              {
                "unified": "1f44b-1f3fe",
                "native": "👋🏾",
                "x": 12,
                "y": 43
              },
              {
                "unified": "1f44b-1f3ff",
                "native": "👋🏿",
                "x": 12,
                "y": 44
              }
            ],
            "version": 1
          },
          "ok_hand": {
            "id": "ok_hand",
            "name": "Ok Hand",
            "keywords": [
              "fingers",
              "limbs",
              "perfect",
              "okay"
            ],
            "skins": [
              {
                "unified": "1f44c",
                "native": "👌",
                "x": 12,
                "y": 45
              },
              {
                "unified": "1f44c-1f3fb",
                "native": "👌🏻",
                "x": 12,
                "y": 46
              },
              {
                "unified": "1f44c-1f3fc",
                "native": "👌🏼",
                "x": 12,
                "y": 47
              },
              {
                "unified": "1f44c-1f3fd",
                "native": "👌🏽",
                "x": 12,
                "y": 48
              },
              {
                "unified": "1f44c-1f3fe",
                "native": "👌🏾",
                "x": 12,
                "y": 49
              },
              {
                "unified": "1f44c-1f3ff",
                "native": "👌🏿",
                "x": 12,
                "y": 50
              }
            ],
            "version": 1
          },
          "v": {
            "id": "v",
            "name": "Victory Hand",
            "keywords": [
              "v",
              "fingers",
              "ohyeah",
              "peace",
              "two"
            ],
            "skins": [
              {
                "unified": "270c-fe0f",
                "native": "✌️",
                "x": 59,
                "y": 9
              },
              {
                "unified": "270c-1f3fb",
                "native": "✌🏻",
                "x": 59,
                "y": 10
              },
              {
                "unified": "270c-1f3fc",
                "native": "✌🏼",
                "x": 59,
                "y": 11
              },
              {
                "unified": "270c-1f3fd",
                "native": "✌🏽",
                "x": 59,
                "y": 12
              },
              {
                "unified": "270c-1f3fe",
                "native": "✌🏾",
                "x": 59,
                "y": 13
              },
              {
                "unified": "270c-1f3ff",
                "native": "✌🏿",
                "x": 59,
                "y": 14
              }
            ],
            "version": 1
          },
          "i_love_you_hand_sign": {
            "id": "i_love_you_hand_sign",
            "name": "Love-You Gesture",
            "keywords": [
              "i",
              "love",
              "you",
              "hand",
              "sign",
              "fingers"
            ],
            "skins": [
              {
                "unified": "1f91f",
                "native": "🤟",
                "x": 40,
                "y": 8
              },
              {
                "unified": "1f91f-1f3fb",
                "native": "🤟🏻",
                "x": 40,
                "y": 9
              },
              {
                "unified": "1f91f-1f3fc",
                "native": "🤟🏼",
                "x": 40,
                "y": 10
              },
              {
                "unified": "1f91f-1f3fd",
                "native": "🤟🏽",
                "x": 40,
                "y": 11
              },
              {
                "unified": "1f91f-1f3fe",
                "native": "🤟🏾",
                "x": 40,
                "y": 12
              },
              {
                "unified": "1f91f-1f3ff",
                "native": "🤟🏿",
                "x": 40,
                "y": 13
              }
            ],
            "version": 55
          },
          "call_me_hand": {
            "id": "call_me_hand",
            "name": "Call Me Hand",
            "keywords": [
              "hands",
              "gesture",
              "shaka"
            ],
            "skins": [
              {
                "unified": "1f919",
                "native": "🤙",
                "x": 39,
                "y": 13
              },
              {
                "unified": "1f919-1f3fb",
                "native": "🤙🏻",
                "x": 39,
                "y": 14
              },
              {
                "unified": "1f919-1f3fc",
                "native": "🤙🏼",
                "x": 39,
                "y": 15
              },
              {
                "unified": "1f919-1f3fd",
                "native": "🤙🏽",
                "x": 39,
                "y": 16
              },
              {
                "unified": "1f919-1f3fe",
                "native": "🤙🏾",
                "x": 39,
                "y": 17
              },
              {
                "unified": "1f919-1f3ff",
                "native": "🤙🏿",
                "x": 39,
                "y": 18
              }
            ],
            "version": 35
          },
          "point_left": {
            "id": "point_left",
            "name": "Backhand Index Pointing Left",
            "keywords": [
              "point",
              "direction",
              "fingers",
              "hand"
            ],
            "skins": [
              {
                "unified": "1f448",
                "native": "👈",
                "x": 12,
                "y": 21
              },
              {
                "unified": "1f448-1f3fb",
                "native": "👈🏻",
                "x": 12,
                "y": 22
              },
              {
                "unified": "1f448-1f3fc",
                "native": "👈🏼",
                "x": 12,
                "y": 23
              },
              {
                "unified": "1f448-1f3fd",
                "native": "👈🏽",
                "x": 12,
                "y": 24
              },
              {
                "unified": "1f448-1f3fe",
                "native": "👈🏾",
                "x": 12,
                "y": 25
              },
              {
                "unified": "1f448-1f3ff",
                "native": "👈🏿",
                "x": 12,
                "y": 26
              }
            ],
            "version": 1
          },
          "point_right": {
            "id": "point_right",
            "name": "Backhand Index Pointing Right",
            "keywords": [
              "point",
              "fingers",
              "hand",
              "direction"
            ],
            "skins": [
              {
                "unified": "1f449",
                "native": "👉",
                "x": 12,
                "y": 27
              },
              {
                "unified": "1f449-1f3fb",
                "native": "👉🏻",
                "x": 12,
                "y": 28
              },
              {
                "unified": "1f449-1f3fc",
                "native": "👉🏼",
                "x": 12,
                "y": 29
              },
              {
                "unified": "1f449-1f3fd",
                "native": "👉🏽",
                "x": 12,
                "y": 30
              },
              {
                "unified": "1f449-1f3fe",
                "native": "👉🏾",
                "x": 12,
                "y": 31
              },
              {
                "unified": "1f449-1f3ff",
                "native": "👉🏿",
                "x": 12,
                "y": 32
              }
            ],
            "version": 1
          },
          "point_up_2": {
            "id": "point_up_2",
            "name": "Backhand Index Pointing Up",
            "keywords": [
              "point",
              "2",
              "fingers",
              "hand",
              "direction"
            ],
            "skins": [
              {
                "unified": "1f446",
                "native": "👆",
                "x": 12,
                "y": 9
              },
              {
                "unified": "1f446-1f3fb",
                "native": "👆🏻",
                "x": 12,
                "y": 10
              },
              {
                "unified": "1f446-1f3fc",
                "native": "👆🏼",
                "x": 12,
                "y": 11
              },
              {
                "unified": "1f446-1f3fd",
                "native": "👆🏽",
                "x": 12,
                "y": 12
              },
              {
                "unified": "1f446-1f3fe",
                "native": "👆🏾",
                "x": 12,
                "y": 13
              },
              {
                "unified": "1f446-1f3ff",
                "native": "👆🏿",
                "x": 12,
                "y": 14
              }
            ],
            "version": 1
          },
          "point_down": {
            "id": "point_down",
            "name": "Backhand Index Pointing Down",
            "keywords": [
              "point",
              "fingers",
              "hand",
              "direction"
            ],
            "skins": [
              {
                "unified": "1f447",
                "native": "👇",
                "x": 12,
                "y": 15
              },
              {
                "unified": "1f447-1f3fb",
                "native": "👇🏻",
                "x": 12,
                "y": 16
              },
              {
                "unified": "1f447-1f3fc",
                "native": "👇🏼",
                "x": 12,
                "y": 17
              },
              {
                "unified": "1f447-1f3fd",
                "native": "👇🏽",
                "x": 12,
                "y": 18
              },
              {
                "unified": "1f447-1f3fe",
                "native": "👇🏾",
                "x": 12,
                "y": 19
              },
              {
                "unified": "1f447-1f3ff",
                "native": "👇🏿",
                "x": 12,
                "y": 20
              }
            ],
            "version": 1
          },
          "point_up": {
            "id": "point_up",
            "name": "Index Pointing Up",
            "keywords": [
              "point",
              "hand",
              "fingers",
              "direction"
            ],
            "skins": [
              {
                "unified": "261d-fe0f",
                "native": "☝️",
                "x": 57,
                "y": 20
              },
              {
                "unified": "261d-1f3fb",
                "native": "☝🏻",
                "x": 57,
                "y": 21
              },
              {
                "unified": "261d-1f3fc",
                "native": "☝🏼",
                "x": 57,
                "y": 22
              },
              {
                "unified": "261d-1f3fd",
                "native": "☝🏽",
                "x": 57,
                "y": 23
              },
              {
                "unified": "261d-1f3fe",
                "native": "☝🏾",
                "x": 57,
                "y": 24
              },
              {
                "unified": "261d-1f3ff",
                "native": "☝🏿",
                "x": 57,
                "y": 25
              }
            ],
            "version": 1
          },
          "+1": {
            "id": "+1",
            "name": "Thumbs Up",
            "keywords": [
              "+1",
              "thumbsup",
              "yes",
              "awesome",
              "good",
              "agree",
              "accept",
              "cool",
              "hand",
              "like"
            ],
            "skins": [
              {
                "unified": "1f44d",
                "native": "👍",
                "x": 12,
                "y": 51
              },
              {
                "unified": "1f44d-1f3fb",
                "native": "👍🏻",
                "x": 12,
                "y": 52
              },
              {
                "unified": "1f44d-1f3fc",
                "native": "👍🏼",
                "x": 12,
                "y": 53
              },
              {
                "unified": "1f44d-1f3fd",
                "native": "👍🏽",
                "x": 12,
                "y": 54
              },
              {
                "unified": "1f44d-1f3fe",
                "native": "👍🏾",
                "x": 12,
                "y": 55
              },
              {
                "unified": "1f44d-1f3ff",
                "native": "👍🏿",
                "x": 12,
                "y": 56
              }
            ],
            "version": 1
          },
          "muscle": {
            "id": "muscle",
            "name": "Flexed Biceps",
            "keywords": [
              "muscle",
              "arm",
              "flex",
              "hand",
              "summer",
              "strong"
            ],
            "skins": [
              {
                "unified": "1f4aa",
                "native": "💪",
                "x": 27,
                "y": 58
              },
              {
                "unified": "1f4aa-1f3fb",
                "native": "💪🏻",
                "x": 27,
                "y": 59
              },
              {
                "unified": "1f4aa-1f3fc",
                "native": "💪🏼",
                "x": 27,
                "y": 60
              },
              {
                "unified": "1f4aa-1f3fd",
                "native": "💪🏽",
                "x": 28,
                "y": 0
              },
              {
                "unified": "1f4aa-1f3fe",
                "native": "💪🏾",
                "x": 28,
                "y": 1
              },
              {
                "unified": "1f4aa-1f3ff",
                "native": "💪🏿",
                "x": 28,
                "y": 2
              }
            ],
            "version": 1
          },
          "hand_with_index_finger_and_thumb_crossed": {
            "id": "hand_with_index_finger_and_thumb_crossed",
            "name": "Hand with Index Finger and Thumb Crossed",
            "keywords": [
              "heart",
              "love",
              "money",
              "expensive"
            ],
            "skins": [
              {
                "unified": "1faf0",
                "native": "🫰",
                "x": 55,
                "y": 39
              },
              {
                "unified": "1faf0-1f3fb",
                "native": "🫰🏻",
                "x": 55,
                "y": 40
              },
              {
                "unified": "1faf0-1f3fc",
                "native": "🫰🏼",
                "x": 55,
                "y": 41
              },
              {
                "unified": "1faf0-1f3fd",
                "native": "🫰🏽",
                "x": 55,
                "y": 42
              },
              {
                "unified": "1faf0-1f3fe",
                "native": "🫰🏾",
                "x": 55,
                "y": 43
              },
              {
                "unified": "1faf0-1f3ff",
                "native": "🫰🏿",
                "x": 55,
                "y": 44
              }
            ],
            "version": 145
          },
        }
        pickerOptions.set = 'native'
        pickerOptions.data = {...data, emojis}
      }
      const picker = new Picker(pickerOptions);
      document.querySelector(".EmojiWrap")?.appendChild(picker);
    }
  }, [clicked]);

  const hide = () => {
    setClicked(false);
    setHovered(false);
  };

  const handleHoverChange = (open) => {
    setHovered(open);
    setClicked(false);
  };

  const handleClickChange = (open) => {
    setHovered(false);
    setClicked(open);
  };
  return (
    <Tooltip
      overlayClassName="WibotEmoji-hover-Container"
      title="表情"
      trigger="hover"
      open={hovered}
      onOpenChange={handleHoverChange}
    >
      <Popover
        overlayClassName="WibotEmoji-click-Container"
        destroyTooltipOnHide={{
          keepParent: false
        }}
        content={<div className="EmojiWrap"></div>}
        trigger="click"
        open={clicked}
        onOpenChange={handleClickChange}
      >
        <SmileOutlined className={`EmojiIcon ${iconClassName}`} style={iconStyle} />
      </Popover>
    </Tooltip >
  );
};

export default WibotEmoji;
