/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/04/27 14:49
 * @LastEditTime: 2023/05/04 15:16
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaintainLabel/home.jsx
 * @Description: '维护标签'
 */

import { Tree, Input, Divider, Empty } from "antd";
import "./home.less";

const WibotMaintainLabel = ({
  operaType = "add",
  selectedRows = [],
  initTreeData = [],
  searchValue = "",
  treeData = [],
  checkedKeys = [],
  onChangeSearchTag,
  onCheckTagKeys,
  checkStrictly = false,
  placeholder = "搜索",
  fieldNames = { title: "title", key: "key", children: "children" }
}) => {
  return (
    <div className="WibotMaintainLabel-Container">
      {selectedRows && selectedRows.length > 0 && (
        <>
          <div className="wrap-customers">
            {selectedRows.map((item, index) => (
              <p key={index.toString()}>{item.name}</p>
            ))}
          </div>
          <Divider
            type="vertical"
            style={{ height: "auto", borderLeftColor: "#d9d9d9" }}
          />
        </>
      )}
      {initTreeData && initTreeData.length > 0 && (
        <div className="wrap-tree">
          <Input
            style={{ marginBottom: 10 }}
            placeholder={placeholder}
            allowClear
            value={searchValue}
            onChange={(e) => onChangeSearchTag(e, operaType)}
          />
          {
            treeData && treeData.length ? <Tree
              defaultExpandAll
              checkable
              blockNode
              checkStrictly={checkStrictly}
              selectable={false}
              treeData={treeData}
              checkedKeys={checkedKeys}
              onCheck={(checkedKeys, e) =>
                onCheckTagKeys(checkedKeys, e, operaType)
              }
              height={300}
              fieldNames={fieldNames}
            /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </div>
      )}
    </div>
  );
};

export default WibotMaintainLabel;
