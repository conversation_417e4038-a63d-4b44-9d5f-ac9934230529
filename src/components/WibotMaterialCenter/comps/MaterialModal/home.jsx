/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/08/09 14:02
 * @LastEditTime: 2025/05/19 09:51
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaterialCenter/comps/MaterialModal/home.jsx
 * @Description: '素材对话框'
 */

import React, { useState, useEffect } from "react"
import {
  Modal,
  Button,
  Form,
  Input,
  Pagination,
  Spin,
  Card,
  Typography,
  Tabs,
  Image,
  Empty,
  Popover,
} from "antd"
import { CheckCircleTwoTone } from "@ant-design/icons"
import FilterBar from "components/FilterBar/FilterBar"
import LinkCard from "components/LinkCard/home"
import AppletCard from "components/AppletCard/home"
import { apiCall } from "common/utils"
import { removeInputEmpty } from "common/regular"
import { QrCodeBase } from "common/qrcode"
import { FileHOC } from "components/FileHOC/FileHOC"
import "./home.less"

const { Paragraph } = Typography

const PopoverContainer = ({ reason, script, children }) => (
  <Popover
    mouseEnterDelay="0.5"
    placement="topLeft"
    getPopupContainer={(triggerNode) => triggerNode.parentNode}
    content={
      <>
        <p>推荐理由：{reason}</p>
        <p style={{ margin: "0px" }}>参考话术：{script}</p>
      </>
    }
  >
    {children}
  </Popover>
)

const MaterialModal = (props) => {
  const {
    visible = false,
    tabs = [
      "Article",
      "pageArticle",
      "Video",
      "copyWriter",
      "MINI_PROGRAM",
      "Picture",
      "Poster",
      "FORM",
      "Product",
      "QrCode",
    ], // 标签列表
    multiple = true, // 是否多选，默认多选
  } = props.params
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 })
  const [dataSource, setDataSource] = useState([])
  const [selected, setSelected] = useState([])
  const [activeKey, setActiveKey] = useState(tabs[0])
  const [currentData, setCurrentData] = useState([])

  useEffect(() => {}, [])

  useEffect(async () => {
    if (visible) {
      !dataSource.length && fetchList()
    } else {
      setLoading(false)
      setSelected([])
      formForm.resetFields()
    }
  }, [visible])

  const fetchList = (params = {}) => {
    setLoading(true)
    formForm.validateFields().then((formData) => {
      // formData.typeId = formData.typeId?.join(',') || null;
      // if (resourceTypes != 'MicroScript') {
      //   delete formData.microScriptItemId;
      // }
      const { pagination, resourceTypes = activeKey } = params
      const pageInfo = pagination || { current: 1, pageSize: 8 }
      let data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
        resourceTypes,
        resourceStatus: "Added",
      }
      let apiUrl = "/info/infoResource"
      switch (resourceTypes) {
        case "Poster":
          data.resourceTypes = "POSTER_TOOL"
          break
        case "FORM":
          data.type = "FORM"
          data.state = "COLLECTING"
          apiUrl = "/base/form"
          break
        // case 'MicroScript':
        //   data = {
        //     current: pageInfo.current,
        //     size: pageInfo.pageSize,
        //     microScriptItemId,
        //   };
        //   data.type = 'FORM';
        //   apiUrl = '/info/microScript/filter';
        //   break;
        // case 'ResourceSet':
        //   data.needList = true;
        //   apiUrl = '/info/resourceSet';
        //   break;
        case "Product":
          data.subType = "FACE"
          break
        case "QrCode":
          data.type = "GROUP"
          apiUrl = "/activity/dynamicCode/page"
          break
      }
      apiCall(apiUrl, "GET", data)
        .then((res) => {
          let { records, current, size, total, pages } = res
          switch (resourceTypes) {
            case "Poster":
              records = records.map(
                (item) =>
                  (item = {
                    ...item,
                    type: "Poster",
                    fileId: item.images,
                  })
              )
              break
            case "QrCode":
              records = records.map(
                (item) =>
                  (item = {
                    ...item,
                    type: "QrCode",
                    images: [QrCodeBase({ url: item.url })],
                  })
              )
              break
          }
          setDataSource(records)
          let paginations = {
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: [8, 20, 50, 100],
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          }
          setPaginations({ ...paginations })
          // 把当前类型的列表数据缓存起来，再次切换tabs的时候不需要再请求接口了。除非分页更新类型列表
          let newCurrentData = JSON.parse(JSON.stringify(currentData))
          newCurrentData = newCurrentData.filter(
            (item) => item.resourceTypes != resourceTypes
          )
          newCurrentData.push({ resourceTypes, records, paginations })
          setCurrentData([...newCurrentData])
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formForm.resetFields()
    fetchList()
  }

  const switchTypeName = (type) => {
    let name = ""
    switch (type) {
      case "Article":
        name = "推文"
        break
      case "pageArticle":
        name = "网页文章"
        break
      case "Video":
        name = "视频"
        break
      case "copyWriter":
        name = "文案"
        break
      case "MINI_PROGRAM":
        name = "小程序"
        break
      case "Picture":
        name = "图片"
        break
      case "Poster":
        name = "海报"
        break
      case "FORM":
        name = "问卷"
        break
      case "Product":
        name = "产品"
        break
      case "QrCode":
        name = "群活码"
        break
    }
    return name
  }

  const switchTypeHtmlDom = ({ item, index }) => {
    const { id, type, reason, script } = item
    let htmlDom = ""
    switch (type) {
      case "Article":
      case "pageArticle":
      case "Product":
      case "FORM":
        if (type == "FORM") {
          item = {
            ...item,
            title: item.shareTitle,
            description: item.shareDescription,
            images: [item.shareImage],
          }
        }
        htmlDom = (
          <div
            className="link-card-box"
            key={index}
            onClick={() => {
              handleSelect(item)
            }}
          >
            <LinkCard
              isLink={false}
              data={{ ...item }}
              mask={
                selected.some((i) => i.id == id) ? (
                  <div className="mask">
                    <CheckCircleTwoTone />
                  </div>
                ) : (
                  ""
                )
              }
            />
          </div>
        )
        break
      case "Video":
      case "Picture":
      case "Poster":
      case "QrCode":
        item = {
          ...item,
          fileId: type == "Video" ? [...item.videos] : [...item.images],
        }
        htmlDom = (
          <div
            className="media-card"
            key={index}
            onClick={() => {
              handleSelect(item)
            }}
          >
            <FileHOC src={item.fileId[0]}>
              {(url) => (
                <>
                  {type == "Video" && item.fileId?.length ? (
                    <video src={url} />
                  ) : (
                    ""
                  )}
                  {type == "Picture" || type == "Poster" || type == "QrCode" ? (
                    <Image preview={false} src={url} />
                  ) : (
                    ""
                  )}
                </>
              )}
            </FileHOC>
            {selected.some((i) => i.id == id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ""
            )}
          </div>
        )
        break
      case "copyWriter":
        item = {
          ...item,
          content: item.copyWriter,
        }
        htmlDom = (
          <div
            className="text-card"
            key={index}
            onClick={() => {
              handleSelect(item)
            }}
          >
            <Paragraph ellipsis={{ rows: 4 }}>{item.content}</Paragraph>
            {selected.some((i) => i.id == id) ? (
              <div className="mask">
                <CheckCircleTwoTone />
              </div>
            ) : (
              ""
            )}
          </div>
        )
        break
      case "MINI_PROGRAM":
        htmlDom = (
          <div
            className="applet-card-box"
            key={index}
            onClick={() => {
              handleSelect(item)
            }}
          >
            <AppletCard
              data={{ ...item }}
              mask={
                selected.some((i) => i.id == id) ? (
                  <div className="mask">
                    <CheckCircleTwoTone />
                  </div>
                ) : (
                  ""
                )
              }
            />
          </div>
        )
        break
    }
    return (
      <PopoverContainer reason={reason} script={script}>
        {htmlDom}
      </PopoverContainer>
    )
  }

  const handleSelect = (item) => {
    let arr = JSON.parse(JSON.stringify(selected))
    if (arr.some((i) => i.id == item.id)) {
      const newArr = arr.filter((i) => i.id != item.id)
      setSelected(newArr)
    } else {
      if (multiple) {
        // 多选
        arr.push(item)
        setSelected(arr)
      } else {
        // 单选
        arr = [item]
        setSelected(arr)
      }
    }
  }

  const onOk = () => {
    props.params?.onOk?.(selected)
  }

  const onCancel = () => {
    props.params?.onCancel?.()
  }

  return (
    <Modal
      className="MaterialModal-Container"
      visible={visible}
      width={970}
      title={`选择素材（当前已选${selected.length}条）`}
      maskClosable={false}
      okButtonProps={{
        disabled: !selected.length,
      }}
      onCancel={onCancel}
      onOk={onOk}
    >
      <FilterBar bodyStyle={{ padding: "unset", margin: "unset" }}>
        <Form layout={"inline"} form={formForm}>
          <Form.Item
            name="searchStr"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="请输入关键字" allowClear />
          </Form.Item>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: "unset" }}>
          <Tabs
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key)
              const findItem = currentData.find(
                (item) => item.resourceTypes == key
              )
              if (findItem) {
                const { records, paginations } = findItem
                setDataSource(records)
                setPaginations(paginations)
              } else {
                fetchList({ resourceTypes: key })
              }
            }}
          >
            {tabs.map((item, index) => (
              <Tabs.TabPane
                className="custom-scrollbar"
                tab={switchTypeName(item)}
                key={item}
              >
                {dataSource.length ? (
                  dataSource.map((item, index) =>
                    switchTypeHtmlDom({ item, index })
                  )
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Tabs.TabPane>
            ))}
          </Tabs>
          <Pagination
            {...paginations}
            onChange={(page, pageSize) => {
              fetchList({ pagination: { current: page, pageSize: pageSize } })
            }}
          />
        </Card>
      </Spin>
    </Modal>
  )
}

export default MaterialModal
