.MaterialModal-Container {
  .ant-pagination {
    text-align: right;
    margin-top: 10px;
  }

  .ant-popover-content {
    max-width: 500px;
  }

  .mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    -webkit-overflow-scrolling: touch;

    .anticon {
      font-size: 40px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .ant-tabs {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;

    .ant-tabs-tabpane {
      max-height: 416px;
    }

    .link-card-box {
      display: inline-flex;
      margin: 0 37px 10px;
      cursor: pointer;
    }

    .applet-card-box {
      display: inline-flex;
      margin: 0 30px 10px;
      cursor: pointer;
    }

    .media-card,
    .text-card {
      border: 1px solid #ccc;
      border-radius: 6px;
      box-shadow: 1px 2px 3px 0px #ccc;
    }

    .media-card {
      width: 150px;
      height: 198px;
      position: relative;
      display: inline-block;
      margin: 0 39px 10px;
      padding: 10px;
      box-sizing: border-box;
      cursor: pointer;
      vertical-align: middle;
      overflow: hidden;

      video {
        width: 100%;
        height: 176px;
      }

      .ant-image {
        width: 100%;
        height: 176px;

        .ant-image-img {
          height: 100%;
          object-fit: contain;
        }
      }
    }

    .text-card {
      position: relative;
      display: inline-block;
      width: 230px;
      padding: 10px;
      text-align: left;
      white-space: break-spaces;
      overflow: hidden;
      margin: 0 37px 10px;
      min-height: 112px;
      cursor: pointer;

      .ant-typography {
        margin: 0;
      }
    }
  }
}