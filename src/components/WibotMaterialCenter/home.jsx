/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/08/07 09:19
 * @LastEditTime: 2025/05/19 09:50
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaterialCenter/home.jsx
 * @Description: '素材中心'
 */

import React, { useEffect, useState } from "react"
import { Button, Form, Input, message, Space } from "antd"
import {
  MenuOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons"
import {
  sortableContainer,
  sortableElement,
  sortableHandle,
} from "react-sortable-hoc"
import { normFile } from "common/regular"
import WibotUpload from "components/WibotUpload/home"
import MaterialModal from "./comps/MaterialModal/home"
import LinkCard from "components/LinkCard/home"
import AppletCard from "components/AppletCard/home"
import { FileHOC } from "../FileHOC/FileHOC"
import "./home.less"

const { TextArea } = Input

const WibotMaterialCenter = (props) => {
  const {
    menus = ["copyWriter", "Picture", "Material"], // 类型列表
    formRef = null, // 经 Form.useForm() 创建的 form 控制实例，不提供时会自动创建
    globalDragFlag = true, // 是否全局可拖拽，默认true
    describe = "这是一个描述信息", // 描述
    maxLength = 9, // 素材数量上限，默认为9
    // -----------对话窗参数
    tabs = [
      "Article",
      "pageArticle",
      "Video",
      "copyWriter",
      "MINI_PROGRAM",
      "Picture",
      "Poster",
      "FORM",
      "Product",
      "QrCode",
    ], // 标签列表
    multiple = true, // 是否多选，默认多选
    // -----------对话窗参数
  } = props
  const [MaterialModalParams, setMaterialModalParams] = useState({
    visible: false,
  })

  useEffect(() => {}, [])

  const switchTypeName = (type) => {
    let name = ""
    switch (type) {
      case "Article":
        name = "推文"
        break
      case "pageArticle":
        name = "文章"
        break
      case "Video":
        name = "视频"
        break
      case "copyWriter":
        name = "文案"
        break
      case "MINI_PROGRAM":
        name = "小程序"
        break
      case "Picture":
        name = "图片"
        break
      case "Poster":
        name = "海报"
        break
      case "FORM":
        name = "问卷"
        break
      case "Product":
        name = "产品"
        break
      case "QrCode":
        name = "群活码"
        break
    }
    return name
  }

  const SortableContainer = sortableContainer(({ children }) => {
    return <div>{children}</div>
  })

  const DragHandle = sortableHandle(() => (
    <MenuOutlined className="dynamic-move-button" />
  ))

  /**
   * @description: 可排序元素 HOC
   * @param {Object} field // 表单对象
   * @param {Function} remove // 删除表单项
   * @listItem {Boolean} dragFlag 是否可拖拽,默认true
   * @listItem {Boolean} required 是否为必选字段,默认true
   * @listItem {Boolean} nickNameFlag 是否显示客户昵称按钮，默认true
   * @listItem {Boolean} maxLength 文案最大长度，默认200
   * @listItem {Boolean} removeFlag 是否展示删除素材按钮，默认true
   * @return {HTML}
   * @Author: Janaeiw
   * @Date: 2024/08/15 09:28
   */
  const SortableItem = sortableElement(({ field, remove }) => {
    const { index } = field
    const listItem = formRef?.getFieldValue("list")[index]
    const {
      type,
      fileId,
      defaultFlag = false,
      dragFlag = true,
      required = true,
      nickNameFlag = true,
      maxLength = 1000,
      removeFlag = true,
    } = listItem
    return (
      <Form.Item
        className="form-item"
        label={
          <>
            {globalDragFlag && dragFlag ? <DragHandle /> : ""}
            {switchTypeName(type)}
          </>
        }
        required={required}
      >
        <Space align="flexStart">
          {/* 文案 */}
          {type == "copyWriter" ? (
            <Form.Item
              style={{ margin: "0" }}
              name={[field.name, "content"]}
              rules={[
                {
                  required: required,
                  message: `请输入${maxLength}字以内的文案`,
                },
              ]}
              extra={
                <>
                  {nickNameFlag ? (
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        const { content } =
                          formRef?.getFieldValue("list")[index]
                        if (maxLength - content.length < 6) {
                          message.warning(`超出文案上限，请修改后再添加！`)
                          return false
                        }
                        addFocusContent({
                          dom: document.getElementById(
                            `copyWriter-textArea-${index}`
                          ),
                          content: content,
                          insertHTML: "#客户昵称#",
                        }).then((res) => {
                          formRef?.setFieldValue(
                            ["list", field.name, "content"],
                            res
                          )
                        })
                      }}
                    >
                      客户昵称
                    </Button>
                  ) : null}
                </>
              }
            >
              <TextArea
                id={`copyWriter-textArea-${index}`}
                showCount
                maxLength={maxLength}
                allowClear
                autoSize={{ minRows: 4, maxRows: 6 }}
                placeholder={"请输入文案"}
              />
            </Form.Item>
          ) : null}

          {/* 图片、海报、群活码 */}
          {type == "Picture" || type == "Poster" || type == "QrCode" ? (
            <Form.Item
              style={{ margin: "0" }}
              name={[field.name, "fileId"]}
              getValueFromEvent={normFile}
              rules={[{ required: required, message: "请上传图片" }]}
            >
              <WibotUpload
                fileList={fileId?.[0] || ""}
                deletable={false}
                onDone={(params) => {
                  const { fileUrl, fileId } = params
                  formRef?.setFieldValue(
                    ["list", field.name, "fileId"],
                    [fileUrl]
                  )
                  // remove()用于手动更新表单列表，不作为删除使用。
                  remove()
                }}
              />
            </Form.Item>
          ) : null}

          {/* 推文、网页文章、问卷、产品 */}
          {type == "Article" ||
          type == "pageArticle" ||
          type == "FORM" ||
          type == "Product" ? (
            <Form.Item style={{ margin: "0" }} name={[field.name]}>
              <LinkCard
                isLink={false}
                data={{
                  ...listItem,
                }}
              />
            </Form.Item>
          ) : null}

          {/* 视频 */}
          {type == "Video" ? (
            <Form.Item style={{ margin: "0" }} name={[field.name, "fileId"]}>
              <FileHOC src={fileId?.[0] || ""}>
                {(url) => (
                  <video
                    controls
                    src={url}
                    style={{ width: "102px", height: "102px" }}
                  />
                )}
              </FileHOC>
            </Form.Item>
          ) : null}

          {/* 小程序 */}
          {type == "MINI_PROGRAM" ? (
            <Form.Item style={{ margin: "0" }} name={[field.name, "fileId"]}>
              <AppletCard data={{ ...listItem }} />
            </Form.Item>
          ) : null}

          {removeFlag ? (
            <MinusCircleOutlined
              className="dynamic-delete-button"
              onClick={() => remove(field.name)}
            />
          ) : null}
        </Space>
      </Form.Item>
    )
  })

  // 添加焦点内容
  const addFocusContent = ({
    dom,
    content = "",
    insertHTML = "#客户昵称#",
  }) => {
    return new Promise((resolve, reject) => {
      const startPos = dom.selectionStart
      const endPos = dom.selectionEnd
      if (startPos === undefined || endPos === undefined) return
      setTimeout(async () => {
        // react-关键在于给任意一个方法同步
        await dom.focus()
        await dom.setSelectionRange(
          endPos + insertHTML.length,
          endPos + insertHTML.length
        )
      }, 300)
      resolve(
        content.substring(0, startPos) + insertHTML + content.substring(endPos)
      )
    })
  }

  return (
    <div className="WibotMaterialCenter-Container">
      <Form.List name="list" initialValue={[]}>
        {(fields, { add, remove, move }) => (
          <>
            <Form.Item extra={describe}>
              <Space wrap className="typeButton-list">
                {menus.map((item, index) => (
                  <>
                    {item == "copyWriter" && (
                      <Button
                        key={index}
                        disabled={fields.length >= maxLength}
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          add({ type: "copyWriter", content: "" })

                          // const list = formRef?.getFieldValue("list");
                          // formRef?.setFieldsValue({
                          //   list: [...list, { type: "copyWriter", content: "", dragFlag: false, removeFlag: false, required: false }],
                          // });
                        }}
                      >
                        文案
                      </Button>
                    )}
                    {item == "Picture" && (
                      <Button
                        key={index}
                        disabled={fields.length >= maxLength}
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          add({ type: "Picture" })
                        }}
                      >
                        图片
                      </Button>
                    )}
                    {item == "Material" && (
                      <Button
                        key={index}
                        disabled={fields.length >= maxLength}
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          setMaterialModalParams({
                            visible: true,
                            multiple: multiple,
                            tabs: tabs,
                            onOk: (selected) => {
                              for (let i = 0; i < selected.length; i++) {
                                const item = selected[i]
                                const length =
                                  formRef?.getFieldValue("list").length
                                if (length >= maxLength) {
                                  message.warning(
                                    `当前素材数量上限为${maxLength}条，超出部分不支持添加！`
                                  )
                                  break
                                }
                                add({ ...item })
                              }
                              setMaterialModalParams({ visible: false })
                            },
                            onCancel: () => {
                              setMaterialModalParams({ visible: false })
                            },
                          })
                        }}
                      >
                        素材
                      </Button>
                    )}
                  </>
                ))}
              </Space>
            </Form.Item>

            <SortableContainer
              useDragHandle
              onSortEnd={({ oldIndex, newIndex }) => {
                move(oldIndex, newIndex)
              }}
            >
              {fields.map((field, index) => (
                <SortableItem
                  key={`item-${index}`}
                  collection={formRef?.getFieldValue("list")[index].dragFlag}
                  index={index}
                  field={{ ...field, index }}
                  remove={remove}
                />
              ))}
            </SortableContainer>
          </>
        )}
      </Form.List>
      <MaterialModal params={MaterialModalParams} />
    </div>
  )
}

export default WibotMaterialCenter
