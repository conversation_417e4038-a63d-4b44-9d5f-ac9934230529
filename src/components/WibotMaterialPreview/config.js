/**
 * @description: 合并并过滤上级部门与当前员工添加的 copyWriter 类型素材，整理后返回新的列表。
 * 将内容合并为一条 copyWriter 项，并与其他类型素材一起整理为最多 10 条的预览列表。
 * @param {Array} listData - 原始消息列表数据
 * @return {Array} 处理后的预览列表数据
 * @Author: Janaeiw
 * @Date: 2025/05/08 16:22
 */
export const mergeAndFilterCopyWriters = (listData = []) => {
  if (listData.length === 0) return listData

  const copyWriterArr = listData.filter((item) => item.type === "copyWriter")

  if (copyWriterArr.length === 0) return listData

  const mergedContent = copyWriterArr
    .map((item) => item.content || "")
    .filter(Boolean) // 移除空字符串
    .join("\n")

  const mergedCopyWriter = { ...copyWriterArr[0], content: mergedContent }
  const otherArr = listData.filter((item) => item.type !== "copyWriter")

  return [mergedCopyWriter, ...otherArr].slice(0, 10)
}
