/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 14:57
 * @LastEditTime: 2025/07/28 10:33
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaterialPreview/modal.jsx
 * @Description: ''
 */

import { Modal } from "antd"
import React, { useEffect, useState } from "react"
import AppStore from "stores/AppStore"
import Moment from "./moment"
import Session from "./session"
import { mergeAndFilterCopyWriters } from "./config"

const Template = (props) => {
  const {
    visible = false,
    title = AppStore.state.User.userName,
    mergeFlag = false, // 是否合并素材
    listData = [],
    type = "moment",
    onCancel,
  } = props.params
  const [processedList, setProcessedList] = useState([])

  useEffect(() => {
    if (listData.length) {
      const newListData = listData
        .map((item) => {
          const videoUrl =
            item.videos?.[0] ||
            item.fileId?.[0] ||
            item.videoUrl ||
            item.video ||
            ""
          const imageUrl =
            item.images?.[0] ||
            item.fileId?.[0] ||
            item.shareImage ||
            item.imageUrl ||
            item.image ||
            ""
          return {
            ...item,
            videoUrl,
            imageUrl,
          }
        })
        .filter((i) => !(i.type === "copyWriter" && !i.content)) // 过滤空内容的文案素材

      setProcessedList(
        mergeFlag ? mergeAndFilterCopyWriters(newListData) : newListData
      )
    }
  }, [listData])

  return (
    <Modal
      className="WibotMaterialPreview-Modal-Container"
      visible={visible}
      width={300}
      destroyOnClose
      closable={false}
      footer={null}
      bodyStyle={{ padding: "0" }}
      onCancel={() => {
        onCancel?.()
      }}
    >
      {type === "moment" ? (
        <Moment title={title} listData={processedList} />
      ) : (
        <Session title={title} listData={processedList} />
      )}
    </Modal>
  )
}

export default Template
