/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 14:57
 * @LastEditTime: 2025/06/09 09:41
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaterialPreview/moment.jsx
 * @Description: '素材预览-朋友圈'
 */

import { UserOutlined } from "@ant-design/icons"
import { Avatar, Image } from "antd"
import AppletCard from "components/AppletCard/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import LinkCard from "components/LinkCard/home"
import "./moment.less"

const Moment = (props) => {
  const { title, listData = [] } = props

  return (
    <div
      className="WibotMaterialPreview-Moment-Container"
      onClick={(e) => {
        e.preventDefault() // 阻止浏览器默认行为
        e.stopPropagation() // 阻止事件冒泡
      }}
    >
      <div className="header">
        <span className="name">{title}</span>
        <Avatar size={50} shape="square" icon={<UserOutlined />} />
      </div>
      <div className="content custom-scrollbar">
        <div className="left">
          <Avatar size={30} shape="square" icon={<UserOutlined />} />
        </div>
        <div className="right">
          <span className="name">{title}</span>
          {listData.map((item, index) => (
            <span
              className="item"
              key={index}
              style={{
                display: item.type == "copyWriter" ? "block" : "inline-flex",
              }}
            >
              {item.type == "copyWriter" && <p>{item.content}</p>}

              {(item.type == "Picture" ||
                item.type == "Poster" ||
                item.type == "QrCode") && (
                <FileHOC src={item.imageUrl}>
                  {(url) => (
                    <Image
                      src={url}
                      width={60}
                      height={60}
                      fallback="images/fallbackImg.png"
                    />
                  )}
                </FileHOC>
              )}

              {item.type == "Video" && (
                <FileHOC src={item.videoUrl}>
                  {(url) => (
                    <video controls src={url} style={{ width: "100%" }} />
                  )}
                </FileHOC>
              )}

              {(item.type == "Article" ||
                item.type == "pageArticle" ||
                item.type == "FORM" ||
                item.type == "Product" ||
                item.type == "FACE_PRODUCT" ||
                item.type == "SURVEY" ||
                item.type == "RESOURCE_COLLECTION") && (
                <LinkCard data={item} isLink={false} />
              )}

              {item.type === "MINI_PROGRAM" && <AppletCard data={item} />}
            </span>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Moment
