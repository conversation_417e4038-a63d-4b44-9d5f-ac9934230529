.WibotMaterialPreview-Moment-Container {
  position: sticky;
  top: 0px;
  width: 300px;
  height: 500px;
  background: #fff;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0px 0px 10px #999;
  margin: 0 auto;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;
    height: 130px;
    background: #3364d6;
    position: relative;
    flex-shrink: 0;

    .name {
      color: #fff;
      font-size: 14px;
      position: absolute;
      bottom: 0;
      right: 70px;
    }

    .ant-avatar {
      position: absolute;
      bottom: -25px;
      right: 10px;
      border-radius: 4px;
    }
  }

  .content {
    padding: 0 16px;
    margin: 25px 0 10px;
    display: flex;
    flex: 1;
    overflow-x: hidden;

    .right {
      margin: -2px 0 0 8px;

      .name {
        color: #295cd0;
        font-size: 14px;
        width: 100%;
        display: block;
      }

      .item {
        p {
          margin: 0 0 4px 0;
          white-space: pre-line;
          word-break: break-all;
        }

        .ant-image {
          margin: 0 6px 6px 0;

          img {
            object-fit: cover;
          }
        }

        .link-card {
          .ant-image {
            margin: unset !important;
          }
        }
      }
    }
  }
}

.ant-modal-content:has(.WibotMaterialPreview-Moment-Container) {
  background: unset;
}
