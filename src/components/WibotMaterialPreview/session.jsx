/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/04/30 10:36
 * @LastEditTime: 2025/06/09 09:37
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMaterialPreview/session.jsx
 * @Description: '素材预览-会话'
 */

import { UserOutlined } from "@ant-design/icons"
import { Avatar, Image } from "antd"
import AppletCard from "components/AppletCard/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import LinkCard from "components/LinkCard/home"
import "./session.less"

const Session = (props) => {
  const { title, listData = [] } = props

  return (
    <div
      className="WibotMaterialPreview-Session-Container"
      onClick={(e) => {
        e.preventDefault() // 阻止浏览器默认行为
        e.stopPropagation() // 阻止事件冒泡
      }}
    >
      <div className="header">{title}</div>
      <div className="content custom-scrollbar">
        {listData.map((item, index) => (
          <div className="item" key={index}>
            <div className="left">
              <Avatar size={30} shape="square" icon={<UserOutlined />} />
            </div>

            <div className="right">
              <span className="template">
                {item.type === "copyWriter" && item.content && (
                  <p>{item.content}</p>
                )}

                {(item.type === "Picture" ||
                  item.type === "Poster" ||
                  item.type == "QrCode") && (
                  <FileHOC src={item.imageUrl}>
                    {(url) => (
                      <Image
                        src={url}
                        width={60}
                        height={60}
                        fallback="images/fallbackImg.png"
                      />
                    )}
                  </FileHOC>
                )}

                {item.type === "Video" && (
                  <FileHOC src={item.videoUrl}>
                    {(url) => (
                      <video controls src={url} style={{ width: "100%" }} />
                    )}
                  </FileHOC>
                )}

                {(item.type === "Article" ||
                  item.type === "pageArticle" ||
                  item.type === "FORM" ||
                  item.type === "Product" ||
                  item.type === "FACE_PRODUCT" ||
                  item.type == "SURVEY" ||
                  item.type == "RESOURCE_COLLECTION") && (
                  <LinkCard data={item} isLink={false} />
                )}

                {item.type === "MINI_PROGRAM" && <AppletCard data={item} />}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Session
