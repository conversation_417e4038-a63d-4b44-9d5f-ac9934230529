.WibotMaterialPreview-Session-Container {
  position: sticky;
  top: 0px;
  width: 300px;
  height: 500px;
  background: #fff;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0px 0px 10px #999;
  margin: 0 auto;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;
    background: #3364d6;
    position: relative;
    color: #fff;
    flex-shrink: 0;
  }

  .content {
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 10px 0;
    overflow-x: hidden;
    .item {
      display: inline-flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
      .right {
        margin: 0 0 0 8px;

        .template {
          display: inline-flex;
          p {
            margin: 0;
            white-space: pre-line;
            word-break: break-all;
            overflow: hidden;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-shadow: 1px 2px 3px 0px #ccc;
            padding: 6px;
            min-height: 36px;
          }
        }
      }
    }
  }
}

.ant-modal-content:has(.WibotMaterialPreview-Session-Container) {
  background: unset;
}
