/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/05/09 16:55
 * @LastEditTime: 2025/05/16 14:10
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMobilePreview/home.jsx
 * @Description: ''
 */

import React, { useEffect } from "react"
import AppStore from "stores/AppStore"
import "./home.less"

const Template = (props) => {
  const {
    className = "",
    title = AppStore.state.User.userName, // ReactNode
    children, // ReactNode
    footer = null, // ReactNode
    style,
    headerStyle,
    bodyStyle,
    footerStyle,
  } = props

  useEffect(() => {}, [])

  return (
    <div
      className={`WibotMobilePreview-Container ${className}`}
      onClick={(e) => {
        e.preventDefault() // 阻止浏览器默认行为
        e.stopPropagation() // 阻止事件冒泡
      }}
      style={style}
    >
      {title ? (
        <div className="header" style={headerStyle}>
          {title}
        </div>
      ) : null}
      <div className="content custom-scrollbar" style={bodyStyle}>
        {children}
      </div>
      {footer ? (
        <div className="footer" style={footerStyle}>
          {footer}
        </div>
      ) : null}
    </div>
  )
}

export default Template
