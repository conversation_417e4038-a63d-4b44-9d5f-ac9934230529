/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/05/14 10:23
 * @LastEditTime: 2025/05/16 14:10
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMobilePreview/modal.jsx
 * @Description: ''
 */

import { Modal } from "antd"
import React, { useEffect } from "react"
import AppStore from "stores/AppStore"
import Home from "./home"

const Template = (props) => {
  const {
    visible = false,
    className = "",
    title = AppStore.state.User.userName, // ReactNode
    children, // ReactNode
    footer = null, // ReactNode
    style,
    headerStyle,
    bodyStyle,
    footerStyle,
    onCancel,
  } = props

  useEffect(() => {}, [])

  return (
    <Modal
      className={`WibotMobilePreview-Modal-Container ${className}`}
      visible={visible}
      width={300}
      destroyOnClose
      closable={false}
      footer={null}
      bodyStyle={{ padding: "0" }}
      onCancel={() => {
        onCancel?.()
      }}
    >
      <Home
        style={style}
        headerStyle={headerStyle}
        bodyStyle={bodyStyle}
        footerStyle={footerStyle}
        title={title}
        footer={footer}
      >
        {children}
      </Home>
    </Modal>
  )
}

export default Template
