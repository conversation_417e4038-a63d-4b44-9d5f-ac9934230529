import React, {createContext, useState} from "react";
import { Modal } from "antd";
import ReactDOM from "react-dom";
export const WibotModalContext = createContext(null);
export const WibotModal = (props) => {
  const [isModalOpen, setIsModalOpen] = useState(props.open || true);

  const handleOk = () => {
    if (props.onOk) {
      props.onOk();
    }
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    if (props.onCancel) {
      props.onCancel();
    }
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={props.title || "Basic Modal"}
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      {...props}
    >
      <WibotModalContext.Provider value={{...props}}>
        {props.children}
      </WibotModalContext.Provider>
    </Modal>
  );
};

// 添加静态方法
WibotModal.open = (options) => {
  // 临时创建一个 DOM 节点
  const div = document.createElement("div");
  document.body.appendChild(div);

  // 用于销毁 Modal 的函数
  const destroy = () => {
    ReactDOM.unmountComponentAtNode(div);
    document.body.removeChild(div);
  };

  // 事件拦截封装
  const onOk = () => {
    if (options.onOk) options.onOk(); // 调用传入的 onOk
    destroy(); // 销毁 Modal
  };

  const onCancel = () => {
    if (options.onCancel) options.onCancel(); // 调用传入的 onCancel
    destroy(); // 销毁 Modal
  };

  // 渲染 Modal
  ReactDOM.render(
    <WibotModal {...options} open={true} onOk={onOk} onCancel={onCancel}>
      {options.children}
    </WibotModal>,
    div,
  );

  return () => destroy(); // 返回用于销毁 Modal 的函数
};
