/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/12/04 10:57
 * @LastEditTime: 2024/05/24 10:56
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/components/WibotStatistic/home.jsx
 * @Description: '统计数值'
 */

import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import './home.less';

const WibotStatistic = (props) => {
  const { list = [], gutter = [16, 0], span = null, flex = null, bordered = true, precision = 0, selected = '' } = props;

  useEffect(() => { }, []);

  return list?.length > 0 ? <div className="WibotStatistic-Container">
    <Row gutter={gutter}>
      {list.map((item, index) => (
        <Col
          span={span}
          flex={flex}
          key={index}>
          <Card bordered={bordered} onClick={item.onClick} className={selected == item.state ? 'selected' : ''}>
            <Statistic
              title={item.describe ? <Tooltip placement="topLeft" title={item.describe}>{item.title}<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip> : item.title}
              value={item.value}
              suffix={item.suffix}
              precision={precision}
            />
          </Card>
        </Col>
      ))}
    </Row>
  </div> : '';
};

export default WibotStatistic;
