/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/20 14:12
 * @LastEditTime: 2024/09/02 14:16
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/WibotTableTag/home.jsx
 * @Description: '表格标签'
 */

import React, { useEffect } from 'react';
import { Space, Tag, Popover } from 'antd';
import './home.less';

const WibotTableTag = (props) => {
  const { tagList = [], tagColor = 'blue', maxLength = 1 } = props;

  useEffect(() => { }, []);

  const PopoverContent = (
    <Space className="WibotTableTag-Popover-Space custom-scrollbar" align="center" wrap>
      {tagList?.map((item, index) => (
        <Tag key={index} color={tagColor}>
          {item}
        </Tag>
      ))}
    </Space>
  );

  const WibotTableTag = (
    <Space className="WibotTableTag-Popover" align="center" wrap>
      {tagList?.slice(0, maxLength).map((item, index) => (
        <Tag key={index} color={tagColor}>
          {item}
        </Tag>
      ))}
      {tagList?.length > maxLength && '...'}
    </Space>
  );

  return tagList?.length > 0 ? (
    <div className="WibotTableTag-Container">
      {tagList.length > maxLength ? (
        <Popover content={PopoverContent} placement="topLeft">
          {WibotTableTag}
        </Popover>
      ) : (
        WibotTableTag
      )}
    </div>
  ) : (
    ''
  );
};

export default WibotTableTag;
