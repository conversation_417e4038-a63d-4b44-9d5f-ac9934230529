/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/20 14:12
 * @LastEditTime: 2025/05/19 10:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotUpload/home.jsx
 * @Description: 'Upload上传图片-质量优化'
 * @Description: '根据fileList的类型（String/Array）决定使用单图/图片墙模式'
 */

import React, { useEffect, useState } from "react"
import { Upload, message, Image } from "antd"
import { apiCall } from "common/utils"
import {
  base64ToFile,
  beforeUpload,
  lesenImageParams,
  lesenImageSuffix,
} from "common/image"
import WibotUploadImage from "components/WibotUploadImage/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import { formatURL } from "config"

const WibotUpload = (props) => {
  const {
    maxWidth = 1080,
    maxHeight = 10800,
    quality = 0.8,
    size = 10,
    fileList = [],
    disabled = false,
    deletable = true,
    compress = true,
    showUploadList = false,
    listType = "picture-card",
    maxCount = 9,
    onChange,
    onDone,
    onRemove,
    onClose,
  } = props
  const [loading, setLoading] = useState(false)
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [initFileList, setInitFileList] = useState([])
  const [mode, setMode] = useState("single")

  useEffect(() => {
    setInitFileList(fileList)
  }, [])

  useEffect(() => {
    // 处理图片回调，是否满足限制要求
    if (compress) {
      if (fileList.constructor == String && fileList) {
        lesenImage(fileList)
      } else if (fileList.constructor == Array && fileList.length) {
        fileList.forEach((item, index) => {
          // 处理图片异步重复调用
          if (initFileList.length && initFileList[index]?.url == item?.url) {
            return false
          }
          lesenImage(item.url, index)
        })
      }
    }
    // 判断模式
    if (fileList.constructor == String) {
      setMode("single")
    } else if (fileList.constructor == Array) {
      setMode("multiple")
    }
  }, [fileList])

  const customRequest = (config, detail = {}) => {
    const File = config.file
    new html5ImgCompress(File, {
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      quality: quality,
      before: function (file) {
        console.log("压缩前...")
      },
      done: async function (file, base64) {
        console.log("压缩成功...")
        const beforeFileParams = await lesenImageParams(file)
        const afterFileParams = await lesenImageParams(base64)
        const formData = new FormData()
        if (
          compress &&
          (beforeFileParams.width > maxWidth ||
            beforeFileParams.height > maxHeight)
        ) {
          formData.append("file", base64ToFile(base64, File.name))
          message.warning(
            `图片尺寸${beforeFileParams.width}*${beforeFileParams.height}超过企微限制尺寸${maxWidth}*${maxHeight}，系统已自动调整为${afterFileParams.width}*${afterFileParams.height}，请预览图片确认处理结果！`
          )
        } else {
          formData.append("file", file)
        }
        const data = formData
        apiCall("/file/image", "POST", data)
          .then((res) => {
            const { name, index } = detail
            const { fileId, fileUrl } = res
            onDone?.({ fileId, fileUrl, index })
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setLoading(false)
          })
      },
      fail: function (file) {
        console.log("压缩失败...")
        message.error("压缩失败...")
        setLoading(false)
      },
      complete: function (file) {
        console.log("压缩完成...")
      },
      notSupport: function (file) {
        console.log("浏览器不支持！...")
        message.warning("浏览器不支持！")
        setLoading(false)
      },
    })
  }

  const lesenImage = (imageUrl, index = 0) => {
    const img = document.createElement("img")
    imageUrl = formatURL(imageUrl)
    img.src = imageUrl
    img.setAttribute("crossOrigin", "Anonymous") // 图片跨域处理
    img.onload = function () {
      if (img.width > maxWidth || img.height > maxHeight) {
        const canvas = document.createElement("canvas")
        canvas.setAttribute("width", img.width)
        canvas.setAttribute("height", img.height)
        canvas.getContext("2d").drawImage(this, 0, 0, img.width, img.height)
        const suffix = lesenImageSuffix(imageUrl)
        const base64 = canvas.toDataURL(`image/${suffix}`, 1)
        customRequest(
          { file: base64ToFile(base64, `1.${suffix}`) },
          { index: index }
        )
      }
    }
  }

  return (
    <>
      <Upload
        fileList={mode == "single" ? null : fileList}
        customRequest={customRequest}
        listType={listType}
        showUploadList={showUploadList}
        disabled={disabled}
        beforeUpload={(file, files) =>
          beforeUpload(file, files, { size: size })
        }
        onChange={(e) => {
          if (e.file.status === "uploading") {
            setLoading(true)
          }
          onChange?.(e)
        }}
        onRemove={(file) => {
          onRemove?.(file)
        }}
        onPreview={(file) => {
          setPreviewParams({
            ...file,
            visible: true,
          })
        }}
      >
        {mode == "single" ? (
          <WibotUploadImage
            imageUrl={fileList}
            loading={loading}
            deletable={deletable}
            onClose={(e) => {
              onClose?.(e)
            }}
          />
        ) : fileList.length >= maxCount ? null : (
          <WibotUploadImage loading={loading} />
        )}
      </Upload>

      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
    </>
  )
}

export default WibotUpload
