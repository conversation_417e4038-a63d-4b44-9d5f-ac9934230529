/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/20 14:12
 * @LastEditTime: 2024/10/21 17:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotUpload/home_V1.jsx
 * @Description: 'Upload上传图片-质量优化-第一版'
 */

import React, { useEffect, useState } from 'react';
import { Upload, message } from 'antd';
import { apiCall } from "common/utils";
import { base64ToFile, beforeUpload, lesenImageParams, lesenImageSuffix } from "common/image";
import WibotUploadImage from "components/WibotUploadImage/home";

const WibotUpload = (props) => {
  const { maxWidth = 1080, maxHeight = 10800, quality = 0.8, size = 10, imageUrl = '', disabled = false, deletable = true, compress = true, showUploadList = false, onChange, onDone, onClose } = props;
  const [loading, setLoading] = useState(false);

  useEffect(() => { }, []);

  // 处理图片回调，是否满足限制要求
  useEffect(() => {
    if (imageUrl && compress) {
      const img = new Image();
      img.src = imageUrl;
      img.setAttribute("crossOrigin", 'Anonymous'); // 图片跨域处理
      img.onload = function () {
        if (img.width > maxWidth || img.height > maxHeight) {
          const canvas = document.createElement('canvas');
          canvas.setAttribute('width', img.width);
          canvas.setAttribute('height', img.height);
          canvas.getContext('2d').drawImage(this, 0, 0, img.width, img.height);
          const suffix = lesenImageSuffix(imageUrl)
          const base64 = canvas.toDataURL(`image/${suffix}`, 1);
          customRequest({ file: base64ToFile(base64, `1.${suffix}`) })
        }
      }
    }
  }, [imageUrl]);

  const customRequest = (config) => {
    const File = config.file;
    new html5ImgCompress(File, {
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      quality: quality,
      before: function (file) {
        console.log('压缩前...');
      },
      done: async function (file, base64) {
        console.log('压缩成功...');
        const beforeFileParams = await lesenImageParams(file)
        const afterFileParams = await lesenImageParams(base64)
        const formData = new FormData();
        if (compress && (beforeFileParams.width > maxWidth || beforeFileParams.height > maxHeight)) {
          formData.append("file", base64ToFile(base64, File.name));
          message.warning(`图片尺寸${beforeFileParams.width}*${beforeFileParams.height}超过企微限制尺寸${maxWidth}*${maxHeight}，系统已自动调整为${afterFileParams.width}*${afterFileParams.height}，请预览图片确认处理结果！`)
        } else {
          formData.append("file", file);
        }
        const data = formData;
        apiCall("/file/image", "POST", data)
          .then((res) => {
            const { fileId, fileUrl } = res;
            onDone({ fileId, fileUrl })
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });
      },
      fail: function (file) {
        console.log('压缩失败...');
        message.error('压缩失败...')
        setLoading(false);
      },
      complete: function (file) {
        console.log('压缩完成...');
      },
      notSupport: function (file) {
        console.log('浏览器不支持！...');
        message.warning('浏览器不支持！')
        setLoading(false);
      }
    });
  }

  return (
    <Upload
      customRequest={customRequest}
      listType="picture-card"
      showUploadList={showUploadList}
      disabled={disabled}
      beforeUpload={(file, files) => beforeUpload(file, files, { size: size })}
      onChange={(e) => {
        onChange(e)
        if (e.file.status === "uploading") {
          setLoading(true);
          return;
        }
      }}>
      <WibotUploadImage
        imageUrl={imageUrl}
        loading={loading}
        deletable={deletable}
        onClose={(e) => { onClose(e) }}
      />
    </Upload>
  );
};

export default WibotUpload;
