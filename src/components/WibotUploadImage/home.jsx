/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/20 14:12
 * @LastEditTime: 2025/05/19 09:50
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/components/WibotUploadImage/home.jsx
 * @Description: '上传图片预览'
 */

import React, { useEffect } from "react"
import { Image } from "antd"
import {
  LoadingOutlined,
  PlusOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons"
import { FileHOC } from "components/FileHOC/FileHOC"
import "./home.less"

const WibotUploadImage = (props) => {
  const {
    imageUrl = "",
    loading = false,
    beforeLoadingText = "上传",
    afterLoadingText = "上传中",
    deletable = true,
    onClose,
  } = props

  useEffect(() => {}, [])

  return (
    <div className="WibotUploadImage-Container">
      {imageUrl ? (
        <div
          className="imageBox"
          onClick={(e) => {
            e.preventDefault() // 阻止浏览器默认行为
            e.stopPropagation() // 阻止事件冒泡
          }}
        >
          <FileHOC src={imageUrl}>
            {(url) => (
              <Image
                fallback="images/fallbackImg.png"
                height={"100%"}
                src={url}
              />
            )}
          </FileHOC>
          {deletable && (
            <CloseCircleOutlined
              onClick={(e) => {
                e.preventDefault() // 阻止浏览器默认行为
                e.stopPropagation() // 阻止事件冒泡
                onClose(e)
              }}
              className="cancel-upload-icon"
            />
          )}
        </div>
      ) : (
        <div className="loadingBox">
          {loading ? <LoadingOutlined /> : <PlusOutlined />}
          <div style={{ marginTop: 8 }}>
            {loading ? afterLoadingText : beforeLoadingText}
          </div>
        </div>
      )}
    </div>
  )
}

export default WibotUploadImage
