.WibotUploadImage-Container {
  position: relative;
  width: 100%;
  height: 100%;

  .imageBox {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .previewBox {
      opacity: 0;

      transition: opacity 0.3s;

      .eyeBox {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        z-index: 9;

        .eyeIcon {
          margin-right: 5px;
        }
      }

      .mask {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        background: rgba(0, 0, 0, 0.5);
        cursor: pointer;

      }

      &:hover {
        opacity: 1;
      }
    }
  }

  .loadingBox {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}