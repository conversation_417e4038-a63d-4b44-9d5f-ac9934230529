import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { Form, Button, Input, message } from 'antd';
import { apiCall } from 'common/utils';
import './UserInfo.less';

const FormItem = Form.Item;

class UserInfo extends Reflux.Component {
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = "ApiLog";
    this.state = {
      submitting: false
    };

  }

  onFinish = async (e) => {
    await this.formRef.current.validateFields().then(values => {
      if (values.newpwd != values.renewpwd) {
        message.error('确认新密码不一致');
        return;
      }
      this.setState({
        submitting: true
      })
      const callback = () => {
        this.setState({
          submitting: false
        });
        this.formRef.current.resetFields();
      };

      var data = {
        oldpwd: values.oldpwd,
        newpwd: values.newpwd
      };
      apiCall('modify_password', "POST", data).then(retdata => {
        message.success("密码更新成功");
        callback?.();
      }).catch((ret) => {
        message.error(ret.msg);
        callback?.();
      })

    }).catch(errorInfo => {
      console.log("errorInfo", errorInfo);
    });
  };

  onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo);
  };

  render () {
    const { submitting } = this.state;
    return (
      <div id="form-container">
        <Form name="basic" className="basic-form" size={"default"} ref={this.formRef}
          onFinish={this.onFinish} onFinishFailed={this.onFinishFailed}
          initialValues={{ remember: true }}>
          <FormItem label="旧&nbsp;&nbsp;密&nbsp;&nbsp;码" name="oldpwd"
            rules={[{ required: true, message: '请输入旧密码!', }]}>
            <Input style={{}} />
          </FormItem>

          <FormItem
            label="新&nbsp;&nbsp;密&nbsp;&nbsp;码"
            name="newpwd"
            rules={[{ required: true, message: '请输入新密码!', }]}>
            <Input.Password />
          </FormItem>

          <FormItem
            label="确认密码"
            name="renewpwd"
            rules={[{ required: true, message: '请确认新密码!', }]}>
            <Input.Password />
          </FormItem>

          <FormItem className="item-button" >
            <Button loading={submitting} className="submit-button" type="primary" htmlType="submit">提交</Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}




export default UserInfo;
