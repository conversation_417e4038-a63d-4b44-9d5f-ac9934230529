import React from 'react';

// 参考oss的图片处理参数
// image/resize,m_lfit,h_400,limit_1/auto-orient,0/quality,q_90/format,jpg
//src, resize, quality, format, onLoad
function AliOSSImage(props) {
  process="image";
  if (props.resize) process+="/resize,"+props.resize;
  if (props.quality) process+="/quality,"+props.quality;
  if (props.format) process+="/format,"+props.format;

  let url = `http://iesp-image.oss-cn-shenzhen.aliyuncs.com/${props.imgid}`;
  if (process != "image") {
    //没有任何处理样式,就直出,否则使用oss处理器
    url += `?x-oss-process=${process}`;
  }
  return <img style={{width:"100%"}} src={url} {...props}/>
}

export default AliOSSImage;
