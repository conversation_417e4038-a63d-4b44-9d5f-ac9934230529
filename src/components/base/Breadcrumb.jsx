import React from 'react';
import { Breadcrumb } from 'antd';

import './Breadcrumb.less';

const BreadcrumbWrapper = (props) => {
  const { pathSnippets } = props;
  const genBreadcrumb = pathSnippets.map((routes, index, arr) => {
    if (index == arr.length - 1) {
      return <Breadcrumb.Item key={index}>{routes.name}</Breadcrumb.Item>;
    }
    return (
      <Breadcrumb.Item key={index}>
        {routes.name}
      </Breadcrumb.Item>
    );
  });

  return (
    <div id="breadcrumb">
      <Breadcrumb >
        {/* <Breadcrumb.Item key="/">首页</Breadcrumb.Item> */}
        {genBreadcrumb}
      </Breadcrumb>
    </div>
  );
};

export default BreadcrumbWrapper;
