import React from 'react';
import { Collapse } from 'antd';

import './ErrorBoundary.less';

const Panel = Collapse.Panel;

// https://reactjs.org/blog/2017/07/26/error-handling-in-react-16.html
class ErrorBoundary extends React.Component {
  constructor (props) {
    super(props);
    this.state = { error: null, errorInfo: null };
  }

  componentDidCatch (error, errorInfo) {
    // Catch errors in any components below and re-render with error message
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render () {
    if (this.state.errorInfo) {
      // Error path
      return (
        <div className='error-boundary'>
          <h2>Something went wrong.</h2>
          <Collapse bordered={false} >
            <Panel header={this.state.error?.toString()} >
              <p>{this.state.errorInfo.componentStack}</p>
            </Panel>
          </Collapse>
        </div>
      );
    }
    // Normally, just render children
    return this.props.children;
  }
}

export default ErrorBoundary;
