import React from 'react';
import Reflux from 'reflux';
import { TreeSelect, message } from 'antd';
import AppStore from 'stores/AppStore';
import { api2Call } from 'common/utils';

const TreeNode = TreeSelect.TreeNode;

class FolderSelect extends Reflux.Component {
  constructor (props) {
    super(props);
    this.store = AppStore;
    this.state = {
      value: props.value,
      treedata: null
    };
  }

  componentWillReceiveProps (nextProps) {
    this.setState({
      value: nextProps.value,
    });
  }

  componentWillMount () {

    api2Call('/repotree', 'GET')
      .then((retdata) => {
        this.setState({
          treedata: retdata,
        });
      })
      .catch(() => {
        message.error('文件夹列表获取失败');
      });
  }

  onSelectChange = (key) => {
    this.setState({ value: key });
    if (this.props.onChange) {this.props.onChange(key);}
  }

  render () {
    const width = this.props.width || '100%';

    let flag = 1;
    const genTreeNode = (data) => {
      let ret = [];
      if (this.props.show0 && flag) {
        ret.push(
          <TreeNode key={0} title={'——根目录——'} value={'0'} />
        );
        flag = 0;
      }
      data.forEach((item) => {
        if (item.children) {
          ret.push(
            <TreeNode key={item.id.toString()} title={item.name} value={item.id.toString()}>
              {genTreeNode(item.children)}
            </TreeNode>
          );
        } else {
          ret.push(<TreeNode key={item.id.toString()} title={item.name} value={item.id.toString()} />);
        }
      });
      return ret;
    };
    return (
      <TreeSelect
        {...this.props}
        allowClear
        value={this.state.value}
        style={{ width }}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        treeCheckable={this.props.multiple}
        showCheckedStrategy={TreeSelect.SHOW_PARENT}
        onChange={this.onSelectChange}
      >
        {this.state.treedata && genTreeNode(this.state.treedata)}
      </TreeSelect>
    );
  }
}

export default FolderSelect;
