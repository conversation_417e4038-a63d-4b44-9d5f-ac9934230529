import { Menu } from "antd";
import React from "react";
import Icon from "components/base/Icon";
import { clearCache } from "react-router-cache-route";

import "./ModuleMenu.less";

const { SubMenu } = Menu;

class _Menu extends React.Component {
  constructor(props) {
    super(props);
    const { path: rootPath, name: rootName } = props.menuList;
    const [openKeys, current, currentName] = this.findRoutes(props);

    this.setTitle(currentName)
    this.state = {
      openKeys: openKeys,
      current: current,
    };

    // 面包屑底层信息
    this.rootPathSnippet = {
      path: rootPath,
      name: rootName,
    };

    // 通知父组件改变面包屑信息，应对页面刷新后状态丢失
    this.props.onRouteChanges([
      this.rootPathSnippet,
      {
        path: current,
        name: currentName,
      },
    ]);
  }

  setTitle(name){
    document.title = name
  }

  componentWillReceiveProps (nextProps) {
    const { path: rootPath, name: rootName } = nextProps.menuList;
    const [openKeys, current, currentName] = this.findRoutes(nextProps);
    this.setState({
      openKeys,
      current,
    });

    if (this.props.location.pathname != nextProps.location.pathname) {
      // 通知父组件改变面包屑信息
      this.props.onRouteChanges([
        {
          path: rootPath,
          name: rootName,
        },
        {
          path: current,
          name: currentName,
        },
      ]);
    }
  }

  findSubRoute (menu, key, path) {
    for (let i = 0; i < menu.length; i++) {
      if (menu[i].path == key) {
        return { matchRoute: menu[i], path: path };
      }
      if (menu[i].children && menu[i].children.length > 0) {
        return this.findSubRoute(
          menu[i].children,
          key,
          path + "," + menu[i].path
        );
      }
    }
    return { matchRoute: null, path: "" };
  }

  findRoutes = (props) => {
    const route = props.location.pathname.split("/").filter((i) => i);
    const menuItems = props.menuList.children;

    let openKeys, current, currentName;
    if (route.length == 1) {
      // 进入页面初始菜单展开项及选中值
      openKeys = menuItems[0].children ? [menuItems[0].path] : [];
      current = menuItems[0].children
        ? menuItems[0].children[0].path
        : menuItems[0].path;
      currentName = menuItems[0].children
        ? menuItems[0].children[0].name
        : menuItems[0].name;
      props.history.replace("/" + props.menuList.path + "/" + current);
    } else {
      // 应对页面刷新时菜单展开项及选中值重置的情况
      let submenuKey = [];
      // 根据路由找展开菜单key值
      for (let i = 0; i < menuItems.length; i++) {
        if (menuItems[i].path == route[1] && menuItems[i].children) {
          currentName = menuItems[i].name;
          break;
        }
        if (menuItems[i].children) {
          const { matchRoute, path } = this.findSubRoute(
            menuItems[i].children,
            route[1],
            menuItems[i].path
          );
          if (matchRoute) {
            submenuKey = path.split(",");
            currentName = matchRoute.name;
            break;
          }
        } else {
          if (menuItems[i].path == route[1]) {
            currentName = menuItems[i].name;
          }
        }
      }
      openKeys = submenuKey;
      current = route[1];
    }
    return [openKeys, current, currentName];
  };

  _genMenu = (node) => {
    if (node.children && node.children.length > 0) {
      return (
        <SubMenu
          key={node.path}
          title={
            <span>
              <Icon iconid={node.icon} />
              <span className="min-hidden">{node.name}</span>
            </span>
          }
        >
          {node.children.map((menuItem) => this._genMenu(menuItem))}
        </SubMenu>
      );
    } else {
      return (
        <Menu.Item key={node.path}  onClick={() => {
          this.setTitle(node.name)
        }}>
          {node.icon ? <Icon iconid={node.icon} /> : ""}
          {node.path.includes("http") ? (
            <a target="_blank" href={node.path} rel="noreferrer">
              {node.name}
            </a>
          ) : (
            <span className="min-hidden">{node.name}</span>
          )}
        </Menu.Item>
      );
    }
  };

  genMenuList = () => this.props.menuList.children.map((menuItem) => this._genMenu(menuItem));

  handleClick = (e) => {
    const { key, keyPath } = e;
    this.props.history.push(`/${this.props.menuList.path}/${key}`);
    clearCache(); // 清空路由缓存
    //清空选择素材弹窗缓存
    localStorage.removeItem("materialModalCascader");
    localStorage.removeItem("materialModalTabType");
    this.setState({
      current: key,
    });

    let currentName;
    const menuItems = this.props.menuList.children;

    for (let i = 0; i < menuItems.length; i++) {
      if (menuItems[i].path == key) {
        currentName = menuItems[i].name;
        break;
      }
      if (menuItems[i].children) {
        const matchRoute = menuItems[i].children.find(
          (child) => child.path == key
        );
        if (matchRoute) {
          currentName = matchRoute.name;
          break;
        }
      }
    }

    // 通知父组件改变面包屑信息
    this.props.onRouteChanges([
      this.rootPathSnippet,
      {
        path: key,
        name: currentName,
      },
    ]);
  };

  render () {
    // Don't show popup menu when it is been collapsed
    const menuProps = this.props.collapsed
      ? {}
      : {
        openKeys: this.state.openKeys,
      };
    return (
      <Menu
        mode="inline"
        className="module-menu"
        // // 同步url与菜单选中状态
        onClick={this.handleClick}
        selectedKeys={[this.state.current]}
        // // 用户实现只展开当前父级菜单
        {...menuProps}
        forceSubMenuRender={true}
        onOpenChange={(openKeys) => {
          this.setState({ openKeys });
        }}
      >
        {this.genMenuList()}
      </Menu>
    );
  }
}

export default _Menu;
