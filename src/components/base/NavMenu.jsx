import React from "react";
import { Menu } from "antd";
import { <PERSON> } from "react-router-dom";
import { clearCache } from "react-router-cache-route";

import "./ModuleMenu.less";

class NavMenu extends React.Component {
  constructor(props) {
    super(props);

    const current = this.findRoutes(props);
    this.state = {
      current: current,
    };
  }

  componentWillReceiveProps(nextProps) {
    const current = this.findRoutes(nextProps);
    this.setState({
      current,
    });
  }

  findRoutes = (props) => {
    const route = props.location.pathname.split("/").filter((i) => i);
    const routeList = this.props.menuList.map((item) => item.path);

    let current;
    if (route.length == 0) {
      // 默认选中并展示第一个模块页面
      current = props.menuList[0].path;
      props.history.replace("/" + current);
    } else if (routeList.includes(route[0])) {
      current = route[0];
    } else {
      // 如果第一片路由并没有对应的菜单项，则默认展示第一个模块页面
      current = routeList[0];
      props.history.replace("/" + current);
    }
    return current;
  };

  genMenuList = () =>
    this.props.menuList.map((menuItem) =>
      !menuItem.hidden ? (
        <Menu.Item key={menuItem.path}>
          <Link to={"/" + menuItem.path}>{menuItem.name}</Link>
        </Menu.Item>
      ) : ''
    );

  render() {
    return (
      <Menu
        mode="horizontal"
        style={{ fontSize: "16px" }}
        onClick={() => {
          clearCache(); // 清空路由缓存
        }}
        // 同步url与菜单选中状态
        selectedKeys={[this.state.current]}
      >
        {this.genMenuList()}
      </Menu>
    );
  }
}

export default NavMenu;
