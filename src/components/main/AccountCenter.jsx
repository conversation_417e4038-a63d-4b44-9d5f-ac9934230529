// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Route, Switch } from 'react-router-dom';

// 路由页面
import UserInfo from 'components/accountcenter/UserInfo';

// 组件类
import appendSiderMenu from 'HOC/appendSiderMenu';

@appendSiderMenu
class AccountCenter extends Reflux.Component {
  constructor (props) {
    super(props);
  }

  render () {
    return (
      <Switch>
        <Route path={`${this.props.match.path}/userinfo`} render={(props) => <UserInfo {...props} />} />
      </Switch>
    );
  }
}

export default AccountCenter;
