// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Route, Switch, Redirect } from 'react-router-dom';

// 路由页面
import SubAccount from '../../view/comworkbench/SubAccount/home';
import Department from '../../view/comworkbench/Department/home';
import Role from '../../view/comworkbench/Role/home';
import Sdk from '../../view/comworkbench/Sdk/home';
import Test from '../../view/comworkbench/Test/home';

// 组件类
import appendSiderMenu from 'HOC/appendSiderMenu';
@appendSiderMenu
class ComWorkBench extends Reflux.Component {
  constructor (props) {
    super(props);
  }

  componentWillMount () {
    super.componentWillMount();
  }

  render () {
    return (
      <Switch>
        <Route path={`${this.props.match.path}/subaccount`} render={(props) => <SubAccount {...props} />} />
        <Route path={`${this.props.match.path}/department`} render={(props) => <Department {...props} />} />
        <Route path={`${this.props.match.path}/role`} render={(props) => <Role {...props} />} />
        <Route path={`${this.props.match.path}/sdk`} render={(props) => <Sdk {...props} />} />
        <Route path={`${this.props.match.path}/test`} render={(props) => <Test {...props} />} />

        {/* 路由通配重定向 */}
        <Route path={`${this.props.match.path}/*`} render={() => (
          <Redirect to="/" />
        )} />
      </Switch>
    );
  }
}

export default ComWorkBench;
