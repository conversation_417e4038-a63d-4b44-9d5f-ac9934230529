// 依赖类
import React from 'react';
import Reflux from 'reflux';
import ReactDom from 'react-dom';

import { Route, Link, Switch } from 'react-router-dom';

// 路由页面

import Markdown from 'components/help/Markdown';


// 组件类
import appendSiderMenu from 'HOC/appendSiderMenu';

import Guide from 'components/help/Guide.md';
import APIDoc from 'components/help/APIDoc.md';
import ObjDoc from 'components/help/ObjDoc.md';
import RetDoc from 'components/help/RetDoc.md';
import SDKDoc from 'components/help/SDKDoc.md';

import PriceDoc from 'components/help/PriceDoc.md';
import DownSDKDoc from 'components/help/DownSDKDoc.md';

@appendSiderMenu
class Help extends Reflux.Component {
  constructor (props) {
    super(props);
  }

  render () {
    return (
      <Switch>
        <Route path={`${this.props.match.path}/guide`} render={(props) => <Markdown content={Guide} {...props} />} />
        <Route path={`${this.props.match.path}/apidoc`} render={(props) => <Markdown content={APIDoc} {...props} />} />
        <Route path={`${this.props.match.path}/objdoc`} render={(props) => <Markdown content={ObjDoc} {...props} />} />
        <Route path={`${this.props.match.path}/retdoc`} render={(props) => <Markdown content={RetDoc} {...props} />} />
        <Route path={`${this.props.match.path}/sdkdoc`} render={(props) => <Markdown content={SDKDoc} {...props} />} />

        <Route path={`${this.props.match.path}/pricedoc`} render={(props) => <Markdown content={PriceDoc} {...props} />} />

        <Route path={`${this.props.match.path}/downsdk`} render={(props) => <Markdown content={DownSDKDoc} {...props} />} />
      </Switch>
    );
  }
}

export default Help;
