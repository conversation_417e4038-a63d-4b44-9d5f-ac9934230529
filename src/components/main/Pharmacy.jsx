// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Route, Redirect } from 'react-router-dom';
// 路由缓存
import CacheRoute, { CacheSwitch } from 'react-router-cache-route';
// 组件类
import appendSiderMenu from 'HOC/appendSiderMenu';

// 路由模块
const routeModules = [
  // 处方单订单
  {
    path: '/prescriptionOrder',
    component: require('@pharmacy/Order/PrescriptionOrder/home'),
    meta: { cacheRoute: false }, // cacheRoute是否需要进行页面缓存
  },
  // 客户管理
  {
    path: '/customer/details',
    component: require('@pharmacy/CustomerManagement/Customer/details'),
    meta: { cacheRoute: true },
  },
  {
    path: '/customer',
    component: require('@pharmacy/CustomerManagement/Customer/home'),
    meta: { cacheRoute: true },
  },
];

@appendSiderMenu
class Pharmacy extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentWillMount () {
    super.componentWillMount();
  }

  render () {
    return (
      <CacheSwitch>
        {/* cacheRoutes是否需要进行页面缓存 */}
        {routeModules.map((item, index) =>
          item.meta?.cacheRoute ? (
            <CacheRoute
              cacheKey={`${index}`}
              key={index}
              path={`${this.props.match.path}${item.path}`}
              render={(props) => <item.component.default {...props} />}
            />
          ) : (
            <Route
              key={index}
              path={`${this.props.match.path}${item.path}`}
              render={(props) =>
                <item.component.default {...props} />
              }
            />
          )
        )}
        {/* 路由通配重定向 */}
        <Route
          path={`${this.props.match.path}/*`}
          render={() => <Redirect to="/" />}
        />
      </CacheSwitch>
    );
  }
}

export default Pharmacy;
