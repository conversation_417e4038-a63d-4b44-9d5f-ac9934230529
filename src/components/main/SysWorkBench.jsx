// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Route, Switch, Redirect } from 'react-router-dom';

// 路由页面
import Company from '../../view/sysworkbench/Company/home';
import Suite from '../../view/sysworkbench/Suite/home';
import Machine from '../../view/sysworkbench/Machine/home';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../view/sysworkbench/BotRunner/home';
import Brain from '../../view/sysworkbench/Brain/home';
import Task from '../../view/sysworkbench/Task/home';
import Assistant from '../../view/sysworkbench/Assistant/home';
import AssistantOperateRecords from '../../view/sysworkbench/Assistant/operateRecords';
import SysDict from '../../view/sysworkbench/SysDict/home';
import SysAccount from '../../view/sysworkbench/SysAccount/home';
import BRLog from '../../view/sysworkbench/BRLog';

// 组件类
import appendSiderMenu from 'HOC/appendSiderMenu';

@appendSiderMenu
class SysWorkBench extends Reflux.Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {
    super.componentWillMount();
  }

  render () {
    return (
      <Switch>
        <Route path={`${this.props.match.path}/company`} render={(props) => <Company {...props} />} />
        <Route path={`${this.props.match.path}/suite`} render={(props) => <Suite {...props} />} />
        <Route path={`${this.props.match.path}/machine`} render={(props) => <Machine {...props} />} />
        <Route path={`${this.props.match.path}/botrunner`} render={(props) => <BotRunner {...props} />} />
        <Route path={`${this.props.match.path}/brain`} render={(props) => <Brain {...props} />} />
        <Route path={`${this.props.match.path}/task`} render={(props) => <Task {...props} />} />
        <Route path={`${this.props.match.path}/brlog`} render={(props) => <BRLog {...props} />} />
        <Route path={`${this.props.match.path}/sysdict`} render={(props) => <SysDict {...props} />} />
        <Route path={`${this.props.match.path}/sysaccount`} render={(props) => <SysAccount {...props} />} />
        <Route path={`${this.props.match.path}/assistantmanage/OperateRecords/:id`} render={(props) => <AssistantOperateRecords {...props} />} />
        <Route path={`${this.props.match.path}/assistantmanage`} render={(props) => <Assistant {...props} />} />

        {/* 路由通配重定向 */}
        <Route path={`${this.props.match.path}/*`} render={() => (
          <Redirect to="/" />
        )} />

      </Switch>
    );
  }
}

export default SysWorkBench;
