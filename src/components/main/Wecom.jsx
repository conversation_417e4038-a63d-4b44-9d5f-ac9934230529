// 依赖类
import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";
import Reflux from "reflux";
// 路由缓存
import CacheRoute, { CacheSwitch } from "react-router-cache-route";
// 组件类
import appendSiderMenu from "HOC/appendSiderMenu";

// 路由模块
const routeModules = [
  // 信息资源
  {
    path: "/informationLibrary/form",
    component: lazy(() => import("@wecom/Resource/InformationLibrary/form")),
    meta: { cacheRoute: false }, // cacheRoute是否需要进行页面缓存
  },
  {
    path: "/informationLibrary",
    component: lazy(() => import("@wecom/Resource/InformationLibrary/home")),
    meta: { cacheRoute: false }, // cacheRoute是否需要进行页面缓存
  }, // 话术库
  {
    path: "/scriptLibrary/baseFormInfo",
    component: lazy(() => import("@wecom/Resource/ScriptLibrary/baseFormInfo")),
    meta: { cacheRoute: false },
  },
  {
    path: "/scriptLibrary",
    component: lazy(() => import("@wecom/Resource/ScriptLibrary/home")),
    meta: { cacheRoute: true },
  }, // 资源审核
  {
    path: "/resourceCheck",
    component: lazy(() => import("@wecom/Resource/ResourceCheck/home")),
    meta: { cacheRoute: false },
  }, // 标签库
  {
    path: "/tagLibrary",
    component: lazy(() => import("@wecom/Resource/TagLibrary/home")),
    meta: { cacheRoute: false },
  }, // 金融微店
  {
    path: "/wecomStoreManagement",
    component: lazy(() => import("@wecom/Resource/WecomStoreManagement/home")),
    meta: { cacheRoute: false },
  }, // 数字员工
  {
    path: "/digitalHumanManagement",
    component: lazy(() => import("@wecom/DigitalHuman/Management/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/digitalAccountMultiEscrow",
    component: lazy(() => import("@wecom/DigitalHuman/AccountEscrow/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/digitalAccountMultiManageEscrow/history",
    component: lazy(() =>
      import("@wecom/DigitalHuman/AccountManageEscrow/history")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/digitalAccountMultiManageEscrow",
    component: lazy(() =>
      import("@wecom/DigitalHuman/AccountManageEscrow/home")
    ),
    meta: { cacheRoute: false },
  }, // 侧边栏设置
  {
    path: "/sideBarSetting",
    component: lazy(() =>
      import("@wecom/SystemManagement/SideBarSetting/home")
    ),
    meta: { cacheRoute: false },
  }, // 数据看板
  {
    path: "/dataBoard",
    // component: lazy(() => import("@wecom/DigitalHuman/group/group")),
    component: lazy(() => import("@wecom/DataBoard/DataBoard/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/groupChatInbox",
    component: lazy(() => import("@wecom/DigitalHuman/group/group")),
    // component: lazy(() => import("@wecom/DataBoard/DataBoard/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/customerStatistics",
    component: lazy(() => import("@wecom/DataBoard/CustomerStatistics/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/groupOperating",
    component: lazy(() => import("@wecom/DataBoard/GroupOperating/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/sessionStatistics",
    component: lazy(() => import("@wecom/DataBoard/SessionStatistics/home")),
    meta: { cacheRoute: false },
  }, // 客户管理
  {
    path: "/customer/details",
    component: lazy(() => import("@wecom/CustomerManagement/Customer/details")),
    meta: { cacheRoute: true },
  },
  {
    path: "/customer",
    component: lazy(() => import("@wecom/CustomerManagement/Customer/home")),
    meta: { cacheRoute: true },
  }, // 客户群SOP管理
  {
    path: "/groupSOP/form",
    component: lazy(() =>
      import("@wecom/CustomerManagement/GroupManagement/SOP/form")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/groupSOP/monitor",
    component: lazy(() =>
      import("@wecom/CustomerManagement/GroupManagement/SOP/monitor")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/groupSOP",
    component: lazy(() =>
      import("@wecom/CustomerManagement/GroupManagement/SOP/home")
    ),
    meta: { cacheRoute: true },
  }, // 客户群管理
  {
    path: "/groupManagement/detail",
    component: lazy(() =>
      import("@wecom/CustomerManagement/GroupManagement/Group/details")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/groupManagement",
    component: lazy(() =>
      import("@wecom/CustomerManagement/GroupManagement/home")
    ),
    meta: { cacheRoute: true },
  }, // 渠道活码
  {
    path: "/drivetraffic/liveCode/form",
    component: lazy(() => import("@wecom/Activity/DriveTraffic/LiveCode/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/drivetraffic/liveCode/details",
    component: lazy(() =>
      import("@wecom/Activity/DriveTraffic/LiveCode/details/home")
    ),
    meta: { cacheRoute: true },
  }, // 欢迎语活码
  {
    path: "/drivetraffic/welcomeSpeech/form",
    component: lazy(() =>
      import("@wecom/Activity/DriveTraffic/WelcomeSpeech/form")
    ),
    meta: { cacheRoute: false },
  }, // 员工活码
  {
    path: "/drivetraffic/employeeCode/referralCode",
    component: lazy(() =>
      import("@wecom/Activity/DriveTraffic/EmployeeCode/ReferralCode/home")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/drivetraffic/employeeCode/form",
    component: lazy(() =>
      import("@wecom/Activity/DriveTraffic/EmployeeCode/form")
    ),
    meta: { cacheRoute: false },
  }, // 获客引流
  {
    path: "/drivetraffic",
    component: lazy(() => import("@wecom/Activity/DriveTraffic/home")),
    meta: { cacheRoute: true },
  }, // 定位配置
  {
    path: "/location",
    component: lazy(() => import("@wecom/SystemManagement/Location/home")),
    meta: { cacheRoute: false },
  }, // 营销任务
  {
    path: "/marketingtask/mass/form",
    component: lazy(() => import("@wecom/Activity/MarketingTask/Mass/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/marketingtask/mass/statistics",
    component: lazy(() =>
      import("@wecom/Activity/MarketingTask/Mass/Statistics/home")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/marketingtask/ordinary/form",
    component: lazy(() =>
      import("@wecom/Activity/MarketingTask/Ordinary/form")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/marketingtask/ordinary/details",
    component: lazy(() =>
      import("@wecom/Activity/MarketingTask/Ordinary/details")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/marketingtask",
    component: lazy(() => import("@wecom/Activity/MarketingTask/home")),
    meta: { cacheRoute: true },
  }, // 营销活动
  {
    path: "/marketingActivity/form",
    component: lazy(() => import("@wecom/Activity/MarketingActivity/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/marketingActivity/detail",
    component: lazy(() => import("@wecom/Activity/MarketingActivity/detail")),
    meta: { cacheRoute: true },
  },
  {
    path: "/marketingActivity",
    component: lazy(() => import("@wecom/Activity/MarketingActivity/home")),
    meta: { cacheRoute: true },
  }, // 奖品管理
  {
    path: "/prize",
    component: lazy(() => import("@wecom/Activity/Prize/home")),
    meta: { cacheRoute: true },
  }, // 积分审核
  {
    path: "/pointVerify",
    component: lazy(() => import("@wecom/CustomerManagement/PointVerify/home")),
    meta: { cacheRoute: false },
  }, // 企业工作台
  {
    path: "/department",
    component: lazy(() => import("@comworkbench/Department/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/subaccount",
    component: lazy(() => import("@comworkbench/SubAccount/home")),
    meta: { cacheRoute: false },
  },
  {
    path: "/role",
    component: lazy(() => import("@comworkbench/Role/home")),
    meta: { cacheRoute: false },
  }, // 场景类型
  {
    path: "/sceneSetting",
    component: lazy(() => import("@wecom/SystemManagement/SceneSetting/home")),
    meta: { cacheRoute: false },
  }, // 员工标签
  {
    path: "/employeeTag",
    component: lazy(() => import("@wecom/SystemManagement/EmployeeTag/home")),
    meta: { cacheRoute: false },
  }, // 渠道数据
  {
    path: "/customerChannle/details",
    component: lazy(() =>
      import("@wecom/SystemManagement/CustomerChannle/details")
    ),
    meta: { cacheRoute: true },
  }, // 渠道活码
  {
    path: "/customerChannle",
    component: lazy(() =>
      import("@wecom/SystemManagement/CustomerChannle/home")
    ),
    meta: { cacheRoute: true },
  }, // 产品导入记录
  {
    path: "/productImportRecord",
    component: lazy(() =>
      import("@wecom/SystemManagement/ProductImportRecord/home")
    ),
    meta: { cacheRoute: false },
  }, // 资源中心
  {
    path: "/resourceCenter/postIframe",
    component: lazy(() =>
      import("@wecom/Resource/ResourceCenter/comps/Poster/postIframe")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/resourceCenter/form",
    component: lazy(() => import("@wecom/Resource/ResourceCenter/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/resourceCenter",
    component: lazy(() => import("@wecom/Resource/ResourceCenter/home")),
    meta: { cacheRoute: true },
  }, // 资源集
  {
    path: "/resourceSet/form",
    component: lazy(() => import("@wecom/Resource/ResourceSet/form")),
    meta: { cacheRoute: true },
  },
  {
    path: "/resourceSet",
    component: lazy(() => import("@wecom/Resource/ResourceSet/home")),
    meta: { cacheRoute: true },
  }, // 问答库
  {
    path: "/knowledgeLib/form",
    component: lazy(() => import("@wecom/Resource/KnowledgeLib/form")),
    meta: { cacheRoute: true },
  },
  {
    path: "/knowledgeLib",
    component: lazy(() => import("@wecom/Resource/KnowledgeLib/home")),
    meta: { cacheRoute: true },
  }, // 客户流失统计
  {
    path: "/lossStatistical",
    component: lazy(() =>
      import("@wecom/CustomerManagement/LossStatistical/home")
    ),
    meta: { cacheRoute: true },
  }, // 问卷维护
  {
    path: "/resourceCollection/maintain",
    component: lazy(() =>
      import("@wecom/Activity/ResourceCollection/maintain")
    ),
    meta: { cacheRoute: false },
  }, // 群发问卷
  {
    path: "/resourceCollection/question",
    component: lazy(() =>
      import("@wecom/Activity/ResourceCollection/question")
    ),
    meta: { cacheRoute: false },
  }, // 资源集锦
  {
    path: "/resourceCollection",
    component: lazy(() => import("@wecom/Activity/ResourceCollection/home")),
    meta: { cacheRoute: true },
  }, // 资源中心小程序跳转页面
  {
    path: "/applet/relevance",
    component: lazy(() =>
      import("@wecom/Resource/ResourceCenter/comps/Applet/comps/Relevance")
    ),
    meta: { cacheRoute: false },
  }, // 资源中心小程序页面路径
  {
    path: "/applet/pagePath",
    component: lazy(() =>
      import("@wecom/Resource/ResourceCenter/comps/Applet/comps/PagePath")
    ),
    meta: { cacheRoute: false },
  }, // 资源中心小程序AppId
  {
    path: "/applet/appId",
    component: lazy(() =>
      import("@wecom/Resource/ResourceCenter/comps/Applet/comps/AppId")
    ),
    meta: { cacheRoute: false },
  }, // 海报工具
  {
    path: "/posterTool/form",
    component: lazy(() => import("@wecom/PosterTool/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/posterTool/postIframe",
    component: lazy(() => import("@wecom/PosterTool/postIframe")),
    meta: { cacheRoute: false },
  },
  {
    path: "/posterTool",
    component: lazy(() => import("@wecom/PosterTool/home")),
    meta: { cacheRoute: true },
  }, // 表单工具
  {
    path: "/formList/form",
    component: lazy(() => import("@wecom/FormTool/FormList/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/formList/draw",
    component: lazy(() => import("@wecom/FormTool/FormList/draw")),
    meta: { cacheRoute: false },
  },
  {
    path: "/formList/detail",
    component: lazy(() => import("@wecom/FormTool/FormList/detail")),
    meta: { cacheRoute: false },
  },
  {
    path: "/formList/rules",
    component: lazy(() => import("@wecom/FormTool/FormList/rules")),
    meta: { cacheRoute: false },
  },
  {
    path: "/formList",
    component: lazy(() => import("@wecom/FormTool/FormList/home")),
    meta: { cacheRoute: true },
  },
  {
    path: "/templateList",
    component: lazy(() => import("@wecom/FormTool/TemplateList/home")),
    meta: { cacheRoute: false },
  }, // 员工任务统计
  {
    path: "/employeeTaskStatistics",
    component: lazy(() =>
      import("@wecom/DataBoard/EmployeeTaskStatistics/home")
    ),
    meta: { cacheRoute: false },
  }, // 资源使用统计
  {
    path: "/resourceStatistics",
    component: lazy(() => import("@wecom/DataBoard/ResourceStatistics/home")),
    meta: { cacheRoute: false },
  }, // 客户继承
  {
    path: "/customerInherit/form",
    component: lazy(() => import("@wecom/CustomerManagement/Inheritance/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/customerInherit/detail",
    component: lazy(() =>
      import("@wecom/CustomerManagement/Inheritance/detail")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/customerInherit",
    component: lazy(() => import("@wecom/CustomerManagement/Inheritance/home")),
    meta: { cacheRoute: true },
  }, // 微话本
  {
    path: "/microScript/form",
    component: lazy(() => import("@wecom/Resource/MicroScript/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/microScript/detail",
    component: lazy(() => import("@wecom/Resource/MicroScript/detail")),
    meta: { cacheRoute: true },
  },
  {
    path: "/microScript",
    component: lazy(() => import("@wecom/Resource/MicroScript/home")),
    meta: { cacheRoute: true },
  }, // 学习营
  {
    path: "/studyCamp/form",
    component: lazy(() => import("@wecom/StudentTool/StudyCamp/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/studyCamp/details",
    component: lazy(() => import("@wecom/StudentTool/StudyCamp/details")),
    meta: { cacheRoute: true },
  },
  {
    path: "/studyCamp",
    component: lazy(() => import("@wecom/StudentTool/StudyCamp/home")),
    meta: { cacheRoute: true },
  }, // 课程管理
  {
    path: "/courseManage/course",
    component: lazy(() => import("@wecom/StudentTool/CourseManage/course")),
    meta: { cacheRoute: true },
  },
  {
    path: "/courseManage/details",
    component: lazy(() =>
      import("@wecom/StudentTool/CourseManage/details/home")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/courseManage",
    component: lazy(() => import("@wecom/StudentTool/CourseManage/home")),
    meta: { cacheRoute: true },
  }, // 课时管理
  {
    path: "/classHour/form",
    component: lazy(() =>
      import("@wecom/StudentTool/CourseManage/ClassHour/form")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/classHour/details",
    component: lazy(() =>
      import("@wecom/StudentTool/CourseManage/ClassHour/details/home")
    ),
    meta: { cacheRoute: true },
  }, // 题库管理
  {
    path: "/studyCampExam",
    component: lazy(() => import("@wecom/StudentTool/QuestionManage/home")),
    meta: { cacheRoute: true },
  }, // 海报字体管理
  {
    path: "/posterFont",
    component: lazy(() => import("@wecom/SystemManagement/PosterFont/home")),
    meta: { cacheRoute: false },
  }, // 会话存档
  {
    path: "/sessionArchive",
    component: lazy(() =>
      import("@wecom/CustomerManagement/SessionArchive/Record/home")
    ),
    meta: { cacheRoute: false },
  }, // 收件箱
  {
    path: "/inbox",
    component: lazy(() => import("@wecom/DigitalHuman/Inbox/home")),
    meta: { cacheRoute: false },
  }, // 自定义报表
  {
    path: "/customReport",
    component: lazy(() => import("@wecom/DataBoard/CustomReport/home")),
    meta: { cacheRoute: false },
  }, // 地区编码
  {
    path: "/areaCode",
    component: lazy(() => import("@wecom/SystemManagement/AreaCode/home")),
    meta: { cacheRoute: false },
  }, // 自动话
  {
    path: "/messageAuto/form",
    component: lazy(() => import("@wecom/DigitalHuman/MessageAuto/form")),
    meta: { cacheRoute: true },
  },
  {
    path: "/messageAuto",
    component: lazy(() => import("@wecom/DigitalHuman/MessageAuto/home")),
    meta: { cacheRoute: true },
  }, // 工行配置
  {
    path: "/icbcConfig",
    component: lazy(() => import("@wecom/SystemManagement/IcbcConfig/home")),
    meta: { cacheRoute: true },
  }, // 会话小结
  {
    path: "/sessionSummary",
    component: lazy(() => import("@wecom/DigitalHuman/SessionSummary/home")),
    meta: { cacheRoute: true },
  }, // 坐席管理
  {
    path: "/agentSeatManage/group/form",
    component: lazy(() =>
      import("@wecom/DigitalHuman/AgentSeatManage/Group/form")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/agentSeatManage/auto/form",
    component: lazy(() =>
      import("@wecom/DigitalHuman/AgentSeatManage/Auto/form")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/agentSeatManage",
    component: lazy(() => import("@wecom/DigitalHuman/AgentSeatManage/home")),
    meta: { cacheRoute: true },
  }, // 坐席工作台
  {
    path: "/agentSeat",
    component: lazy(() =>
      import(
        /* webpackChunkName: "agentSeat" */ "@wecom/DigitalHuman/AgentSeat/home"
      )
    ),
    meta: { cacheRoute: true },
  }, // 工单管理
  {
    path: "/ticketManage",
    component: lazy(() => import("@wecom/DigitalHuman/TicketManage/home")),
    meta: { cacheRoute: true },
  }, // 坐席监控
  {
    path: "/ticketMonitor",
    component: lazy(() => import("@wecom/DigitalHuman/TicketMonitor/home")),
    meta: { cacheRoute: true },
  },
  // 对话管理V2
  {
    path: "/conversationManageV2",
    component: lazy(() =>
      import("@wecom/DigitalHuman/ConversationManageV2/home")
    ),
    meta: { cacheRoute: true },
  },
  // 对话管理
  {
    path: "/conversationManage",
    component: lazy(() =>
      import("@wecom/DigitalHuman/ConversationManage/home")
    ),
    meta: { cacheRoute: true },
  }, // 自动化助手
  {
    path: "/autoAsst",
    component: lazy(() => import("@wecom/DigitalHuman/AutoAsst/home")),
    meta: { cacheRoute: true },
  }, // 客户朋友圈
  {
    path: "/moment/detail",
    component: lazy(() => import("@wecom/Activity/Moment/detail")),
    meta: { cacheRoute: false },
  },
  {
    path: "/moment/form",
    component: lazy(() => import("@wecom/Activity/Moment/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/moment",
    component: lazy(() => import("@wecom/Activity/Moment/home")),
    meta: { cacheRoute: true },
  }, // 内容审核
  {
    path: "/audit/detail",
    component: lazy(() => import("@wecom/Resource/Audit/detail")),
    meta: { cacheRoute: true },
  },
  {
    path: "/audit",
    component: lazy(() => import("@wecom/Resource/Audit/home")),
    meta: { cacheRoute: true },
  }, // 质检规则管理
  {
    path: "/qualityControlRule/form",
    component: lazy(() =>
      import("@wecom/DigitalHuman/QualityControlRule/form")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/qualityControlRule",
    component: lazy(() =>
      import("@wecom/DigitalHuman/QualityControlRule/home")
    ),
    meta: { cacheRoute: true },
  }, // 对话质检
  {
    path: "/converstionQualityControl/detail",
    component: lazy(() =>
      import("@wecom/DigitalHuman/ConverstionQualityControl/detail")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/converstionQualityControl",
    component: lazy(() =>
      import("@wecom/DigitalHuman/ConverstionQualityControl/home")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/customerGroupMsg/form",
    component: lazy(() => import("@wecom/Activity/CustomerGroupMsg/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/customerGroupMsg/detail",
    component: lazy(() => import("@wecom/Activity/CustomerGroupMsg/detail")),
    meta: { cacheRoute: false },
  },
  {
    path: "/customerGroupMsg",
    component: lazy(() => import("@wecom/Activity/CustomerGroupMsg/index")),
    meta: { cacheRoute: true },
  },
  {
    path: "/monitorRule/form",
    component: lazy(() => import("@wecom/RiskControl/MonitorRule/form")),
    meta: { cacheRoute: false },
  },
  {
    path: "/monitorRule",
    component: lazy(() => import("@wecom/RiskControl/MonitorRule/index")),
    meta: { cacheRoute: true },
  },
  {
    path: "/riskControlmonitor/blackList",
    component: lazy(() =>
      import("@wecom/RiskControl/RiskControlmonitor/blackList")
    ),
    meta: { cacheRoute: false },
  },
  {
    path: "/riskControlmonitor",
    component: lazy(() =>
      import("@wecom/RiskControl/RiskControlmonitor/index")
    ),
    meta: { cacheRoute: true },
  },
  {
    path: "/deviceConfig",
    component: lazy(() => import("@wecom/SystemManagement/deviceConfig/home")),
    meta: { cacheRoute: true },
  },
  {
    path: "/digitalAccountApply",
    component: lazy(() => import("@wecom/DigitalHuman/AccountApply/home")),
    meta: { cacheRoute: true },
  },
  {
    path: "/notifyConfig",
    component: lazy(() => import("@wecom/NotifyConfig/home")),
    meta: { cacheRoute: true },
  },
]

@appendSiderMenu
class Wecom extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentWillMount() {
    super.componentWillMount();
  }

  render() {
    return (
      <React.Suspense fallback={<div>Loading...</div>}>
        <CacheSwitch>
          {routeModules.map((item, index) =>
            item.meta?.cacheRoute ? (
              <CacheRoute
                cacheKey={`${index}`}
                key={index}
                path={`${this.props.match.path}${item.path}`}
                render={(props) => <item.component {...props} />}
              />
            ) : (
              <Route
                key={index}
                path={`${this.props.match.path}${item.path}`}
                render={(props) => <item.component {...props} />}
              />
            ),
          )}
          {/* 路由通配重定向 */}
          <Route
            path={`${this.props.match.path}/*`}
            render={() => <Redirect to="/" />}
          />
        </CacheSwitch>
      </React.Suspense>
    );
  }
}

export default Wecom;
