// 一级菜单为导航栏，不需要指定icon，必须指定非空唯一path
// 二级菜单为侧边栏，必须指定非空唯一path，如果有children则为SubMenu，建议添加icon，如果没有children则为Menu.Item，无需添加icon
// 三级菜单为SubMenu下的Menu.Item，必须指定非空唯一path，不需要指定icon

import { message } from 'antd';

export function genNavMenu (userrole, menus) {

  let ret = menus?.map((item) => ({
    name: item.name,
    path: item.path,
    hidden: item.hidden ?? false
  }));

  if (ret == null) {return [];}

  return ret;

}

function _genMenu (menu) {
  return {
    name: menu.name,
    path: menu.path,
    icon: menu.icon,
    children: (menu.childMenuList?.length > 0) ? menu.childMenuList.map((child) => _genMenu(child)) : null
  };
}

export function genSideMenu (userrole, key, menus) {
  let index = 0;
  for (index = 0; index < menus.length; index++) {
    if (menus[index].path == key) {
      break;
    }
  }

  // 无法匹配任何menukey,返回一个空的菜单
  if (index >= menus.length) {
    message.error('当前公司没有该套件的使用权限！');
    return {};
  }
  return _genMenu(menus[index]);
}
