import React from 'react';
import Reflux from 'react';
import { Select, } from 'antd';
import { apiCall } from "common/utils";

class AccountSelect extends Reflux.Component {

  constructor(props) {
    super(props);
    this.state = {
      options: null
    };
  }

  static defaultProps = {
    // 仅罗列未绑定员工的账号
    onlyUnbound: false,
    extraOptions: []
  }

  componentDidMount () {
    apiCall("user/option", "GET", { unbundling: this.props.onlyUnbound }).then(res => {
      let options = res.map(user => ({ label: user.name, value: user.id }));
      if (this.props.extraOptions && this.props.extraOptions.length > 0) {
        this.props.extraOptions.forEach(eo => {
          if (!options.some(opt => opt.value === eo.value)) {
            options.push(eo);
          }
        })
      }
      this.setState({ options: options });
    });
  }

  render () {
    let { onlyUnbound, extraOptions, ...passThroughProps } = this.props;
    return (
      <Select
        options={this.state.options}
        showSearch
        filterOption={(input, option) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
        {...passThroughProps}
        value={this.props.value && this.state.options ? this.props.value : null}
      />
    );
  }
}

export default AccountSelect;
