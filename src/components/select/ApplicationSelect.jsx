//依赖类
import React from 'react';
import Reflux from 'reflux';
import { Select } from 'antd';
import AppActions from 'actions/AppActions';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class ApplicationSelect extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {
      appList: undefined,
    }
  }

  componentDidMount () {
    apiCall("option/application", "GET")
      .then((retdata) => {
        this.setState({
          appList: retdata
        });
        if (this.props.value) {
          this.onSelectChange(this.props.value);
        } else
          if (this.props.defaultSelectFirst) {
            this.onSelectChange(retdata[0].appid + "");
          }
      })
  }

  onSelectChange = (key) => {
    AppActions.setState({ appid: parseInt(key) });
    sessionStorage.setItem('appid', parseInt(key));
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { Options, appList, value } = this.state;
    const width = this.props.width || "100%";
    const styleCss = {
      width: width,
      textAlign: "left"
    };
    const options = Options?.[schema];
    return (
      <Select
        value={options && value}
        style={styleCss}
        {...this.props}
        onChange={this.onSelectChange}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          appList?.map((item, index) => <Option value={item.appid + ""} key={item.appid + ""}>{item.name}</Option>)
        }
      </Select>
    )
  }
}

export default ApplicationSelect;
