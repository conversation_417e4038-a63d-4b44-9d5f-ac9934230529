//依赖类
import React from 'react';
import Reflux from 'reflux';
import { Select } from 'antd';
import AppActions from 'actions/AppActions';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class CompanySelect extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {
      companyList: undefined,
    }
  }

  componentDidMount () {
    apiCall("company/option", "GET")
      .then((retdata) => {
        this.setState({
          companyList: retdata
        });
        if (this.props.value) {
          this.onSelectChange(this.props.value);
        } else
          if (this.props.defaultSelectFirst) {
            this.onSelectChange(retdata[0].companyId + "");
          }
      })
  }

  onSelectChange = (key) => {
    AppActions.setState({ companyId: parseInt(key) });
    sessionStorage.setItem('companyId', parseInt(key));
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { Options, companyList, value } = this.state;
    const width = this.props.width || "100%";
    const styleCss = {
      width: width,
      textAlign: "left"
    };
    const options = Options?.[schema];
    return (
      <Select
        value={options && value}
        style={styleCss}
        {...this.props}
        onChange={this.onSelectChange}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          companyList?.map((item, index) => <Option value={item.companyId + ""} key={item.companyId + ""}>{item.name}</Option>)
        }
      </Select>
    )
  }
}

export default CompanySelect;
