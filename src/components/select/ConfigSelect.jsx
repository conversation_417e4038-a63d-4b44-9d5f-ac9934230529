// 依赖类
import React from 'react';
import { Select } from 'antd';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class ConfigSelect extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      confList: undefined,
    };
  }

  componentWillMount () {
    let data = {
      appid: this.props.appid,
      confid: this.props.confid,
    };
    apiCall('option/config', 'GET', data)
      .then((retdata) => {
        this.setState({
          confList: retdata
        });
      });
  }

  componentWillUpdate (nextProps, nextState) {
    // appid 有更新
    if (this.props.appid != nextProps.appid) {
      if (nextProps.appid == null) {
        this.setState({ confList: [] });
        return;
      }
      let data = {
        appid: nextProps.appid
      };
      apiCall('option/config', 'GET', data)
        .then((retdata) => {
          this.setState({
            confList: retdata
          });
        });
    }
  }

  render () {
    const { confList } = this.state;

    const width = this.props.width || '100%';
    return (
      <Select
        defaultValue={this.props.confid ? this.props.confid.toString() : '0'}
        style={{ width }}
        {...this.props}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          confList?.map((item, index) => <Option value={item.confid + ''} key={item.confid + ''}>{item.name}</Option>)
        }
      </Select>
    );
  }
}

export default ConfigSelect;
