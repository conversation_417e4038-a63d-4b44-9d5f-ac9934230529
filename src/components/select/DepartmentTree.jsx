import React from 'react';
import Reflux from 'react';
import { TreeSelect, } from 'antd';
import { apiCall } from "common/utils";

class DepartmentTree extends Reflux.Component {

  constructor(props) {
    super(props);
    this.state = {
      departmentList: null
    };
  }

  componentDidMount () {
    debugger
    apiCall("/dept/option", "GET", {}).then(res => {
      this.setState({ departmentList: DepartmentTree.mapDepartmentList(res) });
    });
  }

  static mapDepartmentList (departmentList, hideNodeFilter) {
    if (!departmentList) {
      return null;
    }
    let list = [];
    departmentList.forEach(node => {
      if (hideNodeFilter && hideNodeFilter(node)) {
        return
      }
      let childList = node.childList;
      if (childList && childList.length > 0) {
        childList = this.mapDepartmentList(childList, hideNodeFilter);
      }
      list.push({
        title: node.name,
        value: node.id,
        children: childList
      });
    })
    return list;
  }

  render () {
    return (
      <TreeSelect
        treeData={this.state.departmentList}
        {...this.props}
        value={this.props.value && this.state.departmentList ? this.props.value : null}
      />
    );
  }
}

export default DepartmentTree;
