// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Select } from 'antd';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class EmployeeSelect extends Reflux.Component {
  constructor (props) {
    super(props);
    this.state = {
      employeeList: undefined,
    };
  }

  componentDidMount () {

    apiCall('appemployee/option', 'GET', null)
      .then((retdata) => {
        this.setState({
          employeeList: retdata
        });
        if (this.props.value) {
          this.onSelectChange(this.props.value);
        } else
        if (this.props.defaultSelectFirst) {
          this.onSelectChange(retdata[0].id + '');
        }
      });
  }

  onSelectChange = (key) => {
    // AppActions.setState({employeeId:parseInt(key)});    //需要全局变更才需要这里
    this.setState({ value: parseInt(key) });
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { employeeList, value } = this.state;
    const width = this.props.width || '100%';
    return (
      <Select
        style={{ width }}
        {...this.props}
        onChange={this.onSelectChange}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          employeeList?.map((item, index) => <Option value={item.id + ''} key={item.id + ''}>{item.name}</Option>)
        }
      </Select>
    );
  }
}

export default EmployeeSelect;
