
import React from 'react';
import Reflux from 'reflux';
import { Radio } from 'antd';
import AppActions from 'actions/AppActions';
import Appstore from 'stores/AppStore';
import { isObjEqual } from 'common/utils';

class GeneralRadio extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [Appstore];
    this.state = {
      g_options: null,
      value: props.value
    };
  }

  getOptionCallback = (data) => {

    if (this.props.value) {
      this.setState({ value: this.props.value });
      this.props.onChange(this.props.value);
    } else {
      if (this.props.defaultSelectFirst) {
        if (this.props.mode == "multiple") {
          this.onSelectChange([data[0][0].toString()])
        } else {
          this.onSelectChange(data[0][0].toString())
        }
      }
      if (this.props.defaultSelectAll && this.props.mode == "multiple") {
        this.onSelectChange(data.map(item => item[0].toString()));
      }
    }
  }

  componentWillReceiveProps (nextProps) {
    if (nextProps.value != this.props.value) {
      this.setState({ value: nextProps.value });
    }

    if (!isObjEqual(nextProps.params, this.props.params) || (nextProps.schema != this.props.schema)) {

      AppActions.getGeneralOptions(nextProps.schema, nextProps.params, this.getOptionCallback);
    }
  }

  componentDidMount () {
    const { schema, params } = this.props;

    if (params || !this.state.g_options?.[schema]) {
      AppActions.getGeneralOptions(schema, params, this.getOptionCallback);
    } else {
      this.getOptionCallback(this.state.g_options[schema])
    }

  }

  onSelectChange = (key) => {
    this.setState({ value: key });
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { g_options, value } = this.state;
    const { schema } = this.props;
    const option = g_options?.[schema];
    const width = this.props.width || "100%";
    return (
      <Radio.Group
        value={option && value}
        mode={this.props.mode ? this.props.mode : null}
        style={{ width }}
        onChange={this.onSelectChange}
        {...this.props}
      >
        {
          (() => option?.map((item, index) => <Radio.Button key={index} value={item.id}>{item.name}</Radio.Button>))()
        }
      </Radio.Group>

    )
  }
}

export default GeneralRadio;
