// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Select } from 'antd';
import AppActions from 'actions/AppActions';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class RobotSelect extends Reflux.Component {
  constructor (props) {
    super(props);
    this.state = {
      robotList: undefined,
    };
  }

  componentDidMount () {

    apiCall('option/robot', 'GET', { appid: this.props.appid })
      .then((retdata) => {
        this.setState({
          robotList: retdata
        });
        if (this.props.value) {
          this.onSelectChange(this.props.value);
        } else
        if (this.props.defaultSelectFirst) {
          this.onSelectChange(retdata[0].robotkey + '');
        }
      });
  }

  onSelectChange = (key) => {
    AppActions.setState({ robotkey: parseInt(key) });
    this.setState({ value: parseInt(key) });
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { robotList, value } = this.state;
    const width = this.props.width || '100%';
    return (
      <Select
        style={{ width }}
        {...this.props}
        onChange={this.onSelectChange}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          robotList?.map((item, index) => <Option value={item.robotkey + ''} key={item.robotkey + ''}>{item.name}</Option>)
        }
      </Select>
    );
  }
}

export default RobotSelect;
