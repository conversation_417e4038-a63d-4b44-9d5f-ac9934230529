// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Select } from 'antd';
import AppActions from 'actions/AppActions';
import { apiCall } from 'common/utils';

const Option = Select.Option;

class SKUSelect extends Reflux.Component {
  constructor (props) {
    super(props);
    this.state = {
      appid: this.props.appid,
      skuList: undefined,
    };
  }

  componentWillUpdate (nextProps, nextState) {
    // appid 有更新
    if (this.props.appid != nextProps.appid) {
      if (nextProps.appid == null) {
        this.setState({ confList: [] });
        return;
      }
      let data = {
        appid: nextProps.appid
      };
      apiCall('option/sku', 'GET', data)
        .then((retdata) => {
          this.setState({
            skuList: retdata
          });
          if (this.props.value) {
            this.onSelectChange(this.props.value);
          } else
          if (this.props.defaultSelectFirst) {
            this.onSelectChange(retdata[0].appid + '');
          }
        });
    }
  }

  componentDidMount () {
    let data = {
      appid: this.state.appid,
      skuid: this.props.skuid,
    };
    apiCall('option/sku', 'GET', data)
      .then((retdata) => {
        this.setState({
          skuList: retdata
        });
        if (this.props.value) {
          this.onSelectChange(this.props.value);
        } else
        if (this.props.defaultSelectFirst) {
          this.onSelectChange(retdata[0].appid + '');
        }
      });
  }

  onSelectChange = (key) => {
    AppActions.setState({ skuid: parseInt(key) });
    if (this.props.onChange) {
      this.props.onChange(key);
    }
  }

  render () {
    const { skuList, value } = this.state;
    const width = this.props.width || '100%';
    return (
      <Select
        style={{ width }}
        {...this.props}
        defaultValue={this.props.skuid ? this.props.skuid.toString() : ''}
        onChange={this.onSelectChange}
        showSearch
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        {
          skuList?.map((item, index) => <Option value={item.skuid + ''} key={item.skuid + ''}>{item.type}-{item.name}</Option>)
        }
      </Select>
    );
  }
}

export default SKUSelect;
