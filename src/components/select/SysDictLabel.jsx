import React from 'react';
import Reflux from 'reflux';
import { Spin } from 'antd';
import AppActions from '../../actions/AppActions';
import AppStore from '../../stores/AppStore';

class SysDictLabel extends Reflux.Component {

  constructor(props) {
    super(props);
    this.state = {
      value: props.value
    };
    this.stores = [AppStore];
  }

  componentWillReceiveProps (nextProps) {
    if (nextProps.dictkey != this.props.dictkey) {
      this.setState({ dictkey: nextProps.dictkey });
    }
  }

  componentWillMount () {
    super.componentWillMount();
    if (!this.state.g_sysdict)
      AppActions.getSysDictOptions();
  }

  render () {

    var dict = this.state.g_sysdict;


    if (dict) {
      if (dict[this.props.dataset]) {
        return (
          <div style={{ ...this.props.style }}>
            {(() => {
              var match = dict[this.props.dataset].find((item, index) => (item[0] == (this.props.dictkey + "")));
              if (match) {
                if (this.props.color) {
                  return <span style={{ color: match[2] }} >{match[1]}</span>
                } else {
                  return match[1];
                }
              }
              else {
                return "-"; // 未匹配，全部按照-显示
              }
            })()
            }
          </div>
        )
      } else {
        return (
          <div>字典数据集不存在</div>
        )
      }
    } else {
      return (<Spin />)
    }

  }
}

export default SysDictLabel;
