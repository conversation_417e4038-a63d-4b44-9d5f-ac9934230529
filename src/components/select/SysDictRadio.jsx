import React from 'react';
import Reflux from 'reflux';
import { Radio, Spin } from 'antd';
import AppActions from '../../actions/AppActions';
import AppStore from '../../stores/AppStore';

class SysDictRadio extends Reflux.Component {

  constructor(props) {
    super(props);
    this.state = {
      value: props.value
    };
    this.stores = [AppStore];
  }

  componentWillReceiveProps (nextProps) {
    if (nextProps.value != this.props.value) {
      this.setState({ value: nextProps.value });
    }
  }

  componentWillMount () {
    super.componentWillMount();
    if (!this.state.g_sysdict)
      AppActions.getSysDictOptions();
  }

  onSelectChange = (key) => {
    this.setState({ value: key });
    if (this.props.onChange)
      this.props.onChange(key);
  }

  render () {
    var dict = this.state.g_sysdict;

    if (dict) {
      if (dict[this.props.dataset]) {
        return (
          <Radio.Group
            value={this.state.value ? this.state.value : ""}
            mode={this.props.mode ? this.props.mode : null}
            style={{ width: '100%' }}
            onFocus={this.props.onFocus}
            onChange={this.onSelectChange}
            optionType="default"
            {...this.props}
          >
            {
              (() => {
                var arr = dict[this.props.dataset]
                if (this.props.range) {
                  //有限定范围
                  arr = dict[this.props.dataset].filter((item) => (this.props.range.includes(parseInt(item[0]))));
                }
                return arr.map((item, index) => (
                  <Radio.Button disabled={this.props.disabledKeys?.includes(item[0])} key={index} value={item[0]}>{item[1]}</Radio.Button>
                ))
              })()
            }
          </Radio.Group>
        )
      } else {
        return (
          <div>选项不存在</div>
        )
      }
    } else {
      return (<Spin />)
    }

  }
}

export default SysDictRadio;
