import React from 'react';
import Reflux from 'reflux';
import { Select, Spin } from 'antd';
import AppActions from '../../actions/AppActions';
import AppStore from '../../stores/AppStore';

const Option = Select.Option;

class SysDictSelect extends Reflux.Component {

  constructor(props) {
    super(props);
    this.state = {
      value: props.value
    };
    this.stores = [AppStore];
  }

  componentWillReceiveProps (nextProps) {
    if (nextProps.value != this.props.value) {
      this.setState({ value: nextProps.value });
    }
  }

  componentWillMount () {
    super.componentWillMount();
    if (!this.state.g_sysdict) { AppActions.getSysDictOptions(); }
  }

  onSelectChange = (key) => {
    this.setState({ value: key });
    if (this.props.onChange) { this.props.onChange(key); }
  }

  render () {
    let dict = this.state.g_sysdict;
    let defaultvalue = (this.props.mode == 'multiple') ? [] : '';
    if (dict) {
      if (dict[this.props.dataset]) {
        return (
          <Select
            allowClear
            // value={this.state.value ? this.state.value : defaultvalue}
            value={this.state.value}
            mode={this.props.mode ? this.props.mode : null}
            style={{ width: '100%' }}
            onFocus={this.props.onFocus}
            onChange={this.onSelectChange}
            showSearch
            showArrow
            maxTagCount="responsive"
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            {...this.props}
          >
            {
              (() => {
                let exclude = this.props.exclude || []
                let arr = dict[this.props.dataset].filter(item => !exclude.includes(item[0]));
                if (this.props.range) {
                  // 有限定范围
                  arr = dict[this.props.dataset].filter((item) => (this.props.range.includes(parseInt(item[0]))));
                }
                return arr.map((item) => (
                  <Option key={item[0]}>{item[1]}</Option>
                ));
              })()
            }
          </Select>
        );
      } else {
        return (
          <div>字典数据集不存在</div>
        );
      }
    } else {
      return (<Spin />);
    }

  }
}

export default SysDictSelect;
