import Cookie from "js-cookie"
import AppStore from "stores/AppStore"

const API_URLS = {
  "/inner_wibot": "/wibot/webapi",
  "/std/inner_wibot": "/std/wibot/webapi",
  "/": "/std/wibot/webapi", // 开发环境兜底
}

const ROUTER_PATHS = {
  "/inner_wibot": "/inner_wibot",
  "/std/inner_wibot": "/std/inner_wibot",
  "/": "/", // 开发环境兜底
}
const H5_PATHS = {
  "/std/inner_wibot": "/std/wibot", // 自己的
  "/inner_wibot/": "/wibot", // sz,zj
  "/": "", // 开发环境兜底
}

const locationOrigin = location.origin
const STATIC_PATH = {
  /**
   *
   * 我们自己的环境（测试、生产）
   * WEB端（模拟行内网）：/std/inner_wibot -> /std/wibot
   * OA端（互联网）：/std/wibot -> /std/wibot
   * H5员工端、H5客户端（互联网）：/std/wibot -> /std/wibot
   * 浙江工行
   * WEB端（行内网）：/inner_wibot -> /wibot
   * OA端（行内网）：/mydeskApp/wibot -> /mydeskApp/wibot
   * H5员工端、H5客户端（互联网）：/wibot -> /wibot
   * 深圳工行
   * WEB端（行内网）：/inner_wibot -> /wibot
   * OA端（行内网）：/icbc/eszbmp/wibot -> /icbc/eszbmp/wibot
   * H5员工端、H5客户端（互联网）：/open/wibot -> /open/wibot
   */
  "/std/inner_wibot": locationOrigin + "/std/wibot/",
  "/inner_wibot": locationOrigin + "/wibot/",
  "/": "https://test.wizone.work/std/wibot/", // 开发环境兜底
}
const PUBLIC_PATH = process.env.PUBLIC_PATH
const baseHref = locationOrigin + PUBLIC_PATH

/**
 * 格式化图片地址
 * @param src
 * @returns {string}
 */
export function formatURL(src = "") {
  if (typeof src !== "string") {
    console.trace("src not a string ")
    throw new Error("src must be a string")
  }
  // base64
  if (src.startsWith("data:image")) {
    return src
  }
  // 外部资源
  if (src.startsWith(locationOrigin) || src.startsWith("http")) {
    return src
  }
  const path = getValueByPathName(STATIC_PATH)
  // 拼接静态资源路径+图片的路径处理
  function _removeHostAndBase(url) {
    return url.replace(baseHref, "")
  }
  return path + _removeHostAndBase(src)
}

export function removeAbsoluteURL(node) {
  try {
    const dom = new DOMParser().parseFromString(node, "text/html").body
    _format(dom, "img")
    // _format(dom, 'video');
    _format(dom, "source")
    return dom.outerHTML
  } catch (e) {
    console.error(`[node 格式化失败]: `, node)
    return node
  }

  function _format(dom, tagName) {
    dom.querySelectorAll(tagName).forEach((media) => {
      media.src = "minio" + media.src.split("minio")[1]
    })
    return dom
  }
}

export function replaceOfEditorMediaURL(node, isRelativePath) {
  let dom = node
  if (typeof node === "string") {
    dom = new DOMParser().parseFromString(node, "text/html").body
  }
  if (dom) {
    _format(dom, "img")
    _format(dom, "source")
  }

  function _format(dom, tagName) {
    dom.querySelectorAll(tagName).forEach((media) => {
      let src = "minio" + media.src.split("minio")[1]
      media.src = formatURL(src)
    })
    return dom
  }
  return dom.outerHTML
}

function setBaseStaticFilePath() {
  function _media(element) {
    // 1. 处理已存在的图片
    const existingImages = document.querySelectorAll(element)
    existingImages.forEach((node) => {
      node.src = formatURL(node.src)
    })
    // 2. 监听 DOM 变化，处理动态添加的图片
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.tagName === element) {
            node.src = formatURL(node.src)
          } else if (node.querySelectorAll) {
            // 处理子节点中的图片
            node.querySelectorAll(element).forEach((node) => {
              node.src = formatURL(node.src)
            })
          }
        })
      })
    })
    observer.observe(document.body, { childList: true, subtree: true }) // 观察整个 body 及其子节点的变化
  }

  function _init() {
    _media("img")
    _media("video")
    _media("audio")
  }

  _init()
}

// setBaseStaticFilePath()

function getValueByPathName(data) {
  let path = location.pathname
  for (const apiKey in data) {
    if (path.startsWith(apiKey)) {
      return data[apiKey]
    }
  }
}

/**
 * 获取api地址
 * @returns {*}
 */
export function getApiUrl() {
  return getValueByPathName(API_URLS)
}

/**
 * 获取路由路径
 * @returns {*}
 */
export function getRouterPath() {
  return getValueByPathName(ROUTER_PATHS)
}

export function getSideBarBaseUrlOfH5() {
  const SideBarBaseUrlOfH5 = location.origin + getValueByPathName(H5_PATHS)
  return SideBarBaseUrlOfH5
}

export const constantMap = {
  system_ui: "system_ui",
  // 分流规则-分配规则-自定义分配
  routingRule_assignmentRule_customFlag:
    "routingRule_assignmentRule_customFlag",
  // 分流规则-工行客户标签
  routingRule_icbcCustomerTagFlag: "routingRule_icbcCustomerTagFlag",
  // (分流规则-自定义分配)
  routingRule_customRouting: "routingRule_customRouting",
  // 坐席工作台-知识随行
  agentSeat_knowledge: "agentSeat_knowledge",
  // 坐席工作台-结束对话-小结内容
  agentSeat_endSession_content: "agentSeat_endSession_content",
}
const moduleVersionMap = {
  [constantMap.system_ui]: {
    v1: {
      version: "v1",
      system_title: "企微支持系统后台",
      // true 显示系统-导航菜单-私域套件
      system_navMenu_wecomFlag: true,
      system_companyFlag: "企业：",
      system_breadcrumb_wecomFlag: true,
      agentSeat_title: "坐席",
      agentSeat_stateName: ["签入", "签出"],
      marketingTabs: [1, 2, 3, 4],
      avatar: false,
      excludeEmoji: false,
      workOrderTitle: "工单",
      resourceCenter_productFlag: true, // 素材中心-产品显示
      rule_oaFlag: false,
    },
    v2: {
      version: "v2",
      system_title: "客户运营支持系统",
      system_navMenu_wecomFlag: false,
      system_companyFlag: "",
      system_breadcrumb_wecomFlag: false,
      // 服务列表 坐席工作中 系统消息 全局报错 '坐席' 替换成'客服'
      agentSeat_title: "客服",
      agentSeat_stateName: ["在线", "离线"],
      marketingTabs: [2],
      avatar: true,
      excludeEmoji: true,
      workOrderTitle: "事件",
      resourceCenter_productFlag: false, // 素材中心-产品隐藏
      rule_oaFlag: true,
    },
  },
  [constantMap.routingRule_assignmentRule_customFlag]: {
    v1: false,
    v2: true,
  },
  [constantMap.routingRule_icbcCustomerTagFlag]: {
    v1: true,
    v2: false,
  },
  [constantMap.routingRule_customRouting]: {
    v1: false,
    v2: true,
  },
  [constantMap.agentSeat_knowledge]: {
    v1: false,
    v2: true,
  },
  [constantMap.agentSeat_endSession_content]: {
    v1: false,
    v2: true,
  },
  // ....
}
let isInit = false
function getModuleVersion(key) {
  if (!moduleVersionMap[key]) {
    throw new Error(`Module ${key} not found in moduleVersionMap`)
  }
  const defaultVersion = "v1"
  const userVersion =
    AppStore.state.User.moduleVersionMap[key] || defaultVersion
  const versionData = moduleVersionMap[key][userVersion]
  if (!isInit) {
    isInit = true
    console.log(
      "配置版本:",
      AppStore.state.User.moduleVersionMap[key],
      "当前配置版本: ",
      userVersion
    )
  }
  if (versionData === undefined) {
    Cookie.remove("weebot_cloud_token")
    throw new Error(`Version ${userVersion} for module ${key} not found`)
  }

  return versionData
}

/**
 * 获取模块版本数据
 * @param key
 * @returns {string} v1 | v2
 */
export const getVersionOfKey = (key) => {
  if (!constantMap[key]) {
    throw new Error(`Constant ${key} not found in constantMap`)
  }
  return AppStore.state.User.moduleVersionMap[key] || moduleVersionMap["v1"]
}

export function isV1() {
  return getVersionOfKey("system_ui") === "v1"
}

export function isV2() {
  return !isV1()
}

/**
 * 获取模块版本数据
 * @type {{}}
 */
export const versionFnMap = Object.keys(constantMap).reduce((acc, key) => {
  acc[key] = () => getModuleVersion(key)
  return acc
}, {})

// versionFnMap.system_ui().system_title();
// versionFnMap.routingRule_assignmentRule_customFlag();
