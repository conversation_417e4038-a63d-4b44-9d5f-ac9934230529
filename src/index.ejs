<!doctype html>
<html>

<head>
  <meta charset="utf-8" />
  <title>企微支持系统后台</title>
  <base href="<%= process.env.PUBLIC_PATH %>">
  <link rel="shortcut icon" href="favicon.ico" />
  <link rel="bookmark" href="favicon.ico" type="image/x-icon" 　 />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="viewport" content="width=1024, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name=referrer content=no-referrer />
  <link rel="stylesheet/less" href="theme/index.less">
  <script src="ImgCompress/index.min.js"></script>
  <style type="text/css">
    .spinner {
      margin: 0px auto 0;
      width: 150px;
      text-align: center;
    }

    .spinner>div {
      width: 30px;
      height: 30px;


      border-radius: 100%;
      display: inline-block;
      -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
      animation: bouncedelay 1.4s infinite ease-in-out;
      /* Prevent first frame from flickering when animation starts */
      -webkit-animation-fill-mode: both;
      animation-fill-mode: both;
    }

    .spinner .bounce1 {
      -webkit-animation-delay: -0.32s;
      animation-delay: -0.32s;
    }

    .spinner .bounce2 {
      -webkit-animation-delay: -0.16s;
      animation-delay: -0.16s;
    }

    @-webkit-keyframes bouncedelay {

      0%,
      80%,
      100% {
        -webkit-transform: scale(0.0)
      }

      40% {
        -webkit-transform: scale(1.0)
      }
    }

    @keyframes bouncedelay {

      0%,
      80%,
      100% {
        transform: scale(0.0);
        -webkit-transform: scale(0.0);
      }

      40% {
        transform: scale(1.0);
        -webkit-transform: scale(1.0);
      }
    }
  </style>
</head>

<body>
  <div id="loadingDiv"
    style="position:fixed;font-size:40px;text-align:center;left:50%;top:50%;margin-left:-75px;margin-top: -75px;">
    <div class="spinner">
      <div class="bounce1"></div>
      <div class="bounce2"></div>
      <div class="bounce3"></div>
    </div>
    <script type="text/javascript">
      document.onreadystatechange = completeLoading;

      function completeLoading () {
        if (document.readyState == "complete") {
          var loadingMask = document.getElementById('loadingDiv');
          loadingMask.parentNode.removeChild(loadingMask);
        }
      }
    </script>
  </div>

  <div id="root"></div>

  <!-- 使用CDN加速的JS文件，配置在webpack.config.js下 -->
  <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
    <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
</body>

</html>