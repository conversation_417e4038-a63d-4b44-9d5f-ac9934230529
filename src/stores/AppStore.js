import Reflux from "reflux"
import { apiCall } from "common/utils"
import <PERSON><PERSON> from "js-cookie"
import history from "common/history"
import { message, notification } from "antd"

// 数据流
import AppActions from "actions/AppActions"

// 目前主要用来保存errormsg等全局状态
class AppStore extends Reflux.Store {
  constructor() {
    super()
    this.listenables = AppActions
  }

  onSetState(state) {
    this.setState(state)
  }

  onClearError() {
    this.setState({ errmsg: null })
  }

  onThrowErrMsg(errmsg) {
    this.setState({ errmsg })
  }

  onRegister(data, callback) {
    apiCall("register", "POST", data)
      .then((retdata) => {
        Cookie.set("weebot_cloud_token", retdata.accesstoken)

        const User = {
          username: retdata.name,
        }

        this.setState({
          User: User,
          loginFailed: false,
          loginErrorhint: "",
        })

        // 实现登陆后自动跳转到先前退出登陆的页面
        const lastLocation = sessionStorage.getItem("weebot_web_lastLocation")
        const lastUser = sessionStorage.getItem("weebot_web_username")
        sessionStorage.setItem("weebot_web_username", data.name)
        if (lastLocation && lastUser == data.name) {
          sessionStorage.removeItem("weebot_web_lastLocation")
          history.push(lastLocation)
        } else {
          sessionStorage.removeItem("weebot_web_lastLocation")
          history.push("/")
        }
      })
      .catch((result) => {
        callback?.()
        if (result.retcode > 0) {
          this.setState({
            loginFailed: true,
            loginErrorhint: result.msg,
          })
        } else {
          this.setState({
            loginFailed: true,
            loginErrorhint: "服务器内部错误",
          })
        }
      })
  }

  companyRegister(data, callback) {
    apiCall("comreg", "POST", data).then((retdata) => {
      this.onSetState({
        showCompanyRegisterHint: true,
      })
    })
  }

  onLogin(data, callback) {
    apiCall("/login", "POST", data)
      .then((retdata) => {
        if (retdata.msg) {
          notification.success({
            placement: "top",
            message: "登录成功",
            description: retdata.msg,
          })
        }

        Cookie.set("weebot_cloud_token", retdata.accessToken)
        const User = {
          ...retdata,
          menus: retdata.menuList,
          moduleVersionMap: retdata.moduleVersionMap || {},
        }

        this.setState({
          User: User,
          loginFailed: false,
          loginErrorhint: "",
        })

        // 实现登陆后自动跳转到先前退出登陆的页面
        let lastLocation = sessionStorage.getItem(
          "weebot_cloud_web_lastLocation"
        )
        if (lastLocation == "/login") {
          lastLocation = null
        }
        if (lastLocation) {
          history.push(lastLocation)
        } else {
          history.push("/")
        }
        sessionStorage.removeItem("weebot_cloud_web_lastLocation")
        sessionStorage.setItem("weebot_cloud_web_username", retdata.userName)
      })
      .catch((result) => {
        callback?.()
        if (result.retcode > 0) {
          this.setState({
            loginFailed: true,
            loginErrorhint: result.msg,
          })
          message.error(result.msg)
        } else {
          this.setState({
            loginFailed: true,
            loginErrorhint: "服务器内部错误",
          })
        }
      })
  }

  onLogout() {
    // 清理state，清理后端缓存，清理session，清理cookie
    this.state = {}
    // this.setState({User:undefined});
    apiCall("logout", "GET")
    sessionStorage.clear()
    Cookie.remove("weebot_cloud_token")
    history.push("/login")
  }

  onVerifyLogin(callback) {
    if (!Cookie.get("weebot_cloud_token")) {
      history.push("/login")
      return false
    }
    let time = new Date().getTime()
    if (this.state.User && time - this.state._vts < 10000) {
      this.state._vts = time
      return // 间隔少于10秒内的verify，忽略
    }
    this.state._vts = time
    let data = {}
    apiCall("/verifytoken", "POST", data)
      .then(async (retdata) => {
        if (this.state.User == undefined) {
          const User = {
            ...retdata,
            menus: retdata.menuList,
            moduleVersionMap: retdata.moduleVersionMap || {},
          }
          this.setState({
            User: User,
          })
        }
        sessionStorage.setItem("weebot_cloud_web_username", retdata.userName)
        callback?.()
      })
      .then(async () => {
        if (!this.state.isEnableTTS) {
          setTimeout(async () => {
            const isEnableTTS = await apiCall(
              "/agentSeat/msgVoiceText/isEnable",
              "GET"
            )
            this.setState({ isEnableTTS })
          }, 2000)
        }
      })
      .catch((result) => {
        history.push("/login")
      })
  }

  onGetSysDictOptions(data) {
    if (!Cookie.get("weebot_cloud_token")) {
      return false
    }
    if (!this.state.g_sysdict || data?.force) {
      apiCall("/sysdict/options", "GET").then((retdata) => {
        this.setState({ g_sysdict: retdata })
      })
    }
  }

  onGetGeneralOptions(schema, params, callback) {
    apiCall(schema + "/option", "GET", params).then((retdata) => {
      const options = this.state.g_options || {}
      options[schema] = retdata
      this.setState({ g_options: options })
      if (retdata.length) {
        callback?.(retdata)
      }
    })
  }

  onSetCollapsed(collapsed) {
    this.setState({
      collapsed: collapsed,
    })
  }
}

export default AppStore
