import Reflux from 'reflux';

class BaseStore extends Reflux.Store {
  // 全局状态的几个接口
  onRefreshAll() {
    this.setState({});
  }

  onSetState(state) {
    this.setState(state);
  }

  onClearError() {
    this.setState({ errmsg: null });
  }

  // 具体调用规则以RestOPCall的方式为标准
  abortApi2Call() {
    if (this.state.api2request) {
      this.state.api2request.abort();
    }
  }
}

export default BaseStore;
