/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/16 15:57
 * @LastEditTime: 2025/08/05 15:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/theme/index.less
 * @Description: '全局主题样式'
 */
@import "./variables.less";

html {
  --antd-wave-shadow-color: @themeColor;
}

#root {

  //顶部栏
  .ant-layout-header {
    background-color: @header-bgcolor  !important;
    color: @header-color;

    .ant-menu {
      background: transparent;

      .ant-menu-item a {
        color: @header-color;

        &:hover {
          color: @header-hover-color;
        }
      }

      .ant-menu-item:hover {
        // background-color: @header-hover-bgcolor;    
        background-color: #00000010;
        color: @aside-menu-color;

        &::after {
          border-bottom: unset;
        }
      }

      .ant-menu-item {
        &::after {
          border-bottom: none;
        }
      }

      .ant-menu-item-selected {
        // background-color: @header-hover-bgcolor;
        background-color: #00000010;

        &::after {
          border-bottom: unset;
        }
      }

      .ant-menu-submenu::after {
        border-bottom: unset;
      }

      .ant-menu-submenu-selected {
        color: @header-color ;
      }

      .ant-menu-submenu-title {
        color: @header-color  !important;

        &:hover {
          color: @header-color  !important;
        }
      }
    }

    .ant-menu-submenu::after {
      border-bottom: unset;
    }
  }
}

//侧栏
.ant-menu-root.module-menu {
  background-color: @aside-bgcolor;
}

.ant-menu-item-selected {
  color: @aside-menu-color;
}

.ant-menu-item:hover {
  color: @aside-menu-color;
}

.ant-menu-submenu-title:hover {
  color: @aside-menu-color;
}

.ant-menu-submenu-selected {
  color: @aside-menu-color;
  .ant-menu-submenu-arrow{
    color: @aside-menu-color;

  }
}

.ant-menu-sub.ant-menu-inline {
  background-color: @aside-menuInline-bgcolor;
}

.ant-menu-inline .ant-menu-item::after {
  border-right-color: @aside-menuAfter-color;
}

.ant-menu-submenu:hover>.ant-menu-submenu-title>.ant-menu-submenu-expand-icon,
.ant-menu-submenu:hover>.ant-menu-submenu-title>.ant-menu-submenu-arrow {
  color: @aside-menuArrow-color;
}


// menu
.ant-menu {
  .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background-color: rgba(@aside-menu-color, 0.1);
  }

  .ant-menu-item:active,
  .ant-menu-submenu-title:active {
    background-color: rgba(@aside-menu-color, 0.1);
  }
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: rgba(@aside-menu-color, 0.1);
}

.ant-menu-submenu-popup>.ant-menu {
  .ant-menu-item a:hover {
    color: @header-bgcolor;
  }

  .ant-menu-item-selected a,
  .ant-menu-item-selected a:hover {
    color: @header-bgcolor;
  }

  .ant-menu-item-selected {
    color: @header-bgcolor;
  }

  .ant-menu-item:hover {
    color: @header-bgcolor;
  }
}

.ant-popover-content {
  .ant-menu-item:hover {
    color: @header-bgcolor;
  }
}

.ant-menu-light .ant-menu-item:hover,
.ant-menu-light .ant-menu-item-active,
.ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
.ant-menu-light .ant-menu-submenu-active,
.ant-menu-light .ant-menu-submenu-title:hover {
  color: @header-bgcolor;
}

//按钮
//   .ant-btn:hover, .ant-btn:focus{
//     color: @button-color;
//   }
//   .ant-btn:active{
//     color: @button-color;
//   }
//  .ant-btn-primary{
//   background:@button-color;
// }
// .ant-btn-primary:hover, .ant-btn-primary:focus{
//   color: @button-fontColor !important;
// }

//顶部栏退出登录
.ant-menu-item:hover {
  color: @aside-menu-color  !important;
}

//按钮
.ant-btn:hover,
.ant-btn:focus {
  color: @button-color;
  border-color: @button-color;
}

.ant-btn-primary {
  background: @button-color;
  border-color: @button-color;

  &:hover,
  &:focus {
    color: @button-fontColor  !important;
    background: rgba(@button-color, .8);
  }
}

.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover,
.ant-btn-primary[disabled]:focus,
.ant-btn-primary[disabled]:active {
  color: @button-disable-color  !important;
  border-color: @button-disable-bordcolor  !important;
  background: @button-disable-bgcolor  !important;
}

.ant-btn-link:hover,
.ant-btn-link:focus,
.ant-btn-link:active {
  border-color: none;
  // color: unset;
}

.ant-btn-link {
  color: @button-color;
  border: unset;

  &:hover {
    border: unset;
  }
}

//pagination
.ant-pagination-item-active {
  border-color: @pagination-color;
  color: @pagination-color;
}

.ant-pagination-item-active:focus-visible a,
.ant-pagination-item-active:hover a {
  color: @pagination-color;
}

.ant-pagination-item:focus-visible a,
.ant-pagination-item:hover a {
  color: @pagination-color;
}

.ant-pagination-item:focus-visible,
.ant-pagination-item:hover {
  border-color: @pagination-color;
}

.ant-pagination-prev:focus-visible .ant-pagination-item-link,
.ant-pagination-next:focus-visible .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border-color: @pagination-color;
  color: @pagination-color;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: @pagination-color;
}

.ant-pagination-options-quick-jumper input:hover {
  border-color: @pagination-color;
}

.ant-pagination-options-quick-jumper input:focus,
.ant-pagination-options-quick-jumper input-focused {
  border-color: @pagination-color;
  box-shadow: 0 0 0 2px rgba(@pagination-color, 0.2);
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  // background: @aside-menuSelect-bgcolor;
  background: rgba(@pagination-color, 0.2);
}

.ant-select-focused:not(.ant-select-disabled) .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: @pagination-color;
}

.ant-pagination-item-active a {
  color: @pagination-color;
}

.ant-pagination-disabled .ant-pagination-item-link,
.ant-pagination-disabled:hover .ant-pagination-item-link,
.ant-pagination-disabled:focus-visible .ant-pagination-item-link {
  color: rgba(0, 0, 0, 0.25) !important;
  border-color: #d9d9d9 !important;
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: @pagination-color;
}

//input
.ant-input:hover,
.ant-input:focus,
.ant-input-focused {
  border-color: @input-borderColor;
  // box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-input-affix-wrapper:hover {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-input-number:hover {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-input-number:focus,
.ant-input-number-focused {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-input-number-handler:hover .ant-input-number-handler-up-inner,
.ant-input-number-handler:hover .ant-input-number-handler-down-inner {
  color: @input-borderColor;
}

.ant-input-search .ant-input:hover,
.ant-input-search .ant-input:focus {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-select-tree-checkbox-inner::after {
  background-color: @input-borderColor;
}

.ant-select-focused {
  border-color: @input-borderColor;
  box-shadow: unset;
}

.ant-cascader-picker-label:hover+.ant-cascader-input:not(.ant-cascader-picker-disabled .ant-cascader-picker-label:hover + .ant-cascader-input) {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-cascader-picker:focus .ant-cascader-input {
  border-color: @input-borderColor;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
  border-color: @input-borderColor;
}

.ant-input[disabled],
.ant-input[disabled]:hover {
  border-color: @input-hoverColor  !important;
  box-shadow: unset !important;
}

//upload
.ant-upload.ant-upload-select-picture-card:hover {
  border-color: @upload-color;
}

//checkbox
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: @checkbox-color;
  border-color: @checkbox-color;
}

.ant-checkbox-checked::after {
  border-color: @checkbox-color;
}

.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: @checkbox-color;
  border-color: @checkbox-color;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus+.ant-checkbox-inner {
  border-color: @checkbox-color;
}

//radio
.ant-radio-wrapper:hover .ant-radio,
.ant-radio:hover .ant-radio-inner,
.ant-radio-input:focus+.ant-radio-inner {
  border-color: @radio-color;
}

.ant-radio-checked .ant-radio-inner {
  border-color: @radio-color;
}

.ant-radio-inner::after {
  background-color: @radio-color;
}

.ant-radio-checked::after {
  border-color: @radio-color;
}

.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: @radio-color;
  border-color: @radio-color;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: @radio-color;
}

.ant-radio-button-wrapper:hover {
  color: @radio-color;
}

.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  color: #fff !important;
}

.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
  box-shadow: 0 0 0 3px rgba(@radio-color, 0.12)
}

//table 
.ant-table-tbody>tr.ant-table-row-selected:hover>td {
  background: @table-bgcolor;
}

.ant-table-tbody>tr.ant-table-row-selected>td {
  background: @table-bgcolor;
}

.ant-table-cell {
  a {
    color: @table-Acolor;

    &:hover {
      color: @table-Acolor;
    }
  }
}

.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
  color: @table-sorterColor;
}

.ant-table-filter-trigger.active {
  color: @themeColor;
}

tr.drop-over-downward td {
  border-bottom: 2px dashed @themeColor  !important;
}

tr.drop-over-upward td {
  border-top: 2px dashed @themeColor  !important;
}

//a链接
// a {
//   color: @aColor;

//   &:hover {
//     color: @aColor;
//   }
// }

//date picker
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
  background: @date-color;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: @date-color;
  border-right-width: 1px !important;
}

.ant-picker-focused {
  border-right-width: 1px !important;
  outline: 0;
  border-color: @date-color;
  box-shadow: 0 0 0 2px rgba(@input-borderColor, .2);
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
  border-color: @date-color;
}

.ant-picker-range .ant-picker-active-bar {
  background: @date-color;
}

.ant-picker-today-btn {
  color: @date-color;
}

.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {
  border-top: 1px dashed rgba(@date-color, 0.8);
  border-bottom: 1px dashed rgba(@date-color, 0.8);
}

tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child::after,
tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after,
.ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(.ant-picker-cell-range-hover-edge-start-near-range)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after {
  border-left: 1px dashed rgba(@date-color, 0.8);
}

tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after,
tr>.ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
.ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(.ant-picker-cell-range-hover-edge-end-near-range)::after,
.ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after {
  border-right: 1px dashed rgba(@date-color, 0.8);
}

.ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  background: rgba(@date-color, 0.2);
}

.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
  background: rgba(@date-color, 0.2);
}

.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner::after,
.ant-picker-date-panel .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner::after {
  background: rgba(@date-color, 0.5);
}

.ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before,
.ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before,
.ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before,
.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before,
.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before,
.ant-picker-panel> :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before,
.ant-picker-panel> :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before {
  background: rgba(@date-color, 0.6);
}

.ant-picker-footer {
  a {
    color: @aFontcolor;

    &:hover {
      color: rgba(@aFontcolor, 0.8);
    }
  }
}

.ant-picker-header-view button:hover {
  color: @date-color  !important;
}

.ant-picker-time-panel-column>li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background: rgba(@date-color, 0.4) !important;
}

//tree
.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: rgba(@tree-bgcolor, 0.4);
}

.ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
  background-color: @tree-checkbox;
  border-color: @tree-checkbox;
}

.ant-select-tree .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  background-color: rgba(@tree-bgcolor, 0.4) !important;
}

.ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner,
.ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner,
.ant-select-tree-checkbox-input:focus+.ant-select-tree-checkbox-inner {
  border-color: @themeColor;
}

.ant-select-tree-checkbox:hover::after,
.ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox::after {
  border: 1px solid @themeColor;
}

.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  background-color: @tree-checkbox;
  border-color: @tree-checkbox;
}

.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
  background-color: @themeColor;
}

.ant-tree-checkbox-checked::after {
  border: 1px solid @themeColor;
}

.ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox:hover .ant-tree-checkbox-inner,
.ant-tree-checkbox-input:focus+.ant-tree-checkbox-inner {
  border-color: @themeColor;
}

//switch
.ant-switch-checked {
  background-color: @switch-color;
}

// [ant-click-animating-without-extra-node=true]::after,
// .ant-click-animating-node {
//   box-shadow: 0 0 0 0 rgba(@switch-color, 0.2);
// }

//loding
.ant-spin-dot-item {
  background-color: @loding-dotColor;
}

//设置颜色icon
// .setIconBox {
//   position: absolute;
//   top: 300px;
//   right: 16px;
//   padding: 10px;
//   cursor: pointer;
//   background: @themeColor;
//   border-radius: 4px 0 0 4px;
//   z-index: 999;
//   display: flex;

//   .set-color-icon {
//     font-size: 20px;
//     color: #fff;
//   }
// }

.theme-color {
  float: left;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  margin-right: 5px;
  cursor: pointer;
}

//客户台账tab切换
.tab-title {
  .isTabActive {
    color: @themeColor;
  }

  div {
    &:hover {
      color: @themeColor;
    }
  }
}

.label-history {
  .label-history-title {
    .check-more {
      &:hover {
        color: @themeColor;
      }
    }
  }
}

.points-more {
  .check-more {
    &:hover {
      color: @themeColor;
    }
  }
}

//upload组件取消上传的图片
.cancel-upload-icon {
  position: absolute;
  right: -10px;
  top: -10px;
  font-size: 23px;
  color: #fff !important;
  background: #bfbfbf;
  border-radius: 50%;

  &:hover {
    background-color: @themeColor;
  }
}

// ant-dropdown-menu
.ant-dropdown-menu-item-selected,
.ant-dropdown-menu-submenu-title-selected {
  color: @themeColor;
  background-color: rgba(@themeColor, 0.2);
}

// ant-tabs 
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: @tab-color;
}

.ant-tabs-tab:hover {
  color: @tab-color;
}

.ant-tabs-ink-bar {
  background: @tab-color;
}

.ant-tabs-tab-btn:focus,
.ant-tabs-tab-remove:focus,
.ant-tabs-tab-btn:active,
.ant-tabs-tab-remove:active {
  color: @tab-color;
}

//登录页申请试用
.a-apply-try {
  display: block;
  margin-bottom: 15px;
  color: @tab-color;

  &:hover {
    color: @tab-color;
  }
}

.ant-cascader-menus {

  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
    background-color: rgba(@themeColor, 0.2);
  }

  .ant-cascader-checkbox-checked .ant-cascader-checkbox-inner {
    background-color: @themeColor;
    border-color: @themeColor;
  }

  .ant-cascader-checkbox-wrapper:hover .ant-cascader-checkbox-inner,
  .ant-cascader-checkbox:hover .ant-cascader-checkbox-inner,
  .ant-cascader-checkbox-input:focus+.ant-cascader-checkbox-inner {
    border-color: @themeColor;
  }

  .ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner::after {
    background-color: @themeColor;
  }

  .ant-cascader-checkbox-checked::after {
    border: 1px solid @themeColor;
  }
}

// .table-header {
//   background: @themeColor;
// }

.Employee-TransferModal-Container {
  .ant-modal-body {
    .modal-tree-wrap {
      .tree-right-scroll-box {
        .ant-tree-title-nodeData {
          &.selected {
            background-color: rgba(@tree-bgcolor, 0.4);
          }
        }
      }
    }
  }
}

// 表单规则
.FormRules-Container {
  .topic-list {
    .item {
      .item-option {
        &.selected {
          color: #fff;
          background: @themeColor;
        }
      }
    }
  }
}

// Steps步骤条
// .ant-steps {
//   .ant-steps-item {
//     .ant-steps-item-icon {
//       background: @themeColor;
//       border-color: @themeColor;
//     }
//   }
// }

.ant-steps-item-process>.ant-steps-item-container>.ant-steps-item-icon {
  background: @themeColor;
  border-color: @themeColor;
}

.ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process)>.ant-steps-item-container[role='button']:hover .ant-steps-item-icon,
.ant-steps .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container[role='button']:hover .ant-steps-item-title,
.ant-steps .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container[role='button']:hover .ant-steps-item-subtitle,
.ant-steps .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container[role='button']:hover .ant-steps-item-description,
.ant-steps .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process)>.ant-steps-item-container[role='button']:hover .ant-steps-item-icon .ant-steps-icon {
  color: @themeColor;
  border-color: @themeColor;
}

//Paragraph复制
.ant-typography-copy {
  color: @themeColor;
}

// 时间轴
.ant-timeline {
  .ant-timeline-item-head {
    color: @themeColor;
    border-color: @themeColor;
  }
}

// 素材中心
.MaterialListForm .msgList .template .title {
  background: @themeColor;
}

.tagClose {
  &:hover {
    color: @themeColor;
  }
}

// 收件箱
.ConvenientReply {

  // 客户详情
  .Inbox-UserInfo {
    .wi-icon-info {
      .icon {
        &:hover {
          svg {
            color: @themeColor;
          }
        }
      }
    }
  }

  // 排序按钮
  .globalRow .wi-chat-sidebar .sidebar-icon :hover {
    color: @themeColor;
  }

  // 结束会话
  .globalRow .wi-chat-content .wi-footer .wi-operate-box .wi-operate-follow {
    color: @themeColor;
  }

  // 当前会话
  .globalRow .wi-chat-content .wi-footer .wiFooter-top .wiFooter-top-right {
    color: @themeColor;
  }

  // 知识随行
  .Inbox-Knowledge {
    .question-box {
      background-color: rgba(@themeColor, 0.2);

      .ellipsis {
        color: @themeColor;
      }
    }

    .ant-tabs {
      .ant-tabs-tabpane {
        .referto-box {
          .copy {
            color: @themeColor;
          }
        }
      }
    }
  }
}

// 坐席模块
.AgentSeat-Container {
  .AgentSeat-UserInfo {
    .icon {
      &:hover {
        svg {
          color: @themeColor;
        }
      }
    }
  }

  .AgentSeat-History {
    .anticon {
      &:hover {
        color: @themeColor;
      }
    }
  }

  .AgentSeat-Knowledge {
    .question-box {
      background-color: rgba(@themeColor, 0.2);

      .ellipsis {
        color: @themeColor;
      }
    }

    .ant-tabs {
      .ant-tabs-tabpane {
        .referto-box {
          .copy {
            color: @themeColor;
          }
        }
      }
    }
  }
}

.AgentSeat-CollectMessageModal {
  .anticon {
    &:hover {
      color: @themeColor;
    }
  }
}

// 统计数值
.WibotStatistic-Container {
  .ant-card {
    &.selected {
      border: 1px solid @themeColor;
    }
  }
}