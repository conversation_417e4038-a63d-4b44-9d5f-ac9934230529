/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/07/12 13:52
 * @LastEditTime: 2022/08/02 17:35
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\theme\variables.less
 * @Description: '全局主题样式'
 */

@themeColor: #5d8dd4 !important;

//header
@header-bgcolor: @themeColor;
@header-color: #ffffff;
@header-hover-color: #ffffff;
@header-hover-bgcolor: #4977bb;

//aside
@aside-bgcolor: #fff;
@aside-menu-color: @themeColor;
@aside-menuInline-bgcolor: #fff !important;
@aside-menuSelect-bgcolor: #e2ebf7;
@aside-menuAfter-color: @themeColor;
@aside-menuArrow-color: @themeColor;

//button
@button-color: @themeColor;
@button-fontColor: #ffffff;
@button-disable-color: rgba(0, 0, 0, 0.25);
@button-disable-bgcolor: #f5f5f5;
@button-disable-bordcolor: #d9d9d9;

//pagination
@pagination-color: @themeColor;

//input
@input-borderColor: @themeColor;
@input-hoverColor: #d9d9d9;

//checkbox
@checkbox-color: @themeColor;

//table
@table-bgcolor: #e2ebf7;
@table-Acolor: @themeColor;
@table-sorterColor: @themeColor;

//a
//  @aColor: #ffffff;
@aFontcolor: @themeColor;

//date
@date-color: @themeColor;
@date-rangeColor: #e2ebf7;

//tree
@tree-bgcolor: @themeColor;
@tree-checkbox: @themeColor;

//switch
@switch-color: @themeColor;

//radio
@radio-color: @themeColor;

//loding
@loding-dotColor: @themeColor;

//upload
@upload-color: @themeColor;

//tab
@tab-color: @themeColor;