/**
 * @description 递归查找数组
 * @param arr
 * @param label
 * @param key
 * @returns {undefined|any}
 */
export function findWithDeep (arr = [], label = '', key = '') {
  if (!Array.isArray(arr)) return undefined;

  for (const item of arr) {
    if (item[label] === key) {
      return item;
    }

    if (item.children && Array.isArray(item.children)) {
      const result = findWithDeep(item.children, label, key);
      if (result) {
        return result;
      }
    }
  }

  return undefined;
}
