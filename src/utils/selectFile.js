// 创建并调用文件选择框，支持动态传入属性
export function openFileSelector(options = {}) {
    return new Promise((resolve, reject) => {
        // 创建一个隐藏的 <input type="file"> 元素
        const input = document.createElement('input');
        input.type = 'file'; // 设置为文件选择框
        input.style.display = 'none'; // 隐藏元素

        // 动态设置传入的属性
        if (options.accept) {
            input.accept = options.accept; // 设置文件类型过滤
        }
        if (options.multiple) {
            input.multiple = true; // 允许多选
        }
        if (options.capture) {
            input.capture = options.capture; // 设置 capture 属性（如摄像头/麦克风）
        }

        // 监听文件选择事件
        input.addEventListener('change', (event) => {
            const files = event.target.files; // 获取用户选择的文件列表
            if (files.length > 0) {
                resolve(files); // 返回文件列表
            } else {
                reject(new Error('No file selected'));
            }
        });

        // 将元素添加到 DOM 中（为了触发文件选择框）
        document.body.appendChild(input);

        // 模拟点击以打开文件选择框
        input.click();

        // 移除元素以清理 DOM
        input.addEventListener('click', () => {
            setTimeout(() => {
                document.body.removeChild(input);
            }, 0);
        });
    });
}
