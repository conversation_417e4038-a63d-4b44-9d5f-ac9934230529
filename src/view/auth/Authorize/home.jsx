/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/11/12 10:06
 * @LastEditTime: 2024/11/12 14:21
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/auth/Authorize/home.jsx
 * @Description: '授权码登陆'
 */

import React from 'react';
import Reflux from 'reflux';
import { qs2obj } from 'common/object';
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';

class Authorize extends Reflux.Component {
  constructor(props) {
    super(props);
    this.store = AppStore;
    this.state = {};
  }

  componentDidMount () {
    const params = qs2obj(this.props.location.search);
    if (JSON.stringify(params) === '{}' || !params.code) {
      this.props.history.replace("/login");
      return false;
    }
    const { redirect, code } = params;
    sessionStorage.setItem('weebot_cloud_web_lastLocation', redirect);
    const data = {
      type: "ICBC_SZ_CODE",
      code: code,
    }
    AppActions.login(data, () => { });
  }

  render () {
    return (
      <div></div>
    )
  }
}

export default Authorize;
