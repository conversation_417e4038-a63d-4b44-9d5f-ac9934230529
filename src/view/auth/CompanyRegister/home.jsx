// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Card, Form, Input, Button, Alert, Checkbox, Result } from 'antd';

import history from 'common/history';

// 数据流
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';

const FormItem = Form.Item;

import './home.less';
import logo from 'images/logo.png';

class CompanyRegister extends Reflux.Component {

  formRef = React.createRef();
  constructor (props) {
    super(props);
    this.store = AppStore;
    this.state = {
      regFailed: false,
      submitting: false,
      showCompanyRegisterHint: false
    };
  }

  onBizClassChange = (list) => {

  };

  render () {
    const { submitting, regFailed, regErrorhint, showCompanyRegisterHint } = this.state;

    const handleSubmit = async (values) => {
      this.setState({
        loginFailed: false,
      });
      await this.formRef.current.validateFields().then((formData) => {
        this.setState({
          submitting: true
        });
        const callback = () => {
          this.setState({
            submitting: false
          });
        };
        const data = {
          type: 2, // 个人用户
          name: formData.name,
          site: formData.site,
          bizclass: formData.bizclass,
          contact: formData.contact,
          mobile: formData.mobile
        };

        AppActions.companyRegister(data, callback);
      })
        .catch((errorInfo) => {
          console.log('errorInfo', errorInfo);
        });
    };

    const plainOptions = ['银行保险', '电商电销', '企业RPA', '其它'];

    return (
      <div id="companyregister-container">
        <div className="background"></div>

        {(!showCompanyRegisterHint)
          ? <Card>
            <div className="login-logo">
              <img src={logo} />
            </div>

            <div className="login-title">产品试用信息登记</div>

            <Form onFinish={handleSubmit} className="auth-login-form" ref={this.formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>

              <FormItem label="公司名称" name="name" rules={[{ required: true, message: '请输入贵司名称' }]} labelCol={{ span: 6 }}
                wrapperCol={{ span: 16 }}>
                <Input size="large" prefix={<></>} placeholder="请输入贵司名称" />
              </FormItem>

              <FormItem label="官网地址" name="site" >
                <Input size="large" prefix={<></>} placeholder="请输入贵司官网地址，方便我们提前了解您们的产品" />
              </FormItem>

              <FormItem label="业务范围" name="bizclass" rules={[{ required: true, message: '请选择贵司业务范围，以方便我们提供方案模板' }]}
              >
                <Checkbox.Group options={plainOptions} defaultValue={[]} onChange={this.onBizClassChange} />
              </FormItem>

              <FormItem label="联系人" name="contact" rules={[{ required: true, message: '请输入业务联系人信息，以方便双方业务对接' }]}
              >
                <Input size="large" prefix={<></>} placeholder="请输入业务联系人信息" />
              </FormItem>

              <FormItem label="手机号码" name="mobile" rules={[
                { required: true, message: '请输入联系人手机号码，以方便收取产品试用信息' },
                { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号' }
              ]}
              >
                <Input size="large" prefix={<></>} placeholder="请输入手机号码" />
              </FormItem>

              <div className="apply-button">
                <a href="/login">已有账号?</a>
              </div>

              {
                regFailed &&
                <Alert
                  message={regErrorhint}
                  type="error"
                  showIcon
                />
              }

              <div className="apply-button">
                <FormItem shouldUpdate>
                  <Button size="large" loading={submitting} type="primary" htmlType="submit"
                    className="auth-form-button">
                    申请试用
                  </Button>
                </FormItem>
              </div>
            </Form>


            {/* <div className="login-footer">
                <p>版权所有 © 上海慧拾智能科技有限公司 2020</p>
              </div> */}
          </Card>
          : <Result
            status="success"
            title="您提交的资料已收到，请等待我们下发试用账号资料"
            subTitle="此期间我司的客户经理可能会与您联系确认资料，请注意保持手机畅通"
            extra={[
              <Button key="home" type="primary" onClick={() => history.push('/')}>
                回到首页
              </Button>,
            ]}
          />
        }
      </div>
    );
  }
}

export default CompanyRegister;
