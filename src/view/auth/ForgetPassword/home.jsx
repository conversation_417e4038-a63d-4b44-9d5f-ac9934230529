/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/25 09:48
 * @LastEditTime: 2021/10/20 10:44
 * @LastEditors: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\auth\ForgetPassword\home.jsx
 * @Description: '忘记密码'
 */

import React from 'react';
import Reflux from 'reflux';
import { Card, Form, Input, Button, Tabs, message, Row, Col, } from 'antd';
import CustomIcon from 'components/base/Icon';
import { apiCall } from 'common/utils';
import Cookie from 'js-cookie';

// 数据流
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';

const FormItem = Form.Item;

import '../Login/home.less';
import logo from 'images/logo.png';

class ForgetPassword extends Reflux.Component {

  formRef = React.createRef();
  formRef2 = React.createRef();

  constructor (props) {
    super(props);
    this.store = AppStore;
    this.state = {
      type: '2',
      submitting: false,
      btnText: '获取验证码'
    };
  }

  componentDidMount () {
    let cd = Cookie.get('weebot_codecd');
    if (cd) {
      this.cd_CountDown(parseInt(cd));
    }
  }

  cd_CountDown = (sec) => {
    this.setState({
      btnText: '重新获取(' + sec + ')',
      btnCodeDisabled: true
    });
    this.state.countdown = sec;
    this.timer = setInterval(() => {
      if (this.state.countdown > 1) {
        this.state.countdown--;
        this.setState({
          btnText: '重新获取(' + this.state.countdown + ')',
          btnCodeDisabled: true
        });
        Cookie.set('weebot_codecd', this.state.countdown);
      } else {
        this.setState({
          btnText: '获取验证码',
          btnCodeDisabled: false,
        });
        Cookie.remove('weebot_codecd');
      }
    }, 1000);

  }

  sendCode = async (e) => {
    e.preventDefault();
    // 判断手机号
    await this.formRef.current.validateFields(['mobile']).then((values) => {
      if (typeof (values.mobile) == 'undefined') {
        message.error('手机号不能为空');
        return;
      } else {
        let data = {
          mobile: values.mobile
        };
        apiCall('sendverifycode', 'GET', data)
          .then((retdata) => {
            this.cd_CountDown(60);
            message.success('已发送，请在手机注意查收验证码');
          })
          .catch((result) => {
            message.error(result.msg);
          });
      }
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onTabChange = (value) => {
    this.setState({ type: value });
  }

  render () {
    const { submitting } = this.state;

    const handleSubmit = async (values) => {
      await this.formRef.current.validateFields().then((formData) => {
        this.setState({
          submitting: true
        });
        formData.type = parseInt(this.state.type);
        AppActions.login(formData, () => {
          this.setState({
            submitting: false
          });
        });
      })
        .catch((errorInfo) => {
          console.log('errorInfo', errorInfo);
        });
    };

    return (
      <div id="login-container">
        <div className="background"></div>
        <Card>
          <div className="login-logo">
            <img src={logo} />
          </div>
          <Tabs activeKey={this.state.type} onChange={this.onTabChange} centered>
            {
              this.state.type == '1' ? <Tabs.TabPane tab="找回密码" key="1">
                <Form onFinish={handleSubmit} className="auth-login-form" ref={this.formRef}>
                  <FormItem name="mobile" rules={[{ required: true, message: '请输入登录的手机号码' }]}>
                    <Input size="large" prefix={<CustomIcon iconid="shouji" />} placeholder="请输入登录的手机号码" />
                  </FormItem>

                  <FormItem>
                    <Row justify={'space-between'}>
                      <Col span={16}>
                        <FormItem name="code" rules={[{ required: true, message: '请输入短信验证码' }]}>
                          <Input size="large" prefix={<CustomIcon iconid="yanzhengma" />} placeholder="请输入短信验证码" />
                        </FormItem>
                      </Col>
                      <Col span={8}>
                        <Button size="large" onClick={this.sendCode} disabled={this.state.btnCodeDisabled}>{this.state.btnText}</Button>
                      </Col>
                    </Row>
                  </FormItem>

                  <FormItem shouldUpdate>
                    <Button size="large" loading={submitting} type="primary" htmlType="submit" className="auth-form-button">
                      确认
                    </Button>
                  </FormItem>
                </Form>

              </Tabs.TabPane>
                : <Tabs.TabPane tab="修改密码" key="2">
                  <Form onFinish={handleSubmit} className="auth-login-form" ref={this.formRef2}>
                    <FormItem name="password" rules={[{ required: true, message: '请输入新密码' }]}>
                      <Input.Password size="large" prefix={<CustomIcon iconid="mima" />} placeholder="请输入新密码" />
                    </FormItem>
                    <FormItem name="newPassword" rules={[{ required: true, message: '请确认新密码' }]}>
                      <Input.Password size="large" prefix={<CustomIcon iconid="mima" />} placeholder="请确认新密码" />
                    </FormItem>
                    <FormItem shouldUpdate>
                      <Button size="large" loading={submitting} type="primary" htmlType="submit" className="auth-form-button">
                        确认
                      </Button>
                    </FormItem>
                  </Form>
                </Tabs.TabPane>
            }
          </Tabs>

          <div className="login-footer">
            {/* <p>版权所有 © 上海慧拾智能科技有限公司 2021-2022</p> */}
            <a href="https://open.work.weixin.qq.com" target="_blank" rel="noreferrer"><img src="https://open.work.weixin.qq.com/service/img?id=wwfc70376824fac0a1&t=isp&c=white&s=medium" srcSet="https://open.work.weixin.qq.com/service/img?id=wwfc70376824fac0a1&t=isp&c=white&s=medium@2x 2x" referrerPolicy="unsafe-url" alt="企业微信" /></a>
          </div>
        </Card>
      </div>
    );
  }
}

export default ForgetPassword;
