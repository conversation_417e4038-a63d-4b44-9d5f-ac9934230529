// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Card, Form, Input, Button, Tabs, message, Space, Image } from "antd"
import CustomIcon from "components/base/Icon"
import { apiCall, genUUID } from "common/utils"
import <PERSON><PERSON> from "js-cookie"
import watermark from "common/watermark"
import { SafetyOutlined } from "@ant-design/icons"
// 数据流
import AppActions from "actions/AppActions"
import AppStore from "stores/AppStore"
import qw_service from "images/qw_service.png"
import logo from "images/logo.png"
import "./home.less"

const FormItem = Form.Item

class Login extends Reflux.Component {
  // 1. ---- 存储Form对象
  form1Ref = React.createRef()
  form2Ref = React.createRef()

  constructor(props) {
    super(props)
    this.store = AppStore
    this.state = {
      type: "1",
      submitting: false,
      btnCodeDisabled: false,
      btnText: "获取验证码",
      captchaInfo: {},
    }
    document.title = "企微支持系统后台"
  }

  componentDidMount() {
    // 移除水印
    watermark.set("")
    let cd = Cookie.get("weebot_codecd")
    if (cd) {
      this.cd_CountDown(parseInt(cd))
    }
    this.getCaptchaInfo()
  }

  cd_CountDown = (sec) => {
    this.setState({
      btnText: "重新获取(" + sec + ")",
      btnCodeDisabled: true,
    })
    let { countdown } = this.state
    countdown = sec
    this.timer = setInterval(() => {
      if (countdown > 1) {
        countdown--
        this.setState({
          btnText: "重新获取(" + countdown + ")",
          btnCodeDisabled: true,
        })
        Cookie.set("weebot_codecd", countdown)
      } else {
        this.setState({
          btnText: "获取验证码",
          btnCodeDisabled: false,
        })
        Cookie.remove("weebot_codecd")
      }
    }, 1000)
  }

  getCaptchaInfo = (e) => {
    apiCall("/captcha", "GET")
      .then((retdata) => {
        this.setState({
          captchaInfo: retdata,
        })
      })
      .catch((err) => {
        console.log(err)
      })
  }

  sendCode = async (e) => {
    e.preventDefault()
    // 判断手机号
    await this.form2Ref.current
      .validateFields(["mobile"])
      .then((values) => {
        if (typeof values.mobile == "undefined") {
          message.error("手机号不能为空")
          return
        } else {
          const data = {
            mobile: values.mobile,
          }
          apiCall("sendverifycode", "GET", data)
            .then((retdata) => {
              this.cd_CountDown(60)
              message.success("已发送，请在手机注意查收验证码")
            })
            .catch((err) => {
              console.log(err)
            })
        }
      })
      .catch((err) => {
        console.log("errorInfo", err)
      })
  }

  onTabChange = (value) => {
    this.setState({ type: value })
  }

  render() {
    const { submitting, type, btnCodeDisabled, btnText } = this.state

    const handleSubmit1 = async (values) => {
      await this.form1Ref.current
        .validateFields()
        .then((formData) => {
          this.setState({
            submitting: true,
          })
          formData.type = parseInt(type)
          const psd = window
            .btoa(formData.password)
            .split("")
            .reverse()
            .join("")
          formData.password = genUUID(8) + psd
          formData.captchaUuid = this.state.captchaInfo?.uuid
          AppActions.login(formData, () => {
            this.getCaptchaInfo()
            this.setState({
              submitting: false,
            })
          })
        })
        .catch((err) => {
          console.log("errorInfo", err)
        })
    }

    const handleSubmit2 = async (values) => {
      await this.form2Ref.current
        .validateFields()
        .then((formData) => {
          this.setState({
            submitting: true,
          })
          formData.type = parseInt(type)
          AppActions.login(formData, () => {
            this.setState({
              submitting: false,
            })
          })
        })
        .catch((err) => {
          console.log("errorInfo", err)
        })
    }

    return (
      <div id="login-container">
        <div className="background"></div>
        <Card>
          <div className="login-logo" style={{ fontSize: "36px" }}>
            {/* <img src={logo} /> */}
            企微支持系统后台
          </div>

          <Tabs activeKey={type} onChange={this.onTabChange} centered>
            <Tabs.TabPane tab="账户登录" key="1">
              <Form
                onFinish={handleSubmit1}
                className="auth-login-form"
                ref={this.form1Ref}
              >
                <FormItem
                  name="account"
                  rules={[{ required: true, message: "请输入用户名" }]}
                >
                  <Input
                    size="large"
                    prefix={<CustomIcon iconid="user-circle" />}
                    placeholder="用户名"
                  />
                </FormItem>

                <FormItem>
                  <FormItem
                    style={{ margin: "unset" }}
                    name="password"
                    rules={[{ required: true, message: "请输入密码!" }]}
                  >
                    <Input
                      size="large"
                      prefix={<CustomIcon iconid="lock-alt" />}
                      type="password"
                      placeholder="密码"
                    />
                  </FormItem>
                </FormItem>

                <FormItem>
                  <FormItem
                    style={{ margin: "unset" }}
                    name="captcha"
                    rules={[{ required: true, message: "请输入验证码!" }]}
                  >
                    <Space>
                      <Input
                        style={{ width: "280px" }}
                        size="large"
                        prefix={<SafetyOutlined style={{ fontSize: "24px" }} />}
                        placeholder="验证码"
                        maxLength={4}
                      />
                      <Image
                        style={{ cursor: "pointer" }}
                        width={80}
                        height={40}
                        src={this.state.captchaInfo?.captcha}
                        preview={false}
                        onClick={this.getCaptchaInfo}
                      />
                    </Space>
                  </FormItem>
                </FormItem>

                {/* <a className='a-apply-try' href="/companyreg">申请试用</a> */}
                {/* <a href="/forget">忘记密码</a> */}
                <FormItem shouldUpdate>
                  <Button
                    size="large"
                    loading={submitting}
                    type="primary"
                    htmlType="submit"
                    className="auth-form-button"
                  >
                    登录
                  </Button>
                </FormItem>
              </Form>
            </Tabs.TabPane>
            {/* <Tabs.TabPane tab="手机号登录" key="2">
              <Form onFinish={handleSubmit2} className="auth-login-form" ref={this.form2Ref}>

                <FormItem name="mobile" rules={[{ required: true, message: '请输入手机号' }]}>
                  <Input size="large" prefix={<CustomIcon iconid="phone" />} placeholder="手机号" />
                </FormItem>

                <FormItem>
                  <Row justify={'space-between'}>
                    <Col span={16}>
                      <FormItem style={{ margin: '0' }} name="code" rules={[{ required: true, message: '请输入验证码' }]}>
                        <Input size="large" prefix={<CustomIcon iconid="laptop-code" />} placeholder="请输入收到的验证码" />
                      </FormItem>
                    </Col>
                    <Col span={8}>
                      <Button style={{ margin: '0' }} size="large" onClick={this.sendCode} disabled={btnCodeDisabled}>{btnText}</Button>
                    </Col>
                  </Row>
                </FormItem>

                <a className='a-apply-try' href="/companyreg">申请试用</a>
                <a href="/forget">忘记密码</a>

                <FormItem shouldUpdate>
                  <Button size="large" loading={submitting} type="primary" htmlType="submit" className="auth-form-button">
                    登录
                  </Button>
                </FormItem>

              </Form>
            </Tabs.TabPane> */}
          </Tabs>
          {!process.env.APP_ENV.startsWith("icbc") && (
            <div className="login-footer">
              <a
                href="https://open.work.weixin.qq.com"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src={qw_service}
                  referrerPolicy="unsafe-url"
                  alt="企业微信"
                />
              </a>
            </div>
          )}
        </Card>
        {!process.env.APP_ENV.startsWith("icbc") && (
          <div className="keepOnRecord">
            版权所有 ©{" "}
            <a href="https://www.wisight.cn/">上海慧拾智能科技有限公司</a>
            <a
              href="https://beian.miit.gov.cn/"
              target="_blank"
              rel="noreferrer"
            >
              沪ICP备2021027655号
            </a>
          </div>
        )}
      </div>
    )
  }
}

export default Login;
