// 依赖类
import React from 'react';
import Reflux from 'reflux';
import { Card, Form, Input, Button, Alert, message, Row, Col, } from 'antd';
import CustomIcon from 'components/base/Icon';
import { apiCall } from 'common/utils';
import Cookie from 'js-cookie';

// 数据流
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';

import logo from 'images/logo.png';

const FormItem = Form.Item;

import './home.less';

class Register extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();
  constructor (props) {
    super(props);
    this.store = AppStore;
    this.state = {
      loginFailed: false,
      submitting: false,
      btnCodeDisabled: false,
      countdown: null,
      btnText: '获取验证码'
    };
  }

  componentDidMount () {
    let cd = Cookie.get('weebot_codecd');
    if (cd) {
      this.cd_CountDown(parseInt(cd));
    }
  }

  cd_CountDown = (sec) => {
    this.setState({
      btnText: '重新获取(' + sec + ')',
      btnCodeDisabled: true
    });
    let { countdown } = this.state;
    countdown = sec;
    this.timer = setInterval(() => {
      if (countdown > 0) {
        countdown--;
        this.setState({
          btnText: '重新获取(' + countdown + ')',
          btnCodeDisabled: true
        });
        Cookie.set('weebot_codecd', countdown);
      } else {
        this.setState({
          btnText: '获取验证码',
          btnCodeDisabled: false
        });
        Cookie.remove('weebot_codecd');
      }
    }, 1000);

  }

  sendCode = async (e) => {
    e.preventDefault();
    // 判断手机号
    await this.formRef.current.validateFields(['mobile']).then((values) => {
      if (typeof (values.mobile) == 'undefined') {
        message.error('手机号不能为空');
        return;
      } else {
        this.cd_CountDown(60);
        const data = {
          mobile: values.mobile
        };
        apiCall('send_verifycode', 'POST', data).then((retdata) => {
          Cookie.set('weebot_token', retdata.accesstoken);
        });
      }
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  render () {
    const { submitting, loginFailed, loginErrorhint, btnCodeDisabled, btnText } = this.state;

    const handleSubmit = async (values) => {
      this.setState({
        loginFailed: false,
      });
      await this.formRef.current.validateFields().then((formData) => {
        this.setState({
          submitting: true
        });
        const callback = () => {
          this.setState({
            submitting: false
          });
        };
        const data = {
          type: 1, // 个人用户
          name: formData.name,
          password: formData.password,
          mobile: formData.mobile,
          code: formData.code
        };
        AppActions.register(data, callback);
      })
        .catch((err) => {
          console.log('errorInfo', err);
        });
    };

    return (
      <div id="login-container">
        <div className="background"></div>

        <Card >
          <div className="login-logo">
            <img src={logo} />
          </div>

          <div className="login-title">新用户注册</div>

          <Form onFinish={handleSubmit} className="auth-login-form" ref={this.formRef}>
            <FormItem name="name" rules={[{ required: true, message: '请输入用户名' }]}>
              <Input size="large" prefix={<CustomIcon iconid="user" />} placeholder="用户名" />
            </FormItem>

            <FormItem name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <Input size="large" prefix={<CustomIcon iconid="mima" />} type="password" placeholder="设定密码" />
            </FormItem>

            <FormItem name="mobile" rules={[{ required: false, message: '请输入手机号码' }]}>
              <Input size="large" prefix={<CustomIcon iconid="shouji" />} placeholder="手机号码" />
            </FormItem>

            <FormItem>
              <Row justify={'space-between'}>
                <Col span={16}>
                  <FormItem style={{ margin: '0' }} name="code" rules={[{ required: false, message: '请输入收到的验证码' }]}>
                    <Input size="large" prefix={<CustomIcon iconid="yanzhengma" />} placeholder="手机验证码" />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <Button style={{ margin: '0' }} onClick={this.sendCode} disabled={btnCodeDisabled}>{btnText}</Button>
                </Col>
              </Row>

            </FormItem>

            <a style={{ display: 'block', marginBottom: '15px' }} href="/login">已有账号?</a>

            {
              loginFailed &&
              <Alert
                message={loginErrorhint}
                type="error"
                showIcon
              />
            }

            <FormItem shouldUpdate>
              <Button size="large" loading={submitting} type="primary" htmlType="submit" className="auth-form-button">
                注册
              </Button>
            </FormItem>
          </Form>


          {/* <div className="login-footer">
            <p>版权所有 © 上海慧拾智能科技有限公司 2020</p>
            <p></p>
          </div> */}
        </Card>
      </div>
    );
  }
}

export default Register;
