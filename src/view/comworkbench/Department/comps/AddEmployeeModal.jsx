/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2024/10/21 17:02
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/AddEmployeeModal.jsx
 * @Description: '批量新增员工对话框'
 */

import React, { useRef, useState } from 'react';
import { Form, message, Modal, Spin, Button, Upload } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { UploadOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import './AddEmployeeModal.less';

const FormItem = Form.Item;

const AddEmployeeModal = (props) => {
  const { visible, onSubmit, onCancel } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  const onChangeUpload = (info) => {
    if (!info.file.name.endsWith('xls') && !info.file.name.endsWith('xlsx')) {
      return false;
    }
    if (!info.fileList.length) {
      formRef.current.setFieldsValue({ file: '' });
    }
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const customFileRequest = (config) => {
    setConfirmLoading(true);
    const File = config.file;
    const newFileLIst = fileList;
    let timer = setTimeout(() => {
      newFileLIst[0].status = 'done';
      newFileLIst[0].percent = 100;
      newFileLIst[0].file = File;
      setFileList([...newFileLIst]);
      setConfirmLoading(false);
      clearTimeout(timer);
    }, 2000);
  };

  // 下载模板
  const handleDownload = () => {
    setLoading(true);
    const title = '批量新增员工模板';
    const data = {
      fileName: `${title}.xlsx`,
    };
    apiCall('/file/template', 'GET', data, null, {
      isExit: true,
      title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
    })
      .then((res) => {
        message.success('下载成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      // 通过FormData构造函数创建一个空对象
      const formObj = new FormData();
      // 通过append方法来追加数据
      formObj.append('file', fileList[0].file); // 返回压缩后的图片
      const data = formObj;
      setConfirmLoading(true);
      apiCall('/appemployee/import/batch', 'POST', data)
        .then((res) => {
          if (res) {
            setLoading(true);
            message.error('部分数据上传失败，失败文件已下载！');
            apiCall('/appemployee/import/failList', 'GET', {
              uuid: res
            }, null, {
              isExit: true,
              title: `批量新增员工失败列表.${moment().format('YYYY-MM-DD')}.xlsx`,
            })
              .then((res) => { })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                setLoading(false);
              });
          } else {
            message.success('上传成功！');
          }
          setLoading(false);
          setFileList([]);
          onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  return (
    <Modal
      className="Department-AddEmployeeModal"
      visible={visible}
      title="批量新增员工"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={() => {
        setLoading(false);
        setFileList([]);
        onCancel?.();
      }}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <p className="tips"></p>
          <p className="tips">通过excel进行批量新增员工</p>
          <p className="tips">1.手动录入的员工在企微出现同样ID的员工后，将会自动绑定        企微员工的信息，员工的来源将自动变为“企业微信”。</p>
          <p className="tips">2. 编辑时，来源为“企业微信”的员工无法编辑其姓名、ID、所属组织，请到企微后台进行编辑。</p>
          <p className="tips">3.excel每行只能上传一条员工信息</p>
          <FormItem
            label="选择文件"
            name="file"
            rules={[{ required: true, message: '请上传xls、xlsx格式的文件' }]}
            extra={
              <div>
                请选择批量新增员工清单文件，格式xls、xlsx
                <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                  下载模板
                </a>
              </div>
            }
          >
            <Upload
              name="file"
              accept=".xls,.xlsx"
              fileList={fileList}
              onChange={onChangeUpload}
              beforeUpload={(file) => {
                if (
                  !file.name.endsWith('xls') &&
                  !file.name.endsWith('xlsx')
                ) {
                  message.error('请上传xls、xlsx格式的文件!');
                  return false || Upload.LIST_IGNORE;
                }
              }}
              customRequest={customFileRequest}
            >
              {fileList.length <= 0 ? (
                <Button icon={<UploadOutlined />}>选择文件</Button>
              ) : null}
            </Upload>
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default AddEmployeeModal;
