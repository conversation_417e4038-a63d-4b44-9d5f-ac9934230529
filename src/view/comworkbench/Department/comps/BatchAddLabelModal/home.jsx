/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/27 15:26
 * @LastEditTime: 2024/10/21 17:01
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/BatchAddLabelModal/home.jsx
 * @Description: '批量打标签'
 */

import React, { useEffect, useState } from "react";
import { message, Modal, Spin } from "antd";
import { apiCall } from "common/utils";
import { flatten } from "common/tree";
import WibotMaintainLabel from "components/WibotMaintainLabel/home";
import "./home.less";

const BatchAddLabelModal = (props) => {
  const { disable = [], multiple = true, total } = props.params;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [initTagTreeData, setInitTagTreeData] = useState([]);
  const [tagList, setTagList] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [searchValue, setSearchValue] = useState("");

  useEffect(async () => {
    const { visible } = props.params;
    if (visible) {
      await getInfoTagOption();
      setVisible(true);
    } else {
      setVisible(false);
      setCheckedKeys([]);
      setSearchValue("");
      setConfirmLoading(false);
    }
  }, [props.params.visible]);

  // 获取标签数据
  const getInfoTagOption = async () => {
    await apiCall("/employee_tag/option", "GET")
      .then((res) => {
        setInitTagTreeData(res);
        setTagList(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const onOk = () => {
    if (checkedKeys && !checkedKeys.length > 0) {
      message.warning("请至少选择一个标签");
      return false;
    }
    setConfirmLoading(true);
    const data = {
      tagIdList: checkedKeys,
    };
    props.params?.onSubmit?.({ ...data });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="BatchAddLabelModal"
      visible={visible}
      title={`批量打标签（${total}人）`}
      width={300}
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <WibotMaintainLabel
          operaType="add"
          fieldNames={{ title: "name", key: "id", children: "children" }}
          initTreeData={initTagTreeData}
          searchValue={searchValue}
          treeData={tagList}
          checkedKeys={checkedKeys}
          onChangeSearchTag={(e, operaType = "add") => {
            let { value } = e.target;
            setSearchValue(value);
            if (!value) {
              setTagList([...initTagTreeData]);
              return false;
            }
            const flattenTagTreeData = flatten(initTagTreeData);
            const indexOfTagTreeData = flattenTagTreeData.filter((item) => item.name.indexOf(value) > -1);
            setTagList([...indexOfTagTreeData]);
          }}
          onCheckTagKeys={(checkedKeysData, e, operaType = "add") => {
            const { checked, node } = e;
            let newCheckedKeys = [...checkedKeys];
            if (checked) {
              newCheckedKeys.push(node.key);
            } else {
              newCheckedKeys = newCheckedKeys.filter((item) => item != node.key);
            }
            setCheckedKeys([...newCheckedKeys]);
          }}
        />
      </Spin>
    </Modal>
  );
};

export default BatchAddLabelModal;
