/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2024/10/21 17:02
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/BatchGroupFormModal.jsx
 * @Description: '批量建群'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Input, Form, message, Modal, Spin, Select } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';

const FormItem = Form.Item;

const BatchGroupFormModal = (props) => {
  const { total } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [groupOption, setGroupOption] = useState([]);

  useEffect(async () => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getManualEmployeeIds();
    } else {
      setVisible(false);
      setConfirmLoading(false);
    }
  }, [props.params.visible]);

  // 不同的场景来展示数据来源不同的员工
  const getManualEmployeeIds = async () => {
    await apiCall('/appemployee/getManualEmployeeIdList', 'GET')
      .then((res) => {
        getGroupOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 群主员工
  const getGroupOption = (excludeIds) => {
    setLoading(true);
    apiCall('/employee/option', 'GET')
      .then((res) => {
        const filterOption = res.map((item) => {
          if (!excludeIds.includes(String(item.id))) {
            return {
              label: item.name,
              value: item.id,
            };
          }
        }).filter((item) => item);
        setGroupOption([...filterOption]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      console.log(formData, 'formData');
      setConfirmLoading(true);
      props.params?.onSubmit?.({ ...formData });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title={`批量建群（${total}人）`}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      okText="确认创建"
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            label="群组名称"
            name="targetGroupName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入群组名称' }]}
          >
            <Input placeholder="请输入群组名称" allowClear />
          </FormItem>
          <FormItem
            name="targetGroupLeaderId"
            label="群主员工"
            rules={[{ required: true, message: '请选择群主员工' }]}
          >
            <Select
              options={groupOption}
              allowClear
              showSearch
              placeholder="请选择群主员工"
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </FormItem>
          <FormItem
            label="群欢迎语"
            name="targetGroupWelcomeMessage"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入群欢迎语' }]}
            extra="只有发送群欢迎语才能显示群组"
          >
            <Input placeholder="请输入群欢迎语" allowClear />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default BatchGroupFormModal;
