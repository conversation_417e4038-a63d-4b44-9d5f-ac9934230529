/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/28 09:26
 * @LastEditTime: 2024/07/15 17:10
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/BatchMaterialModal.jsx
 * @Description: '批量发消息'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin, Input } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import MaterialListForm from 'components/MaterialListForm/home';

const FormItem = Form.Item;
const { TextArea } = Input;

const BatchMaterialModal = (props) => {
  const { disable = [], multiple = true, total } = props.params;
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [msgList, setMsgList] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      let timer = setTimeout(() => {
        onRefMaterialListForm?.current?.getInitMsgList([
          {
            type: 'copyWriter',
            content: '',
            defaultFlag: true,
          },
        ]);
        clearTimeout(timer);
      }, 100);
    } else {
      setVisible(false);
      setConfirmLoading(false);
    }
  }, [props.params.visible]);

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      const list = onRefMaterialListForm.current.getModifyMsgList() || [];
      if (list.length <= 1 && !list[0].content) {
        message.error('请至少添加一条发送内容！');
        return false;
      }
      formData.targetTextMessage = list[0].content;
      setConfirmLoading(true);
      props.params?.onSubmit?.({
        ...formData,
        messages: list,
      });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title={`批量发消息（${total}人）`}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      okText="确认创建"
    >
      <Spin spinning={loading}>
        <Form form={formForm}>
          {/* <FormItem
            name="targetTextMessage"
            rules={[{ required: true, message: "请输入发送消息" }]}
          >
            <TextArea
              placeholder="请输入发送消息"
              allowClear
              autoSize={{ minRows: 2, maxRows: 7 }}
              style={{ height: "100px" }}
            />
          </FormItem> */}
          <MaterialListForm
            params={{
              formRef: formForm,
              menuList: ['image', 'material', 'liveCode'],
              materialTabList: [
                'Article',
                'pageArticle',
                'Video',
                'copyWriter',
                'Picture',
                'Poster',
                'FORM',
                'Product',
              ],
              needScriptFlag: true,
              isNickname: false,
              materialAmount: msgList.length,
              extra: `请按发送顺序添加发送内容，最多9条消息，还可添加 ${10 - msgList.length
                }条`,
            }}
            // 监听回调
            callback={(params) => {
              setMsgList(params.data);
            }}
            ref={onRefMaterialListForm}
          />
        </Form>
      </Spin>
    </Modal>
  );
};

export default BatchMaterialModal;
