/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/31 14:47
 * @LastEditTime: 2022/10/25 11:24
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\comworkbench\Department\comps\DepFormModal.jsx
 * @Description: '组织管理-部门表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Form,
  Input,
  Modal,
  Spin,
  TreeSelect,
} from 'antd';
import { restOPCall } from 'common/utils';
import DepartmentTree from 'components/select/DepartmentTree';
import { removeInputEmpty } from 'common/regular';

const FormItem = Form.Item;

class DepFormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'dept';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      departmentId: null
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps, prevState);
  }

  didUpdate = (prevProps, prevState) => {
    const { ts, visible, updateId } = this.props.params;
    const { data } = this.state;
    if (ts != prevProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: visible, data: null });  // 重置状态数据
      if (updateId) {
        const data = { [this.rowKey]: updateId };
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
    if (data?.id != prevState.data?.id) {
      this.formRef.current?.setFieldsValue({ ...data });
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();

    await this.formRef.current.validateFields().then((formData) => {
      const data = {
        name: formData.name,
        parentId: formData.parentId,
      };
      this.setState({ confirmLoading: true });
      const { updateId } = this.props.params;
      if (updateId) {
        data[this.rowKey] = updateId;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      } else {
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      }
      // 提交成功后 -> 重置表单内容
      this.formRef.current.setFieldsValue({});
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
      departmentId: null
    });
    this.props.callback?.onCancel?.();
    // 点击取消后 -> 重置表单内容
    this.formRef.current.setFieldsValue({});
  }

  render () {
    const { visible, confirmLoading, data } = this.state;
    const { updateId, departmentId, departmentList } = this.props.params;

    return (
      <Modal
        visible={visible}
        title={updateId ? '修改部门' : '新增部门'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateId ? '修改' : '新增'}
        cancelText="取消"
        width={600}
      >
        {((updateId) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef}
            labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{ parentId: departmentId }}
          >

            <FormItem label="部门名称" name="name"
              rules={[{ required: true, message: '部门名称必填' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear />
            </FormItem>

            <FormItem label="所属组织" name="parentId"
              rules={[{ required: true, message: '所属组织必填' }]}>
              <TreeSelect
                allowClear
                treeData={DepartmentTree.mapDepartmentList(departmentList, (node) => node.id == updateId)}
                treeDefaultExpandAll
              />
            </FormItem>

          </Form>
        )}
      </Modal>
    );
  }
}

export default DepFormModal;
