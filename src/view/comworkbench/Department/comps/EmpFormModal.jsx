/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/31 14:47
 * @LastEditTime: 2022/10/25 11:24
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\comworkbench\Department\comps\EmpFormModal.jsx
 * @Description: '组织管理-员工表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Form,
  Input,
  Modal,
  Spin,
  TreeSelect,
  Row,
  Col,
  Button
} from 'antd';
import { apiCall } from 'common/utils';
import AccountSelect from 'components/select/AccountSelect';
import DepartmentTree from 'components/select/DepartmentTree';
import { removeInputEmpty } from 'common/regular';
const FormItem = Form.Item;

class EmpFormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'appemployee';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      departmentId: null
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps, prevState);
  }

  didUpdate = (prevProps, prevState) => {
    const { ts, visible, updateId } = this.props.params;
    const { data } = this.state;
    if (ts != prevProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: visible, data: null });  // 重置状态数据
      if (updateId) {
        const data = { [this.rowKey]: updateId };
        apiCall(this.objName + '/getVOById', 'GET', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
    if (data?.id != prevState.data?.id) {
      this.formRef.current?.setFieldsValue({ ...data });
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        name: formData.name,
        telephone: formData.telephone,
        departmentId: formData.departmentId,
        userId: formData.userId
      };
      this.setState({ confirmLoading: true });
      const { updateId } = this.props.params;
      if (updateId) {
        data[this.rowKey] = updateId;
        data.oldDepartmentId = this.state.data.departmentId;
        data.oldUserId = this.state.data.userId;
      }
      apiCall(this.objName + '/addOrModify', 'PUT', data).then(() => {
        this.setState({ visible: false, confirmLoading: false });
        this.props.params?.onUpdated?.();
      })
        .catch((err) => {
          this.setState({ confirmLoading: false });
        });
      // 提交成功后 -> 重置表单内容
      this.formRef.current.setFieldsValue({});
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
      departmentId: null
    });
    this.props.callback?.onCancel?.();
    // 点击取消后 -> 重置表单内容
    this.formRef.current.setFieldsValue({});
  }

  onChangeRelieve = () => {
    this.formRef.current.setFieldsValue({
      userId: ''
    });
  }

  render () {
    const { visible, data, confirmLoading } = this.state;
    const { updateId, departmentId, departmentList } = this.props.params;
    const userId = data?.userId;
    const account = data?.account;
    let extraOptions = userId && account && [{ label: account, value: userId }];

    return (
      <Modal
        visible={visible}
        title={updateId ? '修改员工' : '新增员工'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateId ? '修改' : '新增'}
        cancelText="取消"
        width={600}
      >
        {((updateId) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef}
            labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{ departmentId: departmentId }}>

            <FormItem label="员工姓名" name="name" rules={[{ required: true, message: '员工姓名必填' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear />
            </FormItem>
            <FormItem label="手机号码" name="telephone"
              rules={[{ required: true, message: '手机号码必须是11位数字', pattern: /^1\d{10}$/ }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear />
            </FormItem>
            <FormItem label="所属组织" name="departmentId"
              rules={[{ required: true, message: '所属组织必填' }]}>
              <TreeSelect
                allowClear
                treeData={DepartmentTree.mapDepartmentList(departmentList)}
                treeDefaultExpandAll
              />
            </FormItem>

            <FormItem label="关联账号" extra={updateId ? '修改关联账号后，被修改的账号将需要重新登录。' : ''}>
              <Row justify={'space-between'}>
                <Col span={19}>
                  <FormItem name="userId" style={{ margin: 'unset' }}>
                    <AccountSelect showSearch allowClear optionFilterProp="label" onlyUnbound extraOptions={extraOptions || null} />
                  </FormItem>
                </Col>
                {
                  userId ? <Col span={4}>
                    <Button onClick={this.onChangeRelieve}>解除</Button>
                  </Col> : ''
                }
              </Row>
            </FormItem>

          </Form>
        )}
      </Modal>
    );
  }
}

export default EmpFormModal;
