/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2024/10/22 09:48
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from "react";
import { Tabs, Form, message, Modal, Spin, Button, Upload } from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import { UploadOutlined } from "@ant-design/icons";
import "./ExportModal.less";

const FormItem = Form.Item;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("1");
  const [fileList, setFileList] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    setFileList([]);
    formRef.current.resetFields();
  };

  const onChangeUpload = (info) => {
    if (!(info.file.name.endsWith('xls') || info.file.name.endsWith('xlsx'))) {
      return false;
    }
    if (!info.fileList.length) {
      formRef.current.setFieldsValue({ file: "" });
    }
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const customFileRequest = (config) => {
    setConfirmLoading(true);
    const File = config.file;
    const newFileLIst = fileList;
    let timer = setTimeout(() => {
      newFileLIst[0].status = "done";
      newFileLIst[0].percent = 100;
      newFileLIst[0].file = File;
      setFileList([...newFileLIst]);
      setConfirmLoading(false);
      clearTimeout(timer);
    }, 2000);
  };

  // 下载模板
  const handleDownload = () => {
    setLoading(true);
    let title = '';
    let suffix = 'xls';
    switch (TabsActiveKey) {
      case '1':
        title = '人力资源系统对账模版';
        break;
      case '2':
        title = '员工批量覆盖标签模版';
        break;
      case '3':
        title = '员工批量打标签模板';
        break;
      case '4':
        title = '员工批量修改模板';
        suffix = 'xlsx';
        break;
    }
    const data = {
      fileName: `${title}.${suffix}`,
    };
    apiCall("/file/template", "GET", data, null, {
      isExit: true,
      title: `${title}.${moment().format('YYYY-MM-DD')}.${suffix}`,
    })
      .then((res) => {
        message.success("下载成功！");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      // 通过FormData构造函数创建一个空对象
      const formObj = new FormData();
      // 通过append方法来追加数据
      formObj.append("file", fileList[0].file); // 返回压缩后的图片
      const data = formObj;
      setConfirmLoading(true);
      let apiUrl = '';
      switch (TabsActiveKey) {
        case '1':
          apiUrl = '/employee/uploadUpdateEmployeeInfoFile';
          break;
        case '2':
          apiUrl = '/employee/uploadUpdateEmployeeTagsFile';
          break;
        case '3':
          apiUrl = '/employee/uploadAddEmployeeTagsFile';
          break;
        case '4':
          apiUrl = '/appemployee/import/update/telephone';
          break;
      }
      apiCall(apiUrl, 'POST', data)
        .then((res) => {
          message.success("上传成功！");
          setConfirmLoading(false);
          props.params?.onOk?.();
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setFileList([]);
    setTabsActiveKey("1");
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="Department-ExportModal"
      visible={visible}
      title="用户管控"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="人力资源系统对账" key="1">
              <p className="tips">
                请上传需要禁用的企微账号
                ID，上传后点确定将直接禁用，如存在账号错误请修正后重试
              </p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[
                  { required: true, message: '请上传xls、xlsx格式的文件' },
                ]}
                extra={
                  <div>
                    请上传需要更新的员工清单文件，格式xls、xlsx
                    <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !(file.name.endsWith('xls') || file.name.endsWith('xlsx'))
                    ) {
                      message.error('请上传xls、xlsx文件!');
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>
            <Tabs.TabPane tab="批量覆盖标签" key="2">
              <p className="tips">批量上传员工及标签清单维护标签</p>
              <p className="tips">
                1.
                字段：9位通行证编号，标签名称，字段间分隔符半角逗号“,”，多个标签之间使用“,”分割。
              </p>
              <p className="tips">
                2. 标签名称必须与“员工标签”名称完全一致，否则不不更新。
              </p>
              <p className="tips">
                3. 注意：文件上传后将完全覆盖原员工标签，无法回退。
              </p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[{ required: true, message: '请上传xls、xlsx文件' }]}
                extra={
                  <div>
                    请选择批量覆盖标签清单文件，格式xls、xlsx
                    <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !(file.name.endsWith('xls') || file.name.endsWith('xlsx'))
                    ) {
                      message.error('请上传xls、xlsx文件!');
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>
            <Tabs.TabPane tab="批量打标签" key="3">
              <p className="tips">批量上传员工及标签清单维护标签</p>
              <p className="tips">
                1.
                字段：9位通行证编号，标签名称，字段间分隔符半角逗号“,”，多个标签之间使用“,”分割。
              </p>
              <p className="tips">
                2.标签名称必须与“员工标签”名称完全一致，否则不更新。
              </p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[{ required: true, message: '请上传xls、xlsx文件' }]}
                extra={
                  <div>
                    请选择批量打标签清单文件，格式xls、xlsx
                    <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !(file.name.endsWith('xls') || file.name.endsWith('xlsx'))
                    ) {
                      message.error('请上传xls、xlsx文件!');
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>
            <Tabs.TabPane tab="批量修改" key="4">
              <p className="tips">
                根据表格中的员工ID更新员工信息，每一行代表更新一位员工”，表格列为：员工ID、手机号码
              </p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[
                  { required: true, message: '请上传xls、xlsx格式的文件' },
                ]}
                extra={
                  <div>
                    请选择批量修改清单文件，格式xls、xlsx
                    <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !file.name.endsWith('xls') &&
                      !file.name.endsWith('xlsx')
                    ) {
                      message.error('请上传xls、xlsx格式的文件!');
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
