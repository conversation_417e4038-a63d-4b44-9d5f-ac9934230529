/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/22 10:03
 * @LastEditTime: 2023/11/03 10:03
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/comps/FormModal.jsx
 * @Description: ''
 */

import React, { useState, memo, useEffect } from 'react';
import { Form, Input, Modal, Spin } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;

const FormModal = (props) => {
  const { visible, id = null, onOk, onCancel } = props.params;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [disabled, setDisabled] = useState(false);

  useEffect(() => {
    if (visible) {
      id && getInfoData(id);
    }
  }, [visible]);
  1;
  const getInfoData = (id) => {
    setLoading(true);
    const data = {
      id: id
    };
    apiCall('/appemployee/get_dto', 'GET', data).then((res) => {
      const { sourceType, name, userId, departmentId, tags, telephone, position } = res;
      formForm.setFieldsValue({
        name: name,
        userId: userId,
        departmentId: departmentId ? [departmentId] : [],
        telephone: telephone,
        position: position,
        tags: tags || [],
      });
      if (sourceType == 'WECOM') {
        setDisabled(true);
      }
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);

      });
  };

  const handleSubmit = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        id: id || null,
        ...formData,
        departmentId: formData.departmentId.join(',')
      };
      apiCall('/appemployee/addOrModify', 'PUT', data).then((res) => {
        formForm.resetFields();
        setDisabled(false);
        onOk?.();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  return (
    <Modal
      visible={visible}
      title={(id ? '编辑' : '新增') + '员工'}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onOk={handleSubmit}
      onCancel={() => {
        formForm.resetFields();
        setLoading(false);
        setConfirmLoading(false);
        setDisabled(false);
        onCancel?.();
      }}
    >
      <Spin spinning={loading}>
        <Form
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          form={formForm}
        >
          <FormItem
            name="name"
            label="员工姓名"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入员工姓名', }]}

          >
            <Input placeholder="请输入员工姓名" allowClear disabled={disabled} />
          </FormItem>

          <FormItem
            name="userId"
            label="员工ID"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入员工ID', }]}
          >
            <Input placeholder="请输入员工ID" allowClear disabled={disabled} />
          </FormItem>

          <FormItem
            name="departmentId"
            label="所属组织"
            rules={[{ required: true, message: '请选择所属组织', }]}
          >
            <ETypeTransferModal title="所属组织" onlyDepartment multiple={false} readonly={disabled} />
          </FormItem>

          <FormItem
            name="telephone"
            label="手机号"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: false, message: '请输入手机号', }, { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号' }]}
          >
            <Input placeholder="请输入手机号" allowClear maxLength={11} />
          </FormItem>

          <FormItem
            name="position"
            label="职务"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: false, message: '请输入职务', }]}
          >
            <Input placeholder="请输入职务" allowClear disabled={disabled} />
          </FormItem>

          <FormItem name="tags" label="员工标签" >
            <ETypeTransferModal title="员工标签" onlyTag />
          </FormItem>

          <div style={{ fontSize: '12px', color: '#a0a0a0', textAlign: 'left' }}>
            <p style={{ margin: 'unset' }}>注：</p>
            <p style={{ margin: 'unset' }}>1.手动录入的员工在企微出现同样ID的员工后，将会自动绑定企微员工的信息，员工的来源将自动变为“企业微信”。</p>
            <p style={{ margin: 'unset' }}>2.编辑时，来源为“企业微信”的员工无法编辑其姓名、ID、所属组织，请到企微后台进行编辑。</p>
            <p style={{ margin: 'unset' }}>3.员工手机号可能来自企业微信，也可能是手动填写，系统以最新的手机号码最准。</p>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(FormModal);
