/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/31 14:23
 * @LastEditTime: 2023/03/17 11:06
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\comworkbench\Department\comps\TagFormModal.jsx
 * @Description: '标签-表单(新增/编辑)'
 */

import React, { useState, useEffect } from 'react';
import { Form, Modal, TreeSelect, Spin } from 'antd';
import { apiCall } from 'common/utils';

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const FormModal = (props) => {
  const { visible, item, onOk, onCancel } = props.params;
  const [formRef] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [tagTreeData, setTagTreeData] = useState([]);

  useEffect(() => {
    if (visible) {
      getEmployeeTagOption();
      if (item) {
        const { tags } = item;
        formRef.setFieldsValue({ 'tagIds': tags || [] });
      }
    }
  }, [visible]);

  // 获取员工标签
  const getEmployeeTagOption = () => {
    setLoading(true);
    const data = {};
    apiCall('/employee_tag/option', 'GET', data).then((res) => {
      setTagTreeData([...res]);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleOk = () => {
    formRef.validateFields().then((formData) => {
      setConfirmLoading(true);
      const { tagIds } = formData;
      const { id } = item;
      const data = {
        employeeIds: [id],
        tagIds: tagIds,
        override: true,
      };
      apiCall('/employee/change_tag', 'POST', data).then((res) => {
        onReset();
        onOk?.();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const handleCancel = () => {
    onReset();
    onCancel?.();
  };

  const onReset = () => {
    setConfirmLoading(false);
    formRef.resetFields();
  };

  return (
    <Modal
      className='Department-TagFormModal'
      title={'编辑标签'}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} form={formRef}>
          <FormItem name="tagIds" label="员工标签" rules={[{ required: true, message: '请选择员工标签' }]}>
            <TreeSelect
              treeData={tagTreeData}
              treeCheckable
              allowClear
              showArrow
              showSearch
              fieldNames={{ label: 'name', value: 'id' }}
              treeNodeFilterProp='name'
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder='员工标签'
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
