/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/23 15:06
 * @LastEditTime: 2024/10/21 17:36
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Department/home.jsx
 * @Description: '组织管理'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Button,
  Col,
  Empty,
  Form,
  Input,
  message,
  Row,
  Spin,
  Table,
  Tree,
  Tooltip,
  Switch,
  Dropdown,
  Menu,
} from 'antd';
import { apiCall, restOPCall } from 'common/utils';
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import DepFormModal from './comps/DepFormModal';
import EmpFormModal from './comps/EmpFormModal';
import { removeInputEmpty } from 'common/regular';
import TagFormModal from './comps/TagFormModal';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import FilterBar from 'components/FilterBar/FilterBar';
import ExportModal from './comps/ExportModal';
import BatchAddLabelModal from './comps/BatchAddLabelModal/home';
import BachGroupFormModal from './comps/BatchGroupFormModal';
import BatchMaterialModal from './comps/BatchMaterialModal';
import FormModal from './comps/FormModal';
import AddEmployeeModal from './comps/AddEmployeeModal';
import SysDictSelect from 'components/select/SysDictSelect';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import './home.less';

const FormItem = Form.Item;

class Department extends Reflux.Component {
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.objName = 'dept';
    this.rowKey = 'id';
    this.state = {
      empFormModalParams: { visible: false },
      depFormModalParams: { visible: false },
      tagFormModalParams: { visible: false },
      pagination: { current: 1, pageSize: 10 }, // 不需要翻页机制时，pagination=null即可
      departmentList: [],
      dataSource: [],
      employeeTotal: 0,
      currentDepartmentId: null,
      currentDepartmentName: null,
      departmentSearch: null,
      focusNode: null,
      selectedRowKeys: [],
      selectedRows: [],
      OperateModalParams: { visible: false },
      exportParams: { visible: false },
      batchAddLabelParams: { visible: false },
      bachGroupFormParams: { visible: false },
      batchMaterialParams: { visible: false },
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '员工姓名',
          width: '160px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          ),
        },
        {
          title: '员工标签',
          width: '160px',
          dataIndex: 'tagNames',
          key: 'tagNames',
          align: 'center',
          render: (value, record, index) => <WibotTableTag tagList={value} />,
        },
        {
          title: '员工ID',
          width: '160px',
          dataIndex: 'userId',
          key: 'userId',
          align: 'center',
          ellipsis: 'true',
          render: (value) => (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          ),
        },
        {
          title: '手机号',
          width: '160px',
          dataIndex: 'telephone',
          key: 'telephone',
          align: 'center',
          ellipsis: 'true',
        },
        {
          title: '职务',
          width: '160px',
          dataIndex: 'position',
          key: 'position',
          align: 'center',
          ellipsis: 'true',
          render: (value) => (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          ),
        },
        {
          title: '所属组织',
          width: '160px',
          dataIndex: 'departmentName',
          key: 'departmentName',
          align: 'center',
          ellipsis: 'true',
          render: (value) => (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          ),
        },
        {
          title: '状态',
          width: '160px',
          dataIndex: 'state',
          key: 'state',
          align: 'center',
          ellipsis: 'true',
          render: (value, record, index) =>
            value == 4 ? (
              <span>未激活</span>
            ) : (
              <Switch
                disabled={record.sourceType != 'WECOM'}
                checkedChildren="启用"
                unCheckedChildren="禁用"
                checked={value == 1}
                onChange={(checked) => {
                  this.onChangeSwitchStatus(checked, record);
                }}
              />
            ),
        },
        {
          title: '数据来源',
          width: '160px',
          dataIndex: 'sourceType',
          key: 'sourceType',
          align: 'center',
          render: (value) => (
            <Tooltip
              placement="topLeft"
              title={value == 'WECOM' ? '企业微信' : '手动录入'}
            >
              {value == 'WECOM' ? '企业微信' : '手动录入'}
            </Tooltip>
          ),
        },
        {
          title: '创建时间',
          width: '160px',
          dataIndex: 'createTime',
          key: 'createTime',
          align: 'center',
        },
        {
          title: '更新时间',
          width: '160px',
          dataIndex: 'updateTime',
          key: 'updateTime',
          align: 'center',
        },
        {
          title: '操作',
          width: '120px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => {
            let opts = [
              // { onClick: () => this.handleEditTag(record), name: "编辑标签" },
              { onClick: () => this.handleEdit(record), name: '编辑' },
            ];
            if (record.sourceType != 'WECOM') {
              opts.push({
                onClick: () => this.handleDelete(record),
                name: '删除',
              });
            }
            return <ListOperation opts={opts} />;
          },
        },
      ],
      openFlag: true,
      formParams: { visible: false },
      AddEmployeeParams: { visible: false },
    };
  }

  componentDidMount () {
    this.initDepartmentAndEmployeeList();
  }

  initDepartmentAndEmployeeList () {
    this.setState({ deptTreeLoading: true });
    apiCall('/' + this.objName + '/option', 'GET', {})
      .then((res) => {
        if (res && res.length > 0) {
          this.setState(
            {
              departmentList: res,
              currentDepartmentId: res[0].id,
              currentDepartmentName: res[0].name,
            },
            () => {
              this.getEmployeeList();
            }
          );
        }
      })
      .finally(() => this.setState({ deptTreeLoading: false }));
  }

  onDepartmentSelect = (selectedKeys, e) => {
    this.setState(
      {
        loading: true,
        currentDepartmentName: e.node.name,
        currentDepartmentId: e.node.key,
      },
      () => {
        this.getEmployeeList();
      }
    );
  };

  onDepartmentSearch = (e) => {
    const searchKey = e.target.value;
    let timer = setTimeout(() => {
      this.setState({ departmentSearch: searchKey });
      clearTimeout(timer);
    }, 500);
  };

  // 全量同步
  onSynchronization = () => {
    apiCall('/wxcp/sync', 'POST')
      .then((res) => {
        this.initDepartmentAndEmployeeList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  onDepartmentAdd = () => {
    const { departmentList, currentDepartmentId } = this.state;
    this.setState({
      depFormModalParams: {
        visible: true,
        updateId: null,
        ts: parseInt(moment().format('X')),
        departmentList: departmentList,
        departmentId: currentDepartmentId,
        onUpdated: () => {
          message.success('新增部门成功');
          this.initDepartmentAndEmployeeList();
        },
      },
    });
  };

  mapDepartmentList (nodeList) {
    const { departmentSearch } = this.state;
    let list = [];
    let search = departmentSearch;
    nodeList.forEach((node) => {
      let childList = node.childList;
      let name = node.name;
      if (childList && childList.length > 0) {
        childList = this.mapDepartmentList(childList);
      }
      let title = <span>{name}</span>;
      if (search) {
        const index = name.indexOf(search);
        if (index > -1) {
          title = (
            <span>
              {name.substr(0, index)}
              <span style={{ color: '#f50' }}>{search}</span>
              {name.substr(index + search.length)}
            </span>
          );
        }
      }
      list.push({
        title: title,
        key: node.id,
        name: name,
        children: childList,
      });
    });
    return list;
  }

  getEmployeeList = async (params = {}) => {
    this.setState({ loading: true });
    await this.formRef.current.validateFields().then((formData) => {
      formData.tagIds = formData.tagIds?.join(',') || null;
      formData.stateList = formData.stateList?.join(',') || null;
      const { pagination, query } = params;
      const { currentDepartmentId } = this.state;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        departmentId: currentDepartmentId,
      };
      restOPCall('appemployee', 'getlist', data)
        .then(({ records, current, pages, size, total }) => {
          this.setState({
            dataSource: records,
            pagination: {
              current: current,
              pageSize: size,
              total: total,
              showQuickJumper: true,
              showSizeChanger: true,
              showTotal: (total, range) =>
                `共 ${total} 条记录 第${current}/${pages}页`,
            },
            employeeTotal: total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.setState({ loading: false });
        });
    });
  };

  onTableChange = (pagination, filters, sorter) => {
    this.getEmployeeList({ pagination });
  };

  onSelectChange = (selectedRowKeys, selectedRows, info) => {
    this.setState({
      selectedRowKeys,
      selectedRows,
    });
  };

  handleDelete = (record) => {
    const { id, name } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        content: `您将删除员工【${name}】的信息，确定继续吗？`,
        onSubmit: () => {
          const data = {
            id: id,
          };
          apiCall('/employee/canDelete', 'GET', data)
            .then((res) => {
              if (res) {
                apiCall('/appemployee/del', 'DELETE', data)
                  .then((res) => {
                    message.success('删除成功！');
                    this.getEmployeeList();
                  })
                  .catch((err) => {
                    console.log(err);
                  })
                  .finally(() => {
                    this.setState({
                      OperateModalParams: {
                        visible: false,
                      },
                    });
                  });
              } else {
                message.error('数据已关联，删除失败！');
                this.setState({
                  OperateModalParams: {
                    visible: false,
                  },
                });
              }
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => { });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  handleEditTag = (record) => {
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length > 0) {
      return false;
    }
    this.setState({
      tagFormModalParams: {
        visible: true,
        item: record,
        onOk: () => {
          this.setState({
            tagFormModalParams: {
              visible: false,
            },
          });
          message.success('编辑成功！');
          this.getEmployeeList();
        },
        onCancel: () => {
          this.setState({
            tagFormModalParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  // 处理批量操作
  handleOpen = (type) => {
    this.setState({
      openFlag: false,
    });
    const { selectedRowKeys, currentDepartmentId } = this.state;
    const total =
      selectedRowKeys.length > 0
        ? selectedRowKeys.length
        : this.state.pagination.total;
    const obj =
      selectedRowKeys.length > 0
        ? { employeeIds: selectedRowKeys }
        : { ...this.formRef.current.getFieldsValue() };
    if (type == 'label') {
      if (selectedRowKeys.length > 0) {
        this.setState({
          batchAddLabelParams: {
            visible: true,
            total,
            onCancel: () => {
              this.setState({
                batchAddLabelParams: {
                  visible: false,
                },
              });
            },
            onSubmit: (data) => {
              console.log(data, 'data');
              const params = {
                departmentId: currentDepartmentId,
                operationType: 'TAGS',
                targetTagIds: data.tagIdList,
                ...obj,
              };
              apiCall('/employee/batchOperation', 'POST', params)
                .then((res) => {
                  message.success('批量打标签成功！');
                  this.getEmployeeList();
                  // rowSelection.onSelectNone();
                })
                .catch((err) => {
                  console.log(err);
                })
                .finally(() => {
                  this.setState({
                    batchAddLabelParams: {
                      visible: false,
                    },
                  });
                });
            },
          },
        });
        return false;
      }
      this.setState({
        OperateModalParams: {
          visible: true,
          title: '批量打标签确认',
          content: `${this.state.pagination.total > 2000
            ? `当前查询结果${this.state.pagination.total}行已超出批量操作【打标签】的限制2000行，请调整筛选条件后再试！`
            : `即将为当前查询结果的${this.state.pagination.total}行数据进行批量操作【打标签】，确定这样做吗？`
            }`,
          onSubmit: () => {
            if (this.state.pagination.total > 2000) {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
            } else {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
              this.setState({
                batchAddLabelParams: {
                  visible: true,
                  total,
                  onCancel: () => {
                    this.setState({
                      batchAddLabelParams: {
                        visible: false,
                      },
                    });
                  },
                  onSubmit: (data) => {
                    console.log(data, 'data');
                    const params = {
                      departmentId: currentDepartmentId,
                      operationType: 'TAGS',
                      targetTagIds: data.tagIdList,
                      ...obj,
                    };
                    apiCall('/employee/batchOperation', 'POST', params)
                      .then((res) => {
                        message.success('批量打标签成功！');
                        this.getEmployeeList();
                        // rowSelection.onSelectNone();
                      })
                      .catch((err) => {
                        console.log(err);
                      })
                      .finally(() => {
                        this.setState({
                          batchAddLabelParams: {
                            visible: false,
                          },
                        });
                      });
                  },
                },
              });
            }
          },
          onCancel: () => {
            this.setState({
              OperateModalParams: {
                visible: false,
              },
            });
          },
        },
      });
    } else if (type == 'group') {
      if (selectedRowKeys.length > 0) {
        this.setState({
          bachGroupFormParams: {
            visible: true,
            total,
            onCancel: () => {
              this.setState({
                bachGroupFormParams: {
                  visible: false,
                },
              });
            },
            onSubmit: (data) => {
              console.log(data, 'data');
              const params = {
                departmentId: currentDepartmentId,
                operationType: 'EMPLOYEE_GROUP',
                ...data,
                ...obj,
              };
              apiCall('/employee/batchOperation', 'POST', { ...params })
                .then((res) => {
                  message.success('批量建群成功！');
                  this.getEmployeeList();
                })
                .catch((err) => {
                  console.log(err);
                })
                .finally(() => {
                  this.setState({
                    bachGroupFormParams: {
                      visible: false,
                    },
                  });
                });
            },
          },
        });
        return false;
      }
      this.setState({
        OperateModalParams: {
          visible: true,
          title: '批量建群确认',
          content: `${this.state.pagination.total > 2000
            ? `当前查询结果${this.state.pagination.total}行已超出批量操作【建群】的限制2000行，请调整筛选条件后再试！`
            : `即将为当前查询结果的${this.state.pagination.total}行数据进行批量操作【建群】，确定这样做吗？`
            }`,
          onSubmit: () => {
            if (this.state.pagination.total > 2000) {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
            } else {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
              this.setState({
                bachGroupFormParams: {
                  visible: true,
                  total,
                  onCancel: () => {
                    this.setState({
                      bachGroupFormParams: {
                        visible: false,
                      },
                    });
                  },
                  onSubmit: (data) => {
                    console.log(data, 'data');
                    const params = {
                      departmentId: currentDepartmentId,
                      operationType: 'EMPLOYEE_GROUP',
                      ...data,
                      ...obj,
                    };
                    apiCall('/employee/batchOperation', 'POST', { ...params })
                      .then((res) => {
                        message.success('批量建群成功！');
                        this.getEmployeeList();
                      })
                      .catch((err) => {
                        console.log(err);
                      })
                      .finally(() => {
                        this.setState({
                          bachGroupFormParams: {
                            visible: false,
                          },
                        });
                      });
                  },
                },
              });
            }
          },
          onCancel: () => {
            this.setState({
              OperateModalParams: {
                visible: false,
              },
            });
          },
        },
      });
    } else if (type == 'info') {
      if (selectedRowKeys.length > 0) {
        this.setState({
          batchMaterialParams: {
            visible: true,
            total,
            onCancel: () => {
              this.setState({
                batchMaterialParams: {
                  visible: false,
                },
              });
            },
            onSubmit: (data) => {
              const params = {
                departmentId: currentDepartmentId,
                operationType: 'EMPLOYEE_MESSAGE',
                ...data,
                ...obj,
              };
              apiCall('/employee/batchOperation', 'POST', { ...params })
                .then((res) => {
                  message.success('批量发消息成功！');
                  this.getEmployeeList();
                })
                .catch((err) => {
                  console.log(err);
                })
                .finally(() => {
                  this.setState({
                    batchMaterialParams: {
                      visible: false,
                    },
                  });
                });
            },
          },
        });
        return false;
      }
      this.setState({
        OperateModalParams: {
          visible: true,
          title: '批量发消息确认',
          content: `${this.state.pagination.total > 2000
            ? `当前查询结果${this.state.pagination.total}行已超出批量操作【发消息】的限制2000行，请调整筛选条件后再试！`
            : `即将为当前查询结果的${this.state.pagination.total}行数据进行批量操作【发消息】，确定这样做吗？`
            }`,
          onSubmit: () => {
            if (this.state.pagination.total > 2000) {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
            } else {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
              this.setState({
                batchMaterialParams: {
                  visible: true,
                  total,
                  onCancel: () => {
                    this.setState({
                      batchMaterialParams: {
                        visible: false,
                      },
                    });
                  },
                  onSubmit: (data) => {
                    const params = {
                      departmentId: currentDepartmentId,
                      operationType: 'EMPLOYEE_MESSAGE',
                      ...data,
                      ...obj,
                    };
                    apiCall('/employee/batchOperation', 'POST', { ...params })
                      .then((res) => {
                        message.success('批量发消息成功！');
                        this.getEmployeeList();
                      })
                      .catch((err) => {
                        console.log(err);
                      })
                      .finally(() => {
                        this.setState({
                          batchMaterialParams: {
                            visible: false,
                          },
                        });
                      });
                  },
                },
              });
            }
          },
          onCancel: () => {
            this.setState({
              OperateModalParams: {
                visible: false,
              },
            });
          },
        },
      });
    }
  };

  onChangeSwitchStatus = (checked, record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: checked ? '启用企微账号确认' : '禁用企微账号确认',
        content: checked
          ? '该员工存在删客户风险，继续启用有可能会造成客户流失，是否确认启用？'
          : '禁用将导致该员工立即无法使用企业微信账号，禁用不会导致企微账号数据丢失，后续可手动启用以恢复使用，是否确认禁用？',
        onSubmit: () => {
          const data = {
            employeeId: id,
            state: checked ? 1 : 0,
          };
          apiCall('/employee/updateEmployeeState', 'POST', data)
            .then((res) => {
              message.success('修改成功！');
              this.getEmployeeList();
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                },
              });
            });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  handleExport = () => {
    this.setState({
      exportParams: {
        visible: true,
        onOk: () => {
          this.getEmployeeList();
        },
        onCancel: () => {
          this.setState({
            exportParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  handleQuery = () => {
    this.getEmployeeList();
  };

  handleReset = () => {
    this.formRef.current.resetFields();
    this.getEmployeeList();
  };

  handleAdd = () => {
    this.setState({
      formParams: {
        visible: true,
        onOk: (params) => {
          this.setState({
            formParams: {
              visible: false,
            },
          });
          message.success('新增成功！');
          this.getEmployeeList();
        },
        onCancel: () => {
          this.setState({
            formParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  handleAddEmployee = () => {
    this.setState({
      AddEmployeeParams: {
        visible: true,
        onSubmit: (params) => {
          this.setState({
            AddEmployeeParams: {
              visible: false,
            },
          });
          this.getEmployeeList();
        },
        onCancel: () => {
          this.setState({
            AddEmployeeParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      formParams: {
        visible: true,
        id: id,
        onOk: (params) => {
          this.setState({
            formParams: {
              visible: false,
            },
          });
          message.success('编辑成功！');
          this.getEmployeeList();
        },
        onCancel: () => {
          this.setState({
            formParams: {
              visible: false,
            },
          });
        },
      },
    });
  };

  render () {
    const {
      dataSource,
      pagination,
      selectedRowKeys,
      columns,
      deptTreeLoading,
      departmentList,
      currentDepartmentId,
      currentDepartmentName,
      employeeTotal,
      loading,
      depFormModalParams,
      empFormModalParams,
      OperateModalParams,
      tagFormModalParams,
      exportParams,
      batchAddLabelParams,
      bachGroupFormParams,
      batchMaterialParams,
      formParams,
      AddEmployeeParams,
    } = this.state;

    let departmentTree;
    if (deptTreeLoading) {
      departmentTree = (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}>
          <Spin />
        </Empty>
      );
    } else {
      if (departmentList.length > 0) {
        let depList = this.mapDepartmentList(departmentList);
        let defaultExpandedKeys = [depList[0].key];
        depList[0].isRoot = true;
        departmentTree = (
          <Tree
            height={660}
            className="framework-tree"
            defaultExpandedKeys={defaultExpandedKeys}
            treeData={depList}
            blockNode
            onSelect={this.onDepartmentSelect}
            selectedKeys={[currentDepartmentId]}
            titleRender={(node) => (
              <Row className="row-tree">
                <Col flex="auto">{node.title}</Col>
              </Row>
            )}
          />
        );
      } else {
        departmentTree = <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
      }
    }

    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      // selections: [
      //   Table.SELECTION_ALL,
      //   Table.SELECTION_INVERT,
      //   Table.SELECTION_NONE,
      // ],
    };

    // const locales = {
    //   selectionAll: '选择全部',
    //   selectInvert: '反选本页',
    //   selectNone: '取消全部',
    // };

    return (
      <div className="Department">
        <Row style={{ backgroundColor: 'white' }}>
          <Col span={5} style={{ borderRight: '1px solid #f2f2f2' }}>
            <Row
              style={{
                height: 50,
                borderBottom: '1px solid #f2f2f2',
                display: 'flex',
              }}
              align="middle"
            >
              <Col offset={1}>
                <h2 style={{ margin: '0' }}>组织架构</h2>
              </Col>
              <Col offset={1}>
                <Button onClick={this.onSynchronization}>全量同步</Button>
                {/* <Button onClick={() => this.initDepartmentAndEmployeeList()}>全量同步</Button> */}
              </Col>
            </Row>
            <Row style={{ margin: '20px 10px' }}>
              <Col lg={24} xxl={24}>
                <Input
                  allowClear
                  placeholder="组织搜索"
                  onChange={this.onDepartmentSearch}
                ></Input>
              </Col>
              {/* <Col lg={10} xxl={6}><Button style={{ width: '100%' }} type="primary" onClick={this.onDepartmentAdd}>新增</Button></Col> */}
            </Row>
            <Row style={{ margin: '0 10px' }}>
              <Col span={24}>{departmentTree}</Col>
            </Row>
          </Col>
          <Col span={19}>
            <Row
              style={{ height: 50, borderBottom: '1px solid #f2f2f2' }}
              align="middle"
            >
              <Col style={{ marginLeft: '20px' }}>
                <h2 style={{ margin: '0' }}>
                  员工管理 - {currentDepartmentName}（{employeeTotal}人）
                </h2>
              </Col>
            </Row>
            <FilterBar bodyStyle={{ margin: 'unset' }}>
              <Form ref={this.formRef} layout={'inline'}>
                <FormItem
                  name="keyWord"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                >
                  <Input placeholder="姓名/手机号/员工ID" allowClear />
                </FormItem>
                <FormItem
                  name="tagIds"
                  style={{ minWidth: 'unset', maxWidth: '200px' }}
                >
                  <ETypeTransferModal title="员工标签" mode={['tag']} />
                </FormItem>
                <FormItem name="stateList">
                  <SysDictSelect
                    placeholder="状态"
                    dataset="EMPLOYEE_STATUS"
                    mode="multiple"
                  />
                </FormItem>
              </Form>
              <div className="flex flex-space-between">
                <div>
                  <Button type="primary" onClick={() => this.handleQuery()}>
                    查询
                  </Button>
                  <Button onClick={() => this.handleReset()}>重置筛选</Button>
                </div>
                <div>
                  <Button type="primary" onClick={this.handleAddEmployee}>
                    批量新增员工
                  </Button>
                  <Button type="primary" onClick={this.handleAdd}>
                    新增
                  </Button>
                  <Button type="primary" onClick={this.handleExport}>
                    用户管控
                  </Button>
                  <Dropdown
                    overlay={
                      <Menu>
                        <div
                          style={{
                            color: '#AAAAAA',
                            padding: '5px',
                            borderBottom: '1px solid #e5e5e5',
                          }}
                        >
                          {selectedRowKeys.length > 0 ? (
                            <>
                              操作已选的
                              <span style={{ color: '#fd0100' }}>
                                &nbsp;{selectedRowKeys.length}&nbsp;
                              </span>
                              行数据
                            </>
                          ) : (
                            <>
                              操作查询结果的
                              <span style={{ color: '#fd0100' }}>
                                &nbsp;{pagination.total || 0}&nbsp;
                              </span>
                              行数据
                            </>
                          )}
                        </div>
                        <Menu.Item
                          key="0"
                          onClick={() => {
                            this.handleOpen('label');
                          }}
                          style={{ textAlign: 'center' }}
                        >
                          打标签
                        </Menu.Item>
                        <Menu.Item
                          key="1"
                          onClick={() => {
                            this.handleOpen('group');
                          }}
                          style={{ textAlign: 'center' }}
                        >
                          建群
                        </Menu.Item>
                        <Menu.Item
                          key="2"
                          onClick={() => {
                            this.handleOpen('info');
                          }}
                          style={{ textAlign: 'center' }}
                        >
                          发消息
                        </Menu.Item>
                      </Menu>
                    }
                    placement="bottomLeft"
                    arrow
                  >
                    <Button type="primary">批量操作</Button>
                  </Dropdown>
                </div>
              </div>
            </FilterBar>
            <Row>
              <Col offset={1} span={23} style={{ marginLeft: '20px' }}>
                <Table
                  loading={loading}
                  columns={columns}
                  dataSource={dataSource}
                  pagination={pagination}
                  rowKey={(record) => `${record.id}`}
                  onChange={this.onTableChange}
                  scroll={{ x: 1300 }}
                  rowSelection={rowSelection}
                // locale={locales}
                ></Table>
              </Col>
            </Row>
          </Col>
        </Row>
        <DepFormModal params={depFormModalParams} />
        <EmpFormModal params={empFormModalParams} />
        <TagFormModal params={tagFormModalParams} />
        <OperateModal params={OperateModalParams} />
        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
        {/* 批量打标签 */}
        <BatchAddLabelModal params={batchAddLabelParams} />
        {/* 批量建群 */}
        <BachGroupFormModal params={bachGroupFormParams} />
        {/* 批量发消息 */}
        <BatchMaterialModal params={batchMaterialParams} />
        <FormModal params={formParams} />
        <AddEmployeeModal params={AddEmployeeParams} />
      </div>
    );
  }
}

export default Department;
