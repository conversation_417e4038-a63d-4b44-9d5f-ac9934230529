/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/09 16:34
 * @LastEditTime: 2025/07/15 16:00
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/H5Authority/comps/FormModal.jsx
 * @Description: '移动端权限（新增/编辑）'
 */

import React, { useState, useEffect, memo } from 'react';
import { Form, Spin, Modal, Input, Tree, Select, message } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { flatten } from 'common/tree';

const FormItem = Form.Item;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const { visible, item, onOk, onCancel } = props.params;
  const [formRef] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [tagOptions, setTagOptions] = useState([]);
  const [h5MenuList, setH5MenuList] = useState([]);
  const [menuIds, setMenuIds] = useState([]);

  useEffect(async () => {
    if (visible) {
      getEmployeeTagOption();
      if (item) {
        getH5EmployeeMenu();
      } else {
        getH5MenuList();
      }
    }
  }, [visible]);

  // 获取单个员工标签菜单
  const getH5EmployeeMenu = () => {
    setLoading(true);
    const data = {
      id: item.employeeTagId,
    };
    apiCall('/employee_tag_menu/get', 'GET', data)
      .then((res) => {
        const { employeeTagId, description, menuIds, menuTree } = res;
        setH5MenuList([...mapMenuList(menuTree)]);
        setMenuIds(menuIds);
        formRef.setFieldsValue({
          employeeTagId: employeeTagId,
          description: description,
          menuIds: menuIds,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取员工标签
  const getEmployeeTagOption = async () => {
    setLoading(true);
    await apiCall('/employee_tag/option?filter=true', 'GET')
      .then((res) => {
        setTagOptions(
          res.map((item) => ({
            label: item.name,
            value: item.id,
            disabled: item.disabled,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取移动端菜单
  const getH5MenuList = async () => {
    setLoading(true);
    await apiCall('menu/getH5MenuList', 'GET')
      .then((res) => {
        setH5MenuList([...mapMenuList(res)]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const mapMenuList = (menuList, excludeKeys = []) => {
    if (!menuList) {
      return null;
    }
    let list = [];
    menuList.forEach((menu, index) => {
      let childList = menu.children || menu.childMenuList;
      if (childList && childList.length > 0) {
        excludeKeys.push(Number(menu.menuId || menu.key));
        childList = mapMenuList(childList, excludeKeys);
      }
      list.push({
        ...menu,
        title: menu.name || menu.title,
        key: Number(menu.menuId || menu.key),
        sort: index + 1,
        // checked: formRef?.getFieldValue('menuIds')?.indexOf(Number(menu.menuId || menu.key)) <= -1 ? null : true,
        children: childList,
        checkable: !(childList && childList.length > 0),
      });
    });
    return list;
  };

  const onMenuCheck = (checkedKeys, e) => {
    const { checked, halfChecked } = checkedKeys;
    formRef.setFieldsValue({
      menuIds: checked,
    });
    setMenuIds(checked);
  };

  const onDragEnter = (info) => {
    // console.log(info, 'onDragEnter');
  };

  const onDrop = info => {
    // console.log(info);
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const loop = (
      data,
      key,
      callback,
    ) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    };
    const data = [...h5MenuList];

    // 禁止向下级拖拽/合并
    if (!info.dropToGap) {
      return false;
    }
    // 禁止向上级拖拽
    if (info.node.key == h5MenuList[0].key) {
      return false;
    }

    // Find dragObject
    let dragObj;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      // Drop on the content
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
      });
    } else if (
      ((info.node).props.children || []).length > 0 && // Has children
      (info.node).props.expanded && // Is expanded
      dropPosition === 1 // On the bottom gap
    ) {
      loop(data, dropKey, item => {
        item.children = item.children || [];
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
        // in previous version, we use item.children.push(dragObj) to insert the
        // item to the tail of the children
      });
    } else {
      let ar = [];
      let i;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i, 0, dragObj);
      } else {
        ar.splice(i + 1, 0, dragObj);
      }
    }
    setH5MenuList(mapMenuList(data));
  };


  // 去重
  const noRepeat = (arr) => {
    let newArr = [...new Set(arr)];
    return newArr;
  };

  const handleOk = () => {
    formRef.validateFields().then((formData) => {
      setConfirmLoading(true)
      flatten(h5MenuList).forEach((item) => {
        if (formData.menuIds.indexOf(item.key) > -1) {
          formData.menuIds = noRepeat([...formData.menuIds, ...item.parentIds]);
        }
      });
      let apiUrl = ""
      if (item?.id) {
        apiUrl = `/employee_tag_menu/modify`
      } else {
        apiUrl = "/employee_tag_menu/add"
      }
      const data = {
        id: item ? item.id : null,
        ...formData,
        menuTree: mapMenuList(h5MenuList),
      }
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          onReset()
          onOk?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    });
  };

  const handleCancel = () => {
    onReset();
    onCancel?.();
  };

  const onReset = () => {
    setLoading(false);
    setConfirmLoading(false);
    setTagOptions([]);
    setH5MenuList([]);
    setMenuIds([]);
    formRef.resetFields();
  };

  return (
    <Modal
      className="H5Authority-FormModal"
      width={600}
      title={item ? '编辑权限' : '新增权限'}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} form={formRef}>
          <FormItem
            name="employeeTagId"
            label="员工标签"
            rules={[{ required: true, message: '请选择员工标签' }]}
            extra="员工标签不可重复选择"
          >
            <Select
              placeholder="请选择员工标签"
              options={tagOptions}
              allowClear
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </FormItem>

          <FormItem
            label="权限描述"
            name="description"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <TextArea
              placeholder="请输入权限描述（50字内）"
              allowClear
              showCount
              maxLength={50}
              autoSize={{ minRows: 2, maxRows: 7 }}
            />
          </FormItem>

          {(h5MenuList && h5MenuList.length && (
            <FormItem
              label="角色菜单"
              name="menuIds"
              rules={[{ required: true, message: '请选择菜单' }]}
            // extra="对菜单进行拖动排序可以调整移动端显示顺序"
            >
              <Tree
                checkable
                blockNode
                // fieldNames={{ title: 'name', key: 'menuId', children: 'childMenuList' }}
                onCheck={onMenuCheck}
                defaultExpandAll
                checkStrictly
                treeData={h5MenuList}
                checkedKeys={menuIds}
                height={300}
                selectable={false}
                draggable={(node) => {
                  return false; // 关闭拖拽功能
                  if (node.children) {
                    return false;
                  } else {
                    return true;
                  }
                }}
                onDragEnter={onDragEnter}
                onDrop={onDrop}
                style={{ border: '1px solid #d9d9d9', borderRadius: '2px' }}
              />
            </FormItem>
          )) ||
            null}
        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(FormModal);
