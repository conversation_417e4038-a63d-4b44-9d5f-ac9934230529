/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/17 17:19
 * @LastEditTime: 2023/04/18 15:58
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/H5Authority/comps/ViewRoleModal.jsx
 * @Description: '查看角色'
 */

import React, { useState, useEffect } from 'react';
import { apiCall } from 'common/utils';
import { Spin, Modal, Tree, Descriptions, } from 'antd';

const ViewRoleModal = (props) => {
  const { visible, item, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [roleInfo, setRoleInfo] = useState({});

  useEffect(async () => {
    if (visible) {
      if (item) {
        getH5EmployeeMenu();
      }
    }
  }, [visible]);

  // 获取单个员工标签菜单
  const getH5EmployeeMenu = () => {
    setLoading(true);
    const data = {
      id: item.employeeTagId
    };
    apiCall('/employee_tag_menu/get', 'GET', data).then((res) => {
      // const { menuTree, menuIds } = res;
      // res.menuList = filterAndMapMenuList(menuTree, menuIds);
      setRoleInfo(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const filterAndMapMenuList = (menuList, menuIds) => {
    if (!menuList) {
      return null;
    }
    let list = [];
    menuList.forEach((menu) => {
      let childList = menu.children;
      if (childList && childList.length > 0) {
        childList = filterAndMapMenuList(childList, menuIds);
      }
      menu.key = Number(menu.key);
      if ((childList && childList.length > 0) || menuIds?.indexOf(menu.key) > -1) {
        list.push({
          title: menu.title,
          key: menu.key,
          children: childList
        });
      }
    });
    return list;
  };

  const handleCancel = () => {
    onReset();
    onCancel?.();
  };

  const onReset = () => {
    setLoading(false);
    setRoleInfo(null);
  };

  return (
    <Modal
      className='H5Authority-ViewRoleModal'
      width={600}
      title='查看角色'
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      footer={null}
      onCancel={handleCancel}
    >
      <Spin spinning={loading}>
        <Descriptions column={1}>
          <Descriptions.Item label="员工标签">{roleInfo?.name}</Descriptions.Item>
          <Descriptions.Item label="权限描述">{roleInfo?.description}</Descriptions.Item>
          <Descriptions.Item label="角色菜单">
            {roleInfo?.menuTreeFilter && roleInfo?.menuTreeFilter.length > 0 ? <Tree defaultExpandAll treeData={roleInfo?.menuTreeFilter} /> : null}
          </Descriptions.Item>
        </Descriptions>
      </Spin>
    </Modal>
  );
};

export default ViewRoleModal;
