/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 16:52
 * @LastEditTime: 2023/11/21 10:39
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/H5Authority/home.jsx
 * @Description: '移动端权限'
 */

import React, { useState, useEffect } from 'react';
import { apiCall } from 'common/utils';
import { Button, Table, Card, Tooltip, Row, Col, message } from 'antd';
import FormModal from './comps/FormModal';
import ViewRoleModal from './comps/ViewRoleModal';
import OperateModal from 'components/Modal/OperateModal/index';
import ExportModal from './comps/ExportModal';
import ListOperation from 'components/ListOperation/home';
import './home.less';

const H5Authority = (props) => {
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [FormModalParams, setFormModalParams] = useState({ visible: false });
  const [ViewRoleModalParams, setViewRoleModalParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [exportParams, setExportParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.employeeTagId - b.employeeTagId,
    },
    {
      title: '员工标签',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>,
    },
    {
      title: '权限描述',
      width: '160px',
      dataIndex: 'description',
      key: 'description',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>,
    },
    {
      title: '账号数量',
      width: '160px',
      dataIndex: 'accountCount',
      key: 'accountCount',
      align: 'center',
      sorter: (a, b) => a.accountCount - b.accountCount
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleView(record), name: "查看" },
        ];
        if (record.accountCount <= 0) {
          opts.push({ onClick: () => handleDelete(record), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
    };
    apiCall('/employee_tag_menu/page', 'GET', data).then((res) => {
      const { records, current, size, total, pages } = res;
      setDataSource(records);
      setPaginations({
        current: current,
        pageSize: size,
        total: total,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 导出
  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false });
      }
    });
  };

  const handleAdd = () => {
    setFormModalParams({
      visible: true,
      onOk: (params) => {
        setFormModalParams({ visible: false });
        message.success('新增成功！');
        fetchList();
      },
      onCancel: () => {
        setFormModalParams({ visible: false });
      }
    });
  };

  const handleEdit = (record) => {
    setFormModalParams({
      visible: true,
      item: record,
      onOk: () => {
        setFormModalParams({ visible: false });
        message.success('修改成功！');
        fetchList();
      },
      onCancel: () => {
        setFormModalParams({ visible: false });
      }
    });
  };

  const handleDelete = (record) => {
    const { employeeTagId } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: '您确认要删除这个员工标签吗？',
      onSubmit: () => {
        apiCall(`/employee_tag_menu/del?id=${employeeTagId}`, 'DELETE').then((res) => {
          message.success('删除成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false
            })
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false
        })
      },
    });
  };

  const handleView = (record) => {
    setViewRoleModalParams({
      visible: true,
      item: record,
      onCancel: () => {
        setViewRoleModalParams({ visible: false });
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='H5Authority'>
      <Row justify="end" gutter={24}>
        <Col><Button type="primary" onClick={() => handleExport()}>导出</Button></Col>
        <Col><Button type="primary" onClick={() => handleAdd()}>新增权限</Button></Col>
      </Row>
      <Card bordered={false}>
        <Table rowKey="employeeTagId" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
      <FormModal params={FormModalParams} />
      <ViewRoleModal params={ViewRoleModalParams} />
      <OperateModal params={operateParams} />
      {/* 导出对话框 */}
      <ExportModal params={exportParams} />
    </div>
  );
};

export default H5Authority;
