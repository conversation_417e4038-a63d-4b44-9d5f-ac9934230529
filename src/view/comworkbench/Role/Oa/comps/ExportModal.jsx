/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/04/28 16:33
 * @LastEditTime: 2025/08/04 14:04
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/Oa/comps/ExportModal.jsx
 * @Description: ''
 */

import React, { useEffect, useState } from "react"
import { Form, Modal, Spin, Button, Image, Row, Col } from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"

const Template = (props) => {
  const { visible = false, onOk, onCancel } = props.params
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [previewVisible, setPreviewVisible] = useState(false)
  const previewImage = require("images/移动端标签员工数.png")

  useEffect(() => {
    if (visible) {
    } else {
      formForm.resetFields()
      setLoading(false)
      setConfirmLoading(false)
    }
  }, [visible])

  const handleOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true)
      const data = {
        employeeTagType: "OA",
        ...formData,
      }
      apiCall("/employee_tag/export", "POST", data, null, {
        isExit: true,
        title: `OA端标签员工数.${moment().format("YYYY-MM-DD")}.xlsx`,
      })
        .then((res) => {
          onOk?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    })
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Modal
      className="Rule-Oa-ExportModal-Container"
      title="导出OA端标签员工数"
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      footer={
        <Row
          justify="space-between"
          style={{ width: "100%", alignItems: "center" }}
        >
          <Col>
            <a onClick={() => setPreviewVisible(true)}>报表示例图</a>
            <Image
              style={{ display: "none" }}
              preview={{
                visible: previewVisible,
                src: previewImage,
                onVisibleChange: (value) => setPreviewVisible(value),
              }}
            />
          </Col>
          <Col>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" loading={confirmLoading} onClick={handleOk}>
              导出
            </Button>
          </Col>
        </Row>
      }
    >
      <Spin spinning={loading}>
        <Form form={formForm}>
          <Form.Item
            label="统计对象"
            name="depEmployeeIdList"
            extra="统计部门内所有OA端含有员工标签的员工数量，统计对象只支持选择部门"
            rules={[{ required: true, message: "请选择统计对象" }]}
          >
            <ETypeTransferModal
              title="选择对象"
              onlyDepartment
              needDisableDepFlag={false}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  )
}

export default Template
