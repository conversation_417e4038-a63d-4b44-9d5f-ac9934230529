/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/07/22 15:09
 * @LastEditTime: 2025/08/01 15:40
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/Oa/comps/FormModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from "react"
import { Form, Modal, Input, Tree, Select, Spin } from "antd"
import { apiCall } from "common/utils"
import { mapMenuList, getNonLeafMenuIds } from "../../config"
import { flatten } from "common/tree"

const { TextArea } = Input
const layout = {
  labelCol: { span: 5 },
}

const Template = (props) => {
  const { visible = false, item = null, onOk, onCancel } = props.params
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [employeeTagOptions, setEmployeeTagOptions] = useState([])
  const [menuList, setMenuList] = useState([])
  const [menuIds, setMenuIds] = useState([])

  useEffect(async () => {
    if (visible) {
      getEmployeeTagOption()
      getMenuList()
    } else {
      formForm?.resetFields()
      setLoading(false)
      setConfirmLoading(false)
      setEmployeeTagOptions([])
      setMenuList([])
      setMenuIds([])
    }
  }, [visible])

  useEffect(async () => {
    if (menuList.length && item) {
      getInfo()
    }
  }, [menuList])

  // 获取员工标签
  const getEmployeeTagOption = async () => {
    setLoading(true)
    await apiCall("/employee_tag/option?filter=true", "GET", {
      employeeTagType: "OA",
    })
      .then((res) => {
        setEmployeeTagOptions(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取菜单树
  const getMenuList = async () => {
    setLoading(true)
    await apiCall("/menu/getMenuList", "GET", { type: "OA" })
      .then((res) => {
        setMenuList([...mapMenuList(res)])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getInfo = async () => {
    setLoading(true)
    const data = {
      id: item.employeeTagId,
      employeeTagType: "OA",
    }
    await apiCall("/employee_tag_menu/get", "GET", data)
      .then((res) => {
        const { employeeTagId, description, menuIds } = res
        const nonLeafMenuIds = getNonLeafMenuIds(menuList)
        const leafMenuIds = menuIds.filter((id) => !nonLeafMenuIds.includes(id))
        setMenuIds(leafMenuIds)
        formForm.setFieldsValue({
          employeeTagId,
          description,
          menuIds: leafMenuIds,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true)
      let apiUrl = ""
      if (item) {
        apiUrl = `/employee_tag_menu/modify`
      } else {
        apiUrl = "/employee_tag_menu/add"
      }

      let { menuIds } = formData
      const flatMenuList = flatten(menuList, "childMenuList")
      flatMenuList.forEach((item) => {
        if (menuIds.includes(item.menuId)) {
          menuIds = [...new Set([...menuIds, ...(item.parentIds || [])])]
        }
      })
      const updatedFormData = { ...formData, menuIds }

      const data = {
        employeeTagType: "OA",
        id: item?.id || null,
        ...updatedFormData,
      }
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          onOk?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    })
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Modal
      className="Rule-Oa-FormModal-Container"
      title={item ? "编辑权限" : "新增权限"}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Spin spinning={loading}>
        <Form form={formForm} {...layout}>
          <Form.Item
            name="employeeTagId"
            label="员工标签"
            rules={[{ required: true, message: "请选择员工标签" }]}
            extra="员工标签不可重复选择"
          >
            <Select
              placeholder="请选择员工标签"
              fieldNames={{ label: "name", value: "id" }}
              options={employeeTagOptions}
              allowClear
              showSearch
              showArrow
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item label="权限描述" name="description">
            <TextArea
              placeholder="请输入权限描述（50字内）"
              allowClear
              showCount
              maxLength={50}
              autoSize={{ minRows: 4, maxRows: 6 }}
            />
          </Form.Item>

          {menuList?.length ? (
            <Form.Item
              label="角色菜单"
              name="menuIds"
              rules={[{ required: true, message: "请选择角色菜单" }]}
            >
              <Tree
                checkable
                defaultExpandAll
                checkStrictly
                selectable={false}
                style={{ border: "1px solid #d9d9d9", borderRadius: "2px" }}
                height={300}
                fieldNames={{
                  title: "name",
                  key: "menuId",
                  children: "childMenuList",
                }}
                treeData={menuList}
                checkedKeys={menuIds}
                onCheck={(checkedKeys) => {
                  const { checked, halfChecked } = checkedKeys
                  setMenuIds(checked)
                  formForm.setFieldsValue({
                    menuIds: checked,
                  })
                }}
              />
            </Form.Item>
          ) : null}
        </Form>
      </Spin>
    </Modal>
  )
}

export default Template
