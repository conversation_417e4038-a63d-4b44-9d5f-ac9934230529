/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/05/27 17:22
 * @LastEditTime: 2025/08/04 14:03
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/Oa/comps/ViewModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from "react"
import { apiCall } from "common/utils"
import { Spin, Modal, Tree, Descriptions } from "antd"
import { filterMenuTreeByIds } from "../../config"

const Template = (props) => {
  const { visible = false, item = null, onCancel } = props.params
  const [loading, setLoading] = useState(false)
  const [info, setInfo] = useState(null)
  const [menuList, setMenuList] = useState([])

  useEffect(async () => {
    if (visible) {
      item && getInfo()
    } else {
      setLoading(false)
      setInfo(null)
      setMenuList([])
    }
  }, [visible])

  useEffect(async () => {
    info && getMenuList()
  }, [info])

  // 获取菜单树
  const getMenuList = async () => {
    setLoading(true)
    await apiCall("/menu/getMenuList", "GET", { type: "OA" })
      .then((res) => {
        setMenuList(filterMenuTreeByIds(res, info?.menuIds))
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getInfo = async () => {
    setLoading(true)
    const data = {
      id: item.employeeTagId,
      employeeTagType: "OA",
    }
    await apiCall("/employee_tag_menu/get", "GET", data)
      .then((res) => {
        setInfo(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Modal
      className="Rule-Oa-ViewModal-Container"
      title="查看权限"
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      footer={null}
      onCancel={handleCancel}
    >
      <Spin spinning={loading}>
        <Descriptions column={1}>
          <Descriptions.Item label="员工标签">{info?.name}</Descriptions.Item>
          <Descriptions.Item
            label="权限描述"
            contentStyle={{ whiteSpace: "break-spaces" }}
          >
            {info?.description}
          </Descriptions.Item>
          <Descriptions.Item label="角色菜单">
            {menuList?.length > 0 ? (
              <Tree
                defaultExpandAll
                selectable={false}
                rootStyle={{ width: "100%" }}
                height={300}
                fieldNames={{
                  title: "name",
                  key: "menuId",
                  children: "childMenuList",
                }}
                treeData={menuList}
              />
            ) : null}
          </Descriptions.Item>
        </Descriptions>
      </Spin>
    </Modal>
  )
}

export default Template
