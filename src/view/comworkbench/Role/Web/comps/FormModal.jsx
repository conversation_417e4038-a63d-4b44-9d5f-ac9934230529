/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/07/22 15:09
 * @LastEditTime: 2025/08/04 14:00
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/Web/comps/FormModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from "react"
import { Form, Modal, Input, Tree, Spin } from "antd"
import { apiCall } from "common/utils"
import { mapMenuList, getNonLeafMenuIds } from "../../config"
import { flatten } from "common/tree"
import { removeInputEmpty } from "common/regular"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"

const { TextArea } = Input
const layout = {
  labelCol: { span: 5 },
}

const Template = (props) => {
  const { visible = false, item = null, onOk, onCancel } = props.params
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [menuList, setMenuList] = useState([])
  const [menuIds, setMenuIds] = useState([])
  const [roleDataType, setRoleDataType] = useState("")

  useEffect(async () => {
    if (visible) {
      getMenuList()
    } else {
      formForm?.resetFields()
      setLoading(false)
      setConfirmLoading(false)
      setMenuList([])
      setMenuIds([])
      setRoleDataType("")
    }
  }, [visible])

  useEffect(async () => {
    if (menuList.length && item) {
      getInfo()
    }
  }, [menuList])

  // 获取菜单树
  const getMenuList = async () => {
    setLoading(true)
    await apiCall("/menu/getMenuList", "GET", { type: "WEB" })
      .then((res) => {
        setMenuList([...mapMenuList(res)])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getInfo = async () => {
    setLoading(true)
    const data = {
      id: item.id,
    }
    await apiCall("/role/get", "GET", data)
      .then((res) => {
        const {
          name,
          description,
          assignDepartmentIdList,
          roleDataType,
          type,
          menuIds,
        } = res
        const nonLeafMenuIds = getNonLeafMenuIds(menuList)
        const leafMenuIds = menuIds.filter((id) => !nonLeafMenuIds.includes(id))
        setMenuIds(leafMenuIds)
        setRoleDataType(String(roleDataType))
        formForm.setFieldsValue({
          name,
          description,
          assignDepartmentIdList,
          roleDataType: String(roleDataType),
          type: String(type),
          menuIds: leafMenuIds,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true)
      let apiUrl = ""
      if (item) {
        apiUrl = `/role/modify`
      } else {
        apiUrl = "/role/add"
      }

      let { menuIds } = formData
      const flatMenuList = flatten(menuList, "childMenuList")
      flatMenuList.forEach((item) => {
        if (menuIds.includes(item.menuId)) {
          menuIds = [...new Set([...menuIds, ...(item.parentIds || [])])]
        }
      })
      const updatedFormData = { ...formData, menuIds }
      const data = {
        id: item?.id || null,
        ...updatedFormData,
      }
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          onOk?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    })
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Modal
      className="Rule-Web-FormModal-Container"
      title={item ? "编辑角色" : "新增角色"}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Spin spinning={loading}>
        <Form form={formForm} {...layout}>
          <Form.Item
            label="角色名称"
            name="name"
            rules={[{ required: true, message: "请输入角色名称" }]}
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input allowClear maxLength={50} placeholder="请输入角色名称" />
          </Form.Item>

          <Form.Item label="角色描述" name="description">
            <TextArea
              placeholder="请输入角色描述（50字内）"
              allowClear
              showCount
              maxLength={50}
              autoSize={{ minRows: 4, maxRows: 6 }}
            />
          </Form.Item>

          <Form.Item
            label="数据权限"
            name="roleDataType"
            rules={[{ required: true, message: "请选择数据权限" }]}
            extra={
              roleDataType == "5" ? (
                <Form.Item
                  style={{ margin: "8px 0 0 0" }}
                  name="assignDepartmentIdList"
                  rules={[{ required: true, message: "请选择特定部门" }]}
                >
                  <ETypeTransferModal
                    title="特定部门"
                    onlyDepartment
                    onlyEmployee={false}
                    needDisableDepFlag={false}
                    multiple
                  />
                </Form.Item>
              ) : null
            }
          >
            <SysDictSelect
              dataset="ROLEDATATYPE"
              placeholder="请选择数据权限"
              onChange={(value) => {
                setRoleDataType(value)
              }}
            />
          </Form.Item>

          <Form.Item label="角色类型" name="type" initialValue={"7"}>
            <SysDictSelect
              dataset="ROLETYPE"
              placeholder="请选择角色类型"
              disabled={true}
              // bordered={false}
            />
          </Form.Item>

          {menuList?.length ? (
            <Form.Item
              label="角色菜单"
              name="menuIds"
              rules={[{ required: true, message: "请选择角色菜单" }]}
            >
              <Tree
                checkable
                defaultExpandAll
                checkStrictly
                selectable={false}
                style={{ border: "1px solid #d9d9d9", borderRadius: "2px" }}
                height={300}
                fieldNames={{
                  title: "name",
                  key: "menuId",
                  children: "childMenuList",
                }}
                treeData={menuList}
                checkedKeys={menuIds}
                onCheck={(checkedKeys) => {
                  const { checked, halfChecked } = checkedKeys
                  setMenuIds(checked)
                  formForm.setFieldsValue({
                    menuIds: checked,
                  })
                }}
              />
            </Form.Item>
          ) : null}
        </Form>
      </Spin>
    </Modal>
  )
}

export default Template
