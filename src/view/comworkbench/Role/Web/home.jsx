/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/07/30 10:19
 * @LastEditTime: 2025/08/01 17:39
 * @LastEditors: <PERSON>ei<PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/Web/home.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from "react"
import { Row, Col, Button, Table, Tooltip, message } from "antd"
import { apiCall } from "common/utils"
import ListOperation from "components/ListOperation/home"
import FormModal from "./comps/FormModal"
import ViewModal from "./comps/ViewModal"
import ExportModal from "./comps/ExportModal"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"

const Template = (props) => {
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "角色名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "角色类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value) => <SysDictLabel dataset="ROLETYPE" dictkey={value} />,
    },
    {
      title: "角色描述",
      width: "160px",
      dataIndex: "description",
      key: "description",
      ellipsis: "true",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "账号数量",
      width: "160px",
      dataIndex: "userCount",
      key: "userCount",
      align: "center",
      sorter: (a, b) => a.userCount - b.userCount,
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [{ onClick: () => handleView(record), name: "查看" }]
        if (record.type == 7) {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" })
        }
        if (record.type == 7 && record.userCount <= 0) {
          opts.push({ onClick: () => handleDelete(record), name: "删除" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [formModalParams, setFormModalParams] = useState({ visible: false })
  const [viewRoleModalParams, setViewRoleModalParams] = useState({
    visible: false,
  })
  const [exportModalParams, setExportModalParams] = useState({
    visible: false,
  })
  const [operateParams, setOperateParams] = useState({ visible: false })

  useEffect(() => {
    fetchList()
  }, [])

  const fetchList = (params = {}) => {
    setLoading(true)
    const { pagination } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
    }
    apiCall("/role/page", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        setDataSource(records)
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const handleExport = () => {
    setExportModalParams({
      visible: true,
      onCancel: () => {
        setExportModalParams({ visible: false })
      },
    })
  }

  const handleAdd = () => {
    setFormModalParams({
      visible: true,
      onOk: (params) => {
        message.success("新增成功！")
        setFormModalParams({ visible: false })
        fetchList()
      },
      onCancel: () => {
        setFormModalParams({ visible: false })
      },
    })
  }

  const handleEdit = (record) => {
    setFormModalParams({
      visible: true,
      item: record,
      onOk: () => {
        message.success("编辑成功！")
        setFormModalParams({ visible: false })
        fetchList()
      },
      onCancel: () => {
        setFormModalParams({ visible: false })
      },
    })
  }

  const handleDelete = (record) => {
    const { id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: "您确认要删除这个角色吗？",
      onSubmit: () => {
        apiCall(`/role/del?id=${id}`, "DELETE")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleView = (record) => {
    setViewRoleModalParams({
      visible: true,
      item: record,
      onCancel: () => {
        setViewRoleModalParams({ visible: false })
      },
    })
  }

  return (
    <div className="Rule-Web-Container">
      <Row justify="end" gutter={16}>
        <Col>
          <Button type="primary" onClick={() => handleExport()}>
            导出
          </Button>
        </Col>
        <Col>
          <Button type="primary" onClick={() => handleAdd()}>
            新增权限
          </Button>
        </Col>
      </Row>
      <br />
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        scroll={{ x: 1300 }}
        pagination={paginations}
        onChange={onChangeTable}
      />
      <FormModal params={formModalParams} />
      <ViewModal params={viewRoleModalParams} />
      <ExportModal params={exportModalParams} />
      <OperateModal params={operateParams} />
    </div>
  )
}

export default Template
