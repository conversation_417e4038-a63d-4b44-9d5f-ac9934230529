/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2023/04/19 11:14
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/WebAuthority/comps/ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Tabs, Form, message, Modal, Spin, DatePicker, Button, Image } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './ExportModal.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
import {FileHOC} from 'components/FileHOC/FileHOC';
const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState('1');
  const [imageVisible, setImageVisible] = useState(false);
  const ImagePreviewList = [require('images/后台部门角色员工数.png')];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ...formData
      };
      apiCall('/role/export', 'POST', data, null, { isExit: true, title: `后台角色员工数据.${moment().format('YYYY-MM-DD')}.xlsx` }).then((res) => {
        message.success('导出成功！');
        onCancel();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className='CustomerTag-ExportModal'
      visible={visible}
      title='导出后台角色员工数据'
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
              style={{ display: 'none' }}
              preview={{
                visible: imageVisible,
                src: url,
                onVisibleChange: (value) => {
                  setImageVisible(value);
                },
              }}
            />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button key="submit" type="primary" loading={confirmLoading} onClick={onOk}>
            导出
          </Button>
        </div>
      ]}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>

            <Tabs.TabPane tab="后台角色员工数据" key="1">
              <p className='tips'>统计后台所有用户角色的员工数量，统计对象只支持选择部门</p>
              <FormItem label="统计对象" name="depEmployeeIdList" extra="支持选择部门" rules={[{ required: true, message: '请选择统计对象' }]}>
                <ETypeTransferModal title="选择对象" onlyDepartment needDisableDepFlag={false} />
              </FormItem>
            </Tabs.TabPane>

          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
