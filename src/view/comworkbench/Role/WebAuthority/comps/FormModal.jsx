import React from 'react';
import Reflux from 'reflux';
import { Form, Input, message, Modal, Spin, Tree } from 'antd';
import { restOPCall } from 'common/utils';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import { removeInputEmpty } from 'common/regular';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import { flatten } from 'common/tree';

const FormItem = Form.Item;
const { TextArea } = Input;

class FormModal extends Reflux.Component {
  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'role';
    this.state = {
      visible: false,
      confirmLoading: false,
      menuIds: null,
      menuList: null,
      menuIdsKeys: null,
      excludeKeys: null,
      data: null,
      dataAuthority: null,
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps, prevState);
  }

  didUpdate = (prevProps, prevState) => {
    const { ts, visible, updateId, menuList } = this.props.params;
    const { excludeKeys } = this.state;
    if (ts != prevProps.params.ts) {
      let excludeKeys = [];
      let newMenuList = this.mapMenuList(menuList, excludeKeys);
      this.setState({
        visible: visible,
        menuList: newMenuList,
        excludeKeys: excludeKeys,
      });
      if (updateId) {
        let data = { id: updateId };
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            retdata.roleDataType = retdata.roleDataType + '';
            const menuIds = retdata.menuIds?.filter(
              (id) => excludeKeys.indexOf(id) < 0
            );
            this.setState({
              data: retdata,
              menuIds,
              menuIdsKeys: retdata.menuIds,
              dataAuthority: retdata.roleDataType == '5' ? 5 : null,
            });
          })
          .catch((err) => {
            console.log(err);
            this.onCancel();
          });
      }
    }
    if (this.state.data?.id != prevState.data?.id) {
      this.formRef.current?.setFieldsValue({
        ...this.state.data,
        dataAuthority: this.state.data?.roleDataType,
        menuList: this.state.data?.menuIds,
      });
    }
  };

  onMenuCheck = (checkedKeys, e) => {
    const { checked, halfChecked } = checkedKeys;
    this.setState({
      menuIds: checked,
      // menuIdsKeys: checkedKeys.concat(e.halfCheckedKeys),
      menuIdsKeys: checked,
    });
    this.formRef.current?.setFieldsValue({
      menuList: checked,
    });
  };

  // 去重
  noRepeat = (arr) => {
    var newArr = [...new Set(arr)];
    return newArr
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current
      .validateFields()
      .then((formData) => {
        this.setState({ confirmLoading: true });
        const { updateId } = this.props.params;
        let { menuList, menuIdsKeys, dataAuthority } = this.state;

        if (
          dataAuthority == 5 &&
          (!formData.assignDepartmentIdList ||
            formData.assignDepartmentIdList.length <= 0)
        ) {
          message.error('请选择部门！');
          return false;
        }

        flatten(menuList).forEach((item) => {
          if (menuIdsKeys.indexOf(item.key) > -1) {
            menuIdsKeys = this.noRepeat([...menuIdsKeys, ...item.parentIds])
          }
        })

        const data = {
          id: updateId ? updateId : null,
          ...formData,
          menuIds: menuIdsKeys,
        };
        restOPCall(this.objName, updateId ? 'update' : 'add', data)
          .then(() => {
            this.setState({ visible: false });
            this.props.params?.onUpdated?.();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            this.onCancel();
            this.setState({ confirmLoading: false });
          });
      })
      .catch((err) => {
        console.log('errorInfo', err);
      })
      .finally(() => {
        this.setState({ confirmLoading: false });
      });
  };

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
      menuIds: null,
      menuList: null,
      menuIdsKeys: null,
      excludeKeys: null,
      data: null,
      dataAuthority: null,
    });
    this.props.callback?.onCancel?.();
    // 点击取消后 -> 重置表单内容
    this.formRef.current.setFieldsValue({});
  };

  mapMenuList (menuList, excludeKeys) {
    if (!menuList) {
      return null;
    }
    let list = [];
    menuList.forEach((menu) => {
      let childList = menu.childMenuList;
      if (childList && childList.length > 0) {
        excludeKeys.push(menu.menuId);
        childList = this.mapMenuList(childList, excludeKeys);
      }
      list.push({
        ...menu,
        title: menu.name,
        key: menu.menuId,
        children: childList,
        checkable: childList && childList.length > 0 ? false : true,
      });
    });
    return list;
  }

  render () {
    const { visible, confirmLoading, data, menuList, menuIds, dataAuthority } =
      this.state;
    const { updateId } = this.props.params;

    return (
      <Modal
        visible={visible}
        title={updateId ? '修改角色' : '新增角色'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        centered
        confirmLoading={confirmLoading}
        okText={updateId ? '修改' : '新增'}
        cancelText="取消"
        width={600}
      >
        {updateId && data == null ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form
            layout="horizontal"
            ref={this.formRef}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}
          >
            <FormItem
              label="角色名称"
              name="name"
              rules={[{ required: true, message: '角色名称必填' }]}
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input allowClear maxLength={50} placeholder="请输入角色名称" />
            </FormItem>
            <FormItem
              label="角色描述"
              name="description"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <TextArea
                placeholder="请输入角色描述（50字内）"
                allowClear
                showCount
                maxLength={50}
                autoSize={{ minRows: 2, maxRows: 7 }}
              />
            </FormItem>
            <FormItem
              label="数据权限"
              name="dataAuthority"
              rules={[{ required: true, message: '数据权限必填' }]}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <FormItem name="roleDataType" style={{ marginBottom: '0px' }}>
                  <SysDictSelect
                    dataset="ROLEDATATYPE"
                    placeholder="请选择"
                    style={{ width: '200px' }}
                    onChange={(value) => {
                      this.setState({
                        dataAuthority: value,
                      });
                      this.formRef.current?.setFieldsValue({
                        dataAuthority: value,
                      });
                    }}
                  />
                </FormItem>
                {dataAuthority == 5 && (
                  <FormItem
                    name="assignDepartmentIdList"
                    style={{ maxWidth: '150px', marginBottom: '0px' }}
                  >
                    <ETypeTransferModal
                      title="选择部门"
                      onlyDepartment
                      onlyEmployee={false}
                      needDisableDepFlag={false}
                      multiple
                      onChange={(value, options) => { }}
                    />
                  </FormItem>
                )}
              </div>
            </FormItem>
            <FormItem
              label="角色类型"
              name="type"
              initialValue={7}
              rules={[{ required: true }]}
            >
              {updateId ? (
                <SysDictLabel dataset="ROLETYPE" dictkey={data.type} />
              ) : (
                <span>自定义角色</span>
              )}
            </FormItem>
            <FormItem
              label="角色菜单"
              name="menuList"
              rules={[{ required: true, message: '请选择角色菜单' }]}
            >
              <Tree
                checkable
                blockNode
                defaultExpandAll
                checkStrictly
                onCheck={this.onMenuCheck}
                selectable={false}
                checkedKeys={menuIds}
                treeData={menuList}
                height={300}
                style={{ border: '1px solid #d9d9d9', borderRadius: '2px' }}
              />
            </FormItem>
          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
