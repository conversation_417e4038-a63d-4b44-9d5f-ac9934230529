import React from 'react';
import Reflux from 'reflux';
import { Descriptions, Modal, Tree } from 'antd';
import { restOPCall } from 'common/utils';
import SysDictLabel from 'components/select/SysDictLabel';

class ViewRoleModal extends Reflux.Component {
  constructor(props) {
    super(props);
    this.objName = 'role';
    this.rowKey = 'id';
    this.state = {
      menuIds: [],
      menuList: [],
      visible: false,
      assignDepartmentIdNames: [],
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    const { data, visible, ts } = this.props.params;
    if (ts != prevProps.params.ts) {
      restOPCall(this.objName, 'get', { [this.rowKey]: data.id }).then(
        (res) => {
          this.setState({
            menuIds: res.menuIds,
            menuList: res.menus,
            visible,
            assignDepartmentIdNames: res.assignDepartmentIdNames,
          });
        }
      );
    }
  }

  filterAndMapMenuList (menuList, menuIds) {
    if (!menuList) {
      return null;
    }
    let list = [];
    menuList.forEach((menu) => {
      let childList = menu.childMenuList;
      if (!menuIds?.includes(menu.menuId)) {
        return false;
      }
      if (childList && childList.length > 0) {
        childList = this.filterAndMapMenuList(childList, menuIds);
      }
      list.push({
        title: menu.name,
        key: menu.menuId,
        children: childList,
      });
    });
    return list;
  }

  onCancel = () => {
    this.setState({
      menuIds: [],
      visible: false,
    });
  };

  render () {
    const { menuIds, visible, menuList, assignDepartmentIdNames } = this.state;
    const data = this.props.params?.data || {};
    // const menuList = this.filterAndMapMenuList(
    //   this.props.params.menuList,
    //   menuIds
    // );
    return (
      <Modal
        visible={visible}
        title="查看角色"
        width={600}
        footer={null}
        destroyOnClose
        onCancel={this.onCancel}
      >
        <Descriptions column={1}>
          <Descriptions.Item label="角色名称">{data.name}</Descriptions.Item>
          <Descriptions.Item label="角色描述">
            {data.description}
          </Descriptions.Item>
          <Descriptions.Item label="数据权限">
            <SysDictLabel
              dataset="ROLEDATATYPE"
              dictkey={data.roleDataType + ''}
              color
            />
            <span style={{ marginLeft: '20px' }}>
              {assignDepartmentIdNames?.join('、')}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="角色类型">
            <SysDictLabel dataset="ROLETYPE" dictkey={data.type} />
          </Descriptions.Item>
          <Descriptions.Item label="角色菜单">
            {menuList && menuList.length > 0 ? (
              <Tree fieldNames={{ title: 'name', key: 'menuId', children: 'childMenuList' }} defaultExpandAll treeData={menuList} />
            ) : null}
          </Descriptions.Item>
        </Descriptions>
      </Modal>
    );
  }
}

export default ViewRoleModal;
