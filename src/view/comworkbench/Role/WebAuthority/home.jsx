/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/23 15:06
 * @LastEditTime: 2023/11/21 10:41
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/WebAuthority/home.jsx
 * @Description: '后台权限'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import {
  Button,
  Card,
  Col,
  message,
  Row,
  Table,
  Tooltip,
} from 'antd';
import { apiCall, restOPCall } from 'common/utils';
import moment from 'moment';
import SysDictLabel from 'components/select/SysDictLabel';
import OperateModal from 'components/Modal/OperateModal/index';
import ViewRoleModal from './comps/ViewRoleModal';
import FormModal from './comps/FormModal';
import ExportModal from './comps/ExportModal';
import ListOperation from 'components/ListOperation/home';
import './home.less';
class WebAuthority extends Reflux.Component {
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'role';
    this.rowKey = 'id';
    this.state = {
      FormModalParams: { visible: false },
      ViewRoleModalParams: { visible: false },
      dataSource: null,
      pagination: { current: 1, pageSize: 10 },
      loading: false,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '角色名称',
          width: '160px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '角色类型',
          width: '160px',
          dataIndex: 'type',
          key: 'type',
          align: 'center',
          render: (value) => <SysDictLabel dataset="ROLETYPE" dictkey={value} />
        },
        {
          title: '角色描述',
          width: '160px',
          dataIndex: 'description',
          key: 'description',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '账号数量',
          width: '160px',
          dataIndex: 'userCount',
          key: 'userCount',
          align: 'center',
        },
        {
          title: '操作',
          width: '120px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => {
            let opts = [
              { onClick: () => this.onView(record), name: "查看" }
            ];
            if (record.type == 7) {
              opts.push({ onClick: () => this.onEdit(record), name: "编辑" });
            }
            if (record.type == 7 && record.userCount <= 0) {
              opts.push({ onClick: () => this.onDel(record), name: "删除" });
            }
            return <ListOperation opts={opts} />;
          },
        },
      ],
      menuList: null,
      h5MenuList: null,
      OperateModalParams: { visible: false },
      exportParams: { visible: false }
    };
  }

  async componentDidMount () {
    await this.getMenuList();
    await this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    await restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
        loading: false
      });
    });
  }

  getMenuList = async () => {
    this.setState({ loading: true });
    await apiCall('menu/getMenusList', 'GET').then((res) => {
      this.setState({ menuList: res });
    });
  }

  onRefresh = () => {
    this.fetchList();
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  // 导出
  handleExport = () => {
    this.setState({
      exportParams: {
        visible: true,
        onCancel: () => {
          this.setState({
            exportParams: { visible: false }
          });
        }
      }
    });
  }

  onAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        h5MenuList: this.state.h5MenuList,
        menuList: this.state.menuList,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  onEdit = (record) => {
    this.setState({
      FormModalParams: {
        visible: true,
        menuList: this.state.menuList,
        h5MenuList: this.state.h5MenuList,
        updateId: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('编辑成功');
          this.fetchList();
        },
      }
    });
  }

  onView = (record) => {
    this.setState({
      ViewRoleModalParams: {
        visible: true,
        data: record,
        menuList: this.state.menuList,
        h5MenuList: this.state.h5MenuList,
        ts: parseInt(moment().format('X')),
      }
    });
  }

  onDel = (record) => {
    this.setState({
      OperateModalParams: {
        visible: true,
        content: '您确认要删除这个角色吗？',
        onSubmit: () => {
          const data = {
            id: record.id
          };
          restOPCall(this.objName, 'del', data).then(() => {
            message.success('删除成功！');
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            }).finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, loading, columns, FormModalParams, ViewRoleModalParams, OperateModalParams, exportParams } = this.state;

    return (
      <div className='WebAuthority'>
        <Row justify="end" gutter={24}>
          <Col><Button type="primary" onClick={this.handleExport}>导出</Button></Col>
          <Col><Button type="primary" onClick={this.onAdd}>新增角色</Button></Col>
        </Row>
        <Card bordered={false}>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange}></Table>
        </Card>
        <FormModal params={FormModalParams} />
        <ViewRoleModal params={ViewRoleModalParams} />
        <OperateModal params={OperateModalParams} />
        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
      </div>
    );
  }
}

export default WebAuthority;
