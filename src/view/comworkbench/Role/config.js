/**
 * @description: 递归菜单树，将叶子节点设置为可选中
 * @param {*} menuList
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/07/31 11:00
 */
export const mapMenuList = (menuList = []) => {
  if (!menuList?.length) return []

  return menuList.map((menu) => {
    const hasChildren =
      Array.isArray(menu.childMenuList) && menu.childMenuList.length > 0
    return {
      ...menu,
      checkable: !hasChildren,
      childMenuList: mapMenuList(menu.childMenuList || []),
    }
  })
}

/**
 * @description: 递归收集 childMenuList.length !== 0 的 menuId
 * @param {*} list
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/08/01 14:50
 */
export const getNonLeafMenuIds = (list) => {
  return list.flatMap((item) => {
    const hasChildren =
      Array.isArray(item.childMenuList) && item.childMenuList.length > 0
    return [
      ...(hasChildren ? [item.menuId] : []),
      ...(hasChildren ? getNonLeafMenuIds(item.childMenuList) : []),
    ]
  })
}


/**
 * @description: 递归菜单树，过滤匹配的叶子节点并保留父节点结构
 * @param {*} menuList
 * @param {*} menuIds
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2025/07/31 11:10
 */
export const filterMenuTreeByIds = (menuList = [], menuIds = []) => {
  if (!menuList?.length) return []

  return menuList
    .map((menu) => {
      const childList = filterMenuTreeByIds(menu.childMenuList || [], menuIds)
      const isLeaf = !(menu.childMenuList && menu.childMenuList.length)
      const isMatch = isLeaf && menuIds.includes(menu.menuId)

      if (isMatch || childList.length) {
        return {
          ...menu,
          childMenuList: childList,
        }
      }

      return null
    })
    .filter(Boolean)
}
