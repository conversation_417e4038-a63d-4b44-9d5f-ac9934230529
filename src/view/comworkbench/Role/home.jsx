/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 16:50
 * @LastEditTime: 2025/08/05 13:59
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/Role/home.jsx
 * @Description: '角色管理'
 */

import React, { useState, useEffect } from "react"
import { Card, Tabs } from "antd"
import { localGetItem, localSetItem } from "@/common/storage"
import { versionFnMap } from "config"
// 模块组件
// import WebAuthority from "./WebAuthority/home"
// import H5Authority from "./H5Authority/home"
import Web from "./Web/home"
import H5 from "./H5/home"
import Oa from "./Oa/home"

const Template = (props) => {
  const [index, setIndex] = useState("")

  useEffect(() => {}, [])

  return (
    <div className="Role-Container">
      <Card bordered={false}>
        <Tabs
          defaultActiveKey={localGetItem("TabsRoleActiveKey") || "1"}
          destroyInactiveTabPane
          onChange={(key) => localSetItem("TabsRoleActiveKey", key)}
        >
          <Tabs.TabPane tab="后台权限" key="1">
            <Web />
          </Tabs.TabPane>
          <Tabs.TabPane tab="移动端权限" key="2">
            <H5 />
          </Tabs.TabPane>
          {versionFnMap.system_ui().rule_oaFlag ? (
            <Tabs.TabPane tab="OA端权限" key="3">
              <Oa />
            </Tabs.TabPane>
          ) : null}
        </Tabs>
      </Card>
    </div>
  )
}

export default Template
