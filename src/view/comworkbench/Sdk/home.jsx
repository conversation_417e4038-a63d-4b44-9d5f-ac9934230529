/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/25 10:47
 * @LastEditTime: 2021/10/22 11:30
 * @LastEditors: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\comworkbench\Sdk\home.jsx
 * @Description: 'Sdk'
 */

import React, { PureComponent } from 'react';
import { apiCall } from 'common/utils';
import { Card, Spin, Form, Input, message, Button, Row, Col } from 'antd';

const FormItem = Form.Item;

export default class Sdk extends PureComponent {

  formRef = React.createRef();

  // 1.构建变量-初始化
  constructor (props) {
    super(props);
    this.state = {
      loading: false,
      id: null
    };
  }

  // 2.调用函数
  componentDidMount = async () => {
    this.getSdkData();
  }

  // 3.调用函数
  componentWillMount = async () => { }

  getSdkData () {
    this.setState({
      loading: true,
    });
    apiCall('/sdk/getOrSave', 'GET',).then((res) => {
      const { appKey, appSecret, accessToken, callBackUrl, id } = res;

      this.formRef.current.setFieldsValue({
        // appKey,
        // appSecret,
        accessToken,
        callBackUrl
      });
      this.setState({
        id,
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false,
        });
      });
  }

  handleSubmit = () => {
    this.formRef.current.validateFields().then((formData) => {
      this.setState({
        loading: true,
      });
      const { accessToken, callBackUrl } = formData;
      const { id } = this.state;
      const data = {
        accessToken,
        callBackUrl,
        id
      };
      apiCall('/sdk/modify', 'PUT', data).then((res) => {
        message.success('更新成功');
        this.getSdkData();
      })
        .catch((err) => {
          this.setState({
            loading: false,
          });
          console.log(err);
        })
        .finally(() => { });
    });
  }

  handleResetToken = () => {
    this.setState({
      loading: true,
    });
    apiCall('/sdk/getToken', 'GET').then((res) => {
      this.formRef.current.setFieldsValue({
        accessToken: res,
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false,
        });
      });
  }

  render () {
    const { loading } = this.state;

    return (
      <Spin spinning={loading} wrapperClassName="Sdk">
        <Card>
          <Form ref={this.formRef} labelCol={{ span: 3 }} wrapperCol={{ span: 8 }}>
            {/* <FormItem name='appKey' label="appKey" >
              <Input placeholder="appKey" disabled allowClear />
            </FormItem>
            <FormItem name='appSecret' label="appSecret" >
              <Input placeholder="appSecret" disabled allowClear />
            </FormItem> */}
            <FormItem label="accessToken">
              <Row justify={'space-between'}>
                <Col span={19}>
                  <FormItem name="accessToken" style={{ margin: 'unset' }}>
                    <Input placeholder="accessToken" disabled allowClear />
                  </FormItem>
                </Col>
                <Col>
                  <Button onClick={this.handleResetToken}>重置</Button>
                </Col>
              </Row>
            </FormItem>
            <FormItem name='callBackUrl' label="回调地址" >
              <Input placeholder="请输入回调地址" allowClear />
            </FormItem>
            <FormItem wrapperCol={{ offset: 4 }}>
              <Button type="primary" onClick={() => this.handleSubmit()}>保存</Button>
            </FormItem>
          </Form>
        </Card>
      </Spin>
    );
  }
}
