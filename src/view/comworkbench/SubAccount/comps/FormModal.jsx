/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/31 14:47
 * @LastEditTime: 2023/10/08 17:27
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/SubAccount/comps/FormModal.jsx
 * @Description: '账号对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { Form, Input, Modal, Row, Col, Button, Select, message } from 'antd';
import { apiCall, restOPCall, genUUID } from 'common/utils';
import SysDictSelect from 'components/select/SysDictSelect';
import { removeInputEmpty } from 'common/regular';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;

class FormModal extends Reflux.Component {
  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      roleType: null,
      shortDomain: null,
      roleOption: [],
      employeeOption: [],
      data: {},
    };
  }

  async componentWillUpdate (nextProps, nextState) {
    const { ts } = this.props.params;
    // 时间戳有更新, 属于外面传入
    if (ts != nextProps.params.ts) {
      await this.getRoleOption();
      await this.getEmployeeOption();
      if (nextProps.params.updateid) {
        const data = {
          id: nextProps.params.updateid,
        };
        await this.getUserVOById(data);
      }
      await this.setState({
        visible: nextProps.params.visible,
      });
    }
  }

  async getUserVOById (data) {
    await apiCall('user/getUserVOById', 'GET', data)
      .then(async (res) => {
        if (res.account.indexOf('@') != -1) {
          res.shortDomain = res.account.substring(res.account.indexOf('@') + 1);
          res.account = res.account.substring(0, res.account.indexOf('@'));
        }
        res.employeeId = res.employeeId ? [res.employeeId + ''] : [];
        this.setState({
          data: res,
          roleType: res.roleType,
        });
      })
      .catch((err) => {
        console.log(err);
        this.onCancel();
      });
  }

  async getRoleOption () {
    const data = {};
    await apiCall('/role/option', 'GET', data)
      .then((res) => {
        this.setState({
          roleOption: res.map((item) => ({
            label: item.name,
            value: JSON.stringify(item.id),
          })),
        });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  async getEmployeeOption () {
    const data = {
      // isCreateUser:true
    };
    await apiCall('/employee/option', 'GET', data)
      .then((res) => {
        this.setState({
          employeeOption: res.map((item) => ({
            label: item.name,
            value: JSON.stringify(item.id),
          })),
        });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  handleSubmit = async (e) => {
    const { User, shortDomain } = this.state;
    e.preventDefault();
    await this.formRef.current
      .validateFields()
      .then((formData) => {
        const { name, account, password, mobile, roleId, status, employeeId } =
          formData;
        const psd = window.btoa(password).split('')
          .reverse()
          .join('');
        let data = {
          name,
          account:
            account +
            (User?.shortDomain ? '@' + (shortDomain ?? User.shortDomain) : ''), // 补充域名信息
          password: password == 'NOT_CHANGE' ? 'NOT_CHANGE' : genUUID(8) + psd,
          mobile,
          roleId: parseInt(roleId),
          employeeId: parseInt(employeeId),
          status,
        };
        this.setState({ confirmLoading: true });
        const { updateid } = this.props.params;
        if (updateid) {
          // 更新账号
          data[this.rowKey] = updateid;
          restOPCall(this.objName, 'update', data)
            .then(() => {
              this.setState({
                visible: false,
                confirmLoading: false,
                data: {},
              });
              this.props.params?.onUpdated?.();
            })
            .catch((err) => {
              this.setState({ confirmLoading: false });
            });
        } else {
          // 新增账号
          restOPCall(this.objName, 'add', data)
            .then(() => {
              this.setState({
                visible: false,
                confirmLoading: false,
                data: {},
              });
              this.props.params?.onUpdated?.();
            })
            .catch((err) => {
              this.setState({ confirmLoading: false });
            });
        }
      })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  };

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
      roleType: null,
      shortDomain: null,
      roleOption: [],
      data: {},
    });
    this.formRef.current.resetFields();
  };

  render () {
    const { visible, User, confirmLoading, data, roleOption, employeeOption } =
      this.state;
    const { updateid } = this.props.params;
    // const isDisabled = !((!data) || User.roleType == 1 || (User.roleType == 2 && data.roleType != 2));
    const isDisabled = !(!data || User);

    return (
      <Modal
        visible={visible}
        title={updateid ? '修改账号' : '新增账号'}
        maskClosable={false}
        width={600}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          ref={this.formRef}
          initialValues={{
            name: data ? data.name : '',
            account: data ? data.account : '',
            shortDomain: data ? data.account : '',
            password: updateid ? 'NOT_CHANGE' : '',
            mobile: (data?.mobile ?? '') + '',
            // employeeId: (data?.employeeId ?? "") + "",
            employeeId: data?.employeeId ?? [],
            roleId: (data?.roleId ?? '') + '',
            status: (data?.status ?? '') + '',
          }}
        >
          <FormItem
            label="账号名称"
            rules={[{ required: true, message: '账号名称称必填' }]}
            name="name"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input allowClear maxLength="20" />
          </FormItem>

          <FormItem
            label="登录账号"
            rules={[
              { required: true, message: '账号必填' },
              {
                pattern: /^[A-Za-z0-9]{3,20}$/,
                message: '账号格式有误,只允许大小写字母数字组合,长度3-20位',
              },
            ]}
            name="account"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            {/* <input type="password" style={{ position: 'fixed', left: '-9999px' }} /> */}
            <Input
              allowClear
              disabled={isDisabled}
              suffix={
                data?.shortDomain
                  ? '@' + data?.shortDomain
                  : User.shortDomain
                    ? '@' + User.shortDomain
                    : ''
              }
            />
          </FormItem>

          <FormItem name="shortDomain" hidden />
          {/* { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[A-Za-z0-9]{8,16}$/, message: '密码必须同时包含大小写字母和数字，8-16位' } */}
          <FormItem
            name="password"
            label="登录密码"
            extra="密码必须同时包含大小写字母和数字，8-16位"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '登录密码必填' }]}
          >
            <Input.Password
              autocomplete="new-password"
              visibilityToggle={false}
              allowClear
            />
          </FormItem>

          <FormItem
            label="手机号码"
            name="mobile"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input allowClear maxLength="11" />
          </FormItem>

          <FormItem
            label="关联员工"
            rules={[{ required: true, message: '关联员工必选' }]}
            name="employeeId"
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              needExcludeDepFlag={false}
            />
            {/* <Select
              disabled={isDisabled}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
              options={employeeOption}
            /> */}
          </FormItem>

          <FormItem
            label="账号角色"
            rules={[{ required: true, message: '角色必选' }]}
            name="roleId"
          >
            <Select
              disabled={isDisabled}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
              options={roleOption}
            />
          </FormItem>

          <FormItem label="账号状态" name="status">
            <SysDictSelect disabled={isDisabled} dataset="USERSTATUS" />
          </FormItem>
        </Form>
      </Modal>
    );
  }
}

export default FormModal;
