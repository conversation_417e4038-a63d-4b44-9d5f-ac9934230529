/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/23 15:06
 * @LastEditTime: 2023/11/21 16:08
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/comworkbench/SubAccount/home.jsx
 * @Description: '账号管理'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { Button, Card, Form, Input, message, Table, Tooltip } from 'antd';
import { apiCall, restOPCall } from 'common/utils';
import moment from 'moment';
import GeneralSelect from 'components/select/GeneralSelect';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal';
import { removeInputEmpty } from 'common/regular';
import { exportExcelFile } from 'common/xlsx';
import ListOperation from 'components/ListOperation/home';
import FilterBar from 'components/FilterBar/FilterBar';

const FormItem = Form.Item;

class SubAccount extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      FormModalParams: { visible: false },
      pagination: { current: 1, pageSize: 10 }, // 不需要翻页机制时，
      loading: false,
      query: {},
      selectedRowKeys: [],
      selectedRows: [],
      OperateModalParams: { visible: false },
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '登录账号',
          width: '220px',
          dataIndex: 'account',
          key: 'account',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '账号名称',
          width: '160px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '账号手机号',
          width: '160px',
          dataIndex: 'mobile',
          key: 'mobile',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '关联员工',
          width: '160px',
          dataIndex: 'employeeName',
          key: 'employeeName',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        // {
        //   title: '微信绑定状态',
        //   width: '160px',
        //   dataIndex: 'wechatBinding',
        //   key: 'wechatBinding',
        //   align: 'center',
        //   ellipsis: 'true',
        //   render: (value) => <Tooltip placement='topLeft' title={<SysDictLabel dataset="WECHATBINDING" dictkey={value} />}>{<SysDictLabel dataset="WECHATBINDING" dictkey={value} color />}</Tooltip>
        // },
        // {
        //   title: '微信昵称',
        //   width: '160px',
        //   dataIndex: 'wechatName',
        //   key: 'wechatName',
        //   align: 'center',
        //   ellipsis: 'true',
        //   render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        // },
        {
          title: '账号角色',
          width: '160px',
          dataIndex: 'roleName',
          key: 'roleName',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement='topLeft' title={value}>{value}</Tooltip>
        },
        {
          title: '创建时间',
          width: '160px',
          dataIndex: 'createTime',
          key: 'createTime',
          align: 'center',
        },
        {
          title: '更新时间',
          width: '160px',
          dataIndex: 'updateTime',
          key: 'updateTime',
          align: 'center',
        },
        {
          title: '账号状态',
          width: '160px',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          render: (value) => <SysDictLabel dataset="USERSTATUS" dictkey={value + ''} color />
        },
        {
          title: '操作',
          width: '120px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => {
            let opts = [
              { onClick: () => this.handleEdit(record), name: "编辑" }
            ];
            // if (record.wechatBinding) {
            //   opts.push({ onClick: () => this.handleUnbindWx(record), name: "解绑微信" });
            // }
            if (record.roleType != 1 && record.roleType != 2) {
              if (record.status == 0) {
                opts.push({ onClick: () => this.handleChangeStatus(record, 1), name: "启用" });
              } else {
                opts.push({ onClick: () => this.handleChangeStatus(record, 0), name: "禁用" });
              }
              opts.push({ onClick: () => this.handleDel(record), name: "删除" });
            }
            return <ListOperation opts={opts} />;
          },
        },
      ],
    };
  }

  componentDidMount = () => {
    this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    let formInfo = {};
    await this.formRef.current.validateFields().then((formData) => {
      formInfo = {
        account: formData.account,
        employeeName: formData.employeeName,
        mobile: formData.mobile,
        roleId: formData.roleId || null,
        status: formData.status || null
      };
    });
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
      ...formInfo,
    };
    restOPCall(this.objName, 'getlist', data)
      .then(({ records, current, pages, size, total }) => {
        this.setState({
          dataSource: records,
          pagination: {
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
          }
        });
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  }

  handleReset = (e) => {
    this.formRef.current.resetFields();
    this.fetchList({ pagination: { current: 1, pageSize: 10 } });
  }

  handleQuery = async (e) => {
    this.fetchList();
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    this.setState({
      FormModalParams: {
        visible: true,
        roleType: record.roleType,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '删除该账号后，该账号所绑定的员工将无法登录本系统，与他相关的数据将不再统计，是否确认删除？',
        onSubmit: () => {
          const data = {
            id: record.id
          };
          restOPCall(this.objName, 'del', data).then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            message.success('删除成功！');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            }).finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  handleChangeStatus = (record, value) => {
    let data = { id: record.id, status: value };
    this.setState({ loading: true });
    apiCall('user/changestatus', 'GET', data).then(() => {
      this.fetchList();
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleUnbindWx = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        content: '解除该账号与微信账号绑定后，该账号将无法使用微信登录，是否确认解除？',
        onSubmit: () => {
          apiCall(`user/${id}/unbindWx`, 'PUT').then(() => {
            message.success('删除成功！');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            }).finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys,
      selectedRows
    });
  };

  handleBatchDelete = () => {
    const { selectedRows } = this.state;
    this.setState({
      OperateModalParams: {
        visible: true,
        content: '删除账号后，该账号所绑定的员工将无法登录本系统，与他相关的数据将不再统计，是否确认删除？',
        ts: parseInt(moment().format('X')),
        onSubmit: () => {
          const ids = [];
          selectedRows.forEach((item) => {
            ids.push(item.id);
          });
          const data = {
            ids: ids.join(',')
          };
          apiCall('user/removeByIds', 'DELETE', data).then(() => {
            this.OperateModal.onCancel();
            message.success('删除成功！');
            this.fetchList();
          }).catch((err) => {
            console.log(err);
          }).finally(() => {
            this.setState({
              OperateModalParams: {
                visible: false,
              }
            })
          });
        },
        onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  handleExport = () => {
    const { columns, selectedRows } = this.state;
    const sheetArray = [
      {
        headers: columns,
        data: selectedRows,
      }
    ];
    exportExcelFile(sheetArray, { fileName: `账号管理.${moment().format('YYYY-MM-DD')}.xlsx` });
  }

  render () {
    const { dataSource, pagination, selectedRowKeys, columns, loading, FormModalParams, OperateModalParams } = this.state;

    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    const hasSelected = selectedRowKeys.length > 0;

    return (
      <div className='SubAccount-Container'>
        <FilterBar >
          <Form layout={'inline'} ref={this.formRef}>
            <FormItem name="account" getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear placeholder="账号" />
            </FormItem>
            <FormItem name="employeeName" getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear placeholder="关联员工姓名" />
            </FormItem>
            <FormItem name="mobile" getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input allowClear placeholder="关联手机号" />
            </FormItem>
            <FormItem name="roleId">
              <GeneralSelect allowClear placeholder='账号角色' schema="role" />
            </FormItem>
            <FormItem name="status" >
              <SysDictSelect allowClear placeholder='账号状态' dataset="USERSTATUS" />
            </FormItem>
          </Form >
          <div className="flex flex-space-between" >
            <div>
              <Button type="primary" onClick={() => this.handleQuery()}>
                查询
              </Button>
              <Button onClick={() => this.handleReset()}>重置筛选</Button>
            </div>
            <div>
              <Button type="primary" onClick={() => this.handleAdd()}>
                新建账号
              </Button>
              <Button disabled={!hasSelected} type="primary" onClick={this.handleExport}>导出</Button>
            </div>
          </div>
        </FilterBar >
        <Card bordered={false}>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1500 }} rowSelection={rowSelection}> </Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div >
    );
  }
}

export default SubAccount;
