/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/30 09:50
 * @LastEditTime: 2022/01/04 09:31
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\comworkbench\Test\home.jsx
 * @Description: '测试专用'
 */

import React, { PureComponent } from 'react';
import { apiCall } from 'common/utils';
import { Card, Spin, Form, Button, message, DatePicker } from 'antd';
import moment from 'moment';
import RemindModal from 'components/Modal/RemindModal/RemindModal';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;

export default class Test extends PureComponent {

  formRef = React.createRef();

  // 1.构建变量-初始化
  constructor (props) {
    super(props);
    this.state = {
      loading: false,
      confirmVisible: false
    };
  }

  // 2.调用函数
  componentDidMount = async () => {
  }

  // 3.调用函数
  componentWillMount = async () => { }

  handleSubmit = () => {
    this.formRef.current.validateFields().then((formData) => {
      if (formData.PyqTime) {
        this.setState({
          confirmVisible: true,
        });
      } else {
        message.error('请选择测试时间');
      }
    });
  }

  handleConfirm = () => {
    this.formRef.current.validateFields().then((formData) => {
      this.setState({
        loading: true,
      });
      if (formData.PyqTime) {
        formData.beginTime = moment(formData.PyqTime[0]._d).format('YYYY-MM-DD');
        formData.endTime = moment(formData.PyqTime[1]._d).format('YYYY-MM-DD');
      }
      const { beginTime, endTime } = formData;
      const data = {
        beginTime,
        endTime
      };
      apiCall('/pyqinfo/pyqcmd', 'GET', data).then((res) => {
        message.success('分析成功！');
        this.setState({
          loading: false,
          confirmVisible: false
        });
      })
        .catch((err) => {
          this.setState({
            loading: false,
            confirmVisible: false
          });
          console.log(err);
        });
    });
  }

  onCancel = () => {
    this.setState({
      confirmVisible: false,
    });
  }

  render () {
    const { loading, confirmVisible } = this.state;
    const extra = <div>
      注意：
      <br />
      1、本功能仅系统内部工作人员测试使用，非企业人员使用
      <br />
      2、测试时间建议以当前时间作为起点，时长不超过3个月
      <br />
      3、开始分析后请隔10分钟再去【营销套件-朋友圈分析数据】中查看测试结果。
    </div>;

    return (
      <>
        <Spin spinning={loading}>
          <Card>
            <Form ref={this.formRef} labelCol={{ xxl: { span: 2 }, xl: { span: 5 } }}
              wrapperCol={{ span: 10 }}>

              <FormItem label="测试时间" name='PyqTime' extra={extra}>
                <RangePicker format="YYYY-MM-DD" />
              </FormItem>

              <FormItem wrapperCol={{ xxl: { offset: 2 }, xl: { offset: 5 } }}>
                <Button type="primary" onClick={() => this.handleSubmit()}>立即分析</Button>
              </FormItem>
            </Form>
          </Card>
        </Spin>
        <RemindModal
          visible={confirmVisible}
          title="立即分析确认"
          content="是否立即开启分析测试？"
          handleSubmit={this.handleConfirm}
          onCancel={this.onCancel}
        />
      </>
    );
  }
}
