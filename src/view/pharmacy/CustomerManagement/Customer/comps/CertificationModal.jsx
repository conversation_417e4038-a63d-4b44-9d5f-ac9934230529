/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/01/16 09:41
 * @LastEditTime: 2024/11/15 09:22
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/CustomerManagement/Customer/comps/CertificationModal.jsx
 * @Description: '客户管理-手动识别'
 */

import React, { useEffect, useRef, useState } from "react"
import { Form, message, Modal, Input, Spin, notification } from "antd"
import { apiCall } from "common/utils"

const FormItem = Form.Item
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
}

const CertificationModal = (props) => {
  const formRef = useRef(null)
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    const { visible, id } = props.params
    if (visible) {
      setVisible(true)
      setId(id)
    }
  }, [props])

  const manualAuthentication = async (mobile) => {
    // 发送请求，手动识别
    const data = {
      customerId: id,
      mobile: mobile,
    }
    setLoading(true)
    setConfirmLoading(true)
    // 发送请求
    await apiCall("/wecom/icbc/customer/auth", "POST", data)
      .then((res) => {
        // 判断是否识别成功
        if (res.updateFlag) {
          message.success("手动识别成功")
          // 当前页面更新对应信息
          props.params?.successInit()
        } else {
          message.warning("手机号码未在银行开户，手机号码：" + res.mobile)
        }
        props.params?.onSubmit?.()
      })
      .catch((err) => {
        console.log(err)
        if (err.retcode != 0) {
          notification.open({
            className: "warning",
            message: `响应异常：${err.retcode} (${err.msg})`,
            description: (
              <div>
                <div>{"手机号码：" + mobile}</div>
              </div>
            ),
          })
        }
      })
      .finally(() => {
        setVisible(false)
        setLoading(false)
        setConfirmLoading(false)
      })
  }

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      const { authenticationMobile } = formData
      manualAuthentication(authenticationMobile)
    })
  }

  const onCancel = () => {
    setVisible(false)
    setConfirmLoading(false)
    props.params?.onCancel?.()
  }

  return (
    <Modal
      visible={visible}
      title="手动识别"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} ref={formRef}>
          <FormItem
            label="手机号码"
            name="authenticationMobile"
            rules={[
              { required: true, message: "请输入手机号码" },
              {
                pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                message: "请输入正确的手机号码",
              },
            ]}
          >
            <Input
              placeholder="请输入手机号码"
              allowClear
              showCount
              maxLength={11}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  )
}

export default CertificationModal
