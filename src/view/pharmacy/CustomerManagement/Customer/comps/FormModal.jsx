/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/14 14:41
 * @LastEditTime: 2025/06/17 14:36
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/CustomerManagement/Customer/comps/FormModal.jsx
 * @Description: '客户详情-设置负责人对话框'
 */

import React, { useState, useEffect, useRef } from "react"
import { Modal, Form, message, Spin, Select } from "antd"
import { apiCall } from "common/utils"

const FormItem = Form.Item

const FormModal = (props) => {
  const formRef = useRef(null)
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [id, setId] = useState(null)
  const [name, setName] = useState("")
  const [employeeOptions, setEmployeeOptions] = useState([])

  useEffect(() => {
    const { visible, employeeList, id, name } = props.params
    if (visible) {
      setVisible(true)
      setId(id)
      setName(name)
      setEmployeeOptions(
        employeeList.map((item, index) => {
          if (item.responsible) {
            let timer = setTimeout(() => {
              formRef.current.setFieldsValue({
                employeeId: item.employeeId,
              })
              clearTimeout(timer)
            }, 300)
          }
          return {
            label: item.employeeName,
            value: item.employeeId,
          }
        })
      )
    }
  }, [props])

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true)
      const data = {
        id: Number(id),
        ...formData,
      }
      apiCall("/wecom/customer/modifyAndLog", "PUT", data)
        .then((res) => {
          message.success("设置成功！")
          setVisible(false)
          setLoading(false)
          setConfirmLoading(false)
          setId(null)
          setEmployeeOptions([])
          props.params?.onSubmit?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    })
  }

  const onCancel = () => {
    setVisible(false)
    setLoading(false)
    setConfirmLoading(false)
    setId(null)
    setEmployeeOptions([])
    props.params?.onCancel?.()
  }

  return (
    <Modal
      visible={visible}
      width={500}
      title={`为【${name}】设置客户负责人`}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            name="employeeId"
            label="员工"
            extra={
              <>
                1、请选择关联员工中的其中一个员工为负责人
                <br />
                2、设置后只有该员工可以编辑客户信息
                <br />
                3、其他员工已编辑的信息不会合并
                <br />
                4、已有【管户员工】时，不可设置负责人
              </>
            }
          >
            <Select
              allowClear
              options={employeeOptions}
              showSearch
              placeholder="请选择"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  )
}

export default FormModal
