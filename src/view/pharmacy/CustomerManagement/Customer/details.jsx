/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/14 09:24
 * @LastEditTime: 2024/11/14 18:11
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/CustomerManagement/Customer/details.jsx
 * @Description: '客户管理-客户详情'
 */

import React, { useEffect, useState } from "react"
import {
  Spin,
  Button,
  Card,
  Row,
  Col,
  Avatar,
  Image,
  Descriptions,
  List,
  Tag,
  Typography,
  Tabs,
  Timeline,
  Empty,
  Skeleton,
  Popover,
  notification,
  message,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import { createFromIconfontCN, QuestionCircleOutlined } from "@ant-design/icons"
import FormModal from "./comps/FormModal"
import CertificationModal from "./comps/CertificationModal"
import SysDictSelect from "components/select/SysDictSelect"
import SysDictLabel from "components/select/SysDictLabel"
import { qs2obj } from "common/object"
import AppStore from "stores/AppStore"
import { WordCloud } from "@ant-design/plots"
import { FileHOC } from "components/FileHOC/FileHOC"
import "./details.less"

const { TabPane } = Tabs
const { Text } = Typography
const IconFont = createFromIconfontCN({})

const CustomerDetails = (props) => {
  const [id, setId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [tabsIndex, setTabsIndex] = useState("1")
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [userInfo, setUserInfo] = useState({})
  const [operatorType, setOperatorType] = useState(null)
  const [accessedType, setAccessedType] = useState(null)
  const [dynamicDataSource, setDynamicDataSource] = useState(null)
  const [followDataSource, setFollowDataSource] = useState(null)
  const [formParams, setFormParams] = useState({ visible: false })
  const [customerTag, setCustomerTag] = useState([])
  const [dataOptions, setDataOptions] = useState([])
  const [allTagCounts, setAllTagCounts] = useState(0)
  const [certificationParams, setCertificationParams] = useState({
    visible: false,
  })

  const customerCollection = {
    data: dataOptions,
    wordField: "name",
    weightField: "value",
    colorField: "name",
    tooltip: {
      showContent: false,
    },
    wordStyle: {
      fontFamily: "Verdana",
      fontSize: [12, 32],
      rotation: 0,
    },
    // 返回值设置成一个 [0, 1) 区间内的值，
    // 可以让每次渲染的位置相同（前提是每次的宽高一致）。
    random: () => 0.5,
  }

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      init(id)
    }
  }, [])

  const init = async (id) => {
    const params = {
      customerId: id,
    }
    await getInfo(params)
    // await getCustomerData(params);
    await fetchDynamicsList(params)
  }

  const getInfo = async (params = { customerId: id }) => {
    setLoading(true)
    const data = {
      ...params,
    }
    await apiCall("/wecom/customer/getInfo", "GET", data)
      .then((res) => {
        setUserInfo(res)
        const customerTags = customerTagArr(res.tagList)
        setCustomerTag(customerTags)
        if (customerTags.length > 0) {
          let sum = 0
          customerTags.forEach((item) => {
            sum += item.tagList.length
          })
          setAllTagCounts(sum)
        } else {
          setAllTagCounts(0)
        }

        const data = res?.tagPreference?.map((item) => ({
          // value: 100,
          value: item.count,
          name: item.name,
        }))
        setDataOptions(data)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getCustomerData = async (params = {}) => {
    setLoading(true)
    const { customerId } = params
    await apiCall(
      `/wecom/activity/surveyFeedback/customer/${customerId}`,
      "GET"
    )
      .then((res) => {
        const data = res?.collectionOption?.map((item) => ({
          value: 100,
          name: item.name,
        }))
        setDataOptions(data)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const customerTagArr = (obj) => {
    let arr = []
    for (let [key, value] of Object.entries(obj)) {
      arr.push({
        title: key,
        tagList: value,
      })
    }
    return arr
  }

  // 标签删除
  const handleClose = (e, tag) => {
    e.preventDefault()
    setLoading(true)
    apiCall(`/wecom/customer/tag/del/${id}/${tag.id}`, "DELETE")
      .then((res) => {
        message.success("删除成功！")
        getInfo()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchDynamicsList = async (params = {}) => {
    setLoading(true)
    const {
      customerId = id,
      pagination = paginations,
      types,
      trackingTypes,
    } = params
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      customerId,
      types: types || null,
      trackingTypes: trackingTypes?.join(",") || null,
    }
    await apiCall("/wecom/customer/operationLog/page", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        if (params.push) {
          setDynamicDataSource(dynamicDataSource.concat(records))
        } else {
          setDynamicDataSource(records)
        }
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchFollowList = async (params = {}) => {
    setLoading(true)
    const { customerId = id, pagination = paginations, types } = params
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      customerId,
      types: types?.join(",") || null,
    }
    await apiCall("/wecom/customer/followLog/page", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        if (params.push) {
          setFollowDataSource(dynamicDataSource.concat(records))
        } else {
          setFollowDataSource(records)
        }
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeTabs = (index) => {
    setTabsIndex(index)
    if (index == 1) {
      const params = {
        pagination: {
          current: 1,
          pageSize: 10,
        },
      }
      fetchDynamicsList(params)
    } else if (index == 2) {
      const params = {
        pagination: {
          current: 1,
          pageSize: 10,
        },
      }
      fetchFollowList(params)
    }
  }

  const onChangeOperatorType = (value) => {
    setOperatorType(value)
    const params = {
      types: value,
      pagination: {
        current: 1,
        pageSize: 10,
      },
    }
    fetchDynamicsList(params)
  }

  const onChangeAccessedType = (value) => {
    setAccessedType(value)
    const params = {
      types: operatorType,
      trackingTypes: value,
      pagination: {
        current: 1,
        pageSize: 10,
      },
    }
    fetchDynamicsList(params)
  }

  const onChangeSelectCustomerFollow = (value) => {
    setOperatorType(value)
    const params = {
      types: value,
      pagination: {
        current: 1,
        pageSize: 10,
      },
    }
    fetchFollowList(params)
  }

  const loadMoreBtn = (type) => (
    <Button
      style={{ display: "flex", margin: "12px auto 0" }}
      type="primary"
      onClick={(e) => handleLoadMore(type)}
    >
      加载更多
    </Button>
  )

  const handleLoadMore = (type) => {
    let params = {}
    switch (type) {
      case "customerDynamic":
        params = {
          push: true,
          types: operatorType,
          trackingTypes: accessedType,
          pagination: {
            current: paginations.current + 1,
            pageSize: paginations.pageSize,
          },
        }
        fetchDynamicsList(params)
        break
      case "followRecord":
        params = {
          push: true,
          types: operatorType,
          pagination: {
            current: paginations.current + 1,
            pageSize: paginations.pageSize,
          },
        }
        fetchFollowList(params)
        break
    }
  }

  const employeeListRenderItem = (item) => (
    <List.Item>
      <List.Item.Meta
        title={
          <div className="list-item-meta-title-box">
            <div className="info">
              <span className="name">{item.employeeName}</span>
              <span className="describe">{item.departmentName}</span>
              <Tag color={item.state == "NORMAL" ? "#1890ff" : "#8c8c8c"}>
                {item.state == "NORMAL" ? "好友" : "非好友"}
              </Tag>
              {/* {item.responsible ? (
                <Tag color="#f59a23">
                  负责人{(userInfo.adminFlag && '(管户)') || ''}
                </Tag>
              ) : null} */}
            </div>
            <Text ellipsis={{ tooltip: item.addWay }}>{item.addWay}</Text>
          </div>
        }
        description={
          <>
            {"客户添加时间：" +
              moment(item.createTime).format("YYYY-MM-DD HH:mm:ss")}
            {/* <br /> */}
            {/* {'上次联系时间：' + moment(item.lastContactTime).format('YYYY-MM-DD HH:mm:ss')} */}
          </>
        }
      />
    </List.Item>
  )

  const groupListRenderItem = (item) => (
    <List.Item>
      <List.Item.Meta
        title={
          <div className="list-item-meta-title-box">
            <div className="info">
              <span className="name">{item.groupName}</span>
              <span className="describe">{item.memberCount}人</span>
            </div>
            <Text ellipsis={{ tooltip: item.joinScene }}>{item.joinScene}</Text>
          </div>
        }
        description={
          "客户入群时间：" +
          moment(item.createTime).format("YYYY-MM-DD HH:mm:ss")
        }
      />
    </List.Item>
  )

  const handleSetResponsible = () => {
    setFormParams({
      visible: true,
      id: id,
      name: userInfo.name,
      employeeList: userInfo.employeeList,
      onCancel: () => {
        setFormParams({ visible: false })
      },
      onSubmit: () => {
        setFormParams({ visible: false })
        getInfo()
      },
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const isMobile = (value) => /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)

  const showManualModal = () => {
    setCertificationParams({
      id: id,
      visible: true,
      onSubmit: (data) => {
        setCertificationParams({ visible: false })
        getInfo()
      },
      onCancel: () => {
        setCertificationParams({ visible: false })
      },
      successInit: () => {
        const params = {
          customerId: id,
        }
        getInfo()
        fetchDynamicsList(params)
      },
    })
  }

  return (
    <div className="CustomerDetails">
      <Spin spinning={loading}>
        <Row gutter={20}>
          <Col xs={24} lg={10}>
            <Card
              bordered={false}
              title="客户详情（基本信息）"
              className="basicInfo"
              extra={
                <Button type="primary" onClick={() => handleGoBack()}>
                  返 回
                </Button>
              }
            >
              {JSON.stringify(userInfo) != "{}" ? (
                <div className="userInfo">
                  <Avatar
                    size={40}
                    src={
                      <FileHOC src={userInfo.avatar}>
                        {(url) => <Image src={url} />}
                      </FileHOC>
                    }
                  />
                  <div className="leftInfo">
                    <p>
                      {userInfo.name}
                      <span
                        style={{
                          color: userInfo.type == 1 ? "#07c160" : "#f59a23",
                        }}
                      >
                        {userInfo.companyName}
                      </span>
                      {userInfo.gender == 1 ? (
                        <IconFont
                          type="icon-228yonghu_xingbienan"
                          style={{ color: "#1989fa" }}
                        />
                      ) : userInfo.gender == 2 ? (
                        <IconFont
                          type="icon-229yonghu_xingbienv"
                          style={{ color: "#ee0a24" }}
                        />
                      ) : (
                        ""
                      )}
                    </p>
                    {/* <p>积分：{userInfo.points}</p> */}
                    {/* <p>
                      <span style={{ whiteSpace: 'nowrap' }}>识别状态：</span>
                      <span>
                        {userInfo.authFlagName}
                        {userInfo.authType ? (
                          <span>
                            &nbsp;
                            {moment(userInfo.authTime).format(
                              'YYYY/MM/DD HH:mm:ss'
                            )}
                            &nbsp;
                            <Tag color="#5d8dd4">
                              {userInfo.authType == 'HAND_AUTH'
                                ? '手动识别'
                                : '主动识别'}
                            </Tag>
                          </span>
                        ) : (
                          ''
                        )}
                      </span>
                    </p> */}
                    <p>
                      业绩负责人：
                      {(userInfo.performanceEmployeeName &&
                        userInfo.performanceEmployeeName +
                          `（${userInfo.performanceEmployeeWecomId}）`) ||
                        "-"}
                    </p>
                    {/* <p>
                      客户负责人：
                      {(userInfo.employeeName &&
                        userInfo.employeeName +
                          ((userInfo.adminFlag && '(管户)') || '')) ||
                        '-'}
                    </p> */}
                    {/* <p>识别时间：{userInfo.authTime || '-'}</p> */}
                    {userInfo.lastActiveDesc && (
                      <p>
                        {userInfo.lastActiveDesc}：{userInfo.lastActiveTime}
                      </p>
                    )}
                    {userInfo.lastContactDesc && (
                      <p>
                        {userInfo.lastContactDesc}
                        {userInfo.lastContactEmployeeName &&
                          "：" + userInfo.lastContactEmployeeName}{" "}
                        {userInfo.lastContactTime}
                      </p>
                    )}
                  </div>
                  <div className="rightButton">
                    {/* <Button
                      type="primary"
                      size={10}
                      onClick={() => {
                        showManualModal();
                      }}
                    >
                      手动识别
                    </Button> */}
                  </div>
                </div>
              ) : (
                <Skeleton avatar paragraph={{ rows: 2 }} />
              )}
              <div className="customerTag">
                <h2>客户标签（{allTagCounts}）</h2>
                {customerTag.length > 0 ? (
                  customerTag.map((item, index) => (
                    <Row key={index}>
                      <Col span={5}>
                        <Text ellipsis={{ tooltip: item.title }}>
                          {item.title}
                        </Text>
                      </Col>
                      <Col span={19}>
                        {item.tagList?.length > 0
                          ? item.tagList.map((atem, andex) => (
                              <Tag
                                key={andex}
                                closable
                                onClose={(e) => handleClose(e, atem)}
                              >
                                {atem.name}
                              </Tag>
                            ))
                          : ""}
                      </Col>
                    </Row>
                  ))
                ) : (
                  <Empty
                    description="暂无数据 "
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </div>
              <Descriptions title="身份信息" column={{ xs: 1, sm: 2 }}>
                <Descriptions.Item label="客户姓名">
                  {userInfo.realName || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="出生日期">
                  {userInfo.birthday
                    ? moment(userInfo.birthday).format("YYYY-MM-DD")
                    : "-"}
                </Descriptions.Item>
                <Descriptions.Item label="联系方式">
                  {userInfo.mobile || "-"}
                </Descriptions.Item>
                {/* <Descriptions.Item label="CIS编号">
                  {userInfo.cisNumber || '-'}
                </Descriptions.Item> */}
              </Descriptions>
              <List
                header={
                  <>
                    <h2>关联员工（{userInfo.employeeList?.length || 0}）</h2>{" "}
                    {/* {userInfo.adminFlag ? (
                      <Popover
                        content={
                          <>
                            <p>1.客户负责人用于维护客户信息、群发客户消息</p>{' '}
                            <p>
                              2.当客户负责人是行内管户员工时，修改请前往行内系统。
                            </p>{' '}
                          </>
                        }
                      >
                        <Button
                          type="primary"
                          disabled
                          icon={<QuestionCircleOutlined />}
                        >
                          设置负责人
                        </Button>
                      </Popover>
                    ) : (
                      <Button
                        type="primary"
                        onClick={() => handleSetResponsible()}
                      >
                        设置负责人
                      </Button>
                    )} */}
                  </>
                }
                className="employeeList"
                dataSource={userInfo.employeeList}
                renderItem={(item) => employeeListRenderItem(item)}
              />
              <List
                header={
                  <>
                    <h2>所在群组（{userInfo.groupList?.length || 0}）</h2>
                  </>
                }
                className="groupList"
                dataSource={userInfo.groupList}
                renderItem={(item) => groupListRenderItem(item)}
              />
            </Card>
          </Col>
          <Col xs={24} lg={14} style={{ overflow: "hidden" }}>
            <div style={{ height: "100%" }}>
              <div className="customerCollection">
                <div>标签画像</div>
                <div className="WordCloud">
                  {dataOptions?.length > 0 ? (
                    <WordCloud {...customerCollection} />
                  ) : (
                    <Empty
                      description="暂无数据 "
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  )}
                </div>
              </div>
              {/* <Card bordered={false} title="客户标签画像" className='customerCollection'>
                {customerCollection && <WordCloud {...customerCollection} />}
              </Card> */}
              <Card bordered={false} title="动态信息" className="dynamicInfo">
                <Tabs
                  activeKey={tabsIndex}
                  destroyInactiveTabPane
                  onChange={onChangeTabs}
                >
                  <TabPane tab="客户动态" key="1">
                    <SysDictSelect
                      placeholder="客户动态"
                      dataset="OPERATORTYPE"
                      onChange={onChangeOperatorType}
                    />
                    {operatorType == "3" && (
                      <SysDictSelect
                        style={{ marginLeft: "10px" }}
                        placeholder="轨迹类型"
                        dataset="ACCESSED_TYPE"
                        mode="multiple"
                        onChange={onChangeAccessedType}
                      />
                    )}
                    {dynamicDataSource?.length > 0 ? (
                      <Timeline mode="left">
                        {dynamicDataSource.map((item, index) => (
                          <Timeline.Item
                            key={index}
                            label={moment(item.logTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )}
                          >
                            <span className="title">
                              <SysDictLabel
                                dataset="OPERATORTYPE"
                                dictkey={item.type}
                              />
                            </span>
                            <ul className="content">
                              {/* 信息变动 */}
                              {item.type == 1 &&
                                item.detailVO?.newContentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      <span className="title">{atem.type}</span>
                                      <p>{atem.details}</p>
                                    </li>
                                  )
                                )}

                              {/* 标签变动 */}
                              {item.type == 2 &&
                                item.detailVO.tagLog?.contentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      <span className="title">
                                        {atem.type == "ADD" ? "增加" : "删除"}
                                      </span>
                                      {atem.list?.map((btem, bndex) => (
                                        <p key={bndex}>{btem}</p>
                                      ))}
                                    </li>
                                  )
                                )}

                              {/* 轨迹变动 */}
                              {item.type == 3 &&
                                item.detailVO?.contentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      <span className="title">
                                        {
                                          AppStore.state.g_sysdict.ACCESSED_TYPE.find(
                                            (btem, bndex) =>
                                              item.detailVO.accessType ==
                                              btem[0]
                                          )[1]
                                        }
                                      </span>
                                      {atem.list?.map((btem, bndex) => (
                                        <p key={bndex}>{btem}</p>
                                      ))}
                                    </li>
                                  )
                                )}

                              {/* 群组变动 */}
                              {item.type == 4 &&
                                item.detailVO?.newContentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      <span className="title">{atem.type}</span>
                                      <p>{atem.details}</p>
                                    </li>
                                  )
                                )}

                              {/* 好友状态变动 */}
                              {item.type == 6 &&
                                item.detailVO?.contentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      {atem.list?.map((btem, bndex) => (
                                        <p key={bndex}>{btem}</p>
                                      ))}
                                    </li>
                                  )
                                )}

                              {/* 资源标签变动 */}
                              {item.type == 7 &&
                                item.detailVO.tagLog?.contentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      <span className="title">
                                        {atem.type == "ADD" ? "增加" : "删除"}
                                      </span>
                                      {atem.list?.map((btem, bndex) => (
                                        <p key={bndex}>{btem}</p>
                                      ))}
                                    </li>
                                  )
                                )}

                              {/* 朋友圈变动 */}
                              {item.type == 5 &&
                                item.detailVO?.contentList?.map(
                                  (atem, andex) => (
                                    <li key={andex}>
                                      {atem.list?.map((btem, bndex) => (
                                        <p key={bndex}>{btem}</p>
                                      ))}
                                    </li>
                                  )
                                )}
                            </ul>
                          </Timeline.Item>
                        ))}
                        {paginations.current * paginations.pageSize >=
                        paginations.total
                          ? null
                          : loadMoreBtn("customerDynamic")}
                      </Timeline>
                    ) : (
                      <Empty
                        description="暂无客户动态"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    )}
                  </TabPane>
                  <TabPane tab="跟进记录" key="2">
                    <SysDictSelect
                      placeholder="跟进方式"
                      dataset="FOLLOW_UP_WAY"
                      mode="multiple"
                      onChange={onChangeSelectCustomerFollow}
                    />
                    {followDataSource?.length > 0 ? (
                      <Timeline mode="left">
                        {followDataSource.map((item, index) => (
                          <Timeline.Item key={index} label={item.createTime}>
                            <span className="title">
                              {item.typeName || "-"}
                              <span className="name">
                                {item.followName || "-"}
                              </span>
                            </span>
                            <div className="content">{item.content || "-"}</div>
                            {item.isNextFollow ? (
                              <>
                                <span className="title">下次跟进</span>
                                <div className="content">
                                  <p>时间：{item.nextFollowTime || "-"}</p>
                                  <p>内容：{item.nextFollowContent || "-"}</p>
                                </div>
                              </>
                            ) : null}
                          </Timeline.Item>
                        ))}
                        {paginations.current * paginations.pageSize >=
                        paginations.total
                          ? null
                          : loadMoreBtn("followRecord")}
                      </Timeline>
                    ) : (
                      <Empty
                        description="暂无跟进记录 "
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    )}
                  </TabPane>
                </Tabs>
              </Card>
            </div>
          </Col>
        </Row>
      </Spin>
      <FormModal params={formParams} />
      <CertificationModal params={certificationParams} />
    </div>
  )
}

export default CustomerDetails
