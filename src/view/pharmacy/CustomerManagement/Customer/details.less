.CustomerDetails {
  .ant-row {

    .ant-col {
      margin-bottom: 20px;

      .ant-card {
        height: 100%;
      }

      .basicInfo {
        .userInfo {
          display: flex;
          margin-bottom: 16px;

          .leftInfo { 
            flex: 1;
            margin-left: 16px;
            width: 300px;

            p {
              margin: 0;
              display: flex;
              align-items: center;
            }

            .anticon {
              margin-left: 6px;
              font-size: 16px;
            }
          }
 
          .rightButton {
            width: 26%;

            button {
              float: right;
            }
          }
        }

        .employeeList,
        .groupList {
          .ant-list-header {
            display: flex;
            border: unset;
            padding: 0;
            margin: 0 0 8px;

            h2 {
              flex: auto;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.85);
              font-weight: bold;
              font-size: 16px;
              line-height: 1.5715;
              white-space: nowrap;
              text-overflow: ellipsis;
              margin: 0;
            }
          }

          .ant-list-items {
            margin-bottom: 16px;

            .ant-list-item-meta {
              .ant-list-item-meta-title {
                .list-item-meta-title-box {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;

                  .info {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;

                    .name {
                      font-size: 16px;
                    }

                    .describe {
                      color: #aaaaaa;
                      margin: 0 10px;
                    }
                  }

                  .ant-typography {
                    width: 250px;
                    text-align: right;
                  }
                }
              }
            }
          }
        }

        .customerTag {
          h2 {
            flex: auto;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.85);
            font-weight: bold;
            font-size: 16px;
            line-height: 1.5715;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin: 0;
          }
        }
      }

      .dynamicInfo {
        .ant-tabs {
          .ant-tabs-content-holder {
            .ant-tabs-content {
              .ant-tabs-tabpane {

                .ant-select {
                  width: 200px !important;
                  margin-bottom: 20px;
                }

                .ant-timeline {
                  width: 400px;

                  .ant-timeline-item {
                    .ant-timeline-item-content {
                      width: calc(66% - 14px);

                      .title {
                        font-weight: bold;
                        display: flex;

                        .name {
                          font-weight: initial;
                          color: #aaa;
                          margin-left: 6px;
                        }
                      }

                      .content {
                        margin: 0;
                        padding: 10px;
                        border-radius: 6px;
                        background: #f2f2f2;

                        li {
                          display: block;


                        }

                        p {
                          margin: 0;
                          display: flex;
                          flex-wrap: wrap;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .customerCollection {
        height: 340px;
        margin-bottom: 10px;
        background-color: #fff;
        padding: 20px;

        .WordCloud {
          height: 100%;
        }

        // .ant-card-body {
        //   height: 100%;
        // }
      }
    }
  }
}

// @media screen and (max-width: 1000px) {
//   .ant-row {
//     .ant-col {
//     }
//   }
// }