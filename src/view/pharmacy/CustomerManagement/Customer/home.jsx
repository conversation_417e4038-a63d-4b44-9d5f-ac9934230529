/*
 * @Author: LinQunXun
 * @Date: 2022-04-02 15:52:33
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024/11/14 18:14
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/CustomerManagement/Customer/home.jsx
 * @Description: '客户管理-客户'
 */

import React, { useEffect, useRef, useState } from "react"
import FilterBar from "components/FilterBar/FilterBar"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  TreeSelect,
  Avatar,
  message,
  DatePicker,
  Select,
} from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import moment from "moment"
import MaintainLabelModal from "./comps/MaintainLabelModal"
import { recursionTagKeyTreeData } from "common/tree"
import SysDictSelect from "components/select/SysDictSelect"
import ExportModal from "./comps/ExportModal"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotTableTag from "components/WibotTableTag/home"
// import { useDidRecover } from 'react-router-cache-route';

const FormItem = Form.Item
const { SHOW_PARENT } = TreeSelect
const { RangePicker } = DatePicker
const { Option } = Select

const Customer = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [labelTreeData, setLabelTreeData] = useState([])
  const [tagPreferenceTreeData, setTagPreferenceTreeData] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRows, setSelectedRows] = useState([])
  const [dataSource, setDataSource] = useState([])
  const [exportParams, setExportParams] = useState({ visible: false })
  const [MaintainLabelParams, setMaintainLabelParams] = useState({
    visible: false,
  })
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        )
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        )
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        )
        return (
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        )
      },
    },
    {
      title: "手机号",
      width: "160px",
      dataIndex: "mobile",
      key: "mobile",
      align: "center",
    },
    // {
    //   title: '客户负责人',
    //   width: '200px',
    //   dataIndex: 'employeeName',
    //   key: 'employeeName',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => {
    //     const content =
    //       (value && value + ((record.adminFlag && '(管户)') || '')) || '';
    //     return (
    //       <Tooltip placement="topLeft" title={content}>
    //         {content}
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      title: "业绩负责人",
      width: "160px",
      dataIndex: "performanceEmployeeName",
      key: "performanceEmployeeName",
      align: "center",
      render: (value, record, index) =>
        value ? `${value}（${record.performanceEmployeeWecomId}）` : "",
    },
    // {
    //   title: '识别状态',
    //   width: '160px',
    //   dataIndex: 'authFlagName',
    //   key: 'authFlagName',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip placement="topLeft" title={value}>
    //       {value}
    //     </Tooltip>
    //   ),
    // },
    // {
    //   title: '积分',
    //   width: '160px',
    //   dataIndex: 'points',
    //   key: 'points',
    //   align: 'center',
    // },
    {
      title: "联系情况",
      width: "160px",
      dataIndex: "lastContactTime",
      key: "lastContactTime",
      align: "center",
      render: (value, record, index) => {
        const content =
          (record.lastContactDesc && (
            <div>
              <div>{record.lastContactDesc}</div>
              <div>{record.lastContactEmployeeName}</div>
              <div>{value}</div>
            </div>
          )) ||
          ""
        return (
          <Tooltip placement="topLeft" title={content}>
            {content}
          </Tooltip>
        )
      },
    },
    {
      title: "活跃情况",
      width: "160px",
      dataIndex: "lastActiveTime",
      key: "lastActiveTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.lastActiveDesc}
          <br />
          {value}
        </>
      ),
    },
    {
      title: "所在群组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
      render: (value, record, index) => (
        <WibotTableTag tagList={value?.split(",") || []} />
      ),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "资源标签",
      width: "160px",
      dataIndex: "resourceTagList",
      key: "resourceTagList",
      align: "center",
      render: (value, record, index) => (
        <WibotTableTag tagList={value.map((item) => item.name)} />
      ),
    },
    {
      title: "标签画像",
      width: "160px",
      dataIndex: "tagPreferenceNameList",
      key: "tagPreferenceNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "最早添加时间",
      width: "160px",
      dataIndex: "firstAddTime",
      key: "firstAddTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstAddTime) - timeStamp(b.firstAddTime),
    },
    {
      title: "关联员工",
      width: "160px",
      dataIndex: "relevanceEmployee",
      key: "relevanceEmployee",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "流失状态",
      width: "160px",
      dataIndex: "friendState",
      key: "friendState",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "最近流失时间",
      width: "160px",
      dataIndex: "lastRemovalTime",
      key: "lastRemovalTime",
      align: "center",
      sorter: (a, b) =>
        timeStamp(a.lastRemovalTime) - timeStamp(b.lastRemovalTime),
    },
    // {
    //   title: 'OPEN ID',
    //   width: '200px',
    //   dataIndex: 'openId',
    //   key: 'openId',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip placement="topLeft" title={value}>
    //       {value}
    //     </Tooltip>
    //   ),
    // },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ]

  useEffect(() => {
    fetchList()
    getTagCategoryTreeTwo()
    getTagPreferenceTree()
  }, [])

  // 恢复缓存
  //  useDidRecover(() => {
  //   fetchList();
  // });

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/wecom/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        setLabelTreeData([
          {
            title: "全选",
            value: "customer",
            key: "customer",
            children: tagTreeData,
          },
        ])
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 获取标签画像数据
  const getTagPreferenceTree = async () => {
    await apiCall("/wecom/info/tag/tagGroup/tree", "GET")
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        setTagPreferenceTreeData([
          {
            title: "全选",
            value: "all",
            key: "all",
            children: tagTreeData,
          },
        ])
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minRemovalDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxRemovalDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      if (formData.addTime) {
        formData.minAddDate = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxAddDate = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.contactTime) {
        formData = {
          ...formData,
          ...switchContactTime(formData.contactTime),
        }
      }
      if (formData.activeTime) {
        formData = {
          ...formData,
          ...switchActiveTime(formData.activeTime),
        }
      }
      formData.tagIdList = formData.tagIdList?.join(",") || null
      formData.tagPreferenceIdList =
        formData.tagPreferenceIdList?.join(",") || null
      formData.adminDeptList = formData.adminDeptList?.join(",") || null
      formData.refDeptList = formData.refDeptList?.join(",") || null
      formData.performanceEmployeeIdList =
        formData.performanceEmployeeIdList?.join(",") || null
      formData.gender = formData.gender == "null" ? "" : formData.gender
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/wecom/customer/page", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const switchContactTime = (type) => {
    let params = {}
    switch (type) {
      case "0":
        params = {
          minContactDate: moment()
            .subtract(7, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxContactDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "1":
        params = {
          minContactDate: moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxContactDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "2":
        params = {
          minContactDate: moment()
            .subtract(90, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxContactDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "3":
        params = {
          minContactDate: moment()
            .subtract(180, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxContactDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "4":
        params = {
          minContactDate: moment()
            .subtract(365, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxContactDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "5":
        params = {
          neverContact: true,
        }
        break
      case "6":
        params = {
          minContactDate: null,
          maxContactDate: moment()
            .subtract(365, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
    }
    return params
  }

  const switchActiveTime = (type) => {
    let params = {}
    switch (type) {
      case "0":
        params = {
          minActiveDate: moment()
            .subtract(7, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxActiveDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "1":
        params = {
          minActiveDate: moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxActiveDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "2":
        params = {
          minActiveDate: moment()
            .subtract(90, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxActiveDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "3":
        params = {
          minActiveDate: moment()
            .subtract(180, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxActiveDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "4":
        params = {
          minActiveDate: moment()
            .subtract(365, "days")
            .format("YYYY-MM-DD 00:00:00"),
          maxActiveDate: moment()
            .subtract(1, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
      case "5":
        params = {
          minActiveDate: null,
          maxActiveDate: moment()
            .subtract(365, "days")
            .format("YYYY-MM-DD 23:59:59"),
        }
        break
    }
    return params
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const handleMaintainLabel = () => {
    setMaintainLabelParams({
      visible: true,
      selectedRows: [...selectedRows],
      onCancel: () => {
        setMaintainLabelParams({ visible: false })
      },
      onSubmit: (data) => {
        if (selectedRowKeys.length) {
          let apiUrl = ""
          switch (data.labelType) {
            case "add":
              apiUrl = "/wecom/customer/tag"
              break
            case "del":
              apiUrl = "/wecom/customer/remove/tag"
              break
            case "replace":
              apiUrl = "/wecom/customer/replace/tag"
              break
          }
          apiCall(apiUrl, "POST", { ...data })
            .then((res) => {
              message.success("维护成功")
              setSelectedRowKeys([])
              setSelectedRows([])
              fetchList()
            })
            .catch((err) => {
              console.log(err)
            })
            .finally(() => {
              setMaintainLabelParams({ visible: false })
            })
        } else {
          apiCall("/wecom/customer/batchAddOrCoverTag", "POST", data)
            .then((res) => {
              message.success("维护成功")
              fetchList()
            })
            .catch((err) => {
              console.log(err)
            })
            .finally(() => {
              setMaintainLabelParams({ visible: false })
            })
        }
      },
    })
  }

  const handleDetails = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/pharmacy/customer/details",
      search: `?id=${id}`,
    })
  }

  const handleExportAll = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(false)
      if (formData.time) {
        formData.minRemovalDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxRemovalDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      if (formData.addTime) {
        formData.minAddDate = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxAddDate = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.contactTime) {
        formData = {
          ...formData,
          ...switchContactTime(formData.contactTime),
        }
      }
      if (formData.activeTime) {
        formData = {
          ...formData,
          ...switchActiveTime(formData.activeTime),
        }
      }
      formData.tagIdList = formData.tagIdList?.join(",") || null
      formData.tagPreferenceIdList =
        formData.tagPreferenceIdList?.join(",") || null
      formData.adminDeptList = formData.adminDeptList?.join(",") || null
      formData.refDeptList = formData.refDeptList?.join(",") || null
      formData.gender = formData.gender == "null" ? "" : formData.gender
      const data = {
        ...formData,
      }
      apiCall("/wecom/customer/list/export", "GET", data, null, {
        isExit: true,
        title: `全部客户信息.${moment().format("YYYY-MM-DD")}.xlsx`,
      })
        .then((res) => {})
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys)
    setSelectedRows(selectedRows)
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  }

  const hasSelected = selectedRowKeys.length > 0

  return (
    <div className="Customer">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户姓名/微信昵称" allowClear />
          </FormItem>
          <FormItem
            name="mobile"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="手机号" allowClear />
          </FormItem>
          <FormItem
            name="performanceEmployeeIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="业绩负责人" />
          </FormItem>
          {/* <FormItem
            name="adminDeptList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="客户负责人" />
          </FormItem> */}
          <FormItem name="groupName">
            <Input placeholder="所在群组" allowClear />
          </FormItem>
          <FormItem
            name="refDeptList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="关联员工" />
          </FormItem>
          <FormItem name="friendState">
            <SysDictSelect placeholder="流失状态" dataset="LOSS_STATUS" />
          </FormItem>
          {/* <FormItem name="authFlag">
            <Select placeholder="识别状态" allowClear>
              <Option value>已识别</Option>
              <Option value={false}>未识别</Option>
            </Select>
          </FormItem> */}
          <FormItem name="gender">
            <SysDictSelect dataset="GENDER" placeholder="性别" />
          </FormItem>
          {/* <FormItem name="adminFlag">
            <Select placeholder="是否管户" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem> */}
          <FormItem name="tagIdList">
            <TreeSelect
              treeData={labelTreeData}
              treeCheckable
              treeDefaultExpandedKeys={["customer"]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder="客户标签"
            />
          </FormItem>
          <FormItem name="contactTime">
            <SysDictSelect dataset="CONTACT_DATE" placeholder="联系情况" />
          </FormItem>
          <FormItem name="activeTime">
            <SysDictSelect dataset="ACTIVE_DATE" placeholder="活跃情况" />
          </FormItem>
          <FormItem name="tagPreferenceIdList">
            <TreeSelect
              treeData={tagPreferenceTreeData}
              treeCheckable
              treeDefaultExpandedKeys={["all"]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder="标签画像"
            />
          </FormItem>
          {/* <FormItem
            name="openId"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="OPEN ID" allowClear />
          </FormItem> */}
          <FormItem name="time" label="最近流失时间">
            <RangePicker />
          </FormItem>
          <FormItem name="addTime" label="最早添加时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            {/* <Button type="primary" onClick={() => handleExportAll()}>
              导出全部
            </Button> */}
            {/* <Button type="primary" onClick={() => handleExport()}>
              导出客户统计数据
            </Button> */}
            {/* <Button
              // disabled={!hasSelected}
              type="primary"
              onClick={() => {
                handleMaintainLabel();
              }}
            >
              维护标签
            </Button> */}
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <ExportModal params={exportParams} />
      <MaintainLabelModal params={MaintainLabelParams} />
    </div>
  )
}

export default Customer
