/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/04/10 10:45
 * @LastEditTime: 2024/05/22 15:41
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/Order/PrescriptionOrder/comps/DetailModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import { Modal, Descriptions, Button, Empty, Spin } from 'antd';
import "./DetailModal.less"

const PrescriptionOrderDetailModal = (props) => {
  const { visible = false, info = null, onCancel } = props.params;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
    } else {
      setLoading(false);
    }
  }, [visible]);

  return (
    <Modal
      className="PrescriptionOrder-DetailModal"
      title='订单详情'
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={() => { onCancel(); }}
      footer={[
        <Button onClick={() => { onCancel(); }}>关闭</Button>
      ]}
    >
      <Spin spinning={loading}>
        {
          info ? <>
            <Descriptions>
              <Descriptions.Item label="处方单订单号" span={3}>{info.orderNo}</Descriptions.Item>
              <Descriptions.Item label="ERP处方单号" span={3}>{info.cfdh}</Descriptions.Item>
              <Descriptions.Item label="患者姓名">{info.customerInfoName}</Descriptions.Item>
              <Descriptions.Item label="性别">{info.gender == 'MAN' ? '男' : '女'}</Descriptions.Item>
              <Descriptions.Item label="年龄">{info.age}岁</Descriptions.Item>
            </Descriptions>
            <Descriptions title="Rp：">
              {info.detail.cfmx.map((item, index) => (<Descriptions.Item key={index}>{item.ypmc}{item.sl}{item.dw}</Descriptions.Item>))}
            </Descriptions>
            <Descriptions>
              <Descriptions.Item label="副数" span={3}>共计{info.detail.fssl}副</Descriptions.Item>
              <Descriptions.Item label="核方医师" span={3}>{info.detail.hfys}</Descriptions.Item>
            </Descriptions>
            <Descriptions title="服用方法">
              <Descriptions.Item>{info.detail.fyff}</Descriptions.Item>
            </Descriptions>
            <Descriptions title="收货地址">
              <Descriptions.Item span={3}>{info.consigneeName} {info.consigneeMobile}</Descriptions.Item>
              <Descriptions.Item span={3}>{info.address.replace(/\//g, '')}{info.addressDetail}</Descriptions.Item>
            </Descriptions>
            <Descriptions title="费用">
              <Descriptions.Item label="药材费" span={3}>{info.detail.yf}</Descriptions.Item>
              <Descriptions.Item label="加工费" span={3}>{info.detail.jgf}</Descriptions.Item>
              <Descriptions.Item label="煎药费" span={3}>{info.detail.jyf}</Descriptions.Item>
              <Descriptions.Item label="运费" span={3}>{info.detail.psf}</Descriptions.Item>
              <Descriptions.Item label="总计" span={3}>{info.detail.zje}</Descriptions.Item>
            </Descriptions>
          </> : (<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />)
        }
      </Spin>
    </Modal>
  );
};

export default PrescriptionOrderDetailModal;
