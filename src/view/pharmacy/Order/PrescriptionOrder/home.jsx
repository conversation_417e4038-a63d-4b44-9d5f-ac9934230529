/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/04/09 15:13
 * @LastEditTime: 2025/06/17 14:36
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/pharmacy/Order/PrescriptionOrder/home.jsx
 * @Description: '处方单订单'
 */

import React, { useState, useEffect, useRef } from "react"
import FilterBar from "components/FilterBar/FilterBar"
import { Button, Form, DatePicker, Card, Table, Input } from "antd"
import moment from "moment"
import { removeInputEmpty } from "common/regular"
import { timeStamp, getDay } from "common/date"
import { apiCall } from "common/utils"
import ListOperation from "components/ListOperation/home"
import SysDictLabel from "components/select/SysDictLabel"
import SysDictSelect from "components/select/SysDictSelect"
import DetailModal from "./comps/DetailModal.jsx"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"

const { RangePicker } = DatePicker

const PrescriptionOrder = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "处方单订单号",
      width: "160px",
      dataIndex: "orderNo",
      key: "orderNo",
      align: "center",
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "客户名称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
    },
    {
      title: "业绩负责人",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) =>
        value ? `${value}（${record.employeeWecomId}）` : "",
    },
    {
      title: "ERP处方单号",
      width: "160px",
      dataIndex: "cfdh",
      key: "cfdh",
      align: "center",
    },
    {
      title: "审核结果",
      width: "160px",
      dataIndex: "auditState",
      key: "auditState",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="AUDIT_STATE" dictkey={value} />
      ),
    },
    {
      title: "原因",
      width: "160px",
      dataIndex: "reason",
      key: "reason",
      align: "center",
    },
    {
      title: "审核时间",
      width: "160px",
      dataIndex: "auditTime",
      key: "auditTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.auditTime) - timeStamp(b.auditTime),
    },
    {
      title: "订单金额",
      width: "160px",
      dataIndex: "amount",
      key: "amount",
      align: "center",
      render: (value, record, index) => (value ? value / 100 : ""),
    },
    {
      title: "支付结果",
      width: "160px",
      dataIndex: "payState",
      key: "payState",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="PAY_STATE" dictkey={value} />
      ),
    },
    {
      title: "支付时间",
      width: "160px",
      dataIndex: "payTime",
      key: "payTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.payTime) - timeStamp(b.payTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = []
        if (record.auditState == "PASS") {
          opts.push({ onClick: () => handleDetail(record), name: "详情" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [detailParams, setDetailParams] = useState({ visible: false })
  const [dateLog, setDateLog] = useState([
    moment(getDay(-30)),
    moment(getDay(0)),
  ])

  useEffect(() => {
    fetchList()
  }, [])

  const fetchList = async (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minCreateTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxCreateTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      formData.depEmpList = formData.depEmpList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/order", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = async () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    setDateLog([moment(getDay(-30)), moment(getDay(0))])
    fetchList()
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const handleChooseTime = async (value) => {
    let time = []
    switch (value) {
      case "yesterday":
        time = [moment(getDay(-1)), moment(getDay(-1))]
        break
      case "week":
        time = [moment(getDay(-6)), moment(getDay(0))]
        break
      case "month":
        time = [moment(getDay(-30)), moment(getDay(0))]
        break
    }
    if (value) {
      await formRef.current.setFieldsValue({ time })
      setDateLog(time)
    }
  }

  //导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true)
      if (formData.time) {
        formData.minCreateTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxCreateTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      const data = {
        ...formData,
      }
      apiCall("/order/export", "GET", data, null, {
        isExit: true,
        title: `处方单订单.${moment().format("YYYY-MM-DD")}.xlsx`,
      })
        .then((res) => {})
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleDetail = (record) => {
    setDetailParams({
      visible: true,
      info: record,
      onSubmit: () => {
        setDetailParams({ visible: false })
      },
      onCancel: () => {
        setDetailParams({ visible: false })
      },
    })
  }

  return (
    <div className="PrescriptionOrder-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item
            name="time"
            label="创建时间"
            initialValue={[moment(getDay(-30)), moment(getDay(0))]}
          >
            <RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={(current) => {
                const dates = formRef?.current?.getFieldValue("time")
                if (!dates) {
                  return false
                }
                const tooLate = dates[0] && current.diff(dates[0], "days") > 30
                const tooEarly = dates[1] && dates[1].diff(current, "days") > 30
                return !!tooEarly || !!tooLate
              }}
              onCalendarChange={(val) =>
                formRef.current.setFieldValue("time", val)
              }
              onChange={(val) => setDateLog(val)}
              onOpenChange={(open) => {
                if (open) {
                  formRef.current.setFieldValue("time", [null, null])
                } else {
                  let timer = setTimeout(() => {
                    formRef.current.setFieldValue("time", dateLog)
                    clearTimeout(timer)
                  }, 300)
                }
              }}
            />
          </Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => handleChooseTime("yesterday")}
          >
            昨日
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => handleChooseTime("week")}
          >
            近7天
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => handleChooseTime("month")}
          >
            近31天
          </Button>

          <p style={{ width: "100vw" }} />

          <Form.Item
            name="orderNo"
            label="处方单订单号"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input
              placeholder="请输入处方单订单号"
              allowClear
              maxLength={18}
              onChange={(event) => {
                const regex = /^[a-zA-Z0-9]*$/
                const inputValue = event.target.value
                if (!regex.test(inputValue)) {
                  const sanitizedValue = inputValue.replace(/[^a-zA-Z0-9]/g, "")
                  formRef?.current?.setFieldsValue({ orderNo: sanitizedValue })
                } else {
                  formRef?.current?.setFieldsValue({ orderNo: inputValue })
                }
              }}
            />
          </Form.Item>

          <Form.Item
            name="cfdh"
            label="ERP处方单号"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input
              placeholder="请输入ERP处方单号"
              allowClear
              maxLength={18}
              onChange={(event) => {
                const regex = /^[a-zA-Z0-9]*$/
                const inputValue = event.target.value
                if (!regex.test(inputValue)) {
                  const sanitizedValue = inputValue.replace(/[^a-zA-Z0-9]/g, "")
                  formRef?.current?.setFieldsValue({ cfdh: sanitizedValue })
                } else {
                  formRef?.current?.setFieldsValue({ cfdh: inputValue })
                }
              }}
            />
          </Form.Item>

          {/* <Form.Item name="employeeName" label="员工名称" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入员工名称" allowClear />
          </Form.Item> */}

          {/* <Form.Item name="employeeWecomId" label="员工ID" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入员工ID" allowClear />
          </Form.Item> */}

          <Form.Item
            name="depEmpList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="业绩负责人" />
          </Form.Item>

          <Form.Item name="auditState">
            <SysDictSelect dataset="AUDIT_STATE" placeholder="审核结果" />
          </Form.Item>

          <Form.Item name="payState">
            <SysDictSelect dataset="PAY_STATE" placeholder="支付结果" />
          </Form.Item>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery()}
            >
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>

      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <DetailModal params={detailParams} />
    </div>
  )
}

export default PrescriptionOrder
