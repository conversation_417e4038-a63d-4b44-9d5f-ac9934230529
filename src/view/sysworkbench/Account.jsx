import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import {
  Form,
  Row,
  Col,
  Select,
  message,
  Spin,
  Alert,
  Modal,
  Button,
  Input,
  Card,
  Table,
} from 'antd';
import { restOPCall } from 'common/utils';
import ConfigSelect from 'components/select/ConfigSelect';
import moment from 'moment';

const { Option } = Select;
const Column = Table.Column;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Account extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      modalUpdateState: { show: false },
      list: null,
      pagination: { current: 1, pageSize: 10 },   // 不需要翻页机制时，pagination=null即可
      loading: false
    };
  }

  getList = (pagination, query) => {
    const data = {
      pagination: pagination || { current: 1, pageSize: 10 },
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ list, pagination }) => {
        this.setState({
          list,
          pagination,
          loading: false
        });
      });
  }

  componentDidMount () {
    this.getList();
  }

  onRefresh = () => {
    this.getList();
  }

  onTableChange = (pagination, filters, sorter) => {
    let query = {
      // "oid":filters?.oid.map((x)=>parseInt(x))
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.getList();
        }
      }
    });
  }

  onEdit = (record) => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }

  onDel = (record) => {
    confirm({
      title: '您确认要删除这个应用吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        restOPCall(this.objName, 'del', data)
          .then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.getList();
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  render () {
    const { list, pagination } = this.state;
    return (
      <div>
        <Row justify="space-between">
          <Col span={6}><Button type="primary" style={{ marginBottom: '16px' }} onClick={this.onAdd}>新增账号</Button></Col>
        </Row>
        <Card title={<span className="card-title-vertline">账号列表</span>} bordered={false}>
          <Table loading={this.state.loading} dataSource={list} pagination={pagination} rowKey="id" onChange={this.onTableChange}>
            <Column title="账号" dataIndex="account" width={250} />
            <Column title="名称" dataIndex="name" width={250} />
            <Column title="类型" dataIndex="type" width={100} render={(value) => {
              switch (value) {
                case 1: return '系统账号';
                case 2: return '公司-管理账号';
                case 3: return '公司-子账号';
              }
            }} />
            <Column title="归属公司" dataIndex="companyName" width={250} />
            <Column title="账号状态" dataIndex="status" width={80} />
            <Column title="更新时间" dataIndex="utime" width={80} />

            <Column
              title="操作"
              width={150}
              render={(value, record) => (
                <div>
                  <a onClick={() => this.onDownload(record)}>下载</a>
                  <a onClick={() => this.onEdit(record)}>修改</a>
                  <a onClick={() => this.onDel(record)}>删除</a>
                </div>
              )}
            />

          </Table>
          <UpdateAccount params={this.state.modalUpdateState} />
        </Card>
      </div>
    );
  }
}

class UpdateAccount extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      visible: props.params.show,
      confirmLoading: false,
      errmsg: null,
    };

  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.show });
    if (this.props.params.updateid) {
      let data = {};
      data[this.rowKey] = this.props.params.updateid;

      restOPCall(this.objName, 'get', data)
        .then((retdata) => {
          this.setState({ data: retdata });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            this.setState({ data: retdata });
          })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        name: formData.appName,
        type: parseInt(formData.appType ?? '0'),
        confid: parseInt(formData.appConfid)
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
      // 提交成功后 -> 重置表单内容
      this.formRef.current.setFieldsValue({ appName: '', appType: '0' });
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false });
    this.props.callback?.onCancel?.();
    // 点击取消后 -> 重置表单内容
    this.formRef.current.setFieldsValue({ appName: '', appType: '0' });
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  render () {
    const { visible } = this.state;
    const { ref } = this.props.params;
    return (
      <Modal
        visible={visible}
        title={this.props.params.updateid ? '修改账号' : '新增账号'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={this.props.params.updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              appName: this.state.data ? this.state.data.name : '',
              appType: this.state.data ? this.state.data.type.toString() : '0',
              appConfid: this.state.data ? this.state.data.confid.toString() : '0'
            }}>

            <FormItem label="账号" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '账号必填' }]} name="account">
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="名称" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]} name="name">
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="账号类型" name="type" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Select style={{ width: 200 }} >
                <Option value="1">系统账号</Option>
                <Option value="2">公司-管理账号</Option>
                <Option value="3">公司-子账号</Option>
              </Select>
            </FormItem>

            <FormItem label="公司" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '公司必选' }]} name="companyId">
              <Input style={{ width: 200 }} />
            </FormItem>

            {
              this.props.params.updateid
                ? <FormItem label="当前配置" name="appConfid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                  <ConfigSelect confid={this.state.data?.confid} appid={this.state.data?.appid} />
                </FormItem>
                : ''
            }

            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}


export default Account;
