import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { FullscreenOutlined } from '@ant-design/icons';
import { Form, Alert, Modal, Button, Input, InputNumber, Card, Table, message, Spin } from 'antd';
import { restOPCall } from 'common/utils';
import moment from 'moment';

const Column = Table.Column;
const { TextArea } = Input;
const FormItem = Form.Item;

class ApiLog extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'ApiLog';
    this.state = {
      modalUpdateState: { show: false },
      data: null,
      pagination: { current: 1, pageSize: 20 },   // 不需要翻页机制时，pagination=null即可
      loading: false,
    };
  }

  getList = (pagination, query) => {
    const data = {
      page: pagination || { current: 1, pageSize: 10 },
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ data, pagination }) => {
        this?.setState({

          data,
          pagination,
          loading: false
        });
      });
  }

  componentDidMount () {
    this.getList();
  }

  onRefresh = () => {
    this.getList();
  }


  onTableChange = (pagination, filters, sorter) => {
    let query = {
      // "oid":filters?.oid.map((x)=>parseInt(x))
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAddTest = (rec) => {

    let ref = rec;
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        ref: rec,
        onUpdated: this.getList,
      }
    });
  }

  render () {
    const { data, pagination } = this.state;
    return (
      <div>
        <Card title={<span className="card-title-vertline">调用日志</span>} bordered={false} extra={<Button onClick={this.onRefresh}>刷新</Button>}>
          <Table loading={this.state.loading} dataSource={data} pagination={pagination} rowKey="id" onChange={this.onTableChange}>
            <Column title="时间" dataIndex="ts" width={180} render={(x) => moment(x, 'X').format('YYYY-MM-DD HH:mm:ss')} />
            <Column title="来源" dataIndex="ip" width={180} render={(x) => {
              let url = 'http://ip138.com/ips138.asp?ip=' + x;
              return <a href={url}>{x}</a>;
            }}
            />
            <Column title="文件名" dataIndex="fileid" width={150} render={(x) => {
              let url = 'api/getimage/' + x;
              return (
                <div>
                  <img src={url + '?w=100&h=100'} width={100} height={100} />
                  <a href={url} target="_blank" rel="noreferrer"><FullscreenOutlined /></a>
                </div>
              );
            }}
            />
            <Column title="apiname" dataIndex="apiname" />

            <Column title="返回结果" width={400} dataIndex="result" render={(x) => (<TextArea style={{ border: 0 }} autoSize={{ minRows: 5, maxRows: 5 }} value={x} />)} />

            <Column title="处理时间(ms)" dataIndex="time" />

            <Column title="调用位置" dataIndex="from" render={(x) => {
              switch (x) {
                case 1: return 'demo';
                case 2: return 'http';
                case 3: return 'php-sdk';
                case 4: return 'js-sdk';
                case 5: return 'py-sdk';
                case 6: return 'java-sdk';
              }
            }}
            />

            <Column
              title="操作"
              render={(value, record) => (
                (!record.testid)
                  ? <div>
                    <a onClick={() => this.onAddTest(record)} title="加入测试">加入测试</a>
                  </div>
                  : ''
              )}
            />
          </Table>
          <UpdateTest params={this.state.modalUpdateState} />
        </Card>
      </div>
    );
  }
}

class UpdateTest extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Test';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
      errmsg: null,
    };

  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.visible });
    if (this.props.params.updateid) {
      // this.formRef.current.resetFields();
      restOPCall(this.objName, 'get', this.props.params.updateid)
        .then((retdata) => {
          this.setState({ data: retdata, cls: retdata.cls });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      // this.formRef.current.resetFields();
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        restOPCall(this.objName, 'get', nextProps.params.updateid)
          .then((retdata) => {
            this.setState({ data: retdata, cls: retdata.cls });
          })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((values) => {
      let data = {
        fileid: values.fileid,
        memo: values.memo,
        std: values.std,
        stdtime: parseInt(values.stdtime),
        refid: this.props.params.ref?.id
      };

      this.setState({ confirmLoading: true });

      restOPCall(this.objName, 'add', data).then(() => {
        this.setState({ visible: false, confirmLoading: false });
        this.props.params?.onUpdated?.();
      })
        .catch((ret) => {
          this.setState({ errmsg: ret.msg, confirmLoading: false });
        });
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false });
    this.props.callback?.onCancel?.();
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  afterClose = (e) => {
    // this.formRef.current.resetFields();
  }

  render () {
    const { visible } = this.state;
    const { ref } = this.props.params;

    return (
      <Modal visible={visible}
        title={'新增测试点'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={this.afterClose}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={'新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              fileid: this.state.data ? this.state.data.fileid : (ref?.fileid ?? ''),
              memo: this.state.data ? this.state.data.memo : '',
              std: this.state.data ? this.state.data.std : (ref?.result ?? ''),
              stdtime: this.state.data ? this.state.data.stdtime : 5000
            }}>

            <FormItem label="图片ID" name="fileid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]}>
              <Input style={{ width: 200 }} readOnly />
            </FormItem>

            <FormItem label="关键字" name="memo" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Input style={{ width: '200px' }} placeholder="请填写" />
            </FormItem>

            <FormItem label="标准返回" name="std" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <TextArea autoSize={{ minRows: 10, maxRows: 10 }} />
            </FormItem>

            <FormItem label="基准时间" name="stdtime" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <InputNumber /> &nbsp;ms
            </FormItem>

            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}


export default ApiLog;
