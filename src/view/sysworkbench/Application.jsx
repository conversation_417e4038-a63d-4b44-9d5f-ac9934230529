import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import AppActions from 'actions/AppActions';
import {
  Form,
  Row,
  Col,
  Select,
  message,
  Spin,
  Alert,
  Modal,
  Button,
  Input,
  Card,
  Table,
} from 'antd';
import { restOPCall } from 'common/utils';
import CustomIcon from 'components/base/Icon';
import ConfigSelect from 'components/select/ConfigSelect';
import moment from 'moment';
import history from 'common/history';

const { Option } = Select;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Application extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Application';
    this.rowKey = 'id';
    this.state = {
      modalUpdateState: { show: false },
      list: null,
      pagination: { current: 1, size: 100 },   // 不需要翻页机制时，pagination=null即可
      loading: false
    };
  }

  getList = (pagination, query) => {
    const data = {
      current: 1, size: 100,
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ list, pagination, records }) => {
        this.setState({
          list: records,
          pagination,
          loading: false
        });
      });
  }

  componentDidMount () {
    this.getList();
  }

  onRefresh = () => {
    this.getList();
  }

  onTableChange = (pagination, filters, sorter) => {
    let query = {
      // "oid":filters?.oid.map((x)=>parseInt(x))
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.getList();
        }
      }
    });
  }

  onEdit = (record) => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }

  onDel = (record) => {
    confirm({
      title: '您确认要删除这个应用吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        restOPCall(this.objName, 'del', data)
          .then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.getList();
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  goRobot = (appid) => {
    AppActions.setState({ appid });
    history.push('/workbench/robot?appid=' + appid);
  }

  goResource = (appid) => {
    AppActions.setState({ appid });
    history.push('/workbench/resource?appid=' + appid);
  }

  goSKU = (appid) => {
    AppActions.setState({ appid });
    history.push('/ecsuite/sku?appid=' + appid);
  }


  goConsumer = (appid) => {
    AppActions.setState({ appid });
    history.push('/ecsuite/consumer?appid=' + appid);
  }

  goDistributor = (appid) => {
    AppActions.setState({ appid });
    history.push('/ecsuite/distributor?appid=' + appid);
  }

  goOrder = (appid) => {
    AppActions.setState({ appid });
    history.push('/ecsuite/order?appid=' + appid);
  }

  render () {
    const { list, pagination } = this.state;
    return (
      <div>
        <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.onAdd}>新增应用</Button>

        <Card title={<span className="card-title-vertline">应用列表</span>} bordered={false}>
          <Row justify="left" align="top">
            {
              list?.map((item, index) => (
                <Col span={8} key={index}>
                  <Card title={item.name} bordered style={{ width: '95%', marginBottom: 20, borderColor: '#eeeeee' }}
                    extra={
                      <div>
                        <a onClick={() => this.onEdit(item)}><CustomIcon iconid="edit" /></a>
                        <a onClick={() => this.onDel(item)}><CustomIcon iconid="delete1" /></a>
                      </div>
                    }>
                    <Row>
                      <Col span={8} >应用类型</Col>
                      <Col span={16}>
                        {
                          (() => {
                            switch (item.type) {
                              case 0: return '默认';
                              case 1: return '电商场景';
                              case 2: return '办公场景';
                              case 3: return '营销场景';
                            }
                          })()
                        }
                      </Col>
                    </Row>
                    <Row>
                      <Col span={8} >AccessKey</Col>
                      <Col span={16}>{item.accesskey}</Col>
                    </Row>
                    <Row>
                      <Col span={8} >机器人总数</Col>
                      <Col span={16}><a onClick={() => this.goRobot(item.appid)}>{item.nrobot + '个'}</a></Col>
                    </Row>
                    <Row>
                      <Col span={8} >计划任务</Col>
                      <Col span={16}>{item.ntask}</Col>
                    </Row>
                    <Row>
                      <Col span={8} >当前配置</Col>
                      <Col span={16}>{item.confname}</Col>
                    </Row>
                  </Card>
                </Col>
              ))
            }
          </Row>
          <UpdateApplication params={this.state.modalUpdateState} />
        </Card>
      </div>
    );
  }
}

class UpdateApplication extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Application';
    this.rowKey = 'appid';
    this.state = {
      visible: props.params.show,
      confirmLoading: false,
      errmsg: null,
    };

  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.show });
    if (this.props.params.updateid) {
      let data = {};
      data[this.rowKey] = this.props.params.updateid;

      restOPCall(this.objName, 'get', data)
        .then((retdata) => {
          this.setState({ data: retdata });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            this.setState({ data: retdata });
          })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        name: formData.appName,
        type: parseInt(formData.appType ?? '0'),
        confid: parseInt(formData.appConfid)
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
      // 提交成功后 -> 重置表单内容
      this.formRef.current.setFieldsValue({ appName: '', appType: '0' });
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false });
    this.props.callback?.onCancel?.();
    // 点击取消后 -> 重置表单内容
    this.formRef.current.setFieldsValue({ appName: '', appType: '0' });
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  render () {
    const { visible } = this.state;
    const { ref } = this.props.params;
    return (
      <Modal visible={visible}
        title={this.props.params.updateid ? '修改应用' : '新增应用'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={this.props.params.updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              appName: this.state.data ? this.state.data.name : '',
              appType: this.state.data ? this.state.data.type.toString() : '0',
              appConfid: this.state.data ? this.state.data.confid.toString() : '0'
            }}>

            <FormItem label="应用名称" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]} name="appName">
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="应用类型" name="appType" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Select style={{ width: 200 }} >
                <Option value="0">默认</Option>
                <Option value="1">电商场景</Option>
                <Option value="2">办公场景</Option>
                <Option value="3">营销场景</Option>
              </Select>
            </FormItem>

            {
              this.props.params.updateid
                ? <FormItem label="当前配置" name="appConfid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                  <ConfigSelect confid={this.state.data?.confid} appid={this.state.data?.appid} />
                </FormItem>
                : ''
            }

            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}


export default Application;
