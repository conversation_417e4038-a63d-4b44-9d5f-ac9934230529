/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/07/20 18:06
 * @LastEditTime: 2022/03/09 14:49
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Assistant\comps\FormModal.jsx
 * @Description: '助理管理-EditFormModal'
 */
import React from 'react';
import Reflux from 'reflux';
import { Modal, Spin, Form, Input, Select, message } from 'antd';
import AppStore from 'stores/AppStore';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';

const FormItem = Form.Item;
class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.state = {
      loading: false,
      visible: false,
      confirmLoading: false,
      brOption: [],
      companyOption: [],
      versionOption: [],
      terminalType: null,
      terminalSource: null
    };
  }

  componentDidMount () {
    this.props.onRefFormFn(this);
  }

  async componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps, prevState);
  }

  didUpdate = async (prevProps, prevState) => {
    const { ts, visible, params } = this.props.params;
    try {
      // 时间戳有更新, 属于外面传入
      if (ts != prevProps.params.ts) {
        // 重置状态数据
        await this.setState({ loading: true, visible, data: params });
        if (params?.id) {
          this.setState({
            terminalType: params.terminalType,
            terminalSource: params.terminalSource
          });
          await this.formRef.current?.setFieldsValue({ ...params });
        }
        await this.getCompanyOption();
        await this.getBrOption(params?.robotRunnerId);
        await this.getVersionOption();
      }
    } catch (err) {
      console.log(err);
      this.onCancel();
    }
  }

  async getBrOption (extraId = null) {
    const data = {
      extraId,
      type: 'unbundling'
    };
    await apiCall('/br/option', 'GET', data).then((res) => {
      this.setState({
        loading: false,
        brOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  async getCompanyOption () {
    const data = {};
    await apiCall('/company/sysOption', 'GET', data).then((res) => {
      this.setState({
        companyOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  async getVersionOption () {
    const data = {
      type: 'BOT'
    };
    await apiCall('/programVersion/option', 'GET', data).then((res) => {
      this.setState({
        versionOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  onCancel = () => {
    this.setState({
      loading: false,
      visible: false,
      confirmLoading: false,
      brOption: [],
      companyOption: [],
      versionOption: [],
      terminalType: null,
      terminalSource: null
    });
    this.formRef.current.setFieldsValue({});
    // this.props.callback?.onCancel?.();
  }

  handleTerminalTypeChange = (value) => {
    this.setState({
      terminalType: value
    });
  }

  handleTerminalSourceChange = (value) => {
    this.setState({
      terminalSource: value
    });
  }

  handleSubmit = (e) => {
    e.preventDefault();
    this.formRef.current.validateFields().then((formData) => {
      const { versionOption, data } = this.state;
      const { name, description, robotRunnerId, companyId, programVersionId, mobile, corpName, terminalType, terminalSource, sn, ip } = formData;
      if (!data && !sn && !ip) {
        message.error('UCloud 资源ID和IP地址两个至少需要输入一个！');
        return;
      }
      this.setState({ confirmLoading: true });
      let version = '';
      versionOption.forEach((item) => {
        if (item.value == programVersionId) {
          version = item.label;
        }
      });
      const Data = {
        name,
        description,
        robotRunnerId,
        companyId,
        programVersionId,
        version,
        mobile,
        corpName,
        terminalType,
        terminalSource,
        sn,
        ip
      };
      this.props.params?.onUpdated?.(Data);
    });
  }

  render () {
    const { loading, data, visible, brOption, confirmLoading, companyOption, versionOption, terminalType, terminalSource } = this.state;
    const { roleType } = this.stores[0].state.User;
    let title = null;
    let entryTitle = data ? '编辑' : '新增';
    let content = null;
    if (roleType === 3) {
      title = '（系统普通员工）';
    } else {
      title = '（系统管理员）';
    }
    if (loading) {
      content = <div style={{ textAlign: 'center', fontSize: '16px' }}>
        <Spin size="large" />
      </div>;
    } else {
      content = <Form layout="horizontal" ref={this.formRef}
        labelCol={{ span: 7 }} wrapperCol={{ span: 16 }} initialValues={data}>
        <FormItem label="助理名称" name="name" rules={[{ required: true, message: '助理名称必填' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
          <Input />
        </FormItem>
        {
          terminalType == 'PC_GUI' && terminalSource == 'WECHAT' && data ? <FormItem label="所属运行环境" name="robotRunnerId" >
            <Select
              allowClear
              options={brOption}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </FormItem> : ''
        }
        <FormItem label="设备类型" name="terminalType" >
          <Select
            allowClear
            options={[
              {
                label: 'Windows',
                value: 'PC_GUI'
              },
              {
                label: 'Android',
                value: 'ANDROID_GUI'
              }
            ]}
            disabled={!!terminalType && data}
            showSearch
            onChange={this.handleTerminalTypeChange}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            } />
        </FormItem>
        <FormItem label="应用类型" name="terminalSource" >
          <Select
            allowClear
            options={[
              {
                label: '微信',
                value: 'WECHAT'
              },
              {
                label: '企业微信',
                value: 'WECOM'
              }
            ]}
            disabled={!!terminalSource && data}
            showSearch
            onChange={this.handleTerminalSourceChange}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            } />
        </FormItem>
        <FormItem label="所属公司" name="companyId" rules={[{ required: true, message: '所属公司必填' }]}>
          <Select
            allowClear
            options={companyOption}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </FormItem>
        {
          terminalType == 'PC_GUI' && terminalSource == 'WECHAT' ? <FormItem label="版本" name="programVersionId" >
            <Select
              allowClear
              options={versionOption}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </FormItem> : ''
        }

        {/* <FormItem label="手机号" name="mobile" rules={[{ required: false, pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入11位有效手机号' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
          <Input />
        </FormItem>
        <FormItem label="企微企业名称" name="corpName" rules={[{ required: true, message: '企微企业名称必填' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
          <Input />
        </FormItem> */}
        <FormItem label="助理备注" name="description" rules={[{ required: true, message: '助理备注必填' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
          <Input />
        </FormItem>
        {/* {
          !data && terminalType == 'PC_GUI' && terminalSource == 'WECHAT' ? <FormItem label="下拉菜单" name="downMenu" >
            <Select
              allowClear
              options={[
                {
                  label: 'UCloud',
                  value: 0
                },
                {
                  label: '本地',
                  value: 1
                }
              ]}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </FormItem> : ''
        } */}
        {
          !data && terminalType == 'PC_GUI' && terminalSource == 'WECHAT' ? <FormItem label="UCloud 资源ID" name="sn" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input />
          </FormItem> : ''
        }
        {
          !data && terminalType == 'PC_GUI' && terminalSource == 'WECHAT' ? <FormItem label="IP地址" name="ip" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input />
          </FormItem> : ''
        }
      </Form>;
    }
    return (
      <Modal
        visible={visible}
        title={entryTitle + '助理信息' + title}
        maskClosable={false}
        afterClose={null}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        destroyOnClose
        confirmLoading={confirmLoading}
        width={450}
        centered
      >
        {content}
      </Modal>
    );
  }
}

export default FormModal;
