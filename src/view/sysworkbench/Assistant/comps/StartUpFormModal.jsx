/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/07/20 18:06
 * @LastEditTime: 2022/03/08 11:09
 * @LastEditors: <PERSON>hong<PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Assistant\comps\StartUpFormModal.jsx
 * @Description: '助理管理-StartUpFormModal'
 */

import React from 'react';
import Reflux from 'reflux';
import { QrCodeBase } from 'common/qrcode';
import { Modal, Spin, Image, message } from 'antd';
import { apiCall } from 'common/utils';

class StartUpFormModal extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      loading: false,
      confirmLoading: false,
      QrCode: null,
    };
  }

  async componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps);
  }

  didUpdate = async (prevProps) => {
    const { ts, robotId, visible } = this.props.params;
    try {
      // 时间戳有更新, 属于外面传入
      if (ts != prevProps.params.ts) {
        if (robotId) {
          await this.setState({ loading: true, visible });
          const data = { robotId };
          await this.sendLoginCmd(data);
          await this.getLoginQrCode(data);
          let timer = setTimeout(async () => {
            await this.getRobotStatus(data);
            clearTimeout(timer);
          }, 2000);
        }
      }
    } catch (err) {
      console.log(err);
      this.onCancel();
    }
  }

  async sendLoginCmd (data) {
    await apiCall('/robot/sendLoginCmd', 'GET', data);
  }

  async getLoginQrCode (data) {
    await apiCall('/robot/getLoginQrCode', 'GET', data).then((res) => {
      if (res) {
        this.setState({
          loading: false,
          QrCode: QrCodeBase({ url: res.qrCodeUrl }),
        });
        // return false;
      }
      if (this.state.visible) {
        let timer = setTimeout(() => {
          this.getLoginQrCode(data);
          clearTimeout(timer);
        }, 1500);
      }
    })
      .catch((err) => {
        console.log(err);
      });
  }

  async getRobotStatus (data) {
    await apiCall('/robot/getRobotStatus', 'GET', data).then((res) => {
      if (res == 1) {
        this.onCancel();
        this.props.params?.onUpdated?.();
        return false;
      } else if (res == 4) {
        this.onCancel();
        message.error('启动失败！您所登录的微信和助手维护的微信信息不一致！');
        return false;
      }
      if (this.state.visible) {
        let timer = setTimeout(() => {
          this.getRobotStatus(data);
          clearTimeout(timer);
        }, 1500);
      }
    })
      .catch((err) => {
        console.log(err);
      });
  }

  onCancel = () => {
    this.setState({
      visible: false,
      loading: false,
      confirmLoading: false,
      QrCode: null,
    });
    this.props.callback?.onCancel?.();
  }

  render () {
    const color_aaa = { color: '#aaaaaa' };
    const { loading, QrCode, visible, confirmLoading } = this.state;
    let content = null;
    if (loading) {
      content = <div style={{ textAlign: 'center', fontSize: '16px' }}>
        <Spin size="large" />
      </div>;
    } else {
      content = <div style={{ display: 'flex', flexDirection: 'column' }}>
        <FileHOC src={QrCode}>
          {(url) => (
            <Image preview={false} width="180" style={{ margin: '0 auto', width: '180px' }} src={url} />
          )}
        </FileHOC>
        <p>请使用助手的微信扫码并且点击登录！</p>
        <p style={color_aaa}>注意事项：</p>
        <p style={color_aaa}>1、请使用手机微信扫码登录，长按识别二维码无效。</p>
        <p style={color_aaa}>2、请勿登录电脑版/网页版微信或在手机上退出登录</p>
      </div>;
    }
    return (
      <Modal
        visible={visible}
        title="登录助手微信"
        maskClosable={false}
        afterClose={null}
        onCancel={this.onCancel}
        destroyOnClose
        confirmLoading={confirmLoading}
        width={450}
        footer={null}
      >
        {content}
      </Modal>
    );
  }
}
export default StartUpFormModal;
