/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/07/20 18:06
 * @LastEditTime: 2021/10/20 09:44
 * @LastEditors: <PERSON>hong<PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Assistant\comps\StopItFormModal.jsx
 * @Description: '助理管理-StopItFormModal'
 */

import React from 'react';
import Reflux from 'reflux';
import { Modal } from 'antd';
import { apiCall } from 'common/utils';

class StopItFormModal extends Reflux.Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      confirmLoading: false,
      assistantName: '',
      timeStatus: null,
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps);
  }

  didUpdate = async (prevProps) => {
    const { ts, visible, name, robotId } = this.props.params;
    // 时间戳有更新, 属于外面传入
    if (ts != prevProps.params.ts) {
      // 重置状态数据
      this.setState({ visible, assistantName: name, robotId });
    }
  }

  handleSubmit = async () => {
    try {
      this.setState({
        confirmLoading: true
      });
      const data = {
        robotId: this.state.robotId
      };
      await this.sendLogoutCmd(data);
      let timer = setTimeout(async () => {
        await this.getRobotStatus(data);
        clearTimeout(timer);
      }, 2000);
    } catch (err) {
      console.log(err);
      this.setState({
        confirmLoading: false
      });
    }
  }

  async sendLogoutCmd (data) {
    await apiCall('/robot/sendLogoutCmd', 'GET', data).then((res) => { })
      .catch((err) => {
        console.log(err);
      });
  }

  async getRobotStatus (data) {
    await apiCall('/robot/getRobotStatus', 'GET', data).then((res) => {
      if (res === 2) {
        this.onCancel();
        this.props.params?.onUpdated?.();
        return false;
      }
      if (this.state.visible) {
        let timer = setTimeout(() => {
          this.getRobotStatus(data);
          clearTimeout(timer);
        }, 1500);
      }
    })
      .catch((err) => {
        console.log(err);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false });
    clearInterval(this.state.timeStatus);
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, assistantName } = this.state;

    return (
      <Modal
        visible={visible}
        title="停止助手确认"
        maskClosable={false}
        afterClose={null}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        destroyOnClose
        confirmLoading={confirmLoading}
        width={450}
      >
        <p>您将停止助手【{assistantName}】，停止后对应的微信也会退出登录，确认继续吗？</p>
      </Modal>
    );
  }
}
export default StopItFormModal;
