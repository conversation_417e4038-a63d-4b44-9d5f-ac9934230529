/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/07/20 18:06
 * @LastEditTime: 2023/11/17 10:14
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Assistant/home.jsx
 * @Description: '系统工作台-助理管理'
 */

import React from 'react';
import { Card, Table, Tooltip, Form, Input, message, Button, Select, Image } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import { removeInputEmpty } from 'common/regular';
import FormModal from './comps/FormModal';
import StartUpFormModal from './comps/StartUpFormModal';
import StopItFormModal from './comps/StopItFormModal';
import JsonConfigModal from 'components/Modal/JsonConfigModal/home';
import OperateModal from 'components/Modal/OperateModal/index';
import { getWibotFileUrl } from 'common/image';
import {FileHOC} from 'components/FileHOC/FileHOC';
const FormItem = Form.Item;

class AssistantManagement extends React.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '公司名称',
          dataIndex: 'companyName',
          key: 'companyName',
          width: '150px',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '机器人访问秘钥',
          dataIndex: 'robotKey',
          key: 'robotKey',
          width: '150px',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '登录的微信号',
          dataIndex: 'wechatId',
          key: 'wechatId',
          align: 'center',
          width: '150px',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '登录的微信号昵称',
          dataIndex: 'wechatName',
          key: 'wechatName',
          align: 'center',
          width: '150px',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '助理名称',
          dataIndex: 'name',
          key: 'name',
          width: '100px',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '助理备注',
          dataIndex: 'description',
          key: 'description',
          align: 'center',
          width: '100px',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '所属运行环境',
          dataIndex: 'robotRunnerName',
          key: 'robotRunnerName',
          align: 'center',
          width: '150px',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '登录状态',
          dataIndex: 'state',
          key: 'state',
          align: 'center',
          width: '100px',
          ellipsis: 'true',
          render: (value) => <SysDictLabel dataset="ROBOTSTATUS" dictkey={value} color />
        },
        {
          title: '离线备注',
          width: '240px',
          align: 'center',
          render: (value, record) => (
            <div>
              {record.offlineDate ? moment(record.offlineDate).format('YYYY-MM-DD HH:mm:ss') : ''}
              {record.offlineDate ? <br /> : ''}
              {record.offlineFileName ? <FileHOC src={getWibotFileUrl(record.offlineFileName)}>
                {(url) => (
                  <Image width={50} src={url} preview={{
                    src: url
                  }} />
                )}
              </FileHOC> : ''}
              {record.offlineFileName ? <br /> : ''}
              {record.offlineDesc ? `可能原因：${record.offlineDesc}` : ''}
            </div>
          )
        },
        {
          title: '操作',
          width: '370px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            <div>
              {
                value.state === 1 ? <a onClick={() => this.handleStopIt(record)}>停止</a> : <a onClick={() => this.handleStartUp(record.id)}>启动</a>
              }
              {
                value.state === 2 || value.state === 3 || value.state === 4 ? <a onClick={() => this.handleChangeNumber(record)}>换号</a> : ''
              }
              {value.state != 1 ? <a onClick={() => this.handleEdit(record)}>编辑</a> : ''}
              <a onClick={() => this.handleUpdateConfig(record)}>更新配置</a>
              <a onClick={() => this.handleOperateRecords(record)}>操作记录</a>
              {record.terminalSource == 'WECHAT' && record.terminalType == 'PC_GUI' ? <a onClick={() => this.handleUpgrade(record)}>升级运行环境</a> : ''}
              <a onClick={() => this.handleViewLog(record)}>查看日志</a>
              {/* <Upload
                name="file"
                customRequest={(config) => this.customRequest(config, record.id)}
                showUploadList={false}
                onChange={this.handleChange} >
                <a>更新配置</a>
              </Upload> */}
            </div>
          )
        },
      ],
      companyOption: [],
      pagination: { current: 1, pageSize: 10 },
      FormModalParams: { visible: false },
      StartUpFormModalParams: { visible: false },
      StopItFormModalParams: { visible: false },
      OperateModalParams: { visible: false },
      JsonConfigModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.getCompanyOption();
    this.fetchList();
  }

  async fetchList (params = {}) {
    this.setState({
      loading: true,
    });
    let formInfo = {};
    await this.formRef.current.validateFields().then((formData) => {
      // formData.employeeIds = formData.employeeIds ? formData.employeeIds.join(',') : null;
      formInfo = formData;
    });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
      ...formInfo,
    };
    apiCall('/robot/sysPage', 'GET', data).then((res) => {
      const { records, current, size, total, pages } = res;
      this.setState({
        loading: false,
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        this.setState({
          loading: false,
        });
        console.log(err);
      });
  }

  getCompanyOption () {
    const data = {};
    apiCall('/company/sysOption', 'GET', data).then((res) => {
      this.setState({
        companyOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        ts: parseInt(moment().format('X')),
        onUpdated: (formData) => {
          const data = {
            ...formData
          };
          apiCall('/robot/addBySystem', 'POST', data).then((res) => {
            message.success('新增成功');
            this.fetchList();
            this.FormFn.onCancel();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
            });
        }
      }
    });
  }

  handleEdit = (params) => {
    this.setState({
      FormModalParams: {
        visible: true,
        params: params,
        ts: parseInt(moment().format('X')),
        onUpdated: (formData) => {
          const data = {
            robotId: params.id,
            ...formData
          };
          apiCall('/robot/modifyBySystem', 'PUT', data).then((res) => {
            message.success('编辑成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.FormFn.onCancel();
            });
        }
      }
    });
  }

  handleChange = (info) => { // 暂时遗弃
    if (info.file.status === 'uploading') {
      this.setState({
        loading: true,
      });
      return;
    }
  };

  /**
   * @description: 自定义上传配置-json格式
   * @param {*}
   * @return {*}
   * @Author: ZhongJunWei
   * @Date: 2021/08/05 14:18
   */
  customRequest = (config, id) => { // 暂时遗弃
    const File = config.file;
    const formData = new FormData();
    formData.append('file', File);
    const data = formData;
    apiCall(`/robot/importRobotConfig?robotId=${id}`, 'POST', data).then((res) => {
      message.success('更新成功！');
      this.fetchList();
    })
      .catch((err) => {
        this.setState({
          loading: false,
        });
      });
  }

  handleStartUp = (id) => {
    this.setState({
      StartUpFormModalParams: {
        visible: true,
        robotId: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('启动成功');
          this.fetchList();
        }
      }
    });
  }

  handleStopIt = (params) => {
    const { name, id } = params;
    this.setState({
      StopItFormModalParams: {
        visible: true,
        name: name,
        robotId: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('停止成功');
          this.fetchList();
        }
      }
    });
  }

  handleChangeNumber = (record) => {
    const { id, name } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '助手换号确认',
        content: `您将为助手【${name}】进行换号，换号后之前的微信信息将被清空，确认继续吗?`,
        onSubmit: () => {
          const data = {
            robotId: id,
          };
          apiCall('/robot/changeAccount', 'GET', data).then((res) => {
            message.success('换号成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  handleQuery = () => {
    this.fetchList();
  }

  handleReset = () => {
    this.formRef.current.resetFields();
    this.fetchList({ pagination: { current: 1, pageSize: 10 } });
  };

  handleUpdateConfig = (record) => {
    const { id, config } = record;
    this.setState({
      JsonConfigModalParams: {
        visible: true,
        config: config,
        ts: parseInt(moment().format('X')),
        onSubmit: (formData) => {
          const { config } = formData;
          const data = {
            config,
            id,
          };
          apiCall('/robot/importRobotConfig', 'POST', data).then((res) => {
            message.success('更新成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.JsonConfigFn.onCancel();
            });
        }
      }
    });
  }

  // 操作记录
  handleOperateRecords = (records) => {
    const { id } = records;
    this.props.history.push(`/syswb/assistantmanage/OperateRecords/${id}`);
  }

  // 查看日志
  handleViewLog = (records) => {
    const { id, robotKey } = records;
    this.props.history.push(`/syswb/assistantmanage/Log/${id}/${robotKey}`);
  }

  handleUpgrade = (record) => {
    const { robotRunnerId } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '升级运行环境确认',
        content: ' 当前操作会触发br升级运行环境，请确认！',
        onSubmit: () => {
          const data = {
            id: robotRunnerId,
          };
          apiCall('/br/updatingVersion', 'GET', data).then((res) => {
            message.success('已提交升级请求！');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  onRefJsonConfigFn = (ref) => {
    this.JsonConfigFn = ref;
  }

  onRefFormFn = (ref) => {
    this.FormFn = ref;
  }

  render () {
    const {
      loading,
      dataSource,
      columns,
      pagination,
      FormModalParams,
      StartUpFormModalParams,
      StopItFormModalParams,
      JsonConfigModalParams,
      OperateModalParams,
      companyOption
    } = this.state;

    return (
      <div>
        <FilterBar>
          <Form layout={'inline'} ref={this.formRef}>
            <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input placeholder="助理名称" allowClear />
            </FormItem>
            <FormItem name="description" getValueFromEvent={(e) => removeInputEmpty(e)}>
              <Input placeholder="助理备注" allowClear />
            </FormItem>
            <FormItem name="state" >
              <SysDictSelect placeholder="登录状态" dataset="ROBOTSTATUS" />
            </FormItem>
            <FormItem name="companyId">
              <Select
                allowClear
                options={companyOption}
                showSearch
                placeholder="所属公司"
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </FormItem>
          </Form>
          <Button type="primary" htmlType="submit" onClick={() => this.handleQuery()}>
            查询
          </Button>
          <Button onClick={() => this.handleReset()}>重置</Button>
          <Button type="primary" htmlType="submit" onClick={() => this.handleAdd()}>
            新增
          </Button>
        </FilterBar>
        <Card bordered={false}>
          <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
            pagination={pagination} onChange={this.onTableChange} />
          <FormModal params={FormModalParams} onRefFormFn={this.onRefFormFn} />
          <StartUpFormModal params={StartUpFormModalParams} />
          <StopItFormModal params={StopItFormModalParams} />
          <OperateModal params={OperateModalParams} />
          <JsonConfigModal params={JsonConfigModalParams} onRefJsonConfigFn={this.onRefJsonConfigFn} />
        </Card>
      </div >
    );
  }
}

export default AssistantManagement;
