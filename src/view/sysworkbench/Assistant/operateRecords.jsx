/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/12/21 17:39
 * @LastEditTime: 2023/04/11 09:33
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Assistant/operateRecords.jsx
 * @Description: '助理管理-操作记录'
 */


import React from 'react';
import Reflux from 'reflux';
import { Card, Table, Tooltip, Image, Button } from 'antd';
import { apiCall } from 'common/utils';
import SysDictLabel from 'components/select/SysDictLabel';
import { getWibotFileUrl } from 'common/image';
import {FileHOC} from 'components/FileHOC/FileHOC';
class AssistantOperateRecords extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      dataSource: [],
      pagination: { current: 1, pageSize: 10 },
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '操作时间',
          dataIndex: 'createTime',
          key: 'createTime',
          align: 'center',
        },
        {
          title: '助理名称',
          dataIndex: 'robotName',
          key: 'robotName',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '操作动作',
          dataIndex: 'operationType',
          key: 'operationType',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <SysDictLabel dataset="ROBOT_LOG_OPERATION" dictkey={value} color />
        },
        {
          title: '备注',
          width: '240px',
          align: 'center',
          dataIndex: 'content',
          key: 'content',
          render: (value, record) => (
            <div>
              {value ? value : ''}
              {value ? <br /> : ''}
              {record.fileName ? <FileHOC src={getWibotFileUrl(record.fileName)}>
                {(url) => (
                  <Image width={50} src={url} preview={{
                    src: url
                  }} />
                )}
              </FileHOC> : ''}
              {!value && !record.fileName && '-'}
            </div>
          )
        },
      ],
    };
  }

  async componentDidMount () {
    await this.fetchList();
  }

  async fetchList (params = {}) {
    this.setState({
      loading: true,
    });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      robotId: this.props.match.params.id,
      ...query,
    };
    apiCall('/robotlog/page', 'GET', data).then((res) => {
      const { records, current, size, total, pages } = res;
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false,
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleGoBack = () => {
    this.props.history.push('/syswb/assistantmanage');
  }

  render () {
    const {
      loading,
      dataSource,
      columns,
      pagination,
    } = this.state;

    return (
      <div>
        <Card extra={<Button type="primary" onClick={() => this.handleGoBack()}>返回</Button>} bordered={false}>
          <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns}
            pagination={pagination} onChange={this.onTableChange} />
        </Card>
      </div >
    );
  }
}

export default AssistantOperateRecords;
