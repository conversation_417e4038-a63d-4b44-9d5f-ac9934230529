/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/07 14:08
 * @LastEditTime: 2022/03/08 09:57
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\BotRunner\comps\FormModal.jsx
 * @Description: 'BR管理-表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import { Form, Select, Spin, Modal, Input } from 'antd';
import { restOPCall, apiCall } from 'common/utils';

const { Option } = Select;
const { TextArea } = Input;
const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'br';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      machineOption: [],
    };
  }

  componentWillMount () { }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null }); // 重置状态数据
      this.getMachineOption(nextProps.params.updateid);
      if (nextProps.params.updateid) {
        const data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  getMachineOption = (extraId = null) => {
    const data = {
      extraId,
      type: 'unbundling'
    };
    apiCall('/machine/option', 'GET', data).then((res) => {
      this.setState({
        machineOption: res.map((item) => ({
          label: item.ipPublic,
          value: item.ipPublic
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      const data = {
        name: formData.name,
        ip: formData.ip,
        type: formData.type,
        description: formData.description,
        companyId: 57,
        status: 0,
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      }
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
    });
    // this.formRef.current.resetFields({});
    // this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, data, machineOption } = this.state;
    const { updateid } = this.props.params;

    return (
      <Modal
        visible={visible}
        title={updateid ? '修改主机' : '新增主机'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
        width={450}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{
              name: data ? data.name : '',
              ip: data ? data.ip : '',
              description: data ? data.description : '',
              type: String(data ? data.type : '1')
            }}>

            <FormItem label="主机名称"
              rules={[{ required: true, message: '主机名称必填' }]} name="name">
              <Input />
            </FormItem>

            {/* <FormItem label="云主机IP" rules={[{ required: true, message: '云主机IP必填' }]} name="ip">
              <Select
                allowClear
                options={machineOption}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </FormItem> */}

            {/* <FormItem label="终端类型" name="type" >
              <Select
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }>
                <Option value="1">安卓</Option>
                <Option value="2">windows</Option>
              </Select>
            </FormItem> */}

            <FormItem label="备注"
              rules={[{ required: true, message: '备注必填' }]} name="description">
              <TextArea />
            </FormItem>

          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
