/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/11/15 13:52
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/BotRunner/home.jsx
 * @Description: 'BR管理'
 */

import React from 'react';
import Reflux from 'reflux';
import { Row, Col, message, Button, Input, Card, Table, Tooltip, Form } from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';
import JsonConfigModal from 'components/Modal/JsonConfigModal/home';
import { removeInputEmpty } from 'common/regular';
import SysDictLabel from 'components/select/SysDictLabel';

const FormItem = Form.Item;

class BotRunner extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'br';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: [],
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '主机名称',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: 'IP地址',
          dataIndex: 'ip',
          key: 'ip',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <SysDictLabel dataset="BRSTATUS" dictkey={value} />
        },
        {
          title: '登记时间',
          dataIndex: 'createTime',
          key: 'createTime',
          align: 'center',
        },
        {
          title: '运行的机器人',
          dataIndex: 'robotName',
          key: 'robotName',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '唯一标识SN',
          dataIndex: 'sn',
          key: 'sn',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '备注',
          dataIndex: 'description',
          key: 'description',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '操作',
          width: '250px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            <div>
              <a onClick={() => this.handleEdit(record)}>修改</a>
              <a onClick={() => this.handleDel(record)}>删除</a>
              {/* <a onClick={() => this.handleUpdateConfig(record)}>更新配置</a> */}
              {/* <a onClick={() => this.handleUpgrade(record)}>程序升级</a> */}
            </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      JsonConfigModalParams: { visible: false },
      OperateModalParams: { visible: false },
      FormModalParams: { show: false },
    };
  }

  componentDidMount () {
    this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({
      loading: true
    });
    let formInfo = {};
    await this.formRef.current.validateFields().then((formData) => {
      formInfo = formData;
    });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
      ...formInfo,
    };
    restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个数据吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/br/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  handleSearch = (e) => {
    this.fetchList();
  }

  handleReset = () => {
    this.formRef.current.resetFields();
    this.fetchList({ pagination: { current: 1, pageSize: 10 } });
  }

  handleUpdateConfig = (record) => {
    const { id } = record;
    restOPCall('br', 'get', { id: id }).then((res) => {
      const { config } = res;
      this.setState({
        JsonConfigModalParams: {
          visible: true,
          config: config,
          ts: parseInt(moment().format('X')),
          onSubmit: (formData) => {
            const { config } = formData;
            const data = {
              config,
              id,
            };
            apiCall('/br/modify', 'PUT', data).then((res) => {
              message.success('更新成功');
              this.fetchList();
            })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                this.JsonConfigFn.onCancel();
              });
          }
        }
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleUpgrade = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '程序升级确认',
        content: ' 当前操作会触发br程序升级，请确认！',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/br/updatingVersion', 'GET', data).then((res) => {
            message.success('已提交升级请求！');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  onRefJsonConfigFn = (ref) => {
    this.JsonConfigFn = ref;
  }

  render () {
    const { dataSource, pagination, JsonConfigModalParams, columns, OperateModalParams, FormModalParams, loading } = this.state;

    return (
      <div>
        <Card bordered={false}>
          <Row justify="space-between">
            <Col>
              {/* <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增主机</Button> */}
            </Col>
            <Col style={{ marginBottom: '16px' }}>
              <Form layout={'inline'} ref={this.formRef}>
                <FormItem label="IP地址" name="ip" getValueFromEvent={(e) => removeInputEmpty(e)}>
                  <Input placeholder="IP地址" allowClear />
                </FormItem>
                <FormItem>
                  <Button type="primary" onClick={this.handleSearch}>查询</Button>
                </FormItem>
                <FormItem>
                  <Button type="default" onClick={this.handleReset}>重置</Button>
                </FormItem>
              </Form>
            </Col>
          </Row>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1300 }}></Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
        <JsonConfigModal params={JsonConfigModalParams} onRefJsonConfigFn={this.onRefJsonConfigFn} />
      </div>
    );
  }
}

export default BotRunner;
