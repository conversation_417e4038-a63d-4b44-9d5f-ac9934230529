/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/09 10:09
 * @LastEditTime: 2022/02/25 15:36
 * @LastEditors: <PERSON>hong<PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Brain\comps\FormModal.jsx
 * @Description: 'Brain管理-表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import {
  Form,
  Select,
  Spin,
  Modal,
  Input,
} from 'antd';
import { restOPCall, } from 'common/utils';
import ConfigSelect from 'components/select/ConfigSelect';

const { Option } = Select;
const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
    };
  }

  componentWillMount () { }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, });  // 重置状态数据
      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        name: formData.appName,
        type: parseInt(formData.appType ?? '0'),
        confid: parseInt(formData.appConfid)
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      }
      this.formRef?.current?.resetFields();
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, data } = this.state;
    const { updateid } = this.props.params;
    return (
      <Modal
        visible={visible}
        title={updateid ? '修改账号' : '新增账号'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{
              account: data ? data.account : '',
              name: data ? data.name : '',
              type: data ? data.type.toString() : '0',
              appConfid: data ? data.confid.toString() : '0'
            }}>

            <FormItem label="账号" rules={[{ required: true, message: '账号必填' }]} name="account">
              <Input />
            </FormItem>

            <FormItem label="名称" rules={[{ required: true, message: '名称必填' }]} name="name">
              <Input />
            </FormItem>

            <FormItem label="账号类型" name="type" >
              <Select
                showSearch
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }>
                <Option value="1">系统账号</Option>
                <Option value="2">公司-管理账号</Option>
                <Option value="3">公司-子账号</Option>
              </Select>
            </FormItem>

            <FormItem label="公司" rules={[{ required: true, message: '公司必选' }]} name="companyId">
              <Input />
            </FormItem>

            {
              updateid
                ? <FormItem label="当前配置" name="appConfid" >
                  <ConfigSelect confid={data?.confid} appid={data?.appid} />
                </FormItem>
                : ''
            }

          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
