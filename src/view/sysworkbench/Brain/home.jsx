/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/11/15 13:52
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Brain/home.jsx
 * @Description: 'Brain管理'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  message,
  Button,
  Card,
  Table,
  Tooltip,
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';

class Brain extends Reflux.Component {

  constructor(props) {
    super(props);
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '账号',
          width: '240px',
          dataIndex: 'account',
          key: 'account',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '名称',
          width: '240px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '类型',
          width: '240px',
          dataIndex: 'type',
          key: 'type',
          ellipsis: 'true',
          align: 'center',
          render: (value) => {
            switch (value) {
              case 1: return '系统账号';
              case 2: return '公司-管理账号';
              case 3: return '公司-子账号';
            }
            return <Tooltip placement="topLeft" title={value}>{value}</Tooltip>;
          }
        },
        {
          title: '归属公司',
          width: '240px',
          dataIndex: 'companyName',
          key: 'companyName',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '账号状态',
          width: '160px',
          dataIndex: 'status',
          key: 'status',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '更新时间',
          width: '160px',
          dataIndex: 'utime',
          key: 'utime',
          align: 'center',
        },
        {
          title: '操作',
          width: '220px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            <div>
              <a onClick={() => this.handleDownload(record)}>下载</a>
              <a onClick={() => this.handleEdit(record)}>修改</a>
              <a onClick={() => this.handleDel(record)}>删除</a>
            </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      FormModalParams: { visible: false },
      OperateModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.fetchList();
  }

  fetchList = (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleDownload = (record) => {

  }

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个应用吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/user/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, columns, loading, FormModalParams, OperateModalParams } = this.state;

    return (
      <div>
        <Card bordered={false}>
          <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增账号</Button>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1300 }}></Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default Brain;
