/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/08 15:13
 * @LastEditTime: 2023/02/17 10:32
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Company\comps\FormModal.jsx
 * @Description: '企业客户-表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Form,
  Row,
  Col,
  Spin,
  Modal,
  Button,
  Input,
  Radio,
  Checkbox,
  Cascader
} from 'antd';
import { restOPCall } from 'common/utils';
import cityoptions from 'common/cityoption';
import GeneralCheckbox from 'components/select/GeneralCheckbox';

const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor (props) {
    super(props);
    this.objName = 'company';
    this.rowKey = 'id';
    this.state = {
      audit: null,
      visible: false,
      confirmLoading: false,
      radioKitType: null
    };
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, audit: nextProps.params.audit, data: null, }); // 重置状态数据
      if (nextProps.params.updateid) {
        const data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      const data = {
        ...formData,
        name: formData.name,
        type: parseInt(formData.type ?? '2'),
        site: formData.site,
        bizClass: formData.bizclass.join(','),
        contact: formData.contact,
        mobile: formData.mobile,
        state: formData.state,
        organizationId: formData.organizationid,
        wechatId: formData.wechatid,
        cityCode: formData.citycode.join('/'),
        address: formData.address,
        province: formData.province,
        city: formData.city,
        shortDomain: formData.domain,
        accountPassword: formData.accountPassword,
        kitIds: formData.kits ?? [],
        kitType: formData.kitType,
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      }

    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({
      audit: null,
      visible: false,
      confirmLoading: false,
    });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  onCityChange = (keys, options) => {
    this.formRef.current.setFieldsValue({ province: (options[0]?.label) ?? '111', city: (options[1]?.label) ?? '11' });
  }

  changeContent = (e) => {
    e.persist();
    this.formRef.current.setFieldsValue({
      account: `admin@${e.target.value}`
    });
  }

  onRadioChange = (e) => {
    this.setState({
      radioKitType: e.target.value
    });
  }

  render () {
    const { visible, data, confirmLoading, radioKitType } = this.state;
    const { updateid } = this.props.params;

    return (
      <Modal
        visible={visible}
        title={updateid ? '修改企业客户信息' : '新增企业客户'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
        width={700}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef}
            labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={
              data
                ? {
                  name: data.name || '',
                  type: data.type?.toString() || '',
                  site: data.site || '',
                  bizclass: data.bizClass?.split(',') || [],
                  contact: data.contact || '',
                  mobile: data.mobile || '',
                  state: data.state?.toString() || '1',
                  citycode: data.cityCode?.split('/') || [],
                  domain: data.shortDomain,
                  accountPassword: 'NOT_CHANGE',
                  account: `admin@${data.shortDomain || ''}`,
                  address: data.address,
                  wechatid: data.wechatId,
                  organizationid: data.organizationId,
                  province: data.province,
                  city: data.city,
                  kits: data.kitIds,
                  kitType: data.kitType,
                  agentId: data.agentId,
                  secret: data.secret,
                  aesKey: data.aesKey,
                  token: data.token
                } : {
                  name: '',
                  type: '2',
                  site: '',
                  bizclass: [],
                  contact: '',
                  mobile: '',
                  state: '1',
                  citycode: [],
                  domain: '',
                  accountPassword: '',
                  account: 'admin@',
                  address: '',
                  wechatid: '',
                  organizationid: '',
                  province: '',
                  city: '',
                  kits: [],
                  kitType: '1',
                  agentId: '',
                  secret: '',
                  aesKey: '',
                  token: ''
                }
            } onFieldsChange={this.onFieldsChange}
          >

            <FormItem label="企业名称" name="name"
              rules={[{ required: true, message: '企业名称必填' }]} >
              <Input />
            </FormItem>

            <FormItem label="企业官网" name="site">
              <Input />
            </FormItem>

            <FormItem label="业务范围" name="bizclass" >
              <Checkbox.Group options={['银行保险', '电商电销', '企业RPA', '其它']} onChange={this.onBizClassChange} />
            </FormItem>

            <FormItem label="联系人" name="contact">
              <Input />
            </FormItem>

            <FormItem label="联系人手机" name="mobile"
              rules={[{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号' }]}
            >
              <Input />
            </FormItem>

            <FormItem label="联系人微信" name="wechatid">
              <Input />
            </FormItem>

            {/* <FormItem name="province" hidden />
            <FormItem name="city" hidden /> */}

            <FormItem label="组织结构代码" name="organizationid"
            >
              <Input />
            </FormItem>

            <FormItem label="省市区" name="citycode">
              <Cascader options={cityoptions} onChange={this.onCityChange} changeOnSelect />
            </FormItem>

            <FormItem label="地址" name="address">
              <Input />
            </FormItem>

            <FormItem label="账号后缀" name="domain"
              rules={[{ required: true, message: '账号后缀必须填写' },
                { pattern: /^[A-Za-z][A-Za-z0-9\\.]+[A-Za-z]$/, message: '账号后缀格式有误' }]}
            >
              <Input onChange={this.changeContent} />
            </FormItem>

            <FormItem label="管理员账号" name="account">
              <Input disabled />
            </FormItem>

            <FormItem label="登录密码" >
              <Row justify={'space-between'}>
                <Col span={19}>
                  <FormItem name="accountPassword" style={{ margin: 'unset' }}>
                    <Input.Password placeholder={'登录密码'} visibilityToggle={false} allowClear />
                  </FormItem>
                </Col>
                {/* <Col>
                  <Button onClick={this.handleResetPass}>重置</Button>
                </Col> */}
              </Row>
            </FormItem>

            <FormItem label="开通套件" name="kits"
              rules={[{ required: true, message: '必须选择一个套件' }]}
            >
              <GeneralCheckbox schema="kit" />
            </FormItem>

            <FormItem label="审核状态" name="state">
              <Radio.Group>
                <Radio value="1">待审核</Radio>
                <Radio value="2">30天试用</Radio>
                <Radio value="3">正式商用</Radio>
                <Radio value="4">待续费</Radio>
                <Radio value="5">停用</Radio>
              </Radio.Group>
            </FormItem>
            <FormItem label="套件选择" name="kitType"
              rules={[{ required: true, message: '必须选择一个套件' }]}
            >
              <Radio.Group onChange={this.onRadioChange}>
                <Radio value="1">营销套件</Radio>
                <Radio value="2">服务套件</Radio>
                <Radio value="3">私域套件</Radio>
              </Radio.Group>
            </FormItem>
            {
              radioKitType == '3' ? <FormItem label="agentId" name="agentId">
                <Input />
              </FormItem> : ''
            }
            {
              radioKitType == '3' ? <FormItem label="secret" name="secret">
                <Input />
              </FormItem> : ''
            }
            {
              radioKitType == '3' ? <FormItem label="aesKey" name="aesKey">
                <Input />
              </FormItem> : ''
            }
            {
              radioKitType == '3' ? <FormItem label="token" name="token">
                <Input />
              </FormItem> : ''
            }
          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
