/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/11/15 13:58
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Company/home.jsx
 * @Description: '企业客户'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  message,
  Button,
  Card,
  Table,
  Tooltip,
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import FormModal from './comps/FormModal';
import SysDictLabel from 'components/select/SysDictLabel';
import OperateModal from 'components/Modal/OperateModal/index';

class Company extends Reflux.Component {

  constructor(props) {
    super(props);
    this.objName = 'company';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '企业名称',
          width: '160px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '账号后缀',
          width: '160px',
          dataIndex: 'shortDomain',
          key: 'shortDomain',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '类型',
          width: '100px',
          dataIndex: 'type',
          key: 'type',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <SysDictLabel dataset="COMPANYTYPE" dictkey={value} color />
        },
        {
          title: '业务范围',
          width: '240px',
          dataIndex: 'bizClass',
          key: 'bizClass',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '联系人',
          width: '160px',
          dataIndex: 'contact',
          key: 'contact',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '手机号',
          width: '160px',
          dataIndex: 'mobile',
          key: 'mobile',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '状态',
          width: '160px',
          dataIndex: 'state',
          key: 'state',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <SysDictLabel dataset="COMPANYSTATUS" dictkey={value} color />
        },
        {
          title: '子账号数',
          width: '160px',
          dataIndex: 'accountCount',
          key: 'accountCount',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '更新时间',
          width: '160px',
          dataIndex: 'updateTime',
          key: 'updateTime',
          align: 'center',
        },
        {
          title: '操作',
          width: '220px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            (record.state == 1 || record.state == 5)
              ? <div>
                <a onClick={() => this.handleAudit(record)}>审核</a>
                <a onClick={() => this.handleDel(record)}>删除</a>
              </div>
              : <div>
                <a onClick={() => this.handleDisable(record)}>停用</a>
                <a onClick={() => this.handleEdit(record)}>修改</a>
                <a onClick={() => this.handleDel(record)}>删除</a>
              </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      FormModalParams: { visible: false },
      OperateModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleAudit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        audit: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('审核成功');
          this.fetchList();
        }
      }
    });
  }

  handleDisable = (record) => {
    const { id } = record;
    const data = {
      id,
    };
    this.setState({ loading: true });
    apiCall('company/disable', 'GET', data).then(() => {
      message.success('停用成功');
      this.fetchList();
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个公司客户吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/company/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, columns, FormModalParams, loading, OperateModalParams } = this.state;

    return (
      <div>
        <Card bordered={false}>
          <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增企业客户</Button>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1300 }}></Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default Company;
