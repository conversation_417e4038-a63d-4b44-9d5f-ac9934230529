import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import {
  message,
  Spin,
  Alert,
  Modal,
  Form,
  Button,
  Input,
  Card,
  Table,
} from 'antd';
import { ts2str, restOPCall } from 'common/utils';
import moment from 'moment';
import ApplicationSelect from 'components/select/ApplicationSelect';

const Column = Table.Column;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Config extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Config';
    this.rowKey = 'confid';

    this.state = {
      modalUpdateState: { show: false },
      list: null,
      pagination: { current: 1, pageSize: 20 },   // 不需要翻页机制时，pagination=null即可
      loading: false,
    };
  }

  getList = (pagination, query) => {
    const data = {
      page: pagination || { current: 1, pageSize: 10 },
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ list, pagination }) => {
        this?.setState({
          list,
          pagination,
          loading: false
        });
      });
  }

  componentDidMount () {
    this.getList();
  }

  onRefresh = () => {
    this.getList();
  }


  onTableChange = (pagination, filters, sorter) => {
    let query = {
      // "oid":filters?.oid.map((x)=>parseInt(x))
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.getList();
        }
      }
    });
  }


  onEdit = (record) => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }


  onDel = (record) => {
    confirm({
      title: '您确认要删除这个配置吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        restOPCall(this.objName, 'del', data)
          .then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.getList();
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  render () {
    const { list, pagination } = this.state;
    return (
      <div>
        <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.onAdd}>新增配置</Button>
        <Card title={<span className="card-title-vertline">我的配置</span>} bordered={false}>
          <Table loading={this.state.loading} dataSource={list} pagination={pagination} rowKey="confid" onChange={this.onTableChange}>
            <Column title="配置名称" dataIndex="name" width={180} />
            <Column title="归属应用" dataIndex="appname" width={180} />
            <Column title="修改时间" dataIndex="utime" width={180} render={
              (value) => (ts2str(value, 'yyyy-MM-dd hh:mm:ss'))
            }
            />
            <Column
              title="操作"
              width={150}
              render={(value, record) => (
                <div>
                  <a onClick={() => this.onEdit(record)}>编辑</a>
                  <a onClick={() => this.onDel(record)}>删除</a>
                </div>
              )}
            />

          </Table>
          <UpdateConfig params={this.state.modalUpdateState} />
        </Card>
      </div>
    );
  }
}

class UpdateConfig extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Config';
    this.rowKey = 'confid';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
      errmsg: null,
    };

  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.visible });
    if (this.props.params.updateid) {
      // this.formRef.current.resetFields();
      let data = {};
      data[this.rowKey] = this.props.params.updateid;
      restOPCall(this.objName, 'get', data).then((retdata) => {
        this.setState({ data: retdata });
      })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      // this.formRef.current.resetFields();
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((values) => {
      let data = {
        name: values.name,
        appid: parseInt(values.appid ?? 0),
        content: values.content,
      };

      this.setState({ confirmLoading: true });

      if (this.props.params.updateid) {
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else {
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false });
    this.props.callback?.onCancel?.();
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  afterClose = (e) => {
    // this.formRef.current.resetFields();
  }

  render () {

    const { visible } = this.state;
    const { ref } = this.props.params;

    return (
      <Modal visible={visible}
        title={this.props.params.updateid ? '修改应用' : '新增应用'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={this.afterClose}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={this.props.params.updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              name: this.state.data ? this.state.data.name : '',
              appid: (this.state.data ? this.state.data.appid : this.state.appid) + ''
            }}>

            <FormItem label="配置名称" name="name" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]}>
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="归属应用" name="appid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <ApplicationSelect />
            </FormItem>


            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}


export default Config;
