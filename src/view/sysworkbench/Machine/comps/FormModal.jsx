/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/08 15:13
 * @LastEditTime: 2022/02/22 13:49
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Machine\comps\FormModal.jsx
 * @Description: '云主机环境-表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Form,
  Spin,
  Modal,
  Input,
  Select
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';

const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'machine';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      companyOption: [],
    };
  }

  componentWillMount () { }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, });  // 重置状态数据
      this.getCompanyOption();
      if (nextProps.params.updateid) {
        const data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  async getCompanyOption () {
    const data = {};
    await apiCall('/company/sysOption', 'GET', data).then((res) => {
      this.setState({
        companyOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      const { sn, companyId } = formData;
      const data = {
        sn,
        companyId
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      }
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false, companyOption: [], });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, data, companyOption } = this.state;
    const { updateid } = this.props.params;

    return (
      <Modal visible={visible}
        title={updateid ? '修改云主机环境' : '新增云主机环境'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
        width={450}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef}
            labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{
              sn: data ? data.sn : '',
              companyId: data ? data.companyId : ''
            }}>

            <FormItem label="资源ID" rules={[{ required: true, message: '资源ID必填，请输入UCLOUD中的云主机资源ID' }]} name="sn">
              <Input />
            </FormItem>
            <FormItem label="所属公司" name="companyId">
              <Select
                allowClear
                options={companyOption}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </FormItem>

          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
