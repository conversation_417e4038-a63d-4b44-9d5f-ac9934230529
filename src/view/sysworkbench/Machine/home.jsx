/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2022/02/23 16:40
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Machine\home.jsx
 * @Description: '云主机环境'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  message,
  Button,
  Card,
  Table, Tooltip,
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';

class Machine extends Reflux.Component {

  constructor(props) {
    super(props);
    this.objName = 'machine';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '资源ID',
          width: '240px',
          dataIndex: 'sn',
          key: 'sn',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '名称',
          width: '240px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '地址',
          width: '240px',
          dataIndex: 'ipaddr',
          key: 'ipaddr',
          ellipsis: 'true',
          align: 'center',
          render: (value, record) => {
            const content = <>外网：{record.ipPublic}<br />内网：{record.ipPrivate}</>;
            return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
          }
        },
        {
          title: '配置',
          width: '240px',
          dataIndex: 'remark',
          key: 'remark',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '运行状态',
          width: '240px',
          dataIndex: 'status',
          key: 'status',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '操作',
          width: '200px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            <div>
              {/* <a onClick={() => this.handleEdit(record)}>修改</a> */}
              <a onClick={() => this.handleDel(record)}>删除</a>
            </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      FormModalParams: { visible: false },
      OperateModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.fetchList();
  }

  fetchList = (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个云主机环境吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/machine/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, columns, FormModalParams, loading, OperateModalParams } = this.state;
    return (
      <div>
        <Card bordered={false}>
          {/* <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增云主机</Button> */}
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1300 }}></Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default Machine;
