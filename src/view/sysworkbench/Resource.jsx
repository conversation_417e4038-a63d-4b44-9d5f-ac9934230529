import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { LoadingOutlined, CloudUploadOutlined } from '@ant-design/icons';
import {
  Row,
  Col,
  Select,
  message,
  Spin,
  Alert,
  Modal,
  Form,
  Button,
  Input,
  Card,
  Table,
  Upload,
} from 'antd';
import { ts2str, gen<PERSON>ey, restOPCall } from 'common/utils';
import ApplicationSelect from 'components/select/ApplicationSelect';
import moment from 'moment';
import queryString from 'query-string';
import Cookie from 'js-cookie';
import { beforeUpload } from 'common/image';

const { Option } = Select;
const Column = Table.Column;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Resource extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Resource';
    this.rowKey = 'resid';
    let params = queryString.parse(props.location.search);

    this.state = {
      modalUpdateState: { show: false },
      list: null,
      pagination: { current: 1, pageSize: 20 },   // 不需要翻页机制时，pagination=null即可
      loading: false,
      appid: parseInt(params['appid']),
      query: params['appid'] ? { // query
        'appid': [parseInt(params['appid'])]
      } : null

    };
  }

  getList = (pagination, query) => {
    const data = {
      pagination: pagination || { current: 1, pageSize: 10 },
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ list, pagination }) => {
        this?.setState({
          list,
          pagination,
          loading: false
        });
      });
  }

  onApplicationChange = (appid) => {
    let query = {
      'appid': [parseInt(appid)]
    };
    this.setState({ query, appid: parseInt(appid) });
    this.getList(
      null,
      query
    );
  }


  onTableChange = (pagination, filters, sorter) => {
    let query = {
      'appid': [this.state.appid]
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.getList();
        }
      }
    });
  }


  onEdit = (record) => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }

  onDel = (record) => {
    confirm({
      title: '您确认要删除这个数据资源吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        restOPCall(this.objName, 'del', data)
          .then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.getList();
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  onDownload = (record) => {
    location.href = '/webapi/download_resource_by_name?name=' + record['name'] + '&accesstoken=' + Cookie.get('weebot_token');
  }


  render () {

    const { list, pagination, appid } = this.state;
    return (
      <div>
        <Row justify="space-between">
          <Col span={6}><Button type="primary" style={{ marginBottom: '16px' }} onClick={this.onAdd}>新增资源</Button></Col>
          <Col span={18}><div align="right">选择应用：<ApplicationSelect width={200} defaultSelectFirst value={appid ? appid + '' : null} onChange={this.onApplicationChange} /></div></Col>
        </Row>
        <Card title={<span className="card-title-vertline">资源列表</span>} bordered={false}>
          <Table loading={this.state.loading} dataSource={list} pagination={pagination} rowKey="resid" onChange={this.onTableChange}>
            <Column title="资源名称" dataIndex="name" width={250} />
            <Column title="数据类型" dataIndex="type" width={100} render={(value) => {
              switch (value) {
                case 1: return '机器人资源';
                case 2: return '输出表格';
                case 3: return '数据文件';
              }
            }} />
            <Column title="更新时间" dataIndex="utime" width={180} render={(x) => ts2str(x, 'yyyy-MM-dd hh:mm:ss')} />
            <Column title="状态" dataIndex="status" width={180}
            />

            <Column
              title="操作"
              width={150}
              render={(value, record) => (
                <div>
                  <a onClick={() => this.onDownload(record)}>下载</a>
                  <a onClick={() => this.onEdit(record)}>修改</a>
                  <a onClick={() => this.onDel(record)}>删除</a>
                </div>
              )}
            />

          </Table>
          <UpdateResource params={this.state.modalUpdateState} />
        </Card>
      </div>
    );
  }
}


class UpdateResource extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Resource';
    this.rowKey = 'resid';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
      errmsg: null,
      type: 1,
      imageUrl: null,
      btndisable: true,
      fileid: null,
    };
  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.visible });
    if (this.props.params.updateid) {
      // this.formRef.current.resetFields();
      let data = {};
      data[this.rowKey] = this.props.params.updateid;
      restOPCall(this.objName, 'get', data)
        .then((retdata) => {
          this.setState({ data: retdata, type: retdata.type });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      // this.formRef.current.resetFields();
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            this.setState({ data: retdata, type: retdata.type });
          })
          .catch((ret) => {
            message.error(ret.msg);

            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((values) => {
      let data = {
        name: values.name,
        type: parseInt(values.type ?? 1),
        appid: parseInt(values.appid ?? 0)
      };

      if ((data.type == 1) && (this.state.fileid)) { data.fileid = this.state.fileid; }

      if (data.type == 2) { data.fileurl = values.fileurl; }
      if (data.type == 3) { data.connstr = values.connstr; }


      this.setState({ confirmLoading: true });

      if (this.props.params.updateid) {
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else {
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false });
    this.props.callback?.onCancel?.();
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  onGenAppSecret = () => {
    this.formRef.current.setFieldsValue({
      appsecret: genKey(16),
    });
  }

  onTypeChange = (value) => {
    this.setState({ type: parseInt(value) });
  }

  handleChange = (info) => {
    let fileList = [...info.fileList];
    fileList = fileList.slice(-1);
    this.setState({ fileList });

    if (info.file.status === 'uploading') {
      this.setState({ loading: true, btndisable: true, processing: false });
      return;
    }
    if (info.file.status === 'done') {
      this.setState({
        btndisable: false,
        loading: false,
        fileid: info.file.response?.result.filename,
      });
    }
  };

  afterClose = (e) => {
    this.setState({ type: 1, fileList: '' });
  }

  render () {

    const uploadButton = (
      <div style={{ width: 200, height: 160, padding: 60, fontSize: 14 }}>
        {(this.state.loading) ? (
          <LoadingOutlined />
        ) : (<CloudUploadOutlined />)}
        <div className="ant-upload-text">选择文件</div>

      </div>
    );

    const { visible, imageUrl } = this.state;
    const { ref } = this.props.params;
    return (
      <Modal visible={visible}
        title={this.props.params.updateid ? '修改资源' : '新增资源'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={this.afterClose}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={this.props.params.updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form preserve={false} layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              name: this.state.data ? this.state.data.name : '',
              type: (this.state.data ? this.state.data.type : 1) + '',
              fileurl: this.state.data ? this.state.data.fileurl : '',
              connstr: this.state.data ? this.state.data.connstr : '',
              appid: (this.state.data ? this.state.data.appid : this.state.appid) + ''
            }}>

            <FormItem label="资源名称" name="name" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]}>
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="资源类型" name="type" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>

              <Select defaultValue={'1'} style={{ width: 200 }} onChange={this.onTypeChange}>
                <Option value="1">本地文件</Option>
                <Option value="2">在线文件</Option>
                <Option value="3">数据库表</Option>
              </Select>
            </FormItem>

            {(this.state.type == 1) &&
              <FormItem label="上传文件" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>

                <Upload
                  name="file"
                  action={'/webapi/uploadfile?accesstoken=' + Cookie.get('weebot_token')}
                  beforeUpload={beforeUpload}
                  onChange={this.handleChange}
                  fileList={this.state.fileList}
                >
                  <Button>
                    <CloudUploadOutlined />点击上传
                  </Button>
                </Upload>
              </FormItem>
            }

            {(this.state.type == 2) &&
              <FormItem label="文件URL" name="fileurl" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <Input style={{ width: '100%' }} />
              </FormItem>
            }
            {(this.state.type == 3) &&
              <FormItem label="数据库连接符" name="connstr" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <Input style={{ width: '100%' }} />
              </FormItem>
            }

            <FormItem label="归属应用" name="appid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <ApplicationSelect />
            </FormItem>

            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}


export default Resource;
