import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import {
  Row,
  Col,
  Menu,
  Popover,
  Select,
  message,
  Spin,
  Alert,
  Modal,
  Form,
  Button,
  Input,
  Card,
  Table,
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import CustomIcon from 'components/base/Icon';
import ApplicationSelect from 'components/select/ApplicationSelect';
import moment from 'moment';
import queryString from 'query-string';
import QRCode from 'qrcode.react';

const { Option } = Select;
const Column = Table.Column;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Robot extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Robot';
    this.rowKey = 'robotid';
    let params = queryString.parse(props.location.search);
    this.state = {
      modalUpdateState: { show: false },
      modalShowQRCodeState: { show: false },
      list: null,
      pagination: { current: 1, pageSize: 10 },   // 不需要翻页机制时，pagination=null即可
      loading: false,
      appid: parseInt(params['appid']),
      query: params['appid'] ? { // query
        'appid': [parseInt(params['appid'])]
      } : null
    };
  }

  getList = (pagination, query) => {
    const data = {
      pagination: pagination || { current: 1, pageSize: 10 },
      query: query || this.state.query,
    };
    this.setState({ loading: true });
    restOPCall(this.objName, 'getlist', data)
      .then(({ list, pagination }) => {
        this?.setState({
          list,
          pagination,
          loading: false
        });
      });
  }

  onApplicationChange = (appid) => {
    let query = {
      'appid': [parseInt(appid)]
    };
    this.setState({ query, appid: parseInt(appid) });
    this.getList(
      null,
      query
    );
  }

  onTableChange = (pagination, filters, sorter) => {
    let query = {
      'appid': [this.state.appid]
    };

    this.setState({ query });
    this.getList(
      pagination,
      query
    );
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.getList();
        }
      }
    });
  }


  onEdit = (record) => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }

  onQRCode = (record) => {
    this.setState({
      modalShowQRCodeState: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: this.getList,
      }
    });
  }


  onDel = (record) => {
    confirm({
      title: '您确认要删除这个机器人吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        restOPCall(this.objName, 'del', data)
          .then(() => {
            if (this.state.pagination) { delete this.state.pagination.total; } // 触发重新获取总数;
            this.getList();
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  onReboot = (record) => {
    confirm({
      title: '您确认重启这个机器人吗?',
      onOk: () => {
        let data = {};
        data[this.rowKey] = record[this.rowKey];
        let cmdobj = {
          cmd: 'reboot',
          params: {}
        };
        data['cmdstr'] = JSON.stringify(cmdobj);

        apiCall('robot_cmd', 'GET', data)
          .then(() => {
            message.success('指令已发送');
          })
          .catch((ret) => {
            // 删除出错
            message.error(ret.msg);
          });
      },
      onCancel: () => { }
    });
  }

  onScreenShot = (record) => {
    let data = {};
    data[this.rowKey] = record[this.rowKey];
    let cmdobj = {
      cmd: 'screenshot',
      params: {}
    };
    data['cmdstr'] = JSON.stringify(cmdobj);
    apiCall('robot_cmd', 'GET', data)
      .then(() => {
        message.success('指令已发送');
      })
      .catch((ret) => {
        message.error(ret.msg);
      });
  }

  handleResetResource = (record) => {
    let data = {};
    data[this.rowKey] = record[this.rowKey];
    let cmdobj = {
      cmd: 'resetresource',
      params: {}
    };
    data['cmdstr'] = JSON.stringify(cmdobj);
    apiCall('robot_cmd', 'GET', data)
      .then(() => {
        message.success('指令已发送');
      })
      .catch((ret) => {
        message.error(ret.msg);
      });
  }

  render () {

    const { list, pagination, appid } = this.state;
    return (
      <div>
        <Row justify="space-between">
          <Col span={6}><Button type="primary" style={{ marginBottom: '16px' }} onClick={this.onAdd}>新增机器人</Button></Col>
          <Col span={18}><div align="right">选择应用：<ApplicationSelect width={200} defaultSelectFirst value={appid ? appid + '' : null} onChange={this.onApplicationChange} /></div></Col>
        </Row>
        <Card title={<span className="card-title-vertline">机器人列表</span>} bordered={false}>
          <Table loading={this.state.loading} dataSource={list} pagination={pagination} rowKey={this.rowKey} onChange={this.onTableChange}>
            <Column title="机器人名称" dataIndex="name" width={120} />
            <Column title="角色" dataIndex="role" width={60} />
            <Column title="RobotKey" dataIndex="robotkey" width={250} />
            <Column title="状态" dataIndex="status" width={130} render={(value, record) => {
              switch (value) {
                // 前两个是monitor回报的状态
                case 0: return '未初始化';
                case 1: return '未检测到信号';
                case 2: return '机器人已停止';
                case 3: return '机器人启动中';

                // 以下都是Robot回报的状态
                case 101: return (
                  <div>
                    <span style={{ color: 'red' }} >等待手工</span>
                    <Button onClick={() => this.onQRCode(record)}>处理</Button>
                  </div>
                );
                case 102: return <div style={{ color: 'green' }}>机器人正常</div>;
                case 103: return <div style={{ color: 'red' }}>机器人异常</div>;
              }
            }
            } />
            <Column
              title="操作"
              width={180}
              render={(value, record) => (
                <div>
                  <a onClick={() => this.onEdit(record)}>修改</a>
                  <a onClick={() => this.onDel(record)}>删除</a>

                  <Popover placement="bottom" trigger="click" content={
                    <div >
                      <Menu selectable={false}>
                        <Menu.Item key="stop" onClick={() => this.onReboot(record)}>停止</Menu.Item>
                        <Menu.Item key="reboot" onClick={() => this.onReboot(record)}>重启</Menu.Item>
                        <Menu.Item key="screen" onClick={() => this.onScreenShot(record)}>截屏</Menu.Item>
                        <Menu.Item key="resetresource" onClick={() => this.handleResetResource(record)}>更新资源</Menu.Item>
                      </Menu>
                    </div>
                  } >
                    <a><CustomIcon iconid="shenglvehao" /></a>
                  </Popover>

                </div>
              )}
            />

          </Table>
          <UpdateRobot params={this.state.modalUpdateState} />
          <ShowQRCode params={this.state.modalShowQRCodeState} />
        </Card>
      </div>
    );
  }
}

// @Form.create()
class UpdateRobot extends Reflux.Component {
  // 1. ---- 存储Form对象
  // formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Robot';
    this.rowKey = 'robotid';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
      errmsg: null,
    };
    this.formRef = React.createRef();
  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.visible });
    if (this.props.params.updateid) {
      this.formRef.current.resetFields();
      let data = {};
      data[this.rowKey] = this.props.params.updateid;
      restOPCall(this.objName, 'get', data)
        .then((retdata) => {
          this.setState({ data: retdata });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      // this.formRef.current.resetFields();
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            this.setState({ data: retdata });
          })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }


  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((values) => {
      let data = {
        name: values.name,
        type: parseInt(values.type ?? 0),
        appid: parseInt(values.appid),
        role: values.role
      };

      this.setState({ confirmLoading: true });

      if (this.props.params.updateid) {
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else {
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false });
    this.props.callback?.onCancel?.();
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  afterClose = (e) => {
    // this.formRef.current.resetFields();
  }

  render () {

    const { visible } = this.state;
    const { ref } = this.props.params;
    return (
      <Modal visible={visible}
        title={this.props.params.updateid ? '修改机器人' : '新增机器人'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={this.afterClose}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        okText={this.props.params.updateid ? '修改' : '新增'}
        cancelText="取消"
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            initialValues={{
              name: this.state.data ? this.state.data.name : '',
              type: (this.state.data ? this.state.data.type : 0) + '',
              appid: (this.state.data ? this.state.data.appid : this.state.appid) + '',
              role: this.state.data ? this.state.data.role : ''
            }}>

            <FormItem label="机器人名称" name="name" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
              rules={[{ required: true, message: '名称必填' }]}>
              <Input style={{ width: 200 }} />
            </FormItem>

            <FormItem label="机器人类型" name="type" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Select style={{ width: 200 }} >
                <Option value="0">——未指定——</Option>
                <Option value="1">微信Bot</Option>
                <Option value="2">WebBot</Option>
                <Option value="3">安卓Bot</Option>
                <Option value="4">iOSBot</Option>
                <Option value="5">控制Bot</Option>
              </Select>
            </FormItem>

            <FormItem label="归属应用" name="appid" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <ApplicationSelect width={200} />
            </FormItem>


            <FormItem label="角色" name="role" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Input style={{ width: 200 }} />
            </FormItem>


            <Alert description={this.state.errmsg} type="error" showIcon
              style={{ display: (this.state.errmsg != null) ? 'block' : 'none' }}
            />
          </Form>
        )}
      </Modal>
    );
  }
}

class ShowQRCode extends Reflux.Component {
  // 1. ---- 存储Form对象
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'Robot';
    this.rowKey = 'robotid';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
      errmsg: null,
    };
  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.visible });
    if (this.props.params.updateid) {
      let data = {};
      data[this.rowKey] = this.props.params.updateid;

      restOPCall(this.objName, 'get', data)
        .then((retdata) => {
          this.setState({ data: retdata });
        })
        .catch((ret) => {
          message.error(ret.msg);
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, errmsg: null });  // 重置状态数据

      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data)
          .then((retdata) => {
            this.setState({ data: retdata });
          })
          .catch((ret) => {
            message.error(ret.msg);
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((values) => {
      let data = {
        appname: values.appname,
        appsecret: values.appsecret,
        appurl: values.apprul
      };

      this.setState({ confirmLoading: true });

      if (this.props.params.updateid) {
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      } else {
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((ret) => {
            this.setState({ errmsg: ret.msg, confirmLoading: false });
          });
      }
    })
      .catch((errorInfo) => {
        console.log('errorInfo', errorInfo);
      });
  }

  onCancel = () => {
    this.setState({ visible: false });
    this.props.callback?.onCancel?.();
  }

  onFocus = () => {
    this.setState({ errmsg: null });
  }

  render () {

    const { visible } = this.state;

    return (
      <Modal visible={visible}
        title={'机器人登录'}
        maskClosable={false}
        onCancel={this.onCancel}
        destroyOnClose
        confirmLoading={this.state.confirmLoading}
        footer={null}
      >
        {((this.props.params.updateid) && (this.state.data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}>

            <FormItem label="登录名称" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
              <Input style={{ width: 200 }} value={(this.state.data ? this.state.data.login_name : '')} />
            </FormItem>

            {(this.state.data?.login_params != '')
              ? <FormItem label="请扫码" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <QRCode
                  value={this.state.data ? this.state.data.login_params : ''}
                  size={300} // 二维码的大小
                  fgColor="#000000" // 二维码的颜色
                />
              </FormItem>
              : <FormItem label="请操作" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
                <div style={{ color: 'red' }}>在手机端完成确认登录</div>
              </FormItem>
            }

          </Form>
        )}
      </Modal>
    );
  }
}


export default Robot;
