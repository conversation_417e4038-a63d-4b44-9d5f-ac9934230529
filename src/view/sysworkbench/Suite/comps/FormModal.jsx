/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/08 15:20
 * @LastEditTime: 2022/02/22 11:42
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Suite\comps\FormModal.jsx
 * @Description: '套件模板-表单对话框'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  Form,
  Spin,
  Modal,
  Input,
} from 'antd';
import { restOPCall } from 'common/utils';
const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.objName = 'kit';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
    };
  }

  componentWillMount () { }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null }); // 重置状态数据
      if (nextProps.params.updateid) {
        const data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      const data = { ...formData };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) {
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          })
          .finally(() => this.formRef.current.resetFields({}));
      }
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({
      visible: false,
      confirmLoading: false,
    });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, data } = this.state;
    const { updateid } = this.props.params;

    return (
      <Modal visible={visible}
        title={updateid ? '修改套件' : '新增套件'}
        maskClosable={false}
        onOk={this.handleSubmit} onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
        width={450}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form ref={this.formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{
              name: data?.name ?? '',
              key: data?.key ?? '',
              description: data?.description ?? '',
              url: data?.url ?? '',
            }}>

            <FormItem label="套件名称" rules={[{ required: true, message: '名称必填' }]} name="name">
              <Input allowClear />
            </FormItem>

            <FormItem label="套件路径" rules={[{ required: true, message: '路径必填' }]} name="key" >
              <Input allowClear />
            </FormItem>

            <FormItem label="套件描述" name="description">
              <Input allowClear />
            </FormItem>

            <FormItem label="网址" name="url">
              <Input allowClear />
            </FormItem>
          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
