/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/09/25 17:05
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Suite/home.jsx
 * @Description: '套件模板'
 */

import React from 'react';
import Reflux from 'reflux';
import {
  message,
  Button,
  Card,
  Table,
  Tooltip,
} from 'antd';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';

class Suite extends Reflux.Component {

  constructor(props) {
    super(props);
    this.objName = 'kit';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '套件名称',
          width: '200px',
          dataIndex: 'name',
          key: 'name',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '套件路径',
          width: '200px',
          dataIndex: 'key',
          key: 'key',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '套件描述',
          width: '300px',
          dataIndex: 'description',
          key: 'description',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '套件菜单',
          width: '400px',
          dataIndex: 'menu',
          key: 'menu',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '网址',
          width: '300px',
          dataIndex: 'url',
          key: 'url',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '操作',
          width: '200px',
          align: 'center',
          fixed: 'right',
          render: (value, record, index) => (
            <div>
              <a onClick={() => this.handleEdit(record)}>编辑</a>
              <a onClick={() => this.handleDel(record)}>删除</a>
            </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      FormModalParams: { visible: false },
      OperateModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.fetchList();
  }

  fetchList = (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    restOPCall(this.objName, 'getlist', data).then(({ records, current, pages, size, total }) => {
      this.setState({
        dataSource: records,
        pagination: {
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    const { id } = record;
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: id,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        },
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个套件模板吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/kit/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, columns, OperateModalParams, loading, FormModalParams } = this.state;
    return (
      <div>
        <Card bordered={false}>
          <Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增套件模板</Button>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1500 }}>
          </Table>
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default Suite;
