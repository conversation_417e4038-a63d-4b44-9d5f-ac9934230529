/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/06 17:17
 * @LastEditTime: 2023/02/17 10:33
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\SysAccount\comps\FormModal.jsx
 * @Description: '系统账号-新增/修改账号'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { Form, Input, Modal, Spin } from 'antd';
import { restOPCall } from 'common/utils';
import GeneralSelect from 'components/select/GeneralSelect';
import SysDictSelect from 'components/select/SysDictSelect';

const FormItem = Form.Item;

class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor (props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      visible: false,
      confirmLoading: false,
      roleType: null,
      shortDomain: null,
    };
  }

  componentWillMount () {
    super.componentWillMount();
    this.setState({ visible: this.props.params.show });
    if (this.props.params.updateid) {
      let data = {};
      data[this.rowKey] = this.props.params.updateid;
      restOPCall(this.objName, 'get', data).then((retdata) => {
        if (retdata.account.indexOf('@') != -1) {
          retdata.shortDomain = retdata.account.substring(retdata.account.indexOf('@') + 1);
          retdata.account = retdata.account.substring(0, retdata.account.indexOf('@'));
        }
        this.setState({ data: retdata });
      })
        .catch((err) => {
          this.onCancel();
        });
    }
  }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.setState({ visible: nextProps.params.visible, data: null, });  // 重置状态数据
      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          if (retdata.account.indexOf('@') != -1) {
            retdata.shortDomain = retdata.account.substring(retdata.account.indexOf('@') + 1);
            retdata.account = retdata.account.substring(0, retdata.account.indexOf('@'));
          }
          this.setState({
            data: retdata,
            roleType: nextProps.params.roleType
          });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    const { User } = this.state;
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        account: formData.account + ((User?.shortDomain) ? ('@' + (this.state.shortDomain ?? User.shortDomain)) : ''),    // 补充域名信息
        password: formData.password,
        roleId: formData.roleId ? parseInt(formData.roleId) : null,
        status: formData.status,
        employeeId: formData.employeeId ? parseInt(formData.employeeId) : null,
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新账号
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      } else { // 新增账号
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      }
      // 提交成功后 -> 重置表单内容
      this.formRef?.current?.resetFields();
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false, roleType: null });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, User, data, confirmLoading } = this.state;
    const { updateid } = this.props.params;
    // const isDisabled = !((!data) || User.roleType == 1 || (User.roleType == 2 && data.roleType != 2));
    const isDisabled = !((!data) || User);
    const initialValues = {
      account: data ? data.account : '',
      shortDomain: data ? data.account : '',
      password: data ? 'NOT_CHANGE' : '',
      roleId: (data?.roleId ?? '') + '',
      status: (data?.status ?? '') + '',
      employeeId: (data?.employeeId ?? '') + '',
    };
    return (
      <Modal visible={visible}
        title={updateid ? '修改账号' : '新增账号'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        destroyOnClose
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" ref={this.formRef} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={initialValues}>

            <FormItem label="登录账号" rules={[{ required: true, message: '账号必填' }]} name="account">
              <Input disabled={isDisabled} suffix={data?.shortDomain ? ('@' + data?.shortDomain) : (User.shortDomain ? ('@' + User.shortDomain) : '')} />
            </FormItem>

            <FormItem name="shortDomain" hidden />

            <FormItem label="初始密码" name="password">
              <Input.Password visibilityToggle={false} />
            </FormItem>

            <FormItem label="账号角色" rules={[{ required: true, message: '角色必选' }]} name="roleId">
              <GeneralSelect disabled={isDisabled} schema="role" />
            </FormItem>

            <FormItem label="账号状态" name="status" >
              <SysDictSelect disabled={isDisabled} dataset="USERSTATUS" />
            </FormItem>

            <FormItem label="关联员工" name="employeeId">
              <GeneralSelect schema="appemployee" params={{ type: 'unbundlinguser', extraId: data?.employeeId }} allowClear />
            </FormItem>
          </Form>
        )}
      </Modal>
    );
  }
}

export default FormModal;
