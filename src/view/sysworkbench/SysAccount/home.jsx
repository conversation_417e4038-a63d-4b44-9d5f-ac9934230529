/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/06 17:17
 * @LastEditTime: 2023/11/17 11:18
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/SysAccount/home.jsx
 * @Description: '系统账号'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { Button, Card, Col, Form, Input, message, Row, Table, Tooltip } from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import GeneralSelect from 'components/select/GeneralSelect';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';

const FormItem = Form.Item;

class SysAccount extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      dataSource: null,
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
          sorter: (a, b) => a.id - b.id,
        },
        {
          title: '关联员工姓名',
          width: '160px',
          align: 'center',
          dataIndex: 'employeeName',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '关联手机号',
          width: '160px',
          align: 'center',
          dataIndex: 'mobile',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '微信绑定状态',
          width: '160px',
          align: 'center',
          dataIndex: 'openid',
          render: (value) => value ? <font color="green">已绑定</font> : '未绑定'
        },
        {
          title: '微信昵称',
          width: '160px',
          align: 'center',
          dataIndex: 'wechatName',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '账号角色',
          width: '160px',
          align: 'center',
          dataIndex: 'roleName',
          render:
            (text, row, index) => text ? text : <SysDictLabel dataset="ROLETYPE" dictkey={row.roleType} />
        },
        {
          title: '创建时间',
          width: '160px',
          dataIndex: 'createTime',
          key: 'createTime',
          align: 'center',
          sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime',
          width: '160px',
          key: 'updateTime',
          align: 'center',
          sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
        },
        {
          title: '账号状态',
          width: '160px',
          align: 'center',
          dataIndex: 'status',
          render: (value) => <SysDictLabel dataset="USERSTATUS" dictkey={value + ''} color />
        },
        {
          title: '操作',
          width: '160px',
          fixed: 'right',
          ellipsis: 'true',
          align: 'center',
          render: (value, record, index) => (
            <div>
              {(record.roleType == 1 || record.roleType == 2) ? ''
                : (record.status == 0) ? <a onClick={() => this.handleChangeStatus(record, 1)}>启用</a>
                  : <a onClick={() => this.handleChangeStatus(record, 0)}>禁用</a>
              }
              {record.openid ? <a onClick={() => this.handleUnbindWx(record)}>解绑微信</a> : null}
              <a onClick={() => this.handleEdit(record)}>编辑</a>
              {
                (record.roleType == 1 || record.roleType == 2) ? '' : <a onClick={() => this.handleDel(record)}>删除</a>
              }
            </div>
          )
        },
      ],
      pagination: { current: 1, pageSize: 10 },
      loading: false,
      FormModalParams: { visible: false },
      OperateModalParams: { visible: false },
    };
  }

  componentDidMount = () => {
    this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({ loading: true });
    let formInfo = {};
    await this.formRef.current.validateFields().then((formData) => {
      formInfo = {
        account: formData.account,
        employeeName: formData.employeeName,
        mobile: formData.mobile,
        roleId: formData.roleId ? parseInt(formData.roleId) : null,
        status: formData.status ? parseInt(formData.status) : null
      };
    });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...formInfo,
      ...query,
    };
    apiCall('user/pagesys', 'GET', data)
      .then(({ records, current, pages, size, total }) => {
        this.setState({
          dataSource: records,
          pagination: {
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
          }
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  handleReset = (e) => {
    this.formRef.current.resetFields();
    this.fetchList({ pagination: { current: 1, pageSize: 10 } });
  }

  handleQuery = async (e) => {
    this.fetchList();
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  handleAdd = () => {
    this.setState({
      FormModalParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    this.setState({
      FormModalParams: {
        visible: true,
        roleType: record.roleType,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个账号吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/user/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  handleChangeStatus = (record, value) => {
    let data = { id: record.id, status: value };
    this.setState({ loading: true });
    apiCall('user/changestatus', 'GET', data).then(() => {
      this.fetchList();
    })
      .catch((err) => {
        console.log(err);
      });
  }

  handleUnbindWx = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '解除该账号与微信账号绑定后，该账号将无法使用微信登录，是否确认解除?',
        onSubmit: () => {
          apiCall(`user/${id}/unbindWx`, 'PUT').then((res) => {
            message.success('解绑成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  render () {
    const { dataSource, pagination, loading, FormModalParams, columns, OperateModalParams } = this.state;
    return (
      <div>
        <Card bordered={false}>
          <Form className="ant-advanced-search-form" ref={this.formRef}>
            <Row gutter={24}>
              <Col span={4} >
                <FormItem name="account">
                  <Input placeholder="账号" />
                </FormItem>
              </Col>
              <Col span={4}>
                <FormItem name="employeeName">
                  <Input placeholder="关联员工姓名" />
                </FormItem>
              </Col>

              <Col span={4}>
                <FormItem name="mobile">
                  <Input placeholder="关联手机号" />
                </FormItem>
              </Col>
              <Col span={4}>
                <FormItem name="roleId">
                  <GeneralSelect mode="multiple" placeholder="账号角色" style={{ width: 200 }} schema="role" />
                </FormItem>
              </Col>
              <Col span={4}>
                <FormItem name="status" >
                  <SysDictSelect mode="multiple" placeholder='账号状态' style={{ width: 200 }} dataset="USERSTATUS" />
                </FormItem>
              </Col>

            </Row>

            <Row justify="space-between">
              <Col span={12}>
                <Row justify="start" gutter={24}>
                  <Col><Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleQuery}>查询</Button></Col>
                  <Col><Button style={{ marginBottom: '16px' }} onClick={this.handleReset}>重置</Button></Col>
                </Row>
              </Col>
              <Col span={12}>
                <Row justify="end" gutter={24}>
                  <Col><Button type="primary" style={{ marginBottom: '16px' }} onClick={this.handleAdd}>新增账号</Button></Col>
                </Row>
              </Col>
            </Row>
          </Form>
        </Card>
        <br />
        <Card bordered={false}>
          <Table loading={loading} dataSource={dataSource} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1500 }} />
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default SysAccount;
