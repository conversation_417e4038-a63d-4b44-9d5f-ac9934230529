/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2021/10/20 10:27
 * @LastEditors: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\SysDict\comps\FormMoadl.jsx
 * @Description: '系统字典-添加/修改弹窗'
 */

import React from 'react';
import Reflux from 'reflux';
import AppStore from 'stores/AppStore';
import { restOPCall } from 'common/utils';
import { BlockPicker } from 'react-color';
import { Modal, Input, Form, Spin, } from 'antd';
import './FormMoadl.less';

const FormItem = Form.Item;

class FormMoadl extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'SysDict';
    this.rowKey = 'id';
    this.state = {
      visible: props.params.visible,
      confirmLoading: false,
    };
  }

  componentWillMount () { }

  componentWillUpdate (nextProps, nextState) {
    if (this.props.params.ts != nextProps.params.ts) {
      // 时间戳有更新, 属于外面传入
      this.formRef.current?.resetFields();
      this.setState({ visible: nextProps.params.visible, data: null, });  // 重置状态数据
      if (nextProps.params.updateid) {
        let data = {};
        data[this.rowKey] = nextProps.params.updateid;
        restOPCall(this.objName, 'get', data).then((retdata) => {
          this.setState({ data: retdata });
        })
          .catch((err) => {
            this.onCancel();
          });
      }
    }
  }

  handleSubmit = async (e) => {
    e.preventDefault();
    await this.formRef.current.validateFields().then((formData) => {
      let data = {
        dataset: formData.dataset,
        key: formData.key,
        value: formData.value,
        color: formData.color.hex
      };
      this.setState({ confirmLoading: true });
      if (this.props.params.updateid) { // 更新应用
        data[this.rowKey] = this.props.params.updateid;
        restOPCall(this.objName, 'update', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      } else { // 新增应用
        restOPCall(this.objName, 'add', data).then(() => {
          this.setState({ visible: false, confirmLoading: false });
          this.props.params?.onUpdated?.();
        })
          .catch((err) => {
            this.setState({ confirmLoading: false });
          });
      }
      // 提交成功后 -> 重置表单内容
      this.formRef?.current?.resetFields();
    })
      .catch((err) => {
        console.log('errorInfo', err);
      });
  }

  onCancel = () => {
    this.setState({ visible: false, confirmLoading: false });
    this.formRef?.current?.resetFields();
    this.props.callback?.onCancel?.();
  }

  render () {
    const { visible, confirmLoading, data } = this.state;
    const { updateid } = this.props.params;

    return (
      <Modal visible={visible}
        title={updateid ? '修改字典项' : '新增字典项'}
        maskClosable={false}
        onOk={this.handleSubmit}
        onCancel={this.onCancel}
        afterClose={null}
        confirmLoading={confirmLoading}
        okText={updateid ? '修改' : '新增'}
      >
        {((updateid) && (data == null)) ? (
          <div style={{ textAlign: 'center', fontSize: '16px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Form layout="horizontal" hideRequiredMark ref={this.formRef}
            labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}
            initialValues={{
              dataset: data?.dataset || '',
              key: data?.key || '',
              value: data?.value || '',
              color: data?.color || '#000000'
            }
            }
          >
            <FormItem label="数据集" name="dataset"
              rules={[{ required: true, message: '数据集必填' }]} >
              <Input />
            </FormItem>

            <FormItem label="KEY" name="key"
              rules={[{ required: true, message: 'KEY必填' }]} >
              <Input />
            </FormItem>

            <FormItem label="VALUE" name="value"
              rules={[{ required: true, message: 'VALUE必填' }]} >
              <Input />
            </FormItem>

            <FormItem label="颜色" name="color" valuePropName="color" >
              <BlockPicker />
            </FormItem>

          </Form>
        )}
      </Modal>
    );
  }
}

export default FormMoadl;
