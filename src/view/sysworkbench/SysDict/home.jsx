/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/09/25 17:07
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/SysDict/home.jsx
 * @Description: '系统字典'
 */

import React from 'react';
import Reflux from 'reflux';
import AppActions from 'actions/AppActions';
import AppStore from 'stores/AppStore';
import { restOPCall, apiCall } from 'common/utils';
import moment from 'moment';
import { Card, Button, Table, Tooltip, message } from 'antd';
import FormMoadl from './comps/FormMoadl';
import OperateModal from 'components/Modal/OperateModal/index';

class SysDict extends Reflux.Component {

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'SysDict';
    this.rowKey = 'id';
    this.state = {
      loading: false,
      dataSource: null,
      columns: [],
      pagination: { current: 1, pageSize: 10 },
      FormMoadlParams: { visible: false },
      OperateModalParams: { visible: false },
      dsoptions: [],
    };
  }

  componentDidMount () {
    this.didMount();
    this.fetchList();
  }

  didMount = () => {
    const { g_sysdict } = this.state;
    this.setState({
      columns: [
        {
          title: '序号',
          width: '80px',
          align: 'center',
          render: (text, record, index) => index + 1,
        },
        {
          title: '数据集',
          width: '200px',
          dataIndex: 'dataset',
          key: 'dataset',
          ellipsis: 'true',
          align: 'center',
          filters: Object.keys(g_sysdict)?.map((x) => ({ value: x, text: x })),
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: 'KEY',
          width: '200px',
          dataIndex: 'key',
          key: 'key',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: 'VALUE',
          width: '200px',
          dataIndex: 'value',
          key: 'value',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '颜色',
          width: '200px',
          dataIndex: 'color',
          key: 'color',
          ellipsis: 'true',
          align: 'center',
          render: (value) => <span style={{ color: value }}>{value}</span>
        },
        {
          title: '操作',
          width: '160px',
          fixed: 'right',
          ellipsis: 'true',
          align: 'center',
          render: (value, record, index) => (
            <div>
              <a onClick={() => this.handleEdit(record)}>修改</a>
              <a onClick={() => this.handleDel(record)}>删除</a>
            </div>
          )
        },
      ]
    });
  }

  fetchList = (params = {}) => {
    this.setState({ loading: true });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      dataset: query?.dataset?.join(',') ?? '',
    };
    restOPCall(this.objName, 'getlist', data)
      .then(({ records, current, pages, size, total }) => {
        this.setState({
          dataSource: records,
          pagination: {
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
          },
        });
      })
      .catch((ret) => {
        console.log(ret);
      })
      .finally(() => {
        this.setState({
          loading: false,
        });
      });
  }

  handleAdd = () => {
    this.setState({
      FormMoadlParams: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  handleEdit = (record) => {
    this.setState({
      FormMoadlParams: {
        visible: true,
        updateid: record[this.rowKey],
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('修改成功');
          this.fetchList();
        }
      }
    });
  }

  handleDel = (record) => {
    const { id } = record;
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '删除确认',
        content: '您确认要删除这个字典项吗?',
        onSubmit: () => {
          const data = {
            id,
          };
          apiCall('/sysdict/del', 'DELETE', data).then((res) => {
            message.success('删除成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  onPageChange = (selectedpage) => {
    this.state[this.objName + 'datas'].pagination.current = selectedpage;
    AppActions.Rest_gets(this.objName, { query: this.state.query, page: this.state[this.objName + 'datas'].pagination });
  }

  onTableChange = (pagination, filters, sorter) => {
    const query = {
      dataset: filters.dataset ? filters.dataset : null
    };
    this.setState({ query });
    this.fetchList({ pagination, query });
  }

  render () {
    const { dataSource, pagination, FormMoadlParams, columns, loading, OperateModalParams } = this.state;
    return (
      <Card bordered={false}>
        <Button type="primary" onClick={this.handleAdd} style={{ marginBottom: 16 }}>添加字典项</Button>
        <Table bordered loading={loading} dataSource={dataSource} columns={columns} pagination={pagination}
          rowKey={this.rowKey} onChange={this.onTableChange} scroll={{ x: 1300 }} />
        <FormMoadl params={FormMoadlParams} />
        <OperateModal params={OperateModalParams} />
      </Card>
    );
  }
}

export default SysDict;
