/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/01/11 14:14
 * @LastEditTime: 2022/02/22 13:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\sysworkbench\Task\comps\FormModal.jsx
 * @Description: '任务状态-FormModal '
 */
import React from 'react';
import Reflux from 'reflux';
import { Modal, Form, Input, Button } from 'antd';
import AppStore from 'stores/AppStore';
import SysDictSelect from 'components/select/SysDictSelect';
import moment from 'moment';

const FormItem = Form.Item;
const { TextArea } = Input;
class FormModal extends Reflux.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.state = {
      visible: false,
      confirmLoading: false,
      employeeOption: [],
      employeeOptionsAppUser: [],
    };
  }

  componentDidMount () {

  }


  async componentDidUpdate (prevProps, prevState, snapshot) {
    this.didUpdate(prevProps, prevState);
  }

  didUpdate = async (prevProps, prevState) => {
    const { ts, visible, params } = this.props.params;
    try {
      // 时间戳有更新, 属于外面传入
      if (ts != prevProps.params.ts) {
        // 重置状态数据
        await this.setState({ visible, data: params });
        if (this.state.data?.id != prevState.data?.id) {
          let timer = setTimeout(() => {
            this.formRef.current?.setFieldsValue({
              ...this.state.data,
              state: this.state.data.state + '',
              createTime: this.state.data.createTime ? moment(this.state.data.createTime).format('YYYY-MM-DD HH:mm:ss') : '',
              sendTime: this.state.data.sendTime ? moment(this.state.data.sendTime).format('YYYY-MM-DD HH:mm:ss') : '',
            });
            clearTimeout(timer);
          }, 100);
        }
      }
    } catch (err) {
      console.log(err);
      this.onCancel();
    }
  }

  handleSure = () => {
    this.setState({ visible: false });
  }

  render () {
    const { visible, confirmLoading } = this.state;
    let content = null;
    content = <Form layout="horizontal" ref={this.formRef}
      labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} >
      <FormItem label="任务参数" name="content" initialValue={'{}'}>
        <TextArea
          autoSize={{ minRows: 2, maxRows: 6 }}
          disabled
        // onChange={this.onChangeJsonVal}
        />
      </FormItem>
      <FormItem label="执行的BOT" name="robotName" >
        <Input disabled />
      </FormItem>
      <FormItem label="任务状态" name="state" >
        <SysDictSelect dataset="TASK_STATUS" disabled />
      </FormItem>
      <FormItem label="创建时间" name="createTime" >
        <Input disabled />
      </FormItem>
      <FormItem label="下发时间" name="sendTime" >
        <Input disabled />
      </FormItem>
    </Form>;
    return (
      <Modal visible={visible}
        title="任务状态"
        maskClosable={false}
        afterClose={null}
        onCancel={this.handleSure}
        footer={[
          <Button key="back" type="primary" onClick={this.handleSure}>
            确定
          </Button>
        ]}
        destroyOnClose
        confirmLoading={confirmLoading}
        width={700}
      >
        {content}
      </Modal>
    );
  }
}

export default FormModal;

