/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/09/03 10:16
 * @LastEditTime: 2023/11/17 11:18
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/sysworkbench/Task/home.jsx
 * @Description: '任务状态'
 */

import React from 'react';
import AppStore from 'stores/AppStore';
import {
  message,
  Card,
  Table,
  Tooltip, Form, Input, Select, Button
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import FormModal from './comps/FormModal';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import OperateModal from 'components/Modal/OperateModal/index';

const FormItem = Form.Item;

class Task extends React.Component {

  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.stores = [AppStore];
    this.objName = 'user';
    this.rowKey = 'id';
    this.state = {
      list: null,
      columns: [
        {
          title: '任务参数',
          dataIndex: 'content',
          key: 'content',
          width: '200px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '执行的BOT',
          dataIndex: 'robotName',
          key: 'robotName',
          width: '100px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '公司名称',
          dataIndex: 'companyName',
          key: 'companyName',
          width: '100px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '微信名称',
          dataIndex: 'wechatName',
          key: 'wechatName',
          width: '100px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '操作名称',
          dataIndex: 'actionNames',
          key: 'actionNames',
          width: '150px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
        },
        {
          title: '任务状态',
          dataIndex: 'state',
          key: 'state',
          width: '100px',
          align: 'center',
          ellipsis: 'true',
          render: (value) => <SysDictLabel dataset="TASK_STATUS" dictkey={value} />
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: '160px',
          align: 'center',
        },
        {
          title: '下发时间',
          dataIndex: 'sendTime',
          key: 'sendTime',
          width: '160px',
          align: 'center',
        },
        {
          title: '操作',
          width: '150px',
          fixed: 'right',
          ellipsis: 'true',
          align: 'center',
          render: (value, record, index) => (
            <div>
              <a onClick={() => this.onReIssue(record)}>重新下发</a>
              <a onClick={() => this.onCheck(record)}>查看</a>
            </div>
          )
        }
      ],
      pagination: { current: 1, pageSize: 10 }, // 不需要翻页机制时，pagination=null即可
      loading: false,
      companyOption: [],
      robotOption: [],
      OperateModalParams: { visible: false },
      FormModalParams: { visible: false },
    };
  }

  componentDidMount () {
    this.getCompanyOption();
    this.getRobotOption();
    this.fetchList();
  }

  fetchList = async (params = {}) => {
    this.setState({ loading: true });
    let formInfo = {};
    await this.formRef.current.validateFields().then((formData) => {
      formInfo = formData;
    });
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      source: 2,
      ...query,
      ...formInfo
    };
    apiCall('/cmd/page', 'GET', data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        this.setState({
          list: records,
          pagination: {
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
          },
        });
      })
      .catch((ret) => {
        // 出错
        console.log(ret);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  }

  getCompanyOption () {
    const data = {};
    apiCall('/company/sysOption', 'GET', data).then((res) => {
      this.setState({
        companyOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  getRobotOption () {
    const data = {};
    apiCall('/robot/optionNotTenement', 'GET', data).then((res) => {
      this.setState({
        robotOption: res.map((item) => ({
          label: item.name,
          value: Number(item.id)
        })
        )
      });
    })
      .catch((err) => {
        console.log(err);
      });
  }

  onTableChange = (pagination, filters, sorter) => {
    this.fetchList({ pagination });
  }

  onAdd = () => {
    this.setState({
      modalUpdateState: {
        visible: true,
        updateid: null,
        ts: parseInt(moment().format('X')),
        onUpdated: () => {
          message.success('新增成功');
          this.fetchList();
        }
      }
    });
  }

  onReIssue = (record) => {
    this.setState({
      OperateModalParams: {
        visible: true,
        title: '重新下发确认',
        content: '您确认要重新下发吗?',
        onSubmit: () => {
          const data = {
            taskId: record.id,
          };
          apiCall('/cmd/reissue_command', 'GET', data).then((res) => {
            message.success('重新下发成功');
            this.fetchList();
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.setState({
                OperateModalParams: {
                  visible: false,
                }
              })
            });
        }, onCancel: () => {
          this.setState({
            OperateModalParams: {
              visible: false,
            }
          })
        }
      }
    });
  }

  onCheck = (record) => {
    this.setState({
      FormModalParams: {
        visible: true,
        params: record,
        ts: parseInt(moment().format('X')),
        onUpdated: () => { }
      }
    });
  }

  handleQuery = () => {
    this.fetchList();
  }

  handleReset = () => {
    this.formRef.current.resetFields();
    this.fetchList({ pagination: { current: 1, pageSize: 10 } });
  };


  render () {
    const { list, pagination, loading, columns, OperateModalParams, FormModalParams, companyOption, robotOption } = this.state;
    return (
      <div>
        <FilterBar>
          <Form layout={'inline'} ref={this.formRef}>
            <FormItem name="state" >
              <SysDictSelect placeholder="任务状态" dataset="TASK_STATUS" />
            </FormItem>
            <FormItem name="companyId">
              <Select
                allowClear
                options={companyOption}
                showSearch
                placeholder="所属公司"
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </FormItem>
            <FormItem name="robotId">
              <Select
                allowClear
                options={robotOption}
                showSearch
                placeholder="机器人"
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </FormItem>
          </Form>
          <Button type="primary" htmlType="submit" onClick={() => this.handleQuery()}>
            查询
          </Button>
          <Button onClick={() => this.handleReset()}>重置</Button>
        </FilterBar>
        <Card bordered={false}>
          <Table loading={loading} dataSource={list} columns={columns} pagination={pagination} rowKey="id"
            onChange={this.onTableChange} scroll={{ x: 1300 }} />
        </Card>
        <FormModal params={FormModalParams} />
        <OperateModal params={OperateModalParams} />
      </div>
    );
  }
}

export default Task;
