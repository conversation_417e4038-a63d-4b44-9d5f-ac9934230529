import { apiCall } from "common/utils";

/**
 * 查询客户群消息列表
 * @param current
 * @param depEmployeeIdList
 * @param maxStartTime
 * @param minStartTime
 * @param nameOrDescription
 * @param paged
 * @param size
 * @param state
 * @returns {Promise | Promise<unknown>}
 */
export function queryCustomerGroupMsgList({
  current,
  depEmployeeIdList,
  maxStartTime,
  minStartTime,
  nameOrDescription,
  paged,
  size,
  state,
}) {
  return apiCall("/customerGroupMsg", "GET", {
    current,
    depEmployeeIdList,
    maxStartTime,
    minStartTime,
    nameOrDescription,
    paged,
    size,
    state,
  });
}

/**
 * 创建客户群发
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export function postCreateCustomerGroupMsg(data) {
  return apiCall("/customerGroupMsg", "POST", data);
}

/**
 * 更新客户群发
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export function postUpdateCustomerGroupMsg(customerGroupMsgId, data) {
  return apiCall(
    `/customerGroupMsg/update/${customerGroupMsgId}`,
    "POST",
    data,
  );
}

/**
 * 查询客户群发详情
 * @param customerGroupMsgId
 * @returns {Promise | Promise<unknown>}
 */
export function queryCustomerGroupMsgDetail(customerGroupMsgId) {
  return apiCall(`/customerGroupMsg/${customerGroupMsgId}`, "GET", {});
}

/**
 * 员工统计
 * @param current
 * @param size
 * @param customerGroupMsgId
 * @param state
 * @param depEmployeeIdList
 * @returns {Promise<unknown>}
 */
export function queryCustomerGroupMsgCustomerTable({
  current,
  size,
  customerGroupMsgId,
  state,
  depEmployeeIdList,
}) {
  return apiCall(`/customerGroupMsg/publish`, "GET", {
    current,
    size,
    customerGroupMsgId,
    state,
    depEmployeeIdList,
  });
}

/**
 * 同步数据
 * @param customerGroupMsgId
 * @returns {Promise | Promise<unknown>}
 */
export function postSyncCustomer({ customerGroupMsgId }) {
  return apiCall(
    `/customerGroupMsg/manualFetchResult?customerGroupMsgId=${customerGroupMsgId}`,
    "POST",
    {},
  );
}

/**
 * 更新客户群发
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export function updateCustomerGroupMsg(data) {
  return apiCall(
    `/customerGroupMsg/update/${customerGroupMsgId}`,
    "POST",
    data,
  );
}

/**
 * 获取群发客户数量
 * @param data
 * @returns {Promise | Promise<unknown>}
 */
export function queryCustomerGroupMsgCount(data) {
  return apiCall("/customerGroupMsg/countCustomer", "POST", data);
}

/**
 * 停止同步
 * @param customerGroupMsgId
 * @returns {Promise | Promise<unknown>}
 */
export function postStopSync(customerGroupMsgId) {
  return apiCall(
    `/customerGroupMsg/manualStop?customerGroupMsgId=${customerGroupMsgId}`,
    "POST",
    {},
  );
}
