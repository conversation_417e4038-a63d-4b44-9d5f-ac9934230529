.conversation-Container {
  width: 300px;
  height: 500px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0px 0px 10px #999;
  margin: 0 auto;

  .ant-modal-content {
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    background: #3364d6;
    position: relative;
    color: #fff;
  }

  .close {
    color: #fff;
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 18px;
    cursor: pointer;
  }

  .custom-scrollbar {
    //padding-top: 12px;
    overflow: auto;
    height: calc(100% - 50px);
    .item {
      display: flex;
      margin-top: 8px;
      .left {
        margin: 8px;
      }
      .right {
        margin-right: 8px;
        margin-top: 4px;

        .template {
          p {
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 6px;
            margin-bottom: 0;
            box-shadow: 0 0 1px;
          }
        }
      }
    }
  }

  .content {
    padding: 0 10px 0 16px;
    margin-top: 25px;
    display: flex;
    max-height: 328px;

    .right {
      margin: -2px 0 0 8px;

      .name {
        color: #295cd0;
        font-size: 14px;
      }

      .template {
        p {
          margin: 0 0 4px 0;
          white-space: pre-line;
        }

        .ant-image {
          margin: 0 6px 6px 0;

          img {
            object-fit: cover;
          }
        }

        .link-card {
          .ant-image {
            margin: unset;
          }
        }
      }
    }
  }
}
