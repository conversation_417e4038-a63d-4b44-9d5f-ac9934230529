/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 14:57
 * @LastEditTime: 2024/07/12 15:06
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/components/WibotMomentPreview/modal.jsx
 * @Description: '朋友圈预览'
 */

import React, { useEffect, useState } from "react";
import { Modal } from "antd";
import "./conversationModal.less";
import Converastion from "./converastion";

const ConversationModal = (props) => {
  const { visible = false, listData = [], onCancel } = props.params;
  const [resetListData, setResetListData] = useState([]);
  useEffect(() => {
    console.log(`[listData]: `, listData);
    if (listData.length) {
      setResetListData(
        listData.map((item, index) => {
          item.videoUrl = item.fileId?.[0] || "";
          item.imageUrl = item.fileId?.[0] || "";
          return { ...item };
        }),
      );
    }
  }, [listData]);

  return (
    <Modal
      className="conversation-Modal-Container"
      open={visible}
      width={300}
      destroyOnClose
      closable={false}
      footer={null}
      bodyStyle={{ padding: "0" }}
      onCancel={() => {
        onCancel?.();
      }}
    >
      <Converastion params={{ listData: resetListData }} />
    </Modal>
  );
};

export default ConversationModal;
