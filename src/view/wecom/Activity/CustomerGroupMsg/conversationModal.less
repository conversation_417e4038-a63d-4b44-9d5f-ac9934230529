.conversation-Modal-Container {
  width: 300px;
  height: 500px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0px 0px 10px #999;
  margin: 0 auto;

  .ant-modal-content {
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    background: #3364d6;
    position: relative;
    color: #fff;

    //     .name {
    //       color: #fff;
    //       font-size: 14px;
    //       position: absolute;
    //       bottom: 0;
    //       right: 70px;
    //     }

    //     .ant-avatar {
    //       position: absolute;
    //       bottom: -25px;
    //       right: 10px;
    //       border-radius: 4px;
    //     }
  }

  .close {
    color: #fff;
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 18px;
    cursor: pointer;
  }

  .content {
    padding: 0 10px 0 16px;
    margin-top: 25px;
    display: flex;
    max-height: 328px;

    .right {
      margin: -2px 0 0 8px;

      .name {
        color: #295cd0;
        font-size: 14px;
      }

      .template {
        p {
          margin: 0 0 4px 0;
          white-space: pre-line;
        }

        .ant-image {
          margin: 0 6px 6px 0;

          img {
            object-fit: cover;
          }
        }

        .link-card {
          .ant-image {
            margin: unset;
          }
        }
      }
    }
  }
}
