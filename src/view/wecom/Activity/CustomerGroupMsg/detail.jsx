import { InfoCircleOutlined, LinkOutlined } from "@ant-design/icons"
import {
  Button,
  Card,
  Descriptions,
  Form,
  message,
  Space,
  Table,
  Tabs,
  Tag,
  Tooltip,
} from "antd"
import FormItem from "antd/es/form/FormItem"
import { qs2obj } from "common/object"
import { apiCall } from "common/utils"
import FilterBar from "components/FilterBar/FilterBar"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { useDict } from "../../../../hooks/useDict"
import {
  postSyncCustomer,
  queryCustomerGroupMsgCustomerTable,
  queryCustomerGroupMsgDetail,
} from "./api"

const CustomerGroupMsgDetail = (props) => {
  const [form] = Form.useForm()
  const CUSTOMER_GROUP_MSG_STATE = useDict("CUSTOMER_GROUP_MSG_STATE")
  const CUSTOMER_GROUP_MSG_PUBLISH_STATE = useDict(
    "CUSTOMER_GROUP_MSG_PUBLISH_STATE"
  )
  const CUSTOMER_GROUP_MSG_SUB_STATE = useDict("CUSTOMER_GROUP_MSG_SUB_STATE")

  const [id, setId] = useState(null)
  const [dataSource, setDataSource] = useState([])
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 })
  const [detail, setDetail] = useState({
    id: 9,
    createTime: "2024-12-14 15:52:46",
    updateTime: "2024-12-14 15:51:15",
    name: "11球球",
    state: "IN_PROGRESS",
    subState: null,
    description: "22",
    scope: {
      type: "STANDARD_FILTER",
      depEmployeeIdList: [],
      employeeIdList: [],
      minAddTime: null,
      maxAddTime: null,
      groupIdList: [],
      gender: null,
      tagIdList: [],
      tagDTOList: [],
      authFlag: null,
      importCustomerListFileId: null,
      customerNotInIdList: [2000203],
    },
    copyWriteContent: null,
    content: [
      {
        title: "工行理财产品——您的稳健理财之选",
        fileId: [
          "https://test.wizone.work/std/wibot/minio/imageproxy/0x0/wecom-marketing-test/338/a6d12082-bb01-425a-b502-1d97f5161098.png",
        ],
        description:
          "寻找一款安全可靠、收益可观的理财产品？工行理财产品将是您的理想选择！我们致力于为您提供优质的理财服务，助您实现财富增值。",
        url: "https://test.wizone.work/std/wibot/m/customer/wecom/wecomRedirect.html?companyId=c98f0433b9046e62939e40db688311fb&employeeShareId=4637d2ee1d9cbc281e9cccbba80d4261&employeeId=4637d2ee1d9cbc281e9cccbba80d4261&id=e54acdf26c1bccd04bd950ca521a8099&refKey=b92d8da658ec430894da8fe7e1275c0f&type=resource",
        content: null,
        type: "Article",
        appId: null,
        defaultFlag: false,
        id: 3981,
        showProduct: false,
        productId: null,
      },
    ],
    startTime: "2024-12-03 15:52:31",
    endTime: "2024-12-28 15:52:33",
    executeTime: "2024-12-14 15:53:12",
    createEmployeeId: 8256,
    departmentId: 991,
    createEmployeeName: "关现",
    updateEmployeeId: null,
  })
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      initDetail(id)
    }
  }, [])

  const handleQuery = () => {
    initTable()
  }
  const handleReset = () => {
    form.resetFields()
    initTable()
  }
  const handleSync = () => {
    postSyncCustomer({ customerGroupMsgId: Number(id) }).then((res) => {
      console.log(`[res]: `, res)
      message.success("同步成功")
    })
  }
  const onChangeTable = (pagination, filters, sorter) => {
    console.log(`[pagination]: `, pagination)
    setPagination({ ...pagination })
    initTable(pagination)
  }

  const onChange = (type) => {
    if (type === "info") {
      initDetail(id)
    } else if (type === "table") {
      initTable()
    }
  }
  const initDetail = (id) => {
    setLoading(true)
    queryCustomerGroupMsgDetail(id).then((res) => {
      console.log(`[res]: `, res)
      setDetail(res)
    })
  }

  const initTable = (page = pagination) => {
    setLoading(true)
    form.validateFields().then((formData) => {
      console.log(`[formData]: `, formData)
      queryCustomerGroupMsgCustomerTable({
        customerGroupMsgId: id,
        current: page.current,
        size: page.pageSize,
        ...formData,
        depEmployeeIdList: formData.depEmployeeIdList?.join(","),
      })
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPagination({
            current,
            total,
            pageSize: size,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const [isModalOpen, setIsModalOpen] = useState(false)

  const fileName = moment().format("YYYY-MM-DD") + ".xlsx"

  const handleDownload = () => {
    handleDownloadCustomerList()
  }
  const handleDownloadCustomerList = () => {
    const data = {
      fileId: detail.scope.importCustomerListFileId,
    }
    apiCall("/customerGroupMsg/downloadCustomerList", "GET", data, null, {
      isExit: true,
      title: fileName,
    })
      .then((res) => {
        message.success("下载成功！")
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }
  const GENDER = {
    "": "全部",
    null: "全部",
    MAN: "男",
    WOMAN: "女",
    UNKNOWN: "未知",
  }

  const standardFilter = (
    <>
      {detail.scope.depEmployeeIdList?.length ? (
        <Descriptions.Item label="客户负责人">
          <ETypeTransferModal
            readonly
            btnShowFlag={false}
            value={detail.scope.depEmployeeIdList}
          />
        </Descriptions.Item>
      ) : null}

      {detail.scope.tagNameList?.length ? (
        <Descriptions.Item label="客户标签">
          {detail.scope.tagNameList?.map((item) => <Tag>{item}</Tag>) ??
            "-"}
        </Descriptions.Item>
      ) : null}

      {detail.scope.authFlag ? (
        <Descriptions.Item label="识别状态">
          {detail.scope.authFlag ? "已识别" : "未识别"}
        </Descriptions.Item>
      ) : null}

      {detail.scope.minAddTime && detail.scope.maxAddTime ? (
        <Descriptions.Item label="添加时间">
          {detail.scope.minAddTime} {detail.scope.minAddTime ? "~" : ""}{" "}
          {detail.scope.maxAddTime}
        </Descriptions.Item>
      ) : null}

      {detail.scope?.groupNameList?.length ? (
        <Descriptions.Item label="群组">
          {detail.scope?.groupNameList?.map((item) => (
            <Tag>{item}</Tag>
          ))}
        </Descriptions.Item>
      ) : null}

      <Descriptions.Item label="性别">
        {GENDER[detail.scope.gender]}
      </Descriptions.Item>
    </>
  )

  const importTable = (
    <>
      <Descriptions.Item label="客户清单">
        <div style={{ alignItems: "unset", display: "inline-flex" }}>
          <span style={{ marginRight: 12 }}>
            <LinkOutlined />
            {fileName}
          </span>
          <Button type="primary" onClick={handleDownload}>
            下载清单
          </Button>
        </div>
      </Descriptions.Item>
    </>
  )
  const detailComponent = (
    <>
      <Descriptions column={1}>
        <Descriptions.Item label="任务名称">{detail.name}</Descriptions.Item>
        <Descriptions.Item label="任务说明">
          <div style={{ whiteSpace: "pre-wrap" }}>{detail.description}</div>
        </Descriptions.Item>
        <Descriptions.Item label="发送范围">
          <Descriptions column={1} style={{ background: "#eee", padding: 24 }}>
            {detail.scope.type === "STANDARD_FILTER"
              ? standardFilter
              : importTable}
          </Descriptions>
        </Descriptions.Item>
        <Descriptions.Item label="可发送时间">
          {detail.startTime} ~ {detail.endTime}
        </Descriptions.Item>
        <Descriptions.Item label="任务发布时间">
          {detail.executeTime}
        </Descriptions.Item>
        <Descriptions.Item label="创建人">
          {detail.createEmployeeName}
        </Descriptions.Item>
        <Descriptions.Item label="群发内容" style={{ alignItems: "unset" }}>
          <div
            style={{
              display: "inline-flex",
              flexWrap: "wrap",
              alignItems: "unset",
            }}
          >
            <span style={{ marginRight: 12, whiteSpace: "pre-wrap" }}>
              {detail.copyWriteContent}
            </span>
            <a
              type="link"
              onClick={() => {
                setResourcePreviewParams({
                  visible: true,
                  listData: [
                    {
                      type: "copyWriter",
                      content: detail.copyWriteContent,
                    },
                    ...detail.content,
                  ],
                  type: "session",
                  onCancel: () => {
                    setResourcePreviewParams({
                      visible: false,
                    })
                  },
                })
              }}
            >
              预览
            </a>
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="状态">
          {CUSTOMER_GROUP_MSG_STATE.get(detail.state)}
          {CUSTOMER_GROUP_MSG_SUB_STATE.get(detail.subState)
            ? `(${CUSTOMER_GROUP_MSG_SUB_STATE.get(detail.subState)})`
            : ""}
        </Descriptions.Item>
        {detail.state !== "AUDITING" ? (
          <>
            <Descriptions.Item label="审核人">
              {detail.auditEmployeeName}
            </Descriptions.Item>
            <Descriptions.Item label="审核意见">
              {detail.auditOpinion}
            </Descriptions.Item>
          </>
        ) : null}
      </Descriptions>
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </>
  )

  const columns = [
    {
      title: "序号",
      dataIndex: "id",
      key: "id",
      width: 80,
      render: (text, record, index) => index + 1,
    },
    {
      title: "员工名称",
      dataIndex: "employeeName",
      width: 150,
    },
    {
      title: "发送状态",
      dataIndex: "state",
      render: (text, record, index) => (
        <div>{CUSTOMER_GROUP_MSG_PUBLISH_STATE.get(record.state)}</div>
      ),
      width: 100,
    },
    {
      title: "预计送达",
      dataIndex: "expectedDeliveryCount",
      width: 100,
    },
    {
      title: "已送达",
      dataIndex: "actualDeliveryCount",
      width: 100,
    },
    {
      title: () => (
        <span>
          超上限
          <Tooltip title={"因客户已经收到其他群发消息导致发送失败"}>
            <InfoCircleOutlined
              style={{ fontSize: "14px", marginLeft: "6px", color: "#c1c1c1" }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: "exceedLimitCount",
      width: 100,
    },
    {
      title: () => (
        <span>
          非好友
          <Tooltip title={"因客户不是好友导致发送失败"}>
            <InfoCircleOutlined
              style={{ fontSize: "14px", marginLeft: "6px", color: "#c1c1c1" }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: "nonFriendCount",
      width: 100,
    },
  ]

  const employeeComponent = (
    <>
      <FilterBar>
        <Form layout={"inline"} form={form}>
          <FormItem name="state">
            <SysDictSelect
              placeholder="状态"
              dataset="CUSTOMER_GROUP_MSG_PUBLISH_STATE"
            />
          </FormItem>
          <FormItem
            name="depEmployeeIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="选择员工" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleSync()}>
              同步数据
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={pagination}
          onChange={onChangeTable}
        />
      </Card>
    </>
  )

  return (
    <>
      <Card bordered={false}>
        <Tabs
          tabBarExtraContent={
            <Space size={[0, 0]} style={{ flexDirection: "column" }}>
              <Button
                style={{ marginBottom: "8px" }}
                type="primary"
                onClick={() => props.history.goBack()}
              >
                返 回
              </Button>
            </Space>
          }
          defaultActiveKey="info"
          onChange={onChange}
          items={[
            {
              label: `任务详情`,
              key: "info",
              children: detailComponent,
            },
            {
              label: `员工统计`,
              key: "table",
              children: employeeComponent,
            },
          ]}
        />
      </Card>
    </>
  )
}

export default CustomerGroupMsgDetail
