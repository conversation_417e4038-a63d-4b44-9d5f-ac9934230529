import { PlusOutlined } from "@ant-design/icons"
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Descriptions,
  Form,
  Image,
  Input,
  message,
  Popover,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Tag,
  TreeSelect,
  Typography,
} from "antd"
import { qs2obj } from "common/object"
import { removeInputEmpty } from "common/regular"
import {
  recursionTagKeyTreeData,
  recursionTagKeyTreeDataDisabled,
  recursionTagKeyTreeDataTag,
} from "common/tree"
import { apiCall } from "common/utils"
import { FileHOC } from "components/FileHOC/FileHOC"
import LinkCard from "components/LinkCard/home"
import MaterialListForm from "components/MaterialListForm/home"
import CTypeTransferModal from "components/TransferModal/CustomerType/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import SendModal from "../comps/SendModal/home"
import UploadList from "../MarketingTask/comps/UploadList/home"
import {
  postCreateCustomerGroupMsg,
  postUpdateCustomerGroupMsg,
  queryCustomerGroupMsgCount,
  queryCustomerGroupMsgDetail,
} from "./api"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
import WibotEditorView from "components/WibotEditorView/home"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Paragraph } = Typography
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const { Option } = Select
const { Text } = Paragraph

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}
const EditCustomerGroupMsg = (props) => {
  const [formForm] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const onRefMaterialListForm = useRef()
  const onRefCustomerList = useRef()
  const onRefCircleMaterialListForm = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [selectType, setSelectType] = useState("CUSTOMER")
  const [sendObjectType, setSendObjectType] = useState("STANDARD_FILTER")
  const [targetCount, setTargetCount] = useState(0)
  const [staffSelected, setStaffSelected] = useState([])
  const [depEmployeeIdList, setDepEmployeeIdList] = useState([])
  const [groupSelected, setGroupSelected] = useState([])
  const [groupLeaderSelected, setGroupLeaderSelected] = useState([])
  const [msgList, setMsgList] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [operationType, setOperationType] = useState(null)
  const [sendParams, setSendParams] = useState({ visible: false })
  const [surveyFlag, setSurveyFlag] = useState(null)
  const [circleMsgList, setCircleMsgList] = useState([])
  const [query, setQuery] = useState({ type: "add" })
  const [detail, setDetail] = useState({ state: null })
  const [isLoading, setIsLoading] = useState(false)

  const scrollListData = [
    {
      title: "客户群发",
      describe: "将多条消息通过员工群发给客户，每个客户每天可接收1组群发消息。",
      type: "CUSTOMER",
    },
    {
      title: "客户群群发",
      describe:
        "将多条消息通过群主群发给客户群，每个群组每天可接收1组群发消息。",
      type: "CHAT",
    },
    {
      title: "朋友圈群发",
      describe: "每位客户的朋友圈每天最多可展示3条同一成员发布的朋友圈内容。",
      type: "MOMENT",
    },
  ]
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  })
  const [noLeaderIdList, setNoLeaderIdList] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [existTagNameList, setExistTagNameList] = useState([])

  useEffect(async () => {
    const { id, type } = qs2obj(props.location.search)
    await setQuery(qs2obj(props.location.search))
    // getGroupNoLeaderIdList();
    await getCustomerTagTreeData()
    if (id) {
      await setId(id)
      await setOperationType(type)
    } else {
      onRefMaterialListForm.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
    await init(id, type)
  }, [])

  const init = async (id = null, type) => {
    if (id) {
      const params = {
        id,
        type,
      }
      await fetchList(params)
    }
    // await getTargetCount();
    if (type === "detail") {
      let timer = setTimeout(() => {
        WibotEditorRef.current?.disable()
        clearTimeout(timer)
      }, 300)
    }
  }

  // 查询所有不是群主id
  const getGroupNoLeaderIdList = async () => {
    setLoading(true)
    apiCall("/group/noLeaderIdList", "GET")
      .then((res) => {
        setNoLeaderIdList(res.map((item) => String(item)))
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    const data = {
      id,
    }
    await queryCustomerGroupMsgDetail(id)
      .then((res) => {
        const { messages, scope, targetFilter, subType } = res
        console.log(`[res]: `, res)
        setExistTagNameList(scope?.tagNameList)
        setDetail(res)
        // const {
        //     gender,
        //     startTime,
        //     endTime,
        //     groupIds,
        //     tagIds,
        //     authFlag,
        //     employeeList,
        //     groupList,
        //     groupLeaderList,
        //     depEmployeeIdList,
        //     groupLeaderIds,
        //     filterSurveySent,
        //     filterSurveyVisit,
        //     importCustomerList,
        //     importCustomerListFileName,
        //     importCustomerListFileId,
        // } = targetFilter;
        // const sendType = scope === "ALL" ? 1 : scope === "PART" ? 2 : 3;
        // const data = {
        //     "name": name,
        //     "description": description,
        //     "scope": {
        //         "type": type,
        //         "depEmployeeIdList": depEmployeeIdList,
        //         "minAddTime": minAddTime,
        //         "maxAddTime": maxAddTime,
        //         "groupIdList": groupIdList,
        //         // "gender": gender,
        //         "tagIdList": tagIdList,
        //         "authFlag": authFlag,
        //         // "importCustomerListFileId": onRefCustomerList.current.getCustomerList()
        //     },
        //     "copyWriteContent": formForm.getFieldValue('copyWriteContent'),
        //     "content": modifyMsgList.filter((item) => item.type !== "copyWriter"),
        //     "startTime": startTime,
        //     "endTime": endTime
        // }

        const addTime =
          res.scope.minAddTime && res.scope.maxAddTime
            ? [moment(res.scope.minAddTime), moment(res.scope.maxAddTime)]
            : null
        const planTime =
          res.startTime && res.endTime
            ? [moment(res.startTime), moment(res.endTime)]
            : null
        setSendObjectType(res.scope.type)
        onRefCustomerList?.current?.getInitCustomerFile({
          name: moment().format("YYYY-MM-DD") + ".xlsx",
          file: res.scope.importCustomerListFileId,
        })
        formForm.setFieldsValue({
          ...res,
          ...scope,
          scope: {
            ...scope,
          },
          addTime: addTime,
          planTime: planTime,
          // filterSurveySent,
          // filterSurveyVisit,
          // planTime: res.startTime ? [moment(res.startTime), moment(res.endTime)] : null,
          // massTimeType: 2,
          // sendObjectType: sendType,
          // gender: gender ?? "ALL",
          // addTime: startTime ? [moment(startTime), moment(endTime)] : null,
          // groupIds: groupIds ?? [],
          // tagIds: tagIds ?? [],
          // authFlag,
          // depEmployeeIdList: depEmployeeIdList ?? [],
          // groupLeaderIds: groupLeaderIds?.map((item) => String(item)) ?? [],
        })
        setTimeout(() => {
          const data = [
            {
              type: "copyWriter",
              content: res.copyWriteContent || "",
              defaultFlag: true,
            },
            ...res.content,
          ].flat(Infinity)
          onRefMaterialListForm?.current?.getInitMsgList(data)
        }, 100)
        // const { type } = qs2obj(props.location.search);
        // if (type === 'copy') {
        //     setId(null)
        // }

        // // 是否问卷
        // setSurveyFlag(subType);
        // // 所选员工
        // setStaffSelected(employeeList);
        // // 所选群组
        // const arr = groupList?.map((item) => ({
        //     ...item, key: item.id,
        // })) ?? [];
        // setGroupSelected(JSON.parse(JSON.stringify(arr)));
        // // 所选群主
        // setGroupLeaderSelected(groupLeaderList);
        // if (type === "MOMENT") {
        //     let timer = setTimeout(() => {
        //         onRefCircleMaterialListForm?.current?.getInitMsgList(messages);
        //         clearTimeout(timer);
        //     }, 100);
        // }
        // setSelectType(type);
        // setSendObjectType(sendType);
        // // 客户清单
        // if (selectType === "CUSTOMER" && scope === "PART_LIST") {
        //     // onRefCustomerList?.current?.getInitCustomerList(importCustomerList);
        //     onRefCustomerList?.current?.getInitCustomerFile({
        //         name: importCustomerListFileName, file: importCustomerListFileId,
        //     });
        // }
        // // 资源中心组件-群发内容
        // if (selectType !== "MOMENT") {
        //     onRefMaterialListForm?.current?.getInitMsgList(messages);
        // }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 切换群发类型
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setSelectType(item.type)
    setMsgList([])
    formForm.resetFields()
    setGroupSelected([])
    setStaffSelected([])
    setGroupLeaderSelected([])
    // getTargetCount(item.type);
    setSendObjectType(1)
    formForm.setFieldsValue({
      sendObjectType: 1,
    })

    if (item.type === "CUSTOMER" || item.type === "CHAT") {
      let timer = setTimeout(() => {
        onRefMaterialListForm?.current?.getInitMsgList([
          {
            type: "copyWriter",
            content: "",
            defaultFlag: true,
          },
        ])
        clearTimeout(timer)
      }, 100)
    }

    if (selectType !== "MOMENT") {
      onRefMaterialListForm.current.getInitMsgList([])
    }
  }

  // 切换发送对象类型
  const onChangeRadioSendObjectType = (e) => {
    const { value } = e.target
    setSendObjectType(value)
    if (value !== 3) {
      // getTargetCount();
    }
  }

  // 获取任务对象数量
  const getTargetCount = async (itemType = null) => {
    setLoading(true)
    const type = formForm?.getFieldValue("sendObjectType")
    const paramsData = {
      targetType: (itemType || selectType) === "CHAT" ? "CHAT" : "CUSTOMER",
      taskBatchType: (itemType || selectType) === "MOMENT" ? "MOMENT" : null,
    }

    if (type === 1) {
      await apiCall("/employeeTaskBatch/getTargetCount", "GET", paramsData)
        .then((res) => {
          if (res === 0) {
            message.error("没有满足条件的客户，请重新选择")
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "authFlag",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender === "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        gender: newGender ?? null,
        ...paramsData,
      }

      await apiCall("/employeeTaskBatch/getTargetCount", "GET", data)
        .then((res) => {
          if (res === 0) {
            message.error(
              `负责人${
                staffSelected?.map((item) => item.name)?.join("、") ||
                groupLeaderSelected?.map((item) => item.name)?.join("、")
              }名下无满足条件的客户，请重新选择`
            )
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  // 客户列表
  const handleSendCustomer = () => {
    setLoading(true)
    const sendObjectType = formForm?.getFieldValue("sendObjectType")
    const filterSurveyVisit = formForm.getFieldValue("filterSurveyVisit")
    const filterSurveySent = formForm.getFieldValue("filterSurveySent")
    const paramsData = {
      targetType: selectType === "CHAT" ? "CHAT" : "CUSTOMER",
      filterSurveySent,
      filterSurveyVisit,
    }

    if (sendObjectType === 1) {
      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", paramsData)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            type: selectType,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "authFlag",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender === "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        gender: newGender ?? null,
        ...paramsData,
      }

      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", data)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            type: selectType,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  const onSubmit = () => {
    const values = formForm.getFieldsValue(true)
    console.log(`[values]: `, values)
    formForm.validateFields().then((formData) => {
      if (formData.msgList.length === 1 && formData.msgList[0] === "") {
        message.error("请填写文案！")
        return false
      }
      console.log(`[formData]: `, formData)
      console.log(
        `[onRefMaterialListForm.current]: `,
        onRefMaterialListForm.current
      )
      const modifyMsgList = onRefMaterialListForm.current?.getModifyMsgList()
      console.log(`[modifyMsgList]: `, modifyMsgList)
      // const materialList =
      //   selectType !== "MOMENT"
      //     ? onRefMaterialListForm.current.getModifyMsgList()
      //     : circleMsgList;
      //
      // if (selectType !== "MOMENT" && !materialList.length > 0) {
      //   message.error("群发内容不能为空！");
      //   return false;
      // }
      // if (selectType !== "MOMENT" && onRefMaterialListForm.current.getCopyWriterValidate()) {
      //     // 校验资源文案字数
      //     return;
      // }

      if (formData.planTime) {
        formData.startTime = moment(formData.planTime[0]._d).format(
          "YYYY-MM-DD HH:mm:ss"
        )
        formData.endTime = moment(formData.planTime[1]._d).format(
          "YYYY-MM-DD HH:mm:ss"
        )
        delete formData.planTime
      }
      if (formData.addTime) {
        formData.minAddTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxAddTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
      }

      const {
        name,
        description,
        startPlanTime,
        endPlanTime,
        sendObjectType,
        type,
        startTime = null,
        endTime = null,
        gender = null,
        addTime = [null, null],
        tagIds,
        authFlag,
        depEmployeeIdList,
        groupLeaderIds,
        allowSelect,
        filterSurveySent,
        filterSurveyVisit,
        remark,
        file,
        groupIdList,
        tagIdList,
        minAddTime,
        maxAddTime,
        tagNameList,
      } = formData

      setLoading(true)
      // let importCustomerList = [];
      // if (selectType === 'CUSTOMER' && sendObjectType === 3) {
      //   importCustomerList = onRefCustomerList.current.getCustomerList();
      // }
      console.log(`[onRefCustomerList]: `, onRefCustomerList.current)
      const copyWriter = modifyMsgList.filter(
        (item) => item.type === "copyWriter"
      )[0]
      console.log(`[copyWriter]: `, copyWriter)
      console.log(`[groupIdList]: `, groupIdList)
      console.log(`[groupSelected]: `, groupSelected)
      const data = {
        name: name,
        description: description,
        allowSelect,
        scope: {
          type: type,
          depEmployeeIdList: depEmployeeIdList,
          minAddTime: minAddTime,
          maxAddTime: maxAddTime,
          groupIdList: groupSelected.map((item) => item.id),
          gender: gender || null,
          tagNameList: tagNameList,
          authFlag: authFlag,
          importCustomerListFileId: file,
        },
        copyWriteContent: modifyMsgList.filter(
          (item) => item.type === "copyWriter"
        )[0].content,
        content: modifyMsgList.filter((item) => item.type !== "copyWriter"),
        startTime: startTime,
        endTime: endTime,
      }

      if (query.type === "copy") {
      }
      if (query.type !== "copy" && id) {
        postUpdateCustomerGroupMsg(id, data)
          .then(() => {
            message.success("修改成功！")
            // clearCache(); // 清空路由缓存
            props.history.go(-1)
          })
          .finally(() => {
            setLoading(false)
          })
      } else {
        postCreateCustomerGroupMsg(data)
          .then((res) => {
            message.success("新增成功！")
            // clearCache(); // 清空路由缓存
            props.history.go(-1)
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setLoading(false)
          })
      }
      // if (id) {
      //   postUpdateCustomerGroupMsg(id, data)
      //     .then(() => {
      //       message.success("修改成功！");
      //       clearCache(); // 清空路由缓存
      //       props.history.go(-1);
      //     })
      //     .finally(() => {
      //       setLoading(false);
      //     });
      // } else {
      //   postCreateCustomerGroupMsg(data)
      //     .then((res) => {
      //       message.success("新增成功！");
      //       clearCache(); // 清空路由缓存
      //       props.history.go(-1);
      //     })
      //     .catch((err) => {
      //       console.log(err);
      //     })
      //     .finally(() => {
      //       setLoading(false);
      //     });
      // }
    })
  }
  const [count, setCount] = useState(0)
  const onValuesChange = () => {
    const formData = formForm.getFieldsValue(true)
    if (formData.type !== "STANDARD_FILTER") {
      return
    }
    const {
      type,
      authFlag,
      depEmployeeIdList,
      groupLeaderIds,
      allowSelect,
      filterSurveySent,
      filterSurveyVisit,
      remark,
      file,
      gender,
      groupIdList,
      tagIdList,
      minAddTime,
      maxAddTime,
      addTime,
      tagNameList,
    } = formData
    const data = {
      type: type,
      depEmployeeIdList: depEmployeeIdList,
      minAddTime:
        addTime &&
        addTime[0] &&
        moment(addTime[0]._d).format("YYYY-MM-DD HH:mm:ss"),
      maxAddTime:
        addTime &&
        addTime[1] &&
        moment(addTime[1]._d).format("YYYY-MM-DD HH:mm:ss"),
      groupIdList: groupIdList,
      gender: gender || null,
      tagIdList: tagIdList,
      authFlag: authFlag,
      tagNameList,
      // importCustomerListFileId: onRefCustomerList.current?.getCustomerList(),
    }
    setIsLoading(true)
    queryCustomerGroupMsgCount(data)
      .then((res) => {
        setCount(res)
      })
      .finally(() => {
        setTimeout(() => {
          setIsLoading(false)
        }, 500)
      })
  }

  const handleGoBack = () => {
    // 清空选择素材弹窗缓存
    localStorage.removeItem("materialModalCascader")
    localStorage.removeItem("materialModalTabType")
    props.history.go(-1)
  }

  return (
    <div>
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={query.type === "add" ? "新建群发客户" : "编辑群发客户"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />

        {detail.state !== null && detail.state !== "AUDITING" ? (
          <Row style={{ borderBottom: "1px solid #ccc" }}>
            <Col xs={24} lg={24}>
              <Card
                bordered={false}
                style={{ height: "100%" }}
                title="上次审核结果"
              >
                <Descriptions column={1}>
                  <Descriptions.Item label="审核人">
                    {detail.auditEmployeeName} ({detail.auditTime})
                  </Descriptions.Item>
                  <Descriptions.Item label="审核意见">
                    <span style={{ color: "red" }}>{detail.auditOpinion}</span>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>
        ) : null}

        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <h2 className="card-title">基础信息</h2>
              <Form {...layout} form={formForm}>
                <FormItem
                  label="任务名称"
                  name="name"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入任务名称" }]}
                >
                  <Input
                    placeholder="请输入任务名称"
                    allowClear
                    showCount
                    maxLength={30}
                    disabled={operationType === "detail"}
                  />
                </FormItem>

                <FormItem
                  style={{ marginTop: "24px" }}
                  name="description"
                  label="任务说明"
                  rules={[{ required: true, message: "请输入任务说明" }]}
                >
                  <TextArea
                    showCount
                    maxLength={200}
                    placeholder="请输入任务说明（200字内）"
                    disabled={operationType === "detail"}
                    allowClear
                    autoSize={{ minRows: 5, maxRows: 5 }}
                    className="textArea-mid"
                  />
                </FormItem>
                <FormItem
                  label="可发送时间"
                  name="planTime"
                  rules={[{ required: true, message: "请选择可发送时间" }]}
                >
                  <RangePicker
                    showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
                    format="YYYY-MM-DD HH:mm"
                    disabled={operationType === "detail"}
                  />
                </FormItem>
                <FormItem
                  name="type"
                  label="发送范围"
                  initialValue={sendObjectType}
                  rules={[{ required: true, message: "请选择发送对象" }]}
                >
                  <Radio.Group
                    onChange={onChangeRadioSendObjectType}
                    disabled={operationType === "detail"}
                  >
                    <Radio value="STANDARD_FILTER">标准筛选 </Radio>
                    <Radio value="TABLE_IMPORT">
                      <span style={{ marginRight: 12 }}>表格导入</span>
                      <Popover
                        title="群发说明"
                        content={
                          <>
                            <p>
                              1.
                              群发部分客户筛选的员工就是客户负责人，群发客户消息任务由客户负责人执行；
                            </p>
                            <p>
                              2.
                              群发客户任务调企微企业群发接口，群发的客户由后台限制，员工不可手动更改；
                            </p>
                            <p>
                              3.
                              群发部分客户的筛选条件含负责人员工时，按所选要求筛选客户，客户的负责人必须是所选员工（客户的负责人不是所选员工时自动过滤掉），如筛选出的客户数等于0时不给所选员工推送任务，且员工不纳入任务统计；
                            </p>
                            <p>
                              4.群发部分客户的筛选条件不含负责人员工时，筛选出的客户按照客户的负责人员工去推送任务。
                            </p>
                          </>
                        }
                      >
                        <a>群发说明</a>
                      </Popover>
                    </Radio>
                  </Radio.Group>
                </FormItem>

                {sendObjectType === "STANDARD_FILTER" && (
                  <Row>
                    <Col
                      offset={4}
                      span={20}
                      style={{ background: "#eee", paddingTop: 24 }}
                    >
                      <FormItem
                        name="depEmployeeIdList"
                        label="客户负责人"
                        shouldUpdate={(prevValues, curValues) =>
                          prevValues.type !== curValues.type
                        }
                      >
                        <ETypeTransferModal
                          title="选择员工"
                          readonly={operationType === "detail"}
                          onChange={(value, options) => {
                            setStaffSelected(options)
                            setDepEmployeeIdList(options)
                            // onValuesChange();
                          }}
                        />
                      </FormItem>
                      {/* <FormItem name="tagIdList" label="客户标签"> */}
                      {/*   <TreeSelect */}
                      {/*     treeData={labelTreeData} */}
                      {/*     treeCheckable */}
                      {/*     allowClear */}
                      {/*     showArrow */}
                      {/*     showSearch */}
                      {/*     treeNodeFilterProp="title" */}
                      {/*     maxTagCount="responsive" */}
                      {/*     showCheckedStrategy={SHOW_PARENT} */}
                      {/*     placeholder="客户标签" */}
                      {/*   /> */}
                      {/* </FormItem> */}
                      <CustomTagSelect
                        label="客户标签"
                        name="tagNameList"
                        placeholder="客户标签"
                        useForm={formForm}
                        existTagNameList={existTagNameList}
                        labelTreeData={labelTreeData}
                      />
                      <FormItem name="authFlag" label="识别状态">
                        <Select placeholder="识别状态" allowClear>
                          <Option value={true}>已识别</Option>
                          <Option value={false}>未识别</Option>
                        </Select>
                      </FormItem>
                      <FormItem name="addTime" label="添加时间">
                        <RangePicker disabled={operationType === "detail"} />
                      </FormItem>

                      <FormItem
                        name="groupIdList"
                        label="群组"
                        shouldUpdate={(prevValues, curValues) =>
                          prevValues.type !== curValues.type
                        }
                        extra={
                          groupSelected.length > 0 ? (
                            <div>
                              {groupSelected.map((item, index) => (
                                <Tag
                                  closable
                                  style={{ marginTop: "8px" }}
                                  key={index}
                                  onClose={(e) => {
                                    e.preventDefault()
                                    setGroupSelected(
                                      groupSelected.filter(
                                        (i) => i.key !== item.key
                                      )
                                    )
                                  }}
                                >
                                  {item.name}
                                </Tag>
                              ))}
                            </div>
                          ) : null
                        }
                      >
                        <Button
                          icon={<PlusOutlined />}
                          type="primary"
                          disabled={operationType === "detail"}
                          onClick={() => {
                            setCTypeTransferParams({
                              visible: true,
                              type: "group",
                              checkList: groupSelected,
                              onSubmit: (data) => {
                                setGroupSelected(data)
                                formForm.setFieldsValue({
                                  groupIds: data.map((item) => item.id),
                                })
                                setCTypeTransferParams({ visible: false })
                                // onValuesChange();
                              },
                              onCancel: () => {
                                setCTypeTransferParams({ visible: false })
                              },
                            })
                          }}
                        >
                          选择群组
                        </Button>
                      </FormItem>

                      <FormItem
                        name="gender"
                        label="选择性别"
                        initialValue={null}
                      >
                        <Radio.Group disabled={operationType === "detail"}>
                          <Radio value={null}>全部</Radio>
                          <Radio value={"MAN"}>仅男性</Radio>
                          <Radio value={"WOMAN"}>仅女性</Radio>
                          <Radio value={"UNKNOWN"}>未知</Radio>
                        </Radio.Group>
                      </FormItem>

                      <FormItem
                        labelCol={{ span: 4, offset: 1 }}
                        name="count"
                        label="预计送达客户数"
                      >
                        {count}{" "}
                        <Button
                          loading={isLoading}
                          onClick={() => onValuesChange()}
                        >
                          刷新数量
                        </Button>
                      </FormItem>
                    </Col>
                  </Row>
                )}

                {/* 上传客户清单 */}

                {sendObjectType === "TABLE_IMPORT" && (
                  <Row>
                    <Col
                      offset={4}
                      span={20}
                      style={{
                        background: "#eee",
                        paddingTop: 24,
                        paddingBottom: 24,
                      }}
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}
                    >
                      <UploadList
                        ref={onRefCustomerList}
                        params={{
                          uploadUrl: "/customerGroupMsg/uploadCustomerList",
                          downloadUrl: "/customerGroupMsg/downloadCustomerList",
                          formForm,
                          operationType,
                        }}
                      />
                    </Col>
                  </Row>
                )}

                <FormItem
                  label="群发调整"
                  name="allowSelect"
                  extra={"开启后允许员工手动调整发送范围"}
                >
                  <Switch defaultChecked={false} />
                </FormItem>
                <FormItem
                  label="群发内容"
                  colon={false}
                  wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                  required
                >
                  <MaterialListForm
                    params={{
                      formRef: formForm,
                      menuList: ["image", "material"],
                      isNickname: false,
                      needScriptFlag: true,
                      materialAmount: msgList.length,
                      isDisabled: operationType == "detail",
                      isShowProduct: true,
                      materialTabList: [
                        "Article",
                        "pageArticle",
                        "Video",
                        "copyWriter",
                        "MINI_PROGRAM",
                        "Picture",
                        "Poster",
                        "FORM",
                        "Product",
                        "ResourceSet",
                      ],
                      extra: (
                        <div>
                          请按发送顺序添加群发内容，每个客户每天可接收1组群发消息，1组最多9条消息，还可添加{" "}
                          {10 - msgList.length} 条
                        </div>
                      ),
                      changeLoading: (flag) => {
                        setLoading(flag)
                      },
                    }}
                    // 监听回调
                    callback={(params) => {
                      console.log(`[params]: `, params)
                      setMsgList(params.data)
                    }}
                    ref={onRefMaterialListForm}
                  />
                </FormItem>

                {/*<FormItem extra={"审核通过后将自动下发群发任务"}>*/}
                {/*    <Button*/}
                {/*        disabled={operationType === "detail"}*/}
                {/*        type="primary"*/}
                {/*        onClick={() => onSubmit()}*/}
                {/*    >*/}
                {/*        确认*/}
                {/*    </Button>*/}
                {/*</FormItem>*/}

                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    <Button type="primary" onClick={() => onSubmit()}>
                      确认
                    </Button>
                  </Space>
                </div>
                <div
                  type="secondary"
                  style={{
                    display: "block",
                    textAlign: "center",
                    color: "rgba(0, 0, 0, 0.45)",
                  }}
                >
                  审核通过后将自动下发群发任务
                </div>
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              {selectType === "MOMENT" ? (
                <Card bordered={false} style={{ height: "100%" }}>
                  <h2 className="card-title">界面预览</h2>
                  <div className="pre-wrap">
                    <div className="phone-box">
                      <div className="circle-box">
                        {circleMsgList.map((item, index) => (
                          <span key={index} className="template">
                            {item.type === "copyWriter" && (
                              <WibotEditorView html={item.content} />
                            )}
                            {(item.type === "Picture" ||
                              item.type === "Poster") && (
                              <FileHOC src={item.fileUrl}>
                                {(url) => (
                                  <Image
                                    key={index}
                                    className="fileImg"
                                    src={url}
                                    width={60}
                                    height={60}
                                    preview={false}
                                  />
                                )}
                              </FileHOC>
                            )}
                            {item.type === "Video" && (
                              <FileHOC src={item.videoUrl}>
                                {(url) => (
                                  <video
                                    controls
                                    src={url}
                                    style={{ width: "100px", height: "100px" }}
                                  />
                                )}
                              </FileHOC>
                            )}
                            {(item.type === "Article" ||
                              item.type === "pageArticle" ||
                              item.type === "FORM" ||
                              item.type === "Product") && (
                              <LinkCard data={item} isLink={false} />
                            )}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              ) : (
                <WibotMaterialPreview type="session" listData={msgList} />
              )}
            </Col>
          </Row>
        </Card>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
      <CTypeTransferModal {...CTypeTransferParams} />
      <SendModal {...sendParams} />
    </div>
  )
}

export default EditCustomerGroupMsg
