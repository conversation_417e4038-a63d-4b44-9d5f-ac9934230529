import {
  But<PERSON>,
  Card,
  DatePicker,
  Form,
  Input,
  message,
  Table,
  Tooltip,
  Typography,
} from "antd"
import FormItem from "antd/es/form/FormItem"
import { usePageCacheLifeCycle } from "common/hooks"
import { removeInputEmpty } from "common/regular"
import FilterBar from "components/FilterBar/FilterBar"
import ListOperation from "components/ListOperation/home"
import OperateModal from "components/Modal/OperateModal"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import moment from "moment/moment"
import React, { useEffect, useRef, useState } from "react"
import { useDict } from "../../../../hooks/useDict"
import DisturbModal from "../MarketingTask/Mass/comps/DisturbModal"
import { postStopSync, queryCustomerGroupMsgList } from "./api"

const { Paragraph } = Typography
const { RangePicker } = DatePicker

function CustomerGroupMsg(props) {
  const state = useDict("CUSTOMER_GROUP_MSG_STATE")
  const subState = useDict("CUSTOMER_GROUP_MSG_SUB_STATE")
  const [loading, setLoading] = useState(false)
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "任务说明",
      width: "160px",
      dataIndex: "description",
      key: "name",
      ellipsis: true,
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          <p>{value}</p>
        </Tooltip>
      ),
    },
    {
      title: "群发内容",
      width: "160px",
      dataIndex: "copyWriteContent",
      key: "copyWriteContent",
      ellipsis: true,
      align: "center",
      render: (value, record, index) => {
        return (
          <>
            <Paragraph ellipsis style={{ margin: "0" }}>
              {record.copyWriteContent}
            </Paragraph>
            <a
              type="link"
              onClick={() => {
                setResourcePreviewParams({
                  visible: true,
                  listData: [
                    {
                      type: "copyWriter",
                      content: record.copyWriteContent,
                    },
                    ...record.content,
                  ],
                  type: "session",
                  onCancel: () => {
                    setResourcePreviewParams({
                      visible: false,
                    })
                  },
                })
              }}
            >
              预览
            </a>
          </>
        )
      },
    },
    {
      title: "可发送时间",
      width: "160px",
      dataIndex: "date",
      align: "center",
      render: (value, record, index) => (
        <div>
          <div>{record.startTime}</div>
          <div>~</div>
          <div>{record.endTime}</div>
        </div>
      ),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "state",
      align: "center",
      render: (value, record, index) => {
        return (
          <div>
            <div>{state.get(record.state)}</div>
            <div>
              {record.subState ? `(${subState.get(record.subState)})` : ""}
            </div>
          </div>
        )
      },
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      align: "center",
      render: (value, record, index) => (
        <div>
          <div>{record.createEmployeeName}</div>
          <div>{record.createTime}</div>
        </div>
      ),
    },
    {
      title: "操作",
      width: "160px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: "详情" },
          { onClick: () => handleCopy(record), name: "复制" },
        ]
        if (record.state !== "FINISHED") {
          opts.push({ onClick: () => handleStop(record), name: "停止" })
        }
        if (record.state === "REJECTED") {
          opts.push({
            onClick: () => handleEdit(record),
            name: "重新编辑",
          })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [dataSource, setDataSource] = useState([])
  const [form] = Form.useForm()
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  const handleAdd = () => {
    // history.push('/wecom/customerGroupMsg/form', {query: {id: '123'}})
    props.history.push({
      pathname: "/wecom/customerGroupMsg/form",
      search: "?type=add",
    })
  }
  const handleDetail = (record) => {
    props.history.push({
      pathname: "/wecom/customerGroupMsg/detail",
      search: `?id=${record.id}`,
    })
  }
  const handleCopy = (record) => {
    props.history.push({
      pathname: "/wecom/customerGroupMsg/form",
      search: `?id=${record.id}&type=copy`,
    })
  }
  const handleEdit = (record) => {
    props.history.push({
      pathname: "/wecom/customerGroupMsg/form",
      search: `?id=${record.id}&type=edit`,
    })
  }

  const [operateParams, setOperateParams] = useState({ open: false })
  const OperateModalRef = useRef(null)
  const handleStop = (record) => {
    setOperateParams({
      visible: true,
      title: "停止确认",
      content: `您将停止群发任务【${record.name}】，停止后不可恢复，确认继续吗？`,
      onSubmit: () => {
        postStopSync(record.id)
          .then((res) => {
            message.success("操作成功")
            handleQuery()
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  useEffect(() => {
    handleQuery()
  }, [])

  usePageCacheLifeCycle({
    onShow() {
      handleQuery()
    },
  })
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 })

  const onChangeTable = (pagination) => {
    init(pagination)
  }

  const handleReset = () => {
    form.resetFields()
    init()
  }
  const [disturbParams, setDisturbParams] = useState({ visible: false })
  // const [momentPrevieParams, setMomentPrevieParams] = useState({ open: false })

  // 客户免打扰
  const handleDisturb = () => {
    setDisturbParams({
      visible: true,
      onCancel: () => {
        setDisturbParams({ visible: false })
      },
    })
  }

  const handleQuery = () => {
    init()
  }

  const init = (page = { current: 1, pageSize: 10 }) => {
    form.validateFields().then((form) => {
      setLoading(true)
      if (form.date) {
        form.minStartTime = moment(form.date[0]).format("YYYY-MM-DD 00:00:00")
        form.maxStartTime = moment(form.date[1]).format("YYYY-MM-DD 23:59:59")
        delete form.date
      }
      form.depEmployeeIdList = form.depEmployeeIdList?.join(",") || null
      queryCustomerGroupMsgList({
        ...pagination,
        ...page,
        ...form,
      })
        .then((r) => {
          const { records, current, size, total, pages } = r
          setDataSource(records)
          setPagination({
            current,
            total,
            pageSize: size,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }
  return (
    <>
      <FilterBar>
        <Form layout={"inline"} form={form}>
          <FormItem
            name="nameOrDescription"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="任务名称/说明" allowClear />
          </FormItem>
          <FormItem name="state">
            <SysDictSelect
              placeholder="状态"
              dataset="CUSTOMER_GROUP_MSG_STATE"
            />
          </FormItem>
          <FormItem name="date" label="可发送时间">
            <RangePicker format={"YYYY-MM-DD"} />
          </FormItem>
          <FormItem
            name="depEmployeeIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleDisturb()}>
              客户免打扰
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新建群发客户
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={pagination}
          onChange={onChangeTable}
        />
      </Card>
      <DisturbModal params={disturbParams} />
      <OperateModal ref={OperateModalRef} params={operateParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </>
  )
}

export default CustomerGroupMsg
