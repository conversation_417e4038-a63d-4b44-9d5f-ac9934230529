/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/08/28 11:32
 * @LastEditTime: 2024/11/14 17:11
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/EmployeeCode/ReferralCode/comps/CodeModal.jsx
 * @Description: '活码操作对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Modal,
  Form,
  Select,
  TreeSelect,
  Spin,
} from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const CodeModal = (props) => {
  const { visible, type, record = null, employeeCodeId, onSubmit, onCancel } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('');
  const [employeeCodeList, setEmployeeCodeList] = useState([]);
  const [createMode, setCreateMode] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [disableEmployeeIds, setDisableEmployeeIds] = useState(null);
  const [existTagNameList, setExistTagNameList] = useState([])
  useEffect(async () => {
    if (visible) {
      await getDisableEmployeeIds();
      await getChannelOption();
      await getTagGroupTree();
      switch (type) {
        case 'batchAdd':
          setTitle('批量新增推荐码');
          break;
        case 'edit':
          setTitle(`编辑【${record.employeeName}】的推荐码`);
          break;
        case 'transfer':
          setTitle(`转移【${record.employeeName}】的推荐码`);
          getEmployeeCodeList();
          break;
      }
      if (record) {
        const { employeeCodeId, channelId, customerTagIdList } = record;
        let timer = setTimeout(() => {
          setLoading(true)
          setExistTagNameList(record.tagNameList)
          formRef?.current?.setFieldsValue({
            employeeCodeId: employeeCodeId,
            channelId: channelId,
            customerTagIdList: customerTagIdList,
            tagNameList: record.tagNameList,
          });
          setTimeout(() => {
            setLoading(false)
          }, 1000)
          clearTimeout(timer);
        }, 1000);
      }
    }
  }, [visible]);

  // 员工活码下拉列表
  const getEmployeeCodeList = () => {
    setLoading(true)
    apiCall('/activity/employeeCode/selectMenu', 'GET')
      .then((res) => {
        setEmployeeCodeList(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  // 获取当前活码下已经有推荐码的员工列表
  const getDisableEmployeeIds = async () => {
    setLoading(true)
    await apiCall(`/activity/employeeRecommendCode/getEmployeeList/${employeeCodeId}`, 'GET')
      .then((res) => {
        setDisableEmployeeIds(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  // 获客渠道
  const getChannelOption = () => {
    setLoading(true)
    apiCall('/activity/channel/option', 'GET')
      .then((res) => {
        setCreateMode(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  // 客户标签
  const getTagGroupTree = () => {
    setLoading(true)
    const data = {
      type: 'customer',
    };
    apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      let apiUrl = '';
      switch (type) {
        case 'batchAdd':
          apiUrl = '/activity/employeeRecommendCode';
          break;
        default:
          apiUrl = `/activity/employeeRecommendCode/update/${record.id}`;
          break;
      }
      const data = {
        employeeCodeId: employeeCodeId,
        ...formData,
      };
      apiCall(apiUrl, 'POST', data)
        .then((res) => {
          setConfirmLoading(false);
          onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  return (
    <Modal
      className='CodeModal-Container'
      visible={visible}
      title={title}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={() => {
        setConfirmLoading(false);
        onCancel?.();
      }}
      onOk={onOk}
      width={450}
    >
      <Spin spinning={loading}>
        <Form ref={formRef} {...layout}>
          {(type == 'batchAdd' && disableEmployeeIds) && <FormItem
            label="推荐人"
            name="depEmployeeIdList"
            rules={[{ required: true, message: '请选择推荐人' }]}
            extra="同一活码中，每个员工最多只能有一个推荐码，重复新增不会产生新的推荐码"
          >
            <ETypeTransferModal
              title="选择员工"
              multiple
              disable={disableEmployeeIds}
              needExcludeDepFlag={false}
            />
          </FormItem>}

          {type == 'transfer' && <FormItem label="目标活码" name="employeeCodeId"
            rules={[{ required: true, message: '请选择目标活码' }]}>
            <Select
              options={employeeCodeList}
              showSearch
              allowClear
              placeholder="请选择"
              fieldNames={{ label: 'name', value: 'id' }}
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>}

          <FormItem label="获客渠道" name="channelId">
            <Select
              options={createMode}
              showSearch
              allowClear
              placeholder="请选择"
              fieldNames={{ label: 'name', value: 'id' }}
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>


          <CustomTagSelect
            label="客户标签"
            rules={[{ required: true, message: "请选择客户标签" }]}
            extra={type === 'transfer' ? '推荐码转移时，将自动添加目标活码的客户标签' : ''}
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            existTagNameList={existTagNameList}
            labelTreeData={labelTreeData}
          />
          {/* <FormItem label="客户标签" name="customerTagIdList" extra={type == 'transfer' ? '推荐码转移时，将自动添加目标活码的客户标签' : ''}> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={['customer']} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="请选择" */}
          {/*   /> */}
          {/* </FormItem> */}
        </Form>
      </Spin>
    </Modal>
  );
};

export default CodeModal;
