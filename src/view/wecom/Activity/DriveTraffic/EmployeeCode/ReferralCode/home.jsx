/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/08/28 10:12
 * @LastEditTime: 2024/10/21 16:33
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/EmployeeCode/ReferralCode/home.jsx
 * @Description: '推荐码'
 */

import React, { useState, useEffect, useRef } from 'react'; import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  Image,
  message,
  Spin,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import CodeModal from './comps/CodeModal.jsx';
import { qs2obj } from 'common/object';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import {FileHOC} from 'components/FileHOC/FileHOC';
const FormItem = Form.Item;

const ReferralCode = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [employeeCodeId, setEmployeeCodeId] = useState(null);
  const [employeeCodeInfo, setEmployeeCodeInfo] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '推荐码',
      width: '160px',
      dataIndex: 'url',
      key: 'url',
      align: 'center',
      render: (value, record, index) => {
        return <FileHOC src={value}>
          {(url) => (
            <Image width={60} src={url} preview={url}></Image>
          )}
        </FileHOC>;
      },
    },
    {
      title: '推荐人',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      render: (value, record, index) => {
        const content = <div style={{ textAlign: 'left' }}>
          {value} <br /> {record.departmentName}
        </div>
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>
      },
    },
    {
      title: '获客渠道',
      width: '160px',
      dataIndex: 'channelName',
      key: 'channelName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '状态',
      width: '160px',
      dataIndex: 'enable',
      key: 'enable',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? '启用' : '停用'}>{value ? '启用' : '停用'}</Tooltip>
      ),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleCodeModal(record, 'edit'), name: "编辑" },
          { onClick: () => handleCodeModal(record, 'transfer'), name: "转移" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        if (record.enable) {
          opts.push({ onClick: () => handleCollect(record), name: "停用" });
        } else {
          opts.push({ onClick: () => handleCollect(record), name: "启用" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [CodeParams, setCodeParams] = useState({ visible: false });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);

  useEffect(() => {
    const { employeeCodeId } = qs2obj(props.location.search);
    setEmployeeCodeId(employeeCodeId);
  }, []);

  useEffect(() => {
    employeeCodeId && getEmployeeCodeInfo();
  }, [employeeCodeId]);

  const getEmployeeCodeInfo = () => {
    apiCall(`/activity/employeeCode/${employeeCodeId}`, 'GET')
      .then((res) => {
        setEmployeeCodeInfo(res);
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        employeeCodeId: employeeCodeId
      };
      apiCall('/activity/employeeRecommendCode', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleCodeModal = (record = {}, type) => {
    setCodeParams({
      visible: true,
      type: type,
      record: record,
      employeeCodeId: employeeCodeId,
      onSubmit: () => {
        switch (type) {
          case 'batchAdd':
            message.success('新增成功');
            break;
          case 'edit':
            message.success('编辑成功');
            break;
          case 'transfer':
            message.success('转移成功');
            break;
        }
        setCodeParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setCodeParams({ visible: false });
      },
    });
  };

  const handleDelete = (record) => {
    const { id, employeeName } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `确定要删除活码【${employeeCodeInfo?.name}】的【${employeeName}】推荐码吗？`,
      onSubmit: () => {
        apiCall(`/activity/employeeRecommendCode/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleCollect = (record) => {
    const { id, employeeName, enable } = record;
    setOperateParams({
      visible: true,
      title: enable ? "停用确认" : "启用确认",
      content: `您将为活码【${employeeCodeInfo?.name}】的【${employeeName}】推荐码进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          enable: !enable,
        };
        apiCall(`/activity/employeeRecommendCode/update/${id}`, 'POST', data)
          .then((res) => {
            message.success('操作成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleDownloads = () => {
    const data = {
      employeeCodeId: employeeCodeId,
      idList: selectedRowKeys?.join(',') || null
    };
    apiCall(`/activity/employeeRecommendCode/download`, 'GET', data, null, {
      isExit: true,
      title: `活码【${employeeCodeInfo?.name}】的推荐码.${moment().format('YYYY-MM-DD')}.zip`,
    })
      .then((res) => {
        message.success('下载成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  return (
    <div className='ReferralCode-Container'>
      <Card title={`活码【${employeeCodeInfo?.name || ''}】的推荐码`} extra={<Button type="primary" onClick={() => {
        props.history.go(-1);
      }}>返回</Button>} bordered={false}>
        <Spin spinning={loading}>
          <FilterBar bodyStyle={{ padding: 'unset' }}>
            <Form layout={'inline'} ref={formRef}>
              <FormItem
                name="employeeName"
                getValueFromEvent={(e) => removeInputEmpty(e)}
              >
                <Input placeholder="推荐人" allowClear />
              </FormItem>
            </Form>
            <div className="flex flex-space-between">
              <div>
                <Button type="primary" onClick={() => handleQuery()}>
                  查询
                </Button>
                <Button onClick={() => handleReset()}>重置筛选</Button>
              </div>
              <div>
                <Tooltip title={'下载列表中选中的推荐码，没有选中则下载全部'}>
                  <Button type="primary" onClick={() => handleDownloads()}>
                    批量下载
                  </Button>
                </Tooltip>
                <Button type="primary" onClick={() => handleCodeModal({
                  channelId: employeeCodeInfo.channelId,
                  tagNameList: employeeCodeInfo.tagNameList,
                }, 'batchAdd')}>
                  批量新增
                </Button>
              </div>
            </div>
          </FilterBar>
          <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
            <Table
              rowKey="id"
              dataSource={dataSource}
              columns={columns}
              scroll={{ x: 1300 }}
              pagination={paginations}
              onChange={onChangeTable}
              rowSelection={rowSelection}
            />
          </Card>
        </Spin>
      </Card>
      <OperateModal params={operateParams} />
      <CodeModal params={CodeParams} />
    </div>
  );
};

export default ReferralCode;
