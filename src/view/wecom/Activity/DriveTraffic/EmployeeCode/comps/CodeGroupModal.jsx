/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/08/28 09:44
 * @LastEditTime: 2023/11/21 11:12
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/EmployeeCode/comps/CodeGroupModal.jsx
 * @Description: '管理活码分组'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, Button, Table, Input, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import ListOperation from 'components/ListOperation/home';

const CodeGroupModal = (props) => {
  const { visible, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [initDataSource, setInitDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '名称',
      width: '170px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Input
              maxLength={15}
              value={value}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(dataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, '');
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入15字内"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          )}
        </>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    if (visible) {
      getGroupOptions();
    }
  }, [visible]);

  const getGroupOptions = async () => {
    setLoading(true);
    await apiCall('/activity/employeeCodeGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        const { records } = res;
        setDataSource(records);
        setInitDataSource(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = (record = {}) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    const { name = '' } = record;
    newDataSource.unshift({
      name: name,
      isEdit: true,
    });
    setDataSource([...newDataSource]);
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record) => {
    const { id, name } = record;
    if (!name) {
      message.warning('名称不能为空！');
      return false;
    }
    setLoading(true);
    apiCall('/activity/employeeCodeGroup', 'POST', {
      id: id || null,
      name: name,
    })
      .then((res) => {
        message.success('保存成功！');
        getGroupOptions();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/activity/employeeCodeGroup/delete/${id}`, 'POST')
      .then((res) => {
        message.success('删除成功！');
        let newDataSource = dataSource;
        newDataSource.splice(index, 1);
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index) => {
    const { id } = record;
    let newDataSource = dataSource;
    if (id) {
      newDataSource[index].isEdit = false;
      const findData = initDataSource.find(item => item.id == id);
      findData && (newDataSource[index].name = findData.name);
    } else {
      newDataSource.splice(index, 1);
    }
    setDataSource([...newDataSource]);
  };

  return (
    <Modal
      visible={visible}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          活码分组
          <Button
            style={{ marginRight: '20px' }}
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd()}
          >
            新增
          </Button>
        </div>
      }
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      centered
      onCancel={() => {
        setLoading(false);
        setDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default CodeGroupModal;
