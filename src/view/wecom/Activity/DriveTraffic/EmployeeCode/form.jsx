/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/08/24 16:57
 * @LastEditTime: 2025/05/15 17:16
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/EmployeeCode/form.jsx
 * @Description: '活码新增编辑页'
 */

import { QuestionCircleOutlined } from "@ant-design/icons"
import {
  Button,
  Card,
  Col,
  Collapse,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  Tooltip,
  TreeSelect,
  Typography,
} from "antd"
import { qs2obj } from "common/object"
import { removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import MaterialListForm from "components/MaterialListForm/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import { isV1, isV2 } from "config"
import React, { useEffect, useRef, useState } from "react"
import "./form.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
const FormItem = Form.Item
const { Paragraph } = Typography
const { SHOW_PARENT } = TreeSelect
const { TextArea } = Input
const { Panel } = Collapse
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 11 },
  },
}
const EmployeeCodeForm = (props) => {
  const [employeeCodeForm] = Form.useForm()
  const onRefMaterialListForm = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [createMode, setCreateMode] = useState([])
  const [msgList, setMsgList] = useState([])
  const [districtRadio, setDistrictRadio] = useState(null)

  const [employeeCodeGroup, setEmployeeCodeGroup] = useState([])
  const [departmentAreaCode, setDepartmentAreaCode] = useState([])
  const [enable, setEnable] = useState(true) // 启用状态
  const [existTagNameList, setExistTagNameList] = useState([])
  // 活码页标题
  const [pageTitle, setPageTitle] = useState(null)
  // 活码页引导语
  const [pageGuide, setPageGuide] = useState(null)

  useEffect(async () => {
    const { id } = qs2obj(props.location.search)
    await getEmployeeCodeGroup()
    await getcreateModeType()
    await getTagCategoryTreeTwo()
    await getDepartmentAreaCode()
    if (id) {
      setId(Number(id))
      await init(id)
    } else {
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
  }, [])

  const init = async (id) => {
    const params = {
      id,
    }
    await fetchList(params)
  }

  // 活码分组
  const getEmployeeCodeGroup = () => {
    apiCall("/activity/employeeCodeGroup", "GET", {
      paged: false,
    })
      .then((res) => {
        setEmployeeCodeGroup(res.records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获客渠道
  const getcreateModeType = () => {
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setCreateMode(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ])
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 查询部门区域代码列表
  const getDepartmentAreaCode = async () => {
    const data = {
      paged: false,
    }
    await apiCall("/base/departmentAreaCode", "GET", data)
      .then((res) => {
        setDepartmentAreaCode(res.records)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    await apiCall(`/activity/employeeCode/${id}`, "GET")
      .then(async (res) => {
        employeeCodeForm.setFieldsValue({ ...res, szState: { ...res.szState, scenario: !!res.szState?.scenario } })
        const { messages, enable, triageStrategy } = res
        setEnable(enable)
        setIsAuthEnabled(res.szState?.isAuthEnabled)
        setDistrictRadio(triageStrategy?.assignPriority)
        // 资源中心组件-欢迎语
        onRefMaterialListForm.current.getInitMsgList(messages)
        setExistTagNameList(res.tagNameList)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 提交
  const onSubmit = () => {
    employeeCodeForm.validateFields().then((formData) => {
      if (onRefMaterialListForm.current.getCopyWriterValidate()) {
        // 校验资源文案字数
        return
      }
      console.log(formData, "formData")
      setLoading(true)
      const data = {
        id: id ?? null,
        ...formData,
        szState: {
          ...formData.szState
        },
        enable: typeof id == "number" ? enable : true, // id存在编辑状态，id不存在新增状态默认true
        messages: onRefMaterialListForm.current.getModifyMsgList(),
      }
      if (isV2()) {
        data.szState.scenario = formData.szState?.scenario ? 1 : 0
        data.triageStrategy.type = "ICBC_SZ"
      }
      const apiUrl = id
        ? `/activity/employeeCode/update/${id}`
        : "/activity/employeeCode"
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          setTimeout(() => {
            props.history.push("/wecom/drivetraffic")
          }, 1000)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const [isAuthEnabled, setIsAuthEnabled] = useState(true)
  const onAuthEnabledChange = (checked) => {
    // 当 "是否授权手机号" 关闭时，同时关闭 "是否强制授权"
    setIsAuthEnabled(checked)
  };

  return (
    <div className="EmployeeCodeForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑活码" : "新增活码"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Form {...layout} form={employeeCodeForm}>
            <Row gutter={10}>
              <Col xs={24} lg={16}>
                <FormItem
                  label="活码名称"
                  name="name"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[
                    { required: true, message: "请输入活码名称（30字）" },
                  ]}
                >
                  <Input
                    placeholder="请输入活码名称（30字）"
                    allowClear
                    maxLength={30}
                  />
                </FormItem>
                <FormItem
                  label="活码分组"
                  name="employeeCodeGroupId"
                  rules={[{ required: true, message: "请选择活码分组" }]}
                >
                  <Select
                    placeholder="活码分组"
                    fieldNames={{ label: "name", value: "id" }}
                    options={employeeCodeGroup}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                <FormItem label="获客渠道" name="channelId">
                  <Select
                    options={createMode}
                    allowClear
                    placeholder="请选择"
                    fieldNames={{ label: "name", value: "id" }}
                    filterOption={(input, option) =>
                      (option?.name ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                {/* <FormItem label="客户标签" name="customerTagIdList"> */}
                {/*   <TreeSelect */}
                {/*     treeData={labelTreeData} */}
                {/*     treeCheckable */}
                {/*     treeDefaultExpandedKeys={["customer"]} */}
                {/*     allowClear */}
                {/*     showArrow */}
                {/*     showSearch */}
                {/*     treeNodeFilterProp="title" */}
                {/*     maxTagCount="responsive" */}
                {/*     showCheckedStrategy={SHOW_PARENT} */}
                {/*     placeholder="客户标签" */}
                {/*   /> */}
                {/* </FormItem> */}

                <CustomTagSelect
                  label="客户标签"
                  name="tagNameList"
                  placeholder="客户标签"
                  useForm={employeeCodeForm}
                  existTagNameList={existTagNameList}
                  labelTreeData={labelTreeData}
                />
                <FormItem
                  name="autoPassFriend"
                  label="自动通过好友"
                  valuePropName="checked"
                  initialValue
                >
                  <Switch />
                </FormItem>
                {/* <FormItem label="分流策略" required>
                  <FormItem
                    name={["triageStrategy", "type"]}
                    initialValue={"ICBC_CUSTOMER"}
                    rules={[{ required: true, message: "请选择分流策略" }]}
                  >
                    <Radio.Group>
                      <Radio value={"ICBC_CUSTOMER"}>工行客户归属</Radio>
                    </Radio.Group>
                  </FormItem>

                </FormItem> */}
                {isV1() ? (
                  <FormItem
                    label="分流策略"
                    name={["triageStrategy", "type"]}
                    initialValue="ICBC_CUSTOMER"
                    rules={[{ required: true, message: "请选择分流策略" }]}
                  >
                    <Radio.Group>
                      <Radio value={"ICBC_CUSTOMER"}>工行客户归属</Radio>
                    </Radio.Group>
                  </FormItem>
                ) : null}
                <div className="employeeCode-content-mask">
                  <FormItem
                    name={["triageStrategy", "depEmployeeIdList"]}
                    label="目标员工"
                    rules={[{ required: true, message: "请选择员工" }]}
                  >
                    <ETypeTransferModal title="选择员工" multiple />
                  </FormItem>
                  {isV2() ? (
                    <FormItem
                      name={["triageStrategy", "duplicateList"]}
                      label="去重名单"
                      tooltip="添加去重名单后，系统会在用户扫码时自动进行检查。若用户在去重名单或目标员工名单中已存在好友关系，扫码后会显示“已添加过的好友”；若用户不在这两个名单中 ，系统会从目标员工名单里随机展示一张员工码。"
                      rules={[{ required: false, message: "请选择员工" }]}
                    >
                      <ETypeTransferModal title="选择员工" multiple />
                    </FormItem>
                  ) : null}
                  {isV1() ? (
                    <>
                      <FormItem
                        label="分配优先级"
                        name={["triageStrategy", "assignPriority"]}
                        initialValue={1}
                        wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                        // rules={[{ required: true, message: "请选择目标优先级" }]}
                      >
                        <Radio.Group
                          onChange={(e) => {
                            setDistrictRadio(e.target.value)
                          }}
                        >
                          <Radio value={1}>
                            目标员工优先
                            <Tooltip title="直接使用目标员工，如果目标员工有多个，则取第一个。">
                              <QuestionCircleOutlined className="tipsIcon" />
                            </Tooltip>
                          </Radio>
                          <Radio value={2}>
                            全行管户优先
                            <Tooltip title="先查找客户在全行范围的管户员工，找不到时再采用“目标员工优先”的规则进行选择。">
                              <QuestionCircleOutlined className="tipsIcon" />
                            </Tooltip>
                          </Radio>
                          <Radio value={3}>
                            地区管户优先
                            <Tooltip title="先查找客户在指定地区的管户员工，找不到时再采用“目标员工优先”的规则进行选择。">
                              <QuestionCircleOutlined className="tipsIcon" />
                            </Tooltip>
                          </Radio>
                          <Radio value={4}>
                            距离优先
                            <Tooltip title="先查找离客户最近的网点企微账号，找不到时再采用“目标员工优先”的规则进行选择。">
                              <QuestionCircleOutlined className="tipsIcon" />
                            </Tooltip>
                          </Radio>
                          <Radio value={5}>
                            资产优先
                            <Tooltip
                              title="先查找客户存放资产最多的网点企微账号，找不到时再采用“目标员工优先”的规则进行选择。">
                              <QuestionCircleOutlined className="tipsIcon" />
                            </Tooltip>
                          </Radio>
                          <Radio value={6}>
                            接口优先
                            {/* <Tooltip title="直接使用目标员工，如果目标员工有多个，则取第一个。">
                          <QuestionCircleOutlined className="tipsIcon" />
                        </Tooltip> */}
                          </Radio>
                        </Radio.Group>
                      </FormItem>
                      {districtRadio == 3 && (
                        <FormItem
                          label="地区"
                          name={["triageStrategy", "departmentAreaCodeId"]}
                        >
                          <Select
                            options={departmentAreaCode}
                            allowClear
                            placeholder="请选择"
                            fieldNames={{ label: "areaName", value: "id" }}
                            filterOption={(input, option) =>
                              (option?.name ?? "")
                                .toLowerCase()
                                .includes(input.toLowerCase())
                            }
                            style={{ width: "220px" }}
                          />
                        </FormItem>
                      )}
                    </>
                  ) : null}
                  {
                    isV2() ? <>
                        <FormItem
                          name={['szState', 'isAuthEnabled']}
                          label="是否授权手机号"
                          valuePropName="checked"
                          initialValue={isAuthEnabled}
                        >
                          <Switch onChange={onAuthEnabledChange} />
                        </FormItem>
                        {
                          isAuthEnabled && (
                            <FormItem
                              name={['szState', 'isAuthForced']}
                              label="是否强制授权"
                              valuePropName="checked"
                              initialValue={false}
                            >
                              <Switch />
                            </FormItem>
                          )
                        }

                        <FormItem
                          name={['szState', 'scenario']}
                          label="是否厅堂场景"
                          valuePropName="checked"
                          initialValue={false}
                        >
                          <Switch />
                        </FormItem>
                      </> :
                      null
                  }
                </div>

                {/* 素材库列表表单 */}
                <FormItem
                  name="msgList"
                  label="欢迎语"
                  wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                  rules={[{ required: true, message: "请添加欢迎语！" }]}
                >
                  <>
                    <div className="WelcomeSpeech-tips">
                      {isV1() ? "您的欢迎语会随上级部门欢迎语一同发，" : ""}
                      客户最多接收到前1个文案9个附件，且文案超长时会被截断
                      {/* <span className='checkContents' onClick={() => { handlePreview() }}>查看上级部门欢迎语</span> */}
                    </div>
                    <MaterialListForm
                      params={{
                        formRef: employeeCodeForm,
                        menuList: isV1()
                          ? ["image", "material", "liveCode"]
                          : ["image", "material"],
                        needScriptFlag: true,
                        materialAmount: msgList.length,
                        // previewList: previewList,
                        extra: (
                          <div>
                            请按顺序添加预计要发送的欢迎语，还可添加{" "}
                            {10 - msgList.length} 条<br />
                            选择素材后，参考话术将会自动添加在唯一的文案中
                          </div>
                        ),
                      }}
                      // 监听回调
                      callback={(params) => {
                        setMsgList(params.data)
                      }}
                      ref={onRefMaterialListForm}
                    />
                  </>
                </FormItem>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    marginTop: "40px",
                  }}
                >
                  <Space size={40}>
                    <Button type="primary" onClick={() => onSubmit()}>
                      确定
                    </Button>
                  </Space>
                </div>
              </Col>
              <Col xs={24} lg={8}>
                <WibotMaterialPreview
                  type="session"
                  mergeFlag={true}
                  listData={msgList}
                />
              </Col>
            </Row>
          </Form>
        </Card>
      </Spin>
    </div>
  )
}

export default EmployeeCodeForm
