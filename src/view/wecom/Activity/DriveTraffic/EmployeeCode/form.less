.EmployeeCodeForm {
  .employeeCode-content-mask {
    // width: 476px;
    // padding: 10px;
    padding: 10px 0;
    margin-bottom: 24px;
    background: #f2f2f2;
    border-radius: 6px;
    .tipsIcon{
      color: #7F7F7F;
      margin-left: 5px;
    }
  }
  .employeeCode-wrap {
    text-align: center;

    .phone-box {
      position: relative;
      width: 306px;
      height: 600px;
      margin: auto;

      .phone_box_img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .title {
        position: absolute;
        top: 55px;
        left: 125px;
      }

      .text {
        position: absolute;
        top: 100px;
        left: 40px;
        width: 220px;
        text-align: left;
        color: #8b8b8b;
        font-size: 12px;
      }

      .qw-code {
        position: absolute;
        top: 160px;
        left: 70px;

        img {
          width: 160px;
          height: 160px;
          object-fit: contain;
        }

        p {
          color: #8b8b8b;
          font-size: 12px;
          margin-top: 20px;
        }
      }

      .phone-bot {
        position: absolute;
        bottom: 38px;
        left: 35px;
        display: flex;
        flex-direction: row;

        .bot-text {
          width: 132px;
          color: #8b8b8b;
          font-size: 12px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
        }

        .bot-cancat {
          width: 80px;
          background: #f2f2f2;
          padding: 5px;
          box-sizing: content-box;
          color: #8b8b8b;
          border: 1px solid #8b8b8b;
        }

        button {
          vertical-align: middle;
          padding: 4px 10px;
        }
      }

      // .title {
      //   position: absolute;
      //   top: 55px;
      //   left: 122px;
      // }

      .ant-empty {
        position: absolute;
        top: 100px;
        left: 122px;
      }

      .content {
        position: absolute;
        top: 105px;
        left: 18px;

        .list-item {
          padding: 0;
          margin: 0;
          height: 450px;
          overflow-y: scroll;
          text-align: left;

          .item {
            display: block;
            margin: 0 10px 20px;
            position: relative;

            .title {
              margin: 0;
              font-size: 16px;
            }

            .describe {
              max-width: 250px;
              display: inline-block;
              padding: 10px;
              border-radius: 6px;
              box-shadow: 1px 2px 3px 0px #ccc;
              border: 1px solid #ccc;
              margin: 0;
              white-space: normal;
            }

            .content {
              margin: 10px 0;
            }

            .review-wrap {
              display: inline-block;
              vertical-align: top;
              width: 194px;

              .ant-typography {
                width: 100%;

                p {
                  display: inline-block;
                  word-break: break-all;
                }
              }
            }
          }

          // 滚动条整体部分
          &::-webkit-scrollbar {
            width: 6px; //对垂直方向滚动条
            height: 6px; //对水平方向滚动条
          }

          //滚动的滑块
          &::-webkit-scrollbar-thumb {
            border-radius: 6px;
            background-color: #f2f2f2 //滚动条的颜色
;
          }

          //内层滚动槽
          &::-webkit-scrollbar-track-piece {
            background-color: #f2f2f2;
          }
        }
      }
    }
  }

  .scrollList {
    max-width: 1000px;
    height: 150px;
    margin: 0 auto 20px;
    padding: 0;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;

    .listItem {
      position: relative;
      width: 250px;
      display: inline-block;
      height: 124px;
      border: 1px solid #d7d7d7;
      border-radius: 6px;
      padding: 10px;
      overflow: hidden;
      cursor: pointer;
      margin-right: 30px;
      white-space: break-spaces;

      h2 {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
      }

      p {
        font-size: 14px;
        margin: 0;
      }

      .active {
        border: 20px solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-right-color: #1890ff;
        position: absolute;
        transform: rotate(135deg);
        right: -20px;
        top: -20px;
        display: none;

        span {
          position: absolute;
          color: #fff;
          left: 6px;
          bottom: -8px;
          transform: rotate(-120deg);
          font-size: 13px;
        }
      }

      &.activeItem {
        border: 1px solid #1890ff;

        .active {
          display: block;
        }
      }
    }

    // 滚动条整体部分
    &::-webkit-scrollbar {
      width: 6px; //对垂直方向滚动条
      height: 6px; //对水平方向滚动条
    }

    //滚动的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: rgba(0, 0, 0, 0.5) //滚动条的颜色
;
    }

    //内层滚动槽
    &::-webkit-scrollbar-track-piece {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .PreviewFormWrap {
    height: unset !important;
    padding: unset !important;
    // margin-top: 20px;
  }
}
