/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/08/24 15:47
 * @LastEditTime: 2024/10/21 16:32
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/EmployeeCode/home.jsx
 * @Description: '员工活码'
 */

import React, {useEffect, useRef, useState} from 'react';
import {withRouter} from 'react-router-dom';
import {Button, Card, Form, Image, Input, message, Select, Spin, Switch, Table, Tooltip,} from 'antd';
import {removeInputEmpty} from 'common/regular';
import {apiCall} from 'common/utils';
import {timeStamp} from 'common/date';
import FilterBar from 'components/FilterBar/FilterBar';
import OperateModal from 'components/Modal/OperateModal/index';
import CodeGroupModal from './comps/CodeGroupModal';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import {usePageCacheLifeCycle} from "common/hooks";
import {FileHOC} from 'components/FileHOC/FileHOC';
const FormItem = Form.Item;

const EmployeeCode = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [employeeCodeGroup, setEmployeeCodeGroup] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [codeGroupParams, setCodeGroupParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '活码名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '活码二维码',
      width: '160px',
      dataIndex: 'url',
      key: 'url',
      align: 'center',
      render: (value, record, index) => (
        <FileHOC src={value}>
          {(url) => (
            <Image width={60} src={url} preview={url}></Image>
          )}
        </FileHOC>
      ),
    },
    {
      title: '活码分组',
      width: '160px',
      dataIndex: 'employeeCodeGroupName',
      key: 'employeeCodeGroupName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '获客渠道',
      width: '160px',
      dataIndex: 'channelName',
      key: 'channelName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '推荐人',
      width: '160px',
      dataIndex: 'recommendEmployeeNameList',
      key: 'recommendEmployeeNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '启用状态',
      width: '160px',
      dataIndex: 'enable',
      key: 'enable',
      align: 'center',
      render: (value, record, index) => (
        <Switch
          checkedChildren="已启用"
          unCheckedChildren="已停用"
          checked={value}
          onChange={(checked) => {
            onChangeSwitchStatus(checked, record);
          }}
        />
      ),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleRecommend(record), name: "推荐" },
          // { onClick: () => handleDetails(record), name: "数据" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const index = columns.findIndex(i => i.title === '客户标签');
  columns.splice(index, 0, {
    title: '目标员工',
    width: '180px',
    dataIndex: 'targetNameList',
    key: 'targetNameList',
    align: 'center',
    render: (value, record, index) => <WibotTableTag tagList={value}/>,
  },)

  useEffect(() => {
    getEmployeeCodeGroup();
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList();
    }
  })

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.employeeCodeGroupIdList = formData.employeeCodeGroupIdList?.join(',') || null;
      formData.employeeRecommendDepEmployeeIdList = formData.employeeRecommendDepEmployeeIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/activity/employeeCode', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getEmployeeCodeGroup = () => {
    apiCall('/activity/employeeCodeGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        setEmployeeCodeGroup(res.records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeSwitchStatus = (checked, record) => {
    console.log(checked, record);
    const data = {
      enable: checked,
    };
    apiCall(`/activity/employeeCode/updateEnable/${record.id}`, 'POST', data)
      .then((res) => {
        message.success('修改成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 分组管理
  const handleManage = () => {
    setCodeGroupParams({
      visible: true,
      onCancel: () => {
        setCodeGroupParams({ visible: false });
        getEmployeeCodeGroup();
      },
    });
  };

  const handleAdd = () => {
    props.history.push('/wecom/drivetraffic/employeeCode/form');
  };

  const handleRecommend = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/drivetraffic/employeeCode/referralCode',
      search: `?employeeCodeId=${id}`,
    });
  };

  const handleDetails = (record) => {
    const { id, type } = record;
    props.history.push({
      pathname: "/wecom/drivetraffic/liveCode/details",
      search: `?id=${id}&type=${type}`,
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/drivetraffic/employeeCode/form',
      search: `?id=${id}`,
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除活码【${name}】，删除后活码立即失效，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/activity/employeeCode/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };


  return (
    <div className="EmployeeCode">
      <Spin spinning={loading}>
        <FilterBar bodyStyle={{ padding: "unset" }}>
          <Form layout={'inline'} ref={formRef}>
            <FormItem
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="活码名称" allowClear />
            </FormItem>
            <FormItem name="employeeCodeGroupIdList">
              <Select
                placeholder="活码分组"
                fieldNames={{ label: 'name', value: 'id' }}
                options={employeeCodeGroup}
                allowClear
                showSearch
                showArrow
                mode="multiple"
                maxTagCount="responsive"
                filterOption={(input, option) =>
                  (option?.name ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>

            <FormItem
              name="employeeRecommendDepEmployeeIdList"
              style={{
                minWidth: "unset",
                maxWidth: "205px",
                marginRight: "0px",
              }}
            >
              <ETypeTransferModal title="推荐人" needExcludeDepFlag={false} />
            </FormItem>
          </Form>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
            <div>
              <Button type="primary" onClick={() => handleManage()}>
                分组管理
              </Button>
              <Button type="primary" onClick={() => handleAdd()}>
                新增
              </Button>
            </div>
          </div>
        </FilterBar>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
      <OperateModal params={operateParams} />
      <CodeGroupModal params={codeGroupParams} />
    </div>
  );
};

export default withRouter(EmployeeCode);
