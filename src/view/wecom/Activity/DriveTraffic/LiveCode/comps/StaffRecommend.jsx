/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/23 11:18
 * @LastEditTime: 2025/07/11 17:42
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/comps/StaffRecommend.jsx
 * @Description: '员工推荐码'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Form,
  Input,
  Typography,
  Select,
  TreeSelect,
  Switch,
  message,
  Row,
  Col,
  Space,
  Radio,
} from "antd"
import { CustomerServiceOutlined } from "@ant-design/icons"
import { apiCall } from "common/utils"
import { removeInputEmpty } from "common/regular"
import {
  recursionTagKeyTreeData,
  recursionTagKeyTreeDataTag,
} from "common/tree"
import { qs2obj } from "common/object"
import qwCode from "images/qwCode.png"
import MaterialListForm from "components/MaterialListForm/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import { clearCache } from "react-router-cache-route"
import WibotEditor from "components/WibotEditor/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
import WibotEditorView from "components/WibotEditorView/home"

const FormItem = Form.Item
const { Paragraph } = Typography
const { SHOW_PARENT } = TreeSelect
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 11 },
  },
}
const StaffRecommend = (props) => {
  const { changeLoading } = props
  const [liveCodeform] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const onRefMaterialListForm = useRef()
  const [id, setId] = useState(null)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [empData, setEmpData] = useState([])
  const [createMode, setCreateMode] = useState([])
  const [deletEmpKey, setDeletEmpKey] = useState([])
  const [empDetailList, setEmpDetailList] = useState([])
  const [editorMessage, setEditorMessage] = useState("")
  const [msgList, setMsgList] = useState([])
  const [previewList, setPreviewList] = useState([])
  const [bypassType, setBypassType] = useState("FIXED")
  const [existTagNameList, setExistTagNameList] = useState([])
  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    getcreateModeType()
    getTagCategoryTreeTwo()
    liveCodeform.setFieldsValue({
      skipVerify: true,
    })
    if (id) {
      setId(id)
      init(id)
    } else {
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
  }, [])

  const init = async (id) => {
    const params = {
      id,
    }
    await fetchList(params)
  }

  const fetchList = async (params = {}) => {
    changeLoading(true)
    const { id } = params
    const data = {
      id,
    }
    await apiCall("/activity/dynamicCode/getAndRunningDetailList", "GET", data)
      .then(async (res) => {
        liveCodeform.setFieldsValue({
          ...res,
          visibleScopeDepartmentId: res.visibleScopeDepartmentIdList,
        })
        const {
          tagIdList,
          detailList,
          messages,
          leadingMessage,
          bypassType,
          tagNameList,
        } = res
        setBypassType(bypassType)
        setEmpDetailList(detailList)
        setEmpData(
          detailList
            .filter((item) => !item.reserve)
            .map((item) => ({
              id: item.objectId,
              name: item.objectName,
            }))
        )
        liveCodeform.setFieldsValue({
          // tagIdList: tagIdList.length > 0 ? tagIdList.join(",").split(",") : [],
          tagNameList,
          codeObj: detailList
            .filter((item) => !item.reserve)
            .map((item) => String(item.objectId)),
        })

        // 资源中心组件-欢迎语
        onRefMaterialListForm.current.getInitMsgList(messages)
        setExistTagNameList(res.tagNameList)
        let timer = setTimeout(() => {
          WibotEditorRef.current.setHtml(leadingMessage)
          clearTimeout(timer)
        }, 300)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        changeLoading(false)
      })
  }

  // 获客渠道
  const getcreateModeType = () => {
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setCreateMode(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 添加设置
  const onChangeSwitchSetState = (checked) => {
    liveCodeform.setFieldsValue({
      skipVerify: checked ? 1 : 0,
    })
  }

  // 选择部门或员工获取预览数据
  const handleDeptIdChange = (value, options = [], type = null) => {
    changeLoading(true)
    if (type) {
      setEmpData([...options])
      liveCodeform.setFieldsValue({
        codeObj: value,
      })
    }
    const data = {
      deptId: type ? value[0] : value,
    }
    apiCall("/welcomeWord/superior", "GET", data)
      .then((res) => {
        const newItemList = res
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Poster" ||
            item.type == "QrCode"
          ) {
            item.image = item.fileId ? item.fileId[0] : ""
          }
        })
        setPreviewList(newItemList)
        if (type) {
          message.success("已在界面预览处显示上级部门欢迎语！")
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        changeLoading(false)
      })
  }

  // 提交
  const onSubmit = () => {
    liveCodeform.validateFields().then((formData) => {
      const { tagIdList } = formData
      if (onRefMaterialListForm.current.getCopyWriterValidate()) {
        // 校验资源文案字数
        return
      }
      // 被删除的活码对象id
      const detailIdList = []
      empDetailList.map((item) => {
        if (deletEmpKey.indexOf(item.objectId) > -1) {
          detailIdList.push(item.id)
        }
      })
      changeLoading(true)
      const data = {
        id: id ?? null,
        type: "RECOMMEND",
        ...formData,
        createMode: bypassType == "FIXED" ? "ONE" : "MULTI",
        tagIdList: tagIdList ?? [],
        detailIdList,
        state: !id ? true : null,
        messages: onRefMaterialListForm.current.getModifyMsgList(),
      }
      // 员工活码对象
      data.detailList = empData.map((item) => ({
        objectId: item.id,
        objectName: item.name,
        objectType: "EMPLOYEE",
        state: true,
        reserve: false,
      }))
      if (bypassType == "LOCATION") {
        data.object = {
          objectList: empData.map((item) => ({
            voList: [item],
          })),
          objectType: "EMPLOYEE",
          type: "ALL_DAY",
        }
      }
      apiCall("/activity/dynamicCode/saveOrUpdate", "PUT", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          localStorage.removeItem("objList")
          localStorage.removeItem("objGroupList")
          localStorage.removeItem("objReserveList")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/drivetraffic")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          changeLoading(false)
        })
    })
  }

  return (
    <Row>
      <Col xs={24} lg={16}>
        <h2 className="card-title">基础信息</h2>
        <Form {...layout} form={liveCodeform} labelWrap>
          <FormItem
            label="活码名称"
            name="name"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: "请输入活码名称（30字）" }]}
          >
            <Input
              placeholder="请输入活码名称（30字）"
              allowClear
              maxLength={30}
            />
          </FormItem>

          <FormItem label="获客渠道" name="channelId">
            <Select
              options={createMode}
              showSearch
              allowClear
              placeholder="请选择"
              fieldNames={{ label: "name", value: "id" }}
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>

          {/* <FormItem label="客户标签" name="tagIdList"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={["customer"]} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <CustomTagSelect
            label="客户标签"
            rules={[{ required: true, message: "请选择客户标签" }]}
            name="tagNameList"
            allowClear
            showArrow
            showSearch
            placeholder="客户标签"
            useForm={liveCodeform}
            existTagNameList={existTagNameList}
            labelTreeData={labelTreeData}
          />

          <FormItem
            name="modifyAble"
            label="允许调整推荐码信息"
            // labelAlign='left'
            valuePropName="checked"
            extra="开启后，员工在认领该推荐码时将可以调整获客渠道和客户标签"
            initialValue={false}
          >
            <Switch />
          </FormItem>

          <h2>活码信息</h2>
          <FormItem
            label="分流策略"
            name="bypassType"
            initialValue={"FIXED"}
            extra={
              <div>
                {bypassType == "FIXED" ? (
                  <div>活码对象有且只有一个</div>
                ) : (
                  <div>根据客户所在位置分流到距离最近的对象</div>
                )}
              </div>
            }
          >
            <Radio.Group
              onChange={(e) => {
                setBypassType(e.target.value)
                liveCodeform.setFieldsValue({
                  codeObj: [],
                })
              }}
            >
              <Radio value={"FIXED"}>固定</Radio>
              <Radio value={"LOCATION"}>地理位置</Radio>
            </Radio.Group>
          </FormItem>

          <FormItem
            label="活码对象"
            name="codeObj"
            rules={[{ required: true, message: "请选择活码对象！" }]}
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={bypassType != "FIXED"}
              onChange={(value, options) => {
                handleDeptIdChange(value, options, "EMPLOYEE")
              }}
            />
          </FormItem>

          <FormItem
            name="preferDefaultUser"
            label="活码对象优先"
            valuePropName="checked"
            extra="开启后，优先使用活码对象指定的员工而不是接口指定的员工"
            initialValue={false}
          >
            <Switch />
          </FormItem>

          <FormItem
            name="visibleScopeDepartmentId"
            label="可见范围"
            rules={[{ required: true, message: "请选择可见范围" }]}
          >
            <ETypeTransferModal title="可见范围" onlyDepartment />
          </FormItem>

          <FormItem
            name="skipVerify"
            label="添加设置"
            valuePropName="checked"
            extra="开启后，客户添加时无需经过确认自动成为好友"
          >
            <Switch onChange={onChangeSwitchSetState} />
          </FormItem>

          <FormItem
            name="leadingMessage"
            label="引导语"
            initialValue="欢迎通过以下二维码添加好友，方便为您解答问题"
            extra="建议引导语字数不超过100字"
          >
            <WibotEditor
              ref={WibotEditorRef}
              toolbarConfig={{
                toolbarKeys: ["emotion"],
              }}
              editorConfig={{
                placeholder: "请输入内容(建议100字内)...",
              }}
              onChangeHtml={(html) => {
                liveCodeform.setFieldValue("leadingMessage", html)
                setEditorMessage(html)
              }}
            />
          </FormItem>

          {/* 素材库列表表单 */}
          <FormItem
            name="msgList"
            label="欢迎语"
            wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
            // rules={[{ required: true, message: "请添加欢迎语！" }]}
          >
            <>
              <div className="WelcomeSpeech-tips">
                您的欢迎语会随上级部门欢迎语一同发，客户最多接收到前1个文案9个附件，且文案超长时会被截断
                {/* <span className='checkContents' onClick={() => { handlePreview() }}>查看上级部门欢迎语</span> */}
              </div>
              <MaterialListForm
                params={{
                  formRef: liveCodeform,
                  menuList: ["image", "material", "liveCode"],
                  needScriptFlag: true,
                  materialAmount: msgList.length,
                  previewList: previewList,
                  extra: (
                    <div>
                      请按顺序添加预计要发送的欢迎语，还可添加{" "}
                      {10 - msgList.length} 条<br />
                      选择素材后，参考话术将会自动添加在唯一的文案中
                    </div>
                  ),
                }}
                // 监听回调
                callback={(params) => {
                  setMsgList(params.data)
                }}
                ref={onRefMaterialListForm}
              />
            </>
          </FormItem>

          <div style={{ display: "flex", justifyContent: "center" }}>
            <Space size={40}>
              <Button type="primary" onClick={() => onSubmit()}>
                确定
              </Button>
            </Space>
          </div>
        </Form>
      </Col>
      <Col xs={24} lg={8}>
        <WibotMobilePreview title="活码名称">
          <div className="text">
            <Paragraph ellipsis={{ rows: 3, tooltip: true }}>
              {editorMessage ? (
                <WibotEditorView html={editorMessage} />
              ) : (
                <span>欢迎通过以下二维码添加好友，方便为您解答问题</span>
              )}
            </Paragraph>
          </div>
          <div className="qw-code">
            <img src={qwCode} alt="" />
            <p>请长按识别二维码</p>
          </div>
          <div className="phone-bot">
            <div className="bot-text">
              若无法扫码，请添加客服微信，获取最新二维码
            </div>
            <Button icon={<CustomerServiceOutlined />}>联系客服</Button>
          </div>
        </WibotMobilePreview>
        <br />
        <WibotMaterialPreview
          type="session"
          mergeFlag={true}
          listData={[...previewList, ...msgList]}
        />
      </Col>
    </Row>
  )
}

export default withRouter(StaffRecommend)
