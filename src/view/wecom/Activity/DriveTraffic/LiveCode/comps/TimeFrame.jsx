/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/26 11:03
 * @LastEditTime: 2023/11/21 11:32
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/comps/TimeFrame.jsx
 * @Description: '活码对象-分时段'
 */
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  memo,
} from "react";
import {
  Form,
  Tabs,
  Checkbox,
  TimePicker,
  Button,
  Tag,
  Table,
  Tooltip,
  Select,
  message,
} from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import moment from "moment";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import "./TimeFrame.less";

const FormItem = Form.Item;
const { TabPane } = Tabs;

const TimeFrame = forwardRef((props, ref) => {
  const {
    codeId,
    locationScope = false,
    onChooseEmp,
    handleDeptIdChange,
    changeEmployeeOptions,
  } = props;

  useImperativeHandle(ref, () => ({
    getInitObjData,
    getChooseEmpData,
    getModifyObjList,
    getChooseCurrentEmpData,
    getSubmitMessageOne,
    getSubmitMessageTwo,
    getSubmitMessageThree,
    // getSubmitMessageFour
  }));

  //默认显示一条时段
  const defaultPanes = Array.from({ length: 1 }).map((_, index) => {
    const id = String(index + 1);
    return { title: `时段${id}`, key: String(index) };
  });

  const [activeKey, setActiveKey] = useState(defaultPanes[0].key);
  const [panes, setPanes] = useState(defaultPanes);
  const [objList, setObjList] = useState([
    {
      startTime: "",
      endTime: "",
      voList: [],
      weekdayList: [],
    },
  ]);
  const [data, setData] = useState([]);
  const [formRef, setFormRef] = useState(null);
  const [locationOption, setLocationOption] = useState([]);

  const cycleOptions = [
    {
      label: "周一",
      value: 1,
    },
    {
      label: "周二",
      value: 2,
    },
    {
      label: "周三",
      value: 3,
    },
    {
      label: "周四",
      value: 4,
    },
    {
      label: "周五",
      value: 5,
    },
    {
      label: "周六",
      value: 6,
    },
    {
      label: "周日",
      value: 7,
    },
  ];
  const objColumns = [
    {
      title: "员工",
      width: "120px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <>
          {!record?.name ? (
            <Button type="primary" onClick={() => onChooseEmp()}>
              选择员工
            </Button>
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
        </>
      ),
    },
    {
      title: "关联定位",
      width: "170px",
      dataIndex: "locationId",
      key: "locationId",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Select
          placeholder="请选择定位"
          options={locationOption}
          value={value}
          allowClear
          showSearch
          disabled={true}
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          onChange={(val) => {
            getChangeLocationData(val, index);
          }}
        />
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record, index)}>删除</a>
        </>
      ),
    },
  ];
  useEffect(() => {
    setFormRef(props.formRef);
    getLocationOption();
    //获取LocalStorage存储的数据
    getLocalStorageObjList(props.formRef);
    // if (!codeId) {
    //   getLocalStorageObjList(props.formRef);
    // }
  }, []);

  //时段Tab切换改变相应的数据
  useEffect(() => {
    if (formRef) {
      let fieldData = objList[Number(activeKey)];
      formRef.setFieldsValue({
        time: fieldData?.startTime
          ? [
            moment(fieldData.startTime, "HH:mm"),
            moment(fieldData.endTime, "HH:mm"),
          ]
          : "",
        cycle: fieldData?.weekdayList,
        codeObj:
          fieldData?.voList?.length > 0
            ? fieldData?.voList.map((item) => String(item.id))
            : [],
      });
      //活码对象data
      setData(fieldData?.voList);
      //改变预览员工选项
      // changeEmployeeOptions(fieldData?.voList);
    }
  }, [activeKey]);

  // 获取活码对象员工或群组的定位
  const getLocationOption = () => {
    apiCall("/base/location/option", "GET")
      .then((res) => {
        setLocationOption(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  //删除定位授权情况下的活码对象
  const handleDelete = (value, index) => {
    const tabIdx = Number(activeKey);
    const newData = data.filter((item) => item.id != value.id);
    const newObjList = JSON.parse(JSON.stringify(objList));
    newObjList[tabIdx].voList = newData;
    setObjList([...newObjList]);
    setData([...newData]);
  };

  //获取LocalStorage存储的活码对象数据
  const getLocalStorageObjList = (formRef) => {
    const localStorageData = JSON.parse(localStorage.getItem("objList"));
    const newObjList =
      localStorageData?.length > 0
        ? localStorageData
        : [
          {
            startTime: "",
            endTime: "",
            voList: [],
            weekdayList: [],
          },
        ];
    let newPanes = [];
    newObjList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    setObjList([...newObjList]);
    setPanes(newPanes);
    let fieldData = newObjList[0];
    formRef.setFieldsValue({
      time: fieldData?.startTime
        ? [
          moment(fieldData.startTime, "HH:mm"),
          moment(fieldData.endTime, "HH:mm"),
        ]
        : "",
      cycle: fieldData?.weekdayList,
      codeObj: fieldData?.voList.map((item) => String(item.id)),
    });
    setData(fieldData?.voList);
  };

  //编辑活码时获取接口活码数据
  const getInitObjData = (obj) => {
    const { objectList } = obj;
    let newPanes = [];
    objectList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    setObjList(objectList);
    setPanes(newPanes);
    let fieldData = objectList[0];
    formRef.setFieldsValue({
      time: fieldData?.startTime
        ? [
          moment(fieldData.startTime, "HH:mm"),
          moment(fieldData.endTime, "HH:mm"),
        ]
        : "",
      cycle: fieldData?.weekdayList,
      codeObj: fieldData?.voList.map((item) => String(item.id)),
    });
    setData(fieldData?.voList);
  };

  //获取活码对象选择的员工以及员工对应的关联定位
  const getChooseEmpData = (arr) => {
    setTimeout(() => {
      let newObjList = [...objList];
      const tabIdx = Number(activeKey);
      newObjList[tabIdx].voList = [...arr];
      setObjList([...newObjList]);
      setData([...arr]);
    }, 500);
  };

  //获取活码对象的当前时段选择的当前员工
  const getChooseCurrentEmpData = () => {
    if (objList.length > 0) {
      const tabIdx = Number(activeKey);
      return objList[tabIdx].voList;
    } else {
      return [];
    }
  };

  //获取授权定位情况下选择的关联定位
  const getChangeLocationData = (val, idx) => {
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    let loData = [...data];
    newObjList[tabIdx].voList[idx].locationId = val;
    loData[idx].locationId = val;
    setObjList([...newObjList]);
    setData([...loData]);
  };

  const onChange = (key) => {
    setActiveKey(key);
  };

  const onEdit = (targetKey, action) => {
    if (action === "add") {
      handleAdd();
    } else {
      remove(targetKey);
    }
  };

  //新增时段
  const handleAdd = () => {
    const num = panes.length + 1;
    const newPanes = Array.from({ length: num }).map((_, index) => {
      const id = String(index + 1);
      return { title: `时段${id}`, key: String(index) };
    });
    setPanes([...newPanes]);
    let newObjList = [...objList];
    newObjList.push({
      endTime: "",
      startTime: "",
      voList: [],
    });
    setObjList(newObjList);
    setActiveKey(String(panes.length));
  };

  //删除时段
  const remove = (targetKey) => {
    const targetIndex = panes.findIndex((pane) => pane.key === targetKey);
    const newPanes = panes.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeKey) {
      const { key } =
        newPanes[
        targetIndex === newPanes.length ? targetIndex - 1 : targetIndex
        ];
      setActiveKey(key);
    } else {
      if (activeKey == "0") {
        setActiveKey("0");
      } else {
        const newActiveKey = Number(activeKey) - 1;
        setActiveKey(String(newActiveKey));
      }
    }
    const newObjList = JSON.parse(JSON.stringify(objList));
    newObjList.splice(targetIndex, 1);
    setPanes(
      newPanes.map((item, index) => ({
        title: `时段${index + 1}`,
        key: String(index),
      }))
    );
    setObjList(newObjList);
  };

  //选择周期改变
  const onCycleChange = (checkedValues) => {
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].weekdayList = checkedValues;
    setObjList([...newObjList]);
  };

  //选择时段改变
  const onTimeChange = (time) => {
    const startTime = time ? moment(time[0]._d).format("HH:mm") : "";
    const endTime = time ? moment(time[1]._d).format("HH:mm") : "";
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].startTime = startTime;
    newObjList[tabIdx].endTime = endTime;
    setObjList([...newObjList]);
  };

  //监听活码对象数据变化改变对应的本地存储数据
  useEffect(() => {
    setTimeout(() => {
      localStorage.setItem("objList", JSON.stringify(objList));
      let list = [];
      objList?.forEach((item) => {
        item.voList.forEach((atem) => {
          list.push(atem);
        });
      });
      //list去重
      for (let i = 0; i < list.length; i++) {
        for (let j = i + 1; j < list.length; j++) {
          if (list[i].id == list[j].id) {
            list.splice(j, 1);
            j--;
          }
        }
      }
      changeEmployeeOptions(list);
    }, 500);
  }, [objList]);

  //删除活码对象选择的相应员工
  const handleEmpClose = (e, value, idx) => {
    e.preventDefault();
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].voList = newObjList[tabIdx].voList.filter(
      (item) => item.id != value.id
    );
    const newData = data.filter((item) => item.id != value.id);
    setObjList([...newObjList]);
    setData(newData.length > 0 ? [...newData] : []);
  };

  //选择时段-左
  const handleLeft = () => {
    if (activeKey == "0") {
      setActiveKey(String(panes.length - 1));
    } else {
      const newActiveKey = Number(activeKey) - 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //选择时段-右
  const handleRight = () => {
    if (activeKey == panes.length - 1) {
      setActiveKey("0");
    } else {
      const newActiveKey = Number(activeKey) + 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //提交时获取修改后的所有活码对象数据
  const getModifyObjList = () => {
    const newObjList = objList.map((item, index) => {
      if (
        !item.startTime &&
        !item.endTime &&
        !item.weekdayList?.length > 0 &&
        !item.voList?.length > 0
      ) {
        return null;
      } else {
        return {
          startTime: item.startTime,
          endTime: item.endTime,
          voList: item.voList,
          weekdayList: item.weekdayList,
        };
      }
    });
    return newObjList.filter((item) => item);
  };

  //提交时逻辑判断
  const getSubmitMessageOne = () => {
    let flag = false;
    if (!objList.every((item) => item.voList.length > 0)) {
      message.error("活码对象员工不能为空！");
      flag = true;
    }
    return flag;
  };
  const getSubmitMessageTwo = () => {
    let flag = false;
    if (locationScope) {
      for (let i = 0; i < objList.length; i++) {
        const locationFlag = objList[i].voList.every((item) => item.locationId);
        if (!locationFlag) {
          message.error("关联定位不能为空！");
          flag = true;
        }
      }
    }
    return flag;
  };

  const getSubmitMessageThree = () => {
    let flag = false;
    if (!objList.every((item) => item.startTime)) {
      message.error("选择时段不能为空！");
      flag = true;
    }
    return flag;
  };

  const getSubmitMessageFour = () => {
    let flag = false;
    console.log(objList);
    // const empIds = objList.map((item) => Number(item.id)).sort();
    // for (let i = 0; i < empIds.length; i++) {
    //   if (empIds[i] == empIds[i + 1]) {
    //     message.error('活码对象不能重复！');
    //     flag = true;
    //     break
    //   }
    // }
    return true;
  }

  return (
    <div className="timeFrame">
      <Tabs
        hideAdd
        onChange={onChange}
        activeKey={activeKey}
        type="editable-card"
        onEdit={onEdit}
        tabBarExtraContent={
          panes.length > 3 ? (
            {
              left: <LeftOutlined className="left-icon" onClick={handleLeft} />,
              right: (
                <div>
                  <RightOutlined className="right-icon" onClick={handleRight} />
                  <div className="add-btn" onClick={handleAdd}>
                    添加时段
                  </div>
                </div>
              ),
            }
          ) : (
            <div className="add-btn" onClick={handleAdd}>
              添加时段
            </div>
          )
        }
      >
        {panes.map((pane) => (
          <TabPane
            tab={pane.title}
            key={pane.key}
            closable={panes.length != 1}
          ></TabPane>
        ))}
      </Tabs>
      <div className="cycle_bg">
        <FormItem label="选择周期" name="cycle">
          <Checkbox.Group options={cycleOptions} onChange={onCycleChange} />
        </FormItem>
        <FormItem
          name="time"
          label="选择时段"
          rules={[{ required: true, message: "请选择时段！" }]}
        >
          <TimePicker.RangePicker format={"HH:mm"} onChange={onTimeChange} />
        </FormItem>
        <FormItem
          name="codeObj"
          label="选择员工"
        >
          {locationScope ? (
            <>
              <Button type="primary" onClick={onChooseEmp}>
                选择员工
              </Button>
              <Table
                rowKey="id"
                dataSource={data}
                columns={objColumns}
                pagination={false}
              />
            </>
          ) : (
            // <Button type="primary" onClick={onChooseEmp}>选择员工</Button>
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee={true}
              multiple={true}
              onChange={(value, options) => {
                onChooseEmp(value, options);
                handleDeptIdChange(value[0].id);
              }}
            />
          )}
        </FormItem>
      </div>
    </div>
  );
});
export default memo(TimeFrame);
