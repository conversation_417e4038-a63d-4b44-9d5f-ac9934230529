/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/26 11:03
 * @LastEditTime: 2023/05/08 17:53
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/comps/TimeGroupFrame.jsx
 * @Description: '群活码对象-分时段'
 */

import React, { useState, useImperativeHandle, forwardRef, useEffect } from 'react';
import { Form, Tabs, Checkbox, TimePicker, Button, Table, message } from 'antd';
import moment from 'moment';
import { PlusOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import CTypeTransferModal from 'components/TransferModal/CustomerType/home';
import { apiCall } from "common/utils";
import './TimeGroupFrame.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;

const TimeGroupFrame = forwardRef((props, ref) => {
  const { dataSource, columns, onAdd, setGroupData } = props;
  const [loading, setLoading] = useState(false);
  const [CTypeTransferParams, setCTypeTransferParams] = useState({ visible: false });
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });

  useImperativeHandle(ref, () => ({
    getInitObjData,
    getGroupObjData,
    getModifyObjList,
    handleGroupLocastorage,
    handleGroupLocationLocastorage,
    getSubmitMessageOne,
  }))

  //默认显示一条时段
  const defaultPanes = Array.from({ length: 1 }).map((_, index) => {
    const id = String(index + 1);
    return { title: `时段${id}`, key: String(index) };
  });

  const [activeKey, setActiveKey] = useState(defaultPanes[0].key);
  const [panes, setPanes] = useState(defaultPanes);
  const [objList, setObjList] = useState([{
    startTime: '',
    endTime: '',
    voList: [],
    weekdayList: []
  }]);
  const [formRef, setFormRef] = useState(null);
  const cycleOptions = [
    {
      label: '周一', value: 1
    },
    {
      label: '周二', value: 2
    },
    {
      label: '周三', value: 3
    },
    {
      label: '周四', value: 4
    },
    {
      label: '周五', value: 5
    }, {
      label: '周六', value: 6
    },
    {
      label: '周日', value: 7
    }
  ];

  useEffect(() => {
    setFormRef(props.formRef);
    //获取LocalStorage存储的数据
    getLocalStorageObjList(props.formRef)
  }, []);

  //时段Tab切换改变相应的数据
  useEffect(() => {
    if (formRef) {
      let fieldData = objList[Number(activeKey)];
      formRef.setFieldsValue({
        time: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
        cycle: fieldData?.weekdayList
      });
      setGroupData(fieldData?.voList);
    }
  }, [activeKey]);

  //获取LocalStorage存储的活码对象数据
  const getLocalStorageObjList = (formRef) => {
    const localStorageData = JSON.parse(localStorage.getItem("objGroupList"));
    const newObjList = localStorageData?.length > 0 ? localStorageData : [{
      startTime: '',
      endTime: '',
      voList: [],
      weekdayList: []
    }];
    let newPanes = [];
    newObjList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    console.log(newPanes, newObjList, 'newPanesnewPanes');
    setObjList([...newObjList]);
    setPanes(newPanes);
    let fieldData = newObjList[0];
    setGroupData(newObjList[0].voList);//设置父组件中的groupDataSource
    formRef.setFieldsValue({
      time: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
      cycle: fieldData?.weekdayList,
      codeObj: fieldData?.voList
    });
  }

  //选择群组变化时改变相应的数据
  const handleGroupLocastorage = (list, val, idx) => {
    const tabIdx = Number(activeKey);
    let newObjList = JSON.parse(JSON.stringify(objList));
    newObjList[tabIdx].voList[idx].id = val;
    newObjList[tabIdx].voList.forEach(item => {
      item.id = item.addId || item.id;
    })
    setObjList([...newObjList]);
  }

  //选择定位改变时改变相应的数据
  const handleGroupLocationLocastorage = (list, val, idx) => {
    const tabIdx = Number(activeKey);
    let newObjList = JSON.parse(JSON.stringify(objList))
    newObjList[tabIdx].voList[idx].locationId = val;
    setObjList([...newObjList]);
  }

  //编辑活码时获取接口活码数据
  const getInitObjData = (obj) => {
    const { objectList } = obj;
    const newObjList = [...objectList];
    let newPanes = [];
    newObjList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    setObjList(newObjList);
    setPanes(newPanes);
    let fieldData = newObjList[0];
    formRef.setFieldsValue({
      time: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
      cycle: fieldData?.weekdayList,
      codeObj: fieldData?.voList
    });
  }

  //添加或者删除群组时改变存储的数据
  const getGroupObjData = (arr) => {
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].voList = [...arr];
    setObjList([...newObjList]);
  }

  //选择周期改变
  const onCycleChange = (checkedValues) => {
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].weekdayList = checkedValues;
    setObjList([...newObjList]);
  };

  //选择时段改变
  const onTimeChange = (time) => {
    const startTime = time ? moment(time[0]._d).format('HH:mm') : '';
    const endTime = time ? moment(time[1]._d).format('HH:mm') : '';
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].startTime = startTime;
    newObjList[tabIdx].endTime = endTime;
    setObjList([...newObjList]);
  }

  //监听活码对象数据变化改变对应的本地存储数据
  useEffect(() => {
    localStorage.setItem("objGroupList", JSON.stringify([...objList]))
  }, [objList]);

  const onChange = (key) => {
    setActiveKey(key);
  };

  const onEdit = (targetKey, action) => {
    console.log(action, 'action');
    if (action === 'add') {
      handleAdd();
    } else {
      remove(targetKey);
    }
  };

  //添加时段
  const handleAdd = () => {
    const num = panes.length + 1;
    const newPanes = Array.from({ length: num }).map((_, index) => {
      const id = String(index + 1);
      return { title: `时段${id}`, key: String(index) };
    });
    setPanes([...newPanes]);
    let newObjList = [...objList];
    newObjList.push({
      endTime: "",
      startTime: "",
      voList: []
    });
    setObjList(newObjList);
    setActiveKey(String(panes.length));
  };

  //删除时段
  const remove = (targetKey) => {
    const targetIndex = panes.findIndex((pane) => pane.key === targetKey);
    const newPanes = panes.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeKey) {
      const { key } = newPanes[targetIndex === newPanes.length ? targetIndex - 1 : targetIndex];
      setActiveKey(key);
    } else {
      if (activeKey == '0') {
        setActiveKey('0');
      } else {
        const newActiveKey = Number(activeKey) - 1;
        setActiveKey(String(newActiveKey));
      }
    }
    const newObjList = JSON.parse(JSON.stringify(objList));
    newObjList.splice(targetIndex, 1);
    setPanes(newPanes.map((item, index) => ({
      title: `时段${index + 1}`,
      key: String(index)
    })));
    setObjList(newObjList);
  };

  //选择时段-左
  const handleLeft = () => {
    console.log(activeKey, panes);
    if (activeKey == '0') {
      setActiveKey(String(panes.length - 1));
    } else {
      const newActiveKey = Number(activeKey) - 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //选择时段-右
  const handleRight = () => {
    if (activeKey == panes.length - 1) {
      setActiveKey('0');
    } else {
      const newActiveKey = Number(activeKey) + 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //提交时获取修改后的所有活码对象数据
  const getModifyObjList = () => {
    const newObjList = objList.map((item, index) => {
      if (!item.startTime && !item.endTime && !item.weekdayList?.length > 0 && !item.voList?.length > 0) {
        return null;
      } else {
        item.voList.forEach(atem => atem.id = atem.addId || atem.id);
        console.log(item.voList);
        return {
          startTime: item.startTime,
          endTime: item.endTime,
          voList: item.voList,
          weekdayList: item.weekdayList
        }
      }
    })
    return newObjList.filter(item => item);
  }

  //提交时逻辑判断
  const getSubmitMessageOne = () => {
    let flag = false;
    if (!objList.every(item => item.startTime)) {
      message.error('选择时段不能为空！');
      flag = true
    }
    return flag;
  }

  return (
    <div className="timeGroupFrame">
      <Tabs
        hideAdd
        onChange={onChange}
        activeKey={activeKey}
        type="editable-card"
        onEdit={onEdit}
        tabBarExtraContent={panes.length > 3 ? {
          left: <LeftOutlined className='left-icon' onClick={handleLeft} />,
          right: <div>
            <RightOutlined className='right-icon' onClick={handleRight} />
            <div className='add-btn' onClick={handleAdd}>添加时段</div>
          </div>,
        } : <div className='add-btn' onClick={handleAdd}>添加时段</div>}
      >
        {panes.map((pane) => (
          <TabPane tab={pane.title} key={pane.key} closable={panes.length != 1}>
          </TabPane>
        ))}
      </Tabs>
      <div className='cycle_bg'>
        <FormItem label="选择周期" name="cycle">
          <Checkbox.Group options={cycleOptions} onChange={onCycleChange} />
        </FormItem>
        <FormItem name="time" label="选择时段" rules={[{ required: true, message: '请选择时段！' }]}>
          <TimePicker.RangePicker format={'HH:mm'} onChange={onTimeChange} />
        </FormItem>
        <FormItem name="groupIds" label="选择群组">
          {/* <Button type="primary" onClick={onAdd}>群组</Button>
          <Table rowKey="id" dataSource={dataSource} columns={columns} pagination={false} /> */}

          <CTypeTransferModal {...CTypeTransferParams} />
          <Button icon={<PlusOutlined />} type="primary" onClick={() => {
            setCTypeTransferParams({
              visible: true,
              type: 'group',
              checkList: dataSource,
              onSubmit: (data) => {
                onAdd(data);
                setPaginations({
                  current: 1,
                  pageSize: 10,
                  total: data.length,
                  showQuickJumper: true,
                  showSizeChanger: true,
                  showTotal: (total, range) => `共 ${total} 条记录`,
                });
                setCTypeTransferParams({ visible: false });
              },
              onCancel: () => {
                setCTypeTransferParams({ visible: false });
              }
            });
          }}>选择客户群</Button>
          <Table rowKey="key" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 600 }}
            pagination={paginations} onChange={(pagination, filters, sorter) => {
              setPaginations({
                ...pagination,
                showTotal: (total, range) => `共 ${total} 条记录`,
              });
            }} />
        </FormItem>
      </div>
    </div >
  );
});
export default TimeGroupFrame;
