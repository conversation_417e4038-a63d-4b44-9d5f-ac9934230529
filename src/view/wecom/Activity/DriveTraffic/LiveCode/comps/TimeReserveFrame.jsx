/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/26 11:03
 * @LastEditTime: 2023/02/13 20:13
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\DriveTraffic\LiveCode\comps\TimeReserveFrame.jsx
 * @Description: '活码对象-分时段'
 */
import React, { useState, useImperativeHandle, forwardRef, useEffect } from 'react';
import { Form, Tabs, Checkbox, TimePicker, Button, Tag } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './TimeReserveFrame.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;

const TimeReserveFrame = forwardRef((props, ref) => {
  const { codeId, locationScope = false, onChooseEmp } = props;

  useImperativeHandle(ref, () => ({
    getInitObjData,
    getModifyObjList,
    getChooseReserveEmpData,
    getChooseCurrentEmpData,
  }))

  //默认显示一条时段
  const defaultPanes = Array.from({ length: 1 }).map((_, index) => {
    const id = String(index + 1);
    return { title: `时段${id}`, key: String(index) };
  });

  const [activeKey, setActiveKey] = useState(defaultPanes[0].key);
  const [panes, setPanes] = useState(defaultPanes);
  const [objList, setObjList] = useState([{
    startTime: '',
    endTime: '',
    voList: [],
    weekdayList: []
  }]);
  const [data, setData] = useState([]);
  const [formRef, setFormRef] = useState(null);

  const cycleOptions = [
    {
      label: '周一', value: 1
    },
    {
      label: '周二', value: 2
    },
    {
      label: '周三', value: 3
    },
    {
      label: '周四', value: 4
    },
    {
      label: '周五', value: 5
    }, {
      label: '周六', value: 6
    },
    {
      label: '周日', value: 7
    }
  ];

  useEffect(() => {
    setFormRef(props.formRef);
    //获取LocalStorage存储的数据
    getLocalStorageObjList(props.formRef)
  }, []);

  //时段Tab切换改变相应的数据
  useEffect(() => {
    if (formRef) {
      let fieldData = objList[Number(activeKey)];
      formRef.setFieldsValue({
        timereserve: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
        cyclereserve: fieldData?.weekdayList,
        reserve: fieldData?.voList?.length > 0 ? fieldData?.voList.map((item) => String(item.id)) : []
      });
      //备用员工data
      setData(fieldData?.voList);
    }
  }, [activeKey]);

  //获取LocalStorage存储的备用员工相应的数据
  const getLocalStorageObjList = (formRef) => {
    const newObjList = JSON.parse(localStorage.getItem("objReserveList")) ?? [{
      startTime: '',
      endTime: '',
      voList: [],
      weekdayList: []
    }];
    let newPanes = [];
    newObjList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    setObjList([...newObjList]);
    setPanes(newPanes);
    let fieldData = newObjList[0];
    formRef.setFieldsValue({
      timereserve: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
      cyclereserve: fieldData?.weekdayList,
      reserve: fieldData?.voList.map((item) => String(item.id))
    });
    setData(fieldData?.voList);
  }

  //编辑活码时获取接口数据
  const getInitObjData = (obj) => {
    const { objectList } = obj;
    let newPanes = [];
    objectList?.forEach((item, index) => {
      newPanes.push({ title: `时段${index + 1}`, key: String(index) });
    });
    setObjList(objectList);
    setPanes(newPanes);
    let fieldData = objectList[0];
    formRef.setFieldsValue({
      timereserve: fieldData?.startTime ? [moment(fieldData.startTime, 'HH:mm'), moment(fieldData.endTime, 'HH:mm')] : '',
      cyclereserve: fieldData?.weekdayList,
      reserve: fieldData?.voList.map((item) => String(item.id))
    });
    setData(fieldData?.voList);
    // localStorage.setItem("objReserveList", JSON.stringify(objectList));
  }

  //获取选择的备用员工数据
  const getChooseReserveEmpData = (arr) => {
    let newObjList = [...objList];
    // arr.forEach((item) => {
    //   item.id = item.key;
    // });
    const tabIdx = Number(activeKey);
    newObjList[tabIdx].voList = [...arr];
    setObjList([...newObjList]);
    setData([...arr]);
  }

  //获取备用员工的当前时段选择的当前员工
  const getChooseCurrentEmpData = () => {
    if (objList.length > 0) {
      const tabIdx = Number(activeKey);
      return objList[tabIdx].voList;
    } else {
      return [];
    }
  }

  const onChange = (key) => {
    console.log(key, 'key');
    setActiveKey(key);
  };

  const onEdit = (targetKey, action) => {
    if (action === 'add') {
      handleAdd();
    } else {
      remove(targetKey);
    }
  };

  //新增时段
  const handleAdd = () => {
    const num = panes.length + 1;
    const newPanes = Array.from({ length: num }).map((_, index) => {
      const id = String(index + 1);
      return { title: `时段${id}`, key: String(index) };
    });
    setPanes([...newPanes]);
    let newObjList = [...objList];
    newObjList.push({
      endTime: "",
      startTime: "",
      voList: []
    });
    setObjList(newObjList);
    setActiveKey(String(panes.length));
  };

  //删除时段
  const remove = (targetKey) => {
    const targetIndex = panes.findIndex((pane) => pane.key === targetKey);
    const newPanes = panes.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeKey) {
      const { key } = newPanes[targetIndex === newPanes.length ? targetIndex - 1 : targetIndex];
      setActiveKey(key);
    } else {
      if (activeKey == '0') {
        setActiveKey('0');
      } else {
        const newActiveKey = Number(activeKey) - 1;
        setActiveKey(String(newActiveKey));
      }
    }
    const newObjList = JSON.parse(JSON.stringify(objList));
    newObjList.splice(targetIndex, 1);
    setPanes(newPanes.map((item, index) => ({
      title: `时段${index + 1}`,
      key: String(index)
    })));
    setObjList(newObjList);
  };

  //选择周期改变
  const onCycleChange = (checkedValues) => {
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].weekdayList = checkedValues;
    setObjList([...newObjList]);
  };

  //选择时段改变
  const onTimeChange = (time) => {
    const startTime = time ? moment(time[0]._d).format('HH:mm') : '';
    const endTime = time ? moment(time[1]._d).format('HH:mm') : '';
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].startTime = startTime;
    newObjList[tabIdx].endTime = endTime;
    setObjList([...newObjList]);
  }

  //监听备用员工相应数据变化改变对应的本地存储数据
  useEffect(() => {
    localStorage.setItem("objReserveList", JSON.stringify(objList))
  }, [objList]);

  //删除备用员工选择的相应员工
  const handleEmpClose = (e, value, idx) => {
    e.preventDefault();
    const tabIdx = Number(activeKey);
    let newObjList = [...objList];
    newObjList[tabIdx].voList = newObjList[tabIdx].voList.filter((item) => item.id != value.id);
    const newData = data.filter((item) => item.id != value.id);
    setObjList([...newObjList]);
    setData(newData.length > 0 ? [...newData] : []);
  }

  //选择时段-左
  const handleLeft = () => {
    console.log(activeKey, panes);
    if (activeKey == '0') {
      setActiveKey(String(panes.length - 1));
    } else {
      const newActiveKey = Number(activeKey) - 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //选择时段-右
  const handleRight = () => {
    if (activeKey == panes.length - 1) {
      setActiveKey('0');
    } else {
      const newActiveKey = Number(activeKey) + 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //提交时获取修改后的所有备用员工数据
  const getModifyObjList = () => {
    const newObjList = objList.map((item, index) => {
      if (!item.startTime && !item.endTime && !item.weekdayList?.length > 0 && !item.voList?.length > 0) {
        return null;
      } else {
        return {
          startTime: item.startTime,
          endTime: item.endTime,
          voList: item.voList,
          weekdayList: item.weekdayList
        }
      }
    })
    return newObjList.filter(item => item);
  }

  //提交时逻辑判断
  // const getSubmitMessage = () => {
  //   let flag = false;
  //   if (!objList.every(item => item.voList.length > 0)) {
  //     message.error('备用员工不能为空！');
  //     flag = true
  //   }
  //   return flag;
  // }

  return (
    <div className="timeFrame">
      <Tabs hideAdd
        onChange={onChange}
        activeKey={activeKey}
        type="editable-card"
        onEdit={onEdit}
        tabBarExtraContent={panes.length > 3 ? {
          left: <LeftOutlined className='left-icon' onClick={handleLeft} />,
          right: <div>
            <RightOutlined className='right-icon' onClick={handleRight} />
            <div className='add-btn' onClick={handleAdd}>添加时段</div>
          </div>,
        } : <div className='add-btn' onClick={handleAdd}>添加时段</div>}
      >
        {panes.map((pane) => (
          <TabPane tab={pane.title} key={pane.key} closable={panes.length != 1}>
          </TabPane>
        ))}
      </Tabs>
      <div className='cycle_bg'>
        <FormItem label="选择周期" name='cyclereserve'>
          <Checkbox.Group options={cycleOptions} onChange={onCycleChange} />
        </FormItem>
        <FormItem name='timereserve' label="选择时段" >
          <TimePicker.RangePicker format={'HH:mm'} onChange={onTimeChange} />
        </FormItem>
        <FormItem name="reserve" label="选择员工"
        //   extra={
        //   !locationScope ? <div>
        //     {/* 员工对象列表 */}
        //     {
        //       data?.length > 0 && data.map((item, index) => <Tag
        //         closable
        //         style={{ marginTop: '5px' }}
        //         key={index}
        //         onClose={(e) => handleEmpClose(e, item, index)}
        //       >
        //         {item.name}
        //       </Tag>)
        //     }
        //   </div> : ''
        // }
        >
          {/* <Button type="primary" onClick={onChooseEmp}>选择员工</Button> */}
          <ETypeTransferModal title="选择员工" onlyEmployee={true} multiple={false} onChange={(value, options) => {
            onChooseEmp(value, options)
          }} />
        </FormItem>

      </div>
    </div >
  );
});
export default TimeReserveFrame;
