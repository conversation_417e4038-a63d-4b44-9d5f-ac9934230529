.timeFrame {
  width: 500px;

  // .tabBar-box {
  //   .tab-bar {
  //     display: inline-block;
  //     // width: 65px;
  //     // height: 40px;
  //     text-align: center;
  //     // line-height: 40px;

  //     &.tab-bar_active {
  //       background: #f2f2f2;
  //     }
  //   }
  // }

  .cycle_bg {
    background: #f2f2f2;
    padding: 10px;
  }

  .ant-tabs>.ant-tabs-nav {
    margin-bottom: 0px;
  }

  .left-icon {
    position: absolute;
    left: -20px;
    z-index: 99;
    cursor: pointer;
    top: 14px;
  }

  .right-icon {
    cursor: pointer;
  }

  .add-btn {
    display: inline-block;
    color: #5d8dd4;
    margin-left: 20px;
    cursor: pointer;
  }
}