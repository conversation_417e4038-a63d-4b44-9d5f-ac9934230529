/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/11 15:25
 * @LastEditTime: 2024/11/14 17:10
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/details/BaseInfo.jsx
 * @Description: '活码数据详情基础信息'
 */

import React, { useEffect, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Spin,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Tag,
  Descriptions,
  Space,
  Image,
  Tabs,
  Form,
  DatePicker,
  Select,
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { timeStamp, getDay } from "common/date";
import moment from "moment";
import { QrCodeBase } from "common/qrcode";
import { saveAs } from "file-saver";
import { qs2obj } from "common/object";
import WibotCopyBtn from 'components/WibotCopyBtn/home';
import { Line } from '@ant-design/plots';
import "./home.less";
import {FileHOC} from 'components/FileHOC/FileHOC';
const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const BaseInfo = (props) => {
  const [id, setId] = useState(null);
  const [type, setType] = useState("EMPLOYEE");
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [tabsTrendType, setTabsTrendType] = useState("NEW_CUSTOMER_COUNT");
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState("1");
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [chartsData, setChartsData] = useState(null);
  const visitColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "添加员工",
      width: "180px",
      dataIndex: "objectName",
      key: "objectName",
      align: "center",
      render: (value, record, index) => {
        // const companyName = <span style={record.type == '微信用户' ? { color: '#07c160' } : { color: '#f59a23' }}>{record.departmentName}</span>;
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return record.employeeAvatar && value && record.departmentName ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    // {
    //   title: '关联定位',
    //   width: '160px',
    //   dataIndex: 'locationName',
    //   key: 'locationName',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const staffColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.employeeAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? <Tooltip placement="topLeft" title={title}>{content}</Tooltip> : "";
      },
    },
    // {
    //   title: '关联定位',
    //   width: '160px',
    //   dataIndex: 'locationName',
    //   key: 'locationName',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    {
      title: "今日分享次数",
      width: "160px",
      dataIndex: "todayShareCount",
      key: "todayShareCount",
      align: "center",
    },
    {
      title: "累计分享次数",
      width: "160px",
      dataIndex: "shareCount",
      key: "shareCount",
      align: "center",
    },
    {
      title: "今日新增",
      width: "160px",
      dataIndex: "todayCustomerCount",
      key: "todayCustomerCount",
      align: "center",
    },
    {
      title: "累计新增",
      width: "160px",
      dataIndex: "customerCount",
      key: "customerCount",
      align: "center",
    },
  ];
  const clientColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "添加员工",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return record.employeeAvatar && value && record.departmentName ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    // {
    //   title: '关联定位',
    //   width: '160px',
    //   dataIndex: 'locationName',
    //   key: 'locationName',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    {
      title: "添加时间",
      width: "160px",
      dataIndex: "addCustomerTime",
      key: "addCustomerTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.addCustomerTime) - timeStamp(b.addCustomerTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const dateColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "日期",
      width: "160px",
      dataIndex: "date",
      key: "date",
      align: "center",
      sorter: (a, b) => timeStamp(a.date) - timeStamp(b.date),
    },
    {
      title: "新增客户",
      width: "160px",
      dataIndex: "customerCount",
      key: "customerCount",
      align: "center",
    },
    {
      title: "分享次数",
      width: "160px",
      dataIndex: "shareCount",
      key: "shareCount",
      align: "center",
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "访问人数",
      width: "160px",
      dataIndex: "visitCustomerCount",
      key: "visitCustomerCount",
      align: "center",
    },
  ];
  const locationColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "新增客户",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
    {
      title: "访问人数",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
  ];
  const visitGroupColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "locationName",
      key: "locationName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const groupColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "群组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "群组组织",
      width: "160px",
      dataIndex: "departmentName",
      key: "departmentName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "群主",
      width: "160px",
      dataIndex: "groupLeaderName",
      key: "groupLeaderName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "locationName",
      key: "locationName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "今日进群人数",
      width: "160px",
      dataIndex: "todayAddGroupCount",
      key: "todayAddGroupCount",
      align: "center",
    },
    {
      title: "累计进群人数",
      width: "160px",
      dataIndex: "addGroupCount",
      key: "addGroupCount",
      align: "center",
    },
  ];
  const groupClientColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "群组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "群组组织",
      width: "160px",
      dataIndex: "departmentName",
      key: "departmentName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "locationName",
      key: "locationName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "进群时间",
      width: "160px",
      dataIndex: "addTime",
      key: "addTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.addTime) - timeStamp(b.addTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const groupDateColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "日期",
      width: "160px",
      dataIndex: "date",
      key: "date",
      align: "center",
      sorter: (a, b) => timeStamp(a.date) - timeStamp(b.date),
    },
    {
      title: "新增客户",
      width: "160px",
      dataIndex: "customerCount",
      key: "customerCount",
      align: "center",
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "访问人数",
      width: "160px",
      dataIndex: "visitCustomerCount",
      key: "visitCustomerCount",
      align: "center",
    },
  ];
  const groupLocationColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "进群客户",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
    {
      title: "访问人数",
      width: "160px",
      dataIndex: "entryMode",
      key: "entryMode",
      align: "center",
    },
  ];

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search);
    if (id) {
      setType(type);
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ id });
    await getTabEcharts({ tabType: "NEW_CUSTOMER_COUNT", detailId: id });
    await getTabsDataDetails({ detailId: id, tabIdx: "1" });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    const data = {
      id,
    };
    await apiCall("/activity/dynamicCode/getDetail", "GET", data)
      .then((res) => {
        const data = { ...res };
        data.locationNames =
          data.locationNames &&
          data.locationNames.join(",").replace(/,/g, "；");
        setUserInfo(data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabsDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, detailId, tabIdx } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      id: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    let apiUrl = "";
    switch (tabIdx) {
      case "1":
        apiUrl =
          type == "EMPLOYEE"
            ? "/activity/dynamicCode/employee/data"
            : "/activity/dynamicCode/group/data";
        break;
      case "2":
        apiUrl =
          type == "EMPLOYEE"
            ? "/activity/dynamicCode/employee/employee"
            : "/activity/dynamicCode/group/group";
        break;
      case "3":
        apiUrl =
          type == "EMPLOYEE"
            ? "/activity/dynamicCode/employee/customer"
            : "/activity/dynamicCode/group/customer";
        break;
      case "4":
        apiUrl =
          type == "EMPLOYEE"
            ? "/activity/dynamicCode/employee/date"
            : "/activity/dynamicCode/group/date";
        break;
      case "5":
        apiUrl = "/activity/dynamicCode/employee/date";
        break;
      default:
        break;
    }
    apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, pages, size, total } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabEcharts = async (params = {}) => {
    setLoading(true);
    const { tabType, detailId, time } = params;
    const data = {
      id: detailId || id,
      type: tabType || tabsTrendType,
      startDate:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format("YYYY-MM-DD"),
      endDate:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format("YYYY-MM-DD"),
    };
    apiCall("/activity/dynamicCode/chart", "GET", data)
      .then((res) => {
        const { list } = res;
        setChartsData(list)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customerId}`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    getTabsDataDetails({ pagination, tabIdx: tabsDetailsIndex });
  };

  const handleDownloadImg = (value) => {
    saveAs(QrCodeBase({ url: userInfo.url }), "活码");
  };

  const onChangeTabsDataTrend = (type) => {
    const time = formForm.getFieldValue("time");
    setTabsTrendType(type);
    getTabEcharts({
      tabType: type,
      time: time && [
        time[0].format("YYYY-MM-DD"),
        time[1].format("YYYY-MM-DD"),
      ],
    });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
      case "0":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-1":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-7":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-15":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-30":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
    }
    if (value) {
      formForm.setFieldsValue({ time });
    }
  };

  const onChangeTime = async (date, dateString) => {
    getTabEcharts({ time: date ? dateString : null });
    formForm.setFieldsValue({
      quickTime: null,
    });
  };

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index);
    getTabsDataDetails({
      tabIdx: index,
      pagination: { current: 1, pageSize: 10 },
    });
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <div className="BaseInfo">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card
              title={
                <>
                  {type == "EMPLOYEE" ? "员工活码数据" : "客户群活码数据"}
                  （基础信息）
                </>
              }
            >
              {JSON.stringify(userInfo) != "{}" ? (
                <>
                  <Row gutter={16} className="header-box">
                    <Col xs={24} xl={18} className="info">
                      <div className="groupName">{userInfo.name}</div>
                      <Space wrap>
                        <span>活码对象:</span>
                        {userInfo.objectCodes.map((item, index) => (
                          <div className="avatar-name" key={index}>
                            <Avatar
                              shape="square"
                              size="small"
                              src={item.avatar}
                            />
                            <span style={{ verticalAlign: "middle" }}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </Space>
                      <Space wrap>
                        <span>备用客服:</span>
                        <div className="avatar-name">
                          {userInfo.reserveEmployeeName.avatar ? (
                            <Avatar
                              shape="square"
                              size="small"
                              src={userInfo.reserveEmployeeName.avatar}
                            />
                          ) : (
                            ""
                          )}
                          <span style={{ verticalAlign: "middle" }}>
                            {userInfo.reserveEmployeeName.name}
                          </span>
                        </div>
                      </Space>
                      <Space wrap>
                        <span>客户标签:</span>
                        {userInfo.tagNameList.map((item, index) => (
                          <Tag key={index + 1} color="#108ee9">
                            {item}
                          </Tag>
                        ))}
                      </Space>
                      <Descriptions column={{ xs: 1, sm: 2 }}>
                        <Descriptions.Item label="获客渠道">
                          {userInfo.channelName}
                        </Descriptions.Item>
                        {type == "GROUP" && (
                          <Descriptions.Item label="关联定位">
                            {userInfo.locationNames}
                          </Descriptions.Item>
                        )}
                        {type == "EMPLOYEE" ? (
                          <Descriptions.Item label="添加设置">
                            {userInfo.skipVerify
                              ? "自动通过好友"
                              : "手动通过好友"}
                          </Descriptions.Item>
                        ) : (
                          <Descriptions.Item label="自动建群">
                            {userInfo.autoCreateGroup
                              ? "已开启自动满员建新群"
                              : "未开启自动满员建新群"}
                          </Descriptions.Item>
                        )}
                        <Descriptions.Item label="访问授权">
                          {userInfo.avatar_scope && "头像昵称"}
                          {userInfo.avatar_scope && userInfo.location_scope
                            ? "、"
                            : ""}
                          {userInfo.location_scope && "定位权限"}
                        </Descriptions.Item>
                        <Descriptions.Item label="创建">
                          {userInfo.createEmployeeName}&nbsp;
                          {userInfo.createTime}
                        </Descriptions.Item>
                        <Descriptions.Item label="更新时间">
                          {userInfo.updateTime}
                        </Descriptions.Item>
                      </Descriptions>
                    </Col>
                    <Col span={6}>
                      <div className="codeBox">
                        <FileHOC src={QrCodeBase({ url: userInfo.url })}>
                          {(url) => (
                            <Image
                            preview={false}
                            width={120}
                            src={url}
                          />
                          )}
                        </FileHOC>
                        <div className="btn">
                          <a
                            onClick={() => {
                              handleDownloadImg(userInfo);
                            }}
                          >
                            下载活码
                          </a>
                          <WibotCopyBtn text={userInfo.url} title="复制链接" />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="数据总览" className="data-screening">
              {JSON.stringify(userInfo) != "{}" ? (
                <Row>
                  <Col span={12}>
                    <span className="num">
                      {userInfo.visitStatVO.totalCount || 0}
                    </span>
                    <Tooltip title="同一用户可访问多次">
                      <p className="tip">
                        累计访问次数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问次数：{userInfo.visitStatVO.todayCount}
                    </p>
                  </Col>
                  <Col span={12}>
                    <span className="num">
                      {userInfo.visitorStatVO.totalCount || 0}
                    </span>
                    <Tooltip title="同一用户访问多次算一次">
                      <p className="tip">
                        累计访问人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问人数：{userInfo.visitorStatVO.todayCount}
                    </p>
                  </Col>
                  {/* {
                    type == 'EMPLOYEE' ? <Col span={8}>
                      <span className='num'>{userInfo.newCustomerStatVO.totalCount || 0}</span>
                      <Tooltip title="添加员工的人数，不包含流失人数">
                        <p className='tip'>累计新增人数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日新增人数：{userInfo.newCustomerStatVO.todayCount}</p>
                    </Col> : <Col span={8}>
                      <span className='num'>{userInfo.addGroupStatVO.totalCount || 0}</span>
                      <Tooltip title="通过活码进群的人数，不包含流失人数">
                        <p className='tip'>累计进群人数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日进群：{userInfo.addGroupStatVO.todayCount || 0}</p>
                    </Col>
                  } */}
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Card title="数据趋势">
              <Tabs
                activeKey={tabsTrendType}
                destroyInactiveTabPane
                onChange={onChangeTabsDataTrend}
              >
                {type == "EMPLOYEE" ? (
                  <TabPane tab="新增人数" key="NEW_CUSTOMER_COUNT"></TabPane>
                ) : (
                  <TabPane tab="进群人数" key="NEW_CUSTOMER_COUNT"></TabPane>
                )}
                <TabPane tab="访问人数" key="VISIT_CUSTOMER_COUNT"></TabPane>
                <TabPane tab="访问次数" key="VISIT_COUNT">
                  {" "}
                </TabPane>
                {/* <TabPane tab="定位分析" key="4"></TabPane> */}
              </Tabs>
              <Form layout={"inline"} form={formForm}>
                <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                  <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                </FormItem>
                <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                  <Select
                    style={{ width: "200px" }}
                    options={[
                      {
                        label: "今天",
                        value: "0",
                      },
                      {
                        label: "昨天",
                        value: "-1",
                      },
                      {
                        label: "最近7天",
                        value: "-7",
                      },
                      {
                        label: "最近15天",
                        value: "-15",
                      },
                      {
                        label: "最近30天",
                        value: "-30",
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {chartsData && DemoLine(chartsData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
        <Card title="数据明细">
          {type == "EMPLOYEE" ? (
            <Tabs
              activeKey={tabsDetailsIndex}
              destroyInactiveTabPane
              onChange={onChangeTabsDataDetails}
            >
              <TabPane tab="访问明细" key="1">
                <Table
                  rowKey="customerId"
                  loading={loading}
                  dataSource={dataSource}
                  columns={visitColumns}
                  scroll={{ x: 1300 }}
                  pagination={paginations}
                  onChange={onChangeTable}
                />
              </TabPane>
              {/* <TabPane tab="员工" key="2">
                <Table rowKey="id" loading={loading} dataSource={dataSource} columns={staffColumns} scroll={{ x: 1300 }}
                  pagination={paginations} onChange={onChangeTable} />
              </TabPane> */}
              <TabPane tab="客户" key="3">
                <Table
                  rowKey="customerId"
                  loading={loading}
                  dataSource={dataSource}
                  columns={clientColumns}
                  scroll={{ x: 1300 }}
                  pagination={paginations}
                  onChange={onChangeTable}
                />
              </TabPane>
              {/* <TabPane tab="日期" key="4">
                <Table rowKey="id" loading={loading} dataSource={dataSource} columns={dateColumns} scroll={{ x: 1300 }}
                  pagination={paginations} onChange={onChangeTable} />
              </TabPane> */}
              {/* <TabPane tab="定位" key="5">
                <Table rowKey="id" loading={loading} dataSource={dataSource} columns={locationColumns} scroll={{ x: 1300 }}
                  pagination={paginations} onChange={onChangeTable} />
              </TabPane> */}
            </Tabs>
          ) : (
            <Tabs
              activeKey={tabsDetailsIndex}
              destroyInactiveTabPane
              onChange={onChangeTabsDataDetails}
            >
              <TabPane tab="访问明细" key="1">
                <Table
                  rowKey="id"
                  loading={loading}
                  dataSource={dataSource}
                  columns={visitGroupColumns}
                  scroll={{ x: 1300 }}
                  pagination={paginations}
                  onChange={onChangeTable}
                />
              </TabPane>
              {/* <TabPane tab="群组" key="2">
                  <Table rowKey="id" loading={loading} dataSource={dataSource} columns={groupColumns} scroll={{ x: 1300 }}
                    pagination={paginations} onChange={onChangeTable} />
                </TabPane> */}
              <TabPane tab="客户" key="3">
                <Table
                  rowKey="id"
                  loading={loading}
                  dataSource={dataSource}
                  columns={groupClientColumns}
                  scroll={{ x: 1300 }}
                  pagination={paginations}
                  onChange={onChangeTable}
                />
              </TabPane>
              {/* <TabPane tab="日期" key="4">
                  <Table rowKey="id" loading={loading} dataSource={dataSource} columns={groupDateColumns} scroll={{ x: 1300 }}
                    pagination={paginations} onChange={onChangeTable} />
                </TabPane> */}
              {/* <TabPane tab="定位" key="5">
                  <Table rowKey="id" loading={loading} dataSource={dataSource} columns={groupLocationColumns} scroll={{ x: 1300 }}
                    pagination={paginations} onChange={onChangeTable} />
                </TabPane> */}
            </Tabs>
          )}
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(BaseInfo);
