/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/24 11:09
 * @LastEditTime: 2024/10/22 09:28
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/details/RecommendBaseInfo.jsx
 * @Description: '基础信息'
 */

import React, { useEffect, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Spin,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Tag,
  Descriptions,
  Space,
  Image,
  Tabs,
  Form,
  DatePicker,
  Select,
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { timeStamp, getDay } from "common/date";
import moment from "moment";
import { QrCodeBase } from "common/qrcode";
import { saveAs } from "file-saver";
import { qs2obj } from "common/object";
import WibotTableTag from 'components/WibotTableTag/home';
import WibotCopyBtn from 'components/WibotCopyBtn/home';
import { Line } from '@ant-design/plots';
import "./BaseInfo.less";
import {FileHOC} from 'components/FileHOC/FileHOC';
const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const BaseInfo = (props) => {
  const [id, setId] = useState(null);
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [overViewData, setOverViewData] = useState(null);
  const [tabsTrendType, setTabsTrendType] = useState("RECOMMEND_CUSTOMER_COUNT");
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState("1");
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [chartsData, setChartsData] = useState(null);
  const staffColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              <div>{value}</div>
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={value} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "认领时间",
      width: "160px",
      dataIndex: "claimTime",
      key: "claimTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.claimTime) - timeStamp(b.claimTime),
    },
    {
      title: "推荐场景",
      width: "160px",
      dataIndex: "channelName",
      key: "channelName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "customerTagList",
      key: "customerTagList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value.map(item => item.name)} />,
    },
    {
      title: "今日分享次数",
      width: "160px",
      dataIndex: "shareCountToday",
      key: "shareCountToday",
      align: "center",
    },
    {
      title: "累计分享次数",
      width: "160px",
      dataIndex: "shareCount",
      key: "shareCount",
      align: "center",
    },
    {
      title: "今日推荐客户",
      width: "160px",
      dataIndex: "recommendCustomerCountToday",
      key: "recommendCustomerCountToday",
      align: "center",
    },
    {
      title: "累计推荐客户",
      width: "160px",
      dataIndex: "recommendCustomerCount",
      key: "recommendCustomerCount",
      align: "center",
    },
  ];
  const clientColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customer",
      key: "customer",
      align: "center",
      render: (value, record, index) => {
        if (value) {
          const companyName = (
            <span style={{ color: value.type == 1 ? "#07c160" : "#f59a23" }}>
              {value.companyName}
            </span>
          );
          const title = (
            <div>
              {value.name}
              {companyName}
            </div>
          );
          const content = (
            <div style={{ display: "flex" }}>
              {value.avatar && <Avatar size={40} src={value.avatar} />}
              <div
                style={{
                  marginLeft: "6px",
                  width: "120px",
                  textAlign: "left",
                  whiteSpace: "normal",
                }}
              >
                {title}
                <span>{value.realName}</span>
              </div>
            </div>
          );
          return (
            <Tooltip title={title} placement="topLeft">
              {content}
            </Tooltip>
          );
        }
      },
    },
    {
      title: "添加员工",
      width: "180px",
      dataIndex: "employee",
      key: "employee",
      align: "center",
      render: (value, record, index) => {
        if (value) {
          const title = <div>{value.name}</div>;
          const content = (
            <div style={{ display: "flex" }}>
              {value.avatar && <Avatar size={40} src={value.avatar} />}
              <div
                style={{
                  marginLeft: "6px",
                  width: "120px",
                  textAlign: "left",
                  whiteSpace: "normal",
                }}
              >
                {title}
                <span>{value.departmentName}</span>
              </div>
            </div>
          );
          return value.avatar && value.name && value.departmentName ? (
            <Tooltip title={title} placement="topLeft">
              {content}
            </Tooltip>
          ) : (
            "-"
          );
        }
      },
    },
    {
      title: "推荐员工",
      width: "180px",
      dataIndex: "recommendEmployee",
      key: "recommendEmployee",
      align: "center",
      render: (value, record, index) => {
        if (value) {
          const title = <div>{value.name}</div>;
          const content = (
            <div style={{ display: "flex" }}>
              {value.avatar && <Avatar size={40} src={value.avatar} />}
              <div
                style={{
                  marginLeft: "6px",
                  width: "120px",
                  textAlign: "left",
                  whiteSpace: "normal",
                }}
              >
                {title}
                <span>{value.departmentName}</span>
              </div>
            </div>
          );
          return value.avatar && value.name && value.departmentName ? (
            <Tooltip title={title} placement="topLeft">
              {content}
            </Tooltip>
          ) : (
            "-"
          );
        }
      },
    },
    {
      title: "推荐时间",
      width: "160px",
      dataIndex: "addTime",
      key: "addTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.addTime) - timeStamp(b.addTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "lastVisitTime",
      key: "lastVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastVisitTime) - timeStamp(b.lastVisitTime),
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ id });
    getOverViewData({ id });
    await getTabEcharts({ tabType: "RECOMMEND_CUSTOMER_COUNT", detailId: id });
    await getTabsDataDetails({ detailId: id, tabIdx: "1" });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    const data = {
      id,
    };
    await apiCall("/activity/dynamicCode/getDetail", "GET", data)
      .then((res) => {
        const data = { ...res };
        data.locationNames =
          data.locationNames &&
          data.locationNames.join(",").replace(/,/g, "；");
        setUserInfo(data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取数据总览
  const getOverViewData = async (params = {}) => {
    const { id } = params;
    const data = {
      id: id,
    };
    apiCall("/activity/dynamicCode/recommend/data/overview", "GET", data)
      .then((res) => {
        setOverViewData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getTabsDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, detailId, tabIdx } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      id: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    let apiUrl = "";
    switch (tabIdx) {
      case "1":
        apiUrl = "/activity/dynamicCode/recommend/data/employee";
        break;
      case "2":
        apiUrl = "/activity/dynamicCode/recommend/data/customer";
        break;
    }
    apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, pages, size, total } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabEcharts = async (params = {}) => {
    setLoading(true);
    const { tabType, detailId, time } = params;
    const data = {
      codeId: detailId || id,
      type: tabType || tabsTrendType,
      startTime:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format("YYYY-MM-DD"),
      endTime:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format("YYYY-MM-DD"),
    };
    data.startTime = moment(data.startTime).format("YYYY-MM-DD 00:00:00");
    data.endTime = moment(data.endTime).format("YYYY-MM-DD 23:59:59");
    apiCall("/activity/dynamicCode/recommend/data/trend", "GET", data)
      .then((res) => {
        const { list } = res;
        setChartsData(list)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDetails = (record) => {
    const { customer } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customer.id}`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    getTabsDataDetails({ pagination, tabIdx: tabsDetailsIndex });
  };

  const handleDownloadImg = (value) => {
    saveAs(QrCodeBase({ url: userInfo.url }), "活码");
  };

  const onChangeTabsDataTrend = (type) => {
    const time = formForm.getFieldValue("time");
    setTabsTrendType(type);
    getTabEcharts({
      tabType: type,
      time: time && [
        time[0].format("YYYY-MM-DD"),
        time[1].format("YYYY-MM-DD"),
      ],
    });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
      case "0":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-1":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-7":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-15":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-30":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
    }
    if (value) {
      formForm.setFieldsValue({ time });
    }
  };

  const onChangeTime = async (date, dateString) => {
    getTabEcharts({ time: date ? dateString : null });
    formForm.setFieldsValue({
      quickTime: null,
    });
  };

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index);
    getTabsDataDetails({
      tabIdx: index,
      pagination: { current: 1, pageSize: 10 },
    });
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <div className="BaseInfo">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card title={<>员工推荐码 （基础信息）</>}>
              {JSON.stringify(userInfo) != "{}" ? (
                <>
                  <Row gutter={16} className="header-box">
                    <Col xs={24} xl={18} className="info">
                      <div className="groupName">{userInfo.name}</div>
                      <Space wrap>
                        <span>活码对象:</span>
                        {userInfo.objectCodes.map((item, index) => (
                          <div className="avatar-name" key={index}>
                            <Avatar
                              shape="square"
                              size="small"
                              src={item.avatar}
                            />
                            <span style={{ verticalAlign: "middle" }}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </Space>
                      <Space wrap>
                        <span>客户标签:</span>
                        {userInfo.tagNameList.map((item, index) => (
                          <Tag key={index + 1} color="#108ee9">
                            {item}
                          </Tag>
                        ))}
                      </Space>
                      <Descriptions column={{ xs: 1, sm: 2 }}>
                        <Descriptions.Item label="获客渠道">
                          {userInfo.channelName}
                        </Descriptions.Item>
                        <Descriptions.Item label="添加设置">
                          {userInfo.skipVerify
                            ? "自动通过好友"
                            : "手动通过好友"}
                        </Descriptions.Item>
                        <Descriptions.Item label="创建">
                          {userInfo.createEmployeeName}&nbsp;
                          {userInfo.createTime}
                        </Descriptions.Item>
                        <Descriptions.Item label="更新">
                          {userInfo.updateEmployeeName}&nbsp;
                          {userInfo.updateTime}
                        </Descriptions.Item>
                      </Descriptions>
                    </Col>
                    <Col span={6}>
                      <div className="codeBox">
                        <FileHOC src={QrCodeBase({ url: userInfo.url })}>
                          {(url) => (
                            <Image
                            preview={false}
                            width={120}
                            src={url}
                          />
                          )}
                        </FileHOC>
                        <div className="btn">
                          <a
                            onClick={() => {
                              handleDownloadImg(userInfo);
                            }}
                          >
                            下载活码
                          </a>
                          <WibotCopyBtn text={userInfo.url} title="复制链接" />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="数据总览" className="data-screening">
              {overViewData ? (
                <Row>
                  <Col span={8}>
                    <span className="num">
                      {overViewData.recommendCustomerCount || 0}
                    </span>
                    <Tooltip title="通过推荐码完成添加员工的人数，不包含流失人数">
                      <p className="tip">
                        累计推荐人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日推荐人数：{overViewData.recommendCustomerCountToday}
                    </p>
                  </Col>
                  <Col span={8}>
                    <span className="num">
                      {overViewData.claimEmployeeCount || 0}
                    </span>
                    <Tooltip title="员工认领推荐码的人数">
                      <p className="tip">
                        累计认领人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日认领人数：{overViewData.claimEmployeeCountToday}
                    </p>
                  </Col>
                  <Col span={8}>
                    <span className="num">{overViewData.claimCount || 0}</span>
                    <Tooltip title="同一员工对同一推荐码可根据不同推荐场景认领多次">
                      <p className="tip">
                        累计认领次数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日认领次数：{overViewData.claimCountToday}
                    </p>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Card title="数据趋势">
              <Tabs
                activeKey={tabsTrendType}
                destroyInactiveTabPane
                onChange={onChangeTabsDataTrend}
              >
                <TabPane
                  tab="推荐人数"
                  key="RECOMMEND_CUSTOMER_COUNT"
                ></TabPane>
                <TabPane tab="认领人数" key="CLAIM_EMPLOYEE_COUNT"></TabPane>
                <TabPane tab="认领次数" key="CLAIM_COUNT"></TabPane>
                <TabPane tab="访问人数" key="VISITOR_COUNT"></TabPane>
                <TabPane tab="访问次数" key="VISIT_COUNT"></TabPane>
              </Tabs>
              <Form layout={"inline"} form={formForm}>
                <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                  <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                </FormItem>
                <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                  <Select
                    style={{ width: "200px" }}
                    options={[
                      {
                        label: "今天",
                        value: "0",
                      },
                      {
                        label: "昨天",
                        value: "-1",
                      },
                      {
                        label: "最近7天",
                        value: "-7",
                      },
                      {
                        label: "最近15天",
                        value: "-15",
                      },
                      {
                        label: "最近30天",
                        value: "-30",
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {chartsData && DemoLine(chartsData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
        <Card title="数据明细">
          <Tabs
            activeKey={tabsDetailsIndex}
            destroyInactiveTabPane
            onChange={onChangeTabsDataDetails}
          >
            <TabPane tab="员工" key="1">
              <Table
                rowKey="customerId"
                loading={loading}
                dataSource={dataSource}
                columns={staffColumns}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
            <TabPane tab="客户" key="2">
              <Table
                rowKey="customerId"
                loading={loading}
                dataSource={dataSource}
                columns={clientColumns}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(BaseInfo);
