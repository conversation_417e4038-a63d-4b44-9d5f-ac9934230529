/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/24 11:11
 * @LastEditTime: 2025/07/03 10:03
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/details/RecommendModifyRecord.jsx
 * @Description: '修改记录'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Tag,
} from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp, getWeekday } from "common/date"
import { qs2obj } from "common/object"
import moment from "moment"
import FilterBar from "components/FilterBar/FilterBar"
import SysDictSelect from "components/select/SysDictSelect"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import WibotEditorView from "components/WibotEditorView/home"

const FormItem = Form.Item
const { RangePicker } = DatePicker

const ModifyRecord = (props) => {
  const formRef = useRef(null)
  const [id, setId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "修改人/修改时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "修改模块",
      width: "160px",
      dataIndex: "moduleTypeName",
      key: "moduleTypeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "修改内容",
      width: "160px",
      dataIndex: "fieldNameList",
      key: "fieldNameList",
      align: "center",
      render: (value, record, index) => {
        const content = value.join("、")
        return (
          <Tooltip placement="topLeft" title={content}>
            {content}
          </Tooltip>
        )
      },
    },
    {
      title: "修改前",
      width: "250px",
      dataIndex: "oldMap",
      key: "oldMap",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          {value?.name && <div>活码名称：{value.name}</div>}
          {value?.channelName && <div>获客渠道：{value.channelName}</div>}
          {value?.tagNameList && (
            <div>
              客户标签：
              {value.tagNameList.map((item, idx) => (
                <Tag key={idx}>{item}</Tag>
              ))}
            </div>
          )}
          {value?.object && (
            <div style={{ display: "flex" }}>
              <div>活码对象：</div>
              <div style={{ flex: 1 }}>
                {value.object.type == "PERIOD" ? (
                  <div>
                    分时段
                    <div>
                      {value.object &&
                        value.object.objectList.map((item, idx) => (
                          <div key={idx} style={{ display: "flex" }}>
                            <div>时段{idx + 1}：</div>
                            <div style={{ flex: 1 }}>
                              <div>
                                <span>
                                  {item.weekdayList
                                    .map((atem, andex) => getWeekday(atem))
                                    .join("、")}
                                </span>
                                {item.startTime + "~" + item.endTime}
                              </div>
                              <div>
                                {item.voList.map((atem, andex) => (
                                  <Tag key={andex}>{atem.name}</Tag>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    全天
                    <div>
                      {value.object &&
                        value.object.objectList.map((item, idx) => (
                          <span key={idx}>
                            {item.voList.map((atem, andex) => (
                              <Tag key={andex}>{atem.name}</Tag>
                            ))}
                          </span>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {value?.detailList && (
            <div>
              活码对象：
              <span>
                {value.detailList.map((item, index) => (
                  <Tag key={index}>{item.objectName}</Tag>
                ))}
              </span>
            </div>
          )}
          {value?.backupObject && (
            <div style={{ display: "flex" }}>
              <div>备用客服：</div>
              <div style={{ flex: 1 }}>
                {value.backupObject.type == "PERIOD" ? (
                  <div>
                    分时段
                    <div>
                      {value.backupObject &&
                        value.backupObject.objectList.map((item, idx) => (
                          <div key={idx} style={{ display: "flex" }}>
                            <div>时段{idx + 1}：</div>
                            <div style={{ flex: 1 }}>
                              <div>
                                <span>
                                  {item.weekdayList
                                    .map((atem, andex) => getWeekday(atem))
                                    .join("、")}
                                </span>
                                {item.startTime + "~" + item.endTime}
                              </div>
                              <div>
                                {item.voList.map((atem, andex) => (
                                  <Tag key={andex}>{atem.name}</Tag>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    全天
                    <div>
                      {value.backupObject &&
                        value.backupObject.objectList[0].voList.map(
                          (item, idx) => <Tag key={idx}>{item.name}</Tag>
                        )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {JSON.stringify(value.skipVerify) && (
            <div>
              添加设置：{value.skipVerify ? "自动通过好友" : "手动通过好友"}
            </div>
          )}
          {JSON.stringify(value.autoCreateGroup) && (
            <div>
              自动建群：
              {value.autoCreateGroup ? "开启自动建群" : "关闭自动建群"}
            </div>
          )}
          {JSON.stringify(value.locationScope) && (
            <div>
              定位授权：{value.locationScope ? "开启定位授权" : "关闭定位授权"}
            </div>
          )}
          {value?.leadingMessage && (
            <div style={{ display: "flex" }}>
              <span>引导语：</span>
              <WibotEditorView html={value.leadingMessage} />
            </div>
          )}
          {value?.messages && (
            <div style={{ textAlign: "center" }}>
              <a onClick={() => handlePreview(value)}>查看</a>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "修改后",
      width: "250px",
      dataIndex: "newMap",
      key: "newMap",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          {value?.name && <div>活码名称：{value.name}</div>}
          {value?.channelName && <div>获客渠道：{value.channelName}</div>}
          {value?.tagNameList && (
            <div>
              客户标签：
              {value.tagNameList.map((item, idx) => (
                <Tag key={idx}>{item}</Tag>
              ))}
            </div>
          )}
          {value?.object && (
            <div style={{ display: "flex" }}>
              <div>活码对象：</div>
              <div style={{ flex: 1 }}>
                {value.object.type == "PERIOD" ? (
                  <div>
                    分时段
                    <div>
                      {value.object &&
                        value.object.objectList.map((item, idx) => (
                          <div key={idx} style={{ display: "flex" }}>
                            <div>时段{idx + 1}：</div>
                            <div style={{ flex: 1 }}>
                              <div>
                                <span>
                                  {item.weekdayList
                                    .map((atem, andex) => getWeekday(atem))
                                    .join("、")}
                                </span>
                                {item.startTime + "~" + item.endTime}
                              </div>
                              <div>
                                {item.voList.map((atem, andex) => (
                                  <Tag key={andex}>{atem.name}</Tag>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    全天
                    <div>
                      {value.object &&
                        value.object.objectList.map((item, idx) => (
                          <span key={idx}>
                            {item.voList.map((atem, andex) => (
                              <Tag key={andex}>{atem.name}</Tag>
                            ))}
                          </span>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {value?.detailList && (
            <div>
              活码对象：
              <span>
                {value.detailList.map((item, index) => (
                  <Tag key={index}>{item.objectName}</Tag>
                ))}
              </span>
            </div>
          )}
          {value?.backupObject && (
            <div style={{ display: "flex" }}>
              <div>备用客服：</div>
              <div style={{ flex: 1 }}>
                {value.backupObject.type == "PERIOD" ? (
                  <div>
                    分时段
                    <div>
                      {value.backupObject &&
                        value.backupObject.objectList.map((item, idx) => (
                          <div key={idx} style={{ display: "flex" }}>
                            <div>时段{idx + 1}：</div>
                            <div style={{ flex: 1 }}>
                              <div>
                                <span>
                                  {item.weekdayList
                                    .map((atem, andex) => getWeekday(atem))
                                    .join("、")}
                                </span>
                                {item.startTime + "~" + item.endTime}
                              </div>
                              <div>
                                {item.voList.map((atem, andex) => (
                                  <Tag key={andex}>{atem.name}</Tag>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    全天
                    <div>
                      {value.backupObject &&
                        value.backupObject.objectList[0].voList.map(
                          (item, idx) => <Tag key={idx}>{item.name}</Tag>
                        )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {JSON.stringify(value.skipVerify) && (
            <div>
              添加设置：{value.skipVerify ? "自动通过好友" : "手动通过好友"}
            </div>
          )}
          {JSON.stringify(value.autoCreateGroup) && (
            <div>
              自动建群：
              {value.autoCreateGroup ? "开启自动建群" : "关闭自动建群"}
            </div>
          )}
          {JSON.stringify(value.locationScope) && (
            <div>
              定位授权：{value.locationScope ? "开启定位授权" : "关闭定位授权"}
            </div>
          )}
          {value?.leadingMessage && (
            <div style={{ display: "flex" }}>
              <span>引导语：</span>
              <WibotEditorView html={value.leadingMessage} />
            </div>
          )}
          {value?.messages && (
            <div style={{ textAlign: "center" }}>
              <a onClick={() => handlePreview(value)}>查看</a>
            </div>
          )}
        </div>
      ),
    },
  ]
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    fetchList({ dynamicCodeId: id })
    setId(id)
  }, [])

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minCreateTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxCreateTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }

      const { dynamicCodeId, pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        dynamicCodeId: dynamicCodeId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }

      apiCall("/activity/dynamicCodeLog", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const handlePreview = (value) => {
    const { messages } = value
    setResourcePreviewParams({
      visible: true,
      listData: messages,
      type: "session",
      onCancel: () => {
        setResourcePreviewParams({
          visible: false,
        })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="ModifyRecord">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="moduleType">
            <SysDictSelect
              dataset="DYNAMIC_CODE_MODULE_TYPE"
              placeholder="修改模块"
            />
          </FormItem>
          <FormItem
            name="fieldName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="修改内容" allowClear />
          </FormItem>
          <FormItem
            name="createEmployeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="修改人" allowClear />
          </FormItem>
          <FormItem name="time" label="修改时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            {/* <Button type="primary" onClick={() => handleType()}>
              导出
            </Button> */}
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>

      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default withRouter(ModifyRecord)
