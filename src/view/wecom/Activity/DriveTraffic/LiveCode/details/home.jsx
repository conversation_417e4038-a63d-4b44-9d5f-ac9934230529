/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/11 15:22
 * @LastEditTime: 2023/11/06 15:42
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/details/home.jsx
 * @Description: '活码详情'
 */
import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { qs2obj } from 'common/object';
import { Card, Tabs, Button } from 'antd';
// 模块组件
import BaseInfo from './BaseInfo';
import ModifyRecord from './ModifyRecord';
import RecommendBaseInfo from './RecommendBaseInfo';
import RecommendModifyRecord from './RecommendModifyRecord';
import './home.less';

const { TabPane } = Tabs;

const LiveCodeDetails = (props) => {
  const [tabsIndex, setTabsIndex] = useState('');
  const [detailType, setDetailType] = useState('');

  useEffect(() => {
    const { type } = qs2obj(props.location.search);
    setDetailType(type);
    setTabsIndex('1');
  }, []);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className="LiveCodeDetails">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
          tabBarExtraContent={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <TabPane tab="基础信息" key="1">
            {detailType == 'RECOMMEND' ? <RecommendBaseInfo /> : <BaseInfo />}
          </TabPane>
          <TabPane tab="修改记录" key="2">
            {detailType == 'RECOMMEND' ? (<RecommendModifyRecord />) : (<ModifyRecord />)}
          </TabPane>
        </Tabs>
      </Card>

    </div>
  );
};

export default withRouter(LiveCodeDetails);
