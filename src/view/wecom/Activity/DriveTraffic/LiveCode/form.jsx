/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/21 09:59
 * @LastEditTime: 2025/07/03 09:45
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/form.jsx
 * @Description: '活码新增编辑页'
 */

import React, { useEffect, useRef, useState } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Typography,
  Select,
  TreeSelect,
  Checkbox,
  Radio,
  Switch,
  Table,
  Tooltip,
  message,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import {
  PlusOutlined,
  CheckOutlined,
  CustomerServiceOutlined,
} from "@ant-design/icons"
import { removeInputEmpty } from "common/regular"
import TransferFormModal from "components/Modal/TransferFormModal/home"
import qwCode from "images/qwCode.png"
import {
  findTreeItemByName,
  recursionKeyEmployeeOption,
  recursionTagKeyTreeData, recursionTagKeyTreeDataTag
} from "common/tree"
import { qs2obj } from "common/object"
import TimeFrame from "./comps/TimeFrame"
import TimeReserveFrame from "./comps/TimeReserveFrame"
import TimeGroupFrame from "./comps/TimeGroupFrame"
import MaterialListForm from "components/MaterialListForm/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import { clearCache } from "react-router-cache-route"
import { QrCodeBase } from "common/qrcode"
import { timeStamp } from "common/date"
import CTypeTransferModal from "components/TransferModal/CustomerType/home"
import StaffRecommend from "./comps/StaffRecommend"
import WibotEditor from "components/WibotEditor/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./form.less"
import CustomTagSelect from '@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect'

const FormItem = Form.Item
const { Paragraph } = Typography
const { SHOW_PARENT } = TreeSelect
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 11 },
  },
}
const LiveCodeForm = (props) => {
  const [liveCodeform] = Form.useForm()
  const [liveCodeform1] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const onRefMaterialListForm = useRef()
  const onRefTimeFrame = useRef()
  const onRefReserveTimeFrame = useRef()
  const onRefGroupTimeFrame = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [selectType, setSelectType] = useState("EMPLOYEE")
  const [labelTreeData, setLabelTreeData] = useState([])
  const [empDataSource, setEmpDataSource] = useState([])
  const [empData, setEmpData] = useState([])
  const [empReserveData, setEmpReserveData] = useState([])
  const [preImage, setPreImage] = useState("")
  const [valueRadio, setValueRadio] = useState("ONE")
  const [avatarScope, setAvatarScope] = useState(false)
  const [locationScope, setLocationScope] = useState(false)
  const [groupDataSource, setGroupDataSource] = useState([])
  const [createMode, setCreateMode] = useState([])
  const [locationOption, setLocationOption] = useState([])
  const [groupOption, setGroupOption] = useState([])
  const [transferFormParams, setTransferFormParams] = useState({
    visible: false,
  })
  const [isType, setIsType] = useState(false)
  const [deletEmpKey, setDeletEmpKey] = useState([])
  const [deletEmpReserveKey, setDeletEmpReserveKey] = useState([])
  const [empDetailList, setEmpDetailList] = useState([])
  const [editorMessage, setEditorMessage] = useState("")
  const [timeRadio, setTimeRadio] = useState("ALL_DAY") // 活码对象全天-分时段
  const [timeReserveRadio, setTimeReserveRadio] = useState("ALL_DAY") // 备用客服全天-分时段
  const [msgList, setMsgList] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [previewList, setPreviewList] = useState([])
  const [employeeOptions, setEmployeeOptions] = useState([])
  const scrollListData = [
    {
      title: "员工活码",
      describe: "为企业内的员工创建专属的活码，用于获客、引流",
      type: "EMPLOYEE",
    },
    {
      title: "客户群活码",
      describe:
        "为已创建的群聊添加活码，有助于解决群二维码过期及多群切换的问题",
      type: "GROUP",
    },
    {
      title: "员工推荐码",
      describe:
        "通过API调用接口将已识别的客户匹配到给对应的员工，创建时无需设置活码对象，无员工时匹配给备用客服",
      type: "RECOMMEND",
    },
  ]
  const authorizationOptions = ["定位授权"] // ['头像昵称', '定位授权']
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  })
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [groupDataSource1, setGroupDataSource1] = useState([])
  const groupColumns1 = [
    {
      title: "序号",
      width: "60px",
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "原归属人（群主）",
      width: "160px",
      dataIndex: "leaderName",
      key: "leaderName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "群人数",
      width: "160px",
      dataIndex: "memberCount",
      key: "memberCount",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record)}>删除</a>
        </>
      ),
    },
  ]
  const groupLoColumns1 = [
    {
      title: "序号",
      width: "60px",
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "locationName",
      key: "locationName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "原归属人（群主）",
      width: "160px",
      dataIndex: "leaderName",
      key: "leaderName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "群人数",
      width: "160px",
      dataIndex: "memberCount",
      key: "memberCount",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record)}>删除</a>
        </>
      ),
    },
  ]

  const [existTagNameList, setExistTagNameList] = useState([])

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    if (type == "RECOMMEND") {
      setSelectType("RECOMMEND")
      setId(id)
      return false
    }
    getcreateModeType()
    optionGroupByDepartment()
    // getLocationOption();
    getTagCategoryTreeTwo()
    // getGroupOption();
    liveCodeform.setFieldsValue({
      skipVerify: true,
    })
    if (id) {
      setId(id)
      init(id)
    } else {
      // getDefaultMiniItem();
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
  }, [])

  const init = async (id) => {
    const params = {
      id,
    }
    await fetchList(params)
  }

  const getDefaultMiniItem = () => {
    setLoading(true)
    apiCall("/welcomeWord/defaultMiniItem", "GET")
      .then((res) => {
        if (res) {
          onRefMaterialListForm.current.getInitMsgList([res])
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    const data = {
      id,
    }
    await apiCall("/activity/dynamicCode/getAndRunningDetailList", "GET", data)
      .then(async (res) => {
        setSelectType(res.type)
        setIsType(res.type)
        liveCodeform.setFieldsValue({ ...res })
        const {
          avatarScope,
          locationScope,
          tagNameList,
          detailList,
          createMode,
          reserveEmployeeVO,
          messages,
          object,
          backupObject,
          leadingMessage,
        } = res
        setAvatarScope(avatarScope)
        setLocationScope(locationScope)
        setValueRadio(createMode)
        setEmpDetailList(detailList)
        setExistTagNameList(res.tagNameList)

        liveCodeform.setFieldsValue({
          authorization: [
            avatarScope && "头像昵称",
            locationScope && "定位授权",
          ],
          // tagIdList: tagIdList.length > 0 ? tagIdList.join(",").split(",") : [],
          tagNameList
        })

        if (res.type == "EMPLOYEE") {
          // 资源中心组件-欢迎语
          onRefMaterialListForm.current.getInitMsgList(messages)

          const newEmpData = empData
          // 活码对象
          if (createMode == "MULTI") {
            /**
             * 多人一码
             */
            // 员工活码
            setTimeRadio(object?.type)
            setTimeReserveRadio(backupObject?.type)
            // setEmpData(object?.objectList[0]?.voList);
            let data = []
            if (object?.type == "PERIOD") {
              onRefTimeFrame.current?.getInitObjData(object ?? {})
            } else {
              object &&
                object?.objectList?.forEach((item) => {
                  data = data.concat(item.voList)
                })
              setEmpData(data ?? [])
            }
            // 备用客服
            if (backupObject?.type == "PERIOD") {
              onRefReserveTimeFrame.current?.getInitObjData(backupObject ?? {})
            } else {
              setEmpReserveData(
                backupObject?.objectList?.length > 0
                  ? backupObject.objectList[0].voList
                  : []
              )
            }
            liveCodeform.setFieldsValue({
              codeObj:
                (data.length && data?.map((item) => String(item.id))) ||
                object?.objectList[0]?.voList?.map((item) => String(item.id)),
            })
          } else {
            // 员工活码
            detailList?.map((item) => {
              if (!item.reserve) {
                newEmpData.push({
                  id: String(item.objectId),
                  name: item.objectName,
                  key: item.objectId,
                })
              }
            })
            setEmpData([...newEmpData])
            // 备用客服
            setEmpReserveData(reserveEmployeeVO?.id ? [reserveEmployeeVO] : [])
            liveCodeform?.setFieldsValue({
              codeObj: [newEmpData[0].id],
            })
          }
        } else {
          // 活码对象
          setTimeRadio(object?.type)
          setTimeReserveRadio(backupObject?.type)
          if (object?.type == "PERIOD") {
            onRefGroupTimeFrame.current?.getInitObjData(object ?? {})
            setGroupDataSource1(
              object?.objectList && object?.objectList?.length > 0
                ? object?.objectList[0]?.voList
                : []
            )
          } else if (object?.type == "ALL_DAY") {
            setGroupDataSource1(object?.objectList[0]?.voList ?? [])
          }
          // 备用客服
          if (backupObject?.type == "PERIOD") {
            onRefReserveTimeFrame.current?.getInitObjData(backupObject ?? {})
          } else {
            setEmpReserveData(
              backupObject?.objectList?.length > 0
                ? backupObject.objectList[0].voList
                : []
            )
          }
          liveCodeform.setFieldsValue({
            codeObj: object?.type,
            reserve:
              backupObject?.objectList?.length > 0
                ? backupObject.objectList[0].voList.map((item) =>
                    String(item.id)
                  )
                : [],
          })
        }

        let timer = setTimeout(() => {
          WibotEditorRef.current.setHtml(leadingMessage)
          clearTimeout(timer)
        }, 300)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchGroupList = (params = {}) => {
    setLoading(true)
    const { query } = params
    const data = {
      paged: false,
      ...query,
    }
    apiCall("/group/page", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        setGroupDataSource1(records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获客渠道
  const getcreateModeType = () => {
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setCreateMode(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取活码对象选择的所有员工
  const optionGroupByDepartment = () => {
    apiCall("/employee/company_and_employee_option", "GET")
      .then((res) => {
        const treeData = recursionKeyEmployeeOption(res)
        setEmpDataSource(
          treeData.map((item, index) => ({
            ...item,
            checkable: false,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取活码对象员工或群组的定位
  const getLocationOption = () => {
    apiCall("/base/location/option", "GET")
      .then((res) => {
        setLocationOption(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 获取群组数据
  const getGroupOption = async () => {
    await apiCall("/group/option", "GET")
      .then((res) => {
        setGroupOption(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 活码类型切换
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setEmpData([])
    setEmpReserveData([])
    setGroupDataSource([])
    setLocationScope(false)
    setTimeRadio("ALL_DAY")
    setTimeReserveRadio("ALL_DAY")
    setEmployeeOptions([])
    setPreviewList([])
    liveCodeform.setFieldsValue({
      authorization: [],
    })
    liveCodeform.resetFields()
    setSelectType(item.type)
    setEditorMessage("")
    localStorage.removeItem("objList")
    localStorage.removeItem("objGroupList")
    localStorage.removeItem("objReserveList")
    if (item.type == "EMPLOYEE") {
      // getDefaultMiniItem();
      let timer = setTimeout(() => {
        onRefMaterialListForm?.current?.getInitMsgList([
          {
            type: "copyWriter",
            content: "",
            defaultFlag: true,
          },
        ])
        clearTimeout(timer)
      }, 100)
    } else {
      setMsgList([])
    }
  }

  // /访问授权选择
  const onAuthorizationChange = (checkedValues, type = null) => {
    if (checkedValues.includes("定位授权")) {
      setLocationScope(true)
    } else {
      setLocationScope(false)
    }
    if (checkedValues.includes("头像昵称")) {
      setAvatarScope(true)
    } else {
      setAvatarScope(false)
    }
  }

  // 创建模式切换
  const onChangeRadio = (e) => {
    setValueRadio(e.target.value)
    if (e.target.value == "ONE") {
      setAvatarScope(false)
      setLocationScope(false)
      liveCodeform.setFieldsValue({
        authorization: [],
      })
    }
    setEmpData([])
    liveCodeform.setFieldsValue({
      codeObj: [],
    })
    setEmployeeOptions([])
  }

  // 选择备用员工
  const onChooseReserveEmp = () => {
    let timeEmpReserveData = []
    if (timeReserveRadio == "PERIOD") {
      timeEmpReserveData =
        onRefReserveTimeFrame.current.getChooseCurrentEmpData()
      timeEmpReserveData.forEach((item) => {
        item.key = item.id
      })
    }

    setTransferFormParams({
      visible: true,
      dataSource: empDataSource,
      empData:
        timeReserveRadio == "PERIOD" ? timeEmpReserveData : empReserveData,
      isSingle: true,
      onSubmit: (data) => {
        if (timeReserveRadio == "PERIOD") {
          onRefReserveTimeFrame.current.getChooseReserveEmpData(data)
        } else {
          data.forEach((item) => {
            item.id = item.key
          })
          setEmpReserveData(data)
        }
        setTransferFormParams({ visible: false })
        liveCodeform.setFieldsValue({
          reserve: data.filter((item) => item.key),
        })
      },
      onCancel: () => {
        setTransferFormParams({ visible: false })
      },
    })
  }

  // 添加设置
  const onChangeSwitchSetState = (checked) => {
    liveCodeform.setFieldsValue({
      skipVerify: checked ? 1 : 0,
    })
  }

  // 自动建群
  const onChangeSwitchAutoCreateGroup = (checked) => {
    liveCodeform.setFieldsValue({
      autoCreateGroup: checked ? 1 : 0,
    })
  }

  // 活码对象
  const handleAddOject = () => {
    let newDataSource = JSON.parse(JSON.stringify(groupDataSource))
    newDataSource.push({
      id: Math.random().toString(16),
      name: "",
      locationId: "",
      isEdit: true,
    })
    setGroupDataSource([...newDataSource])
    if (timeRadio == "PERIOD") {
      onRefGroupTimeFrame.current.getGroupObjData([...newDataSource])
    }
  }

  const handleEmpClose = (e, item, value) => {
    e.preventDefault()
    if (value == "reserve") {
      const newDeletEmpReserveKey = deletEmpReserveKey
      const newEmpReserveData = empReserveData
      setEmpReserveData(newEmpReserveData.filter((i) => i.key != item.key))
      newDeletEmpReserveKey.push(item.key)
      setDeletEmpReserveKey(newDeletEmpReserveKey)
    } else {
      const newDeletEmpKey = deletEmpKey
      const newEmpData = empData
      setEmpData(newEmpData.filter((i) => i.key != item.key))
      newDeletEmpKey.push(item.key)
      setDeletEmpKey(newDeletEmpKey)
    }
  }

  // 删除操作
  const handleDelete = (record, index) => {
    let newDataSource = JSON.parse(JSON.stringify(groupDataSource1))
    newDataSource.splice(newDataSource.indexOf(record), 1)
    liveCodeform.setFieldsValue({
      codeObj: newDataSource.map((item) => item.id),
    })
    setGroupDataSource1([...newDataSource])
    if (selectType == "GROUP" && timeRadio == "PERIOD") {
      onRefGroupTimeFrame.current.getGroupObjData([...newDataSource])
    }
    setPaginations({
      ...paginations,
      total: newDataSource.length,
    })
  }

  // 活码对象全天-分时段选择
  const onTimeChangeRadio = (e) => {
    setTimeRadio(e.target.value)
    liveCodeform.setFieldsValue({
      codeObj:
        selectType == "GROUP"
          ? groupDataSource.filter((item) => item.id || item.key)
          : empData.map((item) => String(item.id)),
    })
    if (selectType == "GROUP") {
      setGroupDataSource1([])
    }
  }

  // 备用客服全天-分时段选择
  const onTimeReserveChangeRadio = (e) => {
    setTimeReserveRadio(e.target.value)
    // setEmpReserveData([]);
  }

  //查看上级部门欢迎语
  const handlePreview = () => {
    setLoading(true)
    let data = {}
    //单员工情况下传员工id
    if (
      selectType == "EMPLOYEE" &&
      valueRadio == "ONE" &&
      empData.length == 1
    ) {
      data.deptId = empData.map((item) => item.id).join(",")
    } else if (
      selectType == "EMPLOYEE" &&
      valueRadio == "MULTI" &&
      timeRadio == "ALL_DAY" &&
      empData.length == 1
    ) {
      data.deptId = empData.map((item) => item.id).join(",")
    }
    apiCall(`/welcomeWord/superior`, "GET", data)
      .then((res) => {
        const newItemList = res
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Article" ||
            item.type == "LINK_CARD" ||
            item.type == "Poster" ||
            item.type == "Product"
          ) {
            item.imageUrl = item.fileId ? item.fileId[0] : ""
          } else if (item.type == "QrCode") {
            item.imageUrl = QrCodeBase({ url: item.url })
          } else if (item.type == "Video") {
            item.videoUrl = item.fileId ? item.fileId[0] : ""
          }
        })
        setPreviewParams({
          visible: true,
          title: "欢迎语内容",
          dataSource: newItemList,
          onCancel: () => {
            setPreviewParams({ visible: false })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  //选择部门或员工获取预览数据
  const handleDeptIdChange = (value, options = [], type = null) => {
    setLoading(true)
    if (type) {
      setEmployeeOptions(options)
      setEmpData([...options])
      liveCodeform.setFieldsValue({
        codeObj: value,
      })
    }
    const data = {
      deptId: type ? value[0] : value,
    }
    apiCall("/welcomeWord/superior", "GET", data)
      .then((res) => {
        const newItemList = res
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Poster" ||
            item.type == "QrCode"
          ) {
            item.image = item.fileId ? item.fileId[0] : ""
          }
        })
        setPreviewList(newItemList)
        if (type) {
          message.success("已在界面预览处显示上级部门欢迎语！")
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const changeEmployeeOptions = (data) => {
    if (data && data.length > 0) {
      liveCodeform1.setFieldsValue({
        employeeOption: data[0].id,
      })
      setEmployeeOptions(data)
    }
  }

  // 提交
  const onSubmit = () => {
    liveCodeform.validateFields().then((formData) => {
      const {
        name,
        createMode,
        leadingMessage,
        skipVerify,
        channelId,
        tagIdList,
        autoCreateGroup,
        tagNameList
      } = formData
      if (selectType == "GROUP") {
        if (
          !groupDataSource1.every((item) => item.locationId) &&
          locationScope
        ) {
          message.error("关联定位不能为空！")
          return
        }
        if (
          timeRadio == "PERIOD" &&
          onRefGroupTimeFrame.current.getSubmitMessageOne()
        ) {
          return
        }
      }
      if (
        selectType == "EMPLOYEE" &&
        timeRadio == "PERIOD" &&
        onRefTimeFrame.current.getSubmitMessageOne()
      ) {
        return
      }
      if (
        selectType == "EMPLOYEE" &&
        timeRadio == "PERIOD" &&
        onRefTimeFrame.current.getSubmitMessageTwo()
      ) {
        return
      }
      if (
        selectType == "EMPLOYEE" &&
        timeRadio == "PERIOD" &&
        onRefTimeFrame.current.getSubmitMessageThree()
      ) {
        return
      }
      if (
        selectType == "EMPLOYEE" &&
        onRefMaterialListForm.current.getCopyWriterValidate()
      ) {
        //校验资源文案字数
        return
      }
      const detailIdList = []
      empDetailList.map((item) => {
        if (deletEmpKey.indexOf(item.objectId) > -1) {
          detailIdList.push(item.id)
        }
      }) // 被删除的活码对象id
      empDetailList.map((item) => {
        if (deletEmpReserveKey.indexOf(item.objectId) > -1) {
          detailIdList.push(item.id)
        }
      }) // 被删除的备用员工id
      setLoading(true)

      const data = {
        id: id ?? null,
        type: selectType,
        name,
        createMode,
        leadingMessage,
        skipVerify,
        avatarScope,
        locationScope,
        channelId,
        tagIdList: tagIdList ?? [],
        tagNameList,
        autoCreateGroup,
        detailIdList,
        state: !id ? true : null,
        messages:
          selectType == "EMPLOYEE"
            ? onRefMaterialListForm.current.getModifyMsgList()
            : [],
      }

      if (selectType == "EMPLOYEE") {
        if (valueRadio == "MULTI") {
          /**
           * 多人一码
           */
          // 员工活码对象
          if (timeRadio == "PERIOD") {
            // 分时段
            const objData = onRefTimeFrame.current.getModifyObjList()
            data.object = {
              objectList: objData,
              objectType: "EMPLOYEE",
              type: timeRadio,
            }
          } else {
            data.object = {
              objectList: empData.map((item) => ({
                voList: [item],
              })),
              objectType: "EMPLOYEE",
              type: timeReserveRadio,
            }
          }

          // 备用客服
          if (timeReserveRadio == "PERIOD") {
            // 分时段
            const reserveObjData =
              onRefReserveTimeFrame.current.getModifyObjList()
            data.backupObject = {
              objectList: reserveObjData,
              objectType: "EMPLOYEE",
              type: timeReserveRadio,
            }
          } else {
            data.backupObject = {
              objectList: empReserveData.map((item) => ({
                voList: [item],
              })),
              objectType: "EMPLOYEE",
              type: timeReserveRadio,
            }
          }
        } else {
          /**
           * 一人一码
           */
          // 员工活码对象
          let detailList = []
          empData.forEach((item, index) => {
            detailList.push({
              state: true,
              reserve: false,
              objectId: item.id,
              objectType: "EMPLOYEE",
              [locationScope && "locationId"]: locationScope && item.locationId,
            })
          })
          // 员工备用客服
          empReserveData.forEach((item) => {
            detailList.push({
              state: true,
              reserve: true,
              objectId: item.key || item.id,
              objectType: "EMPLOYEE",
            })
          })
          data.detailList = detailList // 活码对象以及备用员工列表
          data.reserveEmployeeId =
            empReserveData.length > 0 ? empReserveData[0].key : "" // 备用员工id
        }
      } else {
        /**
         * 群活码对象
         */
        if (timeRadio == "PERIOD") {
          const objData = onRefGroupTimeFrame.current.getModifyObjList()
          data.object = {
            objectList: objData,
            objectType: "GROUP",
            type: timeRadio,
          }
        } else if (timeRadio == "ALL_DAY") {
          data.object = {
            objectList: [
              {
                voList: groupDataSource1,
              },
            ],
            objectType: "GROUP",
            type: timeRadio,
          }
        }
        // 备用客服
        if (timeReserveRadio == "PERIOD") {
          // 分时段
          const reserveObjData =
            onRefReserveTimeFrame.current.getModifyObjList()
          data.backupObject = {
            objectList: reserveObjData,
            objectType: "EMPLOYEE",
            type: timeReserveRadio,
          }
        } else {
          data.backupObject = {
            objectList: empReserveData.map((item) => ({
              voList: [item],
            })),
            objectType: "EMPLOYEE",
            type: timeReserveRadio,
          }
        }
      }
      apiCall("/activity/dynamicCode/saveOrUpdate", "PUT", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          localStorage.removeItem("objList")
          localStorage.removeItem("objGroupList")
          localStorage.removeItem("objReserveList")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/drivetraffic")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const changeLoading = (flag) => {
    setLoading(flag)
  }

  const handleGoBack = () => {
    localStorage.removeItem("objList")
    localStorage.removeItem("objGroupList")
    localStorage.removeItem("objReserveList")
    props.history.go(-1)
  }

  return (
    <div className="LiveCodeForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑活码" : "创建活码"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <h2 className="card-title">活码类型</h2>
          <ul className="scrollList" id="scrollList">
            {scrollListData.map((item, index) => (
              <li
                key={index}
                className={
                  selectType == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${
                    selectType != item.type && id ? "#c1c1c1" : "unset"
                  }`,
                  cursor: `${
                    selectType != item.type && id ? "unset" : "pointer"
                  }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>
        </Card>
        <br />
        <Card bordered={false}>
          {selectType == "RECOMMEND" ? (
            <StaffRecommend changeLoading={changeLoading} />
          ) : (
            <Row>
              <Col xs={24} lg={16}>
                <h2 className="card-title">基础信息</h2>
                <Form {...layout} form={liveCodeform}>
                  <FormItem
                    label="活码名称"
                    name="name"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[
                      { required: true, message: "请输入活码名称（30字）" },
                    ]}
                  >
                    <Input
                      placeholder="请输入活码名称（30字）"
                      allowClear
                      maxLength={30}
                    />
                  </FormItem>
                  <FormItem label="获客渠道" name="channelId">
                    <Select
                      options={createMode}
                      allowClear
                      placeholder="请选择"
                      fieldNames={{ label: "name", value: "id" }}
                      filterOption={(input, option) =>
                        (option?.name ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    />
                  </FormItem>
                  <CustomTagSelect
                    label="客户标签"
                    name="tagNameList"
                    placeholder="客户标签"
                    useForm={liveCodeform}
                    existTagNameList={existTagNameList}
                    labelTreeData={labelTreeData}
                  />
                  {/* <FormItem label="客户标签" name="tagIdList"> */}
                  {/*   <TreeSelect */}
                  {/*     treeData={labelTreeData} */}
                  {/*     treeCheckable */}
                  {/*     treeDefaultExpandedKeys={["customer"]} */}
                  {/*     allowClear */}
                  {/*     showArrow */}
                  {/*     showSearch */}
                  {/*     treeNodeFilterProp="title" */}
                  {/*     maxTagCount="responsive" */}
                  {/*     showCheckedStrategy={SHOW_PARENT} */}
                  {/*     placeholder="客户标签" */}
                  {/*   /> */}
                  {/* </FormItem> */}
                  {selectType == "GROUP" && (
                    <FormItem
                      label="访问授权"
                      name="authorization"
                      extra="访问权限请根据实际需要勾选，定位权限开启后，所添加的对象必须关联定位，仅用于多人一码。"
                    >
                      <Checkbox.Group
                        options={authorizationOptions}
                        onChange={onAuthorizationChange}
                        disabled={
                          selectType == "EMPLOYEE" && valueRadio == "ONE"
                        }
                      />
                    </FormItem>
                  )}

                  <h2>活码信息</h2>
                  {selectType == "EMPLOYEE" ? (
                    <FormItem
                      label="创建模式"
                      name="createMode"
                      initialValue={"ONE"}
                      rules={[{ required: true, message: "请勾选创建模式！" }]}
                      extra={
                        <div>
                          <div>
                            一人一码：批量为多位员工创建活码，生成多个活码，各活码配置相同
                          </div>
                          <div>
                            多人一码：将多位员工的二维码整合在同一活码中，只生成一个活码，员工切换按序循环。
                          </div>
                        </div>
                      }
                    >
                      <Radio.Group onChange={onChangeRadio} disabled={!!id}>
                        <Radio value={"ONE"}>一人一码</Radio>
                        <Radio value={"MULTI"}>多人一码</Radio>
                      </Radio.Group>
                    </FormItem>
                  ) : (
                    ""
                  )}

                  {/* 定位授权-员工对象列表 */}
                  {/* 多人一码 */}
                  {selectType == "EMPLOYEE" && valueRadio == "MULTI" && (
                    <FormItem label="活码对象">
                      <div>
                        <Radio.Group
                          onChange={onTimeChangeRadio}
                          value={timeRadio}
                          style={{ marginBottom: "10px" }}
                        >
                          <Radio value={"ALL_DAY"}>全天</Radio>
                          <Radio value={"PERIOD"}>分时段</Radio>
                        </Radio.Group>
                        <FormItem name="codeObj" shouldUpdate>
                          {timeRadio == "PERIOD" ? (
                            <TimeFrame
                              codeId={id}
                              data={empData}
                              formRef={liveCodeform}
                              ref={onRefTimeFrame}
                              locationScope={locationScope}
                              onChooseEmp={(value, options) => {
                                onRefTimeFrame.current.getChooseEmpData(options)
                                setEmpData([...options])
                                liveCodeform.setFieldsValue({
                                  codeObj: value,
                                })
                              }}
                              handleEmpClose={handleEmpClose}
                              handleDeptIdChange={(value) => {
                                handleDeptIdChange(value)
                              }}
                              changeEmployeeOptions={changeEmployeeOptions}
                            />
                          ) : (
                            <ETypeTransferModal
                              title="选择员工"
                              onlyEmployee={true}
                              multiple={true}
                              onChange={(value, options) => {
                                handleDeptIdChange(value, options, "EMPLOYEE")
                              }}
                            />
                          )}
                        </FormItem>
                      </div>
                    </FormItem>
                  )}

                  {/* 一人一码 */}
                  {selectType == "EMPLOYEE" && valueRadio != "MULTI" && (
                    <FormItem
                      label="活码对象"
                      name="codeObj"
                      shouldUpdate
                      rules={[{ required: true, message: "请选择活码对象！" }]}
                    >
                      <ETypeTransferModal
                        title="选择员工"
                        onlyEmployee={true}
                        multiple={true}
                        onChange={(value, options) => {
                          handleDeptIdChange(value, options, "EMPLOYEE")
                        }}
                      />
                    </FormItem>
                  )}

                  {/* 客户群活码 */}
                  {selectType == "GROUP" && (
                    <FormItem
                      label="活码对象"
                      name="codeObj"
                      shouldUpdate
                      rules={[{ required: true, message: "请选择活码对象！" }]}
                    >
                      {/* 客户群组对象列表 */}
                      <div>
                        <Radio.Group
                          onChange={onTimeChangeRadio}
                          value={timeRadio}
                          style={{ marginBottom: "10px" }}
                        >
                          <Radio value={"ALL_DAY"}>全天</Radio>
                          <Radio value={"PERIOD"}>分时段</Radio>
                        </Radio.Group>
                        {timeRadio == "PERIOD" ? (
                          <TimeGroupFrame
                            formRef={liveCodeform}
                            ref={onRefGroupTimeFrame}
                            dataSource={groupDataSource1}
                            columns={
                              locationScope ? groupLoColumns1 : groupColumns1
                            }
                            onAdd={(data) => {
                              setGroupDataSource1([...data])
                              onRefGroupTimeFrame.current.getGroupObjData([
                                ...data,
                              ])
                            }}
                            setGroupData={(data) => {
                              data && data.length
                                ? fetchGroupList({
                                    query: {
                                      idList: data
                                        .map((item) => item.id)
                                        .join(","),
                                    },
                                  })
                                : setGroupDataSource1([...data])
                            }}
                          />
                        ) : (
                          <div>
                            <CTypeTransferModal {...CTypeTransferParams} />
                            <Button
                              icon={<PlusOutlined />}
                              type="primary"
                              onClick={() => {
                                setCTypeTransferParams({
                                  visible: true,
                                  type: "group",
                                  checkList: groupDataSource1,
                                  onSubmit: (data) => {
                                    liveCodeform.setFieldsValue({
                                      codeObj: data.map((item) => item.id),
                                    })
                                    setGroupDataSource1([...data])
                                    setPaginations({
                                      current: 1,
                                      pageSize: 10,
                                      total: data.length,
                                      showQuickJumper: true,
                                      showSizeChanger: true,
                                      showTotal: (total, range) =>
                                        `共 ${total} 条记录`,
                                    })
                                    setCTypeTransferParams({ visible: false })
                                  },
                                  onCancel: () => {
                                    setCTypeTransferParams({ visible: false })
                                  },
                                })
                              }}
                            >
                              选择客户群
                            </Button>
                            <Table
                              rowKey="key"
                              loading={loading}
                              dataSource={groupDataSource1}
                              columns={
                                locationScope ? groupLoColumns1 : groupColumns1
                              }
                              scroll={{ x: 600 }}
                              pagination={paginations}
                              onChange={(pagination, filters, sorter) => {
                                setPaginations({
                                  ...pagination,
                                  showTotal: (total, range) =>
                                    `共 ${total} 条记录`,
                                })
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </FormItem>
                  )}

                  {selectType == "GROUP" ? (
                    <FormItem
                      name="autoCreateGroup"
                      label="自动建群"
                      valuePropName="checked"
                      extra="开启后，群聊人数达到上限后，将以原群主身份自动创建新群"
                    >
                      <Switch onChange={onChangeSwitchAutoCreateGroup} />
                    </FormItem>
                  ) : (
                    ""
                  )}

                  {selectType == "GROUP" && (
                    <FormItem
                      label="备用客服"
                      extra={
                        <div>
                          <div style={{ width: "480px" }}>
                            防止二维码无法扫描时客户能及时取得联系
                            {selectType == "EMPLOYEE" &&
                              "，请勿与活码对象所选的员工相同"}
                          </div>
                        </div>
                      }
                    >
                      {(selectType == "EMPLOYEE" && valueRadio == "MULTI") ||
                      selectType == "GROUP" ? (
                        <div>
                          <Radio.Group
                            onChange={onTimeReserveChangeRadio}
                            value={timeReserveRadio}
                            style={{ marginBottom: "10px" }}
                          >
                            <Radio value={"ALL_DAY"}>全天</Radio>
                            <Radio value={"PERIOD"}>分时段</Radio>
                          </Radio.Group>
                          <FormItem
                            name="reserve"
                            rules={[
                              { required: false, message: "请选择备用客服！" },
                            ]}
                          >
                            {timeReserveRadio == "PERIOD" ? (
                              <TimeReserveFrame
                                data={empReserveData}
                                formRef={liveCodeform}
                                ref={onRefReserveTimeFrame}
                                onChooseEmp={(value, options) => {
                                  onRefReserveTimeFrame.current.getChooseReserveEmpData(
                                    options
                                  )
                                  setEmpReserveData([...options])
                                }}
                                handleEmpClose={handleEmpClose}
                              />
                            ) : (
                              <ETypeTransferModal
                                style={{ display: "block" }}
                                title="选择员工"
                                onlyEmployee={true}
                                multiple={false}
                                onChange={(value, options) => {
                                  setEmpReserveData([...options])
                                }}
                              />
                            )}
                          </FormItem>
                        </div>
                      ) : (
                        <Button type="primary" onClick={onChooseReserveEmp}>
                          选择员工
                        </Button>
                      )}
                    </FormItem>
                  )}
                  {selectType == "EMPLOYEE" ? (
                    <FormItem
                      name="skipVerify"
                      label="添加设置"
                      valuePropName="checked"
                      extra="开启后，客户添加时无需经过确认自动成为好友"
                    >
                      <Switch onChange={onChangeSwitchSetState} />
                    </FormItem>
                  ) : (
                    ""
                  )}
                  <FormItem
                    name="leadingMessage"
                    label="引导语"
                    initialValue={
                      selectType == "EMPLOYEE"
                        ? "欢迎通过以下二维码添加好友，方便为您解答问题"
                        : "欢迎通过以下二维码加入群聊"
                    }
                    extra="建议引导语字数不超过100字"
                  >
                    <WibotEditor
                      ref={WibotEditorRef}
                      toolbarConfig={{
                        toolbarKeys: ["emotion"],
                      }}
                      editorConfig={{
                        placeholder: "请输入内容(建议100字内)...",
                      }}
                      onChangeHtml={(html) => {
                        liveCodeform.setFieldValue("leadingMessage", html)
                        setEditorMessage(html)
                      }}
                    />
                  </FormItem>

                  {/* 素材库列表表单 */}
                  {selectType == "EMPLOYEE" && (
                    <FormItem
                      name="msgList"
                      label="欢迎语"
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                      rules={[{ required: true, message: "请添加欢迎语！" }]}
                    >
                      <>
                        <div className="WelcomeSpeech-tips">
                          您的欢迎语会随上级部门欢迎语一同发，客户最多接收到前1个文案9个附件，且文案超长时会被截断
                          {/* <span className='checkContents' onClick={() => { handlePreview() }}>查看上级部门欢迎语</span> */}
                        </div>
                        <MaterialListForm
                          params={{
                            formRef: liveCodeform,
                            menuList: ["image", "material", "liveCode"],
                            needScriptFlag: true,
                            materialAmount: msgList.length,
                            previewList: previewList,
                            extra: (
                              <div>
                                请按顺序添加预计要发送的欢迎语，还可添加{" "}
                                {10 - msgList.length} 条<br />
                                选择素材后，参考话术将会自动添加在唯一的文案中
                              </div>
                            ),
                          }}
                          // 监听回调
                          callback={(params) => {
                            setMsgList(params.data)
                          }}
                          ref={onRefMaterialListForm}
                        />
                      </>
                    </FormItem>
                  )}

                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <Space size={40}>
                      <Button type="primary" onClick={() => onSubmit()}>
                        确定
                      </Button>
                    </Space>
                  </div>
                </Form>
              </Col>
              <Col xs={24} lg={8}>
                <WibotMobilePreview title="活码名称">
                  <div className="text">
                    <Paragraph ellipsis={{ rows: 3, tooltip: true }}>
                      {editorMessage ? (
                        <WibotEditorView html={editorMessage} />
                      ) : selectType == "EMPLOYEE" ? (
                        <span>
                          欢迎通过以下二维码添加好友，方便为您解答问题
                        </span>
                      ) : (
                        <span>欢迎通过以下二维码加入群聊</span>
                      )}
                    </Paragraph>
                  </div>
                  <div className="qw-code">
                    <img src={qwCode} alt="" />
                    <p>请长按识别二维码</p>
                  </div>
                  <div className="phone-bot">
                    <div className="bot-text">
                      若无法扫码，请添加客服微信，获取最新二维码
                    </div>
                    <Button icon={<CustomerServiceOutlined />}>联系客服</Button>
                  </div>
                </WibotMobilePreview>

                {(employeeOptions.length > 0 && (
                  <Form form={liveCodeform1}>
                    <FormItem
                      name="employeeOption"
                      label="活码对象"
                      style={{ marginTop: "24px" }}
                    >
                      <Select
                        placeholder="活码对象"
                        fieldNames={{ label: "name", value: "id" }}
                        options={employeeOptions}
                        defaultValue={employeeOptions[0].value}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.name ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        onSelect={(value) => {
                          handleDeptIdChange(value)
                        }}
                      />
                    </FormItem>
                  </Form>
                )) || <br />}

                <WibotMaterialPreview
                  type="session"
                  mergeFlag={true}
                  listData={[...previewList, ...msgList]}
                />
              </Col>
            </Row>
          )}
        </Card>
        <TransferFormModal {...transferFormParams} />
      </Spin>
    </div>
  )
}

export default LiveCodeForm
