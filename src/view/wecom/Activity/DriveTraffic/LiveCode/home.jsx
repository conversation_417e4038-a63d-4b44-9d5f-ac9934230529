/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2022/04/20 16:05
 * @LastEditTime: 2023/11/21 11:26
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/LiveCode/home.jsx
 * @Description: ''
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  TreeSelect,
  DatePicker,
  Image,
  Switch,
  Select,
  message,
  Spin,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import FilterBar from "components/FilterBar/FilterBar";
import SysDictSelect from "components/select/SysDictSelect";
import OperateModal from "components/Modal/OperateModal/index";
import { QrCodeBase } from "common/qrcode";
import SysDictLabel from "components/select/SysDictLabel";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import {FileHOC} from 'components/FileHOC/FileHOC';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const { RangePicker } = DatePicker;

const LiveCode = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [createMode, setCreateMode] = useState([]);
  const [locationOption, setLocationOption] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [showSpinningTips, setShowSpinningTips] = useState(false);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "活码名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "活码二维码",
      width: "160px",
      dataIndex: "url",
      key: "url",
      align: "center",
      render: (value, record, index) => {
        const image = QrCodeBase({ url: value });
         return <FileHOC src={image}>
                  {(url) => (
                    <Image width={60} src={url} preview={url}></Image>
                  )}
                </FileHOC>
      },
    },
    {
      title: "启用状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <Switch
          checkedChildren="已启用"
          unCheckedChildren="已停用"
          checked={value}
          onChange={(checked) => {
            onChangeSwitchStatus(checked, record);
          }}
        />
      ),
    },
    {
      title: "访问数据",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          访问次数：{record.visitCount}
          <br />
          访问人数：{record.visitorCount}
        </div>
      ),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "活码类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="WECOM_CODE_TYPE" dictkey={value} />
      ),
    },
    {
      title: "可见范围",
      width: "160px",
      dataIndex: "visibleScopeDepartmentNameList",
      key: "visibleScopeDepartmentNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "获客渠道",
      width: "160px",
      dataIndex: "channelName",
      key: "channelName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "活码对象",
      width: "160px",
      dataIndex: "objectNameList",
      key: "objectNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "关联定位",
      width: "160px",
      dataIndex: "location",
      key: "location",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "备用客服",
      width: "160px",
      dataIndex: "backupObjectNameList",
      key: "backupObjectNameList",
      align: "center",
      render: (value, record, index) => value?.length > 0 && (value.join("，")),
    },
    {
      title: "更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.creatorName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDetails(record), name: "数据" },
          { onClick: () => handleIssue(record), name: "推送下发" },
        ];
        if (record.deleteAble) {
          opts.push({ onClick: () => handleDelete(record), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getcreateModeType();
    getLocationOption();
    getTagCategoryTreeTwo();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.updateTime;
      }
      if (formData.channelIds) {
        formData.channelIds = [formData.channelIds]
      }
      if (formData.locationIds) {
        formData.locationIds = [formData.locationIds]
      }
      // formData.tagIdList = formData.tagIdList?.join(",") || null;
      // formData.deptIdList = formData.deptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        types: formData.types ? [formData.types]: null
      };

      apiCall("/activity/dynamicCode/page", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getcreateModeType = () => {
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setCreateMode(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getLocationOption = () => {
    apiCall("/base/location/option", "GET")
      .then((res) => {
        setLocationOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeSwitchStatus = (checked, record) => {
    console.log(checked, record);
    const data = {
      id: record.id,
      state: checked,
    };
    apiCall("/activity/dynamicCode/changeStateById", "PUT", data)
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setShowSpinningTips(true);
      setLoading(true);
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.updateTime;
      }
      // formData.tagIdList = formData.tagIdList?.join(',') || null;
      // formData.deptIdList = formData.deptIdList?.join(',') || null;
      const data = {
        ...formData,
      };
      apiCall("/activity/dynamicCode/export", "POST", data, null, {
        isExit: true,
        title: `活码信息.${moment().format("YYYY-MM-DD")}.xlsx`,
      })
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setShowSpinningTips(false);
          setLoading(false);
        });
    });
  };

  const handleAdd = () => {
    props.history.push("/wecom/drivetraffic/liveCode/form");
  };

  const handleIssue = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "活码推送下发确认",
      content: `您将对活码【${name}】，通过企微应用通知推送下发给活码对象中的员工、群主，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id: id,
        };
        apiCall("/activity/dynamicCode/pushCode", "GET", data)
          .then((res) => {
            message.success("推送成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleDetails = (record) => {
    const { id, type } = record;
    props.history.push({
      pathname: "/wecom/drivetraffic/liveCode/details",
      search: `?id=${id}&type=${type}`,
    });
  };

  const handleEdit = (record) => {
    const { id, type } = record;
    props.history.push({
      pathname: "/wecom/drivetraffic/liveCode/form",
      search: `?id=${id}&type=${type}`,
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除活码【${name}】，删除后活码立即失效，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id,
        };
        apiCall("/activity/dynamicCode/del", "DELETE", data)
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="liveCode">
      <Spin
        spinning={loading}
        tip={showSpinningTips && "请耐心等待文件导出，预计需要30秒"}
      >
        <FilterBar bodyStyle={{ padding: "unset" }}>
          <Form layout={"inline"} ref={formRef}>
            <FormItem
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="活码名称" allowClear />
            </FormItem>
            <FormItem
              name="deptIdList"
              style={{
                minWidth: "unset",
                maxWidth: "205px",
                marginRight: "0px",
              }}
            >
              <ETypeTransferModal title="活码对象(员工、群主)" />
            </FormItem>
            <FormItem name="types">
              <SysDictSelect placeholder="活码类型" dataset="WECOM_CODE_TYPE" />
            </FormItem>
            {/* <FormItem name="tagIdList"> */}
            {/*   <TreeSelect */}
            {/*     treeData={labelTreeData} */}
            {/*     treeCheckable */}
            {/*     treeDefaultExpandedKeys={["customer"]} */}
            {/*     allowClear */}
            {/*     showArrow */}
            {/*     showSearch */}
            {/*     treeNodeFilterProp="title" */}
            {/*     maxTagCount="responsive" */}
            {/*     showCheckedStrategy={SHOW_PARENT} */}
            {/*     placeholder="客户标签" */}
            {/*   /> */}
            {/* </FormItem> */}
            <CustomTagSelect
              creatable
              name="tagNameList"
              placeholder="客户标签"
              useRefForm={formRef}
              labelTreeData={labelTreeData}
            />
            <FormItem name="channelIds">
              <Select
                placeholder="获客渠道"
                fieldNames={{ label: "name", value: "id" }}
                options={createMode}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.name ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
            <FormItem name="locationIds">
              <Select
                placeholder="关联定位"
                fieldNames={{ label: "name", value: "id" }}
                options={locationOption}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.name ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
            <FormItem name="createTime" label="创建时间">
              <RangePicker />
            </FormItem>
            <FormItem name="updateTime" label="更新时间">
              <RangePicker />
            </FormItem>
          </Form>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
            <div>
              <Button type="primary" onClick={() => handleExport()}>
                导出
              </Button>
              <Button type="primary" onClick={() => handleAdd()}>
                创建活码
              </Button>
            </div>
          </div>
        </FilterBar>
        <Card bordered={false} bodyStyle={{ padding: "unset" }}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
      <OperateModal params={operateParams} />
    </div>
  );
};

export default withRouter(LiveCode);
