/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2022/04/25 14:24
 * @LastEditTime: 2022/06/07 15:17
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\DriveTraffic\WelcomeSpeech\comps\LiveCodeFormModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Form, Input, Pagination, Spin, Card, Typography, Image, Empty, } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { QrCodeBase } from 'common/qrcode';
import {FileHOC} from 'components/FileHOC/FileHOC';
import './LiveCodeFormModal.less';

const FormItem = Form.Item;
const { Paragraph } = Typography;

const LiveCodeFormModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});

  useEffect(() => {
    if (props.params.visible) {
      setVisible(props.params.visible);
      let timer = setTimeout(() => {
        fetchList();
        clearTimeout(timer);
      }, 300);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: 'GROUP',
        ...query,
        ...formData,
      };

      apiCall('/activity/dynamicCode/page', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        const newRecords = records;
        newRecords.forEach((item) => {
          item.imageUrl = QrCodeBase({ url: item.url });
        });
        console.log(newRecords);
        setDataSource(newRecords);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    setSelect({ ...dataSource[index] });
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    console.log(select);
    select.isCode = true;
    props.params?.onSubmit?.(select);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择素材"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="LiveCodeFormModal"
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="keyWord" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入关键字" allowClear />
          </FormItem>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          {
            dataSource.length > 0 ? dataSource.map((item, index) => (
              <div className="picture-card" key={index} onClick={() => handleSelectScript(index)}>
                <FileHOC src={item.imageUrl}>
                  {(url) => (
                    <Image src={url} preview={false} />
                  )}
                </FileHOC>
                <Paragraph ellipsis={{ rows: 2 }}>{item.name}</Paragraph>
                {
                  item.id == select.id ? <div className="mask">
                    <CheckCircleTwoTone />
                  </div> : ''
                }
              </div>
            )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
          <Pagination
            {...paginations}
            onChange={onChangePagination}
          />
        </Card>
      </Spin>
    </Modal>
  );
};

export default LiveCodeFormModal;
