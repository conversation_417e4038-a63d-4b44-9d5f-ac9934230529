/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/24 18:12
 * @LastEditTime: 2025/05/14 09:39
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/WelcomeSpeech/form.jsx
 * @Description: '欢迎语编辑-新增'
 */

import React, { useEffect, useState, useRef } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Select,
  TreeSelect,
  message,
  Radio,
} from "antd"
import { apiCall } from "common/utils"
import { removeInputEmpty } from "common/regular"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import MaterialFormModal from "components/Modal/MaterialModal/home"
import LiveCodeFormModal from "./comps/LiveCodeFormModal"
import { findTreeItemByName, recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { qs2obj } from "common/object"
import MaterialListForm from "components/MaterialListForm/home"
import { clearCache } from "react-router-cache-route"
import { QrCodeBase } from "common/qrcode"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import "./form.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { SHOW_PARENT } = TreeSelect
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}

const WelcomeSpeechEdit = (props) => {
  const [welcomeSpeechform] = Form.useForm()
  const onRefMaterialListForm = useRef()
  const [id, setId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [existTagNameList, setExistTagNameList] = useState([])
  const [msgList, setMsgList] = useState([])
  const [formModalParams, setFormModalParams] = useState({ visible: false })
  const [liveCodeModalParams, setLiveCodeModalParams] = useState({
    visible: false,
  })
  const [typeIdOption, setTypeIdOption] = useState([])
  const [typeRadio, setTypeRadio] = useState("")
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [disableDeptList, setDisableDeptList] = useState([])
  const [previewList, setPreviewList] = useState([])
  const [employeeOptions, setEmployeeOptions] = useState([])
  const [groupOptions, setGroupOptions] = useState([])

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    getTagCategoryTreeTwo()
    getInfoTypeOptions()
    getWelcomeWordGroup()
    if (id) {
      setId(id)
      init(id)
    } else {
      getDisableDeptList({ type: "DEPARTMENT" })
      // getDefaultMiniItem();
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
  }, [])

  const init = async (id) => {
    const params = {
      welcomeWordId: id,
    }
    await fetchList(params)
  }

  const getDefaultMiniItem = () => {
    setLoading(true)
    apiCall("/welcomeWord/defaultMiniItem", "GET")
      .then((res) => {
        if (res) {
          onRefMaterialListForm.current.getInitMsgList([res])
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getInfoTypeOptions = async () => {
    const data = {
      type: "WELCOME",
    }
    await apiCall("/activity/type", "GET", data)
      .then((res) => {
        setTypeIdOption(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取禁用的使用对象
  const getDisableDeptList = async (params) => {
    setLoading(true)
    const { type, id } = params
    const data = {
      depOrEmployee: type,
      welcomeWordId: id,
    }
    await apiCall("/welcomeWord/disableDeptList", "GET", data)
      .then((res) => {
        setDisableDeptList(res)
        setTypeRadio(type)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { welcomeWordId } = params
    await apiCall(`/welcomeWord/${welcomeWordId}`, "GET")
      .then((res) => {
        const { type, itemList, depEmployeeIdList, tagIdList } = res
        welcomeSpeechform?.setFieldsValue({
          ...res,
          tagIdList: tagIdList?.map((item) => String(item)) || [],
          depEmployeeIdList,
        })
        // setTypeRadio(type);
        setExistTagNameList(res.tagNameList)
        getDisableDeptList({ id: welcomeWordId, type })
        onRefMaterialListForm.current.getInitMsgList(itemList)

      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ])

        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 欢迎语分组
  const getWelcomeWordGroup = async () => {
    await apiCall("/welcomeWord/welcomeWordGroup", "GET", {
      paged: false,
    })
      .then((res) => {
        setGroupOptions(res.records)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 选择部门或员工获取预览数据
  const handleDeptIdChange = (value, options = [], type = null) => {
    console.log(value, options)
    setLoading(true)
    if (type == "EMPLOYEE") {
      setEmployeeOptions(options)
    }
    const data = {
      deptId: type ? value[0] : value,
    }
    apiCall("/welcomeWord/superior", "GET", data)
      .then((res) => {
        const newItemList = res
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Poster" ||
            item.type == "QrCode"
          ) {
            item.image = item.fileId ? item.fileId[0] : ""
          }
        })
        setPreviewList(newItemList)
        if (type) {
          message.success("已在界面预览处显示上级部门欢迎语！")
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const removeDuplicateObj = (arr) => {
    // 缓存用于记录
    const newArr = []
    for (const t of arr) {
      // 检查缓存中是否已经存在
      if (newArr.find((c) => c.type == t.type) && t.type == "copyWriter") {
        // 已经存在说明以前记录过
        newArr.forEach((item) => {
          if (item.type == "copyWriter") {
            item.content += t.content
          }
        })
        continue
      }
      // 不存在就说明以前没遇到过，把它记录下来
      newArr.push(t)
    }

    // 记录结果就是过滤后的结果
    return newArr
  }

  const handlePreview = () => {
    setLoading(true)
    const depEmployeeIdList =
      welcomeSpeechform.getFieldValue("depEmployeeIdList")
    let data = {}
    if (typeRadio == "DEPARTMENT" && depEmployeeIdList?.length > 0) {
      data.deptId = depEmployeeIdList.join(",")
    } else if (typeRadio == "EMPLOYEE" && depEmployeeIdList?.length == 1) {
      data.deptId = depEmployeeIdList.join(",")
    }
    apiCall("/welcomeWord/superior", "GET", data)
      .then((res) => {
        const newItemList = res
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Article" ||
            item.type == "LINK_CARD" ||
            item.type == "Poster" ||
            item.type == "Product"
          ) {
            item.imageUrl = item.fileId ? item.fileId[0] : ""
          } else if (item.type == "QrCode") {
            item.imageUrl = QrCodeBase({ url: item.url })
          } else if (item.type == "Video") {
            item.videoUrl = item.fileId ? item.fileId[0] : ""
          }
        })
        setPreviewParams({
          visible: true,
          title: "欢迎语内容",
          dataSource: newItemList,
          onCancel: () => {
            setPreviewParams({ visible: false })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleSubmit = () => {
    welcomeSpeechform.validateFields().then((formData) => {
      if (!msgList.length > 0) {
        message.error("请至少添加一条欢迎语内容！")
        return
      }
      // else if (msgList.length > 9) {
      //   message.error('最多选择9条欢迎语内容！');
      //   return;
      // }
      if (onRefMaterialListForm.current.getCopyWriterValidate()) {
        // 校验资源文案字数
        return
      }
      setLoading(true)
      const { name, type, tagNameList, tagIdList, typeId, depEmployeeIdList, groupId } =
        formData
      const itemList = onRefMaterialListForm.current.getModifyMsgList()
      if (!id) {
        itemList.forEach((item, index) => {
          item.sort = index
        })
      }
      const data = {
        tagNameList,
        name,
        type,
        tagIdList,
        typeId,
        groupId,
        depEmployeeIdList: depEmployeeIdList,
        itemList,
      }
      const apiUrl = id ? `/welcomeWord/${id}` : "/welcomeWord"
      const apiMode = id ? "PUT" : "POST"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/drivetraffic")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="WelcomeSpeechEdit">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑欢迎语" : "创建欢迎语"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <Form {...layout} form={welcomeSpeechform}>
                <FormItem
                  label="欢迎语名称"
                  name="name"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入活码名称" }]}
                >
                  <Input placeholder="请输入欢迎语名称" allowClear />
                </FormItem>
                <FormItem
                  label="欢迎语分组"
                  name="groupId"
                  rules={[{ required: true, message: "请选择欢迎语分组" }]}
                >
                  <Select
                    placeholder="欢迎语分组"
                    fieldNames={{ label: "name", value: "id" }}
                    options={groupOptions}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                <FormItem
                  name="type"
                  label="欢迎语类型"
                  initialValue={"DEPARTMENT"}
                  wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                  extra={
                    <div>
                      员工欢迎语：客户通过非活码方式成功添加员工好友时触发，发送时员工欢迎语默认包含上级部门欢迎语
                      <br />
                      部门欢迎语：客户通过非活码方式成功添加员工好友、且员工没有配置欢迎语时触发，创建后会自动逐级被下级沿用
                    </div>
                  }
                >
                  <Radio.Group
                    onChange={(e) => {
                      welcomeSpeechform.setFieldsValue({
                        depEmployeeIdList: [],
                      })
                      getDisableDeptList({ id, type: e.target.value })
                      setEmployeeOptions([])
                      setPreviewList([])
                    }}
                  >
                    <Radio value="DEPARTMENT">部门欢迎语</Radio>
                    <Radio value="EMPLOYEE">员工欢迎语</Radio>
                  </Radio.Group>
                </FormItem>
                {typeRadio == "DEPARTMENT" && (
                  <FormItem
                    label="使用对象"
                    name="depEmployeeIdList"
                    rules={[{ required: true, message: "请选择使用对象" }]}
                    extra="仅支持为一个部门创建欢迎语"
                  >
                    <ETypeTransferModal
                      title="选择部门"
                      onlyDepartment
                      onlyEmployee={false}
                      multiple={false}
                      disable={disableDeptList}
                      onChange={(value, options) => {
                        handleDeptIdChange(value, options, "DEPARTMENT")
                      }}
                    />
                  </FormItem>
                )}
                {typeRadio == "EMPLOYEE" && (
                  <FormItem
                    label="使用对象"
                    name="depEmployeeIdList"
                    rules={[{ required: true, message: "请选择使用对象" }]}
                    extra="支持为一个或多个员工创建欢迎语"
                  >
                    <ETypeTransferModal
                      title="选择员工"
                      onlyDepartment={false}
                      onlyEmployee
                      multiple
                      disable={disableDeptList}
                      onChange={(value, options) => {
                        handleDeptIdChange(value, options, "EMPLOYEE")
                      }}
                    />
                  </FormItem>
                )}
                {/* <FormItem */}
                {/*   label="客户标签" */}
                {/*   name="tagIdList" */}
                {/*   style={{ marginTop: "10px" }} */}
                {/* > */}
                {/*   <TreeSelect */}
                {/*     treeData={labelTreeData} */}
                {/*     treeCheckable */}
                {/*     treeDefaultExpandedKeys={["customer"]} */}
                {/*     allowClear */}
                {/*     showArrow */}
                {/*     showSearch */}
                {/*     treeNodeFilterProp="title" */}
                {/*     maxTagCount="responsive" */}
                {/*     showCheckedStrategy={SHOW_PARENT} */}
                {/*     placeholder="客户标签" */}
                {/*     getPopupContainer={(triggerNode) => triggerNode.parentNode} */}
                {/*   /> */}
                {/* </FormItem> */}
                <CustomTagSelect
                  label="客户标签"
                  name="tagNameList"
                  placeholder="客户标签"
                  useForm={welcomeSpeechform}
                  existTagNameList={existTagNameList}
                  labelTreeData={labelTreeData}
                />
                <FormItem
                  label="欢迎语"
                  className="formBtn"
                  wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                >
                  <>
                    <div className="WelcomeSpeech-tips">
                      您的欢迎语会随上级部门欢迎语一同发，客户最多接收到前1个文案9个附件，且文案超长时会被截断
                      {/* <span
                        className="checkContents"
                        onClick={() => {
                          handlePreview();
                        }}
                      >
                        查看上级部门欢迎语
                      </span> */}
                    </div>
                    <MaterialListForm
                      params={{
                        formRef: welcomeSpeechform,
                        menuList: ["image", "material", "liveCode"],
                        needScriptFlag: true,
                        materialAmount: msgList.length,
                        previewList: previewList,
                        extra: (
                          <div>
                            请按顺序添加预计要发送的欢迎语，还可添加{" "}
                            {10 - msgList.length} 条<br />
                            选择素材后，参考话术将会自动添加在唯一的文案中
                          </div>
                        ),
                      }}
                      // 监听回调
                      callback={(params) => {
                        setMsgList(params.data)
                      }}
                      ref={onRefMaterialListForm}
                    />
                  </>
                </FormItem>
                <FormItem wrapperCol={{ offset: 8 }}>
                  <Button type="primary" onClick={() => handleSubmit()}>
                    确定
                  </Button>
                </FormItem>
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              {employeeOptions.length > 0 && (
                <FormItem label="使用对象">
                  <Select
                    placeholder="使用对象"
                    options={employeeOptions}
                    defaultValue={employeeOptions[0].value}
                    allowClear
                    showSearch
                    fieldNames={{ label: "name", value: "id" }}
                    filterOption={(input, option) =>
                      (option?.name ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    onSelect={(value) => {
                      handleDeptIdChange(value)
                    }}
                  />
                </FormItem>
              )}

              <WibotMaterialPreview
                type="session"
                mergeFlag={true}
                listData={[...previewList, ...msgList]}
              />
            </Col>
          </Row>
        </Card>
        <MaterialFormModal params={formModalParams} />
        <LiveCodeFormModal params={liveCodeModalParams} />
      </Spin>
    </div>
  )
}

export default WelcomeSpeechEdit
