/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/24 16:32
 * @LastEditTime: 2025/05/14 09:49
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/DriveTraffic/WelcomeSpeech/home.jsx
 * @Description: ''
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  TreeSelect,
  DatePicker,
  Select,
  message,
} from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import moment from "moment"
import FilterBar from "components/FilterBar/FilterBar"
import OperateModal from "components/Modal/OperateModal/index"
import { QrCodeBase } from "common/qrcode"
import CodeGroupModal from "./comps/CodeGroupModal"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotTableTag from "components/WibotTableTag/home"
import ListOperation from "components/ListOperation/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { SHOW_PARENT } = TreeSelect
const { RangePicker } = DatePicker
const { Option } = Select

const WelcomeSpeech = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [typeNames, setTypeNames] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [groupOptions, setGroupOptions] = useState([])
  const [codeGroupParams, setCodeGroupParams] = useState({ visible: false })
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "欢迎语名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "欢迎语内容",
      width: "160px",
      dataIndex: "content",
      key: "content",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: "欢迎语类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <Tooltip
          placement="topLeft"
          title={value == "DEPARTMENT" ? "部门欢迎语" : "员工欢迎语"}
        >
          {value == "DEPARTMENT" ? "部门欢迎语" : "员工欢迎语"}
        </Tooltip>
      ),
    },
    {
      title: "使用对象",
      width: "160px",
      dataIndex: "nameList",
      key: "nameList",
      align: "center",
      render: (value, record, index) => (
        <div>
          <WibotTableTag tagList={value} />
          <div>涉及员工：{record.involvedEmployeeCount}</div>
        </div>
      ),
    },
    {
      title: "发送情况",
      width: "160px",
      dataIndex: "case",
      key: "case",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          使用员工：{record.employeeCount}
          <br />
          送达客户：{record.customerCount}
        </div>
      ),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "欢迎语分组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "更新人/更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
          // { onClick: () => handleRemind(record), name: "提醒" },
        ]
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    getInfoTypeOptions()
    getTagCategoryTreeTwo()
    getWelcomeWordGroup()
    fetchList()
  }, [])

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.createTime
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.updateTime
      }
      // formData.tagIdList = formData.tagIdList?.join(',') || null;
      // formData.deptIdList = formData.deptIdList?.join(',') || null;
      // formData.createDeptIdList = formData.createDeptIdList?.join(',') || null;
      // formData.updateDeptIdList = formData.updateDeptIdList?.join(',') || null;
      // formData.groupIdList = formData.groupIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }

      apiCall('/welcomeWord/page', 'POST', data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const getInfoTypeOptions = async () => {
    const data = {
      type: "WELCOME",
    }
    await apiCall("/activity/type", "GET", data)
      .then((res) => {
        setTypeNames(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }
  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 欢迎语分组
  const getWelcomeWordGroup = async () => {
    await apiCall("/welcomeWord/welcomeWordGroup", "GET", {
      paged: false,
    })
      .then((res) => {
        setGroupOptions(res.records)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const handlePreview = (record) => {
    const { id } = record
    setLoading(true)
    apiCall(`/welcomeWord/preview/${id}`, "GET")
      .then((res) => {
        const { itemList } = res
        const newItemList = itemList
        newItemList?.forEach((item) => {
          if (
            item.type == "Picture" ||
            item.type == "Article" ||
            item.type == "LINK_CARD" ||
            item.type == "Poster" ||
            item.type == "Product"
          ) {
            item.imageUrl = item.fileId ? item.fileId[0] : ""
          } else if (item.type == "QrCode") {
            item.imageUrl = QrCodeBase({ url: item.url })
          } else if (item.type == "Video") {
            item.videoUrl = item.fileId ? item.fileId[0] : ""
          }
        })
        setResourcePreviewParams({
          visible: true,
          listData: newItemList,
          type: "session",
          onCancel: () => {
            setResourcePreviewParams({
              visible: false,
            })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 分组管理
  const handleManage = () => {
    setCodeGroupParams({
      visible: true,
      onCancel: () => {
        setCodeGroupParams({ visible: false })
        getWelcomeWordGroup()
      },
    })
  }

  const handleAdd = () => {
    props.history.push("/wecom/drivetraffic/welcomeSpeech/form")
  }

  const handleEdit = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/drivetraffic/welcomeSpeech/form",
      search: `?id=${id}`,
    })
  }

  const handleRemind = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "欢迎语推送提醒确认",
      content: `您将对欢迎语【${name}】，通过企微应用通知推送下发给欢迎语使用的的员工，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/welcomeWord/remind/${id}`, "POST")
          .then((res) => {
            message.success("推送成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleDelete = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除欢迎语【${name}】，删除后欢迎语立即失效，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/welcomeWord/${id}`, "DELETE")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="WelcomeSpeech">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="欢迎语名称" allowClear />
          </FormItem>
          <FormItem name="type">
            <Select placeholder="欢迎语类型" allowClear>
              <Option value="EMPLOYEE">员工欢迎语</Option>
              <Option value="DEPARTMENT">部门欢迎语</Option>
            </Select>
            {/* <Select
              allowClear
              options={typeNames}
              placeholder="欢迎语类型"
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            /> */}
          </FormItem>
          {/* <FormItem name="tagIdList"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={['customer']} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            labelTreeData={labelTreeData}
          />
          <FormItem name="groupIdList">
            <Select
              placeholder="欢迎语分组"
              fieldNames={{ label: "name", value: "id" }}
              options={groupOptions}
              allowClear
              showSearch
              showArrow
              mode="multiple"
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem
            name="deptIdList"
            style={{
              minWidth: "unset",
              maxWidth: "200px",
              marginRight: "0px",
            }}
          >
            <ETypeTransferModal title="使用对象" />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{
              minWidth: "unset",
              maxWidth: "200px",
              marginRight: "0px",
            }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem
            name="updateDeptIdList"
            style={{
              minWidth: "unset",
              maxWidth: "200px",
              marginRight: "0px",
            }}
          >
            <ETypeTransferModal title="更新人" />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleManage()}>
              分组管理
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              创建欢迎语
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          loading={loading}
          rowKey="id"
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>

      <OperateModal params={operateParams} />
      <CodeGroupModal params={codeGroupParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default withRouter(WelcomeSpeech)
