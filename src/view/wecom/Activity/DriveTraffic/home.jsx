/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/20 15:47
 * @LastEditTime: 2023/08/28 09:52
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\DriveTraffic\home.jsx
 * @Description: ''
 */

import React, {useEffect, useState} from 'react';
import {Card, Tabs} from 'antd';
// 模块组件
import LiveCode from './LiveCode/home';
import WelcomeSpeech from './WelcomeSpeech/home';
import EmployeeCode from './EmployeeCode/home';
import {isV1} from 'config'

const { TabPane } = Tabs;

const Activity = (props) => {

  const [tabsIndex, setTabsIndex] = useState(localStorage.getItem('TabsDriveTrafficActiveKey') || (isV1() ? '1' : '3'));

  useEffect(() => {
    console.log(`[tabsIndex]: `, tabsIndex)
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('TabsDriveTrafficActiveKey', index);
    setTabsIndex(index);
  };

  return (
    <div className='activity'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          {
            isV1() ?
                <>
                  <TabPane tab="渠道活码" key="1">
                    <LiveCode/>
                  </TabPane>
                  <TabPane tab="欢迎语" key="2">
                    <WelcomeSpeech/>
                  </TabPane>
                  <TabPane tab="员工活码" key="3">
                    <EmployeeCode/>
                  </TabPane>
                </> :
                <>
                  <TabPane tab="员工活码" key="3">
                    <EmployeeCode/>
                  </TabPane>
                </>
          }
        </Tabs>
      </Card>
    </div>
  );
};

export default Activity;
