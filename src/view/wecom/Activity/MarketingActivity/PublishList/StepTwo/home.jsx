/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/20 14:47
 * @LastEditTime: 2022/12/23 17:50
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingActivity\PublishList\StepTwo\home.jsx
 * @Description: '步骤二'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import {
  Switch, Checkbox, Form, Input, InputNumber
} from 'antd';

const FormItem = Form.Item;
const { TextArea } = Input;

const StepTwo = forwardRef((props, ref) => {
  const { handlePreviewChange } = props;

  useImperativeHandle(ref, () => ({
    getInitDataById,
  }))

  const [isChecked, setIsChecked] = useState(false);
  const materialOptions = [
    {
      label: '图片', value: 1
    },
    {
      label: '视频', value: 2
    },
    {
      label: '链接', value: 3
    },
    {
      label: '文件', value: 4
    }
  ];

  useEffect(() => {

  }, []);

  const getInitDataById = (data) => {
    setIsChecked(data.repeatable);
  }

  return (
    <div className='StepTwo'>
      <h2 className='card-title'>晒单设置</h2>
      <FormItem name={["shareSetting", "demand"]} label="晒单要求" rules={
        [{ required: true, message: '请输入晒单要求', }]}
      >
        <TextArea placeholder="请输入晒单要求（300字内）" allowClear showCount maxLength={300} autoSize={{ minRows: 2, maxRows: 7 }}
          className='textArea-mid' onChange={(e) => {
            handlePreviewChange({ demand: e.target.value });
          }} />
      </FormItem>
      <FormItem label="晒单素材" name="material" rules={[{ required: true, message: '请选择晒单素材' }]}>
        <Checkbox.Group options={materialOptions} onChange={(checked) => {
          handlePreviewChange({ materialOptions: checked });
        }} />
      </FormItem>
      <FormItem label="重复晒单" required extra="开启后，客户可多次晒单，关闭时客户只能晒单1次">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FormItem name={["shareSetting", "repeatable"]} valuePropName="checked" style={{ marginBottom: '0px', marginRight: '5px' }}>
            <Switch onChange={(checked) => {
              setIsChecked(checked);
            }} />
          </FormItem>
          {
            isChecked && <>
              <span>每人每天最多</span>
              <FormItem
                rules={[{ required: true, message: '请输入' }]}
                name={["shareSetting", "frequency"]} style={{ margin: '0 5px', display: 'inline-block' }}>
                <InputNumber min={0} />
              </FormItem>
              <span>次</span>
            </>
          }
        </div>
      </FormItem>
    </div>
  );
});

export default StepTwo;
