/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 17:40
 * @LastEditTime: 2022/12/28 17:41
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingActivity\PublishList\detail\TabDetailsFour.jsx
 * @Description: '详情-日期统计'
 */

import React, { useEffect, useState } from 'react';
import { Card, Table, Tooltip } from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { qs2obj } from 'common/object';
import moment from 'moment';

const TabDetailsFour = (props) => {
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '日期',
      width: '160px',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      sorter: (a, b) => timeStamp(a.date) - timeStamp(b.date)
    },
    {
      title: '晒单人数',
      width: '160px',
      dataIndex: 'activityCustomerCount',
      key: 'activityCustomerCount',
      align: 'center',
    },
    {
      title: '晒单次数',
      width: '160px',
      dataIndex: 'activityCount',
      key: 'activityCount',
      align: 'center',
    },
    {
      title: '访问次数',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
    },
    {
      title: '访问人数',
      width: '160px',
      dataIndex: 'visitorCount',
      key: 'visitorCount',
      align: 'center',
    }
  ];

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { detailId, pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      id: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query
    };

    apiCall('/activity/activity/detail/data/date', 'GET', data).then((res) => {
      const { records, current, size, total, pages } = res;
      setDataSource(records);
      setPaginations({
        current: current,
        pageSize: size,
        total: total,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='TabDetailsFour'>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="date" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default TabDetailsFour;
