/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 17:18
 * @LastEditTime: 2023/11/17 10:47
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/PublishList/detail/TabDetailsOne.jsx
 * @Description: '详情-晒单记录'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
  Image,
  Typography,
  message,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import { saveAs } from "file-saver";
import moment from "moment";
import {FileHOC} from 'components/FileHOC/FileHOC';
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;

const TabDetailsOne = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "晒单时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "晒单文案",
      width: "160px",
      dataIndex: "shareBody",
      key: "shareBody",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value} >
          <Paragraph ellipsis={{ rows: 3 }}>{value}</Paragraph>
        </Tooltip>
      ),
    },
    {
      title: "晒单素材",
      width: "250px",
      dataIndex: "shareFileUrlList",
      key: "shareFileUrlList",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          {record.shareType == "LINK" && value?.length > 0 && (
            <Tooltip placement="topLeft" title={value[0]}>{value[0]}</Tooltip>
          )}
          {record.shareType == "FILE" && value?.length > 0 && (
            <a
              onClick={() => {
                saveAs(value[0], unescapeFile(value[0]));
              }}
            >
              {unescapeFile(value[0])}
            </a>
          )}
          {record.shareType == "VIDEO" && (
            <FileHOC src={value && value.length > 0 ? value[0] : ""}>
              {(url) => (
                <video
                  style={{ maxWidth: "200px", maxHeight: "100px" }}
                  controls
                  src={url}
                />
              )}
            </FileHOC>
          )}
          {record.shareType == "IMAGE" && (
            <>
              {value?.map((item, index) => (
                <FileHOC src={item || "error"}>
                  {(url) => (
                    <Image
                    key={index}
                    width={60}
                    height={60}
                    src={url}
                    fallback="images/fallbackImg.png"
                    style={{ objectFit: "cover" }}
                    preview
                  />
                  )}
                </FileHOC>
              ))}
            </>
          )}
        </div>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { detailId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        id: detailId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/activity/detail/data/share/log", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 编码文件链接中的中文
  const unescapeFile = (url) => {
    const reg = /[\u4e00-\u9fa5]/g;
    if (url.match(reg)) {
      const texts = url.match(reg).join("");
      const index = url.indexOf(texts);
      return url.substring(index, url.length);
    } else {
      const index = url.lastIndexOf("/");
      return url.substring(index + 1, url.length);
    }
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customerId}`,
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleExport = () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const data = {
        id: id,
        ...formData,
      };
      apiCall(
        "/activity/activity/detail/data/share/log/export",
        "POST",
        data,
        null,
        {
          isExit: true,
          title: `${props.userInfo.title}.晒单记录.${moment().format(
            "YYYY-MM-DD"
          )}.xlsx`,
        }
      )
        .then((res) => {
          message.success("导出成功！");
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <div className="TabDetailsOne">
      <Form layout={"inline"} ref={formRef}>
        <FormItem
          name="customerName"
          label="名称"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="名称" allowClear />
        </FormItem>
        <FormItem name="time" label="晒单时间">
          <RangePicker />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button style={{ marginRight: "20px" }} onClick={() => handleReset()}>
            重置筛选
          </Button>
          <Button type="primary" onClick={() => handleExport()}>
            导出
          </Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 800 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(TabDetailsOne);
