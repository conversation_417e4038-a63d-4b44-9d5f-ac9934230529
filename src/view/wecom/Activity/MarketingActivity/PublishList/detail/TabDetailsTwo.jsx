/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 17:23
 * @LastEditTime: 2023/11/17 10:47
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/PublishList/detail/TabDetailsTwo.jsx
 * @Description: '详情-客户统计'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const TabDetailsTwo = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "晒单次数",
      width: "160px",
      dataIndex: "activityCount",
      key: "activityCount",
      align: "center",
    },
    {
      title: "访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "最近晒单时间",
      width: "160px",
      dataIndex: "lastCommitTime",
      key: "lastCommitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastCommitTime) - timeStamp(b.lastCommitTime),
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { detailId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        id: detailId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/activity/detail/data/share/customer", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customerId}`,
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsTwo">
      <Form layout={"inline"} ref={formRef}>
        <FormItem
          name="customerName"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="客户名称" label="客户名称" allowClear />
        </FormItem>
        <FormItem name="time" label="访问时间">
          <RangePicker />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="customerId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(TabDetailsTwo);
