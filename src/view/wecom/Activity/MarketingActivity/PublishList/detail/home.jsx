/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/20 11:50
 * @LastEditTime: 2024/10/22 09:28
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/PublishList/detail/home.jsx
 * @Description: '晒单详情'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Spin, Button, Card, Tooltip, Row, Col, Empty, Space, Image, Tabs, Form, DatePicker, Select, Tag } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { getDay } from 'common/date';
import moment from 'moment';
import { QrCodeBase } from 'common/qrcode';
import { saveAs } from 'file-saver';
import { qs2obj } from 'common/object';
import { DualAxes, Line } from '@ant-design/plots';
import SysDictLabel from 'components/select/SysDictLabel';
import TabDetailsOne from './TabDetailsOne';
import TabDetailsTwo from './TabDetailsTwo';
import TabDetailsThree from './TabDetailsThree';
import TabDetailsFour from './TabDetailsFour';
import TabDetailsFive from './TabDetailsFive';
import WibotCopyBtn from 'components/WibotCopyBtn/home';
import {FileHOC} from 'components/FileHOC/FileHOC';
const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const MarketingActivityDetails = (props) => {
  const [formForm] = Form.useForm();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState('1');
  const [dataTrendVO, setDataTrendVO] = useState({});
  const [tabsTrendIndex, setTabsTrendIndex] = useState('ACTIVITY_CUSTOMER_COUNT');
  const [lineData, setLineData] = useState({});
  const [dualAxesData, setDualAxesData] = useState([]);
  const configLine = {
    data: lineData.data,
    height: 300,
    xField: 'date',
    yField: 'number',
    label: {},
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    tooltip: {
      showMarkers: false,
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: '#000',
          fill: 'red',
        },
      },
    },
    interactions: [
      {
        type: 'marker-active',
      },
    ],
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: true,
        rotate: -45, //倾斜角度
        offset: "20",
        formatter: function (value) {
          if (value.length > 4) {
            return value.substring(5)
            return `${value.slice(0, 4)}...`;
          }
          return value;
        }
      },
    },
    meta: {
      number: {
        alias: lineData.title,
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
  };
  const configDualAxes = {
    data: [dualAxesData, dualAxesData],
    xField: 'channelName',
    yField: ['visitorCount', 'visitCount'],
    height: 300,
    legend: {
      position: 'top'
    },
    meta: {
      visitorCount: {
        alias: '访问人数',
      },
      visitCount: {
        alias: '访问次数',
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
    geometryOptions: [
      {
        geometry: 'line',
        color: '#5B8FF9',
      },
      {
        geometry: 'line',
        color: '#5AD8A6',
      },
    ],
  };

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ id });
    await getDataTrendVO(id);
    await getDataTrendChart({ detailId: id, tabIdx: 'ACTIVITY_CUSTOMER_COUNT' });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    await apiCall(`/activity/activity/${id}`, 'GET').then((res) => {
      setUserInfo(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 数据趋势数据
  const getDataTrendVO = async (id) => {
    setLoading(true);
    await apiCall(`/activity/activity/detail/dataTrend/number?activityId=${id}`, 'GET').then((res) => {
      setDataTrendVO(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 数据趋势图
  const getDataTrendChart = async (params = {}) => {
    setLoading(true);
    const { detailId, time, tabIdx = null } = params;
    const tabType = tabIdx || tabsTrendIndex;
    const data = {
      id: detailId || id,
      type: tabType,
      startDate: time && time.length > 0 ? time[0] : moment(getDay(-30)).format('YYYY-MM-DD'),
      endDate: time && time.length > 0 ? time[1] : moment(getDay(-1)).format('YYYY-MM-DD')
    };
    const apiUrl = (tabType == 'CHANNEL_COUNT') ? '/activity/activity/detail/dataTrend/chart/channel' : '/activity/activity/detail/dataTrend/chart';
    await apiCall(apiUrl, 'GET', data).then((res) => {
      (tabType == 'CHANNEL_COUNT') ? setDualAxesData(res) : getTabEcharts(tabType, res.list);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabEcharts = (type, data) => {
    let title = '';
    switch (type) {
      case 'ACTIVITY_CUSTOMER_COUNT':
        if (data) {
          title = '晒单人数';
        }
        break;
      case 'ACTIVITY_COUNT':
        if (data) {
          title = '晒单次数';
        }
        break;
      case 'NEW_CUSTOMER':
        if (data) {
          title = '新增人数';
        }
        break;
      case 'VISITOR_COUNT':
        if (data) {
          title = '访问人数';
        }
        break;
      case 'VISIT_COUNT':
        if (data) {
          title = '访问次数';
        }
        break;
      case 'CHANNEL_COUNT':
        if (data) {
          title = '推广渠道';
        }
        break;
    }
    setLineData({ data, title });
  };

  const onChangeTabsDataTrend = (type) => {
    const time = formForm.getFieldValue('time');
    setTabsTrendIndex(type);
    getDataTrendChart({ tabIdx: type, time: time && [time[0].format('YYYY-MM-DD'), time[1].format('YYYY-MM-DD')] });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
      case '0':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(value), 'YYYY-MM-DD')];
        getDataTrendChart({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(value)).format('YYYY-MM-DD')] });
        break;
      case '-1':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(value), 'YYYY-MM-DD')];
        getDataTrendChart({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(value)).format('YYYY-MM-DD')] });
        break;
      case '-7':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        getDataTrendChart({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
      case '-15':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        getDataTrendChart({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
      case '-30':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        getDataTrendChart({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
    }
    if (value) {
      await formForm.setFieldsValue({
        time
      });
    }
  };

  const onChangeTime = async (date, dateString) => {
    getDataTrendChart({ time: date ? dateString : null });
    formForm.setFieldsValue({
      quickTime: null
    });
  };

  const getShareMaterial = () => {
    if (userInfo && userInfo.shareSetting) {
      const obj = userInfo.shareSetting;
      let content = [];
      obj.file && content.push('文件');
      obj.picture && content.push('图片');
      obj.video && content.push('视频');
      obj.link && content.push('链接');
      return content.join('、');
    }
  };

  const handleDownloadImg = () => {
    saveAs(QrCodeBase({ url: userInfo.transitUrl }), '活码');
  };

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='MarketingActivityDetails'>
      <Spin spinning={loading}>
        <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} title="活动详情(晒单)" bordered={false} bodyStyle={{ display: 'none' }}></Card>
        <br />
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={9} >
            <Card title="活动信息" className="MA-card">
              {
                JSON.stringify(userInfo) != '{}' ? <>
                  <Row gutter={16} className="header-box">
                    <Col xs={24} xl={16} className='info'>
                      <div style={{ marginBottom: '10px' }}>
                        <span className='groupName' style={{ marginRight: '10px' }}> {userInfo.title}</span>
                        {
                          userInfo.shelfState && <Tag style={{ borderColor: userInfo.shelfState == 'Added' && '#70b603', color: userInfo.shelfState == 'Added' && '#70b603' }}>
                            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={userInfo.shelfState} />
                          </Tag>
                        }
                      </div>
                      {userInfo.createEmployeeName && userInfo.createTime ? <Space wrap>创建：{userInfo.createEmployeeName && <span>{userInfo.createEmployeeName}&nbsp;&nbsp;</span>}{userInfo.createTime}</Space> : ''}
                      {userInfo.updateEmployeeName && userInfo.updateTime ? <Space wrap>更新：{userInfo.updateEmployeeName && <span>{userInfo.updateEmployeeName}&nbsp;&nbsp;</span>}{userInfo.updateTime}</Space> : ''}
                      {userInfo.state == 'STOP' && userInfo.stopEmployeeName && userInfo.stopTime ? <Space wrap>停用：{userInfo.stopEmployeeName && <span>{userInfo.stopEmployeeName}&nbsp;&nbsp;</span>}{userInfo.stopTime}</Space> : ''}
                      <Space wrap>推广渠道： <span>
                        {
                          userInfo.channelNameList.map((item, index) => (
                            <span key={index}>{item}{index != userInfo.channelNameList?.length - 1 && '、'}</span>
                          ))
                        }
                      </span>
                      </Space>
                    </Col>
                    <Col span={8}>
                      <div className='codeBox'>
                        <FileHOC src={QrCodeBase({ url: userInfo.transitUrl || '' })}>
                          {(url) => (
                            <Image
                            preview={false}
                            width={120}
                            src={url}
                          />
                          )}
                        </FileHOC>
                        <div className='btn'>
                          <a onClick={() => { handleDownloadImg(); }}>下载活码</a>
                          <WibotCopyBtn text={userInfo.transitUrl} title="复制链接" />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }

              {
                JSON.stringify(userInfo) != '{}' ? <>
                  <h2>基础设置</h2>
                  <Space wrap>活动时间：{userInfo.activityStartTime}至{userInfo.activityEndTime}</Space>
                  <Space wrap>上下架时间：{userInfo.startTime}至{userInfo.endTime}</Space>
                  <Space wrap>活动描述：{userInfo.description}</Space>
                  <Space wrap>客户标签： <span>
                    {
                      userInfo.customerTagList.map((item, index) => (
                        <Tag key={index} style={{ marginRight: '5px' }}>{item.name}</Tag>
                      ))
                    }
                  </span>
                  </Space>
                  <Space wrap>客服活码：{userInfo.dynamicCodeName}</Space>
                  <Space wrap>活动规则：{userInfo.rule}</Space>
                </> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }

              <h2 style={{ marginTop: '5px' }}>晒单设置</h2>
              <Space wrap>晒单要求：{userInfo.shareSetting?.demand}</Space>
              <Space wrap>晒单内容：{getShareMaterial()}</Space>
              <Space wrap>重复晒单：{userInfo.shareSetting?.repeatable ? `每人每天最多${userInfo.shareSetting?.frequency}次` : '每人每天最多1次'}</Space>
            </Card>
          </Col>
          <Col xs={24} lg={15}  >
            <div style={{ height: '100%', overflow: 'hidden' }}>
              <Card title="数据总览" className='data-screening' style={{ height: 'unset', marginBottom: '10px' }}>
                {
                  JSON.stringify(dataTrendVO) != '{}' ? <Row gutter={16}>
                    <Col span={6}>
                      <span className='num'>{dataTrendVO.activityCustomerCount || 0}</span>
                      <Tooltip title="晒单的人数，包含已加好友及未加好友的访客">
                        <p className='tip'>累计晒单人数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日晒单人数：{dataTrendVO.activityCustomerCountToday}</p>
                    </Col>
                    <Col span={6}>
                      <span className='num'>{dataTrendVO.activityCount || 0}</span>
                      <Tooltip title="晒单的人次，包含已加好友及未加好友的访客">
                        <p className='tip'>累计晒单次数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日晒单次数：{dataTrendVO.activityCountToday}</p>
                    </Col>
                    <Col span={6}>
                      <span className='num'>{dataTrendVO.newCustomerCount || 0}</span>
                      <Tooltip title="通过联系客服的方式成功添加的客户，不包含流失客户">
                        <p className='tip'>累计新增人数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日新增人数：{dataTrendVO.newCustomerCountToday}</p>
                    </Col>
                    <Col span={6}>
                      <span className='num'>{dataTrendVO.visitorCount || 0}</span>
                      <Tooltip title="同一用户访问多次算一次">
                        <p className='tip'>累计访问人数<QuestionCircleOutlined /></p>
                      </Tooltip>
                      <p className='visit'>今日访问人数：{dataTrendVO.visitorCountToday}</p>
                    </Col>
                  </Row> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                }
              </Card>
              <Card bordered={false} title="数据趋势" style={{ height: 'unset', marginBottom: '10px' }} >
                <Tabs activeKey={tabsTrendIndex} destroyInactiveTabPane onChange={onChangeTabsDataTrend}>
                  <TabPane tab="晒单人数" key="ACTIVITY_CUSTOMER_COUNT"></TabPane>
                  <TabPane tab="晒单次数" key="ACTIVITY_COUNT"></TabPane>
                  <TabPane tab="新增人数" key="NEW_CUSTOMER"></TabPane>
                  <TabPane tab="访问人数" key="VISITOR_COUNT"></TabPane>
                  <TabPane tab="访问次数" key="VISIT_COUNT"> </TabPane>
                  <TabPane tab="推广渠道" key="CHANNEL_COUNT"></TabPane>
                </Tabs>
                <Form layout={'inline'} form={formForm}>
                  <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                    <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                  </FormItem>
                  <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                    <Select
                      style={{ width: '100px' }}
                      options={[
                        {
                          label: '今天',
                          value: '0'
                        },
                        {
                          label: '昨天',
                          value: '-1'
                        },
                        {
                          label: '最近7天',
                          value: '-7'
                        },
                        {
                          label: '最近15天',
                          value: '-15'
                        },
                        {
                          label: '最近30天',
                          value: '-30'
                        },
                      ]}
                      onChange={onChangeQuickTime}
                    />
                  </FormItem>
                </Form>
                {(lineData.data?.length > 0 && tabsTrendIndex != 'CHANNEL_COUNT') && <Line {...configLine} />}
                {(dualAxesData.length > 0 && tabsTrendIndex == 'CHANNEL_COUNT') && <DualAxes {...configDualAxes} />}
                {(!dualAxesData.length > 0 && tabsTrendIndex == 'CHANNEL_COUNT') && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
              </Card>
              <Card bordered={false} title="数据明细" style={{ height: '100%' }}>
                <Tabs activeKey={tabsDetailsIndex} destroyInactiveTabPane onChange={onChangeTabsDataDetails}>
                  <TabPane tab="晒单记录" key="1">
                    <TabDetailsOne userInfo={userInfo} />
                  </TabPane>
                  <TabPane tab="客户统计" key="2">
                    <TabDetailsTwo />
                  </TabPane>
                  <TabPane tab="新增客户" key="3">
                    <TabDetailsThree />
                  </TabPane>
                  <TabPane tab="日期统计" key="4">
                    <TabDetailsFour />
                  </TabPane>
                  <TabPane tab="推广渠道" key="5">
                    <TabDetailsFive />
                  </TabPane>
                </Tabs>
              </Card>
            </div>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default withRouter(MarketingActivityDetails);
