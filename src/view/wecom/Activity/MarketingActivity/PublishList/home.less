.MarketingActivityPublishList {
  .WibotMobilePreview-Container {
    .content {
      margin: 0;
      padding: 0;
    }
    .back {
      position: absolute;
      cursor: pointer;
      top: 11px;
      left: 14px;
      font-size: 18px;
      color: #fff;
    }

    .body {
      position: relative;
      top: 0;
      left: 0;

      .content_bg {
        width: 100%;
        height: 100%;
      }

      .bg {
        width: 100%;
        object-fit: contain;
      }

      .content-text {
        position: absolute;
        top: 320px;
        left: 50%;
        transform: translate(-50%);
        font-size: 14px;
        color: #fff;
      }

      .content-top {
        position: absolute;
        top: 20px;
        left: 10px;

        .avator {
          display: inline-block;
          width: 39px;
          height: 39px;
          border: 1px solid #000000;
          line-height: 38px;
          text-align: center;
          color: #000000;
          border-radius: 50%;
          background: #fff;
          box-sizing: border-box;
          font-size: 12px;
        }

        .name {
          display: inline-block;
          color: #fff;
          margin-left: 10px;
        }
      }

      .wrap {
        position: absolute;
        top: 80px;
        left: 50%;
        transform: translate(-50%);
        width: 90%;

        .content-body {
          background: #fff;
          height: 100px;
          padding: 10px;
          border-radius: 5px;
          text-align: left;
        }

        .content-bot {
          position: relative;
          height: 200px;
          background: #fff;
          border-radius: 5px;
          text-align: left;
          margin-top: 10px;

          .bot-box {
            position: absolute;
            bottom: 10px;
            left: 10px;
          }

          .bot-item {
            text-align: center;
            display: inline-block;
            margin-right: 20px;

            .item-text {
              font-size: 12px;
              color: #aaaaaa;
            }
          }
        }
      }

      .content-tips {
        background-color: rgba(255, 255, 255, 0.3);
        color: #fff;
        position: absolute;
        top: 10px;
        right: 0;
        border-right: 0px;
        border-radius: 11px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        font-size: 12px;
        padding: 3px 10px;
        box-sizing: content-box;
      }

      .publish-btn {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translate(-50%);
        width: 183px;
        height: 47px;
        background: #d9001b;
        border-radius: 168px;
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
        line-height: 47px;
        text-align: center;
      }

      .sign-bot {
        position: absolute;
        bottom: 5px;
        left: 50%;
        transform: translate(-50%);
        font-size: 12px;
        color: #000000;
      }
    }

    .text {
      text-align: left;
      color: #8b8b8b;
      font-size: 12px;
    }
  }
}
