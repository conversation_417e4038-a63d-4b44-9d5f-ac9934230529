/*
 * @Author: <PERSON>xiaoyan
 * @Date: 2022/12/20 16:20
 * @LastEditTime: 2023/11/09 14:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/SignIn/StepThree/home.jsx
 * @Description: '步骤三'
 */

import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { Form, Input, Upload, Row, Col, Image } from 'antd';
import { apiCall } from 'common/utils';
import { normFile, removeInputEmpty } from 'common/regular';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import ColorPicker from 'components/ColorPicker/home';
import WibotUploadImage from 'components/WibotUploadImage/home';
import './home.less';
import {FileHOC} from 'components/FileHOC/FileHOC';
const FormItem = Form.Item;

const StepThree = forwardRef((props, ref) => {
  const { formForm, handlePreviewChange } = props;

  useImperativeHandle(ref, () => ({
    getInitDataById,
  }));

  const onRefColorPicker = useRef();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadLoadingPoster, setUploadLoadingPoster] = useState(false);
  const [uploadLoadingGround, setUploadLoadingGround] = useState(false);
  const [imageUrl, setImageUrl] = useState('images/sign_dk.png');
  const [modalImage, setModalImage] = useState('images/modal_df.png');
  const [signGroundImage, setSignGroundImage] = useState('images/default_bg.png');
  const [helpSignImage, setHelpSignImage] = useState('images/default_sign_help_bg.png');
  const [color, setColor] = useState('#aabbcc');
  const [isShow, setIsShow] = useState(false);
  const [previewParams, setPreviewParams] = useState({ visible: false });

  useEffect(() => { }, []);

  const getInitDataById = (data) => {
    setImageUrl(data.backgroundImage || 'images/sign_dk.png');
    setModalImage(data.modalImage || 'images/modal_df.png');
    setSignGroundImage(data.signGroundImage || 'images/default_bg.png');
    setHelpSignImage(data.helpSignImage || 'images/default_sign_help_bg.png');
    setColor(data.color);
  };

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === 'uploading') {
      type ? (type == 'modalImage' ? setUploadLoadingPoster(true) : setUploadLoadingGround(true)) : setUploadLoading(true);
      return;
    }
  };

  const customRequest = (config, type = null) => {
    const File = config.file;
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    apiCall('/file/image', 'POST', data).then((res) => {
      const { fileId, fileUrl } = res;
      if (type == 'modalImage') {
        setModalImage(fileUrl);
        formForm.setFieldsValue({
          modalImage: [fileId]
        });
      } else if (type == 'signGroundImage') {
        setSignGroundImage(fileUrl);
        handlePreviewChange({ signGroundImage: fileUrl });
        formForm.setFieldsValue({
          signGroundImage: [fileId]
        });
      } else if (type == 'helpSignImage') {
        setHelpSignImage(fileUrl);
        handlePreviewChange({ helpSignImage: fileUrl });
        formForm.setFieldsValue({
          helpSignImage: [fileId]
        });
      } else {
        setImageUrl(fileUrl);
        handlePreviewChange({ backgroundImage: fileUrl });
        formForm.setFieldsValue({
          backgroundImage: [fileId]
        });
      }
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setUploadLoading(false);
        setUploadLoadingPoster(false);
        setUploadLoadingGround(false);
      });
  };

  const handleResetUpload = (e, type = null) => {
    e.preventDefault();// 阻止浏览器默认行为
    e.stopPropagation();// 阻止事件冒泡
    if (type == 'modalImage') {
      setModalImage('');
      formForm.setFieldsValue({
        modalImage: ''
      });
    } else if (type == 'signGroundImage') {
      setSignGroundImage('');
      handlePreviewChange({ signGroundImage: '' });
      formForm.setFieldsValue({
        signGroundImage: ''
      });
    } else if (type == 'helpSignImage') {
      setHelpSignImage('');
      handlePreviewChange({ helpSignImage: '' });
      formForm.setFieldsValue({
        helpSignImage: ''
      });
    } else {
      setImageUrl('');
      handlePreviewChange({ backgroundImage: '' });
      formForm.setFieldsValue({
        backgroundImage: ''
      });
    }
  };

  // 图片预览
  const onPreviewImage = (url) => {
    setPreviewParams({
      url,
      visible: true
    });
  };

  // 颜色选择器
  const onChangeColor = (color) => {
    formForm.setFieldValue(['pageSetting', 'color'], color);
    handlePreviewChange({ color });
    setIsShow(false);
  };

  return (
    <div className='StepThree'>
      <h2 className='card-title'>页面样式</h2>
      <FormItem label="主题色" extra="用于文字标题颜色、按钮背景颜色">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FormItem name={['pageSetting', 'color']} initialValue='#d9001b' getValueFromEvent={(e) => removeInputEmpty(e)} style={{ marginBottom: '0px' }}>
            <Input placeholder="请输入主题色" allowClear />
          </FormItem>
          <div>
            <a style={{ marginLeft: '5px' }} onClick={() => {
              setIsShow(true);
              color && setTimeout(() => {
                onRefColorPicker.current.handleSetColor(color);
              }, 500);
            }}>选择颜色</a>
            {isShow && <div style={{ position: 'absolute' }}>
              <ColorPicker
                ref={onRefColorPicker}
                onChangeColor={onChangeColor}
                handleClosePicker={() => {
                  setIsShow(false);
                }}
              />
            </div>}
          </div>

        </div>
      </FormItem>
      <FormItem label=" " colon={false} labelCol={{ span: 2 }} wrapperCol={{ xs: { span: 24 }, sm: { span: 20 } }}>
        <Row gutter={10}>
          <Col span={8}>
            <div>首页背景图 : </div>
            <FormItem name="backgroundImage" valuePropName="fileList" initialValue={['backgroundImage']} getValueFromEvent={normFile} rules={[{ required: true, message: '请上传图片' }]}
              extra="尺寸750*1600，大小限制为2M，最多上传1张" >
              <Upload
                name="file"
                customRequest={(info) => { customRequest(info); }}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={(file, files) => beforeUpload(file, files, { width: 750, height: 1600 })}
                onChange={(info) => { onChangeUpload(info); }}
              >
                <WibotUploadImage
                  imageUrl={imageUrl}
                  loading={uploadLoading}
                  onClose={handleResetUpload}
                />
              </Upload>
            </FormItem>
          </Col>
          <Col span={8}>
            <div>签到日历背景图 : </div>
            <FormItem name="signGroundImage" valuePropName="fileList" initialValue={['signGroundImage']} getValueFromEvent={normFile} rules={[{ required: true, message: '请上传图片' }]}
              extra="尺寸750*1600，大小限制为2M，最多上传1张" >
              <Upload
                name="file"
                customRequest={(info) => { customRequest(info, 'signGroundImage'); }}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={(file, files) => beforeUpload(file, files, { width: 750, height: 1600 })}
                onChange={(info) => { onChangeUpload(info, 'signGroundImage'); }}
              >
                <WibotUploadImage
                  imageUrl={signGroundImage}
                  loading={uploadLoadingGround}
                  onClose={(e) => { handleResetUpload(e, 'signGroundImage') }}
                />
              </Upload>
            </FormItem>
          </Col>
          <Col span={8}>
            <div>签到助力背景图 : </div>
            <FormItem name="helpSignImage" valuePropName="fileList" initialValue={['helpSignImage']} getValueFromEvent={normFile} rules={[{ required: true, message: '请上传图片' }]}
              extra="尺寸750*1600，大小限制为2M，最多上传1张" >
              <Upload
                name="file"
                customRequest={(info) => { customRequest(info, 'helpSignImage'); }}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={(file, files) => beforeUpload(file, files, { width: 750, height: 1600 })}
                onChange={(info) => { onChangeUpload(info, 'helpSignImage'); }}
              >
                <WibotUploadImage
                  imageUrl={helpSignImage}
                  loading={uploadLoadingGround}
                  onClose={(e) => { handleResetUpload(e, 'helpSignImage') }}
                />
              </Upload>
            </FormItem>
          </Col>
          <Col span={8}>
            <div>活动规则、签到记录弹窗 : </div>
            <FormItem name="modalImage" valuePropName="fileList" initialValue={['modalImage']} getValueFromEvent={normFile} rules={[{ required: true, message: '请上传图片' }]}
              extra="尺寸500*640，大小限制为2M，最多上传1张" >
              <Upload
                name="file"
                customRequest={(info) => { customRequest(info, 'modalImage'); }}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={(file, files) => beforeUpload(file, files, { width: 500, height: 640 })}
                onChange={(info) => { onChangeUpload(info, 'modalImage'); }}
              >
                <WibotUploadImage
                  imageUrl={modalImage}
                  loading={uploadLoadingPoster}
                  onClose={(e) => { handleResetUpload(e, 'modalImage') }}
                />
              </Upload>
            </FormItem>
          </Col>
        </Row>
      </FormItem>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
          style={{ display: 'none' }}
          preview={{
            visible: previewParams.visible,
            src: url,
            onVisibleChange: (value) => {
              setPreviewParams({ visible: false });
            },
          }}
        />
        )}
      </FileHOC>
    </div>
  );
});

export default StepThree;
