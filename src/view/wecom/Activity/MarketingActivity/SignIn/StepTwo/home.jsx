/*
 * @Author: <PERSON>xia<PERSON><PERSON>
 * @Date: 2022/12/20 14:47
 * @LastEditTime: 2023/04/19 18:08
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/SignIn/StepTwo/home.jsx
 * @Description: '步骤二'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Form, Switch, InputNumber, Radio, Table, Row, Col } from 'antd';
import moment from 'moment';

const FormItem = Form.Item;

const StepTwo = forwardRef((props, ref) => {
  const { handlePreviewChange, processingFlag } = props;

  useImperativeHandle(ref, () => ({
    getInitDataById,
    getPointsData,
  }));

  const [isChecked, setIsChecked] = useState(true);
  // const [isHelpChecked, setIsHelpChecked] = useState(true);
  const [valueRadio, setValueRadio] = useState('SAME');
  const [dataSource, setDataSource] = useState([]);
  const [pointsDates, setPointsDates] = useState([]);
  const columns = [
    {
      title: '日期',
      width: '80px',
      align: 'center',
      render: (text, record, index) => moment(pointsDates[index]).format('MM-DD'),
    },
    {
      title: '积分',
      width: '140px',
      dataIndex: 'point',
      key: 'point',
      align: 'center',
      render: (value, record, index) => (
        <FormItem
          rules={[{ required: true, message: '请输入积分' }]}
          name={['signSetting', 'pointList', index, 'point']} style={{ margin: '0' }}>
          <InputNumber min={0} precision={0} disabled={processingFlag} />
        </FormItem>
      )
    },
  ];

  useEffect(() => {

  }, []);

  const getInitDataById = (data) => {
    setIsChecked(data.enable);
    setValueRadio(data.type);
  };

  const getPointsData = (data) => {
    if (data.counts) {
      const array = new Array(data.counts).fill('');
      setDataSource(array.map(() => (
        {
          id: Math.random().toString(16),
          point: '',
        }
      )));
    } else {
      setDataSource([]);
    }
    setPointsDates(data.dates);
  };

  // 积分模式
  const onChangeRadio = (e) => {
    setValueRadio(e.target.value);
  };

  return (
    <div className='StepTwo'>
      <h2 className='card-title'>签到设置</h2>
      <FormItem name={['signSetting', 'signHelp']} label="签到助力" valuePropName="checked" initialValue>
        <Switch disabled={processingFlag} onChange={(checked) => {
          // setIsHelpChecked(checked);
        }} />
      </FormItem>
      {/* {
        isHelpChecked && <>
          <FormItem label="助力要求" required>
            <div style={{ display: 'flex', alignItems: 'baseline' }}>
              <span>邀请</span>
              <FormItem
                style={{ margin: '0 6px' }}
                name={['signSetting', 'helpRequire']}
                rules={[{ required: true, message: '请选择助力人数' }]}
              >
                <InputNumber min={0} precision={0} disabled={processingFlag} />
              </FormItem>
              <span>名好友可完成1次助力</span>
            </div>
          </FormItem>
        </>
      } */}
      <FormItem name={['signSetting', 'enable']} label="签到奖励" valuePropName="checked" initialValue>
        <Switch disabled={processingFlag} onChange={(checked) => {
          setIsChecked(checked);
          handlePreviewChange({ enable: checked });
        }} />
      </FormItem>
      {
        isChecked && <>
          <FormItem name={['signSetting', 'type']} label="积分模式" initialValue="SAME" rules={[{ required: true, message: '请选择积分模式' }]}>
            <Radio.Group onChange={onChangeRadio} disabled={processingFlag}>
              <Radio value="SAME">每天相同</Radio>
              <Radio value="DIFFERENCE">每天不同</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem name="pointsDetail" label="积分详情" required>
            {
              valueRadio == 'SAME' ? <Row>
                <Col style={{ display: 'flex', alignItems: 'baseline' }}>
                  <span style={{ marginRight: '6px' }}>每天</span>
                  <FormItem name={['signSetting', 'pointList', 0, 'point']} rules={[{ required: true, message: '请选择积分' }]}>
                    <InputNumber min={0} disabled={processingFlag} precision={0} />
                  </FormItem>
                  <span style={{ marginLeft: '6px' }}>积分</span>
                </Col>
                {/* <Col style={{ display: 'flex', alignItems: 'baseline' }}>
                  <FormItem name="oldCounts" label="老客户 每天" rules={[{ required: true, message: '请选择老客户积分' }]}>
                    <InputNumber min={0} onChange={(e) => { onChangePointsDetail(e, 'old'); }} />
                  </FormItem>
                  <span style={{ marginLeft: '6px' }}>积分</span>
                </Col> */}
              </Row>
                : <div>
                  <div>天数根据设置的签到天数动态调整</div>
                  <Table rowKey={(record) => record.id} bordered columns={columns} dataSource={dataSource} pagination={false}></Table>
                </div>
            }

          </FormItem>
        </>
      }
    </div>
  );
});

export default StepTwo;
