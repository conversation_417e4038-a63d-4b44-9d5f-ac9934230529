/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 17:42
 * @LastEditTime: 2023/11/17 10:48
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/SignIn/detail/TabDetailsFive.jsx
 * @Description: '详情-推广渠道'
 */

import React, { useEffect, useState } from 'react';
import { Card, Table, Tooltip } from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { qs2obj } from 'common/object';
import moment from 'moment';

const TabDetailsFive = (props) => {
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '渠道名称',
      width: '160px',
      dataIndex: 'channelName',
      key: 'channelName',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime)
    },
    {
      title: '签到人数',
      width: '160px',
      dataIndex: 'activityCustomerCount',
      key: 'activityCustomerCount',
      align: 'center',
    },
    {
      title: '签到次数',
      width: '160px',
      dataIndex: 'activityCount',
      key: 'activityCount',
      align: 'center',
    },
    {
      title: '访问次数',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
    },
    {
      title: '访问人数',
      width: '160px',
      dataIndex: 'visitorCount',
      key: 'visitorCount',
      align: 'center',
    }
  ];

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { detailId, pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      activityId: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    apiCall('/activity/activityChannel', 'GET', data).then((res) => {
      const { records, current, size, total, pages } = res;
      setDataSource(records);
      setPaginations({
        current: current,
        pageSize: size,
        total: total,
        showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='TabDetailsFive'>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default TabDetailsFive;
