/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/20 10:23
 * @LastEditTime: 2025/05/15 10:02
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/SignIn/home.jsx
 * @Description: '签到打卡'
 */

import React, { useEffect, useState, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Steps,
  TreeSelect,
  Image,
  Upload,
  message,
  DatePicker,
  Typography,
  Space,
} from "antd"
import moment from "moment"
import { apiCall } from "common/utils"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import {
  PlusOutlined,
  CloseCircleOutlined,
  LeftOutlined,
} from "@ant-design/icons"
import { normFile, removeInputEmpty } from "common/regular"
import { compressImage, base64ToFile, beforeUpload } from "common/image"
import {
  getDaysBet<PERSON><PERSON>,
  getdiffdate,
  disabledDate,
  disabledTime,
} from "common/date"
import { qs2obj } from "common/object"
import { QrCodeBase } from "common/qrcode"
import drawCoin from "images/drawCoin.png"
import drawDemond from "images/drawDemond.png"
import signIcon from "images/signIcon.png"
import StepTwo from "./StepTwo/home"
import StepThree from "./StepThree/home"
import Calendar from "../comps/Calendar/home"
import signDefaultBg from "images/sign_df.png"
import calendarBg from "images/calendar_bg.png"
import LiveCodeModal from "components/Modal/LiveCodeModal/home"
import { clearCache } from "react-router-cache-route"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import "./home.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Step } = Steps
const { SHOW_PARENT } = TreeSelect
const { TextArea } = Input
const { Paragraph } = Typography

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const MarketingActivitySignIn = (props) => {
  const [formForm] = Form.useForm()
  const onRefStepTwoFrame = useRef()
  const onRefStepThreeFrame = useRef()
  const [id, setId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [stepCurrent, setStepCurrent] = useState(0)
  const [status, setStatus] = useState(null)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [uploadLoading, setUploadLoading] = useState(false)
  const [uploadLoadingPoster, setUploadLoadingPoster] = useState(false)
  const [uploadLoadingSharePoster, setUploadLoadingSharePoster] =
    useState(false)
  const [liveCodeModalParams, setLiveCodeModalParams] = useState({
    visible: false,
  })
  const [imageUrl, setImageUrl] = useState("")
  const [posterUrl, setPosterUrl] = useState("")
  const [sharePosterUrl, setSharePosterUrl] = useState("")
  const [isCalendar, setIsCalendar] = useState(false)
  const [isSign, setIsSign] = useState(false)
  const [liveCodeData, setLiveCodeData] = useState(null)
  const [previewData, setPreviewData] = useState({ enable: true })
  const [pointsDates, setPointsDates] = useState([])
  const [processingFlag, setProcessingFlag] = useState(false)

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      init(id)
    }
    getTagCategoryTreeTwo()
  }, [])

  const init = async (id) => {
    const params = {
      activityId: id,
    }
    await fetchList(params)
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { activityId } = params
    await apiCall(`/activity/activity/${activityId}`, "GET")
      .then((res) => {
        const {
          shelfState,
          processingFlag,
          title,
          activityStartTime,
          activityEndTime,
          startTime,
          endTime,
          cover,
          poster,
          sharePoster,
          dynamicCodeUrl,
          signSetting,
          pageSetting,
        } = res
        formForm.setFieldsValue({
          ...res,
          activityTime: [
            activityStartTime ? moment(activityStartTime) : null,
            activityEndTime ? moment(activityEndTime) : null,
          ],
          shelfTime: [
            startTime ? moment(startTime) : null,
            endTime ? moment(endTime) : null,
          ],
          cover: cover && [cover],
          poster: poster && [poster],
          sharePoster: sharePoster && [sharePoster],
          backgroundImage:
            pageSetting && pageSetting.backgroundImage
              ? [pageSetting.backgroundImage]
              : ["backgroundImage"],
          modalImage:
            pageSetting && pageSetting.modalImage
              ? [pageSetting.modalImage]
              : ["modalImage"],
          signGroundImage:
            pageSetting && pageSetting.signGroundImage
              ? [pageSetting.signGroundImage]
              : ["signGroundImage"],
          helpSignImage:
            pageSetting && pageSetting.helpSignImage
              ? [pageSetting.helpSignImage]
              : ["helpSignImage"],
        })
        setProcessingFlag(processingFlag) // 是否进行中
        setStatus(shelfState)
        setImageUrl(cover)
        setPosterUrl(poster)
        setSharePosterUrl(sharePoster)
        dynamicCodeUrl &&
          setLiveCodeData({ imageUrl: QrCodeBase({ url: dynamicCodeUrl }) })
        onRefStepTwoFrame.current.getInitDataById({ ...signSetting })
        if (signSetting.type == "DIFFERENCE") {
          // 积分模式-每天不同
          onRefStepTwoFrame.current.getPointsData({
            dates: signSetting.pointList?.map((item) => item.date),
            counts: signSetting.pointList?.length,
          })
        } else {
          onRefStepTwoFrame.current.getPointsData({
            dates: getdiffdate(
              moment(activityStartTime).format("YYYY-MM-DD"),
              moment(activityEndTime).format("YYYY-MM-DD")
            ),
            counts: getDaysBetween(
              moment(activityStartTime).format("YYYY-MM-DD"),
              moment(activityEndTime).format("YYYY-MM-DD")
            ),
          })
        }
        onRefStepThreeFrame.current.getInitDataById({ ...pageSetting })
        setPreviewData({
          enable: signSetting.enable,
          title,
          color: pageSetting.color,
          backgroundImage: pageSetting.backgroundImage,
          signGroundImage: pageSetting.signGroundImage,
          helpSignImage: pageSetting.helpSignImage,
          datesList: getdiffdate(activityStartTime, activityEndTime).map(
            (item, index) => ({
              isComplete: index == 0,
              date: moment(item).format("MM-DD"),
            })
          ),
        })
        setPointsDates(
          getdiffdate(
            moment(activityStartTime).format("YYYY-MM-DD"),
            moment(activityEndTime).format("YYYY-MM-DD")
          )
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeSteps = (current) => {
    setStepCurrent(current)
  }

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === "uploading") {
      if (type == "poster") {
        setUploadLoadingPoster(true)
      } else if (type == "sharePoster") {
        setUploadLoadingSharePoster(true)
      } else {
        setUploadLoading(true)
      }
      return
    }
  }

  const customRequest = (config, type = null) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        if (type == "poster") {
          setPosterUrl(fileUrl)
          formForm.setFieldsValue({
            poster: [fileId],
          })
        } else if (type == "sharePoster") {
          setSharePosterUrl(fileUrl)
          formForm.setFieldsValue({
            sharePoster: [fileId],
          })
        } else {
          setImageUrl(fileUrl)
          formForm.setFieldsValue({
            cover: [fileId],
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
        setUploadLoadingPoster(false)
        setUploadLoadingSharePoster(false)
      })
  }

  const handleResetUpload = (e, type = null) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    if (type == "poster") {
      setPosterUrl("")
      formForm.setFieldsValue({
        poster: "",
      })
    } else if (type == "sharePoster") {
      setSharePosterUrl("")
      formForm.setFieldsValue({
        sharePoster: "",
      })
    } else {
      setImageUrl("")
      formForm.setFieldsValue({
        cover: "",
      })
    }
  }

  // 活动时间
  const onDatePickerChange = (date, dateString) => {
    const startTime = moment(dateString[0]).format("YYYY-MM-DD")
    const endTime = moment(dateString[1]).format("YYYY-MM-DD")
    const diffdate = getdiffdate(startTime, endTime)
    onRefStepTwoFrame.current.getPointsData({
      dates: date ? diffdate : [],
      counts: date ? getDaysBetween(startTime, endTime) : 0,
    })
    setPointsDates(date ? diffdate : [])
    const newData = { ...previewData }
    setPreviewData({
      ...newData,
      datesList: date
        ? diffdate.map((item, index) => ({
            isComplete: index == 0,
            date: moment(item).format("MM-DD"),
          }))
        : [],
    })
  }

  // 选择活码
  const handleAddMsg = () => {
    setLiveCodeModalParams({
      visible: true,
      type: "EMPLOYEE",
      onSubmit: (data) => {
        if (data.id) {
          setLiveCodeData(data)
          formForm.setFieldsValue({
            dynamicCodeId: data.id,
          })
        }
        setLiveCodeModalParams({ visible: false })
      },
      onCancel: () => {
        setLiveCodeModalParams({ visible: false })
      },
    })
  }

  // 预览数据变化
  const handlePreviewChange = (data) => {
    const newPreviewData = { ...previewData }
    setPreviewData({ ...newPreviewData, ...data })
  }

  const onSubmit = (type = null) => {
    formForm
      .validateFields()
      .then((formData) => {
        setLoading(true)
        if (formData.activityTime) {
          // 上下架时间
          formData.activityStartTime = moment(
            formData.activityTime[0]._d
          ).format("YYYY-MM-DD HH:mm")
          formData.activityEndTime = moment(formData.activityTime[1]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          delete formData.activityTime
        }
        if (formData.shelfTime) {
          // 报名时间
          formData.startTime = moment(formData.shelfTime[0]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          formData.endTime = moment(formData.shelfTime[1]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          delete formData.shelfTime
        }
        formData.cover = formData.cover && formData.cover[0]
        formData.poster = formData.poster && formData.poster[0]
        formData.sharePoster = formData.sharePoster && formData.sharePoster[0]
        formData.pageSetting.backgroundImage =
          formData.backgroundImage?.join(",") == "backgroundImage"
            ? null
            : formData.backgroundImage?.join(",")
        formData.pageSetting.modalImage =
          formData.modalImage?.join(",") == "modalImage"
            ? null
            : formData.modalImage?.join(",")
        formData.pageSetting.signGroundImage =
          formData.signGroundImage?.join(",") == "signGroundImage"
            ? null
            : formData.signGroundImage?.join(",")
        formData.pageSetting.helpSignImage =
          formData.helpSignImage?.join(",") == "helpSignImage"
            ? null
            : formData.helpSignImage?.join(",")
        formData.signSetting.pointList?.forEach((item, index) => {
          item.date = pointsDates[index]
        })

        const data = {
          id,
          type: "SIGN",
          shelfState: type ? type : status,
          ...formData,
        }

        const apiUrl = id
          ? `/activity/activity/update/${id}`
          : "/activity/activity"
        apiCall(apiUrl, "POST", data)
          .then((res) => {
            message.success(id ? "修改成功！" : "新增成功！")
            // clearCache(); // 清空路由缓存
            props.history.push("/wecom/marketingActivity")
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setLoading(false)
          })
      })
      .catch((err) => {
        console.log(err)
        message.error("请完善表单信息！")
      })
  }

  return (
    <div className="MarketingActivitySignIn">
      <Spin spinning={loading}>
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <Steps
                current={stepCurrent}
                onChange={onChangeSteps}
                style={{ width: "80%", margin: "0 auto 20px" }}
              >
                <Step
                  status={stepCurrent == 0 ? "process" : "wait"}
                  title="步骤一"
                  description="基础设置"
                />
                <Step
                  status={stepCurrent == 1 ? "process" : "wait"}
                  title="步骤二"
                  description="签到设置"
                />
                <Step
                  status={stepCurrent == 2 ? "process" : "wait"}
                  title="步骤三"
                  description="页面样式"
                />
              </Steps>
              <Form {...layout} form={formForm}>
                <div
                  style={
                    stepCurrent == 0
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <h2 className="card-title">活动信息</h2>
                  <FormItem
                    label="活动标题"
                    name="title"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[{ required: true, message: "请输入活动标题" }]}
                    extra="链接卡片标题"
                  >
                    <Input
                      placeholder="请输入活动标题(30字内)"
                      allowClear
                      maxLength={30}
                      onChange={(e) => {
                        setPreviewData({
                          ...previewData,
                          title: e.target.value,
                        })
                      }}
                    />
                  </FormItem>
                  <FormItem
                    label="活动描述"
                    name="description"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[{ required: true, message: "请输入活动描述" }]}
                    extra="链接卡片描述"
                  >
                    <Input
                      placeholder="请输入活动描述(30字内)"
                      allowClear
                      maxLength={30}
                    />
                  </FormItem>
                  <FormItem
                    name="cover"
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                    label="链接封面"
                    rules={[{ required: true, message: "请上传图片" }]}
                    extra="建议尺寸1:1，大小限制为2M，最多上传1张"
                  >
                    <Upload
                      name="file"
                      customRequest={(info) => {
                        customRequest(info)
                      }}
                      listType="picture-card"
                      showUploadList={false}
                      beforeUpload={beforeUpload}
                      onChange={(info) => {
                        onChangeUpload(info)
                      }}
                    >
                      <WibotUploadImage
                        imageUrl={imageUrl}
                        loading={uploadLoading}
                        onClose={handleResetUpload}
                      />
                    </Upload>
                  </FormItem>
                  <FormItem
                    name="poster"
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                    label="活动海报"
                    rules={[{ required: true, message: "请上传图片" }]}
                    extra="员工分享宣传使用，大小限制为2M，最多上传1张"
                  >
                    <Upload
                      name="file"
                      customRequest={(info) => {
                        customRequest(info, "poster")
                      }}
                      listType="picture-card"
                      showUploadList={false}
                      beforeUpload={beforeUpload}
                      onChange={(info) => {
                        onChangeUpload(info, "poster")
                      }}
                    >
                      <WibotUploadImage
                        imageUrl={posterUrl}
                        loading={uploadLoadingPoster}
                        onClose={(e) => {
                          handleResetUpload(e, "poster")
                        }}
                      />
                    </Upload>
                  </FormItem>
                  <FormItem
                    name="sharePoster"
                    valuePropName="fileList"
                    getValueFromEvent={normFile}
                    label="分享海报"
                    rules={[{ required: true, message: "请上传图片" }]}
                    extra="活动用户分享海报，尺寸600*720，大小限制为2M，最多上传1张"
                  >
                    <Upload
                      name="file"
                      customRequest={(info) => {
                        customRequest(info, "sharePoster")
                      }}
                      listType="picture-card"
                      showUploadList={false}
                      beforeUpload={(file, files) =>
                        beforeUpload(file, files, { width: 600, height: 720 })
                      }
                      onChange={(info) => {
                        onChangeUpload(info, "sharePoster")
                      }}
                    >
                      <WibotUploadImage
                        imageUrl={sharePosterUrl}
                        loading={uploadLoadingSharePoster}
                        onClose={(e) => {
                          handleResetUpload(e, "sharePoster")
                        }}
                      />
                    </Upload>
                  </FormItem>
                  <FormItem
                    name="rule"
                    label="活动规则"
                    rules={[{ required: true, message: "请输入活动规则" }]}
                  >
                    <TextArea
                      placeholder="请输入活动规则（300字内）"
                      allowClear
                      showCount
                      maxLength={300}
                      autoSize={{ minRows: 2, maxRows: 7 }}
                      className="textArea-mid"
                    />
                  </FormItem>
                  <FormItem
                    name="activityTime"
                    label="活动时间"
                    rules={[{ required: true, message: "请选择活动时间" }]}
                  >
                    <RangePicker
                      disabledDate={disabledDate}
                      disabledTime={disabledTime}
                      showTime={{ format: "HH:mm" }}
                      format="YYYY-MM-DD HH:mm"
                      inputReadOnly
                      onChange={onDatePickerChange}
                    />
                  </FormItem>
                  <FormItem
                    name="shelfTime"
                    label="上下架时间"
                    rules={[{ required: true, message: "请选择上下架时间" }]}
                  >
                    <RangePicker
                      disabledDate={disabledDate}
                      disabledTime={disabledTime}
                      showTime={{ format: "HH:mm" }}
                      format="YYYY-MM-DD HH:mm"
                      inputReadOnly
                    />
                  </FormItem>
                  <FormItem
                    name="visibleScopeDepartmentId"
                    label="可见范围"
                    rules={[{ required: true, message: "请选择可见范围" }]}
                  >
                    <ETypeTransferModal title="可见范围" onlyDepartment />
                  </FormItem>
                  {/* <FormItem */}
                  {/*   name="customerTagIdList" */}
                  {/*   label="客户标签" */}
                  {/*   rules={[{ required: true, message: "请选择客户标签" }]} */}
                  {/*   extra="客户访问时自动添加标签" */}
                  {/* > */}
                  {/*   <TreeSelect */}
                  {/*     treeData={labelTreeData} */}
                  {/*     treeCheckable */}
                  {/*     treeDefaultExpandedKeys={["1"]} */}
                  {/*     allowClear */}
                  {/*     showArrow */}
                  {/*     showSearch */}
                  {/*     treeNodeFilterProp="title" */}
                  {/*     maxTagCount="responsive" */}
                  {/*     showCheckedStrategy={SHOW_PARENT} */}
                  {/*     placeholder="客户标签" */}
                  {/*   /> */}
                  {/* </FormItem> */}
                  <CustomTagSelect
                    label="客户标签"
                    rules={[{ required: true, message: "请选择客户标签" }]}
                    extra="客户访问时自动添加标签"
                    name="tagNameList"
                    placeholder="客户标签"
                    useForm={formForm}
                    existTagNameList={formForm.getFieldValue("tagNameList")}
                    labelTreeData={labelTreeData}
                  />
                  <FormItem
                    name="dynamicCodeId"
                    label="客服活码"
                    extra="引流、反馈、投诉"
                  >
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddMsg()}
                    >
                      选择活码
                    </Button>
                    {liveCodeData && (
                      <div
                        style={{
                          position: "relative",
                          width: "100px",
                          height: "100px",
                          marginTop: "20px",
                        }}
                      >
                        <WibotUploadImage
                          imageUrl={liveCodeData.imageUrl}
                          onClose={() => {
                            setLiveCodeData(null)
                            formForm.setFieldsValue({
                              dynamicCodeId: null,
                            })
                          }}
                        />
                      </div>
                    )}
                  </FormItem>
                </div>
                <div
                  style={
                    stepCurrent == 1
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StepTwo
                    ref={onRefStepTwoFrame}
                    formForm={formForm}
                    processingFlag={processingFlag}
                    handlePreviewChange={handlePreviewChange}
                  />
                </div>
                <div
                  style={
                    stepCurrent == 2
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StepThree
                    ref={onRefStepThreeFrame}
                    formForm={formForm}
                    handlePreviewChange={handlePreviewChange}
                  />
                </div>

                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    {stepCurrent > 0 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent - 1)
                        }}
                      >
                        上一步
                      </Button>
                    )}
                    {stepCurrent < 2 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent + 1)
                        }}
                      >
                        下一步
                      </Button>
                    )}
                    {id && processingFlag && (
                      <Button type="primary" onClick={() => onSubmit()}>
                        保存
                      </Button>
                    )}
                    {((id && status == "soldOut" && stepCurrent == 2) ||
                      (id && status == "notIssue" && stepCurrent == 2) ||
                      (!id && stepCurrent == 2)) && (
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                    )}
                    {((id && status == "soldOut" && stepCurrent == 2) ||
                      (id && status == "notIssue" && stepCurrent == 2) ||
                      (!id && stepCurrent == 2)) && (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </Space>
                </div>
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              <WibotMobilePreview title={previewData.title || " "}>
                <LeftOutlined
                  className="back"
                  onClick={() => {
                    setIsCalendar(false)
                  }}
                />
                <div className="body">
                  {isCalendar ? (
                    <>
                      <img
                        src={previewData.signGroundImage || calendarBg}
                        className={
                          previewData.signGroundImage ? "bg" : "content_bg"
                        }
                      />
                      <div className="sign-calendar">
                        <Calendar />
                      </div>
                    </>
                  ) : (
                    <>
                      <img
                        src={previewData.backgroundImage || signDefaultBg}
                        className={
                          previewData.backgroundImage ? "bg" : "content_bg"
                        }
                      />
                      <div className="content-tips">规则 | 记录</div>
                      <div
                        className="sign-btn"
                        style={{
                          background: previewData.color
                            ? previewData.color
                            : "#d9001b",
                        }}
                        onClick={() => {
                          setIsSign(true)
                        }}
                      >
                        立即打卡
                      </div>
                      {previewData.enable && (
                        <div className="sign-left">已获得 0 积分</div>
                      )}
                      <div
                        className="sign-right"
                        onClick={() => {
                          setIsCalendar(true)
                        }}
                      >
                        已打卡 0 天 &gt;
                      </div>
                      <div className="check-in-list">
                        {previewData.datesList?.map((item, index) => (
                          <div className="check-item" key={index}>
                            <div
                              className={
                                item.isComplete
                                  ? "active-bg reward"
                                  : "un-active-bg reward"
                              }
                            >
                              <p
                                className={
                                  item.isComplete
                                    ? "active-text day"
                                    : "un-active-day day"
                                }
                              >
                                {item.date}
                              </p>
                              {previewData.enable ? (
                                <img
                                  className="icon"
                                  src={item.isComplete ? drawCoin : drawDemond}
                                />
                              ) : (
                                <>
                                  {item.isComplete ? (
                                    <img className="icon" src={drawCoin} />
                                  ) : (
                                    <div className="noSignIcon">√</div>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      {liveCodeData && <div className="sign-bot">联系客服</div>}
                    </>
                  )}
                  {isSign && (
                    <div className="sign-Box">
                      <div className="sign-mask"></div>
                      <div className="sign-content">
                        <img className="signIcon" src={signIcon} />
                        <div className="text">签到成功</div>
                        <div className="words">
                          获得<span style={{ color: "#d9001b" }}>20积分</span>
                        </div>
                        <div
                          className="btn"
                          style={{
                            background: previewData.color
                              ? previewData.color
                              : "#d9001b",
                          }}
                          onClick={() => {
                            setIsSign(false)
                          }}
                        >
                          好的
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </WibotMobilePreview>
            </Col>
          </Row>
        </Card>
      </Spin>
      <LiveCodeModal params={liveCodeModalParams} />
    </div>
  )
}

export default withRouter(MarketingActivitySignIn)
