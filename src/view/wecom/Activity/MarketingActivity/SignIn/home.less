.MarketingActivitySignIn {
  .WibotMobilePreview-Container {
    .content {
      margin: 0;
      padding: 0;
    }
    .back {
      position: absolute;
      cursor: pointer;
      top: 11px;
      left: 14px;
      font-size: 18px;
      color: #fff;
    }

    .body {
      position: relative;
      top: 0;
      left: 0;

      .content_bg {
        width: 100%;
        height: 100%;
      }

      .bg {
        width: 100%;
        object-fit: contain;
      }

      .content-tips {
        background-color: rgba(255, 255, 255, 0.3);
        color: #fff;
        position: absolute;
        top: 10px;
        right: 0;
        border-right: 0px;
        border-radius: 11px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        font-size: 12px;
        padding: 3px 10px;
        box-sizing: content-box;
      }

      .sign-btn {
        position: absolute;
        top: 225px;
        left: 50%;
        transform: translate(-50%);
        text-align: center;
        width: 110px;
        height: 35px;
        background: linear-gradient(
          180deg,
          rgba(251, 177, 0, 1) 0%,
          rgba(247, 112, 0, 1) 100%
        );
        border: none;
        border-radius: 168px;
        box-shadow: none;
        font-size: 20px;
        color: #ffffff;
        cursor: pointer;
      }

      .sign-left {
        position: absolute;
        top: 316px;
        left: 14px;
        color: #ffffff;
      }

      .sign-right {
        position: absolute;
        top: 316px;
        right: 14px;
        color: #ffffff;
        cursor: pointer;
      }

      .sign-bot {
        position: absolute;
        bottom: 3px;
        right: 106px;
        font-size: 12px;
        color: #000000;
      }

      .check-in-list {
        position: absolute;
        top: 340px;
        left: 50%;
        transform: translate(-50%);
        text-align: center;
        border-radius: 5px;
        width: 90%;
        min-height: 112px;
        background: #f2f2f2;
        padding: 10px 10px;
        box-sizing: border-box;
        display: flex;
        overflow-x: auto;
        overflow-y: hidden;

        .check-item {
          // 重点，为了不会被压缩大小
          flex-shrink: 0;
          width: 70px;
          margin-right: 10px;
        }

        .reward {
          border-radius: 6px;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          align-items: center;
          padding: 10px 11px;
          box-sizing: border-box;

          .icon {
            width: 24px;
            margin: 6px 0;
          }

          .day {
            font-size: 14px;
          }

          .reward-value {
            font-size: 12px;
          }

          .un-active-day {
            color: #000000;
          }

          .un-active-reward-value {
            color: #000000;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 90%;
          }

          .active-text {
            color: #ffffff;
          }
        }

        .active-bg {
          background-color: #03c553;
        }

        .un-active-bg {
          background-color: #d7d7d7;
        }

        .noSignIcon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid #7f7f7f;
          text-align: center;
          line-height: 20px;
          font-size: 14px;
          color: #7f7f7f;
          font-weight: bold;
          margin: 6px 0;
        }
      }

      .sign-calendar {
        position: absolute;
        top: 18px;
        left: 50%;
        transform: translate(-50%);
        text-align: center;
        background: #fff;
        border-radius: 5px;
        padding-top: 20px;
        width: 90%;
      }

      .sign-Box {
        position: absolute;
        left: 0px;
        top: 0px;
        height: 100%;
        width: 100%;

        .sign-mask {
          position: absolute;
          left: 0px;
          top: 0px;
          width: 100%;
          height: 100%;
          background: inherit;
          background-color: rgba(0, 0, 0, 0.4980392156862745);
          border: none;
          border-radius: 0px;
        }

        .sign-content {
          position: absolute;
          left: 50%;
          transform: translate(-50%);
          text-align: center;
          top: 90px;
          width: 240px;
          height: 270px;
          background: inherit;
          background-color: #fff;
          border-radius: 10px;
          padding-top: 40px;
          text-align: center;

          .text {
            color: #000000;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
            margin: 10px 0;
          }

          .words {
            color: #000000;
            font-weight: bold;
            font-size: 12px;
          }

          .btn {
            position: absolute;
            bottom: 15px;
            left: 20px;
            width: 200px;
            height: 36px;
            background: inherit;
            background-color: rgba(217, 0, 27, 1);
            border: none;
            border-radius: 168px;
            font-size: 16px;
            color: #ffffff;
            cursor: pointer;
            line-height: 34px;
          }
        }
      }
    }

    .phone-btn {
      position: absolute;
      left: 50px;
      bottom: 35px;
      width: 200px;
      height: 40px;
      background: #d9001b;
      color: #fff;
      text-align: center;
      line-height: 40px;
      border-radius: 20px;
    }

    .text {
      text-align: left;
      color: #8b8b8b;
      font-size: 12px;
    }
  }
}
