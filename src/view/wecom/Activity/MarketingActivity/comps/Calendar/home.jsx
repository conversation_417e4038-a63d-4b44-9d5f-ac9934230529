/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 14:12
 * @LastEditTime: 2022/12/22 10:48
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingActivity\comps\Calendar\home.jsx
 * @Description: '日历'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import {
  Switch, Checkbox, Form, InputNumber,
} from 'antd';
import moment from 'moment';
import { apiCall } from 'common/utils';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import draw from 'images/draw.png';
import './home.less';

const Calendar = forwardRef((props, ref) => {
  const { checkInList = [] } = props;

  useImperativeHandle(ref, () => ({

  }))

  const [calendarList, setCalendarList] = useState([]);
  const [year, setYear] = useState('');
  const [month, setMonth] = useState('');
  const [day, setDay] = useState('');
  const [prevMonth, setPrevMonth] = useState('');
  const [nextMonth, setNextMonth] = useState('');
  const weekDay = ['日', '一', '二', '三', '四', '五', '六'];
  const timestamp = 24 * 60 * 60 * 1000

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    const text = moment((year + '/' + month + '/01'))
    setPrevMonth(moment(text).add(-1, 'M').format("M"));
    setNextMonth(moment(text).add(1, 'M').format("M"))
  }, [month]);

  const changeArrow = (value) => {
    if (value == 'left') {
      getLastMonth()
    } else {
      getNextMonth()
    }
  }
  const isChecked = (item) => {
    const zm = item.month >= 10 ? item.month : `0${item.month}`
    const zd = item.day >= 10 ? item.day : `0${item.day}`
    return checkInList.indexOf(`${item.year}-${zm}-${zd}`) > -1
  }
  const initAllMonth = (date) => {
    const MonthFirstDay = getMonthFirstDay(date)
    let calendarList = [...getCurrentWeekDay(MonthFirstDay)]
    while (calendarList.length < 42) {
      calendarList = calendarList.concat(
        getCurrentWeekDay(
          getNextDay(transferBackToDate(calendarList[calendarList.length - 1]))
        )
      )
    }
    setCalendarList(calendarList);
  }
  const getCurrentWeekDay = (date) => {
    const dt = new Date(date)
    const weekday = dt.getDay()
    const beginDay = new Date(dt.getTime() - weekday * timestamp)
    const backList = [initSpecialDateObject(beginDay)]
    for (let i = 1; i < 7; i++) {
      backList.push(initSpecialDateObject(beginDay.getTime() + i * timestamp))
    }
    return backList
  }
  const isThisMonth = (date) => {
    const dt = new Date(date)
    return dt.getMonth() + 1 === month
  }
  const getMonthFirstDay = (date) => {
    const dt = initDate(date)
    return new Date(dt.getTime() - (dt.getDate() - 1) * timestamp)
  }
  const initDate = (date) => {
    const dt = new Date(date)
    return new Date([dt.getFullYear(), dt.getMonth() + 1, dt.getDate()].join('/'))
  }
  const getNextDay = (date) => {
    const dt = new Date(date)
    return new Date(dt.getTime() + timestamp)
  }
  const initSpecialDateObject = (date) => {
    const dt = new Date(date)
    return {
      year: dt.getFullYear(),
      month: dt.getMonth() + 1,
      day: dt.getDate(),
      weekday: dt.getDay()
    }
  }
  const transferBackToDate = (object) => {
    return new Date([object.year, object.month, object.day].join('/'))
  }
  const changeActive = (item) => {
    if (item.month === month) {
      setDay(item.day);
    } else {
      setYear(item.year);
      setMonth(item.month);
      setDay(item.day);
      initAllMonth(new Date([item.year, item.month, item.day].join('/')))
    }
  }
  const getLastMonth = () => {
    let date;
    let newYear = year;
    let newMonth = month;
    let newDay = day
    if (month === 1) {
      date = [(newYear -= 1), (newMonth = 12), newDay].join('/')
    } else {
      date = [newYear, (newMonth -= 1), newDay].join('/')
    }
    if (!new Date(date) || new Date(date).getMonth() + 1 !== newMonth) {
      date = [newYear, newMonth, (newDay = 1)].join('/')
    }
    initAllMonth(new Date(date))
    setYear(newYear);
    setMonth(newMonth);
    setDay(newDay);
  }
  const getNextMonth = () => {
    let date;
    let newYear = year;
    let newMonth = month;
    let newDay = day
    if (newMonth === 12) {
      date = [(newYear += 1), (newMonth = 1), newDay].join('/')
    } else {
      date = [newYear, (newMonth += 1), newDay].join('/')
    }
    if (!new Date(date) || new Date(date).getMonth() + 1 !== newMonth) {
      date = [newYear, newMonth, (newDay = 1)].join('/')
    }
    initAllMonth(new Date(date));
    setYear(newYear);
    setMonth(newMonth);
    setDay(newDay);
  }
  const isBefore = (item) => {
    const isntCurrentMonth = item.year !== year || item.month !== (new Date().getMonth() + 1)
    return isntCurrentMonth || (!isntCurrentMonth && item.day < day)
  }
  const init = () => {
    const date = new Date()
    setYear(date.getFullYear());
    setMonth(date.getMonth() + 1);
    setDay(date.getDate());
    initAllMonth(date)
  }

  return (
    <div className='Calendar'>
      <div className="ms-calendar">
        <div className="month">
          <div
            onClick={() => { changeArrow('left') }}
            style={{ color: '#fff' }}
          >
            <LeftOutlined />
            <span>{prevMonth}月</span>
          </div>
          <div style={{ color: '#fff' }}>
            <span>{year}年</span>
            <span>{month}月</span>
          </div>
          <div
            onClick={() => { changeArrow('right') }}
            style={{ color: '#fff' }}
          >
            <RightOutlined />
            <span>{nextMonth}月</span>
          </div>
        </div>
        <div className="week">
          {
            weekDay.map((item, index) => <p key={index}>{item}</p>)
          }
        </div>
        <div className="days">
          {
            calendarList.map((item, index) => {
              return month == item.month && <div key={index}>
                {
                  isChecked(item) && <img
                    className="draw-icon"
                    src={draw}
                  />
                }
                {
                  !isChecked(item) && <p
                    className={isBefore(item) ? 'notDay' : ''}
                  >
                    {item.day}
                  </p>
                }
              </div>
            })
          }
        </div>
      </div>
    </div>
  );
});

export default Calendar;
