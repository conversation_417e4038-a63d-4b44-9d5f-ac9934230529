.Calendar {
  .ms-calendar {
    width: 100%;
    height: 370px;
    padding: 0 5px;

    .month {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      // text-align: center;
      // @include flexbox($jc: center, $ai: center);
      font-size: 16px;
      font-weight: 500;
      color: #39393a;
      background: #f77e01;
      padding: 10px;
      border-radius: 5px;

      span {
        // padding: 0 5px;
      }
    }

    .week {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      border-bottom: 1.3px solid #dbdcdd;

      p {
        flex: 1;
        text-align: center;
        color: #989db3;
        font-size: 13px;
      }
    }

    .days {
      width: 100%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .draw-icon {
        width: 16.3px;
      }

      div {
        flex: 0 1 14.28%;
        margin: 10px 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;

        p {
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.5s, color 0.5s;
          border-radius: 50%;
          font-size: 15px;
          text-align: center;
          color: #39393a;
        }

        .active {
          background-color: rgba(0, 0, 0, 0.7);
          color: #fff;
        }

        .notDay {
          color: #989db3;
        }
      }
    }
  }
}