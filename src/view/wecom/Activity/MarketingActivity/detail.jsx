/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/20 11:50
 * @LastEditTime: 2022/12/28 14:28
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingActivity\detail.jsx
 * @Description: '详情'
 */
import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { qs2obj } from 'common/object';
import SignInDetail from './SignIn/detail/home';
import PublishListDetail from './PublishList/detail/home';
import './details.less';

const MarketingActivityDetails = (props) => {
  const [type, setType] = useState(null);

  useEffect(() => {
    const { type } = qs2obj(props.location.search);
    if (type) {
      setType(type);
    }
  }, []);

  return (
    <div className='MarketingActivityDetails'>
      {
        type == 'SIGN' && <SignInDetail />
      }
      {
        type == 'SHARE' && <PublishListDetail />
      }
    </div>
  );
};

export default withRouter(MarketingActivityDetails);
