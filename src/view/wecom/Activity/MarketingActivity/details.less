.MarketingActivityDetails {
  .MA-card {
    .avatar-name {
      display: inline-block;
      margin-left: 10px;

      .ant-avatar {
        margin-right: 6px;
      }
    }

    .ant-space {
      width: 100%;
      margin-bottom: unset !important;
      color: #aaa;

      .ant-tag {
        margin: 0;
      }
    }

    .sign-box {
      display: flex;
      flex-direction: row;
      align-items: center;

      .btn-switch {
        width: 100px;
        height: 30px;
        background: #d9001b;
        color: #fff;
        border-radius: 8px;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
        margin-left: 10px;
      }
    }

    .msgList {
      margin-top: 10px;

      .title {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        margin-right: 10px;
      }
    }
  }

  .ant-row {

    .ant-col {
      margin-bottom: 20px;

      .ant-card {
        height: 100%;
      }
    }
  }

  .info {
    position: relative;

    .groupName {
      margin-bottom: 6px;
    }

    // .avatar-name {
    //   display: inline-block;
    //   margin-left: 10px;

    //   .ant-avatar {
    //     margin-right: 6px;
    //   }
    // }

    .ant-space {
      width: 100%;
      margin-bottom: unset !important;
      color: #aaa;

      .ant-tag {
        margin: 0;
      }
    }

    .ant-descriptions {

      .ant-descriptions-item {
        padding: 0 0 6px;
        margin-bottom: 6px;

        .ant-descriptions-item-label,
        .ant-descriptions-item-content {
          color: #aaa;
        }
      }
    }
  }

  .codeBox {
    white-space: nowrap;
    text-align: center;

    .btn {
      display: flex;
      justify-content: space-around;
      margin-top: 10px;
    }
  }

  .data-screening {
    .ant-col {
      text-align: center;

      .num {
        font-size: 40px;
        font-weight: bold;
      }

      .tip {
        font-size: 18px;
        color: #aaa;
        cursor: pointer;

        .anticon {
          margin-left: 6px;
        }
      }

      .visit {
        color: #aaa;
        font-size: 14px;
      }
    }
  }

  .space-btn {
    margin-top: 10px;

    button {
      height: 30px;
      box-sizing: border-box;
      line-height: 20px;

      &.space-btn_1 {
        color: #5d8dd4;
        border-color: #5d8dd4;
      }

      &.space-btn_2 {
        color: #F59A23 !important;
        border-color: #F59A23 !important;

        &:hover,
        .ant-btn:focus {
          color: #F59A23 !important;
          border-color: #F59A23 !important;
        }
      }

      &.space-btn_3 {
        color: #70B603 !important;
        border-color: #70B603 !important;

        &:hover,
        .ant-btn:focus {
          color: #70B603 !important;
          border-color: #70B603 !important;
        }
      }
    }
  }
}