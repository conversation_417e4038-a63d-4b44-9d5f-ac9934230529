/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/19 18:05
 * @LastEditTime: 2025/05/14 15:55
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/form.jsx
 * @Description: '营销活动新增编辑'
 */

import React, { useEffect, useState } from "react"
import { Button, Card, Form } from "antd"
import { CheckOutlined } from "@ant-design/icons"
import { qs2obj } from "common/object"
import SignIn from "./SignIn/home"
import PublishList from "./PublishList/home"
import "./form.less"

const MarketingActivityForm = (props) => {
  const [formForm] = Form.useForm()
  const [id, setId] = useState(null)
  const [selectType, setSelectType] = useState("")
  const scrollListData = [
    // {
    //   title: '抽奖老虎机',
    //   describe: '老虎机是抽奖活动的一种，支持配置多种奖品',
    //   type: 'way1'
    // },
    // {
    //   title: '抽奖大转盘',
    //   describe: '大转盘是抽奖活动的一种，支持配置多种奖品',
    //   type: 'way2'
    // },
    {
      title: "签到打卡",
      describe: "适合引导用户连续性地参与活动，提高活跃度。",
      type: "SIGN",
    },
    // {
    //   title: '答题有礼',
    //   describe: '用户参与答题活动，可获得奖品',
    //   type: 'way4'
    // },
    {
      title: "晒单",
      describe: "用户在活动内分享截图等内容证明参与活动的程度",
      type: "SHARE",
    },
  ]

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      setSelectType(type)
    } else {
      setSelectType("SIGN")
    }
  }, [])

  // 切换活动类型
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setSelectType(item.type)
    formForm.resetFields()
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="MarketingActivityForm">
      <Card
        extra={
          <Button type="primary" onClick={() => handleGoBack()}>
            返回
          </Button>
        }
        title={id ? "编辑活动" : "新增活动"}
        bordered={false}
        bodyStyle={{ display: "none" }}
      ></Card>
      <br />
      <Card>
        <h2 className="card-title">活动类型</h2>
        <ul className="scrollList">
          {scrollListData.map((item, index) => (
            <li
              key={index}
              className={
                selectType == item.type ? "listItem activeItem" : "listItem"
              }
              style={{
                background: `${
                  selectType != item.type && id ? "#c1c1c1" : "unset"
                }`,
                cursor: `${
                  selectType != item.type && id ? "unset" : "pointer"
                }`,
              }}
              onClick={() => handleScrollItem(item)}
            >
              <h2>{item.title}</h2>
              <p>{item.describe}</p>
              <div className="active">
                <CheckOutlined />
              </div>
            </li>
          ))}
        </ul>
      </Card>
      <br />
      {selectType == "SIGN" && <SignIn />}
      {selectType == "SHARE" && <PublishList />}
    </div>
  )
}

export default MarketingActivityForm
