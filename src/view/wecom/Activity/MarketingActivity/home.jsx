/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/19 17:30
 * @LastEditTime: 2023/11/21 16:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingActivity/home.jsx
 * @Description: '营销活动'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  message,
  Switch,
  Select,
  TreeSelect,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import FilterBar from "components/FilterBar/FilterBar";
import SysDictSelect from "components/select/SysDictSelect";
import OperateModal from "components/Modal/OperateModal/index";
import SysDictLabel from "components/select/SysDictLabel";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import LinkCard from "components/LinkCard/home";
import ExtendModal from "./comps/ExtendModal/home";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import "./home.less";
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { SHOW_PARENT } = TreeSelect;

const MarketingActivity = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [extendFormParams, setExtendFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [channelOptions, setChannelOptions] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "活动",
      width: "250px",
      dataIndex: "title",
      key: "title",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          <LinkCard
            isLink={false}
            data={{
              title: record.title,
              description: record.description,
              image: record.cover,
            }}
          />
        </div>
      ),
    },
    {
      title: "活动类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value == "SIGN" ? "签到打卡" : "晒单"}>
          {value == "SIGN" ? "签到打卡" : "晒单"}
        </Tooltip>
      ),
    },
    {
      title: "推广渠道",
      width: "160px",
      dataIndex: "channelNameList",
      key: "channelNameList",
      align: "center",
      render: (value, record, index) => {
        const content =
          value &&
          value.map((item, index) => (
            <span key={index}>
              {item}
              {index + 1 != value.length && "、"}
            </span>
          ));
        return value ? <Tooltip placement="topLeft" title={content}>{content}</Tooltip> : "-";
      },
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "可见范围",
      width: "160px",
      dataIndex: "visibleScopeDepartmentNameList",
      key: "visibleScopeDepartmentNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "数据",
      width: "160px",
      dataIndex: "statVO",
      key: "statVO",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          转发次数：{record?.shareCount}
          <br />
          访问次数：{record?.visitCount}
          <br />
          访问人数：{record?.visitorCount}
          <br />
          {record.type == "SIGN" && (
            <>
              签到人数：{record?.activityCustomerCount}
              <br />
              签到次数：{record?.activityCount}
              <br />
              送出积分：{record?.points}
            </>
          )}
          {/* 抽奖人数：{record?.signTimes }
       抽奖次数：{record?.signTimes }
       中奖人数：{record?.signTimes }
       中奖次数：{record?.signTimes } */}
          {record.type == "SHARE" && (
            <>
              晒单人数：{record?.activityCustomerCount}
              <br />
              晒单次数：{record?.activityCount}
            </>
          )}
        </div>
      ),
    },
    {
      title: "活动时间",
      width: "160px",
      dataIndex: "activityTime",
      key: "activityTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.activityStartTime}
          <br />至<br />
          {record.activityEndTime}
        </>
      ),
    },
    {
      title: "上下架时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: "活动状态",
      width: "160px",
      dataIndex: "shelfState",
      key: "shelfState",
      align: "center",
      render: (value, record, index) => (
        <>
          {(value == "notIssue" || value == "issued") && (
            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
          )}
          {(value == "Added" || value == "soldOut") && (
            <Switch
              checkedChildren="已上架"
              unCheckedChildren="已下架"
              checked={value == "Added"}
              onChange={(checked) => {
                onChangeSwitchStatus(checked, record);
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "更新人/更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: "详情" },
          { onClick: () => handleCopy(record), name: "复制" },
        ];
        if (record.shelfState == "Added") {
          opts.push({ onClick: () => handleExtend(record), name: "推广" });
        }
        if (record.shelfState == "notIssue") {
          opts.push({ onClick: () => handleShelves(record, "issued"), name: "发布" });
        }
        if (record.shelfState == "issued") {
          opts.push({ onClick: () => handleShelves(record, "notIssue"), name: "取消发布" });
        }
        if (record.shelfState == "notIssue" || record.shelfState == "soldOut") {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" });
        } else {
          opts.push({
            onClick: () => {
              message.error(
                record.shelfState == "issued"
                  ? "活动已发布，不可编辑，请取消发布或下架后重试！"
                  : "活动正在进行中，不可编辑，请取消发布或下架后重试！"
              )
            }, name: "编辑"
          });
        }
        if (record.shelfState == "notIssue") {
          opts.push({ onClick: () => handleDelete(record), name: "删除" });
        } else {
          opts.push({ onClick: () => { message.error("活动已存在数据，不可删除！") }, name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getChannelOptions();
    getTagCategoryTreeTwo();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minActivityTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxActivityTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.shelfUptime) {
        formData.minOnShelfTime = moment(formData.shelfUptime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxOnShelfTime = moment(formData.shelfUptime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.shelfUptime;
      }
      // formData.customerTagIdList =
      //   formData.customerTagIdList?.join(",") || null;
      // formData.createDeptIdList = formData.createDeptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/activity/page", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getChannelOptions = () => {
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setChannelOptions(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record;
    apiCall(
      `/activity/activity/update/shelfState/${id}?shelfState=${checked ? "Added" : "soldOut"
      }`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 发布
  const handleShelves = (record, state) => {
    const { id } = record;
    apiCall(
      `/activity/activity/update/shelfState/${id}?shelfState=${state}`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 创建
  const handleAdd = () => {
    props.history.push("/wecom/marketingActivity/form");
  };

  // 编辑
  const handleEdit = (record) => {
    const { id, type } = record;
    props.history.push({
      pathname: "/wecom/marketingActivity/form",
      search: `?id=${id}&type=${type}`,
    });
  };

  // 推广
  const handleExtend = (record) => {
    setExtendFormParams({
      visible: true,
      activityId: record.id,
      title: "推广渠道",
      onCancel: () => {
        setExtendFormParams({ visible: false });
        fetchList();
      },
    });
  };

  // 详情
  const handleDetail = (record) => {
    props.history.push({
      pathname: "/wecom/marketingActivity/detail",
      search: `?id=${record.id}&type=${record.type}`,
    });
  };

  // 复制
  const handleCopy = (record) => {
    const { title, id } = record;
    setOperateParams({
      visible: true,
      title: "复制确认",
      content: `您将为活动标题为【${title}】进行操作，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/activity/activity/copy/${id}`, "POST")
          .then((res) => {
            message.success("操作成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { title, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除活动【${title}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/activity/activity/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="signActivity">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="keyWord"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="活动标题、活动介绍" allowClear />
          </FormItem>
          <FormItem name="shelfState">
            <SysDictSelect placeholder="活动状态" dataset="SHELF_STATE" />
          </FormItem>
          <FormItem name="type">
            <Select placeholder="活动类型" allowClear>
              {/* <Option value="SIGN_IN">抽奖大转盘</Option>
              <Option value="SIGN_IN">抽奖老虎机</Option> */}
              <Option value="SIGN">签到打卡</Option>
              {/* <Option value="SIGN_IN">答题有礼</Option> */}
              <Option value="SHARE">晒单</Option>
            </Select>
          </FormItem>
          <FormItem name="channelId">
            <Select
              placeholder="推广渠道"
              fieldNames={{ label: "name", value: "id" }}
              options={channelOptions}
              allowClear
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          {/* <FormItem name="customerTagIdList"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={["customer"]} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     treeNodeFilterProp="title" */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}

          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useForm={formRef?.current}
            labelTreeData={labelTreeData}
          />
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem name="time" label="活动时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="shelfUptime" label="上下架时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <Button type="primary" onClick={() => handleAdd()}>
            创建活动
          </Button>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <ExtendModal params={extendFormParams} />
    </div>
  );
};

export default withRouter(MarketingActivity);
