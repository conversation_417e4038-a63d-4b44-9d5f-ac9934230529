/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/20 11:33
 * @LastEditTime: 2025/05/12 10:53
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/BirthdayWish/home.jsx
 * @Description: '生日祝福'
 */

import React, { useEffect, useRef, useState } from "react"
import {
  Spin,
  Button,
  Form,
  Input,
  Row,
  Col,
  Image,
  Tag,
  Radio,
  message,
  DatePicker,
  InputNumber,
  TimePicker,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import { PlusOutlined } from "@ant-design/icons"
import { removeInputEmpty } from "common/regular"
import MaterialListForm from "components/MaterialListForm/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import CTypeTransferModal from "components/TransferModal/CustomerType/home"
import SendModal from "../../comps/SendModal/home"
import UploadList from "../comps/UploadList/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import "./home.less"

const FormItem = Form.Item
const { RangePicker } = DatePicker

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}

const BirthdayWish = (props) => {
  const [formForm] = Form.useForm()
  const onRefMaterialListForm = useRef()
  const onRefCustomerList = useRef()
  const [loading, setLoading] = useState(false)
  const [sendObjectType, setSendObjectType] = useState(1)
  const [targetCount, setTargetCount] = useState(0)
  const [staffSelected, setStaffSelected] = useState([])
  const [groupSelected, setGroupSelected] = useState([])
  const [groupLeaderSelected, setGroupLeaderSelected] = useState([])
  const [msgList, setMsgList] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [sendParams, setSendParams] = useState({ visible: false })
  const [globalSetting, setGlobalSetting] = useState({})
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  })

  useEffect(() => {
    onRefMaterialListForm.current?.getInitMsgList([
      {
        type: "copyWriter",
        content: "",
        defaultFlag: true,
      },
    ])
    fetchList()
  }, [])

  const fetchList = async (params = {}) => {
    setLoading(true)
    await apiCall("/globalSetting/getOneByValid", "GET")
      .then((res) => {
        const { birthdayRemindSetting } = res
        if (birthdayRemindSetting) {
          const { scope, time, targetFilter, messages } = birthdayRemindSetting
          const { importCustomerListFileName, importCustomerListFileId } =
            targetFilter
          const sendObjectType = scope == "ALL" ? 1 : scope == "PART" ? 2 : 3
          formForm.setFieldsValue({
            ...birthdayRemindSetting,
            time: moment(time, "HH:mm"),
            sendObjectType,
          })
          setSendObjectType(sendObjectType)
          // 客户清单
          if (scope == "PART_LIST") {
            // onRefCustomerList?.current?.getInitCustomerList(targetFilter.importCustomerList);
            onRefCustomerList?.current?.getInitCustomerFile({
              name: importCustomerListFileName,
              file: importCustomerListFileId,
            })
          }
          onRefMaterialListForm?.current?.getInitMsgList(messages)
        }
        setGlobalSetting(res)
        getTargetCount()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 切换发送对象类型
  const onChangeRadioSendObjectType = (e) => {
    const { value } = e.target
    setSendObjectType(value)
    if (value != 3) {
      getTargetCount()
    }
  }

  // 获取任务对象数量
  const getTargetCount = async () => {
    setLoading(true)
    const type = formForm?.getFieldValue("sendObjectType")
    const paramsData = {
      targetType: "CUSTOMER",
    }

    if (type == 1) {
      await apiCall("/employeeTaskBatch/getTargetCount", "GET", paramsData)
        .then((res) => {
          if (res == 0) {
            message.error("没有满足条件的客户，请重新选择")
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender == "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        // groupIds: groupSelected?.map((item) => item.key)?.join(',') || null,
        gender: newGender ?? null,
        ...paramsData,
      }

      await apiCall("/employeeTaskBatch/getTargetCount", "GET", data)
        .then((res) => {
          if (res == 0) {
            message.error(
              `负责人${
                staffSelected?.map((item) => item.name)?.join("、") ||
                groupLeaderSelected?.map((item) => item.name)?.join("、")
              }名下无满足条件的客户，请重新选择`
            )
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  // 客户列表
  const handleSendCustomer = () => {
    setLoading(true)
    const sendObjectType = formForm?.getFieldValue("sendObjectType")
    const paramsData = {
      targetType: "CUSTOMER",
    }

    if (sendObjectType == 1) {
      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", paramsData)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender == "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        gender: newGender ?? null,
        ...paramsData,
      }

      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", data)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      const materialList = onRefMaterialListForm.current.getModifyMsgList()
      if (!materialList.length > 0) {
        message.error("祝福内容不能为空！")
        return false
      }
      if (onRefMaterialListForm.current.getCopyWriterValidate()) {
        // 校验资源文案字数
        return
      }
      setLoading(true)
      // let importCustomerList = [];
      // if (sendObjectType == 3) {
      //   importCustomerList = onRefCustomerList.current.getCustomerList();
      // }
      const {
        name,
        day,
        time,
        // sendObjectType,
        startTime = null,
        endTime = null,
        gender = null,
        tagIds,
        depEmployeeIdList,
        groupLeaderIds,
        file,
      } = formData

      const data = {
        ...globalSetting,
        birthdayRemindSetting: {
          name,
          day,
          time: moment(time).format("HH:mm"),
          scope:
            sendObjectType == 1
              ? "ALL"
              : sendObjectType == 2
              ? "PART"
              : "PART_LIST",
          targetFilter:
            sendObjectType == 1
              ? {}
              : {
                  groupIds: groupSelected?.map((item) => item.key) || null,
                  gender: gender == "all" ? null : gender,
                  startTime,
                  endTime,
                  groupLeaderIds: groupLeaderIds,
                  depEmployeeIdList: depEmployeeIdList,
                  tagIds: tagIds,
                  // importCustomerList,
                  importCustomerListFileName:
                    onRefCustomerList.current.getFileName(),
                  importCustomerListFileId: file,
                },
          messages: materialList,
        },
      }

      apiCall("/globalSetting/addOrModify", "POST", data)
        .then((res) => {
          message.success("保存成功！")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  return (
    <div className="BirthdayWish">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2 className="card-title">客户生日规则</h2>
              <FormItem
                label="规则名称"
                name="name"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入规则名称" }]}
              >
                <Input placeholder="请输入规则名称" allowClear />
              </FormItem>
              <FormItem
                label="提醒规则"
                required
                wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
              >
                <div style={{ display: "flex" }}>
                  <span style={{ marginTop: "5px" }}>每年客户生日前第</span>
                  <FormItem
                    rules={[{ required: true, message: "请输入天数" }]}
                    name="day"
                    style={{ margin: "0px 5px" }}
                    initialValue={0}
                  >
                    <InputNumber min={0} />
                  </FormItem>
                  <span style={{ marginTop: "5px" }}>天</span>
                  <FormItem
                    rules={[{ required: true, message: "请选择时分" }]}
                    name="time"
                    style={{ margin: "0px 5px" }}
                  >
                    <TimePicker
                      defaultValue={moment("9:00", "HH:mm")}
                      format="HH:mm"
                    />
                  </FormItem>
                  <span style={{ marginTop: "5px" }}>发送提醒</span>
                </div>
              </FormItem>
              <FormItem
                name="sendObjectType"
                label="提醒对象"
                initialValue={sendObjectType}
                rules={[{ required: true, message: "请选择提醒对象" }]}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                extra={
                  <>
                    预计发送客户数（已去重）：{targetCount} 人
                    <Button
                      style={{ marginLeft: "6px" }}
                      onClick={() => {
                        getTargetCount()
                      }}
                    >
                      刷新
                    </Button>
                    <Button
                      style={{ marginLeft: "10px" }}
                      type="primary"
                      onClick={() => {
                        handleSendCustomer()
                      }}
                    >
                      客户列表
                    </Button>
                  </>
                }
              >
                <Radio.Group onChange={onChangeRadioSendObjectType}>
                  <Radio value={1}>全部客户</Radio>
                  <Radio value={2}>部分客户-条件</Radio>
                  <Radio value={3}>部分客户-清单</Radio>
                </Radio.Group>
              </FormItem>

              {sendObjectType == 2 && (
                <div className="input-content-mask">
                  <FormItem
                    name="depEmployeeIdList"
                    label="客户负责人"
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.type !== curValues.type
                    }
                  >
                    <ETypeTransferModal
                      title="选择员工"
                      onChange={(value, options) => {
                        setStaffSelected(options)
                      }}
                    />
                  </FormItem>
                  <FormItem name="addTime" label="添加时间">
                    <RangePicker />
                  </FormItem>

                  <FormItem
                    name="groupIds"
                    label="群组"
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.type !== curValues.type
                    }
                    extra={
                      groupSelected.length > 0 ? (
                        <div>
                          {groupSelected.map((item, index) => (
                            <Tag
                              closable
                              style={{ marginTop: "8px" }}
                              key={index}
                              onClose={(e) => {
                                e.preventDefault()
                                setGroupSelected(
                                  groupSelected.filter((i) => i.key != item.key)
                                )
                              }}
                            >
                              {item.name}
                            </Tag>
                          ))}
                        </div>
                      ) : null
                    }
                  >
                    <Button
                      icon={<PlusOutlined />}
                      type="primary"
                      onClick={() => {
                        setCTypeTransferParams({
                          visible: true,
                          type: "group",
                          checkList: groupSelected,
                          onSubmit: (data) => {
                            setGroupSelected(data)
                            formForm.setFieldsValue({
                              groupIds: data.map((item) => item.id),
                            })
                            setCTypeTransferParams({ visible: false })
                          },
                          onCancel: () => {
                            setCTypeTransferParams({ visible: false })
                          },
                        })
                      }}
                    >
                      选择群组
                    </Button>
                  </FormItem>

                  <FormItem name="gender" label="选择性别" initialValue={"all"}>
                    <Radio.Group>
                      <Radio value={"all"}>全部</Radio>
                      <Radio value={"男性"}>仅男性</Radio>
                      <Radio value={"女性"}>仅女性</Radio>
                      <Radio value={"未知"}>未知</Radio>
                    </Radio.Group>
                  </FormItem>
                </div>
              )}
              {/* 上传客户清单 */}
              {sendObjectType == 3 && (
                <UploadList ref={onRefCustomerList} params={{ formForm }} />
              )}
              <h2 className="card-title">祝福内容</h2>
              <FormItem
                label=" "
                colon={false}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
              >
                <MaterialListForm
                  params={{
                    formRef: formForm,
                    menuList: ["image", "material"],
                    isNickname: false,
                    needScriptFlag: true,
                    materialAmount: msgList.length,
                    materialTabList: [
                      "Article",
                      "pageArticle",
                      "Video",
                      "copyWriter",
                      "MINI_PROGRAM",
                      "Picture",
                      "Poster",
                      "FORM",
                      "Product",
                    ],
                    extra: (
                      <div>
                        请按发送顺序添加祝福内容，最多9条消息，还可添加{" "}
                        {10 - msgList.length} 条
                      </div>
                    ),
                  }}
                  // 监听回调
                  callback={(params) => {
                    setMsgList(params.data)
                  }}
                  ref={onRefMaterialListForm}
                />
              </FormItem>
              <FormItem wrapperCol={{ offset: 8 }}>
                <Button type="primary" onClick={() => onSubmit()}>
                  保存规则
                </Button>
              </FormItem>
            </Form>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview type="session" listData={msgList} />
          </Col>
        </Row>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
      {/* <MaterialModal params={materialModalParams} /> */}
      {/* <EditFormModal params={editFormParams} /> */}
      {/* <CircleMaterialModal {...circleFormParams} /> */}
      <CTypeTransferModal {...CTypeTransferParams} />
      <SendModal {...sendParams} />
    </div>
  )
}

export default BirthdayWish
