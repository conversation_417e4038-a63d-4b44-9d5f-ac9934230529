/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/21 10:02
 * @LastEditTime: 2023/04/21 14:40
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingTask\Calendar\comps\FormModal.jsx
 * @Description: '新增编辑日程'
 */

import React, { useState, useEffect, useRef } from "react";
import { Modal, Form, message, Spin, Button, Input, DatePicker } from "antd";
import { apiCall } from "common/utils";
import { removeInputEmpty } from "common/regular";
import moment from "moment";

const FormItem = Form.Item;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const FormModal = (props) => {
  const { scheduleData = null } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      if (scheduleData) {
        let timer = setTimeout(() => {
          formRef.current.setFieldsValue({
            ...scheduleData,
            time: [
              moment(scheduleData.startTime),
              moment(scheduleData.endTime),
            ],
          });
          clearTimeout(timer);
        }, 100);
      }
    }
  }, [props]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        );
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        );
        delete formData.time;
      }
      const data = {
        id: scheduleData ? scheduleData.id : null,
        ...formData,
      };
      apiCall("/base/schedule/saveOrUpdate", "POST", data)
        .then((res) => {
          message.success("新增成功！");
          props.params?.onSubmit?.();
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={450}
      title="日程备注"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      className="courseModal"
    // footer={
    //   <div>
    //     <Button type="primary" onClick={onCancel}>
    //       取消
    //     </Button>
    //     <Button type="primary" onClick={onOk}>
    //       确认
    //     </Button>
    //   </div>
    // }
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            label="日程名称"
            name="name"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: "请输入课程名称" }]}
          >
            <Input placeholder="请输入课程名称" allowClear />
          </FormItem>
          <FormItem
            label="日程时间"
            name="time"
            rules={[{ required: true, message: "请选择日程时间" }]}
          >
            <RangePicker
              showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
              format="YYYY-MM-DD HH:mm"
              style={{ width: "100%" }}
            />
          </FormItem>
          <FormItem
            name="content"
            label="日程内容"
            rules={[{ required: true, message: "请输入日程内容" }]}
          >
            <TextArea
              placeholder="请输入日程内容"
              allowClear
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
