/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/17 17:41
 * @LastEditTime: 2023/04/23 14:31
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingTask\Calendar\home.jsx
 * @Description: '运营日历'
 */
import React, { useEffect, useState } from 'react';
import {
  Calendar,
  Divider,
  Typography,
  Button,
  Select,
  Row,
  Col,
  Radio,
  Spin,
  message,
} from 'antd';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import moment from 'moment';
import { apiCall } from 'common/utils';
import AppStore from 'stores/AppStore';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';
import './home.less';
const { Paragraph } = Typography;

const OperateCalendar = (props) => {
  const stores = AppStore;
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState([]);
  const [dateCellData, setDateCellData] = useState({});
  const [formParams, setFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { date } = params;
    const data = {
      date: date || moment().format('YYYY-MM-DD'),
    };
    await apiCall('/employeeTaskBatch/calendar', 'GET', data)
      .then((res) => {
        setListData(res);
        const cellData = res.find(
          (item) => item.date == (date || moment().format('YYYY-MM-DD'))
        );
        setDateCellData(cellData ?? []);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 新增日程
  const handleAdd = () => {
    setFormParams({
      visible: true,
      title: '新增日程',
      onSubmit: () => {
        setFormParams({ visible: false });
        fetchList({ date: dateCellData.date });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 编辑日程
  const handleEdit = (data) => {
    setFormParams({
      visible: true,
      title: '编辑日程',
      scheduleData: data,
      onSubmit: () => {
        setFormParams({ visible: false });
        fetchList({ date: dateCellData.date });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 删除日程
  const handleDelet = (data) => {
    const { id, name } = data;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除日程【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/base/schedule/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList({ date: dateCellData.date });
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onPanelChange = (value) => {
    fetchList({ date: value.format('YYYY-MM-DD') });
  };

  const handleDateCell = (data) => {
    console.log(data, 'datadatadatadata');
    setDateCellData(data);
  };

  // const getMonthData = (value) => {
  //   if (value.month() === 8) {
  //     return 1394;
  //   }
  // };

  // const monthCellRender = (value) => {
  //   const num = getMonthData(value);
  //   return num ? (
  //     <div className="notes-month">
  //       <section>{num}</section>
  //       <span>Backlog number</span>
  //     </div>
  //   ) : null;
  // };

  const dateCellRender = (value) => listData.map((item) => {
    if (moment(value).format('YYYY-MM-DD') == item.date) {
      return (
        <div
          className="calendarEvents"
          key={item.date}
          onClick={() => {
            handleDateCell(item);
          }}
        >
          {item.employeeTaskBatchList.map((atem) => (
            <Paragraph
              style={{
                backgroundColor:
                  atem.type == 'NOTICE' || atem.type == 'INFO_PUSH'
                    ? '#f28300'
                    : '#5d8dd4',
              }}
              className="events"
              ellipsis={{ rows: 1, tooltip: true }}
              key={atem.id}
            >
              {atem.name}
            </Paragraph>
          ))}
          {item.scheduleList.map((atem) => (
            <Paragraph
              className="events"
              style={{ backgroundColor: '#ec808d' }}
              ellipsis={{ rows: 1, tooltip: true }}
              key={atem.id}
            >
              {atem.name}
            </Paragraph>
          ))}
        </div>
      );
    }
  });

  const cellRender = (current) => dateCellRender(current);

  return (
    <div className="operateCalendar">
      <Spin spinning={loading}>
        <div className="operateCalendar-flex">
          <Calendar
            onPanelChange={onPanelChange}
            dateCellRender={cellRender}
            className="Calendar-left"
            headerRender={({ value, type, onChange, onTypeChange }) => {
              const start = 0;
              const end = 12;
              const monthOptions = [];

              let current = value.clone();
              const localeData = value.localeData();
              const months = [];
              for (let i = 0; i < 12; i++) {
                current = current.month(i);
                months.push(localeData.monthsShort(current));
              }

              for (let i = start; i < end; i++) {
                monthOptions.push(
                  <Select.Option key={i} value={i} className="month-item">
                    {months[i]}
                  </Select.Option>
                );
              }

              const year = value.year();
              const month = value.month();
              const options = [];
              for (let i = year - 10; i < year + 10; i += 1) {
                options.push(
                  <Select.Option key={i} value={i} className="year-item">
                    {i}
                  </Select.Option>
                );
              }
              return (
                <div style={{ padding: 8 }}>
                  <Row gutter={8} align={'middle'} justify={'end'}>
                    {/* <Col>
                      <Radio.Group
                        size="small"
                        onChange={(e) => onTypeChange(e.target.value)}
                        value={type}
                      >
                        <Radio.Button value="month">月</Radio.Button>
                        <Radio.Button value="year">年</Radio.Button>
                      </Radio.Group>
                    </Col> */}
                    <Col>
                      <Select
                        size="small"
                        dropdownMatchSelectWidth={false}
                        className="my-year-select"
                        value={year}
                        onChange={(newYear) => {
                          const now = value.clone().year(newYear);
                          onChange(now);
                        }}
                      >
                        {options}
                      </Select>
                    </Col>
                    <Col>
                      <Select
                        size="small"
                        dropdownMatchSelectWidth={false}
                        value={month}
                        onChange={(newMonth) => {
                          const now = value.clone().month(newMonth);
                          onChange(now);
                        }}
                      >
                        {monthOptions}
                      </Select>
                    </Col>
                    <Col>
                      {stores?.state?.User?.roleDataType == 4 && (
                        <Button type="primary" onClick={() => handleAdd()}>
                          新增日程
                        </Button>
                      )}
                    </Col>
                  </Row>
                </div>
              );
            }}
          />
          <Divider type="vertical" style={{ height: 'auto' }} />
          <div className="Calendar-right">
            <h4>{dateCellData.date}</h4>
            {dateCellData.scheduleList?.map((item) => (
              <div className="calendarRight-info" key={item.id}>
                <div className="calendarRight-info-flex">
                  <h4>{item.name}</h4>

                  {stores?.state?.User?.roleDataType == 4 && (
                    <div>
                      <FormOutlined
                        className="schedule-icon"
                        onClick={() => handleEdit(item)}
                      />
                      <DeleteOutlined
                        className="schedule-icon"
                        onClick={() => {
                          handleDelet(item);
                        }}
                      />
                    </div>
                  )}
                </div>
                <div className="info-time">{item.createTime}</div>
                <div style={{ fontSize: '13px' }}>{item.content}</div>
              </div>
            ))}
            {dateCellData.employeeTaskBatchList?.map((item) => (
              <div className="calendarRight-info" key={item.id}>
                <h4>{item.name}</h4>
                <div className="info-time">
                  {item.startTime} 至 {item.endTime}
                </div>
                <div style={{ fontSize: '13px' }}>{item.remark}</div>
              </div>
            ))}
          </div>
        </div>
      </Spin>
      <OperateModal params={operateParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default OperateCalendar;
