.operateCalendar {
  background-color: #fff;
  .operateCalendar-flex {
    display: flex;
    flex-direction: row;
    .Calendar-left {
      flex: 1;
      .ant-picker-calendar-date-today {
        border-color: #5d8dd4 !important;
        // background: #e6f7ff !important;
        .ant-picker-calendar-date-value {
          color: #5d8dd4 !important;
        }
      }
      .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
        background: #e6f7ff !important;
        .ant-picker-calendar-date-value {
          color: #5d8dd4 !important;
        }
      }
      .ant-radio-button-wrapper-checked:not(
          .ant-radio-button-wrapper-disabled
        ) {
        border-color: #5d8dd4 !important;
        color: #5d8dd4 !important;
        &::before {
          background-color: #5d8dd4 !important;
        }
      }
      .ant-radio-button-wrapper:hover {
        color: #5d8dd4 !important;
      }
      .calendarEvents {
        height: 100%;
        .events {
          color: #fff;
          font-size: 12px;
          padding: 0 5px;
        }
      }
      .ant-picker-calendar-date-content {
        // 滚动条整体部分
        &::-webkit-scrollbar {
          width: 6px; //对垂直方向滚动条
          height: 6px; //对水平方向滚动条
        }

        //滚动的滑块
        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background-color: #c1c1c1;//滚动条的颜色
        }

        //内层滚动槽
        &::-webkit-scrollbar-track-piece {
          background-color: #f2f2f2;
        }
      }
    }
    .Calendar-right {
      width: 300px;
      height: 662px;
      overflow-y: auto;
      .calendarRight-info {
        background: #f2f2f2;
        padding: 10px;
        margin-bottom: 10px;
        .info-time {
          font-size: 12px;
          color: #7f7f7f;
        }
        .calendarRight-info-flex{
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          h4{
            width: 140px;
            word-break: break-all;
          }
          .schedule-icon{
            color: #f28300;
            &:last-child{
              color: #5d8dd4;
              margin-left: 5px;
            }
          }
        }
      }
        // 滚动条整体部分
        &::-webkit-scrollbar {
          width: 6px; //对垂直方向滚动条
          height: 6px; //对水平方向滚动条
        }

        //滚动的滑块
        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background-color: #c1c1c1;//滚动条的颜色
        }

        //内层滚动槽
        &::-webkit-scrollbar-track-piece {
          background-color: #f2f2f2;
        }
    }
  }
}
