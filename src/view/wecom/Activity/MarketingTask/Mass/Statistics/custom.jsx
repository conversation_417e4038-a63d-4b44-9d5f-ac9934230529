/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/11/07 14:48
 * @LastEditTime: 2023/11/17 10:51
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/Statistics/custom.jsx
 * @Description: '客户统计'
 */

import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  Select,
  Table,
  Tooltip,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import FilterBar from 'components/FilterBar/FilterBar';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import { timeStamp } from 'common/date';

const FormItem = Form.Item;
const { Option } = Select;

const InteractionData = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '160px',
      dataIndex: 'targetName',
      key: 'targetName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '对应员工',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '送达状态',
      width: '160px',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => <SysDictLabel dataset="TASK_BATCH_TARGET_STATUS" dictkey={value} />
    },
    {
      title: '状态更新时间',
      width: '160px',
      dataIndex: 'receiveTime',
      key: 'receiveTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.receiveTime) - timeStamp(b.receiveTime),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    if (id) {
      fetchList();
    }
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.depEmpList = formData.depEmpList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        taskBatchId: id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/taskBatchTarget', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="InteractionData">
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="targetName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户名称" allowClear />
          </FormItem>
          <FormItem
            name="depEmpList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="选择员工" mode={['dep', 'emp']} />
          </FormItem>
          <FormItem name="statusEnum" >
            <SysDictSelect placeholder="送达状态" dataset="TASK_BATCH_TARGET_STATUS" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>

      <Card
        bordered={false}
        bodyStyle={{ padding: 'unset' }}
      >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(InteractionData);
