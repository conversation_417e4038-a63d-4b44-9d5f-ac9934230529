/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/10 11:03
 * @LastEditTime: 2023/12/04 14:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/Statistics/home.jsx
 * @Description: '群发统计'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs, Button, Descriptions, Skeleton } from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
// 模块组件
import Staff from './staff';
import Custom from './custom';
import CustomGroup from './customGroup';
import Interaction from './interaction';
import './home.less';

const { TabPane } = Tabs;

const MassStatistical = (props) => {
  const [type, setType] = useState(null);
  const [tabsIndex, setTabsIndex] = useState('1');
  const [loading, setLoading] = useState(null);
  const [infoData, setInfoData] = useState(null);

  useEffect(() => {
    const { type, id } = qs2obj(props.location.search);
    getInfoData({ id: id })
    setType(type)
  }, []);

  const getInfoData = (params = {}) => {
    setLoading(true)
    const { id } = params;
    const data = {
      id: id,
    };
    apiCall('/employeeTaskBatch/get', 'GET', data).then((res) => {
      setInfoData(res)
    }).catch((err) => {
      console.log(err);
    }).finally(() => {
      setLoading(false)
    });
  };

  const onChangeTabs = (index) => {
    setTabsIndex(index);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className="MassStatistical">
      <Card
        loading={loading}
        bordered={false}
        title={infoData ? <Descriptions column={1} contentStyle={{ whiteSpace: "initial" }}>
          <Descriptions.Item label="任务名称">{infoData?.name}</Descriptions.Item>
          <Descriptions.Item label="任务时间">{infoData?.startTime} 至 {infoData?.endTime}</Descriptions.Item>
          <Descriptions.Item label="任务说明">{infoData?.remark}</Descriptions.Item>
        </Descriptions> : <Skeleton />}
        extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>}
      >
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          <TabPane tab="员工统计" key="1">
            <Staff />
          </TabPane>
          {/* {(type == 'CUSTOMER' || type == 'MOMENT') &&
            <TabPane tab="客户统计" key="2">
              <Custom />
            </TabPane>
          }
          {type == 'CHAT' &&
            <TabPane tab="客户群统计" key="3">
              <CustomGroup />
            </TabPane>
          } */}
          {type == 'MOMENT' &&
            <TabPane tab="互动统计" key="4">
              <Interaction />
            </TabPane>
          }
        </Tabs>
      </Card>
    </div>
  );
};

export default MassStatistical;
