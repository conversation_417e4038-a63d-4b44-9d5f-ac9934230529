/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/10/25 11:33
 * @LastEditTime: 2023/12/05 10:07
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/Statistics/interaction.jsx
 * @Description: '互动统计'
 */

import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Select,
  Table,
  Tooltip,
  DatePicker,
} from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import { timeStamp } from 'common/date';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotStatistic from 'components/WibotStatistic/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;

const InteractionData = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '160px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '对应员工',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '互动类型',
      width: '160px',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (value, record, index) => (
        <span>{value == 'LIKE' ? '点赞' : '评论'}</span>
      ),
    },
    {
      title: '互动时间',
      width: '160px',
      dataIndex: 'addTime',
      key: 'addTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.addTime) - timeStamp(b.addTime),
    },
  ];
  const [statisticData, setStatisticData] = useState([
    {
      title: '点赞客户数',
      value: 0,
    },
    {
      title: '评论客户数',
      value: 0,
    },
    {
      title: '客户访问人数',
      value: 0,
      describe: '客户访问链接资源时，可增加统计数据',
    },
  ]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      fetchList({ taskBatchId: id });
      getStatisticData(id);
    }
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minAddTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAddTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      formData.depEmployeeIdList =
        formData.depEmployeeIdList?.join(',') || null;
      const { taskBatchId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        taskBatchId: taskBatchId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/taskBatchStat', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getStatisticData = (id) => {
    setLoading(true);
    const data = {
      taskBatchId: id,
    };
    apiCall('/taskBatchStat/summary', 'GET', data)
      .then((res) => {
        const { commentCount, likeCount, visitorCount } = res;
        let newStatisticData = JSON.parse(JSON.stringify(statisticData));
        newStatisticData[0].value = likeCount;
        newStatisticData[1].value = commentCount;
        newStatisticData[2].value = visitorCount;
        setStatisticData(newStatisticData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.minAddTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAddTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      formData.depEmployeeIdList =
        formData.depEmployeeIdList?.join(',') || null;
      const data = {
        taskBatchId: id,
        ...formData,
      };
      apiCall('/taskBatchStat/export', 'GET', data, null, {
        isExit: true,
        title: `互动统计.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="InteractionData">
      <div style={{ marginBottom: '20px' }}>
        <WibotStatistic
          list={statisticData}
          span={6}
        />
      </div>

      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="typeList">
            <Select placeholder="互动类型" allowClear>
              <Option value="LIKE">点赞</Option>
              <Option value="COMMENT">评论</Option>
            </Select>
          </FormItem>
          <FormItem
            name="depEmployeeIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="选择员工" multiple />
          </FormItem>
          <FormItem name="time" label="互动时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>

      <Card
        bordered={false}
        bodyStyle={{ padding: 'unset' }}
      >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(InteractionData);
