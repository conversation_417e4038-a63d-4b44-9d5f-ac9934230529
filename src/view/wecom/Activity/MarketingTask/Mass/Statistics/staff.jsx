/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/10/25 11:33
 * @LastEditTime: 2023/11/08 13:58
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/Statistics/staff.jsx
 * @Description: '员工统计'
 */

import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Select,
  Table,
  Tooltip,
} from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import FilterBar from 'components/FilterBar/FilterBar';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';

const FormItem = Form.Item;
const { Option } = Select;

const InteractionData = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '员工名称',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      render: (value, record, index) => {
        const title = (
          <div
            style={{
              textAlign: 'left',
              whiteSpace: 'normal',
            }}
          >
            {value}
            <br />
            {record.departmentName}
          </div>
        );
        return (
          <Tooltip placement="topLeft" title={title}>
            {title}
          </Tooltip>
        );
      },
    },
    // {
    //   title: '预计送达客户数',
    //   width: '160px',
    //   dataIndex: 'allSendCount',
    //   key: 'allSendCount',
    //   align: 'center',
    //   ellipsis: 'true',
    // },
    // {
    //   title: '实际送达客户数',
    //   width: '160px',
    //   dataIndex: 'actualSendCount',
    //   key: 'actualSendCount',
    //   align: 'center',
    //   ellipsis: 'true',
    // },
    {
      title: '查看状态',
      width: '160px',
      dataIndex: 'readStatus',
      key: 'readStatus',
      align: 'center',
      render: (value, record, index) => (
        <span style={{ color: value ? '#08b855' : '#d5001c' }}>
          {value ? '已读' : '未读'}
        </span>
      ),
    },
    {
      title: '发送状态',
      width: '160px',
      dataIndex: 'issueStatus',
      key: 'issueStatus',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <div>
          <SysDictLabel dataset="TASK_BATCH_EXECUTOR_STATUS" dictkey={value} />
          {record.executeTime}
        </div>
      )
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    if (id) {
      fetchList();
    }
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.depEmpList = formData.depEmpList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        taskBatchId: id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/taskBatchExecutor', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="InteractionData">
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="depEmpList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="选择员工" mode={['dep', 'emp']} />
          </FormItem>
          <FormItem name="readStatus">
            <Select placeholder="查看状态" allowClear>
              <Option value={true}>已读</Option>
              <Option value={false}>未读</Option>
            </Select>
          </FormItem>
          <FormItem name="issueStatus" >
            <SysDictSelect placeholder="发送状态" dataset="TASK_BATCH_EXECUTOR_STATUS" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>

      <Card
        bordered={false}
        bodyStyle={{ padding: 'unset' }}
      >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(InteractionData);
