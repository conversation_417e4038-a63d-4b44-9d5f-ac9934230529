/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/10/09 11:40
 * @LastEditTime: 2024/10/16 09:19
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/comps/CircleMsgContent.jsx
 * @Description: '群发朋友圈内容'
 */

import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import {
  Button,
  Form,
  Input,
  Row,
  Col,
  Image,
  Typography,
  Upload,
  message,
  Tag,
} from "antd";
import { apiCall } from "common/utils";
import { PlusOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { normFile, trimAll } from "common/regular";
import { compressImage, base64ToFile, beforeUpload } from "common/image";
import AppletCard from "components/AppletCard/home";
import MaterialModal from "components/Modal/MaterialModal/home";
import WibotUpload from "components/WibotUpload/home";
import "./CircleMsgContent.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { Paragraph } = Typography;
const { TextArea } = Input;
const { CheckableTag } = Tag;

const CircleMsgContent = forwardRef((props, ref) => {
  const { formForm, changeLoading, isDisabled = false } = props.params;
  const [circleMsgList, setCircleMsgList] = useState([
    {
      type: "copyWriter",
      content: "",
    },
  ]);
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  });
  const [isDisabledBtn, setIsDisabledBtn] = useState(false);

  useImperativeHandle(ref, () => ({
    getInitMsgList,
    // getModifyMsgList,
  }));

  useEffect(() => {
    //如果所选的资源飞图片海报类型并且已有资源数2条则不可再选择任何资源
    if (
      !circleMsgList.some(
        (item) => item.type == "Picture" || item.type == "Poster"
      ) &&
      circleMsgList.length == 2
    ) {
      setIsDisabledBtn(true);
    } else {
      setIsDisabledBtn(false);
    }
    props.onChangeMsgList(circleMsgList);
    console.log(circleMsgList, "circleMsgList");
  }, [circleMsgList]);

  const getInitMsgList = (messages) => {
    const newCircleMsgList = JSON.parse(JSON.stringify(messages));
    let fieldCircleMsgList = [];
    newCircleMsgList?.forEach((item) => {
      if (item.type == "Video") {
        item.videoUrl = item.fileId ? item.fileId[0] : "";
      } else if (item.fileId && Array.isArray(item.fileId)) {
        item.imageUrl = item.fileId ? item.fileId[0] : "";
      } else if (item.images && Array.isArray(item.images)) {
        item.imageUrl = item.images ? item.images[0] : "";
      }
    });
    newCircleMsgList?.forEach((item) => {
      if (item.type == "copyWriter") {
        fieldCircleMsgList.push(item.content);
      } else {
        fieldCircleMsgList.push(item.type);
      }
    });
    setCircleMsgList(newCircleMsgList);
    formForm.setFieldsValue({
      circleMsgList: fieldCircleMsgList,
    });
  };

  //添加素材
  const handleAddMsg = (type) => {
    const newCircleMsgList = JSON.parse(JSON.stringify(circleMsgList));
    const fieldCircleMsgList = formForm.getFieldValue("circleMsgList");
    const isPictureType = newCircleMsgList.some(
      (item) => item.type == "Picture" || item.type == "Poster"
    ); //已选的资源中有一张是图片就是图片类型

    if (isPictureType && newCircleMsgList.length >= 10) {
      message.warning("图片不能超过9条！");
      return;
    }

    if (type == "material") {
      let materialParams = {};
      if (isPictureType) {
        materialParams = {
          multiple: true,
          materialAmount: newCircleMsgList.length - 1,
          tabType: "Picture",
          tabList: ["Picture", "Poster",],
        };
      } else {
        materialParams = {
          multiple: false,
          tabType: "Article",
          tabList: [
            "Article",
            "pageArticle",
            "Video",
            "Picture",
            "Poster",
            "FORM",
            "Product",
            "ResourceSet",
          ],
          isOnlyPicMultiple: true,
        };
      }
      setMaterialModalParams({
        visible: true,
        ...materialParams,
        onSubmit: (data) => {
          setMaterialModalParams({ visible: false });
          if (isPictureType || Array.isArray(data)) {
            //图片类型多选处理
            const msgArr = [];
            data.forEach((item) => {
              switch (item.type) {
                case "Poster":
                  item.imageUrl = (item.fileId && item.fileId[0]) || "";
                  break;
                case "Picture":
                  item.imageUrl = (item.fileId && item.fileId[0]) || "";
                  break;
              }
              item.isMaterial = true;
              msgArr.push(item.type);
            });
            setCircleMsgList(newCircleMsgList.concat(data));
            formForm.setFieldsValue({
              circleMsgList: fieldCircleMsgList.concat(msgArr),
            });
            return false;
          } else if (data.resourceList) {
            //根据有无resourceList字段判断是否资源集类型
            handleResourceSetType(
              JSON.parse(JSON.stringify(data.resourceList))
            );
            return false;
          }
          //正常的单选类型处理
          switch (data.type) {
            case "copyWriter":
              data.content = data.copyWriter;
              break;
            case "pageArticle":
              data.imageUrl = (data.images && data.images[0]) || "";
              data.fileId = data.images;
              data.url = data.transitUrl || data.url;
              break;
            case "Article":
              data.imageUrl = (data.images && data.images[0]) || "";
              data.fileId = data.images;
              data.url = data.transitUrl || data.url;
              break;
            case "Video":
              data.videoUrl = (data.videos && data.videos[0]) || "";
              data.url = data.transitUrl || data.url;
              data.fileId = data.videos;
              break;
            case "FORM":
              delete data.extraSetting;
              delete data.jsonContent;
              delete data.commitActionContent;
              data.imageUrl = data.shareImage || data.image;
              data.fileId = data.images;
              data.title = data.shareTitle;
              data.description = data.shareDescription;
              data.url = data.transitUrl || data.url;
              break;
            case "Product":
              data.imageUrl = (data.images && data.images[0]) || "";
              data.fileId = data.images;
              data.url = data.transitUrl || data.url;
              break;
          }
          data.isMaterial = true;
          fieldCircleMsgList.push(data.type);
          newCircleMsgList.push(data);
          setCircleMsgList(newCircleMsgList);
          formForm.setFieldsValue({
            circleMsgList: fieldCircleMsgList,
          });
        },
        onCancel: () => {
          setMaterialModalParams({ visible: false });
        },
      });
    } else if (type == "Picture") {
      newCircleMsgList.push({
        type: type,
        imageUrl: "",
      });
      setCircleMsgList(newCircleMsgList);
      fieldCircleMsgList.push('');
      formForm.setFieldsValue({
        circleMsgList: fieldCircleMsgList,
      });
    }
  };

  //资源集类型特殊处理
  const handleResourceSetType = (data) => {
    data.forEach((item) => {
      if (item.type == "POSTER_TOOL") {
        item.type = "Poster";
      }
    });
    let newCircleMsgList = JSON.parse(JSON.stringify(circleMsgList));
    let fieldCircleMsgList = formForm.getFieldValue("circleMsgList");
    const typeArr = data.map((item) => item.type);
    const findCopyWriter = data.find((item) => item.type == "copyWriter");
    const copyWriterCount = countType(typeArr, "copyWriter"); //资源集中包含的文案类型数
    if (findCopyWriter) {
      const allCopyWriter = data.filter((item) => item.type === "copyWriter").map(
        (item) => item.copyWriter
      ).join("\n");
      newCircleMsgList[0].content = allCopyWriter;
      fieldCircleMsgList[0] = allCopyWriter;
    }
    if (typeArr.includes("copyWriter") && typeArr.length == 1) {
      //纯文案模式
    } else if (
      (typeArr.every(
        (item) => item == "copyWriter" || item == "Picture" || item == "Poster"
      ) &&
        copyWriterCount == 1) ||
      typeArr.every((item) => item == "Picture" || item == "Poster")
    ) {
      //纯图片模式
      const filterPicTypes = data
        .filter((item) => item.type == "Picture" || item.type == "Poster")
        .slice(0, 9);
      newCircleMsgList = newCircleMsgList.concat(filterPicTypes);
      fieldCircleMsgList = fieldCircleMsgList.concat(
        filterPicTypes.map((item) => item.type)
      );
    } else if (
      (typeArr.includes("copyWriter") &&
        typeArr.includes("Video") &&
        typeArr.length == 2) ||
      (typeArr.includes("Video") && typeArr.length == 1)
    ) {
      //纯视频模式
      const findVideoItem = data.find((item) => item.type == "Video");
      newCircleMsgList.push(findVideoItem);
      fieldCircleMsgList.push(findVideoItem.type);
    } else if (
      (typeArr.includes("copyWriter") &&
        typeArr.some(
          (item) =>
            item == "Article" ||
            item == "pageArticle" ||
            item == "FORM" ||
            item == "Product"
        ) &&
        typeArr.length == 2) ||
      (typeArr.some(
        (item) =>
          item == "Article" ||
          item == "pageArticle" ||
          item == "FORM" ||
          item == "Product"
      ) &&
        typeArr.length == 1)
    ) {
      //纯链接模式
      const obj = data.find((item) => item.type != "copyWriter");
      newCircleMsgList.push(obj);
      fieldCircleMsgList.push(obj.type);
    } else {
      //智能搭配
      if (data.every((i) => i.type == "MINI_PROGRAM")) {
        message.warning("小程序类型无法分享企微朋友圈！");
        return false;
      }
      message.warning(
        "所选资源不符合企业朋友圈的搭配要求，系统将进行智能搭配！"
      );
      const filterTypes = ["copyWriter", "MINI_PROGRAM"];
      const findItem =
        data.find((item) => !filterTypes.includes(item.type)) ?? {}; //排除文案、小程序类型
      if (findItem.type == "Picture" || findItem.type == "Poster") {
        const filterPicTypes = data
          .filter((item) => item.type == "Picture" || item.type == "Poster")
          .slice(0, 9);
        newCircleMsgList = newCircleMsgList.concat(filterPicTypes);
        fieldCircleMsgList = fieldCircleMsgList.concat(
          filterPicTypes.map((item) => item.type)
        );
      } else if (findItem.type == "Video") {
        const findVideoItem = data.find((item) => item.type == "Video");
        newCircleMsgList.push(findVideoItem);
        fieldCircleMsgList.push(findVideoItem.type);
      } else if (
        findItem.type == "Article" ||
        findItem.type == "pageArticle" ||
        findItem.type == "FORM" ||
        findItem.type == "Product"
      ) {
        const findLinkItem = data.find(
          (item) =>
            item.type == "Article" ||
            item.type == "pageArticle" ||
            item.type == "FORM" ||
            item.type == "Product"
        );
        newCircleMsgList.push(findLinkItem);
        fieldCircleMsgList.push(findLinkItem.type);
      }
    }

    newCircleMsgList.forEach((item) => {
      switch (item.type) {
        case "pageArticle":
          item.imageUrl = (item.images && item.images[0]) || "";
          item.fileId = item.images;
          item.url = item.transitUrl || item.url;
          break;
        case "Article":
          item.imageUrl = (item.images && item.images[0]) || "";
          item.fileId = item.images;
          item.url = item.transitUrl || item.url;
          break;
        case "Video":
          item.videoUrl = (item.videos && item.videos[0]) || "";
          item.url = item.transitUrl || item.url;
          item.fileId = item.videos;
          break;
        case "Poster":
          item.imageUrl = (item.images && item.images[0]) || "";
          item.fileId = item.images;
          // item.imageUrl =
          //   (item.fileId && item.fileId[0]) ||
          //   (item.images && item.images[0]) ||
          //   "";
          break;
        case "Picture":
          item.imageUrl = (item.images && item.images[0]) || "";
          item.fileId = item.images;
          break;
        case "FORM":
          delete item.extraSetting;
          delete item.jsonContent;
          delete item.commitActionContent;
          item.imageUrl = item.shareImage || item.image;
          item.fileId = item.images;
          item.title = item.shareTitle;
          item.description = item.shareDescription;
          item.url = item.transitUrl || item.url;
          break;
        case "Product":
          item.imageUrl = (item.images && item.images[0]) || "";
          item.fileId = item.images;
          item.url = item.transitUrl || item.url;
          break;
      }
      item.isMaterial = true;
    });
    console.log(newCircleMsgList, fieldCircleMsgList, ";;;;;");
    setCircleMsgList(newCircleMsgList);
    formForm.setFieldsValue({
      circleMsgList: fieldCircleMsgList,
    });
  };

  //计算数组中某个资源类型出现的次数
  const countType = (arr, num) => {
    const countArr = arr.filter(function isBigEnough (value) {
      return value === num;
    });
    return countArr.length;
  };

  // 消息列表-删除选中
  const handleDelMsg = (order) => {
    const newCircleMsgList = JSON.parse(JSON.stringify(circleMsgList));
    const fltMsgList = newCircleMsgList.filter((el, idx) => idx !== order);
    setCircleMsgList(fltMsgList);
    const fieldCircleMsgList = formForm.getFieldValue("circleMsgList");
    const newFieldCircleMsgList = fieldCircleMsgList.filter(
      (el, idx) => idx !== order
    );
    formForm.setFieldsValue({
      circleMsgList: newFieldCircleMsgList,
    });
  };

  // 自定义上传图片
  const customRequest = (config, index) => {
    const File = config.file;
    const options = {
      quality: 0.9,
    };
    let newCircleMsgList = JSON.parse(JSON.stringify(circleMsgList));
    const fieldCircleMsgList = formForm.getFieldValue("circleMsgList");
    // 用于图片上传中限制
    newCircleMsgList[index].imageUrl = "loading";
    setCircleMsgList(newCircleMsgList);
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append("file", base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall("/file/image", "POST", data)
        .then((res) => {
          const { fileId, fileUrl } = res;
          newCircleMsgList[index].imageUrl = fileUrl;
          newCircleMsgList[index].fileId = [fileId];
          setCircleMsgList([...newCircleMsgList]);
          fieldCircleMsgList[index] = "Picture";
          formForm.setFieldsValue({
            circleMsgList: fieldCircleMsgList,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    });
  };

  //产品码开启关闭
  const handleProductTagChange = async (data, checked) => {
    const { type, id, showProduct } = data;
    const newCircleMsgList = JSON.parse(JSON.stringify(circleMsgList));
    if (type == "POSTER_TOOL" || type == "Picture" || type == "Poster") {
      changeLoading(true);
      await apiCall("/info/infoResource/getPic", "GET", {
        id,
        original: showProduct,
      })
        .then((res) => {
          const { images } = res;
          data.imageUrl = images ? images[0] : "";
          data.showProduct = !data.showProduct;
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          changeLoading(false);
        });
    }
    newCircleMsgList.forEach((item) => {
      if (item.id == data.id) {
        if (
          item.type == "POSTER_TOOL" ||
          item.type == "Picture" ||
          item.type == "Poster"
        ) {
          item.imageUrl = data.imageUrl;
          item.image = data.imageUrl;
          item.fileId = [data.imageUrl];
          item.showProduct = data.showProduct;
        } else {
          item.showProduct = !data.showProduct;
        }
      }
    });
    setCircleMsgList(newCircleMsgList);
  };

  return (
    <div className="CircleMsgContent">
      <FormItem
        required={props.required}
        label="朋友圈内容"
        style={{ marginBottom: "0px" }}
        extra="请根据需要选择朋友圈类型及添加朋友圈内容"
      >
        <>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddMsg("Picture")}
            style={{ marginRight: "20px" }}
            disabled={isDisabled || isDisabledBtn}
          >
            图片
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddMsg("material")}
            disabled={isDisabled || isDisabledBtn}
          >
            选择素材
          </Button>
        </>
      </FormItem>
      <div className="circleMsgList">
        {circleMsgList?.map((item, index) => (
          <div key={index} className="template">
            {item.type == "copyWriter" ? (
              <FormItem
                name={["circleMsgList", index]}
                label="文案"
                // labelCol={{ span: 4 }}
                className="formItem_noMargin"
                rules={[
                  {
                    required: false,
                    message: `请输入1000字以内的文案`,
                  },
                ]}
                initialValue={""}
              >
                <TextArea
                  id={`msgInput${index}`}
                  showCount
                  maxLength={1000}
                  autoSize={{ minRows: 4, maxRows: 6 }}
                  onChange={(e) => {
                    let newCircleMsgList = circleMsgList;
                    newCircleMsgList[index].content = e.target.value;
                    setCircleMsgList([...newCircleMsgList]);
                  }}
                  placeholder={`请输入1000字以内的文案`}
                  allowClear
                  className={`${
                    trimAll(item.content ?? "").length > 1000 && "textAreaAffix"
                  }`}
                />
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "pageArticle" ? (
              <FormItem
                name={["circleMsgList", index]}
                label="文章"
                className="formItem_noMargin"
                extra={
                  item.productId && (
                    <CheckableTag
                      checked={item.showProduct}
                      onChange={(checked) =>
                        handleProductTagChange(item, checked)
                      }
                      className="CheckableTag"
                    >
                      产品码
                    </CheckableTag>
                  )
                }
              >
                <div className="link-card">
                  <Paragraph strong ellipsis={{ rows: 2, tooltip: true }}>
                    {item.title}
                  </Paragraph>
                  <Row justify="space-between">
                    <Col span={17}>
                      <Paragraph
                        style={{ fontSize: "12px", lineHeight: "18px" }}
                        ellipsis={{ rows: 3 }}
                      >
                        {item.description}
                      </Paragraph>
                    </Col>
                    <Col>
                      <FileHOC src={item.imageUrl || "error"}>
                        {(url) => (
                          <Image
                            width={54}
                            src={url}
                            fallback="images/fallbackImg.png"
                            preview={false}
                          />
                        )}
                      </FileHOC>
                    </Col>
                  </Row>
                </div>
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "Picture" ? (
              <FormItem
                name={["circleMsgList", index]}
                className="formItem_noMargin"
                valuePropName="msgList"
                getValueFromEvent={normFile}
                label="图片"
                rules={[{ required: true, message: "请上传图片" }]}
                onClick={(e) => {
                  e.preventDefault(); // 阻止浏览器默认行为
                  e.stopPropagation(); // 阻止事件冒泡
                }}
                extra={
                  item.productId && (
                    <CheckableTag
                      checked={item.showProduct}
                      onChange={(checked) =>
                        handleProductTagChange(item, checked)
                      }
                      className="CheckableTag"
                    >
                      产品码
                    </CheckableTag>
                  )
                }
              >
                <WibotUpload
                  fileList={item.imageUrl}
                  deletable={false}
                  onDone={(params) => {
                    const { fileUrl, fileId } = params;
                    let newCircleMsgList = [...circleMsgList];
                    newCircleMsgList[index].imageUrl = fileUrl;
                    newCircleMsgList[index].image = fileUrl;
                    newCircleMsgList[index].fileId = [fileUrl];
                    newCircleMsgList[index].picType = "creat";
                    setCircleMsgList([...newCircleMsgList]);

                    let fieldCircleMsgList =
                      formForm.getFieldValue("circleMsgList") ?? [];
                    fieldCircleMsgList[index] = fileUrl;
                    formForm.setFieldsValue({
                      circleMsgList: fieldCircleMsgList,
                    });
                  }}
                />
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "Article" ||
            item.type == "SURVEY" ||
            item.type == "Product" ? (
              <FormItem
                name={["circleMsgList", index]}
                label={
                  item.type == "Article"
                    ? "推文"
                    : item.type == "Product"
                    ? "产品"
                    : "问卷"
                }
                // labelCol={{ span: 4 }}
                className="formItem_noMargin"
              >
                <div className="link-card">
                  <Paragraph strong ellipsis={{ rows: 2, tooltip: true }}>
                    {item.title}
                  </Paragraph>
                  <Row justify="space-between">
                    <Col span={17}>
                      <Paragraph
                        style={{ fontSize: "12px", lineHeight: "18px" }}
                        ellipsis={{ rows: 3 }}
                      >
                        {item.description}
                      </Paragraph>
                    </Col>
                    <Col>
                      <FileHOC src={item.imageUrl || "error"}>
                        {(url) => (
                          <Image
                            width={54}
                            src={url}
                            fallback="images/fallbackImg.png"
                            preview={false}
                          />
                        )}
                      </FileHOC>
                    </Col>
                  </Row>
                </div>
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "FORM" ? (
              <FormItem
                name={["circleMsgList", index]}
                label="问卷"
                className="formItem_noMargin"
              >
                <div className="link-card">
                  <Paragraph strong ellipsis={{ rows: 2, tooltip: true }}>
                    {item.title}
                  </Paragraph>
                  <Row justify="space-between">
                    <Col span={17}>
                      <Paragraph
                        style={{ fontSize: "12px", lineHeight: "18px" }}
                        ellipsis={{ rows: 3 }}
                      >
                        {item.description}
                      </Paragraph>
                    </Col>
                    <Col>
                      <FileHOC src={item.imageUrl || "error"}>
                        {(url) => (
                          <Image
                            width={54}
                            src={url}
                            fallback="images/fallbackImg.png"
                            preview={false}
                          />
                        )}
                      </FileHOC>
                    </Col>
                  </Row>
                </div>
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "Video" ? (
              <FormItem
                name={["circleMsgList", index]}
                label="视频"
                // labelCol={{ span: 4 }}
                className="formItem_noMargin"
              >
                <FileHOC src={item?.videoUrl}>
                  {(url) => (
                    <video
                      controls
                      src={url}
                      style={{ width: "102px", height: "102px" }}
                    />
                  )}
                </FileHOC>
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "Poster" ? (
              <FormItem
                name={["circleMsgList", index]}
                className="formItem_noMargin"
                label="海报"
                extra={
                  item.productId && (
                    <CheckableTag
                      checked={item.showProduct}
                      onChange={(checked) =>
                        handleProductTagChange(item, checked)
                      }
                      className="CheckableTag"
                    >
                      产品码
                    </CheckableTag>
                  )
                }
              >
                <WibotUpload
                  fileList={item.imageUrl}
                  deletable={false}
                  onDone={(params) => {
                    const { fileUrl, fileId } = params;
                    let newCircleMsgList = [...circleMsgList];
                    newCircleMsgList[index].imageUrl = fileUrl;
                    newCircleMsgList[index].fileId = [fileUrl];
                    setCircleMsgList([...newCircleMsgList]);

                    let fieldCircleMsgList =
                      formForm.getFieldValue("circleMsgList") ?? [];
                    fieldCircleMsgList[index] = fileUrl;
                    formForm.setFieldsValue({
                      circleMsgList: fieldCircleMsgList,
                    });
                  }}
                />
              </FormItem>
            ) : (
              ""
            )}

            {item.type == "MINI_PROGRAM" ? (
              <FormItem
                name={["circleMsgList", index]}
                label="小程序"
                // labelCol={{ span: 4 }}
                className="formItem_noMargin"
              >
                <AppletCard data={item.miniProgram || {}} />
              </FormItem>
            ) : (
              ""
            )}

            {item.type != "copyWriter" && !isDisabled && (
              <CloseCircleOutlined
                className="tagClose"
                onClick={() => handleDelMsg(index)}
              />
            )}
          </div>
        ))}
      </div>
      <MaterialModal params={materialModalParams} />
    </div>
  );
});

export default CircleMsgContent;
