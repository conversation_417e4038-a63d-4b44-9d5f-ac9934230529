.CircleMsgContent {
  .circleMsgList {
    .template {
      position: relative;
      border-top: 1px solid #f1f2f4;
      padding: 20px 0;
      display: flex;

      &:first-child {
        border: unset;
      }

      .formItem_noMargin {
        flex: 1;
        margin-bottom: 0px;
      }

      .textAreaAffix {
        &::after {
          color: #ff4d4f;
        }
      }

      .tagClose {
        cursor: pointer;
        font-size: 20px;
        color: #d9d9d9;
        margin-left: 20px;

        &.tagCloseInserte {
          position: absolute;
          right: 0;
          top: 20px;
          z-index: 99;
        }
      }

      .CheckableTag {
        background: #f5f5f5;

        &:hover {
          color: #f24e41;
        }

        &.ant-tag-checkable-checked {
          background: #f24e41;

          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
}