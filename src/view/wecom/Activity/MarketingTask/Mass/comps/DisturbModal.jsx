/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/09/12 15:07
 * @LastEditTime: 2024/08/28 17:51
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/comps/DisturbModal.jsx
 * @Description: '客户免打扰设置'
 */
import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin, Button, TreeSelect, Tag } from 'antd';
import { apiCall } from 'common/utils';
import { PlusOutlined } from '@ant-design/icons';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import CTypeTransferModal from 'components/TransferModal/CustomerType/home';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 14 },
};

const DisturbModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [lobalSetting, setGlobalSetting] = useState({});
  const [customerCount, setCustomerCount] = useState(0);
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  });
  const [existTagNameList, setExistTagNameList] = useState([])
  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getTagCategoryTreeTwo();
      getGlobalSetting();
    }
  }, [props]);

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([
        //   {
        //     title: '全选',
        //     value: 'customer',
        //     key: 'customer',
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      });
  };
  // 获取当前企业的全局配置
  const getGlobalSetting = async () => {
    setLoading(true);
    await apiCall('/globalSetting/getOneByValid', 'GET')
      .then((res) => {
        const { doNotDisturbSetting } = res;
        if (doNotDisturbSetting) {
          formRef.current.setFieldsValue({
            ...doNotDisturbSetting,
          });
          setDataSource(doNotDisturbSetting.customerList);
          setExistTagNameList(doNotDisturbSetting.tagNameList)
        }
        setGlobalSetting(res);
        getCountDoNotDisturbCustomer({
          doNotDisturbSetting: {
            customerIdList: doNotDisturbSetting.customerIdList,
            tagIdList: doNotDisturbSetting.tagIdList,
          },
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取免打扰客户数量
  const getCountDoNotDisturbCustomer = (data) => {
    apiCall('/globalSetting/countDoNotDisturbCustomer', 'POST', data)
      .then((res) => {
        setCustomerCount(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        id: lobalSetting.id,
        doNotDisturbSetting: { ...formData },
      };
      apiCall('/globalSetting/addOrModify', 'POST', data)
        .then((res) => {
          message.success('设置成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="DisturbModal"
      visible={visible}
      title="客户免打扰设置"
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} ref={formRef}>
          <CustomTagSelect
            label="客户标签"
            extra="客户的标签调整过后，系统每天会同步到免打扰名单中。"
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            existTagNameList={existTagNameList}
            labelTreeData={labelTreeData}
          />
          {/* <FormItem */}
          {/*   name="tagIdList" */}
          {/*   label="客户标签" */}
          {/*   extra="客户的标签调整过后，系统每天会同步到免打扰名单中。" */}
          {/* > */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={['customer']} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="请选择" */}
          {/*     onChange={(value, label, extra) => { */}
          {/*       getCountDoNotDisturbCustomer({ */}
          {/*         doNotDisturbSetting: { */}
          {/*           customerIdList: */}
          {/*             formRef.current.getFieldValue('customerIdList'), */}
          {/*           tagIdList: value, */}
          {/*         }, */}
          {/*       }); */}
          {/*     }} */}
          {/*   /> */}
          {/* </FormItem> */}
          <FormItem
            name="customerIdList"
            label="客户清单"
            extra={
              dataSource.length > 0 && (
                <div>
                  {dataSource.map((item) => (
                    <Tag
                      closable
                      style={{ marginTop: '8px' }}
                      key={item.id}
                      onClose={(e) => {
                        e.preventDefault();
                        const list = dataSource.filter(
                          (atem) => atem.id != item.id
                        );
                        setDataSource(list);
                        formRef.current.setFieldsValue({
                          customerIdList: list.map((item) => item.id),
                        });
                        getCountDoNotDisturbCustomer({
                          doNotDisturbSetting: {
                            customerIdList: list.map((item) => item.id),
                            tagIdList:
                              formRef.current.getFieldValue('tagIdList'),
                          },
                        });
                      }}
                    >
                      {item.name}
                    </Tag>
                  ))}
                </div>
              )
            }
          >
            <Button
              style={{ marginLeft: '8px' }}
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setCTypeTransferParams({
                  visible: true,
                  type: 'other',
                  checkList: [...dataSource],
                  paramsInfo: {
                    title: '客户',
                    placeholder: '客户名称/负责人名称',
                    apiUrl: '/customer/popup/customer',
                    params: {},
                    fieldNames: { title: 'name', key: 'id' },
                  },
                  onSubmit: (data) => {
                    setDataSource(data);
                    formRef.current.setFieldsValue({
                      customerIdList: data.map((item) => item.id),
                    });
                    getCountDoNotDisturbCustomer({
                      doNotDisturbSetting: {
                        customerIdList: data.map((item) => item.id),
                        tagIdList: formRef.current.getFieldValue('tagIdList'),
                      },
                    });
                    setCTypeTransferParams({ visible: false });
                  },
                  onCancel: () => {
                    setCTypeTransferParams({ visible: false });
                  },
                });
              }}
            >
              选择客户
            </Button>
          </FormItem>
          <div>
            {/* 共设置了 <span style={{ color: '#ff0137' }}> {customerCount} </span>
            位免打扰客户， */}
            群发客户的任务执行时将跳过这些客户。
          </div>
        </Form>
        <CTypeTransferModal {...CTypeTransferParams} />
      </Spin>
    </Modal>
  );
};

export default DisturbModal;
