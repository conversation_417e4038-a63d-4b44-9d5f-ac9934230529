/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/01/16 09:41
 * @LastEditTime: 2023/12/04 17:01
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/comps/ExportModal.jsx
 * @Description: '群发任务-导出对话框'
 */

import React, { useEffect, useRef, useState } from "react";
import { Tabs, Form, message, Modal, Spin, DatePicker } from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import "./ExportModal.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("1");
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format("YYYY-MM-DD 00:00:00");
        formData.maxCreateTime = moment(formData.createTime[1]._d).format("YYYY-MM-DD 23:59:59");
        delete formData.createTime;
      }
      const data = {
        ...formData,
      };
      apiCall("/taskBatchStat/moment/export", "GET", data, null, {
        isExit: true,
        title: `朋友圈互动统计.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success("导出成功！");
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="Mass-ExportModal"
      visible={visible}
      title="导出群发任务数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="朋友圈互动统计明细" key="1">
              <p className="tips">根据筛选时间，选择导出时间内的群发朋友圈任务互动数据</p>
              <FormItem
                name="createTime"
                label="创建时间"
                rules={[{ required: true, message: "请选择创建时间" }]}
              >
                <RangePicker />
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
