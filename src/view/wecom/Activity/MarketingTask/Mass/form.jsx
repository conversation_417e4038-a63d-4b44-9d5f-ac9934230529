/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/06 15:36
 * @LastEditTime: 2025/07/28 11:01
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/form.jsx
 * @Description: '群发任务-表单(新增/编辑)'
 */

import React, { useEffect, useRef, useState } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Image,
  Tag,
  Radio,
  message,
  DatePicker,
  Popover,
  Switch,
  TreeSelect,
  Select,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import { PlusOutlined, CheckOutlined } from "@ant-design/icons"
import { removeInputEmpty } from "common/regular"
import { qs2obj } from "common/object"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import MaterialListForm from "components/MaterialListForm/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import CTypeTransferModal from "components/TransferModal/CustomerType/home"
import SendModal from "../../comps/SendModal/home"
import UploadList from "../comps/UploadList/home"
import CircleMsgContent from "./comps/CircleMsgContent"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import "./form.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const { Option } = Select

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}

const MassForm = (props) => {
  const [formForm] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const onRefMaterialListForm = useRef()
  const onRefCustomerList = useRef()
  const onRefCircleMaterialListForm = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [selectType, setSelectType] = useState("CUSTOMER")
  const [sendObjectType, setSendObjectType] = useState(1)
  const [targetCount, setTargetCount] = useState(0)
  const [staffSelected, setStaffSelected] = useState([])
  const [groupSelected, setGroupSelected] = useState([])
  const [groupLeaderSelected, setGroupLeaderSelected] = useState([])
  const [msgList, setMsgList] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [operationType, setOperationType] = useState(null)
  const [sendParams, setSendParams] = useState({ visible: false })
  const [surveyFlag, setSurveyFlag] = useState(null)
  const [circleMsgList, setCircleMsgList] = useState([])

  const scrollListData = [
    {
      title: "客户群发",
      describe: "将多条消息通过员工群发给客户，每个客户每天可接收1组群发消息。",
      type: "CUSTOMER",
    },
    {
      title: "客户群群发",
      describe:
        "将多条消息通过群主群发给客户群，每个群组每天可接收1组群发消息。",
      type: "CHAT",
    },
    {
      title: "朋友圈群发",
      describe: "每位客户的朋友圈每天最多可展示3条同一成员发布的朋友圈内容。",
      type: "MOMENT",
    },
  ]
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  })
  const [noLeaderIdList, setNoLeaderIdList] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [existTagNameList, setExistTagNameList] = useState([])

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    getGroupNoLeaderIdList()
    getCustomerTagTreeData()
    if (id) {
      setId(id)
      setOperationType(type)
    } else {
      onRefMaterialListForm.current?.getInitMsgList([
        {
          type: "copyWriter",
          content: "",
          defaultFlag: true,
        },
      ])
    }
    init(id, type)
  }, [])

  const init = async (id = null, type) => {
    if (id) {
      const params = {
        id,
        type,
      }
      await fetchList(params)
    }
    // await getTargetCount();
    if (type == "detail") {
      let timer = setTimeout(() => {
        WibotEditorRef.current?.disable()
        clearTimeout(timer)
      }, 300)
    }
  }

  // 查询所有不是群主id
  const getGroupNoLeaderIdList = async () => {
    setLoading(true)
    apiCall("/group/noLeaderIdList", "GET")
      .then((res) => {
        setNoLeaderIdList(res.map((item) => String(item)))
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    const data = {
      id,
    }
    await apiCall("/employeeTaskBatch/get", "GET", data)
      .then((res) => {
        const { messages, type, scope, targetFilter, subType } = res
        const {
          gender,
          startTime,
          endTime,
          groupIds,
          tagIds,
          tagNameList,
          authFlag,
          employeeList,
          groupList,
          groupLeaderList,
          depEmployeeIdList,
          groupLeaderIds,
          filterSurveySent,
          filterSurveyVisit,
          importCustomerList,
          importCustomerListFileName,
          importCustomerListFileId,
        } = targetFilter
        const sendType = scope == "ALL" ? 1 : scope == "PART" ? 2 : 3
        formForm.setFieldsValue({
          ...res,
          filterSurveySent,
          filterSurveyVisit,
          planTime: res.startTime
            ? [moment(res.startTime), moment(res.endTime)]
            : null,
          massTimeType: 2,
          sendObjectType: sendType,
          gender: gender ?? "all",
          addTime: startTime ? [moment(startTime), moment(endTime)] : null,
          groupIds: groupIds ?? [],
          tagIds: tagIds ?? [],
          tagNameList,
          authFlag,
          depEmployeeIdList: depEmployeeIdList ?? [],
          groupLeaderIds: groupLeaderIds?.map((item) => String(item)) ?? [],
        })
        setExistTagNameList(tagNameList)

        // 是否问卷
        setSurveyFlag(subType)
        // 所选员工
        setStaffSelected(employeeList)
        // 所选群组
        const arr =
          groupList?.map((item) => ({
            ...item,
            key: item.id,
          })) ?? []
        setGroupSelected(JSON.parse(JSON.stringify(arr)))
        // 所选群主
        setGroupLeaderSelected(groupLeaderList)
        if (type == "MOMENT") {
          let timer = setTimeout(() => {
            onRefCircleMaterialListForm?.current?.getInitMsgList(messages)
            clearTimeout(timer)
          }, 100)
        }
        setSelectType(type)
        setSendObjectType(sendType)
        // 客户清单
        if (selectType == "CUSTOMER" && scope == "PART_LIST") {
          // onRefCustomerList?.current?.getInitCustomerList(importCustomerList);
          onRefCustomerList?.current?.getInitCustomerFile({
            name: importCustomerListFileName,
            file: importCustomerListFileId,
          })
        }
        // 资源中心组件-群发内容
        if (selectType != "MOMENT") {
          onRefMaterialListForm?.current?.getInitMsgList(messages)
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 切换群发类型
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setSelectType(item.type)
    setMsgList([])
    formForm.resetFields()
    setGroupSelected([])
    setStaffSelected([])
    setGroupLeaderSelected([])
    // getTargetCount(item.type);
    setSendObjectType(1)
    formForm.setFieldsValue({
      sendObjectType: 1,
    })

    if (item.type == "CUSTOMER" || item.type == "CHAT") {
      let timer = setTimeout(() => {
        onRefMaterialListForm?.current?.getInitMsgList([
          {
            type: "copyWriter",
            content: "",
            defaultFlag: true,
          },
        ])
        clearTimeout(timer)
      }, 100)
    }

    if (selectType != "MOMENT") {
      onRefMaterialListForm.current.getInitMsgList([])
    }
  }

  // 切换发送对象类型
  const onChangeRadioSendObjectType = (e) => {
    const { value } = e.target
    setSendObjectType(value)
    if (value != 3) {
      // getTargetCount();
    }
  }

  // 获取任务对象数量
  const getTargetCount = async (itemType = null) => {
    setLoading(true)
    const type = formForm?.getFieldValue("sendObjectType")
    const paramsData = {
      targetType: (itemType || selectType) == "CHAT" ? "CHAT" : "CUSTOMER",
      taskBatchType: (itemType || selectType) == "MOMENT" ? "MOMENT" : null,
    }

    if (type == 1) {
      await apiCall("/employeeTaskBatch/getTargetCount", "GET", paramsData)
        .then((res) => {
          if (res == 0) {
            message.error("没有满足条件的客户，请重新选择")
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "tagNameList",
        "authFlag",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender == "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.tagNameList = formData.tagNameList?.join(",") || null

      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        gender: newGender ?? null,
        ...paramsData,
      }

      await apiCall("/employeeTaskBatch/getTargetCount", "GET", data)
        .then((res) => {
          if (res == 0) {
            message.error(
              `负责人${
                staffSelected?.map((item) => item.name)?.join("、") ||
                groupLeaderSelected?.map((item) => item.name)?.join("、")
              }名下无满足条件的客户，请重新选择`
            )
          }
          setTargetCount(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  // 客户列表
  const handleSendCustomer = () => {
    setLoading(true)
    const sendObjectType = formForm?.getFieldValue("sendObjectType")
    const filterSurveyVisit = formForm.getFieldValue("filterSurveyVisit")
    const filterSurveySent = formForm.getFieldValue("filterSurveySent")
    const paramsData = {
      targetType: selectType == "CHAT" ? "CHAT" : "CUSTOMER",
      filterSurveySent,
      filterSurveyVisit,
    }

    if (sendObjectType == 1) {
      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", paramsData)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            type: selectType,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      const formData = formForm.getFieldsValue([
        "addTime",
        "gender",
        "groupIds",
        "tagIds",
        "tagNameList",
        "authFlag",
        "depEmployeeIdList",
        "groupLeaderIds",
      ])
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }
      if (formData.gender == "all") {
        formData.gender = null
      }
      let newGender = ""
      switch (formData.gender) {
        case "男性":
          newGender = "MAN"
          break
        case "女性":
          newGender = "WOMAN"
          break
        case "未知":
          newGender = "UNKNOWN"
          break
      }
      formData.tagIds = formData.tagIds?.join(",") || null
      formData.tagNameList = formData.tagNameList?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.groupLeaderIds = formData.groupLeaderIds?.join(",") || null
      formData.groupIds = formData.groupIds?.join(",") || null
      const data = {
        ...formData,
        gender: newGender ?? null,
        ...paramsData,
      }

      apiCall("/employeeTaskBatch/getTargetCustomer", "GET", data)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            type: selectType,
            onCancel: () => {
              setSendParams({ visible: false })
            },
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      const materialList =
        selectType != "MOMENT"
          ? onRefMaterialListForm.current.getModifyMsgList()
          : circleMsgList

      if (selectType != "MOMENT" && !materialList.length > 0) {
        message.error("群发内容不能为空！")
        return false
      }
      if (
        selectType != "MOMENT" &&
        onRefMaterialListForm.current.getCopyWriterValidate()
      ) {
        // 校验资源文案字数
        return
      }

      if (formData.planTime) {
        formData.startPlanTime = moment(formData.planTime[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endPlanTime = moment(formData.planTime[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.planTime
      }
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.addTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.addTime
      }

      const {
        name,
        startPlanTime,
        endPlanTime,
        sendObjectType,
        startTime = null,
        endTime = null,
        gender = null,
        tagIds,
        tagNameList,
        authFlag,
        depEmployeeIdList,
        groupLeaderIds,
        allowSelect,
        filterSurveySent,
        filterSurveyVisit,
        remark,
        file,
      } = formData

      if (
        (selectType == "CUSTOMER" &&
          sendObjectType == 2 &&
          !tagIds?.length &&
          !depEmployeeIdList?.length &&
          authFlag == undefined &&
          !startTime &&
          !groupSelected?.length &&
          gender == "all") ||
        (selectType == "MOMENT" &&
          sendObjectType == 2 &&
          !depEmployeeIdList?.length)
      ) {
        message.error("请选择部分客户条件！")
        return false
      }
      setLoading(true)
      // let importCustomerList = [];
      // if (selectType == 'CUSTOMER' && sendObjectType == 3) {
      //   importCustomerList = onRefCustomerList.current.getCustomerList();
      // }
      const newId = operationType != "recover" ? id : null
      const data = {
        id: newId ?? null,
        type: selectType,
        status: "1",
        targetType: selectType == "CHAT" ? "CHAT" : "CUSTOMER",
        name,
        allowSelect,
        remark,
        startTime: startPlanTime,
        endTime: endPlanTime,
        scope:
          sendObjectType == 1
            ? "ALL"
            : sendObjectType == 2
            ? "PART"
            : "PART_LIST",
        targetFilter:
          sendObjectType == 1
            ? {
                filterSurveySent,
                filterSurveyVisit,
              }
            : {
                filterSurveySent,
                filterSurveyVisit,
                groupIds: groupSelected?.map((item) => item.key) || null,
                gender: gender == "all" ? null : gender,
                startTime,
                endTime,
                groupLeaderIds: groupLeaderIds,
                depEmployeeIdList: depEmployeeIdList,
                tagIds: tagIds,
                tagNameList: tagNameList,
                authFlag,
                // importCustomerList,
                importCustomerListFileName:
                  onRefCustomerList?.current?.getFileName(),
                importCustomerListFileId: file,
              },
        messages: materialList?.map((item, index) => {
          if (item.type === "copyWriter") {
            item.content = item.content.trim()
          }
          item.fileId =
            item.fileId || (item.shareImage && [item.shareImage]) || null
          return item
        }),
      }
      apiCall("/employeeTaskBatch/addOrModify", "PUT", data)
        .then((res) => {
          message.success(newId ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.push("/wecom/marketingtask")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    // 清空选择素材弹窗缓存
    localStorage.removeItem("materialModalCascader")
    localStorage.removeItem("materialModalTabType")
    props.history.go(-1)
  }

  return (
    <div className="MassForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑群发任务" : "创建群发任务"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <h2 className="card-title">群发类型</h2>
          <ul className="scrollList">
            {scrollListData.map((item, index) => (
              <li
                key={index}
                className={
                  selectType == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${
                    selectType != item.type && id ? "#c1c1c1" : "unset"
                  }`,
                  cursor: `${
                    selectType != item.type && id ? "unset" : "pointer"
                  }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>
        </Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <h2 className="card-title">基础信息</h2>
              <Form {...layout} form={formForm}>
                <FormItem
                  label="任务名称"
                  name="name"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入任务名称" }]}
                >
                  <Input
                    placeholder="请输入任务名称"
                    allowClear
                    disabled={operationType == "detail"}
                  />
                </FormItem>

                <FormItem
                  label="任务时间"
                  name="planTime"
                  rules={[{ required: true, message: "请选择群发时间" }]}
                >
                  <RangePicker
                    showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
                    format="YYYY-MM-DD HH:mm"
                    disabled={operationType == "detail"}
                  />
                </FormItem>

                {selectType == "CHAT" ? (
                  <FormItem
                    name="sendObjectType"
                    label="任务对象"
                    initialValue={sendObjectType}
                    rules={[{ required: true, message: "请选择发送对象" }]}
                    extra={
                      <>
                        预计发送客户群数（已去重）：{targetCount} 个
                        <Button
                          disabled={operationType == "detail"}
                          style={{ marginLeft: "6px" }}
                          onClick={() => {
                            getTargetCount(selectType)
                          }}
                        >
                          刷新
                        </Button>
                        {/* <Button
                          style={{ marginLeft: "10px" }}
                          type="primary"
                          onClick={() => {
                            handleSendCustomer();
                          }}
                        >
                          客户群列表
                        </Button> */}
                      </>
                    }
                  >
                    <Radio.Group
                      onChange={onChangeRadioSendObjectType}
                      disabled={operationType == "detail"}
                    >
                      <Radio value={1}>全部客户群</Radio>
                      <Radio value={2}>部分客户群</Radio>
                    </Radio.Group>
                  </FormItem>
                ) : (
                  <FormItem
                    wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                    label="任务对象"
                    extra={
                      selectType == "CUSTOMER" ? (
                        sendObjectType == 1 ? (
                          <>
                            预计发送客户数（已去重）：{targetCount} 个
                            <Button
                              disabled={operationType == "detail"}
                              style={{ marginLeft: "6px" }}
                              onClick={() => {
                                getTargetCount(selectType)
                              }}
                            >
                              刷新
                            </Button>
                            {/* <Button
                              style={{ marginLeft: "10px" }}
                              type="primary"
                              onClick={() => {
                                handleSendCustomer();
                              }}
                            >
                              客户列表
                            </Button> */}
                          </>
                        ) : sendObjectType == 2 ? (
                          <>
                            取所有条件交集，预计发送客户数（已去重）：
                            {targetCount} 个
                            <Button
                              disabled={operationType == "detail"}
                              style={{ marginLeft: "6px" }}
                              onClick={() => {
                                getTargetCount(selectType)
                              }}
                            >
                              刷新
                            </Button>
                            {/* <Button
                              style={{ marginLeft: "10px" }}
                              type="primary"
                              onClick={() => {
                                handleSendCustomer();
                              }}
                            >
                              客户列表
                            </Button> */}
                          </>
                        ) : (
                          ""
                        )
                      ) : sendObjectType == 1 ? (
                        <>
                          预计可见微信客户数（已去重）：{targetCount} 个
                          <Button
                            disabled={operationType == "detail"}
                            style={{ marginLeft: "6px" }}
                            onClick={() => {
                              getTargetCount(selectType)
                            }}
                          >
                            刷新
                          </Button>
                          {/* <Button
                            style={{ marginLeft: '10px' }}
                            type="primary"
                            onClick={() => {
                              handleSendCustomer();
                            }}
                          >
                            客户列表
                          </Button> */}
                        </>
                      ) : sendObjectType == 2 ? (
                        <>
                          取所有条件交集，预计可见微信客户数（已去重）：
                          {targetCount} 个
                          <Button
                            disabled={operationType == "detail"}
                            style={{ marginLeft: "6px" }}
                            onClick={() => {
                              getTargetCount(selectType)
                            }}
                          >
                            刷新
                          </Button>
                          {/* <Button
                            style={{ marginLeft: "10px" }}
                            type="primary"
                            onClick={() => {
                              handleSendCustomer();
                            }}
                          >
                            客户列表
                          </Button> */}
                        </>
                      ) : (
                        ""
                      )
                    }
                  >
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <FormItem
                        style={{ margin: "0" }}
                        name="sendObjectType"
                        initialValue={sendObjectType}
                        rules={[{ required: true, message: "请选择发送对象" }]}
                      >
                        <Radio.Group
                          onChange={onChangeRadioSendObjectType}
                          disabled={operationType == "detail"}
                        >
                          <Radio value={1}>全部客户</Radio>
                          <Radio value={2}>
                            部分客户{selectType == "CUSTOMER" && "-条件"}
                          </Radio>
                          {selectType == "CUSTOMER" && (
                            <Radio value={3}>部分客户-清单</Radio>
                          )}
                        </Radio.Group>
                      </FormItem>
                      {selectType == "CUSTOMER" && (
                        <Popover
                          title="群发说明"
                          content={
                            <>
                              <p>
                                1.
                                群发部分客户筛选的员工就是客户负责人，群发客户消息任务由客户负责人执行；
                              </p>
                              <p>
                                2.
                                群发客户任务调企微企业群发接口，群发的客户由后台限制，员工不可手动更改；
                              </p>
                              <p>
                                3.
                                群发部分客户的筛选条件含负责人员工时，按所选要求筛选客户，客户的负责人必须是所选员工（客户的负责人不是所选员工时自动过滤掉），如筛选出的客户数等于0时不给所选员工推送任务，且员工不纳入任务统计；
                              </p>
                              <p>
                                4.群发部分客户的筛选条件不含负责人员工时，筛选出的客户按照客户的负责人员工去推送任务。
                              </p>
                            </>
                          }
                        >
                          <a>群发说明</a>
                        </Popover>
                      )}
                    </div>
                  </FormItem>
                )}

                {(sendObjectType == 2 && selectType == "CUSTOMER") ||
                (sendObjectType == 2 && selectType == "MOMENT") ? (
                  <div className="input-content-mask">
                    <FormItem
                      name="depEmployeeIdList"
                      label="客户负责人"
                      shouldUpdate={(prevValues, curValues) =>
                        prevValues.type !== curValues.type
                      }
                    >
                      <ETypeTransferModal
                        title="选择员工"
                        readonly={operationType == "detail"}
                        onChange={(value, options) => {
                          setStaffSelected(options)
                        }}
                      />
                    </FormItem>
                    {sendObjectType == 2 && selectType == "CUSTOMER" && (
                      <>
                        {/* <FormItem name="tagIds" label="客户标签"> */}
                        {/*   <TreeSelect */}
                        {/*     treeData={labelTreeData} */}
                        {/*     treeCheckable */}
                        {/*     allowClear */}
                        {/*     showArrow */}
                        {/*     showSearch */}
                        {/*     treeNodeFilterProp="title" */}
                        {/*     maxTagCount="responsive" */}
                        {/*     showCheckedStrategy={SHOW_PARENT} */}
                        {/*     placeholder="客户标签" */}
                        {/*   /> */}
                        {/* </FormItem> */}
                        <CustomTagSelect
                          label="客户标签"
                          name="tagNameList"
                          placeholder="客户标签"
                          useForm={formForm}
                          existTagNameList={existTagNameList}
                          labelTreeData={labelTreeData}
                        />
                        <FormItem name="authFlag" label="识别状态">
                          <Select placeholder="识别状态" allowClear>
                            <Option value>已识别</Option>
                            <Option value={false}>未识别</Option>
                          </Select>
                        </FormItem>
                        <FormItem name="addTime" label="添加时间">
                          <RangePicker disabled={operationType == "detail"} />
                        </FormItem>

                        <FormItem
                          name="groupIds"
                          label="群组"
                          shouldUpdate={(prevValues, curValues) =>
                            prevValues.type !== curValues.type
                          }
                          extra={
                            groupSelected.length > 0 ? (
                              <div>
                                {groupSelected.map((item, index) => (
                                  <Tag
                                    closable
                                    style={{ marginTop: "8px" }}
                                    key={index}
                                    onClose={(e) => {
                                      e.preventDefault()
                                      setGroupSelected(
                                        groupSelected.filter(
                                          (i) => i.key != item.key
                                        )
                                      )
                                    }}
                                  >
                                    {item.name}
                                  </Tag>
                                ))}
                              </div>
                            ) : null
                          }
                        >
                          <Button
                            icon={<PlusOutlined />}
                            type="primary"
                            disabled={operationType == "detail"}
                            onClick={() => {
                              setCTypeTransferParams({
                                visible: true,
                                type: "group",
                                checkList: groupSelected,
                                onSubmit: (data) => {
                                  setGroupSelected(data)
                                  formForm.setFieldsValue({
                                    groupIds: data.map((item) => item.id),
                                  })
                                  setCTypeTransferParams({ visible: false })
                                },
                                onCancel: () => {
                                  setCTypeTransferParams({ visible: false })
                                },
                              })
                            }}
                          >
                            选择群组
                          </Button>
                        </FormItem>

                        <FormItem
                          name="gender"
                          label="选择性别"
                          initialValue={"all"}
                        >
                          <Radio.Group disabled={operationType == "detail"}>
                            <Radio value={"all"}>全部</Radio>
                            <Radio value={"男性"}>仅男性</Radio>
                            <Radio value={"女性"}>仅女性</Radio>
                            <Radio value={"未知"}>未知</Radio>
                          </Radio.Group>
                        </FormItem>
                      </>
                    )}
                  </div>
                ) : null}

                {selectType == "CHAT" && sendObjectType == 2 ? (
                  <FormItem
                    name="groupLeaderIds"
                    label="群主"
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.type !== curValues.type
                    }
                  >
                    <ETypeTransferModal
                      title="选择群主"
                      onlyEmployee
                      multiple
                      exclude={noLeaderIdList}
                      onChange={(value, options) => {
                        setGroupLeaderSelected(options)
                      }}
                    />
                  </FormItem>
                ) : null}

                {/* 上传客户清单 */}
                {selectType != "CHAT" && sendObjectType == 3 && (
                  <UploadList
                    ref={onRefCustomerList}
                    params={{
                      formForm,
                      operationType,
                    }}
                  />
                )}

                {selectType == "CUSTOMER" &&
                  operationType == "detail" &&
                  surveyFlag && (
                    <>
                      <FormItem
                        name="filterSurveySent"
                        label="过滤已推"
                        valuePropName="checked"
                        extra="开启后自动忽略推送过的客户"
                        labelCol={{ span: 6 }}
                        style={{ marginBottom: "10px" }}
                        initialValue={false}
                      >
                        <Switch disabled />
                      </FormItem>
                      <FormItem
                        name="filterSurveyVisit"
                        label="过滤已访"
                        valuePropName="checked"
                        extra="开启后自动忽略访问过该问卷的客户"
                        labelCol={{ span: 6 }}
                        style={{ marginBottom: "10px" }}
                        initialValue={false}
                        disabled
                      >
                        <Switch disabled />
                      </FormItem>
                    </>
                  )}

                {selectType != "MOMENT" && (
                  <FormItem
                    name="allowSelect"
                    label="群发调整"
                    valuePropName="checked"
                    extra="开启后允许员工手动调整发送范围"
                    // labelCol={{ span: 6 }}
                    initialValue={false}
                  >
                    <Switch disabled={operationType == "detail"} />
                  </FormItem>
                )}

                <FormItem
                  style={{ marginTop: "24px" }}
                  name="remark"
                  label="任务说明"
                  rules={[{ required: true, message: "请输入任务说明" }]}
                >
                  <TextArea
                    maxLength={100}
                    placeholder="请输入任务说明（100字内）"
                    disabled={operationType == "detail"}
                    allowClear
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    className="textArea-mid"
                  />
                </FormItem>

                <h2 className="card-title">群发内容</h2>

                {selectType != "MOMENT" ? (
                  <FormItem
                    label=" "
                    colon={false}
                    wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                  >
                    <MaterialListForm
                      params={{
                        formRef: formForm,
                        menuList: ["image", "material"],
                        isNickname: false,
                        needScriptFlag: true,
                        materialAmount: msgList.length,
                        isDisabled: operationType == "detail",
                        isShowProduct: true,
                        materialTabList: [
                          "Article",
                          "pageArticle",
                          "Video",
                          "copyWriter",
                          "MINI_PROGRAM",
                          "Picture",
                          "Poster",
                          "FORM",
                          "Product",
                          "ResourceSet",
                        ],
                        extra: (
                          <div>
                            请按发送顺序添加群发内容，每个客户每天可接收1组群发消息，1组最多9条消息，还可添加{" "}
                            {10 - msgList.length} 条
                          </div>
                        ),
                        changeLoading: (flag) => {
                          setLoading(flag)
                        },
                      }}
                      // 监听回调
                      callback={(params) => {
                        setMsgList(params.data)
                      }}
                      ref={onRefMaterialListForm}
                    />
                  </FormItem>
                ) : null}

                {selectType == "MOMENT" ? (
                  <CircleMsgContent
                    ref={onRefCircleMaterialListForm}
                    params={{
                      formForm: formForm,
                      isDisabled: operationType == "detail",
                      changeLoading: (flag) => {
                        setLoading(flag)
                      },
                    }}
                    onChangeMsgList={(data) => {
                      setCircleMsgList([...data])
                    }}
                  />
                ) : null}

                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    <Button
                      disabled={operationType == "detail"}
                      type="primary"
                      onClick={() => onSubmit()}
                    >
                      {id
                        ? operationType == "recover"
                          ? "创建任务"
                          : "保存并通知发送"
                        : "创建任务"}
                    </Button>
                  </Space>
                </div>
                {(!id || operationType == "recover") && (
                  <div style={{ textAlign: "center" }}>
                    任务提醒将在任务开始时发送给员工
                  </div>
                )}
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              {selectType == "MOMENT" ? (
                <WibotMaterialPreview type="moment" listData={circleMsgList} />
              ) : (
                <WibotMaterialPreview type="session" listData={msgList} />
              )}
            </Col>
          </Row>
        </Card>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
      <CTypeTransferModal {...CTypeTransferParams} />
      <SendModal {...sendParams} />
    </div>
  )
}

export default MassForm
