/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/06 14:52
 * @LastEditTime: 2025/05/15 10:35
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Mass/home.jsx
 * @Description: '群发任务'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  message,
  Typography,
} from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import moment from "moment"
import FilterBar from "components/FilterBar/FilterBar"
import SysDictSelect from "components/select/SysDictSelect"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"
import StatusFormModal from "../comps/StatusFormModal/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import CustomerStatusFormModal from "../comps/CustomerStatusFormModal/home"
import ChatStatusFormModal from "../comps/ChatStatusFormModal/home"
import DisturbModal from "./comps/DisturbModal"
import ListOperation from "components/ListOperation/home"
import ExportModal from "./comps/ExportModal"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import { usePageCacheLifeCycle } from "common/hooks"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Paragraph } = Typography

const Mass = (props) => {
  const formRef = useRef(null)
  const OperateModalRef = useRef(null)
  const [ConfirmFormRef] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const [statusFormParams, setStatusFormParams] = useState({ visible: false })
  const [customerStatusFormParams, setCustomerStatusFormParams] = useState({
    visible: false,
  })
  const [chatStatusFormParams, setChatStatusFormParams] = useState({
    visible: false,
  })
  const [disturbParams, setDisturbParams] = useState({ visible: false })
  const [exportParams, setExportParams] = useState({ visible: false })
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "任务说明",
      width: "160px",
      dataIndex: "remark",
      key: "remark",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "群发内容",
      width: "160px",
      dataIndex: "messages",
      key: "messages",
      align: "center",
      render: (value, record, index) => (
        <>
          <a onClick={() => handlePreview(record)}>预览</a>
        </>
      ),
    },
    {
      title: "群发类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="WECOM_GROUP_TYPE" dictkey={value} color />
      ),
    },
    {
      title: "任务对象",
      width: "160px",
      dataIndex: "executorNames",
      key: "executorNames",
      align: "center",
      render: (value, record, index) => {
        let content = ""
        if (record.targetType == "CUSTOMER" && record.scope == "PART") {
          content = "部分客户-条件"
        } else if (
          record.targetType == "CUSTOMER" &&
          record.scope == "PART_LIST"
        ) {
          content = "部分客户-清单"
        } else if (record.targetType == "CUSTOMER" && record.scope == "ALL") {
          content = "全部客户"
        } else if (record.targetType == "CHAT" && record.scope == "PART") {
          content = "部分客户群"
        } else if (record.targetType == "CHAT" && record.scope == "ALL") {
          content = "全部客户群"
        }
        return content
      },
    },
    {
      title: "任务时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: "任务状态",
      width: "160px",
      dataIndex: "status",
      key: "status",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="WECOM_TASK_STATUS" dictkey={value} color />
      ),
    },
    // {
    //   title: '员工完成情况',
    //   width: '160px',
    //   dataIndex: 'executorsStat',
    //   key: 'executorsStat',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <a onClick={() => handleSendStatus(record)}>
    //       {record.employeeFinishCount}/{record.employeeTaskCount}
    //     </a>
    //   ),
    // },
    // {
    //   title: '送达情况',
    //   width: '160px',
    //   dataIndex: 'targetStat',
    //   key: 'targetStat',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <a onClick={() => handleCustomerSendStatus(record)}>
    //       {record.receiveCount}/{record.allTaskCount}
    //     </a>
    //   ),
    // },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleStatic(record), name: "统计" },
          { onClick: () => handleStop(record), name: "停止" },
        ]
        if (record.status == 0) {
          opts.push({ onClick: () => handleRemind(record), name: "提醒" })
        }
        if (record.status == 1) {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" })
        }
        if (
          record.subType != "SURVEY" &&
          record.subType != "RESOURCE_COLLECTION"
        ) {
          opts.push({
            onClick: () => handleEdit(record, "recover"),
            name: "复制",
          })
        }
        if (record.status != 1) {
          opts.push({
            onClick: () => handleEdit(record, "detail"),
            name: "配置详情",
          })
        }
        if (record.status == 6) {
          opts.push({ onClick: () => handleConfirm(record), name: "确认执行" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    fetchList()
  }, [])

  usePageCacheLifeCycle({
    onShow() {
      handleQuery()
    },
  })

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.createTime
      }
      if (formData.planTime) {
        formData.startPlanTime = moment(formData.planTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endPlanTime = moment(formData.planTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.planTime
      }
      formData.createDeptIdList = formData.createDeptIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        types: "CUSTOMER,CHAT,MOMENT",
      }

      apiCall("/employeeTaskBatch/page", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 客户免打扰
  const handleDisturb = () => {
    setDisturbParams({
      visible: true,
      onCancel: () => {
        setDisturbParams({ visible: false })
      },
    })
  }

  const handleAdd = () => {
    props.history.push("/wecom/marketingtask/mass/form")
  }

  const handlePreview = (record) => {
    const { id, type } = record
    setLoading(true)
    const data = {
      id,
    }
    apiCall("/employeeTaskBatch/get", "GET", data)
      .then((res) => {
        const { messages } = res
        const newMessages = messages
        if (!res) {
          message.error("暂无群发内容！")
        }
        newMessages?.forEach((item) => {
          if (item.type == "Video") {
            item.videoUrl = item.fileId ? item.fileId[0] : ""
          } else {
            item.image = item.fileId ? item.fileId[0] : ""
          }
        })
        setResourcePreviewParams({
          visible: true,
          listData: newMessages,
          type: type === "MOMENT" ? "moment" : "session",
          onCancel: () => {
            setResourcePreviewParams({
              visible: false,
            })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleSendStatus = (record) => {
    setLoading(true)
    apiCall("/employeeTaskBatch/getExecutorStatList", "GET", { id: record.id })
      .then((res) => {
        setStatusFormParams({
          visible: true,
          title: "员工完成情况",
          issueStatusFlag: true,
          dataSource: res ?? [],
          onSubmit: () => {
            setStatusFormParams({ visible: false })
          },
          onCancel: () => {
            setStatusFormParams({ visible: false })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleCustomerSendStatus = (record) => {
    if (record.type == "CHAT") {
      setChatStatusFormParams({
        visible: true,
        title: "客户群送达情况",
        id: record.id,
        onSubmit: () => {
          setChatStatusFormParams({ visible: false })
        },
        onCancel: () => {
          setChatStatusFormParams({ visible: false })
        },
      })
      return
    }
    setCustomerStatusFormParams({
      visible: true,
      title: "客户送达情况",
      id: record.id,
      onSubmit: () => {
        setCustomerStatusFormParams({ visible: false })
      },
      onCancel: () => {
        setCustomerStatusFormParams({ visible: false })
      },
    })
  }

  const handleStop = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "停止确认",
      content: `您将停止群发任务【${name}】，停止后不可恢复，确认继续吗？`,
      onSubmit: () => {
        const data = {}
        apiCall(`/employeeTaskBatch/stopOrRecall?id=${id}`, "post", data)
          .then((res) => {
            message.success("停止成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleRemind = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "提醒确认",
      content: "您将给所有未发送的成员发送提醒通知，确认继续吗？",
      onSubmit: () => {
        const data = {
          id: id,
        }
        apiCall("/employeeTaskBatch/remind", "GET", data)
          .then((res) => {
            message.success("提醒成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleEdit = (record, type) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/marketingtask/mass/form",
      search: type ? `?id=${id}&type=${type}` : `?id=${id}`,
    })
  }

  // 统计
  const handleStatic = (record) => {
    const { id, type } = record
    props.history.push({
      pathname: "/wecom/marketingtask/mass/statistics",
      search: `?id=${id}&type=${type}`,
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const handleConfirm = (record) => {
    const { name, id } = record
    setLoading(true)
    apiCall("/employeeTaskBatch/getNameAndCount", "GET", { id: id })
      .then((res) => {
        const { count } = res
        setOperateParams({
          visible: true,
          title: "群发确认",
          content: (
            <>
              <Paragraph copyable={{ text: `确认执行${name}` }}>
                <>
                  群发任务【{name}】预计发送给{count}
                  位客户，确认发送请在下方输入：
                  <span style={{ color: "red" }}>确认执行{name}</span>
                </>
              </Paragraph>
              <Form layout={"inline"} form={ConfirmFormRef}>
                <FormItem
                  style={{ width: "100%", margin: "0" }}
                  name="text"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入" }]}
                >
                  <Input allowClear />
                </FormItem>
              </Form>
            </>
          ),
          onSubmit: () => {
            ConfirmFormRef.validateFields()
              .then((formData) => {
                if (formData.text != `确认执行${name}`) {
                  message.error("内容不相符，请重新输入！")
                  OperateModalRef.current.onStopLoading()
                  return false
                }
                apiCall(`/employeeTaskBatch/confirm?id=${id}`, "POST")
                  .then((res) => {
                    setOperateParams({ visible: false })
                    message.success("确认成功！")
                    fetchList()
                  })
                  .catch((err) => {
                    console.log(err)
                  })
                  .finally(() => {})
              })
              .catch(() => {
                OperateModalRef.current.onStopLoading()
              })
          },
          onCancel: () => {
            setOperateParams({
              visible: false,
            })
            ConfirmFormRef.resetFields()
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false })
      },
    })
  }

  return (
    <div className="Mass-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="任务名称、任务说明" allowClear />
          </FormItem>
          <FormItem name="status">
            <SysDictSelect placeholder="任务状态" dataset="WECOM_TASK_STATUS" />
          </FormItem>
          <FormItem name="type">
            <SysDictSelect placeholder="群发类型" dataset="WECOM_GROUP_TYPE" />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="planTime" label="任务时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
            <Button type="primary" onClick={() => handleDisturb()}>
              客户免打扰
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              创建群发任务
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal ref={OperateModalRef} params={operateParams} />
      <StatusFormModal {...statusFormParams} />
      <CustomerStatusFormModal {...customerStatusFormParams} />
      <ChatStatusFormModal {...chatStatusFormParams} />
      <DisturbModal params={disturbParams} />
      <ExportModal params={exportParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default withRouter(Mass)
