/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/14 18:12
 * @LastEditTime: 2025/05/14 11:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Ordinary/comps/PreviewModal/home.jsx
 * @Description: ''
 */
import React, { useState, useEffect, memo } from "react"
import { Space, Row, Col, Radio, Checkbox, Input } from "antd"
import { QrCodeBase } from "common/qrcode"
import moment from "moment"
import WibotMobilePreviewModal from "components/WibotMobilePreview/modal"
import "./home.less"

const { TextArea } = Input

const PreviewModal = (props) => {
  const { visible = false, dataObj = null, onCancel } = props.params
  const [formItemList, setFormItemList] = useState([])

  useEffect(() => {
    if (visible) {
      const newList = dataObj?.formItemList ?? []
      newList?.forEach((item) => {
        if (item.type == "QrCode") {
          item.image = QrCodeBase({ url: item.url })
        } else if (item.type == "Video") {
          item.videoUrl = item.fileId ? item.fileId[0] : ""
        } else {
          item.image = item.fileId ? item.fileId[0] : ""
        }
      })
      setFormItemList(newList)
    }
  }, [visible])

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <WibotMobilePreviewModal
      visible={visible}
      title="任务详情"
      onCancel={handleCancel}
    >
      {dataObj ? (
        <>
          <div className="row-item" style={{ fontWeight: "bold" }}>
            任务信息
          </div>
          <Row className="row-item">
            <Col span={6}>任务名称</Col>
            <Col span={18}>{dataObj.name}</Col>
          </Row>
          <Row className="row-item">
            <Col span={6}>任务类型</Col>
            <Col span={18}>信息报送</Col>
          </Row>
          <Row className="row-item">
            <Col span={6}>任务时间</Col>
            <Col span={18}>
              {moment(dataObj.startTime).format("MM/DD HH:mm") +
                "~" +
                moment(dataObj.endTime).format("MM/DD HH:mm")}
            </Col>
          </Row>
          <Row className="row-item">
            <Col span={6}>任务说明</Col>
            <Col span={18}>{dataObj.remark}</Col>
          </Row>
          <div className="row-item" style={{ fontWeight: "bold" }}>
            报送内容
          </div>
          {formItemList.map((item, index) => (
            <div className="row-item" style={{ textAlign: "left" }} key={index}>
              <p>
                <span>{index + 1}. </span>
                <span>{item.title}</span>
              </p>
              {item.type == "RADIO" && (
                <Radio.Group defaultValue={null}>
                  <Space direction="vertical">
                    {item.optionList.map((atem, andex) => (
                      <Radio value={andex} key={andex}>
                        {atem.other ? (
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <span>其他</span>
                            <Input
                              placeholder="请输入"
                              bordered={false}
                              style={{
                                flex: 1,
                                borderBottom: "1px solid #d9d9d9",
                              }}
                            />
                          </div>
                        ) : (
                          atem.name
                        )}
                      </Radio>
                    ))}
                  </Space>
                </Radio.Group>
              )}
              {item.type == "CHECKBOX" && (
                <Checkbox.Group defaultValue={null}>
                  <Space direction="vertical">
                    {item.optionList.map((atem, andex) => (
                      <Checkbox value={andex} key={andex}>
                        {atem.other ? (
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <span>其他</span>
                            <Input
                              placeholder="请输入"
                              bordered={false}
                              style={{
                                flex: 1,
                                borderBottom: "1px solid #d9d9d9",
                              }}
                            />
                          </div>
                        ) : (
                          atem.name
                        )}
                      </Checkbox>
                    ))}
                  </Space>
                </Checkbox.Group>
              )}
              {item.type == "TEXTAREA" && (
                <TextArea
                  placeholder="请输入"
                  value={item.textContent}
                  bordered={false}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                />
              )}
            </div>
          ))}
        </>
      ) : null}
    </WibotMobilePreviewModal>
  )
}

export default memo(PreviewModal)
