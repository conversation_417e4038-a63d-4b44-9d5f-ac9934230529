/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2022/10/17 11:18
 * @LastEditTime: 2023/11/24 11:13
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Ordinary/details.jsx
 * @Description: '阅知任务详情'
 */

import React, { useEffect, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Spin,
  Button,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Tag,
  Space,
  Tabs,
  Form,
  DatePicker,
  Select,
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { timeStamp, getDay } from "common/date";
import moment from "moment";
import { qs2obj } from "common/object";
import { Line } from '@ant-design/plots';
import "./details.less";

const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const OrdinaryDetails = (props) => {
  const [id, setId] = useState(null);
  const [type, setType] = useState("NOTICE");
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [statisticalInfo, setStatisticalInfo] = useState({});
  const [tabsTrendType, setTabsTrendType] = useState("FINISH_COUNT");
  const [chartsData, setChartsData] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [topicArr, setTopicArr] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        // const companyName = <span style={record.type == '微信用户' ? { color: '#07c160' } : { color: '#f59a23' }}>{record.departmentName}</span>;
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "lastVisitTime",
      key: "lastVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastVisitTime) - timeStamp(b.lastVisitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "完成状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? "已完成" : "未完成"}>
          {value ? "已完成" : "未完成"}
        </Tooltip>
      ),
    },
    {
      title: "完成时间",
      width: "160px",
      dataIndex: "commitTime",
      key: "commitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.commitTime) - timeStamp(b.commitTime),
    },
    ...topicArr,
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDownload(record)}>下载</a>
        </>
      ),
    },
  ];
  const noticeColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        // const companyName = <span style={record.type == '微信用户' ? { color: '#07c160' } : { color: '#f59a23' }}>{record.departmentName}</span>;
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "lastVisitTime",
      key: "lastVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastVisitTime) - timeStamp(b.lastVisitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "完成状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? "已完成" : "未完成"}>
          {value ? "已完成" : "未完成"}
        </Tooltip>
      ),
    },
    {
      title: "完成时间",
      width: "160px",
      dataIndex: "commitTime",
      key: "commitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.commitTime) - timeStamp(b.commitTime),
    },
  ];

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search);
    if (id) {
      setType(type);
      setId(id);
      init(id, type);
    }
  }, []);

  const init = async (id, type) => {
    await fetchList({ id });
    await getStatisticalInfo({ id });
    await getTabEcharts({ tabType: "FINISH_COUNT", detailId: id });
    await getDataDetails({ detailId: id, detailType: type });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    const data = {
      id,
    };
    await apiCall("/employeeTaskBatch/get", "GET", data)
      .then((res) => {
        const data = { ...res };
        data.startTime =
          data.startTime && moment(data.startTime).format("YYYY/MM/DD HH:mm");
        data.endTime =
          data.endTime && moment(data.endTime).format("YYYY/MM/DD HH:mm");
        setUserInfo(data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getStatisticalInfo = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    const data = {
      id,
    };
    await apiCall("/employeeTaskBatch/data", "GET", data)
      .then((res) => {
        setStatisticalInfo(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, detailId, detailType } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      employeeTaskBatchId: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    let apiUrl =
      detailType == "NOTICE"
        ? "/employeeTaskBatch/notice_data"
        : "/employeeTaskBatch/info_push_data";
    apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, pages, size, total } =
          detailType == "NOTICE" ? res : res.value;
        if (detailType == "INFO_PUSH") {
          const arr = res.key.map((item, index) => ({
            title: `${index + 1}.${item.title}`,
            width: "160px",
            dataIndex: `topic${index + 1}`,
            key: `topic${index + 1}`,
            align: "center",
            render: (value, record) => {
              let content = "";
              if (
                record.formItemList.length > 0 &&
                record.formItemList[index].type == "TEXTAREA"
              ) {
                content = record.formItemList[index].textContent;
              } else {
                const checkeds = record.formItemList[index]?.optionList?.filter(
                  (atem) => atem.checked
                );
                content = checkeds
                  ? checkeds.map((atem) => atem.name).join("、")
                  : null;
              }
              return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
            },
          }));
          setTopicArr(arr);
        }
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabEcharts = (params = {}) => {
    setLoading(true);
    const { tabType, detailId, time } = params;
    const data = {
      id: detailId || id,
      type: tabType || tabsTrendType,
      startDate:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format("YYYY-MM-DD"),
      endDate:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format("YYYY-MM-DD"),
    };
    apiCall("/employeeTaskBatch/chart", "GET", data)
      .then((res) => {
        const { list } = res;
        setChartsData(list)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    getDataDetails({ pagination, detailType: type });
  };

  const handleDownload = (value) => {
    const data = value
      ? {
        employeeId: value.employeeId,
        employeeTaskBatchId: id,
      }
      : {
        employeeTaskBatchId: id,
        size: 1000,
      };
    apiCall("/employeeTaskBatch/info_push_data/export", "POST", data, null, {
      isExit: true,
      title: `数据明细.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onChangeTabsDataTrend = (type) => {
    const time = formForm.getFieldValue("time");
    setTabsTrendType(type);
    getTabEcharts({
      tabType: type,
      time: time && [
        time[0].format("YYYY-MM-DD"),
        time[1].format("YYYY-MM-DD"),
      ],
    });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
      case "0":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-1":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-7":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-15":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
      case "-30":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ];
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        });
        break;
    }
    if (value) {
      await formForm.setFieldsValue({
        time,
      });
    }
  };

  const onChangeTime = async (date, dateString) => {
    getTabEcharts({ time: date ? dateString : null });
    formForm.setFieldsValue({
      quickTime: null,
    });
  };

  const getTagStatus = (num) => {
    let data = {};
    switch (num) {
      case 0:
        data = {
          name: "进行中",
          color: "#5d8dd4",
        };
        break;
      case 1:
        data = {
          name: "未开始",
          color: "#aaaaaa",
        };
        break;
      case 2:
        data = {
          name: "已逾期",
          color: "#D9001B",
        };
        break;
      case 3:
        data = {
          name: "已完成",
          color: "#70B603",
        };
        break;
    }
    return data;
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <div className="OrdinaryDetails">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card title={<>基础信息</>}>
              {JSON.stringify(userInfo) != "{}" ? (
                <div className="header-box">
                  <div className="info">
                    <div className="taskName">
                      <span style={{ marginRight: "10px" }}>
                        {userInfo.name}
                      </span>
                      <Tag color={getTagStatus(userInfo.status).color}>
                        {getTagStatus(userInfo.status).name}
                      </Tag>
                    </div>
                    <Space wrap>
                      <span>
                        {type == "NOTICE" ? "阅知公告" : "信息报送"}
                        &nbsp;|&nbsp;
                        {userInfo.startTime + "~" + userInfo.endTime}
                      </span>
                    </Space>
                    <Space wrap>
                      <span>任务对象:</span>
                      {userInfo.executorsStat?.map((item, index) => (
                        <div className="avatar-name" key={index}>
                          {item.avatar && (
                            <Avatar
                              shape="square"
                              size="small"
                              src={item.avatar}
                            />
                          )}
                          <span style={{ verticalAlign: "middle" }}>
                            {item.name}
                          </span>
                        </div>
                      ))}
                    </Space>
                    <Space wrap>
                      <span>任务说明:</span>
                      <div>{userInfo.remark}</div>
                    </Space>
                    {type == "NOTICE" && (
                      <Space wrap>
                        <span>公告内容:</span>
                        <div>
                          {userInfo.messages && userInfo.messages[0].content}
                        </div>
                      </Space>
                    )}
                    <Space wrap>
                      <span>创建:</span>
                      <span>
                        {userInfo.createEmployeeName}&nbsp;{userInfo.createTime}
                      </span>
                    </Space>
                    <Space wrap>
                      <span>更新:</span>
                      <span>
                        {userInfo.updateEmployeeName}&nbsp;{userInfo.updateTime}
                      </span>
                    </Space>
                  </div>
                </div>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card
              title="数据总览"
              className="data-screening"
              extra={
                <Button type="primary" onClick={() => handleGoBack()}>
                  返回
                </Button>
              }
            >
              {JSON.stringify(statisticalInfo) != "{}" ? (
                <Row>
                  <Col span={8}>
                    <span className="num">
                      {statisticalInfo.visitCount || 0}
                    </span>
                    <Tooltip title="同一用户可访问多次">
                      <p className="tip">
                        累计访问次数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问次数：{statisticalInfo.visitCountToday}
                    </p>
                  </Col>
                  <Col span={8}>
                    <span className="num">
                      {statisticalInfo.visitorCount || 0}
                    </span>
                    <Tooltip title="同一用户访问多次算一次">
                      <p className="tip">
                        累计访问人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问人数：{statisticalInfo.visitorCountToday}
                    </p>
                  </Col>
                  <Col span={8}>
                    <span className="num">
                      {statisticalInfo.commitCount || 0}
                    </span>
                    <Tooltip title="点击【我知道了】算完成">
                      <p className="tip">
                        全部完成人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日完成人数：{statisticalInfo.commitCountToday}
                    </p>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Card bordered={false} title="数据趋势">
              <Tabs
                activeKey={tabsTrendType}
                destroyInactiveTabPane
                onChange={onChangeTabsDataTrend}
              >
                <TabPane tab="完成人数" key="FINISH_COUNT"></TabPane>
                <TabPane tab="访问人数" key="VISITOR_COUNT"></TabPane>
                <TabPane tab="访问次数" key="VISIT_COUNT">
                  {" "}
                </TabPane>
              </Tabs>
              <Form layout={"inline"} form={formForm}>
                <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                  <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                </FormItem>
                <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                  <Select
                    style={{ width: "200px" }}
                    options={[
                      {
                        label: "今天",
                        value: "0",
                      },
                      {
                        label: "昨天",
                        value: "-1",
                      },
                      {
                        label: "最近7天",
                        value: "-7",
                      },
                      {
                        label: "最近15天",
                        value: "-15",
                      },
                      {
                        label: "最近30天",
                        value: "-30",
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {chartsData && DemoLine(chartsData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
        <Card
          bordered={false}
          title={
            <div>
              <span style={{ marginRight: "10px" }}>数据明细</span>
              {type == "INFO_PUSH" && (
                <a
                  onClick={() => {
                    handleDownload(null);
                  }}
                >
                  下载全部
                </a>
              )}
            </div>
          }
        >
          <Table
            rowKey="employeeId"
            loading={loading}
            dataSource={dataSource}
            columns={type == "NOTICE" ? noticeColumns : columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(OrdinaryDetails);
