/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/09 10:49
 * @LastEditTime: 2025/05/12 13:47
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Ordinary/form.jsx
 * @Description: '阅知任务-表单(新增/编辑)'
 */

import React, { useEffect, useState } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Radio,
  message,
  DatePicker,
  Row,
  Col,
  Checkbox,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import {
  PlusOutlined,
  CheckOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons"
import { removeInputEmpty } from "common/regular"
import TransferFormModal from "components/Modal/TransferFormModal/home"
import { recursionKeyEmployeeOption } from "common/tree"
import { qs2obj } from "common/object"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import { clearCache } from "react-router-cache-route"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import "./form.less"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { TextArea } = Input

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}
const OrdinaryForm = (props) => {
  const [ordinaryForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [taskRadio, setTaskRadio] = useState(1)
  const [transferFormParams, setTransferFormParams] = useState({
    visible: false,
  })
  const [staffOption, setStaffOption] = useState([])
  const [staffSelected, setStaffSelected] = useState([])
  const [employeeCount, setEmployeeCount] = useState(0)
  const [selectType, setSelectType] = useState("NOTICE")
  const [taskName, setTaskName] = useState("")
  const [taskContent, setTaskContent] = useState("")
  const [remarkContent, setRemarkContent] = useState("")
  const [taskTime, setTaskTime] = useState([])
  const [msgList, setMsgList] = useState([])
  const [operationType, setOperationType] = useState(null)
  const scrollListData = [
    {
      title: "阅知公告",
      describe: "自上而下，为一线员工推送重要通知",
      type: "NOTICE",
    },
    {
      title: "信息报送",
      describe:
        "根据需要配置相应的表单以收集数据，用于一线人员快速提交资料及数据",
      type: "INFO_PUSH",
    },
  ]

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    type && setOperationType(type)
    getOptionGroupByDepartment()
    if (id) {
      setId(id)
      init(id)
    }
  }, [])

  // 员工
  const getOptionGroupByDepartment = () => {
    apiCall("/employee/company_and_employee_option", "GET")
      .then((res) => {
        setStaffOption(
          recursionKeyEmployeeOption(res).map((item, index) => ({
            ...item,
            checkable: false,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const init = async (id) => {
    const params = {
      id,
    }
    await fetchList(params)
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    const data = {
      id,
    }
    apiCall("/employeeTaskBatch/get", "GET", data)
      .then((res) => {
        const {
          name,
          type,
          startTime,
          endTime,
          messages,
          scope,
          executorList,
          formItemList,
          executorDepList,
          remark,
        } = res
        setSelectType(type)
        ordinaryForm.setFieldsValue({
          ...res,
          time: [
            startTime ? moment(startTime) : null,
            endTime ? moment(endTime) : null,
          ],
          messages: messages[0].content,
          executors: scope == "ALL" ? 1 : 2,
          executorDepList,
        })
        setTaskRadio(scope == "ALL" ? 1 : 2)
        const newStaffSelected = executorList?.map((item) => ({
          ...item,
          key: item.id,
        }))
        setStaffSelected(newStaffSelected ?? [])
        setMsgList(formItemList)
        setTaskName(name)
        setRemarkContent(remark)
        setTaskContent(messages[0].content)
        setTaskTime([
          startTime ? moment(startTime).format("MM/DD HH:mm") : null,
          endTime ? moment(endTime).format("MM/DD HH:mm") : null,
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeRadio = (e) => {
    setTaskRadio(e.target.value)
  }

  // 切换群发类型
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setSelectType(item.type)
    setTaskRadio(1)
    setStaffSelected([])
    setTaskName("")
    setTaskTime([])
    setTaskContent("")
    setRemarkContent("")
    ordinaryForm.resetFields()
  }

  const handleChangePicker = (date, dateString) => {
    const startTime = moment(dateString[0]).format("MM/DD HH:mm")
    const endTime = moment(dateString[1]).format("MM/DD HH:mm")
    setTaskTime([startTime, endTime])
  }

  // 报送表单-添加题目
  const handleAddMsg = (value) => {
    const data = JSON.parse(JSON.stringify(msgList))
    switch (value) {
      case "RADIO":
        data.push({
          type: "RADIO",
          optionList: [
            {
              other: false,
            },
            {
              other: false,
            },
            {
              other: false,
            },
          ],
        })
        break
      case "CHECKBOX":
        data.push({
          type: "CHECKBOX",
          optionList: [
            {
              other: false,
            },
            {
              other: false,
            },
            {
              other: false,
            },
          ],
        })
        break
      case "TEXTAREA":
        data.push({
          type: "TEXTAREA",
        })
        break
    }
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 删除题目
  const handleDelMsg = (index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data.splice(index, 1)
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 报送题目改变
  const handleChangeTitle = (e, index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].title = e.target.value
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 报送题目选项改变
  const handleChangeOption = (e, index, andex) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList[andex].name = e.target.value
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 报送题目文案改变
  // const handleChangeCopyWriter = (e, index) => {
  //   const data = JSON.parse(JSON.stringify(msgList));
  //   data[index].textContent = e.target.value;
  //   setMsgList(JSON.parse(JSON.stringify(data)));
  // };

  // 添加题目选项
  const onAdd = (index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.push({
      other: false,
    })
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 添加题目其他选项
  const onAddOther = (index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.push({
      other: true,
    })
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 删除题目选项
  const handleDelItem = (index, andex) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.splice(andex, 1)
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  const onSubmit = () => {
    ordinaryForm.validateFields().then((formData) => {
      if (selectType == "INFO_PUSH") {
        if (!msgList.length > 0) {
          message.error("请至少添加一个题目！")
          return
        }
        for (let i = 0; i < msgList.length; i++) {
          if (msgList[i].type != "TEXTAREA") {
            if (!msgList[i].optionList.length > 0) {
              message.error("请给题目至少添加一个选项！")
              return
            }
          }
        }
      }
      setLoading(true)
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      if (formData.executors == 1) {
        // 任务对象
        formData.executors = []
      }
      delete formData.executors
      // else {
      //   formData.executors = staffSelected.map((item) => item.id);
      // }
      formData.messages = [{ content: formData.messages }]
      const formItemList =
        selectType == "INFO_PUSH"
          ? formData.formItemList.map((item, index) => {
              item.type = msgList[index].type
              item.optionList &&
                item.optionList.forEach((atem, andex) => {
                  atem.other = !atem.name // name存在则不是其他的选项，反之则是。
                  atem.seqNo = andex
                })
              return item
            })
          : null

      const data = {
        id: operationType == "renew" ? null : id,
        ...formData,
        scope: taskRadio == 1 ? "ALL" : "PART",
        status: 1,
        type: selectType,
        formItemList,
      }
      apiCall("/employeeTaskBatch/addOrModify", "PUT", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/marketingtask")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  useEffect(() => {
    if (staffSelected && staffSelected.length) {
      const data = {
        list: staffSelected?.map((item) => item.id),
      }
      apiCall("/employee/getDepEmployeeCount", "POST", data).then((res) => {
        setEmployeeCount(res)
      })
    } else {
      setEmployeeCount(0)
    }
  }, [staffSelected])

  return (
    <div className="OrdinaryForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑阅知任务" : "创建阅知任务"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <h2 className="card-title">任务类型</h2>
          <ul className="scrollList">
            {scrollListData.map((item, index) => (
              <li
                key={index}
                className={
                  selectType == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${
                    selectType != item.type && id ? "#c1c1c1" : "unset"
                  }`,
                  cursor: `${
                    selectType != item.type && id ? "unset" : "pointer"
                  }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>
        </Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <h2 className="card-title">基础信息</h2>
              <Form {...layout} form={ordinaryForm}>
                <FormItem
                  label="任务名称"
                  name="name"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入任务名称" }]}
                >
                  <Input
                    placeholder="请输入任务名称"
                    allowClear
                    onChange={(e) => {
                      setTaskName(e.target.value)
                    }}
                  />
                </FormItem>
                <FormItem
                  name="time"
                  label="任务时间"
                  rules={[{ required: true, message: "请选择任务时间" }]}
                >
                  {/* <RangePicker onChange={handleChangePicker} /> */}
                  <RangePicker
                    showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
                    format="YYYY-MM-DD HH:mm"
                    onChange={handleChangePicker}
                  />
                </FormItem>
                <FormItem
                  label="任务对象"
                  name="executors"
                  initialValue={1}
                  rules={[{ required: true, message: "请选择任务对象" }]}
                  style={{ marginBottom: "0" }}
                >
                  <Radio.Group onChange={onChangeRadio}>
                    <Radio value={1}>全部员工</Radio>
                    <Radio value={2}>部分员工</Radio>
                  </Radio.Group>
                </FormItem>
                {taskRadio == 2 ? (
                  <>
                    <FormItem name="executorDepList" label="员工">
                      <ETypeTransferModal
                        title="选择员工"
                        onChange={(value, options) => {
                          setStaffSelected(options)
                        }}
                      />
                    </FormItem>
                  </>
                ) : (
                  ""
                )}
                <FormItem
                  label=" "
                  className="tagBtn"
                  style={{ margin: "0px 0px 20px 0px" }}
                >
                  <div
                    className="input-content-mask"
                    style={{ color: "rgba(0, 0, 0, 0.45)" }}
                  >
                    发送员工数：
                    {taskRadio == 1
                      ? staffOption[0]?.children?.length
                      : employeeCount}
                    人
                  </div>
                </FormItem>

                <FormItem
                  name="remark"
                  label="任务说明"
                  rules={[{ required: true, message: "请输入任务说明" }]}
                >
                  <TextArea
                    maxLength={100}
                    placeholder="请输入任务说明（100字内）"
                    allowClear
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    className="textArea-mid"
                    onChange={(e) => {
                      setRemarkContent(e.target.value)
                    }}
                  />
                </FormItem>

                {selectType == "NOTICE" && (
                  <>
                    <h2 className="card-title">任务信息</h2>
                    <FormItem
                      name="messages"
                      label="公告内容"
                      rules={[{ required: true, message: "请输入公告内容" }]}
                    >
                      <TextArea
                        maxLength={2000}
                        placeholder="公告内容"
                        allowClear
                        onChange={(e) => {
                          setTaskContent(e.target.value)
                        }}
                      />
                    </FormItem>
                  </>
                )}

                {selectType == "INFO_PUSH" && (
                  <div className="submissionInfo">
                    <h2 className="card-title">报送表单</h2>
                    <FormItem
                      wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}
                    >
                      <FormItem
                        extra="请按编写需要报送的内容"
                        style={{ marginBottom: "0px" }}
                      >
                        <div className="btn-flex">
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => handleAddMsg("RADIO")}
                          >
                            单选
                          </Button>
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => handleAddMsg("CHECKBOX")}
                          >
                            多选
                          </Button>
                          <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => handleAddMsg("TEXTAREA")}
                          >
                            文案
                          </Button>
                        </div>
                      </FormItem>
                      <div className="question">
                        {msgList.map((item, index) => (
                          <div key={index} className="msgItem">
                            {item.type == "RADIO" && (
                              <div
                                className="question_one"
                                style={{ marginBottom: "20px" }}
                              >
                                <span className="num">{index + 1}</span>
                                <div>
                                  <FormItem
                                    name={["formItemList", index, "title"]}
                                    getValueFromEvent={(e) =>
                                      removeInputEmpty(e)
                                    }
                                    rules={[
                                      {
                                        required: true,
                                        message: "请输入题目名称",
                                      },
                                    ]}
                                  >
                                    <Input
                                      placeholder="请输入题目名称"
                                      maxLength={30}
                                      value={msgList.value}
                                      style={{ width: "310px" }}
                                      allowClear
                                      onChange={(e) => {
                                        handleChangeTitle(e, index)
                                      }}
                                    />
                                  </FormItem>
                                  <div style={{ marginTop: "10px" }}>
                                    {item.optionList.map((atem, andex) => (
                                      <div
                                        key={andex}
                                        className="question_two_item"
                                      >
                                        <Radio
                                          defaultChecked={false}
                                          disabled
                                        ></Radio>
                                        {!atem.other && (
                                          <FormItem
                                            name={[
                                              "formItemList",
                                              index,
                                              "optionList",
                                              andex,
                                              "name",
                                            ]}
                                            style={{ display: "inline-block" }}
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入选项名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入选项名称"
                                              maxLength={30}
                                              style={{ width: "280px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeOption(
                                                  e,
                                                  index,
                                                  andex
                                                )
                                              }}
                                            />
                                          </FormItem>
                                        )}
                                        {atem.other && (
                                          <div className="flexItem">
                                            <span
                                              style={{ marginRight: "5px" }}
                                            >
                                              其他
                                            </span>
                                            <FormItem
                                              name={[
                                                "formItemList",
                                                index,
                                                "optionList",
                                                andex,
                                                "name",
                                              ]}
                                              style={{
                                                display: "inline-block",
                                                marginBottom: "0px",
                                              }}
                                            >
                                              <Input
                                                placeholder="请输入"
                                                disabled
                                                style={{
                                                  width: "245px",
                                                  borderBottom:
                                                    "1px solid #d9d9d9",
                                                }}
                                              />
                                            </FormItem>
                                          </div>
                                        )}
                                        <CloseCircleOutlined
                                          className="tagClose"
                                          onClick={() =>
                                            handleDelItem(index, andex)
                                          }
                                        />
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  style={{ marginLeft: "20px" }}
                                  onClick={() => onAdd(index)}
                                >
                                  选项
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  style={{ marginLeft: "10px" }}
                                  onClick={() => onAddOther(index)}
                                >
                                  其他
                                </Button>
                              </div>
                            )}
                            {item.type == "CHECKBOX" && (
                              <div
                                className="question_one"
                                style={{ marginBottom: "20px" }}
                              >
                                <span className="num">{index + 1}</span>
                                <div>
                                  <FormItem
                                    name={["formItemList", index, "title"]}
                                    getValueFromEvent={(e) =>
                                      removeInputEmpty(e)
                                    }
                                    rules={[
                                      {
                                        required: true,
                                        message: "请输入题目名称",
                                      },
                                    ]}
                                  >
                                    <Input
                                      placeholder="请输入题目名称"
                                      maxLength={30}
                                      style={{ width: "310px" }}
                                      allowClear
                                      onChange={(e) => {
                                        handleChangeTitle(e, index)
                                      }}
                                    />
                                  </FormItem>
                                  <div style={{ marginTop: "10px" }}>
                                    {item.optionList.map((atem, andex) => (
                                      <div
                                        key={andex}
                                        className="question_two_item"
                                      >
                                        <Checkbox
                                          defaultChecked={false}
                                          disabled
                                          style={{
                                            marginTop: "5px",
                                            marginRight: "10px",
                                          }}
                                        ></Checkbox>
                                        {!atem.other && (
                                          <FormItem
                                            name={[
                                              "formItemList",
                                              index,
                                              "optionList",
                                              andex,
                                              "name",
                                            ]}
                                            style={{ display: "inline-block" }}
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入选项名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入选项名称"
                                              maxLength={30}
                                              style={{ width: "280px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeOption(
                                                  e,
                                                  index,
                                                  andex
                                                )
                                              }}
                                            />
                                          </FormItem>
                                        )}
                                        {atem.other && (
                                          <div className="flexItem">
                                            <span
                                              style={{ marginRight: "5px" }}
                                            >
                                              其他
                                            </span>
                                            <FormItem
                                              name={[
                                                "formItemList",
                                                index,
                                                "optionList",
                                                andex,
                                                "name",
                                              ]}
                                              style={{
                                                display: "inline-block",
                                                marginBottom: "0px",
                                              }}
                                            >
                                              <Input
                                                placeholder="请输入"
                                                disabled
                                                style={{
                                                  width: "245px",
                                                  borderBottom:
                                                    "1px solid #d9d9d9",
                                                }}
                                              />
                                            </FormItem>
                                          </div>
                                        )}
                                        <CloseCircleOutlined
                                          className="tagClose"
                                          onClick={() =>
                                            handleDelItem(index, andex)
                                          }
                                        />
                                      </div>
                                    ))}
                                  </div>
                                </div>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  style={{ marginLeft: "20px" }}
                                  onClick={() => onAdd(index)}
                                >
                                  选项
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  style={{ marginLeft: "10px" }}
                                  onClick={() => onAddOther(index)}
                                >
                                  其他
                                </Button>
                              </div>
                            )}
                            {item.type == "TEXTAREA" && (
                              <div
                                className="question_one"
                                style={{ marginBottom: "20px" }}
                              >
                                <span className="num">{index + 1}</span>
                                <div>
                                  <FormItem
                                    name={["formItemList", index, "title"]}
                                    getValueFromEvent={(e) =>
                                      removeInputEmpty(e)
                                    }
                                    rules={[
                                      {
                                        required: true,
                                        message: "请输入题目名称",
                                      },
                                    ]}
                                  >
                                    <Input
                                      placeholder="请输入题目名称"
                                      maxLength={30}
                                      style={{ width: "310px" }}
                                      allowClear
                                      onChange={(e) => {
                                        handleChangeTitle(e, index)
                                      }}
                                    />
                                  </FormItem>
                                  <FormItem
                                    name={[
                                      "formItemList",
                                      index,
                                      "textContent",
                                    ]}
                                  >
                                    <TextArea
                                      placeholder="请输入（300字以内）"
                                      disabled
                                      autoSize={{ minRows: 2, maxRows: 7 }}
                                      className="textArea-mid"
                                    />
                                  </FormItem>
                                </div>
                              </div>
                            )}
                            <CloseCircleOutlined
                              className="msgClose"
                              onClick={() => handleDelMsg(index)}
                            />
                          </div>
                        ))}
                      </div>
                    </FormItem>
                  </div>
                )}
                <FormItem wrapperCol={{ offset: 8 }}>
                  <Button type="primary" onClick={() => onSubmit()}>
                    保存并通知员工
                  </Button>
                </FormItem>
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              <WibotMobilePreview
                title={selectType == "NOTICE" ? "活码名称" : "任务详情"}
              >
                {selectType == "NOTICE" && (
                  <>
                    <Row className="row-item">
                      <Col span={6}>任务名称</Col>
                      <Col span={18}>{taskName}</Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务类型</Col>
                      <Col span={18}>公告</Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务时间</Col>
                      <Col span={18}>
                        {taskTime[0] ? taskTime[0] + "-" + taskTime[1] : ""}
                      </Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务说明</Col>
                      <Col span={18}>{remarkContent}</Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>公告内容</Col>
                      <Col span={18}>{taskContent}</Col>
                    </Row>
                  </>
                )}
                {selectType == "INFO_PUSH" && (
                  <>
                    <div className="row-item" style={{ fontWeight: "bold" }}>
                      任务信息
                    </div>
                    <Row className="row-item">
                      <Col span={6}>任务名称</Col>
                      <Col span={18}>{taskName}</Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务类型</Col>
                      <Col span={18}>信息报送</Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务时间</Col>
                      <Col span={18}>
                        {taskTime[0] ? taskTime[0] + "-" + taskTime[1] : ""}
                      </Col>
                    </Row>
                    <Row className="row-item">
                      <Col span={6}>任务说明</Col>
                      <Col span={18}>{remarkContent}</Col>
                    </Row>
                    {/* <Row className='row-item'>
                          <Col span={6}>公告内容</Col>
                          <Col span={18} style={{ textAlign: 'left', paddingLeft: '10px' }}>{taskContent}</Col>
                        </Row> */}
                    <div className="row-item" style={{ fontWeight: "bold" }}>
                      报送内容
                    </div>
                    {msgList.map((item, index) => (
                      <div
                        className="row-item"
                        style={{ textAlign: "left" }}
                        key={index}
                      >
                        <p>
                          <span>{index + 1}. </span>
                          <span>{item.title}</span>
                        </p>
                        {item.type == "RADIO" && (
                          <Radio.Group defaultValue={null}>
                            <Space direction="vertical">
                              {item.optionList.map((atem, andex) => (
                                <Radio value={andex} key={andex}>
                                  {atem.other ? (
                                    <div
                                      style={{
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      <span>其他</span>
                                      <Input
                                        placeholder="请输入"
                                        bordered={false}
                                        style={{
                                          flex: 1,
                                          borderBottom: "1px solid #d9d9d9",
                                        }}
                                      />
                                    </div>
                                  ) : (
                                    atem.name
                                  )}
                                </Radio>
                              ))}
                            </Space>
                          </Radio.Group>
                        )}
                        {item.type == "CHECKBOX" && (
                          <Checkbox.Group defaultValue={null}>
                            <Space direction="vertical">
                              {item.optionList.map((atem, andex) => (
                                <Checkbox value={andex} key={andex}>
                                  {atem.other ? (
                                    <div
                                      style={{
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      <span>其他</span>
                                      <Input
                                        placeholder="请输入"
                                        bordered={false}
                                        style={{
                                          flex: 1,
                                          borderBottom: "1px solid #d9d9d9",
                                        }}
                                      />
                                    </div>
                                  ) : (
                                    atem.name
                                  )}
                                </Checkbox>
                              ))}
                            </Space>
                          </Checkbox.Group>
                        )}
                        {item.type == "TEXTAREA" && (
                          <TextArea
                            placeholder="请输入"
                            value={item.textContent}
                            bordered={false}
                            autoSize={{ minRows: 2, maxRows: 7 }}
                          />
                        )}
                      </div>
                    ))}
                  </>
                )}
              </WibotMobilePreview>
            </Col>
          </Row>
        </Card>
      </Spin>
      <TransferFormModal {...transferFormParams} />
    </div>
  )
}

export default OrdinaryForm
