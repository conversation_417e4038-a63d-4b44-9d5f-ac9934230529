/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/06 14:52
 * @LastEditTime: 2025/07/03 10:38
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/Ordinary/home.jsx
 * @Description: '阅知任务'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  message,
  Select,
} from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import moment from "moment"
import FilterBar from "components/FilterBar/FilterBar"
import SysDictSelect from "components/select/SysDictSelect"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"
import StatusFormModal from "../comps/StatusFormModal/home"
import PreviewModal from "./comps/PreviewModal/home"
import WibotTableTag from "components/WibotTableTag/home"
import ListOperation from "components/ListOperation/home"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Option } = Select

const Ordinary = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const [statusFormParams, setStatusFormParams] = useState({ visible: false })
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "任务说明",
      width: "160px",
      dataIndex: "remark",
      key: "remark",
      ellipsis: true,
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "阅知内容",
      width: "160px",
      dataIndex: "messages",
      key: "messages",
      ellipsis: true,
      align: "center",
      render: (value, record, index) =>
        record.type == "INFO_PUSH" ? (
          <a onClick={() => handlePreview(record)}>查看</a>
        ) : (
          value?.map((item, index) => (
            <Tooltip placement="topLeft" key={index} title={item.content}>
              {item.content}
            </Tooltip>
          ))
        ),
    },
    {
      title: "任务类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <div>{value == "NOTICE" ? "阅知公告" : "信息报送"}</div>
      ),
    },
    {
      title: "任务对象",
      width: "160px",
      dataIndex: "executorNames",
      key: "executorNames",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "任务时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: "任务状态",
      width: "160px",
      dataIndex: "status",
      key: "status",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="WECOM_TASK_STATUS" dictkey={value} color />
      ),
    },
    {
      title: "员工完成情况",
      width: "160px",
      dataIndex: "employeeFinishCount",
      key: "employeeFinishCount",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handleSendStatus(record)}>
          {record.employeeFinishCount}/{record.employeeTaskCount}
        </a>
      ),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [{ onClick: () => handleDetail(record), name: "详情" }]
        if (record.status == 0) {
          opts.push({ onClick: () => handleRecall(record), name: "撤回" })
          opts.push({ onClick: () => handleRemind(record), name: "提醒" })
        }
        if (record.status == 1) {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" })
        }
        if (record.status == 5) {
          opts.push({ onClick: () => handleRenew(record), name: "恢复" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    fetchList()
  }, [])

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.createTime
      }
      if (formData.planTime) {
        formData.startPlanTime = moment(formData.planTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endPlanTime = moment(formData.planTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.planTime
      }
      formData.types = formData.types ? formData.types : "NOTICE,INFO_PUSH"
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }

      apiCall("/employeeTaskBatch/page", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const handleAdd = () => {
    props.history.push("/wecom/marketingtask/ordinary/form")
  }

  const handlePreview = (record) => {
    setPreviewParams({
      visible: true,
      title: "任务详情",
      dataObj: record,
      onCancel: () => {
        setPreviewParams({ visible: false })
      },
    })
  }

  const handleSendStatus = (record) => {
    setLoading(true)
    apiCall("/employeeTaskBatch/getExecutorStatList", "GET", { id: record.id })
      .then((res) => {
        setStatusFormParams({
          visible: true,
          title: "员工完成情况",
          issueStatusFlag: true,
          dataSource: res ?? [],
          onSubmit: () => {
            setStatusFormParams({ visible: false })
          },
          onCancel: () => {
            setStatusFormParams({ visible: false })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleRemind = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "提醒确认",
      content: "您将给所有未发送的成员发送提醒通知，确认继续吗？",
      onSubmit: () => {
        const data = {
          id: id,
        }
        apiCall("/employeeTaskBatch/remind", "GET", data)
          .then((res) => {
            message.success("提醒成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleEdit = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/marketingtask/ordinary/form",
      search: `?id=${id}`,
    })
  }

  const handleDetail = (record) => {
    const { id, type } = record
    props.history.push({
      pathname: "/wecom/marketingtask/ordinary/details",
      search: `?id=${id}&type=${type}`,
    })
  }

  const handleRecall = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "撤回确认",
      content: `您将撤回阅知任务【${name}】，撤回后恢复任务需再次编辑，确认继续吗？`,
      onSubmit: () => {
        const data = {}
        apiCall(`/employeeTaskBatch/stopOrRecall?id=${id}`, "post", data)
          .then((res) => {
            message.success("撤回成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleRenew = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/marketingtask/ordinary/form",
      search: `?id=${id}&type=renew`,
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="liveCode">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="任务名称、任务说明" allowClear />
          </FormItem>
          {/* <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="任务内容" allowClear />
          </FormItem> */}
          <FormItem name="status">
            <SysDictSelect placeholder="任务状态" dataset="WECOM_TASK_STATUS" />
          </FormItem>
          <FormItem name="types">
            <Select placeholder="任务类型" allowClear>
              <Option value="NOTICE">阅知公告</Option>
              <Option value="INFO_PUSH">信息报送</Option>
            </Select>
          </FormItem>
          {/* <FormItem name="executoreNames" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="任务对象" allowClear />
          </FormItem> */}
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="planTime" label="任务时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <Button type="primary" onClick={() => handleAdd()}>
            创建阅知任务
          </Button>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <StatusFormModal {...statusFormParams} />
      <PreviewModal params={previewParams} />
    </div>
  )
}

export default withRouter(Ordinary)
