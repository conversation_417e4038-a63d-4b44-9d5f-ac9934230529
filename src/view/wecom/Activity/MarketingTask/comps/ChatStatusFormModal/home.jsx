/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/01 09:22
 * @LastEditTime: 2023/10/31 17:40
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingTask\comps\ChatStatusFormModal\home.jsx
 * @Description: '客户群群发送达情况'
 */

import React, { memo, useEffect, useState } from 'react';
import { Modal, Spin, Button, Empty } from 'antd';
import { apiCall } from 'common/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import './home.less';

const ChatStatusFormModal = (props) => {
  const { visible, title, id, onSubmit, onCancel } = props;

  const [loading, setLoading] = useState(true);
  const [sentData, setSentData] = useState([]);
  const [notSentData, setNotSentData] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 10,
  });

  useEffect(() => {
    if (visible) {
      getTargetStatSendList();
      getTargetStatNotSendList();
    } else {
      setSentData([]);
      setNotSentData([]);
      setPaginations({ current: 1, pageSize: 10 });
      setPaginations1({ current: 1, pageSize: 10 });
    }
  }, [visible]);

  const getTargetStatSendList = (params = {}) => {
    setLoading(true);
    const { pagination = paginations } = params;
    const data = {
      id,
      current: pagination.current,
      size: pagination.pageSize,
      status: 1,
    };
    apiCall('/employeeTaskBatch/getTargetStatList', 'GET', data)
      .then((res) => {
        const { records, current, size, total } = res;
        setSentData([...sentData, ...records]);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const getTargetStatNotSendList = (params = {}) => {
    setLoading(true);
    const { pagination = paginations } = params;
    const data = {
      id,
      current: pagination.current,
      size: pagination.pageSize,
      status: 0,
    };
    apiCall('/employeeTaskBatch/getTargetStatList', 'GET', data)
      .then((res) => {
        const { records, current, size, total } = res;
        setNotSentData([...notSentData, ...records]);
        setPaginations1({
          current: current,
          pageSize: size,
          total: total,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 下拉加载
  const loadMoreData = (e, type = null) => {
    if (type == '1') {
      if (sentData.length < paginations.total) {
        const pageInfo = {
          ...paginations,
          current: paginations.current + 1,
        };
        getTargetStatSendList({ pagination: pageInfo, push: true });
      }
    } else {
      if (notSentData.length < paginations1.total) {
        const pageInfo = {
          ...paginations1,
          current: paginations1.current + 1,
        };
        getTargetStatNotSendList({ pagination: pageInfo, push: true });
      }
    }
  };

  const onOk = () => {
    onSubmit();
  };
  const onModalCancel = () => {
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      title={title}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onModalCancel}
      className="chatStatusFormModal"
      footer={
        <Button type="primary" onClick={onOk}>
          确认
        </Button>
      }
    >
      <Spin spinning={loading}>
        <div className="emp-modal-wrap">
          <div className="emp-tree-left" id="scrollableDiv1">
            <span>已送达（{paginations.total}）</span>
            {sentData.length > 0 ? (
              <InfiniteScroll
                dataLength={sentData.length}
                next={(e) => {
                  loadMoreData(e, '1');
                }}
                hasMore={sentData.length < paginations.total}
                scrollableTarget="scrollableDiv1"
              >
                {sentData.map((item, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: '10px',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{ width: '100px' }}>
                        <div>{item.name}</div>
                        <div style={{ color: '#AAAAAA', fontSize: '12px' }}>
                          {item.employeeName}（群主）
                        </div>
                      </div>
                    </div>
                    {item.receiveTime ? (
                      <div style={{ width: '80px' }}>
                        <span
                          style={{ verticalAlign: 'top', fontSize: '12px' }}
                        >
                          {item.receiveTime}
                        </span>
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))}
              </InfiniteScroll>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
          <div className="emp-tree-right" id="scrollableDiv2">
            <span>未送达（{paginations1.total}）</span>
            {notSentData.length > 0 ? (
              <InfiniteScroll
                dataLength={notSentData.length}
                next={(e) => {
                  loadMoreData(e, '2');
                }}
                hasMore={notSentData.length < paginations1.total}
                scrollableTarget="scrollableDiv2"
              >
                {notSentData.map((item, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: '10px',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div>
                        <div>{item.name}</div>
                        <div style={{ color: '#AAAAAA', fontSize: '12px' }}>
                          {item.employeeName}（群主）
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </InfiniteScroll>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>
      </Spin>
    </Modal>
  );
};
export default memo(ChatStatusFormModal);
