/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/09 13:51
 * @LastEditTime: 2023/08/14 17:57
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingTask\comps\StatusFormModal\home.jsx
 * @Description: '员工完成情况'
 */
import React, { memo, useEffect, useState } from 'react';
import { Modal, Form, Spin, Row, Col, Avatar, Image, Button } from 'antd';
import moment from 'moment';
import './home.less';

const StatusFormModal = (props) => {
  const {
    visible,
    title,
    issueStatusFlag = false,
    dataSource,
    onSubmit,
    onCancel,
  } = props;

  const [loading, setLoading] = useState(true);
  const [sentData, setSentData] = useState([]);
  const [notSentData, setNotSentData] = useState([]);

  useEffect(() => {
    if (visible) {
      setSentData(dataSource?.filter((item) => item.state) ?? []);
      setNotSentData(dataSource?.filter((item) => !item.state) ?? []);
      setLoading(false);
    }
  }, [visible]);

  const onOk = () => {
    onSubmit();
  };
  const onModalCancel = () => {
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      title={title}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      // onOk={onOk}
      onCancel={onModalCancel}
      className="statusFormModal"
      footer={
        <Button type="primary" onClick={onOk}>
          确认
        </Button>
      }
    >
      <Spin spinning={loading}>
        <div className="emp-modal-wrap">
          <div className="emp-tree-left">
            <span>已完成（{sentData.length}）</span>
            {sentData.map((item, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  position: 'relative',
                  marginTop: '10px',
                }}
              >
                {item.avatar && (
                  <div>
                    <Avatar
                      shape="square"
                      size={30}
                      src={<Image src={item.avatar} preview={false} />}
                    />
                  </div>
                )}
                <div style={{ margin: '0 5px' }}>
                  <div>{item.name}</div>
                  <div style={{ color: '#AAAAAA', fontSize: '12px' }}>
                    {item.dep}
                  </div>
                </div>
                {item.executeTime ? (
                  <div style={{ width: '80px' }}>
                    <span style={{ verticalAlign: 'top', fontSize: '12px' }}>
                      {moment(item.executeTime).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                  </div>
                ) : (
                  ''
                )}
              </div>
            ))}
          </div>
          <div className="emp-tree-right">
            <span>未完成（{notSentData.length}）</span>
            {notSentData.map((item, index) => (
              <Row
                gutter={8}
                key={index}
                align="middle"
                style={{ position: 'relative', marginTop: '10px' }}
              >
                {item.avatar && (
                  <Col>
                    <Avatar
                      shape="square"
                      size={30}
                      src={<Image src={item.avatar} preview={false} />}
                    />
                  </Col>
                )}
                <Col style={{ maxWidth: '150px' }}>
                  <div>{item.name}</div>
                  <div style={{ color: '#AAAAAA', fontSize: '12px' }}>
                    {item.departmentName}
                  </div>
                </Col>
                <Col>
                  {issueStatusFlag ? (
                    <>
                      {item.issueStatus == 'ISSUED' && item.readStatus && (
                        <div className="identification">已读</div>
                      )}
                      {item.issueStatus == 'ISSUED' && !item.readStatus && (
                        <div className="identification noIdent">未读</div>
                      )}
                      {item.issueStatus == 'UNISSUED' && (
                        <div className="identification unissued">未下发</div>
                      )}
                      {item.issueStatus == 'ISSUE_FAILED' && (
                        <div className="identification issueFail">下发失败</div>
                      )}
                      {!item.issueStatus && (
                        <div
                          className={`identification ${
                            !item.readStatus && 'noIdent'
                          }`}
                        >
                          {!item.readStatus ? '未读' : '已读'}
                        </div>
                      )}
                    </>
                  ) : (
                    <div
                      className={`identification ${
                        !item.readStatus && 'noIdent'
                      }`}
                    >
                      {!item.readStatus ? '未读' : '已读'}
                    </div>
                  )}
                </Col>
                {item.executeTime ? (
                  <Col>
                    <span style={{width: '100%', verticalAlign: 'middle', fontSize: '12px' }}>
                      {moment(item.executeTime).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                  </Col>
                ) : (
                  ''
                )}
              </Row>
            ))}
          </div>
        </div>
      </Spin>
    </Modal>
  );
};
export default memo(StatusFormModal);
