.statusFormModal {
  .ant-modal-content {
    height: 505px;
  }

  .emp-modal-wrap {
    display: flex;
    flex-direction: row;

    // .modal-customers{
    //     flex: 1;
    // }
    .emp-tree-left {
      flex: 1;
      overflow-y: auto;
      height: 350px;
      border-right: 1px solid #e5e5e5;

      .modal-empTree-tree {
        height: 300px;
        overflow: auto;
      }
    }

    .emp-tree-right {
      flex: 1;
      overflow-y: auto;
      height: 350px;
      padding-left: 20px;

      .identification {
        width: 32px;
        border: 1px solid #5d8dd4;
        text-align: center;
        color: #5d8dd4;
        line-height: 17px;
        font-size: 13px;

        &.noIdent {
          color: #AAAAAA;
          border: 1px solid #AAAAAA;
        }
        &.unissued{
          color: #fc6e25;
          border: 1px solid #fc6e25;
        }
        &.issueFail{
          color: #ff0137;
          border: 1px solid #ff0137;
        }
      }
    }
  }
}