/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/18 16:08
 * @LastEditTime: 2024/05/15 10:45
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/MarketingTask/comps/UploadList/home.jsx
 * @Description: '上传客户清单'
 */
import React, {forwardRef, useEffect, useImperativeHandle, useState,} from "react";
import {Avatar, Button, Form, message, Table, Tooltip, Upload} from "antd";
import moment from "moment";
import {UploadOutlined} from "@ant-design/icons";
import {apiCall} from "common/utils";
import "./home.less";

const FormItem = Form.Item;

const UploadList = forwardRef((props, ref) => {
  const { formForm, operationType, templateUrl = "/file/template", templateName= "客户群发任务模板.xlsx", uploadUrl = '/employeeTaskBatch/uploadCustomerList', downloadUrl = '/employeeTaskBatch/downloadCustomerList' } = props.params;
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [fileDataSource, setFileDataSource] = useState([]);
  const [fileData, setFileData] = useState([]);
  const [isUpload, setIsUpload] = useState(true);
  const [successCount, setSuccessCount] = useState(0);
  const [failCount, setFailCount] = useState(0);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户cis/客户企微id",
      width: "160px",
      dataIndex: "cisOrWecomId",
      key: "cisOrWecomId",
      align: "center",
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {record.customerNickname}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{value}</span>
            </div>
          </div>
        );
        return !record.customerNickname && !value && !record.customerAvatar ? (
          ""
        ) : (
          <Tooltip placement="topLeft" title={title}>{content}</Tooltip>
        );
      },
    },
    {
      title: "客户负责人",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "失败原因",
      width: "160px",
      dataIndex: "failReason",
      key: "failReason",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
  ];

  useImperativeHandle(ref, () => ({
    getInitCustomerList, // 初始化客户清单
    getInitCustomerFile, // 初始化客户清单文件
    getCustomerList, // 获取上传客户清单列表
    getFileName, // 获取上传客户清单列表
  }));

  useEffect(() => {
    setSuccessCount(fileDataSource.filter((item) => item.success).length || 0);
    setFailCount(fileDataSource.filter((item) => !item.success).length || 0);
  }, []);

  useEffect(() => {
    setSuccessCount(fileDataSource.filter((item) => item.success).length || 0);
    setFailCount(fileDataSource.filter((item) => !item.success).length || 0);
  }, [fileDataSource]);

  const getInitCustomerList = (list) => {
    setIsUpload(false);
    setFileDataSource(list);
  };

  const getInitCustomerFile = (params) => {
    const { name, file } = params;
    setFileData([
      {
        name: name,
        status: "done",
      },
    ]);
    formForm.setFieldsValue({ file: file });
  };

  // 下载模板
  const handleDownload = () => {
    const data = {
      fileName: templateName,
    };
    apiCall(templateUrl, "GET", data, null, {
      isExit: true,
      title: `客户清单.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => {
        message.success("下载成功！");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 下载客户清单模板
  const handleDownloadCustomerList = () => {
    const data = {
      fileId: formForm.getFieldValue('file'),
    };
    apiCall(downloadUrl, "GET", data, null, {
      isExit: true,
      title: fileData[0].name,
    })
      .then((res) => {
        message.success("下载成功！");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 选择文件
  const customFileRequest = (config) => {
    const File = config.file;
    setFileData([
      {
        name: File.name,
        status: "uploading",
      },
    ]);
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append("file", File); // 返回压缩后的图片
    const data = formData;
    apiCall(uploadUrl, "POST", data)
      .then((res) => {
        const { fileId, fileName } = res;
        setFileData([
          {
            name: fileName,
            status: "done",
          },
        ]);
        formForm.setFieldsValue({ file: fileId });
        // setFileDataSource(res);
        // setPaginations({
        //   current: 1,
        //   pageSize: 10,
        //   total: res.length,
        //   showQuickJumper: true,
        //   showSizeChanger: true,
        //   showTotal: (total, range) => `共 ${total} 条记录`,
        // });
      })
      .catch((err) => {
        console.log(err);
        setFileData([]);
      })
      .finally(() => { });
  };

  const onRemoveUpload = (file) => {
    setFileData([]);
    formForm.setFieldsValue({
      file: "",
    });
  };

  const handleFile = () => {
    if (!fileData.length > 0) {
      message.error("请上传文件！");
      return;
    }
    setIsUpload(false);
  };

  const onChangeTable = (pagination, filters, sorter) => {
    setPaginations({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: fileDataSource.length,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total, range) => `共 ${total} 条记录`,
    });
  };

  const getCustomerList = () => fileDataSource;

  const getFileName = () => fileData.length && fileData[0].name || null;

  return (
    <div className="input-content-mask">
      {isUpload ? (
        <>
          <h3>上传客户清单</h3>
          <FormItem
            label="选择文件"
            name="file"
            extra={
              <div>
                可上传客户 unionid 清单，单次上限 5 万条，支持 xls、xlsx、csv 格式文件
                <a onClick={handleDownload} style={{ marginLeft: "5px" }}>
                  下载模板
                </a>
              </div>
            }
            rules={[{ required: true, message: "请上传文件" }]}
          >
            <div style={{ display: "flex" }}>
              <Upload
                name="file"
                fileList={fileData}
                customRequest={customFileRequest}
                accept=".xls, .xlsx, .csv"
                onRemove={onRemoveUpload}
                disabled={operationType == 'detail'}
              >
                {fileData.length <= 0 ? (
                  <Button icon={<UploadOutlined />}>选择文件</Button>
                ) : null}
              </Upload>
              {/* <Button
                type="primary"
                onClick={handleFile}
                style={{ marginLeft: "20px" }}
              >
                解析文件
              </Button> */}
              {
                (fileData.length && fileData[0].status == 'done') && <Button
                  type="primary"
                  onClick={handleDownloadCustomerList}
                  style={{ marginLeft: "20px" }}
                >
                  下载清单
                </Button> || ''
              }
            </div>
          </FormItem>
        </>
      ) : (
        <div className="uploadList-box">
          <div className="uploadList-box-flex">
            <div style={{ display: "flex", alignItems: "center" }}>
              <h3>客户列表</h3>
              <span className="box-flex-tips">
                本次共识别到{fileDataSource.length}个客户，{successCount}
                个成功，{failCount}个失败
              </span>
            </div>
            <Button
              type="primary"
              onClick={() => {
                setIsUpload(true);
                onRemoveUpload();
              }}
            >
              重新上传
            </Button>
          </div>
          <Table
            rowKey="customerId"
            dataSource={fileDataSource}
            columns={columns}
            pagination={paginations}
            onChange={onChangeTable}
            scroll={{ x: 1300 }}
          // scroll={{ x: "max-content" }}
          />
        </div>
      )}
    </div>
  );
});

export default UploadList;
