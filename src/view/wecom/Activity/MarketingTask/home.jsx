/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/06 14:47
 * @LastEditTime: 2023/04/20 14:51
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\MarketingTask\home.jsx
 * @Description: '营销任务'
 */

import { Card, Tabs } from "antd"
import React, { useEffect, useState } from "react"
// 模块组件
import { versionFnMap } from "config"
import Calendar from "./Calendar/home"
import Mass from "./Mass/home"
import Ordinary from "./Ordinary/home"

const { TabPane } = Tabs

const MarketTask = (props) => {
  const [tabsIndex, setTabsIndex] = useState("")

  useEffect(() => {
    setTabsIndex(localStorage.getItem("marketTasktabsIndex") || "2")
  }, [])

  const onChangeTabs = (index) => {
    localStorage.setItem("marketTasktabsIndex", index)
    setTabsIndex(index)
  }

  return (
    <div className="activity">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          {versionFnMap.system_ui().marketingTabs.includes(1) && (
            <TabPane tab="运营日历" key="1">
              <Calendar />
            </TabPane>
          )}
          {versionFnMap.system_ui().marketingTabs.includes(2) && (
            <TabPane tab="群发任务" key="2">
              <Mass />
            </TabPane>
          )}
          {versionFnMap.system_ui().marketingTabs.includes(3) && (
            <TabPane tab="阅知任务" key="3">
              <Ordinary />
            </TabPane>
          )}
          {/* {versionFnMap.system_ui().marketingTabs.includes(4) && (
            <TabPane tab="生日祝福" key="4">
              <BirthdayWish />
            </TabPane>
          )} */}
        </Tabs>
      </Card>
    </div>
  )
}

export default MarketTask
