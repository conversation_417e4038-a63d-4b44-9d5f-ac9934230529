/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/12 09:28
 * @LastEditTime: 2024/07/15 15:27
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Moment/InteracStatistics/home.jsx
 * @Description: '互动统计'
 */

import React, {useEffect, useRef, useState} from 'react';
import {withRouter} from 'react-router-dom';
import {Button, Card, DatePicker, Form, message, Select, Table, Tooltip,} from 'antd';
import {apiCall} from 'common/utils';
import {qs2obj} from 'common/object';
import {timeStamp} from 'common/date';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotStatistic from 'components/WibotStatistic/home';
import {QuestionCircleOutlined} from "@ant-design/icons";
import ListOperation from "components/ListOperation/home";
import {WibotModal} from "components/WibotModal";
import {MomentComment} from "@/view/wecom/Activity/Moment/comps/momentComment";
import {OpenConversation} from "@/view/wecom/Activity/Moment/comps/openConversation";
import TypeMessageDrawer from "@/view/wecom/DigitalHuman/AgentSeat/comps/TypeMessage/drawer";
import {isV1, versionFnMap} from "config";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;

const MomentInteracStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [drawerParams, setDrawerParams] = useState({visible: false});
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: () => (
          <Tooltip title={isV1 && '若同一账号下存在重名客户的评论，会在客户名称后标注（重名）'} placement="topLeft">
            客户名称 {isV1() ? <QuestionCircleOutlined/>: null}
          </Tooltip>
      ),
      width: '160px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value} {isV1() && record.duplicateName ? <span style={{color: 'red'}}>（重名）</span> : ''}
        </Tooltip>
      ),
    },
    {
      title: '对应员工',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '互动类型',
      width: '160px',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (value, record, index) => (
          <span>{value == 'LIKE' ? '点赞' : (isV1() ?
              <Button type="link" onClick={() => handleShowComment(record)}>评论</Button> : '评论')}</span>
      ),
    },
    {
      title: '互动时间',
      width: '160px',
      dataIndex: 'addTime',
      key: 'addTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.addTime) - timeStamp(b.addTime),
    },

  ];

  if (isV1()) {
    columns.push({
      title: '操作',
      width: '160px',
      dataIndex: '',
      key: '',
      align: 'center',
      render: (record) => {
        let opts = [];

        !record.conversationId && opts.push({onClick: () => handleOpenConversation(record), name: "发起对话"});
        record.conversationId && opts.push({onClick: () => handleDetailConversation(record), name: "查看对话"});
        return <ListOperation opts={opts}/>;
      }
    },)
  }


  const handleDetailConversation = (record) => {
    const {conversationId, customerName, employeeName} = record;
    setDrawerParams({
      visible: true,
      title: `对话详情（${customerName}-${employeeName}）`,
      conversationId: conversationId,
      onCancel: () => {
        setDrawerParams({visible: false});
      },
    })
  }
  const handleOpenConversation = (record) => {
    WibotModal.open({
      title: '发起对话',
      footer: null,
      onOk: () => {
        message.success("操作成功");
        fetchList()
      },
      children: <OpenConversation record={record}/>
    })
  }

  const handleShowComment = (record) => {
    setLoading(true);
    apiCall('/moment/momentStat/comment', 'GET', {
      momentStatId: record.id,
    }).then(res => {
      setLoading(false);
      console.log(`[res]: `, res)
      WibotModal.open({
        footer: null,
        title: <div style={{textAlign: 'center'}}>
          <div>评论内容</div>
          <div>更新时间: {res.fetchTime}</div>
        </div>,
        children: <MomentComment list={res.commentList}/>,
      })
    })

  }
  const [statisticData, setStatisticData] = useState([
    {
      title: '点赞客户数',
      value: 0,
    },
    {
      title: '评论客户数',
      value: 0,
    },
    {
      title: '客户访问人数',
      value: 0,
      describe: '客户访问链接资源时，可增加统计数据',
    },
  ]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      getStatisticData(id);
      fetchList({ query: { momentId: id } });
    }
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minAddTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAddTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        momentId: id,
        ...query,
        ...formData,
      };
      apiCall('/moment/momentStat', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getStatisticData = (id) => {
    setLoading(true);
    const data = {
      momentId: id,
    };
    apiCall('/moment/momentStat/summary', 'GET', data)
      .then((res) => {
        const { commentCount, likeCount, visitorCount } = res;
        let newStatisticData = JSON.parse(JSON.stringify(statisticData));
        newStatisticData[0].value = likeCount;
        newStatisticData[1].value = commentCount;
        newStatisticData[2].value = visitorCount;
        setStatisticData(newStatisticData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    getStatisticData(id);
    fetchList();
  };

  // 导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.minAddTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAddTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(',') || null;
      const data = {
        momentId: id,
        ...formData,
      };
      apiCall('/moment/momentStat/export', 'GET', data, null, {
        isExit: true,
        title: `互动统计.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="Moment-InteracStatistics-Container">
      <div style={{ marginBottom: '20px' }}>
        <WibotStatistic
          list={statisticData}
          span={6}
        />
      </div>

      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="typeList">
            <Select placeholder="互动类型" allowClear>
              <Option value="LIKE">点赞</Option>
              <Option value="COMMENT">评论</Option>
            </Select>
          </FormItem>
          <FormItem
            name="depEmployeeIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="选择员工" multiple />
          </FormItem>
          <FormItem name="time" label="互动时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>

      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <TypeMessageDrawer params={drawerParams}/>
    </div>
  );
};

export default withRouter(MomentInteracStatistics);
