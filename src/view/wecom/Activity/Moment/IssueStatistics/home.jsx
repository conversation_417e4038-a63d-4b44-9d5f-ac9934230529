/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/28 11:31
 * @LastEditTime: 2024/07/15 14:59
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Moment/IssueStatistics/home.jsx
 * @Description: '发表统计'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Table,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { qs2obj } from 'common/object';
import SysDictSelect from 'components/select/SysDictSelect';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SysDictLabel from 'components/select/SysDictLabel';

const MomentIssueStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 5 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
    },
    {
      title: '发表状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => (
        <SysDictLabel dataset="MOMENT_PUBLISH_STATE" dictkey={value} color />
      ),
    },
    {
      title: "点赞数",
      width: "160px",
      dataIndex: "likeCount",
      key: "likeCount",
      align: "center",
    },
    {
      title: "评论数",
      width: "160px",
      dataIndex: "commentCount",
      key: "commentCount",
      align: "center",
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      fetchList({ query: { momentId: id } });
    }
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 5 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        momentId: id,
        ...query,
        ...formData,
      };
      apiCall("/moment/momentPublish", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="Moment-IssueStatistics-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item
            name="depEmployeeIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="选择员工" />
          </Form.Item>

          <Form.Item name="state">
            <SysDictSelect dataset="MOMENT_PUBLISH_STATE" placeholder="发表状态" />
          </Form.Item>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div></div>
        </div>
      </FilterBar>

      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(MomentIssueStatistics);
