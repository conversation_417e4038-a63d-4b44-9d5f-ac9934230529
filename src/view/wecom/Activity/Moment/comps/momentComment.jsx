import {List} from "antd";

export const MomentComment = ({list}) => {
    return (
        <List
            className="demo-loadmore-list"
            itemLayout="horizontal"
            dataSource={list}
            locale={{emptyText: '暂无评论'}}
            renderItem={(item) => (
                <List.Item
                    actions={[<span>{item.time}</span>]}
                    style={{padding: "2px"}}
                >
                    <List.Item.Meta title={item.userName} description={<span style={{whiteSpace: "pre-wrap"}}>{item.content}</span>}/>
                </List.Item>
            )}
        />
    );
};
