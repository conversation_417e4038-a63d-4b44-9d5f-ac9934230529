import {Button, Form, Input, Space, TreeSelect} from "antd";
import React, {useContext, useEffect, useState} from "react";
import TextArea from "antd/es/input/TextArea";
import {versionFnMap} from "config";
import {apiCall} from "common/utils";
import {WibotModalContext} from "components/WibotModal";

const layout = {
    labelCol: {span: 5},
    wrapperCol: {span: 19},
};
const tailLayout = {
    wrapperCol: {offset: 17, span: 7},
};
export const OpenConversation = ({record}) => {
    const {onCancel, onOk} = useContext(WibotModalContext);
    const [formForm] = Form.useForm();
    const agentSeat_title = versionFnMap.system_ui().agentSeat_title;
    const [loading, setLoading] = useState(false);
    const [classifyMenu, setClassifyMenu] = useState([]);

    useEffect(async () => {
        formForm.setFieldsValue({
            customerName: record.customerName,
            employeeName: record.employeeName,
        });
        await getInfoSourceTypeTree();
    }, [record]);

    const getInfoSourceTypeTree = async () => {
        setLoading(true);
        const data = {};

        await apiCall("/agentSeat/agentGroup/all/options?signIn=true", "GET", data)
            .then((res) => {
                setLoading(false);
                setClassifyMenu(
                    res.map((item) => {
                        return {
                            ...item,
                            disabled: true,
                        };
                    }),
                );
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => {
            });
    };

    const submit1 = () => {
        formForm.validateFields().then(async (formData) => {
            setLoading(true);
            await apiCall("/moment/momentStat/touchCustomer", "POST", {
                momentStatId: record.id,
                agentId: formData.agentId,
                remark: formData.remark,
            }).finally(() => {
              setTimeout(() => {
                setLoading(false);
              }, 500)
            })
            onOk && onOk();

        });
    };
    return (
        <Form form={formForm} {...layout} onFinish={submit1}>
            <Form.Item label="客户昵称" name="customerName">
                <Input bordered={false} readOnly/>
            </Form.Item>

            <Form.Item label="账号名称" name="employeeName">
                <Input bordered={false} readOnly/>
            </Form.Item>

            <Form.Item
                name="remark"
                label="跟进意见"
                rules={[{required: true, message: "请输入处理意见（200字）"}]}
            >
                <TextArea
                    placeholder="请输入处理意见（200字）"
                    allowClear
                    showCount
                    maxLength={200}
                    autoSize={{minRows: 4, maxRows: 7}}
                />
            </Form.Item>

            <Form.Item
                name="agentId"
                label={`跟进${agentSeat_title}`}
                rules={[{required: true, message: `请选择跟进${agentSeat_title}`}]}
            >
                <TreeSelect
                    allowClear
                    showArrow
                    showSearch
                    treeDefaultExpandAll
                    treeData={classifyMenu}
                    placeholder={`请选择跟进${agentSeat_title}`}
                    fieldNames={{label: "title", value: "key", children: "children"}}
                    treeNodeFilterProp="title"
                />
            </Form.Item>
            <Form.Item {...tailLayout}>
                <Space>
                    <Button onClick={() => onCancel && onCancel()}>取消</Button>
                    <Button type="primary" htmlType="submit" loading={loading}>
                        确定
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};
