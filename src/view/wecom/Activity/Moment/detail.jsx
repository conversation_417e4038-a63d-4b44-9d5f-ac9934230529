/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 16:54
 * @LastEditTime: 2025/04/30 10:12
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Moment/detail.jsx
 * @Description: ''
 */

import {
  Button,
  Card,
  Descriptions,
  message,
  Space,
  Spin,
  Tabs,
  Tag,
} from "antd"
import { qs2obj } from "common/object"
import { apiCall } from "common/utils"
import SysDictLabel from "components/select/SysDictLabel"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import moment from "moment"
import React, { useEffect, useState } from "react"
import "./detail.less"
import InteracStatistics from "./InteracStatistics/home"
import IssueStatistics from "./IssueStatistics/home"

const MomentDetail = (props) => {
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [infoData, setInfoData] = useState(null)
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      getInfoData(id)
    }
  }, [])

  const getInfoData = async (id) => {
    setLoading(true)
    await apiCall(`/moment/${id}`, "GET")
      .then((res) => {
        setInfoData(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handlePreview = (record) => {
    const { content } = record
    setResourcePreviewParams({
      visible: true,
      listData: content,
      type: "moment",
      onCancel: () => {
        setResourcePreviewParams({
          visible: false,
        })
      },
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const handleSyncData = (state) => {
    setLoading(true)
    let apiUrl =
      state == "Issue"
        ? `/moment/getMomentTask?momentId=${id}`
        : `/moment/getMomentComments?momentId=${id}`
    apiCall(apiUrl, "post")
      .then((res) => {
        message.success("同步成功！")
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <div className="Moment-Detail-Container">
      <Spin spinning={loading}>
        <Card
          title={
            <>
              <Descriptions column={1}>
                <Descriptions.Item label="朋友圈名称">
                  {infoData?.name || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="朋友圈说明">
                  {infoData?.description || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="发送范围">
                  {infoData?.depEmpList.length ? (
                    <ETypeTransferModal
                      readonly
                      btnShowFlag={false}
                      value={infoData.depEmpList}
                    />
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="企业客户标签">
                  {infoData?.wxcpTagList.length ? (
                    <Space wrap>
                      {infoData.wxcpTagList.map((item, index) => (
                        <Tag style={{ margin: "0" }} key={index}>
                          {item.title}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
              </Descriptions>
              <Descriptions column={2}>
                <Descriptions.Item label="可发表时间">
                  {infoData ? (
                    <>
                      {moment(infoData.startTime).format("YYYY-MM-DD HH:mm")} ～{" "}
                      {moment(infoData.endTime).format("YYYY-MM-DD HH:mm")}
                    </>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="实际发表时间">
                  {infoData?.executeTime || "-"}
                </Descriptions.Item>
              </Descriptions>
              <Descriptions column={1}>
                <Descriptions.Item label="创建人">
                  {infoData ? (
                    <>
                      {infoData.createEmployeeName}（{infoData.createTime}）
                    </>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item
                  label="朋友圈内容"
                  contentStyle={{ display: "flex", flexDirection: "column" }}
                >
                  {infoData?.copyWriteContent || ""}
                  {infoData ? (
                    <a onClick={() => handlePreview(infoData)}>预览</a>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {infoData ? (
                    <>
                      <SysDictLabel
                        dataset="MOMENT_STATE"
                        dictkey={infoData.state}
                      />
                      {infoData.state == "FINISHED" && (
                        <>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "center",
                            }}
                          >
                            （
                            <SysDictLabel
                              dataset="MOMENT_SUB_STATE"
                              dictkey={infoData.subState}
                            />
                            ）
                          </div>
                        </>
                      )}
                    </>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
                {infoData && infoData.state != "AUDITING" ? (
                  <>
                    <Descriptions.Item label="审核人">
                      {infoData.auditEmployeeName}（{infoData.auditTime}）
                    </Descriptions.Item>
                    <Descriptions.Item label="审核意见">
                      {infoData.auditOpinion}
                    </Descriptions.Item>
                  </>
                ) : (
                  ""
                )}
              </Descriptions>
            </>
          }
          extra={
            <Space size={[0, 0]} style={{ flexDirection: "column" }}>
              <Button
                style={{ marginBottom: "8px" }}
                type="primary"
                onClick={() => handleGoBack()}
              >
                返 回
              </Button>
              <Button
                style={{ marginBottom: "8px" }}
                type="primary"
                onClick={() => handleSyncData("Issue")}
              >
                同步发表数据
              </Button>
              <Button type="primary" onClick={() => handleSyncData("Interac")}>
                同步互动数据
              </Button>
            </Space>
          }
        >
          <Tabs destroyInactiveTabPane>
            <Tabs.TabPane tab="发表统计" key="1">
              <IssueStatistics />
            </Tabs.TabPane>
            <Tabs.TabPane tab="互动统计" key="2">
              <InteracStatistics />
            </Tabs.TabPane>
          </Tabs>
        </Card>
        <WibotMaterialPreviewModal params={resourcePreviewParams} />
      </Spin>
    </div>
  )
}

export default MomentDetail
