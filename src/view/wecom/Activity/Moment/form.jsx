/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 11:17
 * @LastEditTime: 2025/07/28 10:55
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Moment/form.jsx
 * @Description: '群发朋友圈-表单(新增/编辑)'
 */

import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Row,
  Space,
  Spin,
  TreeSelect,
  Typography,
  message,
} from "antd"
import { qs2obj } from "common/object"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import CircleMsgContent from "../MarketingTask/Mass/comps/CircleMsgContent"

const { RangePicker } = DatePicker
const { Text } = Typography
const { TextArea } = Input

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}

const MomentForm = (props) => {
  const [formForm] = Form.useForm()
  const onRefCircleMsgContent = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [type, setType] = useState(null)
  const [targetCount, setTargetCount] = useState(0)
  const [circleMsgList, setCircleMsgList] = useState([])
  const [infoData, setInfoData] = useState(null)
  const [labelTreeData, setLabelTreeData] = useState([])

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    getCustomerTagTreeData()
    if (id) {
      setId(id)
      setType(type)
      getInfoData(id)
    }
  }, [])

  const getInfoData = async (id) => {
    setLoading(true)
    await apiCall(`/moment/${id}`, "GET")
      .then((res) => {
        const { content, startTime, endTime, depEmpList, wxcpTagList } = res
        setInfoData(res)
        formForm.setFieldsValue({
          ...res,
          wxcpTagList: wxcpTagList.map((item) => item.key),
          planTime: [
            moment(startTime, "YYYY-MM-DD HH:mm"),
            moment(endTime, "YYYY-MM-DD HH:mm"),
          ],
        })
        onRefCircleMsgContent?.current?.getInitMsgList(content)
        getVisibleCount(depEmpList)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取预计可见客户数
  const getVisibleCount = async (value) => {
    setLoading(true)
    const data = {
      depEmpList: value,
    }
    await apiCall("/moment/getVisibleCount", "POST", data)
      .then((res) => {
        setTargetCount(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true)
    const data = {}
    await apiCall("/info/tag/wxcp/tree", "GET", data)
      .then((res) => {
        setLabelTreeData([...res])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (
        formData.circleMsgList.length === 1 &&
        formData.circleMsgList[0] === ""
      ) {
        message.error("请填写文案！")
        return false
      }
      if (!formData.depEmpList.length && !formData.wxcpTagList.length) {
        message.error("请选择可见的客户！")
        return false
      }
      if (formData.depEmpList.length && targetCount <= 0) {
        message.error("预计可见客户数必须大于0！")
        return false
      }
      setLoading(true)
      if (formData.planTime) {
        formData.startTime = moment(formData.planTime[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.planTime[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.planTime
      }
      const data = {
        ...formData,
        wxcpTagList: formData.wxcpTagList.map((item) => ({
          key: item,
        })),
        content: circleMsgList.map((item) => ({
          ...item,
          fileId: item.fileId || (item.shareImage && [item.shareImage]) || null,
        })),
      }
      let apiUrl = type == "edit" ? `/moment/update/${id}` : `/moment`
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          message.success(id ? "编辑成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.go(-1)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="Moment-Form-Container">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑朋友圈" : "新建朋友圈"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={14}>
              <Form
                {...layout}
                form={formForm}
                initialValues={{ circleMsgList: [""] }}
              >
                {(infoData && type == "edit" && (
                  <>
                    <h2 className="card-title">上次审核结果</h2>
                    <Form.Item label="审核人">
                      {infoData.auditEmployeeName}（{infoData.auditTime}）
                    </Form.Item>
                    <Form.Item label="审核意见">
                      {infoData.auditOpinion}
                    </Form.Item>
                    <Divider />
                  </>
                )) ||
                  ""}

                <Form.Item
                  name="name"
                  label="朋友圈名称"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入朋友圈名称" }]}
                >
                  <Input
                    placeholder="请输入朋友圈名称"
                    allowClear
                    maxLength={30}
                  />
                </Form.Item>

                <Form.Item name="description" label="朋友圈说明">
                  <TextArea
                    placeholder="请输入朋友圈说明（200字）"
                    allowClear
                    showCount
                    maxLength={200}
                    autoSize={{ minRows: 4, maxRows: 7 }}
                  />
                </Form.Item>

                <Form.Item required label="可见的客户">
                  <div style={{ background: "#f0f0f0", padding: "10px" }}>
                    <Form.Item
                      name="depEmpList"
                      initialValue={[]}
                      label="发送范围"
                      rules={[{ required: false, message: "请选择发送范围" }]}
                      tooltip="受工行配置中 “群发任务允许通知目标范围” 限制，当配置为仅托管账号时，默认筛选符合托管账号的员工。"
                      extra={
                        formForm?.getFieldValue("depEmpList")?.length ? (
                          <Form.Item
                            label="预计可见客户数"
                            tooltip="仅在选择发送范围后进行筛选统计，统计结果不包含企业客户标签筛选条件。"
                            style={{ margin: "0" }}
                          >
                            {targetCount} 人
                          </Form.Item>
                        ) : (
                          ""
                        )
                      }
                    >
                      <ETypeTransferModal
                        title="选择员工"
                        onChange={(value, options) => {
                          getVisibleCount(value)
                        }}
                      />
                    </Form.Item>

                    <Form.Item
                      name="wxcpTagList"
                      initialValue={[]}
                      label="企业客户标签"
                      tooltip="请于企业微信官方管理后台维护企业客户标签"
                    >
                      <TreeSelect
                        allowClear
                        showArrow
                        showSearch
                        treeCheckable
                        treeData={labelTreeData}
                        placeholder="企业客户标签"
                        maxTagCount="responsive"
                        showCheckedStrategy={TreeSelect.SHOW_PARENT}
                        fieldNames={{
                          label: "title",
                          value: "key",
                          children: "children",
                          parentid: "parentid",
                        }}
                        treeNodeFilterProp="title"
                      />
                    </Form.Item>

                    <div style={{ color: "#00000073" }}>
                      温馨提示
                      <br />
                      1. 多个标签时，客户满足其中一个标签即符合条件。
                      <br />
                      2. 符合标签的客户会在发送范围内进行筛选。
                      <br />
                      3.
                      若未选发送范围，而工行配置为仅托管账号时，系统默认选中全部托管账号。
                    </div>
                  </div>
                </Form.Item>

                <Form.Item
                  label="可发表时间"
                  name="planTime"
                  rules={[{ required: true, message: "请选择可发表时间" }]}
                >
                  <RangePicker
                    showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
                    format="YYYY-MM-DD HH:mm"
                    disabledDate={(current) =>
                      current && current < moment().subtract(1, "day")
                    }
                  />
                </Form.Item>

                <CircleMsgContent
                  name="circleMsgList"
                  required
                  ref={onRefCircleMsgContent}
                  params={{
                    formForm: formForm,
                  }}
                  onChangeMsgList={(data) => {
                    setCircleMsgList([...data])
                  }}
                />

                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    <Button type="primary" onClick={() => onSubmit()}>
                      发表朋友圈
                    </Button>
                  </Space>
                </div>
                <Text
                  type="secondary"
                  style={{ display: "block", textAlign: "center" }}
                >
                  若内容需要审核，则审核通过后将自动发表
                </Text>
              </Form>
            </Col>
            <Col xs={24} lg={10}>
              <WibotMaterialPreview type={"moment"} listData={circleMsgList} />
            </Col>
          </Row>
        </Card>
      </Spin>
    </div>
  )
}

export default MomentForm
