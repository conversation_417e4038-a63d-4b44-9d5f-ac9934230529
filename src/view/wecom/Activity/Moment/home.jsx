/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 10:57
 * @LastEditTime: 2025/04/30 14:57
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Moment/home.jsx
 * @Description: '客户朋友圈'
 */

import {
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  message,
  Table,
  Tooltip,
  Typography,
} from "antd"
import { timeStamp } from "common/date"
import { usePageCacheLifeCycle } from "common/hooks"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import FilterBar from "components/FilterBar/FilterBar"
import ListOperation from "components/ListOperation/home"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import ExportModal from "./comps/ExportModal"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Paragraph } = Typography

const Moment = (props) => {
  const formRef = useRef(null)
  const OperateModalRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })
  const [exportParams, setExportParams] = useState({ visible: false })
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "朋友圈名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "朋友圈说明",
      width: "160px",
      dataIndex: "description",
      key: "description",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "可发表时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {moment(record.startTime).format("YYYY-MM-DD HH:mm")}
          <br />～<br />
          {moment(record.endTime).format("YYYY-MM-DD HH:mm")}
        </>
      ),
    },
    {
      title: "实际发表时间",
      width: "160px",
      dataIndex: "executeTime",
      key: "executeTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "朋友圈内容",
      width: "160px",
      dataIndex: "content",
      key: "content",
      align: "center",
      render: (value, record, index) => (
        <>
          <Paragraph ellipsis style={{ margin: "0" }}>
            {record.copyWriteContent}
          </Paragraph>
          <a onClick={() => handlePreview(record)}>预览</a>
        </>
      ),
    },
    // {
    //   title: '发送范围',
    //   width: '160px',
    //   dataIndex: 'depEmpNameList',
    //   key: 'depEmpNameList',
    //   align: 'center',
    //   render: (value, record, index) => <WibotTableTag tagList={value} />,
    // },
    {
      title: "状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <>
          <SysDictLabel dataset="MOMENT_STATE" dictkey={value} />
          {value == "FINISHED" && (
            <>
              <div style={{ display: "flex", justifyContent: "center" }}>
                （
                <SysDictLabel
                  dataset="MOMENT_SUB_STATE"
                  dictkey={record.subState}
                />
                ）
              </div>
            </>
          )}
        </>
      ),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: "详情" },
          { onClick: () => handleCopy(record), name: "复制" },
        ]
        if (record.state == "REJECTED") {
          opts.push({ onClick: () => handleEdit(record), name: "重新编辑" })
        }
        if (
          record.state == "AUDITING" ||
          record.state == "NOT_STARTED" ||
          record.state == "IN_PROGRESS"
        ) {
          opts.push({ onClick: () => handleStop(record), name: "停止" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    fetchList()
  }, [])

  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    },
  })

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.createTime
      }
      formData.createDepEmpList = formData.createDepEmpList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }

      apiCall("/moment", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const handleAdd = () => {
    props.history.push("/wecom/moment/form")
  }

  const handleCopy = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/moment/form",
      search: `?id=${id}&type=copy`,
    })
  }

  const handleEdit = (record, type) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/moment/form",
      search: `?id=${id}&type=edit`,
    })
  }

  const handlePreview = (record) => {
    const { content } = record
    setResourcePreviewParams({
      visible: true,
      listData: content,
      type: "moment",
      onCancel: () => {
        setResourcePreviewParams({
          visible: false,
        })
      },
    })
  }

  const handleDetail = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/moment/detail",
      search: `?id=${id}`,
    })
  }

  const handleStop = (record) => {
    const { id } = record
    setOperateParams({
      visible: true,
      title: "停止确认",
      content: `确认停止发表这条朋友圈吗？`,
      onSubmit: () => {
        const data = {}
        apiCall(`/moment/stop?momentId=${id}`, "post", data)
          .then((res) => {
            message.success("停止成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false })
      },
    })
  }

  return (
    <div className="Moment-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="nameOrDesc"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="朋友圈名称/说明" allowClear />
          </FormItem>
          <FormItem name="state">
            <SysDictSelect placeholder="状态" dataset="MOMENT_STATE" />
          </FormItem>
          <FormItem name="createTime" label="可发表时间">
            <RangePicker />
          </FormItem>
          <FormItem
            name="createDepEmpList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新建朋友圈
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal ref={OperateModalRef} params={operateParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
      <ExportModal params={exportParams} />
    </div>
  )
}

export default Moment
