/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/26 11:43
 * @LastEditTime: 2023/09/21 10:38
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Prize/List/comps/FormModal.jsx
 * @Description: ''
 */
import React, { useState, memo, useEffect } from 'react';
import { Form, Input, Modal, InputNumber, Upload, Select } from 'antd';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import { apiCall } from 'common/utils';
import { removeInputEmpty, normFile } from 'common/regular';
import WibotUploadImage from 'components/WibotUploadImage/home';

const FormItem = Form.Item;
const { Option } = Select;

const FormModal = (props) => {
  const { visible, id, onOk, onCancel } = props.params;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');

  useEffect(() => {
    if (visible) {
      if (id) {
        getByIdPrize(id);
      }
    }
  }, [visible]);

  const getByIdPrize = (id) => {
    apiCall(`/activity/commodity/${id}`, 'GET').then((res) => {
      const { fileId } = res;
      setImageUrl(fileId);
      formForm.setFieldsValue({
        ...res,
        fileId: fileId && [fileId] || []
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const customRequest = (config) => {
    const File = config.file;
    const options = {
      width: 500,
      height: 500,
      quality: 0.9,
    };
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append('file', base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data).then((res) => {
        const { fileId, fileUrl } = res;
        setImageUrl(fileUrl);
        formForm.setFieldsValue({
          fileId: [fileId]
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
          setConfirmLoading(false);
        });
    });
  };

  const handleChange = (info) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      setConfirmLoading(true);
      return;
    }
  };

  const handleResetUpload = (e) => {
    e.preventDefault();// 阻止浏览器默认行为
    e.stopPropagation();// 阻止事件冒泡
    setImageUrl('');
  };

  const handleSubmit = () => {
    formForm.validateFields().then((formData) => {
      formData.fileId = formData.fileId[0];
      onOk?.(formData);
      handleCancel();
    });
  };

  const handleCancel = () => {
    formForm.setFieldsValue({});
    setImageUrl('');
    setLoading(false);
    setConfirmLoading(false);
    onCancel?.();
  };

  return (
    <Modal visible={visible}
      title="奖品信息"
      maskClosable={false}
      afterClose={null}
      onOk={handleSubmit}
      onCancel={handleCancel}
      destroyOnClose
      confirmLoading={confirmLoading}
      width={600}
    >
      <Form labelCol={{ span: 6 }}
        wrapperCol={{ span: 14 }} form={formForm}>
        <FormItem name="name" label="奖品名称" getValueFromEvent={(e) => removeInputEmpty(e)} rules={
          [{ required: true, message: '请输入奖品名称', }]}
        >
          <Input placeholder="请输入奖品名称" allowClear />
        </FormItem>
        <FormItem name="points" label="兑换所需积分" rules={
          [{ required: true, message: '请输入兑换所需积分', }]}
        >
          <InputNumber min={1} />
        </FormItem>
        <FormItem name="state" label="奖品状态">
          <Select placeholder="奖品状态" allowClear>
            <Option value>已上架</Option>
            <Option value={false}>已下架</Option>
          </Select>
        </FormItem>
        <FormItem name="fileId" valuePropName="fileList" getValueFromEvent={normFile} label="奖品图片" rules={
          [{ required: true, message: '请上传图片', }]}
          extra="请上传正方形图片，图片比例为1:1。" >
          <Upload
            name="file"
            customRequest={customRequest}
            listType="picture-card"

            showUploadList={false}
            beforeUpload={beforeUpload}
            onChange={handleChange}
          >
            <WibotUploadImage
              imageUrl={imageUrl}
              loading={loading}
              onClose={handleResetUpload}
            />
          </Upload>
        </FormItem>
        <FormItem name="description" label="奖品描述" getValueFromEvent={(e) => removeInputEmpty(e)} rules={
          [{ required: true, message: '请输入50字以内的描述', }]}
        >
          <Input.TextArea placeholder="请输入奖品描述" allowClear showCount maxLength={50} autoSize={{ minRows: 2, maxRows: 4 }} />
        </FormItem>
      </Form>
    </Modal>
  );
};
export default memo(FormModal);
