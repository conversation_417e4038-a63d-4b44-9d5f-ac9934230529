/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2022/05/26 11:24
 * @LastEditTime: 2023/11/21 16:18
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Prize/List/home.jsx
 * @Description: '奖品列表'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Button, Card, Form, Input, Table, Tooltip, DatePicker, Switch, Typography, Select, message } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal';
import ListOperation from 'components/ListOperation/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;
const { Option } = Select;

const PrizeList = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [formParams, setFormParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '奖品名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '兑换所需积分',
      width: '160px',
      dataIndex: 'points',
      key: 'points',
      align: 'center',
    },
    {
      title: '奖品描述',
      width: '160px',
      dataIndex: 'description',
      key: 'description',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}><Paragraph ellipsis={{ rows: 2, tooltip: true }}>{value}</Paragraph></Tooltip>,
    },
    {
      title: '奖品状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => <Switch className='prizeState' checkedChildren="已上架" unCheckedChildren="已下架" checked={value} onChange={(checked) => { onChangeSwitchStatus(checked, record, 'state'); }} />,
    },
    {
      title: '兑换状态',
      width: '160px',
      dataIndex: 'exchangeState',
      key: 'exchangeState',
      align: 'center',
      render: (value, record, index) => record.state ? <Switch className='prizeExchangeState' checkedChildren="未兑完" unCheckedChildren="已兑完" checked={value} onChange={(checked) => { onChangeSwitchStatus(checked, record); }} /> : '',
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '奖品状态变更时间',
      width: '160px',
      dataIndex: 'stateUpdateTime',
      key: 'stateUpdateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.stateUpdateTime) - timeStamp(b.stateUpdateTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (!record.state) {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxCreateTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      if (formData.statusTime) {
        formData.minStateUpdateTime = moment(formData.statusTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxStateUpdateTime = moment(formData.statusTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.statusTime;
      }
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/activity/commodity', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      onOk: (params) => {
        apiCall('/activity/commodity', 'POST', params).then((res) => {
          message.success('新增成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    console.log(id);
    setFormParams({
      visible: true,
      id: id,
      onOk: (params) => {
        apiCall(`/activity/commodity/${id}`, 'PUT', params).then((res) => {
          message.success('修改成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除奖品【${name}】，删除后立即失效，确认继续？`,
      onSubmit: () => {
        const data = {
          id,
        };
        apiCall(`/activity/commodity/${id}`, 'DELETE').then((res) => {
          message.success('删除成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeSwitchStatus = (checked, record, type) => {
    const { id } = record;
    const data = {
      [type == 'state' ? 'state' : 'exchangeState']: checked
    };
    apiCall(`/activity/commodity/${id}`, 'PUT', data).then((res) => {
      message.success('修改成功！');
      fetchList();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='prizeList'>
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} form={formForm}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="奖品名称" allowClear />
          </FormItem>
          <FormItem name="description" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="奖品描述" allowClear />
          </FormItem>
          <FormItem name="state">
            <Select placeholder="奖品状态" allowClear>
              <Option value>已上架</Option>
              <Option value={false}>已下架</Option>
            </Select>
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="statusTime" label="状态时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <Button type="primary" onClick={() => handleAdd()}>
            新增奖品
          </Button>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
      <OperateModal params={operateParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default withRouter(PrizeList);
