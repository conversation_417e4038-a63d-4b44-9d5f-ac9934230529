/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/26 14:13
 * @LastEditTime: 2023/11/17 10:54
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Prize/Records/home.jsx
 * @Description: ''
 */
import React, { useEffect, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import FilterBar from "components/FilterBar/FilterBar";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const PrizeRecords = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [employeeOption, setEmployeeOption] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "奖品名称",
      width: "160px",
      dataIndex: "commodityName",
      key: "commodityName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span
            style={{
              color: record.customerType == "微信用户" ? "#f59a23" : "#07c160",
            }}
          >
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            <div>
              {value}
              {companyName}
            </div>
            <div>{record.realName}</div>
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "140px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              <div>
                {value}
                {record.companyName && companyName}
              </div>
              <div>{record.realName}</div>
            </div>
          </div>
        );
        return <Tooltip placement="topLeft" title={title}>{content}</Tooltip>;
      },
    },
    {
      title: "消耗积分",
      dataIndex: "points",
      key: "points",
      align: "center",
    },
    {
      title: "核销操作人/核销时间",
      dataIndex: "verifyTime",
      key: "verifyTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.employeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.verifyTime) - timeStamp(b.verifyTime),
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.minVerifyTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxVerifyTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      formData.verificationDeptIdList =
        formData.verificationDeptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/commodityVerification", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 导出
  const handleExport = () => {
    // formForm.validateFields().then((formData) => {
    //   setLoading(true);
    //   if (formData.createTime) {
    //     formData.startCreateTime = moment(formData.createTime[0]._d).format(
    //       "YYYY-MM-DD 00:00:00"
    //     );
    //     formData.endCreateTime = moment(formData.createTime[1]._d).format(
    //       "YYYY-MM-DD 23:59:59"
    //     );
    //     delete formData.createTime;
    //   }
    //   if (formData.updateTime) {
    //     formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
    //       "YYYY-MM-DD 00:00:00"
    //     );
    //     formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
    //       "YYYY-MM-DD 23:59:59"
    //     );
    //     delete formData.updateTime;
    //   }
    //   const data = {
    //     ...formData,
    //   };
    //   apiCall("/activity/dynamicCode/export", "POST", data, null, {
    //     isExit: true,
    //     title: `积分申请记录.${moment().format('YYYY-MM-DD')}.xlsx`,
    //   })
    //     .then((res) => {})
    //     .catch((err) => {
    //       console.log(err);
    //     })
    //     .finally(() => {
    //       setLoading(false);
    //     });
    // });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="prizeList">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} form={formForm}>
          <FormItem
            name="commodityName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="奖品名称" allowClear />
          </FormItem>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="客户名称" allowClear />
          </FormItem>
          {/* <FormItem name="mobile" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户手机号" allowClear />
          </FormItem> */}
          <FormItem
            name="verificationDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="核销操作人" />
          </FormItem>
          <FormItem name="time" label="核销时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(PrizeRecords);
