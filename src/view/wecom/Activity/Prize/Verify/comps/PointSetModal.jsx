/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/23 15:37
 * @LastEditTime: 2023/03/23 16:01
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\Prize\Verify\comps\PointSetModal.jsx
 * @Description: '积分设置'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin, Switch } from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;

const PointSetModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [verifyFlag, setVerifyFlag] = useState(true);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      // const data = {
      //   ...formData,
      // };
      // apiCall("/customer/export", "POST", data)
      //   .then((res) => {
      //     onCancel();
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   })
      //   .finally(() => {
      //     setConfirmLoading(false);
      //   });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title="积分设置"
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      width={450}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            name="forbiddenFlag"
            valuePropName="checked"
            extra="开启后，员工提交的积分申请将需要人工审核通过"
            label="积分审核"
            rules={[{ required: true }]}
            initialValue
          >
            <Switch
              checkedChildren="开"
              unCheckedChildren="关"
              onChange={(value) => {
                setVerifyFlag(value);
              }}
            />
          </FormItem>

          {verifyFlag && (
            <FormItem
              label="审核人员"
              name="deptIdList"
              rules={[{ required: true, message: '请选择审核人员' }]}
              extra="积分审核人为本部门的积分申请进行审核"
            >
              <ETypeTransferModal
                title="组织架构"
                onChange={(value, options) => { }}
                mode={['dep,emp']}
              />
            </FormItem>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default PointSetModal;
