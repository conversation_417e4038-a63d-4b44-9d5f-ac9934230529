/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/20 18:05
 * @LastEditTime: 2023/11/21 14:27
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/Prize/Verify/home.jsx
 * @Description: '积分申请记录'
 */

import React, { useState, useEffect } from "react";
import { withRouter } from "react-router-dom";
import FilterBar from "components/FilterBar/FilterBar";
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
  TreeSelect,
  Avatar,
} from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import { removeInputEmpty } from "common/regular";
import SysDictSelect from "components/select/SysDictSelect";
import { InfoCircleOutlined } from "@ant-design/icons";
import FormModal from "./comps/FormModal";
import SysDictLabel from "components/select/SysDictLabel";
import PointSetModal from "./comps/PointSetModal";
import { timeStamp } from 'common/date';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;

const PointVerify = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [employeeTreeData, setEmployeeTreeData] = useState([]);
  const [formModalParams, setFormModalParams] = useState({ visible: false });
  const [pointSetParams, setPointSetParams] = useState({ visible: false });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span
            style={{ color: record.customerType == 1 ? "#07c160" : "#f59a23" }}
          >
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            <div>
              {value}
              {companyName}
            </div>
            <div>{record.realName}</div>
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "140px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              <div>
                {value}
                {record.companyName && companyName}
              </div>
              <div>{record.realName}</div>
            </div>
          </div>
        );
        return <Tooltip placement="topLeft" title={title}>{content}</Tooltip>;
      },
    },
    {
      title: "申请人",
      width: "160px",
      dataIndex: "applyEmployeeName",
      key: "applyEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "申请原因",
      width: "160px",
      dataIndex: "applyReason",
      key: "applyReason",
      ellipsis: "true",
      align: "center",
      render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: "当时积分",
      width: "140px",
      dataIndex: "currentPoints",
      key: "currentPoints",
      align: "center",
      sorter: (a, b) => a.currentPoints - b.currentPoints,
    },
    {
      title: () => (
        <span>
          申请积分
          <Tooltip title={"申请人提交申请时客户当时的积分，不会变动"}>
            <InfoCircleOutlined
              style={{ fontSize: "14px", marginLeft: "6px", color: "#c1c1c1" }}
            />
          </Tooltip>
        </span>
      ),
      width: "140px",
      dataIndex: "points",
      key: "points",
      align: "center",
    },
    {
      title: () => (
        <span>
          申请时间
          <Tooltip title={"申请人为客户申请添加的积分，不是最终的积分"}>
            <InfoCircleOutlined
              style={{ fontSize: "14px", marginLeft: "6px", color: "#c1c1c1" }}
            />
          </Tooltip>
        </span>
      ),
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
    },
    {
      title: "审核人/审核时间",
      width: "160px",
      dataIndex: "reviewTime",
      key: "reviewTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.reviewEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.reviewTime) - timeStamp(b.reviewTime),
    },
    {
      title: "审核备注",
      width: "200px",
      dataIndex: "reviewRemark",
      key: "reviewRemark",
      ellipsis: "true",
      align: "center",
      render: (value) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: "审核状态",
      width: "120px",
      dataIndex: "stateCode",
      key: "stateCode",
      ellipsis: "true",
      align: "center",
      render: (value) => <SysDictLabel dataset="REVIEWSTATE" dictkey={value} color />,
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [];
        if (record.stateCode == "2") {
          opts.push({ onClick: () => handleAudit(record), name: "审核" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getAppEmployeeOption();
    fetchList();
  }, []);

  const getAppEmployeeOption = () => {
    apiCall("/employee/option", "GET")
      .then((res) => {
        const treeData = [
          {
            title: "全选",
            value: "",
            key: "",
            children: res.map((item) => ({
              title: item.name,
              value: item.id,
              key: item.id,
            })),
          },
        ];
        setEmployeeTreeData(treeData);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.minApplyTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxApplyTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      if (formData.reviewTime) {
        formData.minReviewTime = moment(formData.reviewTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxReviewTime = moment(formData.reviewTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.reviewTime;
      }
      console.log(formData);
      formData.applyEmployeeIdList =
        (formData.applyEmployeeIdList &&
          formData.applyEmployeeIdList.join(",")) ||
        null;
      formData.reviewEmployeeIdList =
        (formData.reviewEmployeeIdList &&
          formData.reviewEmployeeIdList.join(",")) ||
        null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: "APPLY",
        ...query,
        ...formData,
      };

      apiCall("/customer/point", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleAudit = (record) => {
    const { id } = record;
    setFormModalParams({
      visible: true,
      dataSource: record,
      onOk: (params) => {
        apiCall(`/customer/point/review/${id}`, "PUT", params)
          .then((res) => {
            message.success("审核成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setFormModalParams({ visible: false });
      },
      onCancel: () => {
        setFormModalParams({ visible: false });
      },
    });
  };

  // 积分设置
  const handleSet = () => {
    setPointSetParams({
      visible: true,
      onCancel: () => {
        setPointSetParams({ visible: false });
      },
    });
  };

  // 导出
  const handleExport = () => {
    // formForm.validateFields().then((formData) => {
    //   setLoading(true);
    //   if (formData.createTime) {
    //     formData.startCreateTime = moment(formData.createTime[0]._d).format(
    //       "YYYY-MM-DD 00:00:00"
    //     );
    //     formData.endCreateTime = moment(formData.createTime[1]._d).format(
    //       "YYYY-MM-DD 23:59:59"
    //     );
    //     delete formData.createTime;
    //   }
    //   if (formData.updateTime) {
    //     formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
    //       "YYYY-MM-DD 00:00:00"
    //     );
    //     formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
    //       "YYYY-MM-DD 23:59:59"
    //     );
    //     delete formData.updateTime;
    //   }
    //   const data = {
    //     ...formData,
    //   };
    //   apiCall("/activity/dynamicCode/export", "POST", data, null, {
    //     isExit: true,
    //     title: `积分申请记录.${moment().format('YYYY-MM-DD')}.xlsx`,
    //   })
    //     .then((res) => {})
    //     .catch((err) => {
    //       console.log(err);
    //     })
    //     .finally(() => {
    //       setLoading(false);
    //     });
    // });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const userNameTreeDataProps = {
    treeData: employeeTreeData,
    treeCheckable: true,
    treeDefaultExpandedKeys: [""],
    allowClear: true,
    showArrow: true,
    showSearch: true,
    treeNodeFilterProp: "title",
    maxTagCount: 1,
    showCheckedStrategy: SHOW_PARENT,
    placeholder: "申请人",
  };

  const employeeTreeDataProps = {
    treeData: employeeTreeData,
    treeCheckable: true,
    treeDefaultExpandedKeys: [""],
    allowClear: true,
    showArrow: true,
    showSearch: true,
    treeNodeFilterProp: "title",
    maxTagCount: 1,
    showCheckedStrategy: SHOW_PARENT,
    placeholder: "审核人",
  };

  return (
    <div className="prizeList">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} form={formForm}>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="客户昵称、姓名" allowClear />
          </FormItem>
          <FormItem name="stateCode">
            <SysDictSelect placeholder="审核状态" dataset="REVIEWSTATE" />
          </FormItem>
          <FormItem name="applyEmployeeIdList">
            <TreeSelect {...userNameTreeDataProps} />
          </FormItem>
          <FormItem name="reviewEmployeeIdList">
            <TreeSelect {...employeeTreeDataProps} />
          </FormItem>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="审核备注" allowClear />
          </FormItem>
          <FormItem name="time" label="申请时间">
            <RangePicker />
          </FormItem>
          <FormItem name="reviewTime" label="审核时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button
              type="primary"
              onClick={() => {
                handleSet();
              }}
            >
              积分设置
            </Button>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <div>
          <InfoCircleOutlined
            style={{ fontSize: "14px", marginRight: "6px", color: "#c1c1c1" }}
          />
          客户的积分会随客户参与活动持续递增，客户最终的积分以实际为准。
        </div>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formModalParams} />
      <PointSetModal params={pointSetParams} />
    </div>
  );
};

export default withRouter(PointVerify);
