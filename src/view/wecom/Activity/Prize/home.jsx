/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/26 11:19
 * @LastEditTime: 2023/03/23 15:12
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\Prize\home.jsx
 * @Description: '奖品管理'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import Verify from './Verify/home';
import PrizeList from './List/home';
import PrizeRecords from './Records/home';

const { TabPane } = Tabs;

const Prize = (props) => {

  const [tabsIndex, setTabsIndex] = useState('');

  useEffect(() => {
    setTabsIndex(localStorage.getItem('prizeTabsIndex') || '1');
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('prizeTabsIndex', index);
    setTabsIndex(index);
  };

  return (
    <div className='prize'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="积分申请记录" key="1">
            <Verify />
          </TabPane>
          <TabPane tab="积分奖品管理" key="2">
            <PrizeList />
          </TabPane>
          <TabPane tab="积分奖品核销记录" key="3">
            <PrizeRecords />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Prize;
