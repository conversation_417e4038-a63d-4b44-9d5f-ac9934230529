/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/01/11 09:43
 * @LastEditTime: 2024/11/14 17:28
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/Collection/home.jsx
 * @Description: '活动集锦'
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import { timeStamp } from 'common/date';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  Form,
  Spin,
  Input,
  Button,
  Table,
  Card,
  Tooltip,
  Row,
  Col,
  Popover,
  message,
  Select,
  DatePicker,
  Avatar,
  Switch,
  Dropdown,
  Menu,
} from 'antd';
import { QrCodeBase } from 'common/qrcode';
import { saveAs } from 'file-saver';
import SetModal from '../comps/SetModal/home';
import ExportModal from '../comps/ExportModal/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SpaceModal from '../comps/SpaceModal/home';
import SimulateModal from '../comps/SimulateModal/home';
import SysDictSelect from 'components/select/SysDictSelect';
import WibotCopyBtn from 'components/WibotCopyBtn/home';
import OperateModal from 'components/Modal/OperateModal/index';
import './home.less';
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;

const Collection = (props) => {
  const { formForm } = props;
  const [collectData, setCollectData] = useState({});
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [infoData, setInfoData] = useState({});
  const [surveyData, setSurveyData] = useState({});
  const [formSetParams, setFormSetParams] = useState({ visible: false });
  const [exportParams, setExportParams] = useState({ visible: false });
  const [spaceParams, setSpaceParams] = useState({ visible: false });
  const [simulateParams, setSimulateParams] = useState({ visible: false });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '180px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? '#07c160' : '#f59a23' }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            {record.avatar && <Avatar size={40} src={record.avatar} />}
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: '客户负责人',
      width: '180px',
      dataIndex: 'adminEmployeeName',
      key: 'adminEmployeeName',
      align: 'center',
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: 'flex' }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              <div>
                {value}
                {record.adminFlag && '(管户)'}
              </div>
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ''
        );
      },
    },
    {
      title: '集锦发送开关',
      width: '160px',
      dataIndex: 'enable',
      key: 'enable',
      align: 'center',
      render: (value, record, index) => (
        <Switch
          checkedChildren="开启"
          unCheckedChildren="关闭"
          checked={value}
          onChange={(checked) => {
            onChangeSwitchStatus(checked, record);
          }}
        />
      ),
    },
    {
      title: '最近访问集锦时间',
      width: '160px',
      dataIndex: 'lastVisitCollectionTime',
      key: 'lastVisitCollectionTime',
      align: 'center',
      sorter: (a, b) =>
        timeStamp(a.lastVisitCollectionTime) -
        timeStamp(b.lastVisitCollectionTime),
    },
    {
      title: '最近访问问卷时间',
      width: '160px',
      dataIndex: 'lastVisitTime',
      key: 'lastVisitTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.lastVisitTime) - timeStamp(b.lastVisitTime),
    },
    {
      title: '最近提交问卷时间',
      width: '160px',
      dataIndex: 'lastCommitTime',
      key: 'lastCommitTime',
      align: 'center',
      sorter: (a, b) =>
        timeStamp(a.lastCommitTime) - timeStamp(b.lastCommitTime),
    },
    {
      title: '题目1(多选)',
      width: '160px',
      dataIndex: 'collectionOptionVO',
      key: 'collectionOptionVO',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const content = (
          <div>
            {value?.map((item, idx) => (
              <span key={idx}>
                {item.name}
                {idx + 1 != value.length && '、'}
              </span>
            )) || ''}
          </div>
        );
        return value?.length > 0 ? (
          <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {
              <Popover placement="topLeft" content={content}>
                {content}
              </Popover>
            }
          </div>
        ) : (
          ''
        );
      },
    },
    {
      title: '题目2(单选)',
      width: '160px',
      dataIndex: 'frequency',
      key: 'frequency',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        let content = '';
        switch (value.frequency) {
          case 'MONTH':
            content = `按月（每月${value.day}号）`;
            break;
          case 'SEASON':
            content = `按季（每季的首月${value.day}号）`;
            break;
          // case 'EVERYDAY':
          //   content = '我想要得到实时订阅推送';
          //   break;
          case 'NEVER':
            content = '不接收';
            break;
        }
        return (
          <Tooltip placement="topLeft" title={content}>
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: '题目3(单选)',
      width: '160px',
      dataIndex: 'immediateFlag',
      key: 'immediateFlag',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? '是' : '否'}>
          {value ? '是' : '否'}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { collectData } = props;
    const id = collectData.surveyId;
    if (id) {
      init(id);
    } else {
      setDataSource([]);
      setPaginations(false);
    }
    getSurveyData(collectData.id);
    setCollectData(collectData ?? {});
  }, [props.collectData]);
  usePageCacheLifeCycle({
    onShow() {
      const {collectData} = props;
      const id = collectData.surveyId;
      if (id) {
        init(id);
      } else {
        setDataSource([]);
        setPaginations(false);
      }
    }
  })

  const init = async (id) => {
    await getQuestionInfo(id);
    await fetchList({ surveyId: id });
  };

  const getQuestionInfo = async (id) => {
    setLoading(true);
    await apiCall(`/activity/survey/${id}`, 'GET')
      .then((res) => {
        setInfoData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getSurveyData = (id) => {
    setLoading(true);
    apiCall(`/activity/survey/data?collectId=${id}`, 'GET')
      .then((res) => {
        setSurveyData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { surveyId, pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formForm.validateFields().then((formData) => {
      if (formData.visitTime) {
        formData.minVisitDate = moment(formData.visitTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxVisitDate = moment(formData.visitTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.visitTime;
      }
      if (formData.time) {
        formData.minCommitDate = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCommitDate = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      if (formData.collectionTime) {
        formData.minVisitResourceCollectionDate = moment(
          formData.collectionTime[0]._d
        ).format('YYYY-MM-DD 00:00:00');
        formData.maxVisitResourceCollectionDate = moment(
          formData.collectionTime[1]._d
        ).format('YYYY-MM-DD 23:59:59');
        delete formData.collectionTime;
      }
      formData.adminDeptList = formData.adminDeptList?.join(',') || null;
      formData.frequency = formData.frequency?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall(
        `/activity/surveyFeedback?surveyId=${surveyId || collectData.surveyId}`,
        'GET',
        data
      )
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 集锦发送开关
  const onChangeSwitchStatus = (checked, record) => {
    const data = {
      enable: checked,
    };
    apiCall(`/activity/surveyFeedback/${record.id}`, 'PUT', data)
      .then((res) => {
        message.success('修改成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleQuery = () => {
    if (!collectData.surveyId) {
      message.error('查询失败，请发布问卷后再查询');
      return false;
    }
    fetchList();
  };

  const handleReset = () => {
    if (!collectData.surveyId) {
      message.error('查询失败，请发布问卷后再查询');
      return false;
    }
    formForm.resetFields();
    fetchList();
  };

  const handleDownload = () => {
    saveAs(QrCodeBase({ url: infoData.transitUrl }), '问卷二维码');
  };

  // 处理批量操作
  const handleSend = (type) => {
    const params = {
      idList: selectedRowKeys.length > 0 ? selectedRowKeys : [],
      enable: type == 'open',
    };
    let obj = {
      surveyId: collectData.surveyId,
      ...formForm.getFieldsValue(),
    };
    if (!selectedRowKeys.length) {
      if (obj.visitTime) {
        obj.minVisitDate = moment(obj.visitTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        obj.maxVisitDate = moment(obj.visitTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete obj.visitTime;
      }
      if (obj.time) {
        obj.minCommitDate = moment(obj.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        obj.maxCommitDate = moment(obj.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete obj.time;
      }
      if (obj.collectionTime) {
        obj.minVisitResourceCollectionDate = moment(
          obj.collectionTime[0]._d
        ).format('YYYY-MM-DD 00:00:00');
        obj.maxVisitResourceCollectionDate = moment(
          obj.collectionTime[1]._d
        ).format('YYYY-MM-DD 23:59:59');
        delete obj.collectionTime;
      }
      params.dto = obj;
    }
    if (type == 'open') {
      if (selectedRowKeys.length > 0) {
        apiCall('/activity/surveyFeedback/enable', 'PUT', params)
          .then((res) => {
            message.success('批量开启集锦发送成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => { });
        return false;
      }
      setOperateParams({
        visible: true,
        title: '批量开启集锦发送确认',
        content: `${paginations.total > 2000
          ? `当前查询结果${paginations.total}行已超出批量操作【开启集锦发送】的限制2000行，请调整筛选条件后再试！`
          : `即将为当前查询结果的${paginations.total}行数据进行批量操作【开启集锦发送】，确定这样做吗？`
          }`,
        onSubmit: () => {
          if (paginations.total > 2000) {
            setOperateParams({
              visible: false,
            });
          } else {
            apiCall('/activity/surveyFeedback/enable', 'PUT', params)
              .then((res) => {
                message.success('批量开启集锦发送成功！');
                fetchList();
              })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                setOperateParams({
                  visible: false,
                });
              });
          }
        },
        onCancel: () => {
          setOperateParams({
            visible: false,
          });
        },
      });
    } else {
      if (selectedRowKeys.length > 0) {
        apiCall('/activity/surveyFeedback/enable', 'PUT', params)
          .then((res) => {
            message.success('批量关闭集锦发送成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => { });
        return false;
      }
      setOperateParams({
        visible: true,
        title: '批量关闭集锦发送确认',
        content: `${paginations.total > 2000
          ? `当前查询结果${paginations.total}行已超出批量操作【关闭集锦发送】的限制2000行，请调整筛选条件后再试！`
          : `即将为当前查询结果的${paginations.total}行数据进行批量操作【关闭集锦发送】，确定这样做吗？`
          }`,
        onSubmit: () => {
          if (paginations.total > 2000) {
            setOperateParams({
              visible: false,
            });
          } else {
            apiCall('/activity/surveyFeedback/enable', 'PUT', params)
              .then((res) => {
                message.success('批量关闭集锦发送成功！');
                fetchList();
              })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                setOperateParams({
                  visible: false,
                });
              });
          }
        },
        onCancel: () => {
          setOperateParams({
            visible: false,
          });
        },
      });
    }
  };

  // 集锦访问模拟
  const handleSimulate = () => {
    setSimulateParams({
      visible: true,
      surveyId: collectData.surveyId,
      collectId: collectData.id,
      name: collectData.name,
      onCancel: () => {
        setSimulateParams({ visible: false });
      },
    });
  };

  // 集锦广告位
  const handleSpace = () => {
    setSpaceParams({
      visible: true,
      collectId: collectData.id,
      onCancel: () => {
        setSpaceParams({ visible: false });
      },
    });
  };
  // 集锦设置
  const handleSet = () => {
    setFormSetParams({
      visible: true,
      collectId: collectData.id,
      onSubmit: (params) => {
        apiCall('/employeeTaskBatch/collection_setting', 'POST', params)
          .then((res) => {
            message.success('设置成功！');
            // fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setFormSetParams({ visible: false });
      },
      onCancel: () => {
        setFormSetParams({ visible: false });
      },
    });
  };

  const handleQuestionnaire = () => {
    props.history.push({
      pathname: '/wecom/resourceCollection/question',
      search: collectData.surveyId
        ? `?id=${collectData.id}&surveyId=${collectData.surveyId}`
        : `?id=${collectData.id}`,
    });
  };

  const handleMaintain = () => {
    props.history.push({
      pathname: '/wecom/resourceCollection/maintain',
      search: collectData.surveyId
        ? `?id=${collectData.id}&surveyId=${collectData.surveyId}`
        : `?id=${collectData.id}`,
    });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: '/wecom/customer/details',
      search: `?id=${customerId}`,
    });
  };

  // 导出
  const handleExport = () => {
    setExportParams({
      visible: true,
      collectId: collectData.id,
      onCancel: () => {
        setExportParams({ visible: false });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const onSelectChange = (keys, rows, info) => {
    setSelectedRowKeys(keys);
    setSelectedRows(rows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    // selections: [
    //   Table.SELECTION_ALL,
    //   Table.SELECTION_INVERT,
    //   Table.SELECTION_NONE,
    // ],
  };

  const locales = {
    selectionAll: '选择全部',
    selectInvert: '反选本页',
    selectNone: '取消全部',
  };

  return (
    <div className="Collection">
      <Spin spinning={loading}>
        <Row gutter={16} className="statistical">
          <Col xs={24} lg={8}>
            <div className="statistical-item">
              <h2 className="item-title">集锦数据</h2>
              <Row>
                <Col span={12} className="item-box">
                  <div className="nums">{surveyData.collectionVisitCount}</div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计访问次数
                  </span>
                  <Tooltip title="同一用户可访问多次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日访问：{surveyData.collectionVisitCountToday}
                  </div>
                </Col>
                <Col span={12} className="item-box">
                  <div className="nums">
                    {surveyData.collectionVisitCustomerCount}
                  </div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计访问人数
                  </span>
                  <Tooltip title="同一用户访问多次算一次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日访问：{surveyData.collectionVisitCustomerCountToday}
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
          <Col xs={24} lg={16}>
            <div className="statistical-item">
              <h2 className="item-title">问卷数据</h2>
              <Row>
                <Col span={6} className="item-box">
                  <div className="nums">{surveyData.surveyVisitCount}</div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计访问次数
                  </span>
                  <Tooltip title="同一用户可访问多次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日访问：{surveyData.surveyVisitCountToday}
                  </div>
                </Col>
                <Col span={6} className="item-box">
                  <div className="nums">
                    {surveyData.surveyVisitCustomerCount}
                  </div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计访问人数
                  </span>
                  <Tooltip title="同一用户访问多次算一次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日访问：{surveyData.surveyVisitCustomerCountToday}
                  </div>
                </Col>
                <Col span={6} className="item-box">
                  <div className="nums">{surveyData.surveyCommitCount}</div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计提交次数
                  </span>
                  <Tooltip title="同一用户可提交多次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日提交：{surveyData.surveyCommitCountToday}
                  </div>
                </Col>
                <Col span={6} className="item-box">
                  <div className="nums">
                    {surveyData.surveyCommitCustomerCount}
                  </div>
                  <span
                    className="statistical_text"
                    style={{ marginRight: '5px' }}
                  >
                    累计提交人数
                  </span>
                  <Tooltip title="同一用户提交多次算一次">
                    <QuestionCircleOutlined />
                  </Tooltip>
                  <div className="statistical_text">
                    今日提交：{surveyData.surveyCommitCustomerCountToday}
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
        <FilterBar>
          <Form layout={'inline'} form={formForm}>
            <FormItem
              name="customerName"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="客户名称" allowClear />
            </FormItem>
            <FormItem
              name="adminDeptList"
              style={{
                minWidth: 'unset',
                maxWidth: '200px',
                marginRight: '0px',
              }}
            >
              {/* <Input placeholder="管户员工/负责人" allowClear /> */}
              <ETypeTransferModal title="客户负责人" />
            </FormItem>
            <FormItem name="enable">
              <Select placeholder="集锦发送开关" allowClear>
                <Option value>开启</Option>
                <Option value={false}>关闭</Option>
              </Select>
            </FormItem>
            <FormItem name="frequency">
              <SysDictSelect
                placeholder="集锦接收频率"
                dataset="FEEDBACK_FREQUENCY"
                mode="multiple"
                maxTagCount="responsive"
              />
            </FormItem>
            <FormItem name="visitTime" label="最近访问问卷时间">
              <RangePicker />
            </FormItem>
            <FormItem name="time" label="最近提交问卷时间">
              <RangePicker />
            </FormItem>
            <FormItem name="collectionTime" label="最近访问集锦时间">
              <RangePicker />
            </FormItem>
          </Form>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
              <Button type="primary" onClick={() => handleExport()}>
                {' '}
                导出{' '}
              </Button>
              {collectData.surveyId && (
                <Dropdown
                  arrow
                  overlay={
                    <Menu>
                      <Menu.Item>
                        <WibotCopyBtn
                          text={infoData.transitUrl}
                          title="复制问卷链接"
                        />
                      </Menu.Item>
                      <Menu.Item>
                        <a onClick={() => handleDownload()}>下载问卷二维码</a>
                      </Menu.Item>
                    </Menu>
                  }
                >
                  <Button type="primary">分享问卷</Button>
                </Dropdown>
              )}
            </div>
            <div>
              <Dropdown
                overlay={
                  <Menu>
                    <div
                      style={{
                        color: '#AAAAAA',
                        padding: '5px',
                        borderBottom: '1px solid #e5e5e5',
                      }}
                    >
                      {selectedRowKeys.length > 0 ? (
                        <>
                          操作已选的
                          <span style={{ color: '#fd0100' }}>
                            &nbsp;{selectedRowKeys.length}&nbsp;
                          </span>
                          行数据
                        </>
                      ) : (
                        <>
                          操作查询结果的
                          <span style={{ color: '#fd0100' }}>
                            &nbsp;{paginations.total || 0}&nbsp;
                          </span>
                          行数据
                        </>
                      )}
                    </div>
                    <Menu.Item
                      key="0"
                      onClick={() => {
                        handleSend('open');
                      }}
                      style={{ textAlign: 'center' }}
                    >
                      开启集锦发送
                    </Menu.Item>
                    <Menu.Item
                      key="1"
                      onClick={() => {
                        handleSend('close');
                      }}
                      style={{ textAlign: 'center' }}
                    >
                      关闭集锦发送
                    </Menu.Item>
                  </Menu>
                }
                placement="bottomLeft"
                arrow
              >
                <Button type="primary">批量操作</Button>
              </Dropdown>
              {collectData.surveyId && (
                <Button type="primary" onClick={() => handleSet()}>
                  群发集锦设置
                </Button>
              )}
              {collectData.surveyId && (
                <Button type="primary" onClick={() => handleQuestionnaire()}>
                  群发问卷
                </Button>
              )}
              <Button type="primary" onClick={() => handleMaintain()}>
                问卷维护
              </Button>
              {collectData.surveyId && (
                <div style={{ marginTop: '20px' }}>
                  <Button type="primary" onClick={() => handleSimulate()}>
                    集锦访问模拟
                  </Button>
                  <Button type="primary" onClick={() => handleSpace()}>
                    集锦页广告位
                  </Button>
                  {/* {collectData.surveyId && (
                  <Button type="primary" onClick={() => handleSimulate()}>
                    集锦访问模拟
                  </Button>
                )}
                {collectData.surveyId && (
                  <Button type="primary" onClick={() => handleSpace()}>
                    集锦页广告位
                  </Button>
                )} */}
                </div>
              )}
            </div>
          </div>
        </FilterBar>
        <Card bordered={false}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
            rowSelection={rowSelection}
          // locale={locales}
          />
        </Card>
      </Spin>
      <SetModal params={formSetParams} />
      <ExportModal params={exportParams} />
      <SpaceModal params={spaceParams} />
      <SimulateModal params={simulateParams} />
      <OperateModal params={operateParams} />
    </div>
  );
};

export default withRouter(Collection);
