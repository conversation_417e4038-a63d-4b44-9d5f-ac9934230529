/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/14 15:47
 * @LastEditTime: 2024/10/22 09:29
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/comps/ExportModal/home.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  DatePicker,
  Button,
  Image,
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const { collectId } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState('1');

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startTime = moment(formData.date[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.date[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.date;
      }
      const data = {
        collectId,
        ...formData,
      };

      let apiUrl = '/activity/collection/exportCustomerFeedbackSummaryVO';
      let title = '客户问卷反馈汇总';
      if (TabsActiveKey == '2') {
        apiUrl = '/activity/collection/exportCustomerVisitCollectSummaryVO';
        title = '客户问卷反馈明细';
      } else if (TabsActiveKey == '3') {
        apiUrl = '/activity/collection/exportCustomerVisitCollectDetailVO';
        title = '客户集锦访问明细';
      } else if (TabsActiveKey == '4') {
        apiUrl = '/activity/collection/exportCustomerFeedbackListDetailVO';
        title = '客户反馈明细';
      }
      apiCall(apiUrl, 'POST', data, null, {
        isExit: true,
        title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success('导出成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="CollectionExportModal"
      visible={visible}
      title="导出集锦数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs
            defaultActiveKey={TabsActiveKey}
            onChange={onChangeTabs}
            destroyInactiveTabPane
          >
            <Tabs.TabPane tab="客户问卷反馈汇总" key="1">
              <p className="tips">
                根据客户提交问卷的时间段，汇总统计每个统计对象名下客户的反馈情况
              </p>
              <FormItem
                label="反馈日期"
                name="date"
                rules={[{ required: true, message: '请选择反馈日期' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" onlyDepartment />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="客户问卷反馈明细" key="2">
              <p className="tips">
                根据客户提交问卷的时间段，统计每个客户的问卷反馈明细，单客户多次反馈算多条记录
              </p>
              <FormItem
                label="反馈日期"
                name="date"
                rules={[{ required: true, message: '请选择反馈日期' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" onlyDepartment />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="客户集锦访问明细" key="3">
              <p className="tips">
                统计该时间段内每个客户访问集锦的记录，单客户多次反馈算多条记录
              </p>
              <FormItem
                label="访问时间"
                name="date"
                rules={[{ required: true, message: '请选择访问时间' }]}
              >
                <RangePicker />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="客户反馈明细" key="4">
              <p className="tips">
                导出当前活动集锦的所有客户反馈
              </p>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
