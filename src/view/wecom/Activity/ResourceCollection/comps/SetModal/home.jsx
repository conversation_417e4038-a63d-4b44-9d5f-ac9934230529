/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/20 11:28
 * @LastEditTime: 2024/10/22 11:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/comps/SetModal/home.jsx
 * @Description: '群发集锦设置'
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Form,
  Input,
  Typography,
  TimePicker,
  Upload,
  Row,
  Col,
  Spin,
  InputNumber,
  Switch,
} from 'antd';
import { normFile } from 'common/regular';
import { apiCall } from 'common/utils';
import moment from 'moment';
import WibotEmoji from "components/WibotEmoji/home";
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import WibotUploadImage from 'components/WibotUploadImage/home';
import './home.less';

const FormItem = Form.Item;
const { Paragraph } = Typography;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 18 },
};

const SetModal = (props) => {
  const { visible, collectId, onSubmit, onCancel } = props.params;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [textContent, setTextContent] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [autoValue, setAutoValue] = useState(true);
  const [taskJsonData, setTaskJsonData] = useState({});

  useEffect(() => {
    if (visible) {
      fetchList();
    }
  }, [visible]);

  const fetchList = async () => {
    setLoading(true);
    await apiCall(
      `/employeeTaskBatch/collection_setting?collectId=${collectId}`,
      'GET'
    )
      .then((res) => {
        if (res) {
          const { id, enable, taskJson, timeSetting } = res;
          const text = taskJson ? taskJson.messages[0]?.content : '';
          const obj = taskJson
            ? {
              remark: taskJson.remark,
              news: taskJson.messages[1]?.description,
              fileId: taskJson.messages[1]?.fileId,
              planTime: [
                moment(timeSetting?.startTime, 'HH:mm'),
                moment(timeSetting?.endTime, 'HH:mm'),
              ],
              days: timeSetting?.days,
            }
            : {};
          formForm.setFieldsValue({
            enable,
            text,
            ...obj,
          });
          setTextContent(text);
          setImageUrl(taskJson ? taskJson.messages[1]?.fileId[0] || '' : '');
          setId(id);
          setAutoValue(enable);
          setTaskJsonData(res);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 文案改变
  const handleTextChange = (e) => {
    setTextContent(e.target.value);
  };

  // 添加客户昵称
  const handleAddFocusContent = async () => {
    const insertHTML = '#客户昵称#';
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      textContent.substring(0, startPos) +
      insertHTML +
      textContent.substring(endPos);
    setTextContent(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native;
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      textContent.substring(0, startPos) +
      insertEmoji +
      textContent.substring(endPos);
    setTextContent(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    );
  };

  const onChangeUpload = (info) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
  };

  const customRequest = (config) => {
    const File = config.file;
    const options = {
      width: 500,
      height: 500,
      quality: 0.9,
    };
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append('file', base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data)
        .then((res) => {
          const { fileId, fileUrl } = res;
          setImageUrl(fileUrl);
          formForm.setFieldsValue({
            fileId: [fileId],
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setUploadLoading(false);
        });
    });
  };

  const handleResetUpload = (e) => {
    e.preventDefault(); // 阻止浏览器默认行为
    e.stopPropagation(); // 阻止事件冒泡
    setImageUrl('');
    formForm.setFieldsValue({
      fileId: '',
    });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      const { enable, planTime, remark, news, fileId, days } = formData;
      const data = enable
        ? {
          id,
          collectId,
          enable,
          taskJson: {
            collectId,
            subTypeSourceId: collectId,
            type: 'CUSTOMER',
            subType: 'RESOURCE_COLLECTION',
            status: '1',
            targetType: 'CUSTOMER',
            name: '群发集锦',
            scope: 'PART',
            remark,
            messages: [
              {
                content: textContent,
                type: 'copyWriter',
              },
              {
                title: '活动集锦【日期】',
                description: news,
                fileId,
                type: 'RESOURCE_COLLECTION',
              },
            ],
          },
          timeSetting: {
            startTime: moment(planTime[0]._d).format('HH:mm'), // moment(planTime, 'HH:mm')
            endTime: moment(planTime[1]._d).format('HH:mm'),
            days,
          },
        }
        : {
          ...taskJsonData,
          id,
          collectId,
          enable,
        };
      onSubmit?.(data);
      formForm.resetFields();
      setTextContent('');
      setImageUrl('');
    });
  };

  const handleCancel = () => {
    formForm.resetFields();
    onCancel?.();
    setTextContent('');
    setImageUrl('');
  };

  return (
    <Modal
      visible={visible}
      width={600}
      title="群发集锦设置（群发客户任务）"
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={handleCancel}
      // onOk={onOk}
      footer={
        <div className="SetModal_footer">
          {/* <div className="SetModal_footer_text">
            如任务时间早于当前时间，保存时会立即创建一次群发集锦
          </div> */}
          <div>
            <Button type="primary" onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" onClick={onOk}>
              保存
            </Button>
          </div>
        </div>
      }
      className="SetModal"
    >
      <Spin spinning={loading}>
        <Form {...layout} form={formForm}>
          <FormItem
            name="enable"
            label="自动群发集锦"
            valuePropName="checked"
            extra="开启后，系统将自动在指定时间创建发送集锦的群发任务"
            rules={[{ required: true, message: '请选择自动群发集锦' }]}
            initialValue
          >
            <Switch
              onChange={(checked) => {
                setAutoValue(checked);
              }}
            />
          </FormItem>

          {autoValue && (
            <>
              <FormItem
                label="任务时间"
                required
                extra="如任务时间早于当前时间，保存时会立即创建一次群发集锦"
              >
                <div className="flex-wrap">
                  <span style={{ margin: '5px 5px 0 0px' }}>每天</span>
                  <FormItem
                    name="planTime"
                    rules={[{ required: true, message: '请选择任务时间' }]}
                    style={{ marginBottom: '0px' }}
                  >
                    <TimePicker.RangePicker format={'HH:mm'} showTime />
                  </FormItem>
                </div>
              </FormItem>
              <FormItem
                label="任务有效期"
                required
                extra="如员工有效期内未群发，则每天提醒一次直到有效期结束"
                style={{ marginBottom: '10px' }}
              >
                <div className="flex-wrap">
                  <FormItem
                    name="days"
                    rules={[{ required: true, message: '请输入任务有效期' }]}
                    style={{ marginBottom: '0px' }}
                  >
                    <InputNumber min={0} precision={0} />
                  </FormItem>
                  <span style={{ margin: '5px' }}>天</span>
                </div>
              </FormItem>
              <FormItem label="任务对象">
                <span>
                  部分客户（当天所有符合条件的客户，由客户负责人发送）
                </span>
              </FormItem>
              <FormItem
                name="remark"
                label="任务说明"
                rules={[{ required: true, message: '请输入任务说明' }]}
              >
                <TextArea
                  placeholder="请输入任务说明"
                  allowClear
                  autoSize={{ minRows: 2, maxRows: 7 }}
                />
              </FormItem>
              <FormItem label="群发内容" style={{ marginBottom: '0px' }}>
                <div className="mass-box">
                  <div className="mass_item" style={{ marginBottom: '10px' }}>
                    <span className="num">1</span>
                    <FormItem
                      name="text"
                      label="文案"
                      style={{ width: '90%' }}
                      className="text_formItem"
                    >
                      <div className="textarea-emoji">
                        <TextArea
                          id="msgTextInput"
                          placeholder="请输入文案（100字）"
                          value={textContent}
                          allowClear
                          showCount
                          maxLength={1200}
                          autoSize={{ minRows: 2, maxRows: 7 }}
                          onChange={handleTextChange}
                          className="textArea-mid"
                        />

                        <WibotEmoji
                          iconClassName="textarea-emoji_icon"
                          onEmojiSelect={handleEmojiSelect}
                        />
                      </div>
                    </FormItem>
                  </div>
                  <div className="mass_item">
                    <span className="num">2</span>
                    <FormItem
                      name="text"
                      label="集锦"
                      required
                      style={{ width: '100%', flex: 1, marginBottom: '0px' }}
                    >
                      <div className="link-card" style={{ maxWidth: '350px' }}>
                        <Paragraph
                          strong
                          ellipsis={{ rows: 2, tooltip: true }}
                          style={{ marginBottom: '10px' }}
                        >
                          最新活动集锦
                        </Paragraph>
                        <Row justify="space-between" gutter={16}>
                          <Col span={14}>
                            <FormItem
                              name="news"
                              rules={[
                                { required: true, message: '请输入集锦描述' },
                              ]}
                            >
                              <TextArea
                                placeholder="为您提供丰富有趣的集锦新闻"
                                allowClear
                                showCount
                                maxLength={100}
                                autoSize={{ minRows: 2, maxRows: 7 }}
                                className="textArea-mid"
                              />
                            </FormItem>
                          </Col>
                          <Col span={10}>
                            <FormItem
                              name="fileId"
                              valuePropName="imgUrl"
                              getValueFromEvent={normFile}
                              rules={[
                                { required: true, message: '请选择集锦图片' },
                              ]}
                            >
                              <Upload
                                name="file"
                                customRequest={customRequest}
                                listType="picture-card"

                                showUploadList={false}
                                beforeUpload={beforeUpload}
                                onChange={onChangeUpload}
                              >
                                <WibotUploadImage
                                  imageUrl={imageUrl}
                                  loading={uploadLoading}
                                  onClose={handleResetUpload}
                                />
                              </Upload>
                            </FormItem>
                          </Col>
                        </Row>
                      </div>
                    </FormItem>
                  </div>
                </div>
              </FormItem>
            </>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default SetModal;
