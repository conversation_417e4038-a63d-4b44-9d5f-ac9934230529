.SetModal {
  .flex-wrap {
    display: flex;
    flex-direction: row;
  }

  .mass-box {
    .mass_item {
      display: flex;
      flex-direction: row;

      .num {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        margin-right: 10px;
        margin-top: 5px;
      }

      .textarea-emoji {
        position: relative;

        .textarea-emoji_icon {
          position: absolute;
          bottom: -20px;
          right: 90px;
          font-size: 16px;
          color: #6f7072;
          cursor: pointer;
          -webkit-font-smoothing: antialiased;

          &:hover {
            color: #333;
          }
        }
      }

      .link-card {
        width: 100%;

        .textArea-mid {
          textarea {
            height: 100px !important;
          }
        }
      }

      .text_formItem {
        .ant-form-item-control {
          margin-left: 10px;
        }
      }
    }
  }

  .textArea-mid {
    textarea {
      height: 100px !important;
    }
  }

  .SetModal_footer {
    display: flex;
    flex-direction: row;
    // justify-content: space-between;
    justify-content: flex-end;

    .SetModal_footer_text {
      width: 390px;
      color: #7F7F7F;
      text-align: left;
    }
  }
}