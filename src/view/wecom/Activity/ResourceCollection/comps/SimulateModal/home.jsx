/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/15 10:03
 * @LastEditTime: 2025/07/04 14:10
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/comps/SimulateModal/home.jsx
 * @Description: '集锦访问模拟'
 */

import React, { useState, useEffect } from "react"
import {
  Modal,
  Spin,
  Button,
  Form,
  Switch,
  Radio,
  Row,
  Col,
  Tag,
  Select,
  Carousel,
  Typography,
  Image,
} from "antd"
import { CheckOutlined, PlusOutlined } from "@ant-design/icons"
import { apiCall } from "common/utils"
import CTypeTransferModal from "components/TransferModal/CustomerType/home"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import moment from "moment"
import { FileHOC } from "components/FileHOC/FileHOC"
import { formatURL } from "config"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"

const FormItem = Form.Item
const { Paragraph } = Typography
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
}

const SimulateModal = (props) => {
  const { visible, name, surveyId, collectId, onCancel } = props.params
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  })
  const [selectType, setSelectType] = useState("PARAM")
  const [collectTagOption, setCollectTagOption] = useState([])
  const [checkData, setCheckData] = useState([])
  const [collectionData, setCollectionData] = useState({})
  const scrollListData = [
    {
      title: "参数模拟",
      describe: "根据集锦设置的参数来模拟客户访问时的集锦内容",
      type: "PARAM",
    },
    {
      title: "客户模拟",
      describe:
        "以客户实际提交的集锦问卷反馈的参数来模拟该客户访问时的集锦内容",
      type: "CUSTOMER",
    },
  ]
  const [linkInfo, setLinkInfo] = useState(null)
  const [listData, setListData] = useState([])
  const [topBanners, setTopBanners] = useState([])
  const [lisbottomBanners, setBottomBanners] = useState([])

  useEffect(() => {
    setLinkInfo(collectionData.link)
    setListData(collectionData.list || [])
    if (collectionData.bannerList) {
      setTopBanners(
        collectionData.bannerList?.filter((item) => item.type == "TOP")
      )
      setBottomBanners(
        collectionData.bannerList?.filter((item) => item.type == "BOTTOM")
      )
    } else {
      setTopBanners([])
      setBottomBanners([])
    }
  }, [collectionData])

  useEffect(() => {
    if (visible) {
      getCollectTagOption()
    }
  }, [visible])

  // 获取集锦标签
  const getCollectTagOption = async () => {
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCollectTagOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = async () => {
    formForm.validateFields().then((formData) => {
      setLoading(true)
      formData.frequency = {
        frequency: formData.frequency,
        day: 0,
      }
      const data = {
        ...formData,
      }
      const apiUrl =
        selectType == "PARAM"
          ? `/info/resourceCollection?collectId=${collectId}`
          : `/info/resourceCollection?collectId=${collectId}&customerId=${formData.customerId}`
      apiCall(apiUrl, "POST", selectType == "PARAM" ? data : {})
        .then((res) => {
          if (res) {
            setCollectionData(res)
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  // 获得当前客户填写的问卷反馈
  const getCustomerSurveyFeedback = (customerId) => {
    setLoading(true)
    apiCall(
      `/info/resourceCollection/getCustomerSurveyFeedback?surveyId=${surveyId}&customerId=${customerId}`,
      "GET"
    )
      .then((res) => {
        const { collectionOption, frequency, immediateFlag } = res
        formForm.setFieldsValue({
          collectionOption,
          frequency: frequency ? frequency.frequency : "",
          immediateFlag,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 切换模拟类型
  const handleScrollItem = (item) => {
    if (id) {
      return
    }
    setSelectType(item.type)
    setCheckData([])
    setCollectionData({})
    formForm.resetFields()
    // formForm.setFieldsValue({
    //   sendObjectType: 1,
    // });
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleCancel = () => {
    formForm.resetFields()
    setSelectType("PARAM")
    setCheckData([])
    setCollectionData({})
    onCancel?.()
  }

  return (
    <Modal
      visible={visible}
      width={1000}
      title={
        <div className="simulate-flex">
          <h3>集锦访问模拟</h3>
          <span className="tips">
            本次模拟将以集锦「{name}」关联的昨天已上架的资源进行实时模拟
          </span>
        </div>
      }
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={handleCancel}
      // onOk={onOk}
      footer={null}
      className="SimulateModal"
    >
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <ul className="scrollList">
              {scrollListData.map((item, index) => (
                <li
                  key={index}
                  className={
                    selectType == item.type ? "listItem activeItem" : "listItem"
                  }
                  style={{
                    background: `${
                      selectType != item.type && id ? "#c1c1c1" : "unset"
                    }`,
                    cursor: `${
                      selectType != item.type && id ? "unset" : "pointer"
                    }`,
                  }}
                  onClick={() => handleScrollItem(item)}
                >
                  <h2>{item.title}</h2>
                  <p>{item.describe}</p>
                  <div className="active">
                    <CheckOutlined />
                  </div>
                </li>
              ))}
            </ul>
            <Form {...layout} form={formForm}>
              {selectType == "CUSTOMER" && (
                <FormItem
                  label="问卷客户"
                  name="customerId"
                  rules={[{ required: true, message: "请选择问卷客户" }]}
                  extra={
                    <>
                      <span>请选择一位已填写问卷且开启推送的客户</span>
                      {checkData.length > 0 && (
                        <div>
                          {checkData.map((item) => (
                            <Tag
                              closable
                              style={{ marginTop: "8px" }}
                              key={item.id}
                              onClose={(e) => {
                                e.preventDefault()
                                console.log(e)
                                const list = checkData.filter(
                                  (atem) => atem.id != item.id
                                )
                                setCheckData(list)
                                setCollectionData({})
                                formForm.resetFields()
                                // formForm.setFieldsValue({
                                //   customerId:
                                //     list.length > 0 ? list[0].id : "",
                                // });
                              }}
                            >
                              {item.name}
                            </Tag>
                          ))}
                        </div>
                      )}
                    </>
                  }
                >
                  <Button
                    style={{ marginLeft: "8px" }}
                    icon={<PlusOutlined />}
                    type="primary"
                    onClick={() => {
                      setCTypeTransferParams({
                        visible: true,
                        type: "other",
                        multiple: true,
                        checkList: checkData,
                        paramsInfo: {
                          title: "客户",
                          placeholder: "请输入客户名称",
                          apiUrl: "/info/resourceCollection/popup/customer",
                          params: { surveyId },
                          fieldNames: { title: "name", key: "id" },
                        },
                        onSubmit: (data) => {
                          setCheckData(data)
                          if (data.length > 0) {
                            getCustomerSurveyFeedback(data[0].id)
                          }
                          formForm.setFieldsValue({
                            customerId: data.length > 0 ? data[0].id : "",
                          })
                          setCTypeTransferParams({ visible: false })
                        },
                        onCancel: () => {
                          setCTypeTransferParams({ visible: false })
                        },
                      })
                    }}
                  >
                    选择客户
                  </Button>
                </FormItem>
              )}
              <FormItem name="collectionOption" label="集锦偏好">
                <Select
                  placeholder="集锦标签"
                  fieldNames={{ label: "name", value: "id" }}
                  options={collectTagOption}
                  allowClear
                  showSearch
                  showArrow
                  mode="multiple"
                  maxTagCount="responsive"
                  style={{ width: "260px" }}
                  disabled={selectType == "CUSTOMER"}
                  filterOption={(input, option) =>
                    (option?.name ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </FormItem>
              <FormItem
                name="frequency"
                label="接收频率"
                initialValue={"MONTH"}
              >
                <Radio.Group disabled={selectType == "CUSTOMER"}>
                  {/* <Radio value={'EVERYDAY'}>实时接收</Radio> */}
                  <Radio value={"MONTH"}>按月接收</Radio>
                  <Radio value={"SEASON"}>按季接收</Radio>
                  <Radio value={"NEVER"}>不接收</Radio>
                </Radio.Group>
              </FormItem>
              <FormItem
                name="immediateFlag"
                label="营销推送"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch disabled={selectType == "CUSTOMER"} />
              </FormItem>

              <FormItem wrapperCol={{ offset: 8 }}>
                <Button type="primary" onClick={() => handleQuery()}>
                  查询
                </Button>
              </FormItem>
            </Form>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMobilePreview
              title={
                linkInfo?.title
                  ? linkInfo.title
                  : `活动集锦【${moment().format("YYYY-MM-DD")}】`
              }
            >
              {topBanners.length > 0 && (
                <Carousel autoplay>
                  {topBanners.map((item, index) => (
                    <div key={index}>
                      <FileHOC src={item.image || "error"}>
                        {(url) =>
                          item.url ? (
                            <a
                              target="_blank"
                              href={item.url}
                              className="carousel-item"
                              rel="noreferrer"
                            >
                              <Image
                                src={url}
                                fallback="images/fallbackImg.png"
                                preview={false}
                              />
                            </a>
                          ) : (
                            <div className="carousel-item">
                              <Image
                                src={url}
                                fallback="images/fallbackImg.png"
                                preview={false}
                              />
                            </div>
                          )
                        }
                      </FileHOC>
                    </div>
                  ))}
                </Carousel>
              )}

              {listData.length > 0 &&
                listData.map((item, index) => (
                  <div key={index}>
                    {(item.type == "Article" ||
                      item.type == "pageArticle" ||
                      item.type == "Product") && (
                      <div className="info-card">
                        <Paragraph
                          strong
                          ellipsis={{ rows: 2, tooltip: true }}
                          style={{ marginBottom: "5px" }}
                        >
                          {item.title}
                        </Paragraph>
                        <Row justify="space-between">
                          <Col span={15}>
                            <Paragraph
                              style={{ fontSize: "12px", lineHeight: "18px" }}
                              ellipsis={{ rows: 3, tooltip: true }}
                            >
                              {item.description}
                            </Paragraph>
                          </Col>
                          <Col>
                            <FileHOC
                              src={(item.images && item.images[0]) || "error"}
                            >
                              {(url) => (
                                <Image
                                  width={54}
                                  src={url}
                                  fallback="images/fallbackImg.png"
                                  preview={false}
                                />
                              )}
                            </FileHOC>
                          </Col>
                        </Row>
                        {item.collectTagList.length > 0 && (
                          <div className="text">
                            {item.collectTagList[0].name}
                          </div>
                        )}
                      </div>
                    )}

                    {item.type == "copyWriter" && (
                      <div className="info-card">
                        <WibotEditorView html={item.copyWriter} />
                        {item.collectTagList.length > 0 && (
                          <div className="text">
                            {item.collectTagList[0].name}
                          </div>
                        )}
                      </div>
                    )}

                    {(item.type == "Poster" ||
                      item.type == "POSTER_TOOL" ||
                      item.type == "Picture") && (
                      <div className="info-card">
                        <WibotEditorView html={item.script} />
                        {item.images?.map((item, index) => (
                          <FileHOC src={item || "error"}>
                            {(url) => (
                              <Image
                                key={index}
                                width={60}
                                height={60}
                                src={url}
                                fallback="images/fallbackImg.png"
                                style={{ objectFit: "cover" }}
                                preview
                              />
                            )}
                          </FileHOC>
                        ))}
                        {item.collectTagList.length > 0 && (
                          <div className="text">
                            {item.collectTagList[0].name}
                          </div>
                        )}
                      </div>
                    )}

                    {item.type == "Video" && (
                      <div className="info-card">
                        <WibotEditorView html={item.script} />
                        <FileHOC src={item.videos ? item.videos[0] : ""}>
                          {(url) => (
                            <video
                              style={{ maxWidth: "200px", maxHeight: "100px" }}
                              controls
                              src={url}
                              poster={formatURL(item.videos[1])}
                            />
                          )}
                        </FileHOC>
                        {item.collectTagList.length > 0 && (
                          <div className="text">
                            {item.collectTagList[0].name}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}

              {lisbottomBanners.length > 0 && (
                <div style={{ marginTop: "10px" }}>
                  {lisbottomBanners.map((item, index) => (
                    <div key={index}>
                      <FileHOC src={item.image || "error"}>
                        {(url) =>
                          item.url ? (
                            <a
                              target="_blank"
                              href={item.url}
                              style={{ display: "block" }}
                              rel="noreferrer"
                            >
                              <Image
                                width="100%"
                                height="100%"
                                src={url}
                                fallback="images/fallbackImg.png"
                                style={{
                                  objectFit: "cover",
                                  marginBottom: "10px",
                                }}
                                preview={false}
                              />
                            </a>
                          ) : (
                            <Image
                              width="100%"
                              height="100%"
                              src={url}
                              fallback="images/fallbackImg.png"
                              style={{
                                objectFit: "cover",
                                marginBottom: "10px",
                              }}
                              preview={false}
                            />
                          )
                        }
                      </FileHOC>
                    </div>
                  ))}
                </div>
              )}
            </WibotMobilePreview>
          </Col>
        </Row>
      </Spin>

      <CTypeTransferModal {...CTypeTransferParams} />
    </Modal>
  )
}

export default SimulateModal
