.SimulateModal {
  .ant-modal-content {
    background: #fff !important;
  }
  .simulate-flex {
    display: flex;
    align-items: center;
    h3 {
      margin-bottom: 0px;
      margin-right: 20px;
    }
    .tips {
      color: #7f7f7f;
    }
  }
  .scrollList {
    max-width: 1000px;
    height: 150px;
    margin: 0 auto 20px;
    padding: 0;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;

    .listItem {
      position: relative;
      width: 250px;
      display: inline-block;
      height: 124px;
      border: 1px solid #d7d7d7;
      border-radius: 6px;
      padding: 10px;
      overflow: hidden;
      cursor: pointer;
      margin-right: 30px;
      white-space: break-spaces;

      h2 {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
      }

      p {
        font-size: 14px;
        margin: 0;
      }

      .active {
        border: 20px solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-right-color: #1890ff;
        position: absolute;
        transform: rotate(135deg);
        right: -20px;
        top: -20px;
        display: none;

        span {
          position: absolute;
          color: #fff;
          left: 6px;
          bottom: -8px;
          transform: rotate(-120deg);
          font-size: 13px;
        }
      }

      &.activeItem {
        border: 1px solid #1890ff;

        .active {
          display: block;
        }
      }
    }

    // 滚动条整体部分
    &::-webkit-scrollbar {
      width: 6px; //对垂直方向滚动条
      height: 6px; //对水平方向滚动条
    }

    //滚动的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: rgba(0, 0, 0, 0.5) //滚动条的颜色
;
    }

    //内层滚动槽
    &::-webkit-scrollbar-track-piece {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .WibotMobilePreview-Container {
    background: #f0f0f0;
    .carousel-item {
      display: block;
      height: 140px;
      border-radius: 10px;
      overflow: hidden;

      .ant-image {
        width: 100%;
        height: 100%;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
    .ant-carousel .slick-dots li {
      width: 5px;
      height: 5px;
      border-radius: 50%;
    }

    .ant-carousel .slick-dots li.slick-active {
      background-color: #1890ff;
    }

    .ant-carousel .slick-dots li button {
      height: 5px;
      border-radius: 50%;
    }

    .ant-carousel .slick-dots li.slick-active button {
      background-color: #1890ff !important;
    }

    .info-card {
      background: #fff;
      border-radius: 10px;
      margin: 10px 0;
      padding: 10px;
      text-align: left;

      .noMargin {
        margin-bottom: 0px;
      }
    }

    .text {
      font-size: 12px;
      color: #7f7f7f;
    }
  }
}
