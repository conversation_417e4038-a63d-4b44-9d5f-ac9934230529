/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/14 17:13
 * @LastEditTime: 2023/11/21 16:18
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/comps/SpaceModal/home.jsx
 * @Description: '集锦广告位'
 */

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Modal,
  Tooltip,
  Button,
  Table,
  Input,
  message,
  Switch,
  Upload,
  Image,
  Typography,
  Tabs,
} from "antd";
import { PlusOutlined, MenuOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import update from "immutability-helper";
import { compressImage, base64ToFile, beforeUpload } from "common/image";
import { validURL } from "common/regular";
import {FileHOC} from 'components/FileHOC/FileHOC';
import "./home.less";

const { confirm } = Modal;
const { Paragraph } = Typography;
const type = "DraggableBodyRow";
const DraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? " drop-over-downward" : " drop-over-upward",
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ""}`}
      style={{ cursor: "move", ...style }}
      {...restProps}
    />
  );
};

const SpaceModal = (props) => {
  const { collectId = null } = props.params;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [tabsActiveKey, setTabsActiveKey] = useState("TOP");
  const columns = [
    {
      title: "拖动排序",
      width: "80px",
      dataIndex: "sort",
      align: "center",
      render: (value, record, index) => <MenuOutlined />,
    },
    {
      title: "图片",
      width: "160px",
      dataIndex: "image",
      key: "image",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        let content = null;
        if (value) {
          content = (
            <div className="table-image-box">
              <FileHOC src={value}>
                {(url) => (
                  <Image width={60} src={url} preview={{ src: url }}></Image>
  )}
              </FileHOC>
              <a onClick={() => handleReplaceImage(record, index)}>删 除</a>
            </div>
          );
        } else {
          content = (
            <Upload
              name="file"
              customRequest={(config) => customRequest(config, record, index)}

              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={(info) => onChangeUpload(info, index)}
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                loading={record.uploadLoading}
              >
                上 传
              </Button>
            </Upload>
          );
        }
        return content;
      },
    },
    {
      title: "跳转链接",
      width: "180px",
      dataIndex: "url",
      key: "url",
      align: "center",
      render: (value, record, index) => (
        <div className="titleEdit">
          {record.isEdit ? (
            <Input
              value={value}
              onChange={(e) => {
                let newDataSource = dataSource;
                newDataSource[index].url = e.target.value.replace(
                  /^\s+|\s+$/g,
                  ""
                );
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>
              <Paragraph
                ellipsis={{ rows: 1 }}
                style={{ whiteSpace: "pre", width: "165px" }}
              >
                {value}
              </Paragraph>
            </Tooltip>
          )}
          {record.isEdit ? (
            <a onClick={() => handleKeep(record, index)}>保存</a>
          ) : (
            <a onClick={() => handleEdit(record, index)}>编辑</a>
          )}
        </div>
      ),
    },
    {
      title: "显示状态",
      width: "100px",
      dataIndex: "enable",
      key: "enable",
      align: "center",
      render: (value, record, index) => (
        <Switch
          checkedChildren="显示"
          unCheckedChildren="隐藏"
          checked={value}
          onChange={(checked) => onChangeShowState(checked, record, index)}
        />
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record, index)}>删除</a>
        </>
      ),
    },
  ];
  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      const params = {
        list: data.map((item) => item.id),
      };
      apiCall("/info/collectBanner/sort", "POST", params)
        .then((res) => {
          message.success("修改成功！");
          setDataSource(
            update(dataSource, {
              $splice: [
                [dragIndex, 1],
                [hoverIndex, 0, dragRow],
              ],
            })
          );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource]
  );

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      setLoading(true);
      fetchList();
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { type } = params;
    const data = {
      collectId,
      type: type || tabsActiveKey,
    };
    apiCall("/info/collectBanner", "GET", data)
      .then((res) => {
        setDataSource(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = () => {
    if (dataSource.some((item) => !item.image)) {
      message.warning("已存在新增图片，请上传后再新增！");
      return false;
    }
    if (tabsActiveKey == "TOP" && dataSource.length == 9) {
      message.error("顶部轮播广告不能超过9个！");
      return false;
    }
    if (tabsActiveKey == "BOTTOM" && dataSource.length == 5) {
      message.error("底部固定广告不能超过5个！");
      return false;
    }
    setLoading(true);
    const data = {
      sort:
        dataSource.length > 0 ? dataSource[dataSource.length - 1].sort + 1 : 0,
      collectId,
      type: tabsActiveKey,
    };
    apiCall("/info/collectBanner", "POST", data)
      .then((res) => {
        message.success("新增成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { id, url } = record;
    let newDataSource = dataSource;
    if (url) {
      if (!validURL(url)) {
        message.warning("跳转链接不合法！");
        return;
      }
    }
    setLoading(true);
    const data = {
      collectId,
      type: tabsActiveKey,
      url,
    };
    apiCall(`/info/collectBanner/update/${id}`, "POST", data)
      .then((res) => {
        message.success("保存成功！");
        newDataSource[index].isEdit = false;
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeShowState = (checked, record, index) => {
    setLoading(true);
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    newDataSource[index].enable = checked;
    const data = {
      collectId,
      type: tabsActiveKey,
      enable: checked,
    };
    apiCall(`/info/collectBanner/update/${record.id}`, "POST", data)
      .then((res) => {
        message.success("修改成功！");
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/info/collectBanner/delete/${id}`, "POST")
      .then((res) => {
        message.success("删除成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const onChangeUpload = (info, index) => {
    if (info.file.status === "uploading") {
      let newDataSource = dataSource;
      newDataSource[index].uploadLoading = true;
      setDataSource([...newDataSource]);
      return;
    }
  };

  const customRequest = (config, record, index) => {
    const File = config.file;
    const options = {
      quality: 0.9,
    };
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append("file", base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall("/file/image", "POST", data)
        .then((res) => {
          const { fileUrl } = res;
          const data = {
            collectId,
            type: tabsActiveKey,
            image: fileUrl,
          };
          apiCall(`/info/collectBanner/update/${record.id}`, "POST", data)
            .then((res) => {
              message.success("上传成功！");
              fetchList();
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => { });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    });
  };

  const handleReplaceImage = (record, index) => {
    confirm({
      title: "您将删除图片，确认继续吗？",
      onOk () {
        let newDataSource = dataSource;
        newDataSource[index].image = "";
        setDataSource([...newDataSource]);
      },
      onCancel () {
        console.log("Cancel");
      },
    });
  };

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    fetchList({ type: activeKey });
  };

  const onCancel = () => {
    const isEmpty = dataSource.some((item) => !item.image);
    if (isEmpty) {
      message.warning("图片不能为空！");
      return false;
    }
    setVisible(false);
    setLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="SpaceModal"
      visible={visible}
      width={800}
      title="集锦页广告位"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      onCancel={onCancel}
      // onOk={onOk}
      footer={false}
    >
      <Tabs
        defaultActiveKey={tabsActiveKey}
        onChange={onChangeTabs}
        destroyInactiveTabPane
        tabBarExtraContent={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd()}
          >
            添加图片
          </Button>
        }
      >
        <Tabs.TabPane tab="顶部轮播广告" key="TOP">
          <DndProvider backend={HTML5Backend}>
            <Table
              loading={loading}
              rowKey="id"
              columns={columns}
              dataSource={dataSource}
              components={components}
              pagination={false}
              onRow={(record, index) => ({
                index,
                moveRow,
              })}
            />
          </DndProvider>
          <div>图片建议尺寸750*394px，大小2M内，最多9张</div>
        </Tabs.TabPane>
        <Tabs.TabPane tab="底部固定广告" key="BOTTOM">
          <DndProvider backend={HTML5Backend}>
            <Table
              loading={loading}
              rowKey="id"
              columns={columns}
              dataSource={dataSource}
              components={components}
              pagination={false}
              onRow={(record, index) => ({
                index,
                moveRow,
              })}
            />
          </DndProvider>
          <div>图片建议尺寸750*260px，大小2M内，最多5张，建议不超过2张</div>
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
};

export default SpaceModal;
