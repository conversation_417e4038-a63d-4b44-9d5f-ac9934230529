/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/19 16:07
 * @LastEditTime: 2023/03/14 15:29
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\ResourceCollection\home.jsx
 * @Description: '活动集锦'
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { apiCall } from 'common/utils';
import { Spin, Tabs, Button, Card, Empty, Form } from 'antd';
import Collection from './Collection/home';
import CollectionFormModal from './comps/CollectionFormModal/home';
import './home.less';
import { usePageCacheLifeCycle } from "common/hooks"

const { TabPane } = Tabs;

const ResourceCollection = (props) => {
  const [formForm] = Form.useForm();
  const [tabsIndex, setTabsIndex] = useState('0');
  const [collectionFormParams, setCollectionFormParams] = useState({ visible: false });
  const [collectionList, setCollectionList] = useState([]);
  const [collectData, setCollectData] = useState({});
  const [loading, setLoading] = useState(false);

  usePageCacheLifeCycle({
    onShow() {
      fetchList();
    }
  })
  useEffect(() => {
    fetchList();
    // setTabsIndex(localStorage.getItem('TabsCollectionActiveKey') || '0');
  }, []);

  const fetchList = () => {
    setLoading(true);
    apiCall('/activity/collection', 'GET').then((res) => {
      setCollectionList(res);
      const idx = localStorage.getItem('TabsCollectionActiveKey') || '0';
      setCollectData(res.length > 0 ? (res[Number(idx)] ? res[Number(idx)] : res[0]) : {});
      setTabsIndex(res[Number(idx)] ? idx : '0');
      !res[Number(idx)] && localStorage.setItem('TabsCollectionActiveKey', '0');
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }

  const handleCollection = () => {
    setCollectionFormParams({
      visible: true,
      title: '管理活动集锦',
      onCancel: () => {
        setCollectionFormParams({ visible: false });
        fetchList();
      },
    });
  }

  const onChangeTabs = (index) => {
    setTabsIndex(index);
    localStorage.setItem('TabsCollectionActiveKey', index);
    setCollectData(collectionList[Number(index)]);
    formForm.resetFields();
  };

  return (
    <div className='ResourceCollection'>
      <Spin spinning={loading}>
        {
          collectionList.length > 0 ? <>
            <div className='tabBox'>
              <Tabs
                activeKey={tabsIndex}
                destroyInactiveTabPane
                onChange={onChangeTabs}
                tabBarExtraContent={<Button type="primary" onClick={() => handleCollection()}>
                  管理活动集锦
                </Button>} >
                {
                  collectionList?.map((item, index) => (
                    <TabPane tab={item.name} key={`${index}`}>
                    </TabPane>
                  ))
                }
              </Tabs>
            </div>
            {JSON.stringify(collectData) != '{}' && <Collection formForm={formForm} collectData={collectData} />}
          </> : <Card>
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={
              <div>当前没有开启活动集锦，请先创建活动集锦
                <Button type="primary" onClick={() => handleCollection()} style={{ marginLeft: '10px' }}>
                  创建
                </Button>
              </div>
            } />
          </Card>
        }
      </Spin>

      <CollectionFormModal params={collectionFormParams} />
    </div>
  );
};

export default withRouter(ResourceCollection);
