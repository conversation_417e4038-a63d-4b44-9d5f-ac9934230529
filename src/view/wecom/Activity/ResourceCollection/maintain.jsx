/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/19 17:12
 * @LastEditTime: 2025/07/02 11:28
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/maintain.jsx
 * @Description: '问卷维护'
 */

import { useState, useEffect, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Radio,
  Switch,
  Checkbox,
  InputNumber,
  Select,
  message,
  Upload,
  Space,
  Typography,
} from "antd"
import { PlusOutlined, CloseCircleOutlined } from "@ant-design/icons"
import { removeInputEmpty, normFile, editorIsEmpty } from "common/regular"
import { beforeUpload } from "common/image"
import { apiCall } from "common/utils"
import { qs2obj } from "common/object"
import { clearCache } from "react-router-cache-route"
import WibotEditor from "components/WibotEditor/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotEditorView from "components/WibotEditorView/home"

import "./maintain.less"

const FormItem = Form.Item
const { TextArea } = Input
const { Paragraph } = Typography

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const Maintain = (props) => {
  const [formForm] = Form.useForm()
  const DesWibotEditorRef = useRef(null)
  const ActWibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [idData, setIdData] = useState({})
  const [questionData, setQuestionData] = useState({})
  const [imageUrl, setImageUrl] = useState("")
  const [linkImageUrl, setLinkImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [uploadShareLoading, setUploadShareLoading] = useState(false)
  const [cecollectTag, setCecollectTag] = useState([])
  const [daysArr, setDaysArr] = useState([10, 10, 0, 0])
  const [typeOption, setTypeOption] = useState([
    {
      label: "选项内容1",
    },
    {
      label: "选项内容2",
    },
    {
      label: "选项内容3",
    },
  ])
  const [checkAll, setCheckAll] = useState(false)
  const [checkedList, setCheckedList] = useState([])

  useEffect(() => {
    const { id, surveyId } = qs2obj(props.location.search)
    if (surveyId) {
      fetchList(surveyId)
    } else {
      let timer = setTimeout(() => {
        ActWibotEditorRef.current.setHtml(
          "<p>感谢您的提交</p><p>我们会按照您设置的接收频率，将最好的内容呈现给您！</p>"
        )
        clearTimeout(timer)
      }, 300)
    }
    setIdData({
      id,
      surveyId,
    })
    getResourCecollectTag()
  }, [])

  // 获取集锦标签
  const getResourCecollectTag = async () => {
    setLoading(true)
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCecollectTag(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (id) => {
    setLoading(true)
    await apiCall(`/activity/survey/${id}`, "GET")
      .then((res) => {
        if (res) {
          const {
            image,
            title,
            description,
            frequencyQuestion,
            collectionQuestion,
            collectionOption,
            collectTagList,
            shareImage,
            frequencyOption,
            immediateQuestion,
            endWord,
            pageTitle,
          } = res
          const frequencyDay = frequencyOption?.map((item) => item.day)
          formForm.setFieldsValue({
            ...res,
            fileId: image ? [image] : [],
            fileLinkId: shareImage ? [shareImage] : [],
            collectionOption,
            frequencyDay,
          })
          setImageUrl(image)
          setLinkImageUrl(shareImage)
          setTypeOption(
            collectTagList.map((item) => ({
              label: item.name,
              value: item.id,
            }))
          )
          setDaysArr(frequencyDay)
          let timer = setTimeout(() => {
            setQuestionData({
              title,
              pageTitle,
              description,
              thankWord: endWord,
              frequencyQuestion,
              collectionQuestion,
              immediateQuestion,
            })
            DesWibotEditorRef.current.setHtml(description)
            ActWibotEditorRef.current.setHtml(endWord)
            clearTimeout(timer)
          }, 300)
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === "uploading") {
      type ? setUploadShareLoading(true) : setUploadLoading(true)
      return
    }
  }

  const customRequest = (config, type = null) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        if (type) {
          setLinkImageUrl(fileUrl)
          formForm.setFieldsValue({
            fileLinkId: [fileId],
          })
          return false
        }
        setImageUrl(fileUrl)
        formForm.setFieldsValue({
          fileId: [fileId],
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
        setUploadShareLoading(false)
      })
  }

  const handleResetUpload = (e, type = null) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    if (type) {
      setLinkImageUrl("")
      formForm.setFieldsValue({
        fileLinkId: "",
      })
    }
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: "",
    })
  }

  // 问题一添加选项
  const onAdd = () => {
    const newTypeOption = [...typeOption]
    newTypeOption.push("")
    setTypeOption(newTypeOption)
  }

  // 集锦标签选项改变
  const handleCecollectSelect = (value, node, index) => {
    const newTypeOption = [...typeOption]
    newTypeOption[index] = {
      ...node,
      label: node.name,
      value: node.id,
    }
    console.log(value, node, index)
    setTypeOption(newTypeOption)
  }

  const handleDelItem = (item, index) => {
    const newTypeOption = [...typeOption]
    newTypeOption.splice(index, 1)
    setTypeOption([...newTypeOption])
    let formData = formForm.getFieldsValue()
    formData.collectionOption.splice(index, 1)
    formForm.setFieldsValue({
      ...formData,
    })
  }

  // 禁止回车
  const onForbidEnter = (e) => {
    if (e.keyCode === 13) {
      e.preventDefault()
      return
    }
  }

  // 问题二选项日期变化
  const handleDateChange = (value, index) => {
    let data = [...daysArr]
    data[index] = value
    setDaysArr(data)
  }

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (!formData.collectionOption) {
        message.error("资源标签选项不能为空！")
        return
      }
      const collectionOption = JSON.parse(
        JSON.stringify(formData.collectionOption)
      )
      const tagIds = collectionOption?.sort()
      for (let i = 0; i < tagIds.length; i++) {
        if (tagIds[i] == tagIds[i + 1]) {
          message.error("资源标签不能重复！")
          return
        }
      }
      const frequencyOption = daysArr.map((item, index) => {
        let options = {}
        switch (index) {
          case 0:
            options = {
              day: item,
              frequency: "MONTH",
            }
            break
          case 1:
            options = {
              day: item,
              frequency: "SEASON",
            }
            break
          case 2:
            options = {
              day: item,
              frequency: "EVERYDAY",
            }
            break
          case 3:
            options = {
              day: item,
              frequency: "NEVER",
            }
            break
        }
        return options
      })
      formData.image = formData.fileId && formData.fileId[0]
      formData.shareImage = formData.fileLinkId && formData.fileLinkId[0]
      setLoading(true)
      const data = {
        collectId: idData.id,
        id: idData.surveyId,
        frequencyOption,
        ...formData,
      }
      apiCall("/activity/survey", "POST", data)
        .then((res) => {
          message.success(idData.surveyId ? "更新成功！" : "新增成功！")
          props.history.push("/wecom/resourceCollection")
          clearCache(); // 清空路由缓存
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const onCheckAllChange = (e) => {
    const ids = typeOption.map((item) => {
      console.log(item.value)
      return item.value
    })
    setCheckedList(e.target.checked ? ids : [])
    setCheckAll(e.target.checked)
  }

  const onChange = (list) => {
    setCheckedList(list)
    setCheckAll(list.length === typeOption.length)
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="maintain">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title="问卷维护"
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <Form
                {...layout}
                form={formForm}
                initialValues={{
                  repeatFlag: false,
                  frequencyDay: [10, 10, 0, 0],
                }}
              >
                <h2>问卷设置</h2>

                <FormItem
                  label="问卷标题"
                  name="title"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入问卷标题" }]}
                >
                  <Input
                    placeholder="请输入问卷标题(30字)"
                    maxLength={30}
                    allowClear
                    onChange={(e) => {
                      const data = questionData
                      data.title = e.target.value
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  name="description"
                  label="问卷描述"
                  rules={[{ required: false, message: "请输入问卷描述" }]}
                >
                  <WibotEditor
                    ref={DesWibotEditorRef}
                    toolbarConfig={{
                      excludeKeys: ["group-video"],
                    }}
                    editorConfig={{
                      placeholder: "请输入内容(建议100字内)...",
                    }}
                    onChangeHtml={(html) => {
                      formForm.setFieldValue("description", html)
                      const data = questionData
                      data.description = html
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  name="fileId"
                  valuePropName="fileList"
                  getValueFromEvent={normFile}
                  label="问卷封面"
                  rules={[{ required: false, message: "请上传图片" }]}
                  extra="建议尺寸500*500px，大小限制为2M，最多上传1张"
                >
                  <Upload
                    name="file"
                    customRequest={customRequest}
                    listType="picture-card"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={onChangeUpload}
                  >
                    <WibotUploadImage
                      imageUrl={imageUrl}
                      loading={uploadLoading}
                      onClose={handleResetUpload}
                    />
                  </Upload>
                </FormItem>

                <FormItem
                  label="网页标题"
                  name="pageTitle"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: false, message: "请输入网页标题" }]}
                >
                  <Input
                    placeholder="请输入网页标题(10字)"
                    maxLength={10}
                    allowClear
                    onChange={(e) => {
                      const data = questionData
                      data.pageTitle = e.target.value
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  name="repeatFlag"
                  label="重复提交"
                  extra="开启后，客户可能会多次修改问卷中的资源兴趣，修改后下次执行时生效。"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>

                <FormItem label="分享设置">
                  <div className="link-card" style={{ width: "100%" }}>
                    <FormItem
                      name="shareTitle"
                      getValueFromEvent={(e) => removeInputEmpty(e)}
                      rules={[
                        { required: true, message: "请输入分享链接标题" },
                      ]}
                    >
                      <Input
                        placeholder="请输入分享链接标题(30字)"
                        maxLength={30}
                        allowClear
                      />
                    </FormItem>
                    <Row
                      justify="space-between"
                      gutter={16}
                      style={{ marginTop: "10px" }}
                    >
                      <Col span={15}>
                        <FormItem name="shareDescription">
                          <TextArea
                            placeholder="请输入分享链接描述（30字内）"
                            allowClear
                            showCount
                            maxLength={30}
                            autoSize={{ minRows: 2, maxRows: 7 }}
                            className="textArea-mid"
                          />
                        </FormItem>
                      </Col>
                      <Col span={9}>
                        <FormItem
                          name="fileLinkId"
                          valuePropName="imgUrl"
                          getValueFromEvent={normFile}
                          rules={[{ required: true, message: "请选择图片" }]}
                        >
                          <Upload
                            name="file"
                            customRequest={(file) => {
                              customRequest(file, "link")
                            }}
                            listType="picture-card"
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                            onChange={(info) => {
                              onChangeUpload(info, "link")
                            }}
                          >
                            <WibotUploadImage
                              imageUrl={linkImageUrl}
                              loading={uploadShareLoading}
                              onClose={(e) => {
                                handleResetUpload(e, "link")
                              }}
                            />
                          </Upload>
                        </FormItem>
                      </Col>
                    </Row>
                  </div>
                </FormItem>

                <FormItem
                  name="endWord"
                  label="问卷结束"
                  rules={[{ required: true, message: "请输入问卷结束词" }]}
                >
                  <WibotEditor
                    ref={ActWibotEditorRef}
                    toolbarConfig={{
                      excludeKeys: ["group-video"],
                    }}
                    onChangeHtml={(html) => {
                      formForm.setFieldValue(
                        "endWord",
                        editorIsEmpty(html) ? "" : html
                      )
                      const data = questionData
                      data.thankWord = html
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                <h2>问卷内容</h2>
                <div style={{ paddingLeft: "40px" }}>
                  <div className="question">
                    <div
                      className="question_one"
                      style={{ marginBottom: "20px" }}
                    >
                      <span className="num">1</span>
                      <div>
                        <FormItem
                          name="collectionQuestion"
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            { required: true, message: "请输入题目文案" },
                          ]}
                        >
                          <Input
                            placeholder="请选择您感兴趣的集锦类型"
                            maxLength={30}
                            style={{ width: "280px" }}
                            allowClear
                            onChange={(e) => {
                              const data = questionData
                              data.collectionQuestion = e.target.value
                              setQuestionData({ ...data })
                            }}
                          />
                        </FormItem>
                        <div style={{ marginTop: "10px" }}>
                          {typeOption.length > 0 && (
                            <div style={{ marginBottom: "15px" }}>
                              <Checkbox
                                defaultChecked={false}
                                disabled
                                style={{
                                  marginRight: "10px",
                                }}
                              ></Checkbox>
                              <span>全部均可</span>
                            </div>
                          )}

                          {typeOption.map((item, index) => (
                            <div key={index} className="question_two_item">
                              <Checkbox
                                defaultChecked={false}
                                disabled
                                style={{
                                  marginTop: "5px",
                                  marginRight: "10px",
                                }}
                              ></Checkbox>
                              <FormItem
                                name={["collectionOption", index]}
                                rules={[
                                  { required: true, message: "请选择集锦标签" },
                                ]}
                              >
                                <Select
                                  options={cecollectTag}
                                  allowClear
                                  placeholder="请选择集锦标签"
                                  fieldNames={{ label: "name", value: "id" }}
                                  style={{ width: "200px" }}
                                  onSelect={(value, node) => {
                                    handleCecollectSelect(value, node, index)
                                  }}
                                  filterOption={(input, option) =>
                                    (option?.name ?? "")
                                      .toLowerCase()
                                      .includes(input.toLowerCase())
                                  }
                                />
                              </FormItem>
                              <CloseCircleOutlined
                                className="tagClose"
                                onClick={() => handleDelItem(item, index)}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        style={{ marginLeft: "10px" }}
                        onClick={() => onAdd()}
                      >
                        选项
                      </Button>
                    </div>
                    <div className="question_one">
                      <span className="num">2</span>
                      <div>
                        <FormItem
                          name="frequencyQuestion"
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            { required: true, message: "请输入题目文案" },
                          ]}
                        >
                          <Input
                            placeholder="请选择您要接收资源集锦的频率(从填写次日算起)"
                            maxLength={30}
                            style={{ width: "350px" }}
                            allowClear
                            onChange={(e) => {
                              const data = questionData
                              data.frequencyQuestion = e.target.value
                              setQuestionData({ ...data })
                            }}
                          />
                        </FormItem>
                        <div
                          style={{ lineHeight: "30px", marginBottom: "20px" }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <>
                            <span>按月（每月</span>
                            <FormItem
                              name={["frequencyDay", 0]}
                              style={{
                                display: "inline-block",
                                margin: "0 5px",
                              }}
                            >
                              <InputNumber
                                min={1}
                                max={28}
                                defaultValue={10}
                                onChange={(value) => {
                                  handleDateChange(value, 0)
                                }}
                              />
                            </FormItem>
                            <span>号）</span>
                          </>
                        </div>
                        <div
                          style={{ lineHeight: "30px", marginBottom: "20px" }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <>
                            <span>按季（每季的首月</span>
                            <FormItem
                              name={["frequencyDay", 1]}
                              style={{
                                display: "inline-block",
                                margin: "0 5px",
                              }}
                            >
                              <InputNumber
                                min={1}
                                max={28}
                                defaultValue={10}
                                onChange={(value) => {
                                  handleDateChange(value, 1)
                                }}
                              />
                            </FormItem>
                            <span>号）</span>
                          </>
                        </div>
                        {/* <div
                          style={{ lineHeight: '30px', marginBottom: '15px' }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <span>我想要得到实时订阅推送（第二天起推送）</span>
                        </div> */}
                        <div
                          style={{ lineHeight: "30px", marginBottom: "0px" }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <span>不接收（仅第二次填写时显示）</span>
                        </div>
                      </div>
                    </div>
                    <div className="question_one">
                      <span className="num">3</span>
                      <div>
                        <FormItem
                          name="immediateQuestion"
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            { required: true, message: "请输入题目文案" },
                          ]}
                        >
                          <Input
                            placeholder="是否接受临时热点推荐"
                            maxLength={30}
                            style={{ width: "280px" }}
                            allowClear
                            onChange={(e) => {
                              const data = questionData
                              data.immediateQuestion = e.target.value
                              setQuestionData({ ...data })
                            }}
                          />
                        </FormItem>
                        <div
                          style={{ lineHeight: "30px", marginBottom: "20px" }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <span>是</span>
                        </div>
                        <div
                          style={{ lineHeight: "30px", marginBottom: "20px" }}
                        >
                          <Radio defaultChecked={false} disabled></Radio>
                          <span>否</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Form>

              <div style={{ display: "flex", justifyContent: "center" }}>
                <Space size={40}>
                  <Button type="primary" onClick={() => onSubmit()}>
                    保存
                  </Button>
                </Space>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <WibotMobilePreview
                title={questionData.pageTitle || "网页标题"}
                style={{ position: "relative" }}
              >
                <div className="cover">
                  {imageUrl && (
                    <FileHOC src={imageUrl}>
                      {(url) => <img src={url} />}
                    </FileHOC>
                  )}
                </div>
                <div className="body">
                  <Paragraph
                    className="header_title"
                    ellipsis={{ rows: 2, tooltip: false }}
                  >
                    {questionData.title ?? "问卷标题"}
                  </Paragraph>
                  <WibotEditorView html={questionData.description} />
                  <div>
                    <div style={{ marginBottom: "10px" }}>
                      1、
                      <div
                        className="question_radio"
                        style={{ background: "#facd91" }}
                      >
                        多选
                      </div>
                      {questionData.collectionQuestion ??
                        "请选择您感兴趣的集锦类型"}
                    </div>
                    <div className="question_radio_all">
                      <Checkbox onChange={onCheckAllChange} checked={checkAll}>
                        全部均可
                      </Checkbox>
                    </div>

                    <Checkbox.Group value={checkedList} onChange={onChange}>
                      <Space direction="vertical">
                        {typeOption.map((item, index) => (
                          <Checkbox value={item.value} key={index}>
                            {item.label}
                          </Checkbox>
                        ))}
                      </Space>
                    </Checkbox.Group>
                  </div>
                  <div style={{ marginTop: "10px" }}>
                    <div style={{ marginBottom: "10px" }}>
                      2、 <div className="question_radio">单选</div>
                      {questionData.frequencyQuestion ??
                        "请选择您要接收资源集锦的频率(从填写次日算起)"}
                    </div>
                    <Radio.Group defaultValue={null}>
                      <Space direction="vertical">
                        <Radio value={0}>按月</Radio>
                        <Radio value={1}>按季</Radio>
                        {/* <Radio value={2}>我想要得到实时订阅推送</Radio> */}
                        <Radio value={3}>不接收</Radio>
                      </Space>
                    </Radio.Group>
                  </div>
                  <div style={{ marginTop: "10px" }}>
                    <div style={{ marginBottom: "10px" }}>
                      3、 <div className="question_radio">单选</div>
                      {questionData.immediateQuestion ?? "是否接受临时热点推荐"}
                    </div>
                    <Radio.Group defaultValue={null}>
                      <Space direction="vertical">
                        <Radio value={0}>是</Radio>
                        <Radio value={1}>否</Radio>
                      </Space>
                    </Radio.Group>
                  </div>
                </div>
              </WibotMobilePreview>
              <br />
              <WibotMobilePreview title={"提交成功"}>
                <WibotEditorView html={questionData.thankWord} />
              </WibotMobilePreview>
            </Col>
          </Row>
        </Card>
      </Spin>
    </div>
  )
}

export default withRouter(Maintain)
