.maintain {
  .question {
    .question_one {
      display: flex;
      flex-direction: row;
      margin-top: 10px;

      .num {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        margin-right: 10px;
        margin-top: 5px;
      }

      .tagClose {
        cursor: pointer;
        font-size: 20px;
        color: #d9d9d9;
        position: absolute;
        top: 6px;
        right: 0px;
      }

      .question_two_item {
        display: flex;
        flex-direction: row;
        position: relative;
      }
    }
  }

  .textArea-mid {
    textarea {
      height: 100px !important;
    }
  }

  .WibotMobilePreview-Container {
    .cover {
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .body {
      text-align: left;

      .header_title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin: 10px 0;
      }

      .header_describe {
        text-align: left;
        white-space: normal;
        word-break: break-all;
        margin-bottom: 0px;
      }

      .question_radio {
        display: inline-block;
        background: #89c1f9;
        width: 35px;
        height: 20px;
        border-radius: 5px;
        text-align: center;
        line-height: 19px;
        box-sizing: border-box;
        color: #fff;
        margin-right: 5px;
      }

      .question_radio_all {
        margin-bottom: 8px;
      }
    }
  }
}
