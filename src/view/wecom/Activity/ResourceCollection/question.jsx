/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/20 11:55
 * @LastEditTime: 2024/09/19 15:08
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Activity/ResourceCollection/question.jsx
 * @Description: '群发问卷'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Image,
  Tag,
  TreeSelect,
  Radio,
  Checkbox,
  message,
  DatePicker,
  Popover,
  Switch,
  Space,
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { PlusOutlined } from '@ant-design/icons';
import {
  recursionTagKeyTreeData,
  recursionKeyEmployeeOption, recursionTagKeyTreeDataTag
} from "common/tree"
import { qs2obj } from 'common/object';
import WibotEmoji from "components/WibotEmoji/home";
import LinkCard from 'components/LinkCard/home';
import CTypeTransferModal from 'components/TransferModal/CustomerType/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SendModal from '../comps/SendModal/home';
import './question.less';
import { FileHOC } from 'components/FileHOC/FileHOC';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;
const { TextArea } = Input;

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
const Question = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // const [idData, setIdData] = useState({});
  const [sendObjectType, setSendObjectType] = useState(1);
  const [targetCount, setTargetCount] = useState(0);
  const [staffOption, setStaffOption] = useState([]);
  const [groupOption, setGroupOption] = useState([]);
  const [labelOption, setLabelOption] = useState([]);
  const [staffSelected, setStaffSelected] = useState([]);
  const [groupSelected, setGroupSelected] = useState([]);
  const [previewParams, setPreviewParams] = useState({ visible: false });
  const [textContent, setTextContent] = useState('');
  const [linkCard, setLinkCard] = useState({});
  const [isFilter, setIsFilter] = useState(false);
  const filterOption = [{ label: '过滤已推送', value: true }];
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  });
  const [sendParams, setSendParams] = useState({ visible: false });

  useEffect(() => {
    getOptionGroupByDepartment();
    getGroupOption();
    getTagGroupTree();
    const { surveyId } = qs2obj(props.location.search);
    if (surveyId) {
      init(surveyId);
    }
    // setIdData({
    //   id,
    //   surveyId
    // });
  }, []);

  const init = async (surveyId) => {
    await getLastBySurveyId(surveyId);
    await getQuestionInfo(surveyId);
  };

  // 员工
  const getOptionGroupByDepartment = () => {
    apiCall('/employee/company_and_employee_option', 'GET')
      .then((res) => {
        setStaffOption(
          recursionKeyEmployeeOption(res).map((item, index) => ({
            ...item,
            checkable: false,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 群组
  const getGroupOption = async () => {
    await apiCall('/group/option', 'GET')
      .then((res) => {
        setGroupOption(recursionKeyEmpData(res));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const recursionKeyEmpData = (treeData) => {
    const newTreeData = [];
    let obj = {};
    treeData.forEach((item) => {
      const tmp = { ...item };
      if (tmp.children) {
        // 内部调用即递归
        tmp.children = recursionKeyEmpData(tmp.children);
      }
      obj = {
        name: tmp.name || tmp.leaderName,
        key: tmp.id,
        children: tmp.children,
      };
      newTreeData.push(obj);
    });
    return newTreeData;
  }

  // 客户标签
  const getTagGroupTree = async () => {
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelOption([
        //   {
        //     title: '全选',
        //     value: 'customer',
        //     key: 'customer',
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelOption(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getLastBySurveyId = async (surveyId) => {
    setLoading(true);
    const data = {
      surveyId,
    };
    await apiCall('/employeeTaskBatch/getLastBySurveyId', 'GET', data)
      .then((res) => {
        const { messages, remark } = res;
        setTextContent(messages[0] && messages[0].content);
        formForm.setFieldsValue({
          remark,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getQuestionInfo = async (surveyId) => {
    setLoading(true);
    await apiCall(`/activity/survey/${surveyId}`, 'GET')
      .then((res) => {
        const { id, shareTitle, shareDescription, shareImage } = res;
        setLinkCard({
          id,
          title: shareTitle,
          description: shareDescription,
          image: shareImage,
        });
        getTargetCount(res.id);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 切换发送对象类型
  const onChangeRadioSendObjectType = async (e) => {
    const { value } = e.target;
    setSendObjectType(value);
    formForm.setFieldsValue({
      sendObjectType: value,
    });
    getTargetCount();
  };

  // 获取任务对象数量
  const getTargetCount = async (id = null) => {
    setLoading(true);
    const sendObjectType = formForm?.getFieldValue('sendObjectType');
    const filterSurveyVisit = formForm.getFieldValue('filterSurveyVisit');
    const paramsData = {
      surveyId: id || linkCard.id,
      filterSurveySent: isFilter,
      filterSurveyVisit,
      targetType: 'CUSTOMER',
    };
    if (sendObjectType == 1) {
      apiCall('/employeeTaskBatch/getTargetCount', 'GET', paramsData)
        .then((res) => {
          if (res == 0) {
            message.error('没有满足条件的客户，请重新选择');
          }
          setTargetCount(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      const formData = formForm.getFieldsValue([
        'addTime',
        'gender',
        'tagNameList',
        'depEmployeeIdList',
      ]);
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.addTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.addTime;
      }
      if (formData.gender == 'all') {
        formData.gender = null;
      }
      let newGender = '';
      switch (formData.gender) {
        case '男性':
          newGender = 'MAN';
          break;
        case '女性':
          newGender = 'WOMAN';
          break;
        case '未知':
          newGender = 'UNKNOWN';
          break;
      }
      formData.tagNameList = formData.tagNameList?.join(',') || null;
      formData.depEmployeeIdList =
        formData.depEmployeeIdList?.join(',') || null;
      const data = {
        ...formData,
        groupIds: groupSelected?.map((item) => item.key)?.join(',') || null,
        gender: newGender ?? null,
        ...paramsData,
      };
      apiCall('/employeeTaskBatch/getTargetCount', 'GET', data)
        .then((res) => {
          if (res == 0) {
            message.error(
              `负责人${staffSelected
                ?.map((item) => item.name)
                ?.join('、')}名下无满足条件的客户，请重新选择`
            );
          }
          setTargetCount(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  // 客户列表
  const handleSendCustomer = () => {
    setLoading(true);
    const sendObjectType = formForm?.getFieldValue('sendObjectType');
    const filterSurveyVisit = formForm.getFieldValue('filterSurveyVisit');
    const paramsData = {
      surveyId: linkCard.id,
      filterSurveySent: isFilter,
      filterSurveyVisit,
      targetType: 'CUSTOMER',
    };
    if (sendObjectType == 1) {
      apiCall('/employeeTaskBatch/getTargetCustomer', 'GET', paramsData)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            onCancel: () => {
              setSendParams({ visible: false });
            },
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      const formData = formForm.getFieldsValue([
        'addTime',
        'gender',
        'tagIds',
        'depEmployeeIdList',
      ]);
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.addTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.addTime;
      }
      if (formData.gender == 'all') {
        formData.gender = null;
      }
      let newGender = '';
      switch (formData.gender) {
        case '男性':
          newGender = 'MAN';
          break;
        case '女性':
          newGender = 'WOMAN';
          break;
        case '未知':
          newGender = 'UNKNOWN';
          break;
      }
      formData.tagIds = formData.tagIds?.join(',') || null;
      formData.depEmployeeIdList =
        formData.depEmployeeIdList?.join(',') || null;
      const data = {
        ...formData,
        groupIds: groupSelected?.map((item) => item.key)?.join(',') || null,
        gender: newGender ?? null,
        ...paramsData,
      };
      apiCall('/employeeTaskBatch/getTargetCustomer', 'GET', data)
        .then((res) => {
          setSendParams({
            visible: true,
            customerdata: res,
            onCancel: () => {
              setSendParams({ visible: false });
            },
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const onChangeFilter = (checked) => {
    setIsFilter(checked);
  };

  // 添加客户昵称
  const handleAddFocusContent = async () => {
    const insertHTML = '#客户昵称#';
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      textContent.substring(0, startPos) +
      insertHTML +
      textContent.substring(endPos);
    setTextContent(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  const handleTextChange = (e) => {
    setTextContent(e.target.value);
  };

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native;
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      textContent.substring(0, startPos) +
      insertEmoji +
      textContent.substring(endPos);
    setTextContent(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    );
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (formData.addTime) {
        formData.startTime = moment(formData.addTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.addTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.addTime;
      }
      if (formData.planTime) {
        formData.startPlanTime = moment(formData.planTime[0]._d).format(
          'YYYY-MM-DD HH:mm'
        );
        formData.endPlanTime = moment(formData.planTime[1]._d).format(
          'YYYY-MM-DD HH:mm'
        );
        delete formData.planTime;
      }
      // formData.planTime = moment(formData.planTime).format('YYYY-MM-DD HH:mm');
      // formData.tagIds = formData.tagIds?.join(',') || null;

      const {
        startPlanTime,
        endPlanTime,
        sendObjectType,
        startTime = null,
        endTime = null,
        gender = null,
        tagNameList,
        depEmployeeIdList,
        allowSelect,
        filterSurveyVisit,
        remark,
      } = formData;

      if (
        sendObjectType == 2 &&
        !tagNameList?.length &&
        !depEmployeeIdList?.length &&
        !startTime &&
        !groupSelected?.length &&
        gender == 'all'
      ) {
        message.error('请选择部分客户条件！');
        return false;
      }
      setLoading(true);
      const data = {
        surveyId: linkCard.id,
        subTypeSourceId: linkCard.id,
        type: 'CUSTOMER',
        subType: 'SURVEY',
        status: '1',
        targetType: 'CUSTOMER',
        name: '群发问卷',
        startTime: startPlanTime,
        endTime: endPlanTime,
        scope: sendObjectType == 1 ? 'ALL' : 'PART',
        allowSelect,
        remark,
        targetFilter:
          sendObjectType == 1
            ? {
              surveyId: linkCard.id,
              filterSurveySent: isFilter,
              filterSurveyVisit,
              targetType: 'CUSTOMER',
            }
            : {
              surveyId: linkCard.id,
              filterSurveySent: isFilter,
              filterSurveyVisit,
              targetType: 'CUSTOMER',
              depEmployeeIdList: depEmployeeIdList,
              groupIds: groupSelected?.map((item) => item.key) || null,
              gender: gender == 'all' ? null : gender,
              startTime,
              endTime,
              tagNameList: tagNameList,
            },
        messages: [
          {
            content: textContent,
            type: 'copyWriter',
          },
          {
            title: linkCard.title,
            description: linkCard.description,
            fileId: [linkCard.image],
            type: 'SURVEY',
          },
        ],
      };
      apiCall('/employeeTaskBatch/addOrModify', 'PUT', data)
        .then((res) => {
          message.success('新增成功！');
          props.history.push('/wecom/resourceCollection');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className="question">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title="群发问卷（群发客户任务）"
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card>
          <Form
            {...layout}
            form={formForm}
            initialValues={{
              sendObjectType: 1,
            }}
          >
            <FormItem
              name="planTime"
              label="任务时间"
              rules={[{ required: true, message: "请选择任务时间" }]}
            >
              <RangePicker
                showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
                format="YYYY-MM-DD HH:mm"
              />
              {/* <DatePicker
                format="YYYY-MM-DD HH:mm"
                showTime={{ defaultValue: moment('00:00:00', 'HH:mm') }}
                style={{ width: '280px' }}
              /> */}
            </FormItem>
            <FormItem
              name="sendObjectType"
              label="任务对象"
              rules={[{ required: true, message: "请选择发送对象" }]}
              extra={
                sendObjectType == 1 ? (
                  <>
                    预计发送客户数（已去重）：{targetCount} 个
                    <Button
                      style={{ marginLeft: "6px" }}
                      onClick={() => {
                        getTargetCount();
                      }}
                    >
                      刷新
                    </Button>
                    {/* <Button
                      style={{ marginLeft: '10px' }}
                      type="primary"
                      onClick={() => {
                        handleSendCustomer();
                      }}
                    >
                      客户列表
                    </Button> */}
                  </>
                ) : (
                  <>
                    取所有条件交集，预计发送客户数（已去重）：{targetCount} 个
                    <Button
                      style={{ marginLeft: "6px" }}
                      onClick={() => {
                        getTargetCount();
                      }}
                    >
                      刷新
                    </Button>
                    {/* <Button
                      style={{ marginLeft: '10px' }}
                      type="primary"
                      onClick={() => {
                        handleSendCustomer();
                      }}
                    >
                      客户列表
                    </Button> */}
                  </>
                )
              }
            >
              <div style={{ position: "relative" }}>
                <Radio.Group
                  onChange={onChangeRadioSendObjectType}
                  value={sendObjectType}
                >
                  <Radio value={1}>全部客户</Radio>
                  <Radio value={2}>部分客户</Radio>
                </Radio.Group>
                {/* <Checkbox.Group options={filterOption} onChange={onChangeFilter} style={{ marginLeft: '10px' }} /> */}
                <Popover
                  title="群发说明"
                  content={
                    <>
                      <p>
                        1.
                        群发部分客户筛选的员工就是客户负责人，群发客户消息任务由客户负责人执行；
                      </p>
                      <p>
                        2.
                        群发客户任务调企微企业群发接口，群发的客户由后台限制，员工不可手动更改；
                      </p>
                      <p>
                        3.
                        群发部分客户的筛选条件含负责人员工时，按所选要求筛选客户，客户的负责人必须是所选员工（客户的负责人不是所选员工时自动过滤掉），如筛选出的客户数等于0时不给所选员工推送任务，且员工不纳入任务统计；
                      </p>
                      <p>
                        4.群发部分客户的筛选条件不含负责人员工时，筛选出的客户按照客户的负责人员工去推送任务。
                      </p>
                    </>
                  }
                >
                  <a style={{ marginLeft: "10px" }}>群发说明</a>
                </Popover>
                {/* <FormItem name='autoCreateGroup' label="过滤已推" valuePropName="checked" extra="开启后自动忽略推送过的客户">
                    <Switch />
                  </FormItem> */}
              </div>
            </FormItem>
            {sendObjectType == 2 ? (
              <div className="input-content-mask">
                <FormItem
                  name="depEmployeeIdList"
                  label="客户负责人"
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.type !== curValues.type
                  }
                >
                  <ETypeTransferModal
                    title="选择员工"
                    onChange={(value, options) => {
                      setStaffSelected(options);
                    }}
                  />
                </FormItem>

                <FormItem name="addTime" label="添加时间">
                  <RangePicker />
                </FormItem>

                <FormItem
                  name="groupIds"
                  label="群组"
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.type !== curValues.type
                  }
                  extra={
                    groupSelected.length > 0 ? (
                      <div>
                        {groupSelected.map((item, index) => (
                          <Tag
                            closable
                            style={{ marginTop: "8px" }}
                            key={index}
                            onClose={(e) => {
                              e.preventDefault();
                              setGroupSelected(
                                groupSelected.filter((i) => i.key != item.key)
                              );
                            }}
                          >
                            {item.name}
                          </Tag>
                        ))}
                      </div>
                    ) : null
                  }
                >
                  <Button
                    icon={<PlusOutlined />}
                    type="primary"
                    onClick={() => {
                      setCTypeTransferParams({
                        visible: true,
                        type: "group",
                        checkList: groupSelected,
                        onSubmit: (data) => {
                          setGroupSelected(data);
                          setCTypeTransferParams({ visible: false });
                        },
                        onCancel: () => {
                          setCTypeTransferParams({ visible: false });
                        },
                      });
                    }}
                  >
                    选择群组
                  </Button>
                </FormItem>

                <FormItem name="gender" label="选择性别" initialValue={"all"}>
                  <Radio.Group>
                    <Radio value={"all"}>全部</Radio>
                    <Radio value={"男性"}>仅男性</Radio>
                    <Radio value={"女性"}>仅女性</Radio>
                    <Radio value={"未知"}>未知</Radio>
                  </Radio.Group>
                </FormItem>

                {/* <FormItem label="客户标签" name="tagIds"> */}
                {/*   <TreeSelect */}
                {/*     treeData={labelOption} */}
                {/*     treeCheckable */}
                {/*     treeDefaultExpandedKeys={["customer"]} */}
                {/*     allowClear */}
                {/*     showArrow */}
                {/*     showSearch */}
                {/*     treeNodeFilterProp="title" */}
                {/*     maxTagCount="responsive" */}
                {/*     showCheckedStrategy={SHOW_PARENT} */}
                {/*     placeholder="客户标签" */}
                {/*   /> */}
                {/* </FormItem> */}
                <CustomTagSelect
                  label="客户标签"
                  name="tagNameList"
                  placeholder="客户标签"
                  useForm={formForm}
                  labelTreeData={labelOption}
                />
              </div>
            ) : null}

            <FormItem
              name="autoCreateGroup"
              label="过滤已推"
              valuePropName="checked"
              extra="开启后自动忽略推送过的客户"
              labelCol={{ span: 6 }}
              style={{ marginBottom: "10px" }}
              initialValue={false}
            >
              <Switch onChange={onChangeFilter} />
            </FormItem>
            <FormItem
              name="filterSurveyVisit"
              label="过滤已访"
              valuePropName="checked"
              extra="开启后自动忽略访问过该问卷的客户"
              labelCol={{ span: 6 }}
              style={{ marginBottom: "10px" }}
              initialValue={false}
            >
              <Switch />
            </FormItem>
            <FormItem
              name="allowSelect"
              label="群发调整"
              valuePropName="checked"
              extra="开启后允许员工手动调整发送范围"
              labelCol={{ span: 6 }}
              initialValue={false}
            >
              <Switch />
            </FormItem>
            <FormItem
              name="remark"
              label="任务说明"
              rules={[{ required: true, message: "请输入任务说明" }]}
              className="textArea-mid"
            >
              <TextArea
                placeholder="请输入任务说明"
                allowClear
                autoSize={{ minRows: 2, maxRows: 7 }}
              />
            </FormItem>
            <FormItem label="群发内容">
              <div className="mass-box">
                <div className="mass_item" style={{ marginBottom: "10px" }}>
                  <span className="num">1</span>
                  <FormItem name="text" label="文案" style={{ width: "80%" }}>
                    {/* extra={<Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddFocusContent()}>客户昵称</Button>} */}
                    <div className="textarea-emoji">
                      <TextArea
                        id="msgTextInput"
                        placeholder="请输入文案（100字）"
                        value={textContent}
                        allowClear
                        showCount
                        maxLength={1200}
                        autoSize={{ minRows: 2, maxRows: 7 }}
                        onChange={handleTextChange}
                        className="textArea-mid"
                      />

                      <WibotEmoji
                        iconClassName="textarea-emoji_icon"
                        onEmojiSelect={handleEmojiSelect}
                      />
                    </div>
                  </FormItem>
                </div>
                <div className="mass_item">
                  <span className="num">2</span>
                  <FormItem name="text" label="问卷" style={{ width: "100%" }}>
                    <LinkCard data={linkCard} isLink={false} />
                  </FormItem>
                </div>
              </div>
            </FormItem>

            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                <Button type="primary" onClick={() => onSubmit()}>
                  确定
                </Button>
              </Space>
            </div>
          </Form>
        </Card>
      </Spin>
      <CTypeTransferModal {...CTypeTransferParams} />
      <SendModal {...sendParams} />
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false });
              },
            }}
          />
        )}
      </FileHOC>
    </div>
  );
};

export default withRouter(Question);
