/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/09 13:51
 * @LastEditTime: 2023/06/07 14:51
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Activity\comps\SendModal\home.jsx
 * @Description: '发送客户列表弹窗'
 */
import React, { memo, useEffect, useState } from "react";
import { Modal, Avatar, Image, Empty } from "antd";
import "./home.less";
import {FileHOC} from 'components/FileHOC/FileHOC';
const SendModal = (props) => {
  const { visible, type = "CUSTOMER", customerdata, onCancel } = props;

  const [sentCount, setSentCount] = useState(0);
  const [sentData, setSentData] = useState([]);

  useEffect(() => {
    if (visible) {
      setSentCount(customerdata.key);
      setSentData(customerdata.value);
    }
  }, [visible]);

  const onModalCancel = () => {
    onCancel();
  };

  return (
    <Modal
      visible={visible}
      title={`预计要发送的客户${type == "CHAT" ? "群" : ""}(${sentCount}人)`}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onModalCancel}
      className="SendModal"
      footer={false}
    >
      {sentData?.length > 0 ? (
        <div className="SendModal-wrap">
          {sentData.map((item, index) => (
            <div key={index}>
              <p className="dep-title">
                {item.departmentName}-{item.employeeName}（
                {item.customerVOList?.length || 0}人）
              </p>
              {item.customerVOList?.map((atem, andex) => (
                <div
                  key={andex}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    margin: "10px 0",
                  }}
                >
                  {atem.avatar && (
                    <div>
                      <Avatar
                        shape="square"
                        size={30}
                        src={<FileHOC src={atem.avatar}>
                          {(url) => <Image src={url} preview={false} />}
                        </FileHOC>}
                      />
                    </div>
                  )}
                  {type == "CHAT" ? (
                    <div>
                      {atem.groupName}（{atem.groupMemberCount}人）
                    </div>
                  ) : (
                    <div style={{ margin: "0 5px" }}>
                      {atem.customerName}{" "}
                      <span
                        style={{
                          color: atem.type == 1 ? "#07c160" : "#f59a23",
                        }}
                      >
                        {atem.suffixName}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </Modal>
  );
};
export default memo(SendModal);
