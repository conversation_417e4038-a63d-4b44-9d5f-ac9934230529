import React, { useState, useEffect, useRef } from "react"
import { Select, Form } from "antd"
import { CheckOutlined } from "@ant-design/icons"
import { findTreeItemByName } from "common/tree"

const { Option, OptGroup } = Select
const FormItem = Form.Item

const CustomTagSelect = ({
  name, // FormItem 的 name 属性，用于表单数据绑定
  label, // FormItem 的 label 属性
  labelTreeData: initialLabelTreeData, // 标签树数据，外部传入
  onChange, // 外部传入的 onChange 事件处理函数
  useRefForm,
  useForm,
  creatable = false,
  existTagNameList = [], // 存在标签名称列表, 用于编辑时回显用户已存在但是不在标签树中的标签
  ...props // 传递给 Select 组件的其他属性
}) => {
  const [labelTreeData, setLabelTreeData] = useState(initialLabelTreeData)
  const checkedListRef = useRef([])
  const isInitializingRef = useRef(false)

  useEffect(() => {
    setLabelTreeData(initialLabelTreeData)
  }, [initialLabelTreeData])

  useEffect(() => {
    if (!existTagNameList.length) {
      setLabelTreeData(initialLabelTreeData)
      return
    }

    isInitializingRef.current = true

    // 深拷贝避免直接修改原数据
    const newLabelTreeData = JSON.parse(JSON.stringify(initialLabelTreeData))
    let tempGroup = { label: "--", value: "temp", options: [], checked: false }
    let needTempGroup = false

    existTagNameList.forEach((tagName) => {
      const find = findTreeItemByName(newLabelTreeData, tagName, "label")
      if (!find) {
        tempGroup.options.push({
          label: tagName,
          value: tagName,
          checked: true,
        })
        needTempGroup = true
      } else {
        find.checked = true
      }
    })

    // 更新所有分组的选中状态
    const finalTreeData = newLabelTreeData.map((group) => {
      group.checked =
        group.options?.every((o) => o.checked) && group.options?.length > 0
      return group
      // return {
      //   ...group,
      //   checked: allChecked && group.options?.length > 0,
      // }
    })

    // 如果需要临时分组，添加到最后
    if (needTempGroup) {
      tempGroup.checked = tempGroup.options.every((option) => option.checked)
      finalTreeData.push(tempGroup)
    }

    setLabelTreeData(finalTreeData)

    // 设置初始选中的标签
    setTimeout(() => {
      isInitializingRef.current = false
    }, 0)
  }, [existTagNameList, initialLabelTreeData])

  useEffect(() => {
    // 初始化 checkedListRef
    checkedListRef.current = labelTreeData.reduce((acc, group) => {
      group.checked = group.options?.every((option) => option.checked) || false
      group.options?.forEach((option) => {
        if (option.checked) {
          acc.push(option.label)
        }
      })
      return acc
    }, [])
  }, [labelTreeData])

  useEffect(() => {
    // 监听表单重置事件
    if (useForm || useRefForm?.current) {
      const formInstance = useForm || useRefForm?.current
      const originalResetFields = formInstance.resetFields

      formInstance.resetFields = (...args) => {
        // 在重置表单之前，先重置 labelTreeData
        setLabelTreeData((prevState) => {
          return prevState.map((g) => ({
            ...g,
            checked: false,
            options:
              g.options?.map((option) => ({
                ...option,
                checked: false,
              })) || [],
          }))
        })
        checkedListRef.current = []
        originalResetFields(...args) // 调用原始的 resetFields 方法
      }
    }
  }, [useForm, useRefForm?.current])

  const handleSelectChange = (selectedValues) => {
    // 如果是初始化过程中，不处理
    if (isInitializingRef.current) {
      return
    }

    // 更新 checkedListRef
    checkedListRef.current = selectedValues

    setLabelTreeData((prevState) => {
      return prevState.map((group) => {
        const updatedOptions =
          group.options?.map((option) => ({
            ...option,
            checked: selectedValues.includes(option.label),
          })) || []

        group.checked =
          updatedOptions.length > 0 &&
          updatedOptions.every((option) => option.checked)
        group.options = updatedOptions
        return group
      })
    })

    onChange?.(selectedValues) // 触发外部传入的 onChange
  }

  const handleToggle = (targetGroup) => {
    // 不处理没有选项的组
    if (!targetGroup.options?.length) {
      return
    }

    setLabelTreeData((prevState) => {
      const newState = prevState.map((group) => {
        if (group.label === targetGroup.label) {
          group.checked = !group.checked
          group.options =
            group.options?.map((option) => ({
              ...option,
              checked: group.checked,
            })) || []
        }
        return group
      })

      // 计算新的选中列表
      const newCheckedList = newState.reduce((acc, group) => {
        group.options?.forEach((option) => {
          if (option.checked) {
            acc.push(option.label)
          }
        })
        return acc
      }, [])

      // 更新 checkedListRef 和表单值
      checkedListRef.current = newCheckedList

      // 使用 setTimeout 确保状态更新后再设置表单值
      setTimeout(() => {
        const formInstance = useRefForm?.current || useForm
        if (formInstance) {
          formInstance.setFieldValue(name, newCheckedList)
        }
        onChange?.(newCheckedList)
      }, 0)

      return newState
    })
  }

  return (
    <FormItem name={name} label={label} {...props}>
      <Select
        mode={creatable ? "tags" : "multiple"}
        maxTagCount="responsive"
        placeholder="客户标签"
        onChange={handleSelectChange}
        {...props}
      >
        {labelTreeData.map((group, i) => (
          <OptGroup
            label={
              <div onClick={() => handleToggle(group)}>
                {group.label} {group.checked ? <CheckOutlined /> : null}
              </div>
            }
            key={group.label + i}
          >
            {group.options?.map((option, index) => (
              <Option key={option.label + index} value={option.label}>
                <span>{option.label}</span>
              </Option>
            ))}
          </OptGroup>
        ))}
      </Select>
    </FormItem>
  )
}

export default CustomTagSelect
