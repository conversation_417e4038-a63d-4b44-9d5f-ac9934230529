/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/01/16 09:41
 * @LastEditTime: 2023/12/01 10:55
 * @LastEditors: <PERSON>ei<PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/Customer/comps/ExportModal.jsx
 * @Description: '客户管理-导出对话框'
 */

import React, { useEffect, useRef, useState } from "react";
import { Tabs, Form, message, Modal, Spin, DatePicker } from "antd";
import { apiCall } from "common/utils";
import { getExportFileDate } from "common/date";
import moment from "moment";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import "./ExportModal.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("1");
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startDate = moment(formData.date[0]._d).format("YYYY-MM-DD");
        formData.endDate = moment(formData.date[1]._d).format("YYYY-MM-DD");
        delete formData.date;
      }
      const data = {
        ...formData,
        type: TabsActiveKey == 1 ? "CUSTOMER_STATISTICS" : "CUSTOMER_DETAIL",
      };
      apiCall("/customer/export", "POST", data, null, {
        isExit: true,
        title: `${TabsActiveKey == 1 ? "客户统计数据" : "客户明细清单"
          }.${getExportFileDate(formData.startDate, formData.endDate)}.xlsx`,
      })
        .then((res) => {
          message.success("导出成功！");
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="Customer-ExportModal"
      visible={visible}
      title="导出客户数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="客户统计数据" key="1">
              <p className="tips">导出客户的汇总数据</p>
              <FormItem
                name="date"
                label="统计日期"
                extra="日期为员工添加客户的日期"
                rules={[{ required: true, message: "请选择统计日期" }]}
              >
                <RangePicker />
              </FormItem>

              <FormItem
                label="统计对象"
                name="deptIdList"
                rules={[{ required: true, message: "请选择统计对象" }]}
              >
                <ETypeTransferModal
                  title="选择对象"
                />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="客户明细清单" key="2">
              <p className="tips">导出客户明细清单</p>
              <FormItem
                name="date"
                label="统计日期"
                extra="日期为员工添加客户的日期"
                rules={[{ required: true, message: "请选择统计日期" }]}
              >
                <RangePicker />
              </FormItem>

              <FormItem
                label="统计对象"
                name="deptIdList"
                rules={[{ required: true, message: "请选择统计对象" }]}
              >
                <ETypeTransferModal
                  title="选择对象"
                />
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
