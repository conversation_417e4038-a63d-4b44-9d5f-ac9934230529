/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2023/12/29 14:32
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/Customer/comps/MaintainLabelModal.jsx
 * @Description: '维护标签对话框'
 */

import React, { useEffect, useState, useRef } from "react";
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  Divider,
  Button,
  Upload,
} from "antd";
import { apiCall } from "common/utils";
import { flatten, recursionTagKeyTreeDataTag } from "common/tree"
import moment from "moment";
import WibotMaintainLabel from "components/WibotMaintainLabel/home";
import { UploadOutlined } from "@ant-design/icons";
import "./MaintainLabelModal.less";

const FormItem = Form.Item;

const MaintainLabelModal = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("add");
  const [selectedRows, setSelectedRows] = useState(null);
  const [initTagTreeData, setInitTagTreeData] = useState([]);
  const [tagTreeData, setTagTreeData] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [oldTagTreeData, setOldTagTreeData] = useState([]);
  const [oldCheckedKeys, setOldCheckedKeys] = useState([]);
  const [oldSearchValue, setOldSearchValue] = useState("");
  const [disabledSection, setDisabledSection] = useState(true);
  const formRef = useRef(null);
  const [fileList, setFileList] = useState([]);

  useEffect(async () => {
    const { visible, selectedRows } = props.params;
    if (visible) {
      await getInfoTagGroupTree();
      setSelectedRows(selectedRows);
      setVisible(true);
    } else {
      // formRef.current.resetFields();
      setCheckedKeys([]);
      setSearchValue("");
      setOldCheckedKeys([]);
      setOldSearchValue("");
      setConfirmLoading(false);
      setFileList([]);
      setVisible(false);
    }
  }, [props.params]);

  useEffect(async () => {
    if (selectedRows?.length) {
      setDisabledSection(false);
      setTabsActiveKey("add");
    } else {
      setDisabledSection(true);
      setTabsActiveKey("batchAddTag");
    }
  }, [selectedRows]);

  // 获取标签数据
  const getInfoTagGroupTree = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res).map((item, index) => ({
          ...item,
          checkable: false,
        }));
        setInitTagTreeData(tagTreeData);
        setTagTreeData(tagTreeData);
        setOldTagTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const recursionTagKeyTreeData = (treeData) => {
    const newTreeData = [];
    let obj = {};
    treeData.forEach((item) => {
      const tmp = { ...item };
      if (tmp.children) {
        // 内部调用即递归
        tmp.children = recursionTagKeyTreeData(tmp.children);
      }
      obj = { ...tmp, label: tmp.title, value: tmp.title, key: tmp.title };
      delete obj.checkable;
      newTreeData.push(obj);
    });
    return newTreeData;
  };

  const onChangeTabs = (activeKey) => {
    setTagTreeData([...initTagTreeData]);
    setCheckedKeys([]);
    setSearchValue("");
    setOldTagTreeData([...initTagTreeData]);
    setOldCheckedKeys([]);
    setOldSearchValue("");
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
    setFileList([]);
  };

  const onOk = () => {
    if (selectedRows.length) {
      if (
        checkedKeys &&
        !checkedKeys.length &&
        oldCheckedKeys &&
        !oldCheckedKeys.length
      ) {
        message.warning("请至少选择一个标签");
        return false;
      }
      setConfirmLoading(true);
      const data = {
        labelType: TabsActiveKey,
        customerIdList: selectedRows.map((item) => item.id),
        tagNameList: checkedKeys,
        removeTagNameList: oldCheckedKeys,
      };
      props.params?.onSubmit?.({ ...data });
    } else {
      formRef.current.validateFields().then((formData) => {
        setConfirmLoading(true);
        const { file } = formData;
        const formObj = new FormData();
        formObj.append("file", file.file.file);
        formObj.append(
          "removeAllOldTag",
          TabsActiveKey == "batchAddTag" ? false : true
        );
        const data = formObj;
        props.params?.onSubmit?.(data);
      });
    }
  };

  const onCancel = () => {
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  const onChangeSearchTag = (e, operaType = "add") => {
    if (operaType != "add") {
      let { value } = e.target;
      setOldSearchValue(value);
      if (!value) {
        setOldTagTreeData([...initTagTreeData]);
        return false;
      }
      const flattenTagTreeData = flatten(initTagTreeData);
      const indexOfTagTreeData = flattenTagTreeData
        .filter((item) => item.parentid)
        .filter((item) => item.title.indexOf(value) > -1);
      setOldTagTreeData([...indexOfTagTreeData]);
    } else {
      let { value } = e.target;
      setSearchValue(value);
      if (!value) {
        setTagTreeData([...initTagTreeData]);
        return false;
      }
      const flattenTagTreeData = flatten(initTagTreeData);
      const indexOfTagTreeData = flattenTagTreeData
        .filter((item) => item.parentid)
        .filter((item) => item.title.indexOf(value) > -1);
      setTagTreeData([...indexOfTagTreeData]);
    }
  };

  const onCheckTagKeys = (checkedKeysData, e, operaType = "add") => {
    const { checked, node } = e;
    if (operaType != "add") {
      let newOldCheckedKeys = [...oldCheckedKeys];
      if (checked) {
        newOldCheckedKeys.push(node.key);
      } else {
        newOldCheckedKeys = newOldCheckedKeys.filter(
          (item) => item != node.key
        );
      }
      setOldCheckedKeys([...newOldCheckedKeys]);
    } else {
      let newCheckedKeys = [...checkedKeys];
      if (checked) {
        newCheckedKeys.push(node.key);
      } else {
        newCheckedKeys = newCheckedKeys.filter((item) => item != node.key);
      }
      setCheckedKeys([...newCheckedKeys]);
    }
  };

  // 下载模板
  const handleDownload = (type) => {
    setLoading(true);
    const title =
      type == "batchOverlayTab" ? "客户批量覆盖标签模板" : "客户批量打标签模板";
    const data = {
      fileName: `${title}.xlsx`,
    };
    apiCall("/file/template", "GET", data, null, {
      isExit: true,
      title: `${title}.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => {
        message.success("下载成功！");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeUpload = (info) => {
    if (!info.file.name.endsWith("xls") && !info.file.name.endsWith("xlsx")) {
      return false;
    }
    if (!info.fileList.length) {
      formRef.current.setFieldsValue({ file: "" });
    }
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const customFileRequest = (config) => {
    setConfirmLoading(true);
    const File = config.file;
    const newFileLIst = fileList;
    let timer = setTimeout(() => {
      newFileLIst[0].status = "done";
      newFileLIst[0].percent = 100;
      newFileLIst[0].file = File;
      setFileList([...newFileLIst]);
      setConfirmLoading(false);
      clearTimeout(timer);
    }, 2000);
  };

  return (
    <Modal
      className="Customer-MaintainLabelModal"
      visible={visible}
      title="批量维护标签"
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="添加" key="add" disabled={disabledSection}>
              <p className="tips">
                请选择要添加的标签，客户已存在该标签时不会重复添加
              </p>
              <WibotMaintainLabel
                operaType="add"
                selectedRows={selectedRows}
                initTreeData={initTagTreeData}
                searchValue={searchValue}
                treeData={tagTreeData}
                checkedKeys={checkedKeys}
                onChangeSearchTag={onChangeSearchTag}
                onCheckTagKeys={onCheckTagKeys}
              />
            </Tabs.TabPane>

            <Tabs.TabPane tab="删除" key="del" disabled={disabledSection}>
              <p className="tips">
                请选择要删除的标签，客户不存在该标签时不会有删除记录
              </p>
              <WibotMaintainLabel
                operaType="del"
                selectedRows={selectedRows}
                initTreeData={initTagTreeData}
                searchValue={oldSearchValue}
                treeData={oldTagTreeData}
                checkedKeys={oldCheckedKeys}
                onChangeSearchTag={onChangeSearchTag}
                onCheckTagKeys={onCheckTagKeys}
              />
            </Tabs.TabPane>

            <Tabs.TabPane tab="替换" key="replace" disabled={disabledSection}>
              <p className="tips">
                请选择要替换的旧标签及新标签，确认操作将会删除旧标签、添加新标增
              </p>
              <div style={{ display: "flex" }}>
                <WibotMaintainLabel
                  operaType="del"
                  initTreeData={initTagTreeData}
                  searchValue={oldSearchValue}
                  treeData={oldTagTreeData}
                  checkedKeys={oldCheckedKeys}
                  onChangeSearchTag={onChangeSearchTag}
                  onCheckTagKeys={onCheckTagKeys}
                />
                <Divider
                  type="vertical"
                  style={{ height: "auto", borderLeftColor: "#d9d9d9" }}
                />
                <WibotMaintainLabel
                  operaType="add"
                  initTreeData={initTagTreeData}
                  searchValue={searchValue}
                  treeData={tagTreeData}
                  checkedKeys={checkedKeys}
                  onChangeSearchTag={onChangeSearchTag}
                  onCheckTagKeys={onCheckTagKeys}
                />
              </div>
            </Tabs.TabPane>

            <Tabs.TabPane
              tab="批量打标签页"
              key="batchAddTag"
              disabled={!disabledSection}
            >
              <p className="tips">通过excel去给客户打标签</p>
              <p className="tips">
                1.上传的客户标签需后台标签库存在，否则不更新
              </p>
              <p className="tips">2.上传多个客户标签时，需要“,”符号分割</p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[
                  { required: true, message: "请上传xls、xlsx格式的文件" },
                ]}
                extra={
                  <div>
                    请选择批量打标签清单文件，格式xls、xlsx
                    <a onClick={handleDownload} style={{ marginLeft: "5px" }}>
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !file.name.endsWith("xls") &&
                      !file.name.endsWith("xlsx")
                    ) {
                      message.error("请上传xls、xlsx格式的文件!");
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane
              tab="批量覆盖标签页"
              key="batchOverlayTab"
              disabled={!disabledSection}
            >
              <p className="tips">通过excel去给客户覆盖标签</p>
              <p className="tips">
                1.上传的客户标签需后台标签库存在，否则不更新
              </p>
              <p className="tips">2.上传多个客户标签时，需要“,”符号分割</p>
              <p className="tips">
                3.注意：文件上传后将完全覆盖原客户标签，无法回退
              </p>
              <FormItem
                label="选择文件"
                name="file"
                rules={[
                  { required: true, message: "请上传xls、xlsx格式的文件" },
                ]}
                extra={
                  <div>
                    请选择批量打标签清单文件，格式xls、xlsx
                    <a
                      onClick={() => {
                        handleDownload("batchOverlayTab");
                      }}
                      style={{ marginLeft: "5px" }}
                    >
                      下载模板
                    </a>
                  </div>
                }
              >
                <Upload
                  name="file"
                  accept=".xls,.xlsx"
                  fileList={fileList}
                  onChange={onChangeUpload}
                  beforeUpload={(file) => {
                    if (
                      !file.name.endsWith("xls") &&
                      !file.name.endsWith("xlsx")
                    ) {
                      message.error("请上传xls、xlsx格式的文件!");
                      return false || Upload.LIST_IGNORE;
                    }
                  }}
                  customRequest={customFileRequest}
                >
                  {fileList.length <= 0 ? (
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  ) : null}
                </Upload>
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default MaintainLabelModal;
