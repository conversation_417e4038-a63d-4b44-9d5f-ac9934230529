/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/14 10:06
 * @LastEditTime: 2024/10/22 09:14
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/Group/details.jsx
 * @Description: '客户管理-客户群详情'
 */

import React, { useEffect, useState, useRef } from "react";
import { withRouter } from "react-router-dom";
import {
  Spin,
  Button,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Tabs,
  Timeline,
  Image,
  Form,
  Input,
  DatePicker,
  Radio,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import SysDictLabel from "components/select/SysDictLabel";
import { createFromIconfontCN } from "@ant-design/icons";
import { qs2obj } from "common/object";
import FilterBar from "components/FilterBar/FilterBar";
import ListOperation from 'components/ListOperation/home';
import "./details.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const IconFont = createFromIconfontCN({});

const GroupManagementDetail = (props) => {
  const GroupMemberFormRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [tabsIndex, setTabsIndex] = useState("1");
  const [inGroup, setInGroup] = useState(true);
  const JoinColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "成员名称",
      width: "220px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.colour == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "140px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {value}
              {record.companyName && companyName}
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "成员类型",
      width: "160px",
      dataIndex: "typeName",
      key: "typeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "部门",
      width: "160px",
      dataIndex: "departmentLink",
      key: "departmentLink",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "入群时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "入群方式",
      width: "160px",
      dataIndex: "joinScene",
      key: "joinScene",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "邀请人",
      width: "160px",
      dataIndex: "inviterUserName",
      key: "inviterUserName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [];
        if (record.type == 2) {
          opts.push({ onClick: () => handleDetails(record), name: "客户详情" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const QuitColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "成员名称",
      width: "220px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.colour == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "140px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {value}
              {record.companyName && companyName}
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "成员类型",
      width: "160px",
      dataIndex: "typeName",
      key: "typeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "部门",
      width: "160px",
      dataIndex: "departmentLink",
      key: "departmentLink",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "退群时间",
      width: "160px",
      dataIndex: "quitTime",
      key: "quitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.quitTime) - timeStamp(b.quitTime),
    },
    {
      title: "邀请人",
      width: "160px",
      dataIndex: "inviterUserName",
      key: "inviterUserName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [];
        if (record.type == 2) {
          opts.push({ onClick: () => handleDetails(record), name: "客户详情" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const sopColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "SOP名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "SOP类型",
      width: "160px",
      dataIndex: "typeName",
      key: "typeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "托管状态",
      width: "160px",
      dataIndex: "disableRobot",
      key: "disableRobot",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? "手动处理" : "托管中"}>
          {value ? (
            <span style={{ color: "#d9001b" }}>手动处理</span>
          ) : (
            <span style={{ color: "#07c160" }}>托管中</span>
          )}
        </Tooltip>
      ),
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "下次执行时间时间",
      width: "160px",
      dataIndex: "nextTime",
      key: "nextTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.nextTime) - timeStamp(b.nextTime),
    },
  ];
  const taskColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "发送消息",
      width: "160px",
      dataIndex: "content",
      key: "content",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "任务状态",
      width: "160px",
      dataIndex: "status",
      key: "status",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value == "TO_EXEC" ? "待执行" : "已执行"}>
          {value == "TO_EXEC" ? (
            <span style={{ color: "#d9001b" }}>待执行</span>
          ) : (
            <span style={{ color: "#07c160" }}>已执行</span>
          )}
        </Tooltip>
      ),
    },
    {
      title: "任务时间",
      width: "160px",
      dataIndex: "execTime",
      key: "execTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.execTime) - timeStamp(b.execTime),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await getInfo(id);
    await fetchList({ initId: id });
  };

  const getInfo = async (initId) => {
    setLoading(true);
    const data = {
      groupId: initId || id,
    };
    await apiCall("/group/getGroupInfo", "GET", data)
      .then((res) => {
        setUserInfo(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = async (params = {}, index = tabsIndex) => {
    setLoading(true);
    const { initId, pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      groupId: initId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    let apiUrl = "/group/log/groupLog/pageByGroupId";
    if (index == 1) {
      apiUrl = "/group/log/groupLog/pageByGroupId";
    } else if (index == 2) {
      apiUrl = "/group/getGroupMemberInfo";
    } else if (index == 3) {
      apiUrl = "/group_sop/group_new";
    } else if (index == 4) {
      apiUrl = "/employee_task/group_new";
    }
    await apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        if (params.push) {
          setDataSource(dataSource.concat(records));
        } else {
          setDataSource(records);
        }
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDetails = (record) => {
    const { userId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${userId}`,
    });
  };

  const onChangeTabs = (index) => {
    setDataSource([])
    setTabsIndex(index);
    const params = {
      pagination: {
        current: 1,
        pageSize: 10,
      },
    };
    if (index == '2') {
      let timer = setTimeout(() => {
        handleQueryGroupMember({}, index);
        clearTimeout(timer)
      }, 300);
    } else {
      fetchList(params, index);
    }
  };

  // 导出成员及动态
  const handleExcel = () => {
    setLoading(true);
    const data = {
      id,
    };
    apiCall("/group/data/export", "GET", data, null, {
      isExit: true,
      title: `群成员及动态.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination }, tabsIndex);
  };

  const loadMoreBtn = () => (
    <Button
      style={{ display: "flex", margin: "12px auto 0" }}
      type="primary"
      onClick={(e) => handleLoadMore()}
    >
      加载更多
    </Button>
  );

  const handleLoadMore = () => {
    const params = {
      push: true,
      pagination: {
        current: paginations.current + 1,
        pageSize: paginations.pageSize,
      },
    };
    fetchList(params, tabsIndex);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const handleQueryGroupMember = (params, index) => {
    GroupMemberFormRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD HH:mm"
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD HH:mm"
        );
        delete formData.createTime;
      }
      fetchList({
        ...params,
        query: {
          ...formData,
        }
      }, index);
    });
  }

  return (
    <div className="groupManagementDetail">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card
              title="客户群详情（基础信息）"
              extra={
                <Button type="primary" onClick={() => handleGoBack()}>
                  返回
                </Button>
              }
            >
              {userInfo ? (
                <>
                  <div className="info">
                    <div className="groupName">{userInfo.name}</div>
                    <div className="info-col">
                      <span>群主：</span>
                      <div className="avatar-name">
                        <Avatar
                          shape="square"
                          size="small"
                          src={userInfo.leader.avatar}
                        />
                        <span style={{ verticalAlign: "middle" }}>
                          {userInfo.leader.name}
                        </span>
                      </div>
                    </div>
                    <div className="info-col" style={{ margin: "0" }}>
                      <span>管理员：</span>
                      <div className="avatar-box">
                        {userInfo.managerList?.map((item, index) => (
                          <div key={index} className="avatar-name">
                            <Avatar
                              shape="square"
                              size="small"
                              src={item.avatar}
                            />
                            <span style={{ verticalAlign: "middle" }}>
                              {item.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="info-col">
                      创建时间：
                      {userInfo.createTime
                        ? moment(userInfo.createTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )
                        : "-"}
                    </div>
                    {/* <div className='info-col'>上次发言时间：{userInfo.lastContactTime ? moment(userInfo.lastContactTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</div> */}
                    {/* <Button type="primary" className='info-btn' onClick={() => handleRecord()}>
                  聊天记录
                </Button> */}
                  </div>
                  <Row className="info-bot">
                    <Col span={4} className="info-nums">
                      <span className="nums">
                        {userInfo.memberStatVO.totalNumber || 0}
                      </span>
                      <p>总人数</p>
                    </Col>
                    <Col span={5} className="info-nums">
                      <span className="nums">
                        {userInfo.memberStatVO.totalCustomerNumber || 0}
                      </span>
                      <p>总客户数</p>
                    </Col>
                    <Col span={5} className="info-nums">
                      <span className="nums">
                        {userInfo.memberStatVO.todayAddNumber || 0}
                      </span>
                      <p>今日进群人数</p>
                    </Col>
                    <Col span={5} className="info-nums">
                      <span className="nums">
                        {userInfo.memberStatVO.todayRemovalNumber || 0}
                      </span>
                      <p>今日退群人数</p>
                    </Col>
                    <Col span={5} className="info-nums">
                      <span className="nums">
                        {userInfo.memberStatVO.todayActiveNumber || 0}
                      </span>
                      <p>今日活跃人数</p>
                    </Col>
                  </Row>
                </>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="群公告">
              {userInfo && userInfo.notice ? (
                <>{userInfo.notice}</>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无公告"
                />
              )}
            </Card>
          </Col>
        </Row>
        <Card bordered={false} className="groupTabInfo">
          <Tabs
            activeKey={tabsIndex}
            destroyInactiveTabPane
            onChange={onChangeTabs}
            tabBarExtraContent={
              <Button type="primary" onClick={handleExcel}>
                导出成员及动态
              </Button>
            }
          >
            <TabPane tab="群动态" key="1">
              {dataSource?.length > 0 ? (
                <Timeline mode="left">
                  {dataSource.map((item, index) => (
                    <Timeline.Item
                      key={index}
                      label={moment(item.createTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    >
                      <span className="title">
                        <SysDictLabel
                          dataset="WECOM_GROUP_LOG"
                          dictkey={item.type}
                        />
                      </span>
                      {/* 成员退群/成员入群 */}
                      {item.type == "DEL_MEMBER" ||
                        item.type == "ADD_MEMBER" ||
                        item.type == "DEL_MANAGER" ||
                        item.type == "ADD_MANAGER" ? (
                        <Row
                          gutter={16}
                          style={{ position: "relative", marginTop: "10px" }}
                        >
                          {item.detail.avatar && (
                            <Col>
                              <Avatar
                                shape="square"
                                size={30}
                                src={
                                  <FileHOC src={item.detail.avatar || "error"}>
                                    {(url) => (
                                      <Image
                                        src={url}
                                        fallback="images/fallbackImg.png"
                                        preview={false}
                                      />
                                    )}
                                  </FileHOC>
                                }
                              />
                            </Col>
                          )}
                          <Col span={13}>
                            <span>{item.detail.name}</span>
                            <span
                              style={
                                item.detail.type == 1
                                  ? { color: "#f59a23" }
                                  : { color: "#07c160" }
                              }
                            >
                              {item.detail.suffix}
                            </span>
                            {item.detail.gender == 1 ? (
                              <IconFont
                                type="icon-228yonghu_xingbienan"
                                style={{ color: "#1989fa" }}
                              />
                            ) : item.detail.gender == 2 ? (
                              <IconFont
                                type="icon-229yonghu_xingbienv"
                                style={{ color: "#ee0a24" }}
                              />
                            ) : (
                              ""
                            )}
                            {item.type == "DEL_MEMBER" ? (
                              <div style={{ display: "flex" }}>
                                退群方式：
                                <SysDictLabel
                                  dataset="WECOM_QUITSCENE"
                                  dictkey={item.detail.quitScene}
                                />
                              </div>
                            ) : item.type == "ADD_MEMBER" ? (
                              <div>进群方式：{item.detail.joinScene}</div>
                            ) : (
                              ""
                            )}
                          </Col>
                        </Row>
                      ) : (
                        ""
                      )}
                      {/* 群主变更 */}
                      {item.type == "CHANGE_OWNER" ? (
                        <ul className="content">
                          <li>群主名称：{item.detail.name}</li>
                          <li>变更前：{item.detail.beforeValue}</li>
                          <li>变更后：{item.detail.laterValue}</li>
                        </ul>
                      ) : (
                        ""
                      )}
                      {/* 群公告变更 */}
                      {item.type == "CHANGE_NOTICE" ? (
                        <ul className="content">
                          <li>群公告：{item.detail.name}</li>
                          <li>变更前：{item.detail.beforeValue}</li>
                          <li>变更后：{item.detail.laterValue}</li>
                        </ul>
                      ) : (
                        ""
                      )}
                      {/* 群名变更 */}
                      {item.type == "CHANGE_NAME" ? (
                        <ul className="content">
                          <li>群组名称：{item.detail.name}</li>
                          <li>变更前：{item.detail.beforeValue}</li>
                          <li>变更后：{item.detail.laterValue}</li>
                        </ul>
                      ) : (
                        ""
                      )}
                      {/* 创建/解散群组 */}
                      {/* 添加/删除管理员 */}
                    </Timeline.Item>
                  ))}
                  {paginations.current * paginations.pageSize >=
                    paginations.total
                    ? null
                    : loadMoreBtn()}
                </Timeline>
              ) : (
                <Empty
                  description="暂无客户动态"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </TabPane>
            <TabPane tab="群成员" key="2">
              <FilterBar bodyStyle={{ padding: "unset" }}>
                <Form
                  layout={"inline"}
                  ref={GroupMemberFormRef}
                >
                  <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
                    <Input placeholder="成员名称" allowClear />
                  </FormItem>

                  <FormItem name="inGroup" label="成员状态" initialValue={true}>
                    <Radio.Group>
                      <Radio value={true}> 在群 </Radio>
                      <Radio value={false}> 退群 </Radio>
                    </Radio.Group>
                  </FormItem>

                  <FormItem name="createTime" label="进群时间">
                    <RangePicker showTime={{ format: 'HH:mm' }} />
                  </FormItem>
                </Form>
                <div className="flex flex-space-between">
                  <div>
                    <Button type="primary" onClick={() => handleQueryGroupMember()}>
                      查询
                    </Button>
                    <Button onClick={() => {
                      GroupMemberFormRef.current.resetFields();
                      handleQueryGroupMember();
                    }}>重置筛选</Button>
                  </div>
                </div>
              </FilterBar>
              <Table
                rowKey="id"
                loading={loading}
                dataSource={dataSource}
                columns={GroupMemberFormRef?.current?.getFieldValue('inGroup') == true ? JoinColumns : QuitColumns}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={(pagination, filters, sorter) => {
                  handleQueryGroupMember({ pagination }, tabsIndex);
                }}
              />
            </TabPane>
            <TabPane tab="群SOP" key="3">
              <Table
                rowKey="id"
                loading={loading}
                dataSource={dataSource}
                columns={sopColumns}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
            <TabPane tab="群任务" key="4">
              <Table
                rowKey="id"
                loading={loading}
                dataSource={dataSource}
                columns={taskColumns}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(GroupManagementDetail);
