.groupManagementDetail {

  .ant-row {

    .ant-col {
      margin-bottom: 20px;

      .ant-card {
        height: 100%;
      }
    }
  }

  .info {
    position: relative;

    .groupName,
    .info-col {
      margin-bottom: 6px;
    }

    .avatar-name {
      display: inline-block;
      margin-right: 6px;

      .ant-avatar {
        margin-right: 6px;
      }
    }

    .info-col {
      color: #AAAAAA;
      display: flex;
      align-items: baseline;

      .avatar-box {
        display: inline-block;
        width: 80%;

        // overflow-x: scroll;
        // white-space: nowrap;
        // vertical-align: top;
        // padding-bottom: 6px;
        .avatar-name {
          margin-bottom: 6px;
        }

        // 滚动条整体部分
        &::-webkit-scrollbar {
          width: 6px; //对垂直方向滚动条
          height: 6px; //对水平方向滚动条
        }

        //滚动的滑块
        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background-color: rgba(0, 0, 0, .5) //滚动条的颜色
        }

        //内层滚动槽
        &::-webkit-scrollbar-track-piece {
          background-color: rgba(0, 0, 0, .1);
        }
      }
    }

    .info-btn {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .info-bot {
    background: #f2f2f2;

    .info-nums {
      text-align: center;
      color: #aaa;
      padding-top: 10px;
      font-size: 16px;
      margin: 0;

      .nums {
        font-size: 18px;
        color: #000;
        font-weight: bold;
      }
    }
  }

  .notice {
    position: relative;
  }

  .groupTabInfo {
    .ant-timeline {
      width: 400px;

      .ant-timeline-item {
        .ant-timeline-item-content {
          width: calc(66% - 14px);

          .title {
            font-weight: bold;
            display: flex;

            .name {
              font-weight: initial;
              color: #aaa;
              margin-left: 6px;
            }
          }

          .content {
            margin: 0;
            padding: 10px;
            border-radius: 6px;
            background: #f2f2f2;

            li {
              display: block;


            }

            p {
              margin: 0;
              display: flex;
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }

  .filter-bar {
    .ant-col {
      margin: 0;
    }
  }
}