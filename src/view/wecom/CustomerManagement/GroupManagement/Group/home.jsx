/*
 * @Author: LinQunXun
 * @Date: 2022-04-02 16:07:10
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024/07/03 09:39
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/Group/home.jsx
 * @Description: '客户管理-客户群'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  TreeSelect,
  DatePicker,
  message,
  Row,
  Col,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import moment from "moment";
import FilterBar from "components/FilterBar/FilterBar";
import { QuestionCircleOutlined } from "@ant-design/icons";
import AddLabelModal from "components/Modal/AddLabelModal/AddLabelModal";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import WibotTableTag from 'components/WibotTableTag/home';
import "./home.less";
import { debounce } from "common/fn"
import { findWithDeep } from "@/utils"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const { RangePicker } = DatePicker;

const GroupManagement = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [labelVisible, setLabelVisible] = useState(false);
  const [tagTreeData, setTagTreeData] = useState([]);
  const [customerContent, setCustomerContent] = useState(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [labelParams, setLabelParams] = useState({ visible: false });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "群主",
      width: "160px",
      dataIndex: "leaderName",
      key: "leaderName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "群管理员",
      width: "160px",
      dataIndex: "managerName",
      key: "managerName",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value?.split(",") || []} />,
    },
    {
      title: "群人数",
      width: "160px",
      dataIndex: "memberCount",
      key: "memberCount",
      align: "center",
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    // {
    //   title: '上次发言时间',
    //   width: '160px',
    //   dataIndex: 'lastMsgTime',
    //   key: 'lastMsgTime',
    //   align: 'center',
    //   sorter: (a, b) => timeStamp(a.lastMsgTime) - timeStamp(b.lastMsgTime)
    // },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户群详情</a>
        </>
      ),
    },
  ];
  const [statisticData, setStatisticData] = useState([
    {
      title: "总客户群数",
      nums: 0,
      describe: "所有客户群数量，解散也算在内",
      dNums: "今日新增：" + 0,
    },
    {
      title: "总群成员数",
      nums: 0,
      describe: "所有客户群的所有群成员数量",
      dNums: "今日新增：" + 0,
    },
    {
      title: "一周内活跃群数",
      nums: 0,
      describe: "所有客户群中，有成员发言的群数量",
    },
    {
      title: " 一周内群活跃人数",
      nums: 0,
      describe: "所有客户群中，有成员发言的群的发言成员数量",
    },
    {
      title: " 一周内进群人数",
      nums: 0,
      describe: "所有客户群中，有新成员进群的群数量",
    },
    {
      title: " 一周内退群人数",
      nums: 0,
      describe: "所有客户群中，有新成员退群的群数量",
      dNums:
        moment().subtract(6, "day").format("MM/DD") +
        "~" +
        moment().format("MM/DD"),
    },
  ]);

  useEffect(() => {
    getTagCategoryTreeTwo();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    getStatisticData();
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.lastMsgTime) {
        formData.startSayTime = moment(formData.lastMsgTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endSayTime = moment(formData.lastMsgTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.lastMsgTime;
      }
      // formData.tagNameList = getTagNameList(formData.tagNameList, labelTreeData)
      // formData.leaderDeptIdList = formData.leaderDeptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/group/page", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getStatisticData = () => {
    const data = {
      scope: "PC",
    };
    apiCall("/group/data", "GET", data)
      .then((res) => {
        const {
          allGroupCount,
          allMemberCount,
          todayGroupCount,
          todayMemberCount,
          weekActiveGroupCount,
          weekActiveMemberCount,
          weekInMemberCount,
          weekOutMemberCount,
        } = res;
        let newStatisticData = JSON.parse(JSON.stringify(statisticData));
        newStatisticData[0].nums = allGroupCount;
        newStatisticData[0].dNums = "今日新增：" + todayGroupCount;
        newStatisticData[1].nums = allMemberCount;
        newStatisticData[1].dNums = "今日新增：" + todayMemberCount;
        newStatisticData[2].nums = weekActiveGroupCount;
        newStatisticData[3].nums = weekActiveMemberCount;
        newStatisticData[4].nums = weekInMemberCount;
        newStatisticData[5].nums = weekOutMemberCount;
        setStatisticData(newStatisticData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const treeData = recursionTagKeyTreeDataTag(res);
        setTagTreeData(
          treeData.map((item, index) => ({
            ...item,
            checkable: false,
          }))
        );
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: treeData,
        //   },
        // ]);
        setLabelTreeData(treeData)
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 打标签
  const handleAddLabel = () => {
    setLabelVisible(true);
  };

  const handleAddLabelSubmit = (data) => {
    setConfirmLoading(true);
    const groupIdList = selectedRowKeys;
    const tagIdList = data.filter((item) => !!Number(item));
    const queryData = {
      groupIdList,
      tagNameList:data,
    };
    apiCall("/group/tag", "POST", queryData)
      .then((res) => {
        message.success("打标签成功");
        setSelectedRowKeys([]);
        setSelectedRows([]);
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
        setLabelVisible(false);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleDetails = (record) => {
    const { id } = record;
    // props.history.push(`/wecom/groupManagement/detail/${id}`);
    props.history.push({
      pathname: "/wecom/groupManagement/detail",
      search: `?id=${id}`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  };

  const hasSelected = selectedRowKeys.length > 0;
  const [treeExpandedKeys, setTreeExpandedKeys] = useState(["customer"])
  const onKeyDown = (e) => {
    if (e.key === "Enter") {
      // continue
      if (!e.target.value) { return }
      const find = findWithDeep(labelTreeData, 'title', e.target.value)
      if (!find) {
        onSearch(e.target.value)
        const value = formRef.current?.getFieldValue('tagNameList');
        const newValue = Array.isArray(value) ? [...value, e.target.value] : [e.target.value];
        formRef.current?.setFieldValue('tagNameList', newValue);
        e.target.value = ''
      }
    }
  }
  const onSearch = (label) => {
    if (!label) {
      return
    }
    handleOnSearch(label)
  }

  const handleOnSearch = debounce((label) => {
    setLabelTreeData((prevState) => {
      // 创建新数组并更新第一个节点的 children
      const newChildren = [
        {
          ...prevState[0],
          children: [
            ...prevState[0].children,
            {
              children: [],
              key: label,
              parentid: null,
              value: label,
              title: label,
            },
          ],
        },
        ...prevState.slice(1), // 保留其他项不变
      ]
      setTreeExpandedKeys((prevState) => [...prevState, label])
      return newChildren
    })
  }, 100)

  const onTreeExpand = (expandedKeys) => {
    setTreeExpandedKeys(prevState => expandedKeys);
  };

  const getTagNameList = (keys = [], data = []) => {
    if (keys.length === 0 || data.length === 0) { return [] }
    const result = [];

    function collectChildren (node) {
      if (node.children.length) {
        node.children.forEach(child => {
          collectChildren(child);
        });
      } else {
        result.push(node.key);
      }
    }

    function traverse (node) {
      if (keys.includes(node.key)) {
        collectChildren(node)
      } else {
        node.children.forEach(child => {
          traverse(child);
        });
      }
    }

    traverse(data && data[0]);
    return result
  }

  return (
    <div className="groupManagement">
      <Card style={{ marginBottom: "20px" }}>
        <Row>
          {statisticData?.map((item, index) => (
            <Col span={4} className="statistical-item" key={index}>
              <div className="nums">{item.nums}</div>
              <div className="text">
                <Tooltip title={item.describe}>
                  {item.title}
                  <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                </Tooltip>
              </div>
              <div className="text">
                {item.dNums ||
                  moment().subtract(7, "day").format("MM/DD") +
                  "~" +
                  moment().subtract(1, "day").format("MM/DD")}
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户群名称" allowClear />
          </FormItem>
          <FormItem
            name="leaderDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="群主" />
          </FormItem>
          {/* <FormItem name="tagNameList"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={treeExpandedKeys} */}
          {/*     treeExpandedKeys={treeExpandedKeys} */}
          {/*     onTreeExpand={onTreeExpand} */}
          {/*     onKeyDown={onKeyDown} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            labelTreeData={labelTreeData}
          />
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          {/* <FormItem name="lastMsgTime" label="上次发言时间">
            <RangePicker />
          </FormItem> */}
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button
              disabled={!hasSelected}
              type="primary"
              onClick={() => {
                handleAddLabel();
              }}
            >
              打标签
            </Button>
          </div>
        </div>
      </FilterBar>

      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <AddLabelModal
        visible={labelVisible}
        selectedData={selectedRows}
        confirmLoading={confirmLoading}
        onCancel={() => {
          setLabelVisible(false);
        }}
        handleSubmit={(data) => {
          handleAddLabelSubmit(data);
        }}
      />
      {/* <AddLabelModal
        visible={labelVisible}
        treeData={tagTreeData}
        customerContent={customerContent}
        confirmLoading={confirmLoading}
        onCancel={() => {
          setLabelVisible(false);
        }}
        handleSubmit={handleAddLabelSubmit}
      /> */}
    </div>
  );
};

export default withRouter(GroupManagement);
