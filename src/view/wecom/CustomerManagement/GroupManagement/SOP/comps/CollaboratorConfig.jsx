/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/06/13 09:28
 * @LastEditTime: 2023/12/18 09:52
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\GroupManagement\SOP\comps\CollaboratorConfig.jsx
 * @Description: '气氛烘托'
 */

import { CloseCircleOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Input,
  Button,
  Form,
  InputNumber,
  Upload,
  Typography,
  Divider,
  Spin,
} from "antd";
import { apiCall } from "common/utils";
import { normFile } from "common/regular";
import { compressImage, base64ToFile, beforeUpload } from "common/image";
import React, { useEffect, useState } from "react";
import MaterialModal from "components/Modal/MaterialModal/home";
import EditFormModal from "components/Modal/EditFormModal/home";
import WibotUpload from "components/WibotUpload/home";
import "./CollaboratorConfig.less";

const FormItem = Form.Item;
const { TextArea } = Input;
const { Paragraph } = Typography;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 8, offset: 4 },
  },
};

const CollaboratorConfig = (props) => {
  const { formRef } = props;
  const [loading, setLoading] = useState(false);
  const [editFormParams, setEditFormParams] = useState({ visible: false });
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  });

  const handleAddItem = () => {
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig") ?? [];
    if (!collaboratorConfigField.messages) {
      collaboratorConfigField.messages = [];
    }
    collaboratorConfigField.messages.push({
      intervalList: "",
      messageList: [],
    });
    formRef.current.setFieldsValue({
      collaboratorConfig: collaboratorConfigField,
    });
  };

  const handleRemoveItem = (index) => {
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig") ?? [];
    collaboratorConfigField.messages.splice(index, 1);
    formRef.current.setFieldsValue({
      collaboratorConfig: collaboratorConfigField,
    });
  };

  const handleAddMsg = (type, index) => {
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig") ?? [];
    console.log(collaboratorConfigField, "collaboratorConfigField");
    if (type == "Text") {
      collaboratorConfigField.messages[index].messageList.push({
        type: type,
        content: "",
      });
      formRef.current.setFieldsValue({
        collaboratorConfig: { ...collaboratorConfigField },
      });
    } else if (type == "Picture") {
      collaboratorConfigField.messages[index].messageList.push({
        type,
        imageUrl: "",
      });
      formRef.current.setFieldsValue({
        collaboratorConfig: { ...collaboratorConfigField },
      });
    }
  };

  const handleDelMsg = (index, andex) => {
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig");
    collaboratorConfigField.messages[index].messageList.splice(andex, 1);
    formRef.current.setFieldsValue({
      collaboratorConfig: collaboratorConfigField,
    });
  };

  const customRequest = (config, index, andex) => {
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig") ?? [];
    const File = config.file;
    // 用于图片上传中限制
    collaboratorConfigField.messages[index].messageList[andex].imageUrl =
      "loading";
    formRef.current.setFieldsValue({
      collaboratorConfig: collaboratorConfigField,
    });
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append("file", File); // 返回压缩后的图片
    const data = formData;
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res;
        collaboratorConfigField.messages[index].messageList[andex] = {
          type: "Picture",
          imageUrl: fileUrl,
          fileId: [fileId],
          picType: "creat",
        };
        formRef.current.setFieldsValue({
          collaboratorConfig: collaboratorConfigField,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onChangeUpload = (info, type = null) => {
    if (type == "creat") {
      return;
    }
  };

  const handleResetUpload = (e, index, andex) => {
    e.preventDefault(); // 阻止浏览器默认行为
    e.stopPropagation(); // 阻止事件冒泡
    let collaboratorConfigField =
      formRef.current.getFieldValue("collaboratorConfig") ?? [];
    collaboratorConfigField.messages[index].messageList[andex] = {
      type: "Picture",
      imageUrl: "",
    };
    formRef.current.setFieldsValue({
      collaboratorConfig: collaboratorConfigField,
    });
  };

  return (
    <div className="CollaboratorConfig">
      <Spin spinning={loading}>
        <Form.List name={["collaboratorConfig", "keys"]} initialValue={[""]}>
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field, index) => (
                <FormItem
                  {...(index === 0
                    ? formItemLayout
                    : formItemLayoutWithOutLabel)}
                  label={index === 0 ? "关键词" : ""}
                  tooltip="关键词只针对群内的员工生效！"
                  required
                  key={field.key}
                >
                  <FormItem
                    name={[field.name]}
                    rules={[{ required: true, message: "请输入关键词" }]}
                    style={{ marginBottom: "0px" }}
                  >
                    <TextArea allowClear />
                  </FormItem>
                  {fields.length > 1 && (
                    <CloseCircleOutlined
                      className="msg-delete-button"
                      onClick={() => remove(field.name)}
                    />
                  )}
                </FormItem>
              ))}
              <FormItem {...formItemLayoutWithOutLabel}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => add()}
                >
                  添加关键词
                </Button>
              </FormItem>
            </>
          )}
        </Form.List>
        <Form.List
          name={["collaboratorConfig", "messages"]}
        // initialValue={[{ intervalList: "", messageList: [] }]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              <FormItem label="烘托人">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddItem()}
                >
                  添加烘托人
                </Button>
              </FormItem>
              {fields.map((field, index) => (
                <FormItem
                  {...formItemLayout}
                  label={`烘托人${index + 1}`}
                  key={field.key}
                >
                  <div className="collaboratorConfig_item">
                    {/* <Row gutter={10}>
                      <Col>
                        <FormItem
                          label="发送间隔"
                          name={[field.name, "intervalList", index]}
                          rules={[
                            { required: true, message: "请输入发送间隔" },
                          ]}
                        >
                          <InputNumber min={0} addonAfter="秒" />
                        </FormItem>
                      </Col>
                    </Row> */}
                    <FormItem
                      label="发送内容"
                      required
                      extra="仅可添加一条发送的消息"
                    >
                      <div className="btn-flex">
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddMsg("Text", index)}
                        >
                          文本
                        </Button>
                        {/* <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddMsg('LINK_CARD', index)}>链接</Button> */}
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddMsg("Picture", index)}
                        >
                          图片
                        </Button>
                        {/* <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddMsg('material', index)}>从素材库选择</Button> */}
                      </div>
                    </FormItem>
                    {formRef.current
                      .getFieldValue("collaboratorConfig")
                      .messages[index].messageList?.map((item, andex) => (
                        <div key={andex} className="collaboratorConfig_content">
                          <FormItem
                            label="发送间隔"
                            name={[field.name, "intervalList", andex]}
                            labelCol={{ span: 4 }}
                          // rules={[
                          //   { required: true, message: "请输入发送间隔" },
                          // ]}
                          >
                            <InputNumber min={0} addonAfter="秒" />
                          </FormItem>
                          {item.type == "Text" ? (
                            <FormItem
                              name={[
                                field.name,
                                "messageList",
                                andex,
                                "content",
                              ]}
                              label="文本"
                              labelCol={{ span: 4 }}
                              className="collaboratorConfig_item_text"
                              rules={[
                                {
                                  required: true,
                                  message: `请输入300字以内的文本`,
                                },
                              ]}
                            >
                              <TextArea
                                showCount
                                maxLength={300}
                                autoSize={{ minRows: 4, maxRows: 6 }}
                                placeholder="请输入300字以内的文本"
                                allowClear
                              />
                            </FormItem>
                          ) : (
                            ""
                          )}

                          {item.type == "Picture" ? (
                            <FormItem
                              name={[
                                field.name,
                                "messageList",
                                andex,
                                "fileId",
                              ]}
                              label="图片"
                              valuePropName="msgList"
                              getValueFromEvent={normFile}
                              rules={[
                                { required: true, message: "请上传图片" },
                              ]}
                              extra="大小限制为2M，最多上传1张"
                              labelCol={{ span: 4 }}
                            >
                              <WibotUpload
                                fileList={item.imageUrl}
                                size={2}
                                deletable={false}
                                onDone={(params) => {
                                  const { fileUrl, fileId } = params;
                                  let collaboratorConfigField =
                                    formRef.current.getFieldValue(
                                      "collaboratorConfig"
                                    ) ?? [];
                                  collaboratorConfigField.messages[
                                    index
                                  ].messageList[andex] = {
                                    type: "Picture",
                                    imageUrl: fileUrl,
                                    fileId: [fileId],
                                    picType: "creat",
                                  };
                                  formRef.current.setFieldsValue({
                                    collaboratorConfig: collaboratorConfigField,
                                  });
                                }}
                              />
                            </FormItem>
                          ) : (
                            ""
                          )}
                          <Divider />
                          <CloseCircleOutlined
                            className="collaboratorConfig_msg_tagClose"
                            onClick={() => handleDelMsg(index, andex)}
                          />
                        </div>
                      ))}
                    <CloseCircleOutlined
                      className="collaboratorConfig_item_Close"
                      onClick={() => handleRemoveItem(index)}
                    />
                  </div>

                  {/* <CloseCircleOutlined
                    className="msg-delete-button"
                    onClick={() => handleRemoveItem(index)}
                  /> */}
                </FormItem>
              ))}
            </>
          )}
        </Form.List>
      </Spin>

      <MaterialModal params={materialModalParams} />
      <EditFormModal params={editFormParams} />
    </div>
  );
};

export default CollaboratorConfig;
