/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/06/14 10:17
 * @LastEditTime: 2024/01/12 10:57
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\GroupManagement\SOP\comps\KeysConfig.jsx
 * @Description: '关键词回复'
 */

import { CloseCircleOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Input,
  Button,
  Form,
  Divider,
  Upload,
  Image,
  Select,
  Spin,
} from "antd";
import { apiCall } from "common/utils";
import { removeInputEmpty, normFile } from "common/regular";
import { compressImage, base64ToFile, beforeUpload } from "common/image";
import React, { useEffect, useState } from "react";
import MaterialModal from "components/Modal/MaterialModal/home";
import LinkCard from "components/LinkCard/home";
import AppletCard from "components/AppletCard/home";
import WibotUpload from "components/WibotUpload/home";
import "./KeysConfig.less";
import { FileHOC } from "components/FileHOC/FileHOC";

const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 8, offset: 4 },
  },
};

const KeysConfig = (props) => {
  const { formRef, type } = props;
  const [loading, setLoading] = useState(false);
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  });

  // useEffect(() => {
  //   if (type == "GROUP_KEYS") {
  //     let keysConfigField = formRef.current.getFieldValue("keysConfig");
  //     if (keysConfigField?.messages?.length > 0) {
  //       keysConfigField.messages.forEach((item, index) => {
  //         item?.msgList?.forEach((atem, andex) => {
  //           if (atem.type == "Video") {
  //             atem.videoUrl = atem.fileId ? atem.fileId[0] : "";
  //             atem.transitUrl = atem.url;
  //           } else if (atem.type == "MINI_PROGRAM") {
  //             atem.miniProgram = { ...atem };
  //           } else {
  //             atem.imageUrl = atem.fileId ? atem.fileId[0] : "";
  //             atem.transitUrl = atem.url;
  //           }
  //         });
  //       });
  //       console.log(keysConfigField, "keysConfigField");
  //       formRef.current.setFieldsValue({
  //         keysConfig: keysConfigField,
  //       });
  //     }
  //   }
  // }, [type]);

  const handleAddItem = () => {
    let keysConfigField = formRef.current.getFieldValue("keysConfig");
    keysConfigField.messages.push({
      keys: [""],
      msgList: [],
    });
    formRef.current.setFieldsValue({
      keysConfig: keysConfigField,
    });
  };

  const handleRemoveItem = (index) => {
    let keysConfigField = formRef.current.getFieldValue("keysConfig");
    keysConfigField.messages.splice(index, 1);
    formRef.current.setFieldsValue({
      keysConfig: keysConfigField,
    });
  };

  const handleDelMsg = (index, andex) => {
    let keysConfigField = formRef.current.getFieldValue("keysConfig");
    keysConfigField.messages[index].msgList.splice(andex, 1);
    formRef.current.setFieldsValue({
      keysConfig: keysConfigField,
    });
  };

  const handleAddMsg = (type, index) => {
    let keysConfigField = formRef.current.getFieldValue("keysConfig");
    if (type == "material") {
      setMaterialModalParams({
        visible: true,
        limitNineFlag: false,
        onSubmit: (dataList) => {
          console.log(dataList, "dataList");
          setMaterialModalParams({ visible: false });
          dataList.forEach((data) => {
            let msg = "";
            switch (data.type) {
              case "copyWriter":
                data.content = data.copyWriter;
                break;
              case "pageArticle":
                msg = (data.images && data.images[0]) || "";
                data.imageUrl = msg;
                data.image = msg;
                data.fileId = data.images;
                break;
              case "Article":
                msg = (data.images && data.images[0]) || "";
                data.imageUrl = msg;
                data.image = msg;
                data.fileId = data.images;
                break;
              case "Video":
                data.videoUrl = (data.videos && data.videos[0]) || "";
                data.videos = data.videos || [];
                data.fileId = data.videos;
                break;
              case "Picture":
                console.log(data, "datadata");
                // data.images.forEach((item) => {
                //   item.isMaterial = true;
                // });
                data.imageUrl = (data.fileId && data.fileId[0]) || "";
                data.isMaterial = true;
                break;
              case "Poster":
                msg = (data.fileId && data.fileId[0]) || "";
                data.imageUrl = msg;
                data.fileId = data.fileId;
                break;
              case "FORM":
                delete data.extraSetting;
                delete data.jsonContent;
                delete data.commitActionContent;
                // data.imageUrl = data.shareImage || data.image;
                data.image = data.shareImage || data.image;
                data.title = data.shareTitle;
                data.description = data.shareDescription;
                break;
              case "Product":
                data.imageUrl = (data.images && data.images[0]) || "";
                break;
              case "MINI_PROGRAM":
                msg = (data.miniProgram && data.miniProgram.fileId) || "";
                data.fileId = data.miniProgram && [data.miniProgram.fileId];
                break;
            }
            if (data.type == "Picture") {
              // keysConfigField.messages[index].msgList = keysConfigField.messages[
              //   index
              // ].msgList.concat(data.images);
              keysConfigField.messages[index].msgList =
                keysConfigField.messages[index].msgList.concat(data);
            } else {
              keysConfigField.messages[index].msgList.push(data);
            }
            formRef.current.setFieldsValue({
              keysConfig: { ...keysConfigField },
            });
          });
        },
        onCancel: () => {
          setMaterialModalParams({ visible: false });
        },
      });
    } else if (type == "copyWriter") {
      keysConfigField.messages[index].msgList.push({
        type: type,
        content: "",
      });
      formRef.current.setFieldsValue({
        keysConfig: { ...keysConfigField },
      });
    } else if (type == "Picture") {
      keysConfigField.messages[index].msgList.push({
        type: type,
        imageUrl: "",
      });
      formRef.current.setFieldsValue({
        keysConfig: { ...keysConfigField },
      });
    } else if (type == "LINK_CARD") {
      keysConfigField.messages[index].msgList.push({
        type: type,
        url: "",
      });
      formRef.current.setFieldsValue({
        keysConfig: { ...keysConfigField },
      });
    }
  };

  const customRequest = (config, index, andex) => {
    let keysConfigField = formRef.current.getFieldValue("keysConfig");
    const File = config.file;
    // 用于图片上传中限制
    keysConfigField.messages[index].msgList[andex].imageUrl = "loading";
    formRef.current.setFieldsValue({
      keysConfig: keysConfigField,
    });
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append("file", File); // 返回压缩后的图片
    const data = formData;
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId } = res;
        keysConfigField.messages[index].msgList[andex] = {
          type: "Picture",
          imageUrl: result,
          fileId: [fileId],
          picType: "creat",
        };
        formRef.current.setFieldsValue({
          keysConfig: keysConfigField,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const onChangeUpload = (info, type = null) => {
    if (type == "creat") {
      return;
    }
  };

  // 输入框光标处手动添加内容
  const handleAddFocusContent = async (value, index, andex) => {
    const insertHTML = "#客户昵称#";
    const inputIndex = document.getElementById(`msgInput${index}${andex}`); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) return;
    // let newMsgList = [...msgList];
    let keysConfigField = formRef.current.getFieldValue("keysConfig") ?? [];
    const msgListField = keysConfigField.messages[index].msgList[andex];
    // newMsgList[index].content = value.substring(0, startPos) + insertHTML + value.substring(endPos);
    console.log(msgListField, "msgListFieldmsgListField");
    msgListField.content =
      value.substring(0, startPos) + insertHTML + value.substring(endPos);
    formRef.setFieldsValue({
      keysConfig: keysConfigField,
    });
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  return (
    <div className="KeysConfig">
      <Spin spinning={loading}>
        <Form.List
          name={["keysConfig", "messages"]}
          initialValue={[{ keys: [""], msgList: [] }]}
        >
          {(fields, { add0, remove0 }, { errors }) => (
            <>
              <FormItem label="回复规则">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddItem()}
                >
                  添加规则
                </Button>
              </FormItem>
              {fields.map((field, index) => (
                <FormItem
                  {...formItemLayout}
                  label={`规则${index + 1}`}
                  key={field.key}
                >
                  <div className="keysConfig_item">
                    <Form.List name={[field.name, "keys"]}>
                      {(fields1, { add, remove }, { errors }) => (
                        <>
                          {fields1.map((field1, index1) => (
                            <FormItem
                              {...(index1 === 0
                                ? formItemLayout
                                : formItemLayoutWithOutLabel)}
                              label={index1 === 0 ? "关键词" : ""}
                              tooltip="关键词只针对群内的客户生效！"
                              required
                              key={field1.key}
                            >
                              <FormItem
                                name={[field1.name]}
                                rules={[
                                  { required: true, message: "请输入关键词" },
                                ]}
                                style={{ marginBottom: "0px", width: "304px" }}
                              >
                                <TextArea allowClear />
                              </FormItem>
                              {fields1.length > 1 && (
                                <CloseCircleOutlined
                                  className="msg-delete-button"
                                  onClick={() => remove(field1.name)}
                                />
                              )}
                            </FormItem>
                          ))}
                          <FormItem {...formItemLayoutWithOutLabel}>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() => add()}
                            >
                              添加关键词
                            </Button>
                          </FormItem>
                        </>
                      )}
                    </Form.List>
                    {/* <FormItem
                      name={[field.name, "keys"]}
                      label="关键词"
                      rules={[{ required: true, message: "请输入关键词" }]}
                    >
                      <Select
                        mode="tags"
                        tokenSeparators={[",", "，", "、"]}
                        allowClear
                        placeholder="关键词"
                      />
                    </FormItem> */}
                    <FormItem label="回复内容" extra="请按发送顺序添加回复内容">
                      <div className="btn-flex">
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddMsg("copyWriter", index)}
                        >
                          文案
                        </Button>
                        {/* <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddMsg('LINK_CARD', index)}>链接</Button> */}
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddMsg("Picture", index)}
                        >
                          图片
                        </Button>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddMsg("material", index)}
                        >
                          从素材库选择
                        </Button>
                      </div>
                    </FormItem>
                    <div className="msgList">
                      {formRef.current
                        .getFieldValue("keysConfig")
                        .messages[index].msgList?.map((item, andex) =>
                          item ? (
                            <div key={andex} className="template">
                              <span className="msg_title">{andex + 1}</span>
                              {item.type == "copyWriter" ? (
                                <FormItem
                                  name={[index, "msgList", andex, "content"]}
                                  label="文案"
                                  labelCol={{ span: 5 }}
                                  className="keysConfig_item_text"
                                  rules={[
                                    {
                                      required: true,
                                      message: `请输入300字以内的文案`,
                                    },
                                  ]}
                                  getValueFromEvent={(e) => removeInputEmpty(e)}
                                  initialValue={item.content}
                                >
                                  <TextArea
                                    id={`msgInput${index}${andex}`}
                                    showCount
                                    maxLength={300}
                                    autoSize={{ minRows: 4, maxRows: 6 }}
                                    placeholder={`请输入300字以内的文案`}
                                    allowClear
                                  />
                                </FormItem>
                              ) : (
                                ""
                              )}
                              {item.type == "Picture" ? (
                                <FormItem
                                  name={[index, "msgList", andex, "imageUrl"]}
                                  //  className='formItem_noMargin'
                                  valuePropName="msgList"
                                  getValueFromEvent={normFile}
                                  label="图片"
                                  labelCol={{ span: 5 }}
                                  rules={[
                                    {
                                      required: true,
                                      message: "请上传图片",
                                    },
                                  ]}
                                  extra="大小限制为2M，最多上传1张"
                                >
                                  <WibotUpload
                                    fileList={item.imageUrl}
                                    size={2}
                                    deletable={false}
                                    onDone={(params) => {
                                      const { fileUrl, fileId } = params;
                                      let keysConfigField =
                                        formRef.current.getFieldValue(
                                          "keysConfig"
                                        );
                                      keysConfigField.messages[index].msgList[
                                        andex
                                      ] = {
                                        type: "Picture",
                                        imageUrl: fileUrl,
                                        fileId: [fileId],
                                        picType: "creat",
                                      };
                                      console.log(
                                        keysConfigField,
                                        "keysConfigField"
                                      );
                                      formRef.current.setFieldsValue({
                                        keysConfig: keysConfigField,
                                      });
                                    }}
                                  />
                                </FormItem>
                              ) : (
                                ""
                              )}
                              {item.type == "Article" ||
                                item.type == "FORM" ||
                                item.type == "SURVEY" ||
                                item.type == "Product" ||
                                item.type == "pageArticle" ? (
                                <FormItem
                                  label={
                                    item.type == "Article"
                                      ? "推文"
                                      : item.type == "FORM"
                                        ? "表单"
                                        : item.type == "Product"
                                          ? "产品"
                                          : item.type == "pageArticle"
                                            ? "文章"
                                            : "问卷"
                                  }
                                  labelCol={{ span: 5 }}
                                >
                                  <LinkCard data={item} />
                                </FormItem>
                              ) : (
                                ""
                              )}
                              {item.type == "Video" ? (
                                <FormItem labelCol={{ span: 5 }} label="视频">
                                  <FileHOC src={item?.videoUrl}>
                                    {(url) => (
                                      <video
                                        controls
                                        src={url}
                                        style={{ width: "100px", height: "100px" }}
                                      />
                                    )}
                                  </FileHOC>
                                </FormItem>
                              ) : (
                                ""
                              )}
                              {item.type == "Poster" ||
                                item.type == "QrCode" ? (
                                <FormItem label="海报" labelCol={{ span: 5 }}>
                                  <WibotUpload
                                    fileList={item.imageUrl}
                                    deletable={false}
                                    onDone={(params) => {
                                      const { fileUrl, fileId } = params;
                                      let keysConfigField =
                                        formRef.current.getFieldValue(
                                          "keysConfig"
                                        );
                                      keysConfigField.messages[index].msgList[
                                        andex
                                      ].imageUrl = fileUrl;
                                      keysConfigField.messages[index].msgList[
                                        andex
                                      ].fileId = [fileUrl];
                                      formRef.current.setFieldsValue({
                                        keysConfig: keysConfigField,
                                      });
                                    }}
                                  />
                                </FormItem>
                              ) : (
                                ""
                              )}
                              {item.type == "MINI_PROGRAM" ? (
                                <FormItem
                                  label="小程序"
                                  labelCol={{ span: 5 }}
                                  className="formItem_noMargin"
                                >
                                  <AppletCard data={item.miniProgram || {}} />
                                </FormItem>
                              ) : (
                                ""
                              )}
                              <CloseCircleOutlined
                                className="msg_tagClose"
                                onClick={() => handleDelMsg(index, andex)}
                              />
                            </div>
                          ) : (
                            ""
                          )
                        )}
                    </div>
                    <Divider />
                    {index != 0 ? (
                      <CloseCircleOutlined
                        className="keysConfig_item_Close"
                        onClick={() => handleRemoveItem(index)}
                      />
                    ) : (
                      ""
                    )}
                  </div>
                </FormItem>
              ))}
            </>
          )}
        </Form.List>
      </Spin>

      <MaterialModal params={materialModalParams} />
    </div>
  );
};

export default KeysConfig;
