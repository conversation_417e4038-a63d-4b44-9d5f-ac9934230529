.KeysConfig {
  .btn-flex {
    // display: flex;
    // flex-direction: row;
    // justify-content: space-between;
    // width: 100%;

    button {
      margin-right: 10px;

      // &:last-child {
      //   margin-right: 0;
      // }
    }
  }

  .msg-delete-button {
    cursor: pointer;
    font-size: 20px;
    color: #d9d9d9;
    position: absolute;
    top: 58px;
    right: -229px;

    &:hover {
      color: #1989fa;
    }
  }

  .keysConfig_item {
    position: relative;
    width: 500px;
    box-shadow: 1px 2px 3px 0px #ccc;
    padding: 10px;
    border-top: 1px solid #f0f0f0;

    .keysConfig_item_text {
      .ant-form-item-control-input-content {
        max-width: 80%;
      }
    }

    .keysConfig_item_Close {
      position: absolute;
      top: 0px;
      right: -40px;
      font-size: 20px;
      color: #d9d9d9;
      cursor: pointer;

      &:hover {
        color: #1989fa;
      }
    }
  }

  .msgList {
    // max-height: 300px;
    // overflow-y: scroll;

    .template {
      position: relative;
      border-top: 1px solid #f1f2f4;
      padding: 20px 0;

      &:first-child {
        border: unset;
      }

      .ant-row {
        margin-bottom: unset;
      }

      .msg_title {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        position: absolute;
        left: 0px;
        top: 24px;
      }

      .msg_tagClose {
        cursor: pointer;
        font-size: 20px;
        color: #d9d9d9;
        position: absolute;
        top: 25px;
        right: 32px;

        &:hover {
          color: #1989fa;
        }
      }

      .ant-input-affix-wrapper {
        width: 304px;
      }
    }
  }
}