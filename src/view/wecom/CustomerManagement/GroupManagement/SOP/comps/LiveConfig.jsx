/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/06/13 17:27
 * @LastEditTime: 2023/09/06 14:46
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/SOP/comps/LiveConfig.jsx
 * @Description: '课程讲解'
 */

import { CloseCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Input, Button, Form, InputNumber, Spin } from "antd";
import React, { useState } from "react";
import "./LiveConfig.less";

const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};

const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 8, offset: 4 },
  },
};

const LiveConfig = (props) => {
  const [loading, setLoading] = useState(false);

  return (
    <div className="LiveConfig">
      <Spin spinning={loading}>
        <Form.List name={["liveConfig", "keys"]} initialValue={[""]}>
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field, index) => (
                <FormItem
                  {...(index === 0
                    ? formItemLayout
                    : formItemLayoutWithOutLabel)}
                  label={index === 0 ? "口令" : ""}
                  tooltip="口令只针对群内员工生效，并且需要与消息完全匹配！"
                  key={field.key}
                  required
                >
                  <FormItem
                    name={[field.name]}
                    rules={[{ required: true, message: "请输入口令" }]}
                    style={{ marginBottom: "0px" }}
                  >
                    <TextArea allowClear />
                    {/* <Input allowClear/> */}
                  </FormItem>
                  {fields.length > 1 && (
                    <CloseCircleOutlined
                      className="msg-delete-button"
                      onClick={() => remove(field.name)}
                    />
                  )}
                </FormItem>
              ))}
              <FormItem {...formItemLayoutWithOutLabel}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => add()}
                >
                  添加口令
                </Button>
              </FormItem>
            </>
          )}
        </Form.List>
        <FormItem
          label="默认发送间隔"
          name={["liveConfig", "interval"]}
          // rules={[{ required: true, message: '请输入默认发送间隔' }]}
          extra="如果回复消息的发送间隔不填，则使用默认发送间隔"
        >
          <InputNumber addonAfter="秒" />
        </FormItem>
      </Spin>
    </div>
  );
};

export default LiveConfig;
