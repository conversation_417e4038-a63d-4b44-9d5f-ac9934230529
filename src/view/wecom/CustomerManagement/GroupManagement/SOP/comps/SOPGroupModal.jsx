/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/04/24 15:11
 * @LastEditTime: 2023/11/17 15:49
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/SOP/comps/SOPGroupModal.jsx
 * @Description: '管理SOP组'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, Button, Table, Input, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import OperateModal from 'components/Modal/OperateModal/index';
import ListOperation from 'components/ListOperation/home';

const SOPGroupModal = (props) => {
  const { visible, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [initDataSource, setInitDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '名称',
      width: '170px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Input
              maxLength={15}
              value={value}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(dataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, '');
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入15字内"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          )}
        </>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleCopy(record, index), name: "复制" });
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const [operateParams, setOperateParams] = useState({ visible: false });

  useEffect(() => {
    if (visible) {
      getGroupOptions();
    }
  }, [visible]);

  const getGroupOptions = () => {
    setLoading(true);
    apiCall('/sopGroup', 'GET')
      .then((res) => {
        setDataSource(res);
        setInitDataSource(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = (record = {}) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    const { copyId = null, name = '' } = record;
    newDataSource.unshift({
      name: name,
      isEdit: true,
      copyId: copyId,
    });
    setDataSource([...newDataSource]);
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { id, name, copyId } = record;
    if (!name) {
      message.warning('名称不能为空！');
      return false;
    }
    setLoading(true);
    if (copyId) {
      apiCall(`/sopGroup/copy/${copyId}?name=${name}`, 'POST')
        .then((res) => {
          message.success('复制成功！');
          getGroupOptions();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      apiCall('/sopGroup', 'POST', {
        id: id || null,
        name: name,
      })
        .then((res) => {
          message.success('保存成功！');
          getGroupOptions();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/sopGroup/delete/${id}`, 'POST')
      .then((res) => {
        message.success('删除成功！');
        let newDataSource = dataSource;
        newDataSource.splice(index, 1);
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index) => {
    const { id } = record;
    let newDataSource = dataSource;
    if (id) {
      newDataSource[index].isEdit = false;
      const findData = initDataSource.find(item => item.id == id);
      findData && (newDataSource[index].name = findData.name);
    } else {
      newDataSource.splice(index, 1);
    }
    setDataSource([...newDataSource]);
  };

  const handleCopy = async (record) => {
    const { id, name } = record;
    setOperateParams({
      visible: true,
      title: '复制SOP组确认',
      content: `本次复制将对客户群SOP组【${name}】内所有SOP脚本进行复制，复制后需要修改并保存SOP组名称才能生效，确认继续吗？`,
      onSubmit: async () => {
        setOperateParams({
          visible: false,
        })
        handleAdd({ copyId: id, name });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  return (
    <Modal
      visible={visible}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          SOP分组
          <Button
            style={{ marginRight: '20px' }}
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd()}
          >
            新增
          </Button>
        </div>
      }
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      centered
      onCancel={() => {
        setLoading(false);
        setDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ y: 500 }}
      />
      <OperateModal params={operateParams} />
    </Modal>
  );
};

export default SOPGroupModal;
