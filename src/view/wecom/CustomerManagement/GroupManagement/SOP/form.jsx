/*
 * @Author: LinQunXun
 * @Date: 2022-04-02 17:04:35
 * @LastEditTime: 2025/07/15 16:07
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/SOP/form.jsx
 * @Description: '客户群SOP-表单(新增/编辑)'
 */

import {
  Button,
  Card,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Select,
  Space,
  Spin,
  TimePicker,
  Typography,
  Table,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  CloseCircleOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { qs2obj } from "common/object";
import CollaboratorConfig from "./comps/CollaboratorConfig";
import LiveConfig from "./comps/LiveConfig";
import KeysConfig from "./comps/KeysConfig";
import { clearCache } from "react-router-cache-route";
import MaterialListForm from "components/MaterialListForm/home";
import CTypeTransferModal from "components/TransferModal/CustomerType/home";
import { timeStamp } from "common/date";
import "./form.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const timeFormat = "HH:mm";
const dateFormat = "YYYY-MM-DD";
const dateTimeFormat = "YYYY-MM-DD HH:mm";
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 8, offset: 4 },
  },
};

const GroupSOPForm = (props) => {
  const formRef = useRef(null);
  const onRefMaterialListForm = useRef();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [groupOptions, setGroupOptions] = useState([]);
  const [selectType, setSelectType] = useState("GROUP_NEW_MEMBER");
  const [msgList, setMsgList] = useState([]);
  const [msgText, setMsgText] = useState("欢迎语");
  // const [msgConfigData, setMsgConfigData] = useState([]);
  const [SOPGroupOptions, setSOPGroupOptions] = useState([]);

  const types = [
    {
      title: "新人进群",
      describe: "当有新人进群时，发送欢迎语。",
      type: "GROUP_NEW_MEMBER",
    },
    {
      title: "定时推送",
      describe: "按照设定好的周期时间在群内发送消息。",
      type: "GROUP_TIMED_PUSH",
    },
    {
      title: "违规监测",
      describe: "当有客户在群内发送违规消息，会及时提醒员工进行处理。",
      type: "GROUP_VIOLATION",
    },
    {
      title: "气氛烘托",
      describe:
        "当群内需要活跃气氛时，员工将会安排在群里发送一些话术活跃气氛。",
      type: "GROUP_COLLABORATOR",
    },
    {
      title: "课程讲解",
      describe: "在群内直播课程。",
      type: "GROUP_LIVE",
    },
    {
      title: "关键词回复",
      describe: "当客户的消息匹配关键词时，自动回复客户。",
      type: "GROUP_KEYS",
    },
  ];
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  });
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "60px",
      align: "center",
      render: (text, record, index) => index + 1,
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "原归属人（群主）",
      width: "160px",
      dataIndex: "leaderName",
      key: "leaderName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "群人数",
      width: "160px",
      dataIndex: "memberCount",
      key: "memberCount",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a
            onClick={() => {
              const newDataSource = dataSource;
              newDataSource.splice(newDataSource.indexOf(record), 1);
              formRef.current.setFieldsValue({
                groupIds: newDataSource.map((item) => item.id),
              });
              setDataSource([...newDataSource]);
              setPaginations({
                ...paginations,
                total: newDataSource.length,
              });
            }}
          >
            删除
          </a>
        </>
      ),
    },
  ];

  useEffect(() => {
    setLoading(true);
    // initGroupOptions();
    getSopGroupOptions();
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      getInfoData(id);
    }
  }, []);

  // 获取SOP组
  const getSopGroupOptions = async () => {
    setLoading(true);
    await apiCall("/sopGroup", "GET")
      .then((res) => {
        setSOPGroupOptions(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const initGroupOptions = () => {
    const data = {
      data: null,
    };
    apiCall("/group/option", "GET", data)
      .then((res) => {
        setGroupOptions(
          res.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchGroupList = (params = {}) => {
    setLoading(true);
    const { query } = params;
    const data = {
      paged: false,
      ...query,
    };
    apiCall("/group/page", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: 10,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = (id) => {
    setLoading(true);
    const data = {
      id: id,
    };
    apiCall("/group_sop/get", "GET", data)
      .then((res) => {
        let messageList = [];
        let scrollIndex = 0;
        if (res.type == "GROUP_NEW_MEMBER") {
          res.newMemberConfig.interval = res.newMemberConfig.interval / 60;
          res.newMemberConfig.silentTimeRange =
            res.newMemberConfig.silentTimeRange.map((item) =>
              moment(item, timeFormat)
            );
          messageList = res.newMemberConfig.messageList ?? [];
        } else if (res.type == "GROUP_TIMED_PUSH") {
          if (res.timedPushConfig.cycle) {
            res.timedPushConfig.dateRange = res.timedPushConfig.dateRange.map(
              (item) => moment(item, dateFormat)
            );
            res.timedPushConfig.time = moment(
              res.timedPushConfig.time,
              timeFormat
            );
          } else {
            res.timedPushConfig.sendDateTime = moment(
              res.timedPushConfig.sendDateTime,
              dateTimeFormat + ":ss"
            );
          }
          const info = res.timedPushConfig.messageList;
          res.intervalList = res.timedPushConfig.intervalList.map((item) => {
            if (item == -1) {
              return null;
            } else {
              return item;
            }
          });
          messageList = info ?? [];
        } else if (res.type == "GROUP_VIOLATION") {
          messageList = res.violationConfig.messageList ?? [];
        } else if (res.type == "GROUP_COLLABORATOR") {
          res.collaboratorConfig.messages.forEach((item) => {
            item.messageList.forEach((atem) => {
              if (atem.type == "Picture") {
                atem.imageUrl = atem.fileId && atem.fileId[0];
              }
            });
            item.intervalList = item.intervalList.map((item) => {
              if (item == -1) {
                return null;
              } else {
                return item;
              }
            });
          });
          scrollIndex = 3;
        } else if (res.type == "GROUP_LIVE") {
          const info = res.liveConfig.messageList;
          res.intervalList = res.liveConfig.intervalList.map((item) => {
            if (item == -1) {
              return null;
            } else {
              return item;
            }
          });
          messageList = info ?? [];
          scrollIndex = 4;
        } else if (res.type == "GROUP_KEYS") {
          // 关键词回复
          res.keysConfig.messages = res.keysConfig.rules;
          res.keysConfig.messages.forEach((item, index) => {
            item.messageList.forEach((atem) => {
              if (atem.type == "Video") {
                atem.videoUrl = atem.fileId ? atem.fileId[0] : "";
                atem.transitUrl = atem.url;
              } else if (atem.type == "MINI_PROGRAM") {
                atem.miniProgram = { ...atem };
              } else {
                atem.imageUrl = atem.fileId ? atem.fileId[0] : "";
                atem.transitUrl = atem.url;
              }
            });
            item.msgList = [...item.messageList];
            delete item.messageList;
          });
          scrollIndex = 5;
        }
        setSelectType(res.type);
        res.groupIds = res.groups.map((g) => g.id);
        fetchGroupList({ query: { idList: res.groupIds.join(",") } });
        formRef.current.setFieldsValue({ ...res });

        getMsgText(res.type);
        onRefMaterialListForm.current?.getInitMsgList(messageList);

        if (scrollIndex >= 3) {
          document.querySelector(".scrollList").scrollLeft =
            document.querySelectorAll(".listItem")[scrollIndex].offsetLeft -
            400;
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formRef.current.validateFields().then((formData) => {
      if (
        (selectType == "GROUP_NEW_MEMBER" ||
          selectType == "GROUP_TIMED_PUSH" ||
          selectType == "GROUP_VIOLATION") &&
        !msgList.length > 0
      ) {
        message.warning(`${msgText}不能为空！`);
        return false;
      }
      if (
        selectType == "GROUP_KEYS" &&
        !formData.keysConfig.messages.every((item) => item.msgList.length > 0)
      ) {
        message.warning("回复内容不能为空！");
        return false;
      }
      setLoading(true);
      const data = {
        id: id,
        ...formData,
      };

      const messageList = onRefMaterialListForm.current?.getModifyMsgList();
      if (data.type == "GROUP_NEW_MEMBER") {
        data.newMemberConfig.interval = data.newMemberConfig.interval * 60;
        data.newMemberConfig.silentTimeRange =
          data.newMemberConfig.silentTimeRange.map((item) =>
            item.format(timeFormat)
          );
        data.newMemberConfig.messageList = messageList;
      } else if (data.type == "GROUP_TIMED_PUSH") {
        if (data.timedPushConfig.cycle) {
          data.timedPushConfig.dateRange = data.timedPushConfig.dateRange.map(
            (item) => item.format(dateFormat)
          );
          data.timedPushConfig.time =
            data.timedPushConfig.time.format(timeFormat);
          data.timedPushConfig.weekday = data.timedPushConfig.weekday || [];
          data.timedPushConfig.month = data.timedPushConfig.month || [];
          data.timedPushConfig.year = data.timedPushConfig.year || [];
        } else {
          data.timedPushConfig.sendDateTime =
            data.timedPushConfig.sendDateTime.format(dateTimeFormat + ":ss");
        }
        data.timedPushConfig.messageList = messageList;
        data.timedPushConfig.intervalList = formData.intervalList.map(
          (item) => {
            if (item == null) {
              return -1;
            } else {
              return item;
            }
          }
        );
      } else if (data.type == "GROUP_VIOLATION") {
        data.violationConfig.messageList = messageList;
      } else if (data.type == "GROUP_COLLABORATOR") {
        data.collaboratorConfig.messages.forEach((item) => {
          item.intervalList = item.intervalList.map((item) => {
            if (item == null) {
              return -1;
            } else {
              return item;
            }
          });
        });
      } else if (data.type == "GROUP_LIVE") {
        data.liveConfig.messageList = messageList;
        data.liveConfig.intervalList = formData.intervalList.map((item) => {
          if (item == null) {
            return -1;
          } else {
            return item;
          }
        });
      } else if (data.type == "GROUP_KEYS") {
        data.keysConfig.rules = [...formData.keysConfig.messages];
        data.keysConfig.rules.forEach((item, index) => {
          item.messageList = item.msgList.map((atem, andex) => {
            let msg = {};
            switch (atem.type) {
              case "copyWriter":
                msg = {
                  ...atem,
                };
                break;
              case "pageArticle":
                msg = {
                  id: atem.id,
                  title: atem.title,
                  description: atem.description,
                  fileId: atem.fileId,
                  image: atem.fileId && atem.fileId[0],
                  url: atem.transitUrl || atem.url,
                  type: "pageArticle",
                };
                break;
              case "Picture":
                msg = {
                  id: atem.id ?? null,
                  fileId: atem.fileId ? atem.fileId : null,
                  image: atem.isMaterial ? atem.fileId[0] : atem.imageUrl,
                  type: "Picture",
                };
                break;
              case "Article":
                msg = {
                  id: atem.id,
                  title: atem.title,
                  description: atem.description,
                  fileId: atem.fileId,
                  image: atem.fileId && atem.fileId[0],
                  url: atem.transitUrl || atem.url,
                  type: "Article",
                };
                break;
              case "Video":
                msg = {
                  id: atem.id,
                  fileId: atem.fileId,
                  url: atem.transitUrl || atem.url,
                  type: "Video",
                };
                break;
              case "Product":
                msg = {
                  id: atem.id,
                  fileId: atem.images ? atem.images : atem.fileId,
                  url: atem.transitUrl || atem.url,
                  title: atem.title,
                  description: atem.description,
                  type: "Product",
                };
                break;
              case "Poster":
                msg = {
                  id: atem.id,
                  fileId: atem.fileId,
                  image: atem.imageUrl,
                  type: "Poster",
                };
                break;
              case "MINI_PROGRAM": // 小程序
                msg = {
                  ...atem.miniProgram,
                  url: atem.miniProgram?.page || atem.miniProgram?.url,
                  fileId: atem?.fileId,
                  type: "MINI_PROGRAM",
                };
                break;
              case "FORM": // 表单
                msg = {
                  id: atem.id,
                  title: atem.title,
                  description: atem.description,
                  fileId: atem.image ? [atem.image] : atem.fileId,
                  image: atem.image,
                  url: atem.transitUrl || atem.url,
                  type: "FORM",
                };
                break;
              default:
                msg = {
                  ...msgList[index],
                };
                break;
            }
            return msg;
          });
          delete item.msgList;
        });
      }

      let apiUrl = ""
      if (id) {
        apiUrl = `/group_sop/modify`
      } else {
        apiUrl = "/group_sop/add"
      }
      apiCall(apiUrl, id ? "PUT" : "POST", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.push("/wecom/groupSOP")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    });
  };

  const fillTime = (unit) => {
    formRef.current?.setFieldsValue({
      timedPushConfig: {
        sendDateTime: moment().add(1, unit),
      },
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const handleScrollItem = (item) => {
    if (id) {
      return;
    }
    setSelectType(item.type);
    getMsgText(item.type);
    setMsgList([]);
    // setMsgConfigData([]);
    formRef.current.setFieldsValue({
      type: item.type,
      msgList: [],
    });
  };

  // 资源库名称提示语
  const getMsgText = (type) => {
    let msgText = "";
    switch (type) {
      case "GROUP_NEW_MEMBER":
        msgText = "欢迎语";
        break;
      case "GROUP_TIMED_PUSH":
        msgText = "推送内容";
        break;
      case "GROUP_VIOLATION":
        msgText = "警告内容";
        break;
      case "GROUP_LIVE":
        msgText = "回复规则";
        break;
    }
    setMsgText(msgText);
  };

  // 新人进群配置
  const NewMemberConfig = () => (
    <>
      <FormItem
        label="发送间隔"
        tooltip="从每个间隔的首个新人进群开始计算"
        required
      >
        <Space>
          <Typography.Text>每过</Typography.Text>
          <FormItem
            name={["newMemberConfig", "interval"]}
            noStyle
            rules={[{ required: true, message: "请输入时间间隔" }]}
            initialValue={30}
          >
            <InputNumber />
          </FormItem>
          <Typography.Text>
            分钟<Typography.Text strong>或</Typography.Text>每进
          </Typography.Text>
          <FormItem
            name={["newMemberConfig", "maxCount"]}
            noStyle
            rules={[{ required: true, message: "请输入人数间隔" }]}
            initialValue={5}
          >
            <InputNumber />
          </FormItem>
          <Typography.Text>位新人</Typography.Text>
        </Space>
      </FormItem>
      <FormItem
        label="欢迎语勿扰时间段"
        name={["newMemberConfig", "silentTimeRange"]}
        initialValue={[moment("22:00", "HH:mm"), moment("7:00", "HH:mm")]}
      >
        <TimePicker.RangePicker
          order={false}
          showTime={{ format: timeFormat }}
          format={timeFormat}
          allowClear
        />
      </FormItem>
    </>
  );

  // 定时推送配置
  const TimedPushConfig = () => {
    // 周期推送配置
    const CyclePushConfig = () => {
      const [cycleType, setCycleType] = useState("everyday");
      const PickerWithType = ({ type }) => {
        switch (type) {
          case "dayOfWeek":
            return (
              <FormItem
                name={["timedPushConfig", "dayOfWeek"]}
                noStyle
                rules={[{ required: true, message: "请选择发送日" }]}
                initialValue={[]}
              >
                <Select mode="multiple" style={{ width: "calc(100% - 210px)" }}>
                  <Option value="1">周一</Option>
                  <Option value="2">周二</Option>
                  <Option value="3">周三</Option>
                  <Option value="4">周四</Option>
                  <Option value="5">周五</Option>
                  <Option value="6">周六</Option>
                  <Option value="7">周日</Option>
                </Select>
              </FormItem>
            );
          case "dayOfMonth":
            return (
              <FormItem
                name={["timedPushConfig", "dayOfMonth"]}
                noStyle
                rules={[{ required: true, message: "请选择发送日" }]}
                initialValue={[]}
              >
                <Select mode="multiple" style={{ width: "calc(100% - 210px)" }}>
                  <Option value="1">第一天</Option>
                  <Option value="15">十五号</Option>
                  <Option value="L">最后一天</Option>
                </Select>
              </FormItem>
            );
          case "day":
            return (
              <FormItem
                name={["timedPushConfig", "day"]}
                noStyle
                rules={[{ required: true, message: "请选择发送日" }]}
                initialValue={[]}
              >
                <Select mode="multiple" style={{ width: "calc(100% - 210px)" }}>
                  <Option value="yuandan">元旦节</Option>
                  <Option value="qingming">清明节</Option>
                  <Option value="laodong">劳动节</Option>
                  <Option value="zhongqiu">中秋节</Option>
                  <Option value="guoqing">国庆节</Option>
                  <Option value="chuxi">除夕</Option>
                </Select>
              </FormItem>
            );
          default:
            return null;
        }
      };

      return (
        <>
          <FormItem
            label="日期范围"
            name={["timedPushConfig", "dateRange"]}
            rules={[{ required: true, message: "请选择日期范围" }]}
          >
            <RangePicker format={dateFormat} locale="zh-CN" />
          </FormItem>
          <FormItem label="发送规则" required>
            <Input.Group compact>
              <FormItem
                name={["timedPushConfig", "cycleType"]}
                noStyle
                rules={[{ required: true, message: "请选择" }]}
                initialValue="everyday"
              >
                <Select
                  placeholder="选择规则"
                  onChange={setCycleType}
                  style={{ width: "100px" }}
                >
                  <Option value="everyday">每天</Option>
                  <Option value="dayOfWeek">每周</Option>
                  <Option value="dayOfMonth">每月</Option>
                  <Option value="day">每年</Option>
                </Select>
              </FormItem>
              <PickerWithType type={cycleType} />
              <FormItem
                name={["timedPushConfig", "time"]}
                noStyle
                rules={[{ required: true, message: "请选择" }]}
                initialValue={moment()}
              >
                <TimePicker
                  showTime
                  format={timeFormat}
                  placeholder="发送时间"
                  style={{ width: "110px" }}
                />
              </FormItem>
            </Input.Group>
          </FormItem>
        </>
      );
    };

    // 单次推送配置
    const SinglePushConfig = () => (
      <FormItem label="发送时间" required>
        <Space split={<Divider type="vertical" />}>
          <FormItem
            name={["timedPushConfig", "sendDateTime"]}
            rules={[{ required: true, message: "请输入发送时间" }]}
            noStyle
            initialValue={moment()}
          >
            <DatePicker showTime format={dateTimeFormat} />
          </FormItem>
          <Typography.Link onClick={() => fillTime("minutes")}>
            一分钟后
          </Typography.Link>
          <Typography.Link onClick={() => fillTime("hours")}>
            一小时后
          </Typography.Link>
          <Typography.Link onClick={() => fillTime("days")}>
            一天后
          </Typography.Link>
        </Space>
      </FormItem>
    );

    return (
      <>
        <FormItem
          label="推送类型"
          name={["timedPushConfig", "cycle"]}
          rules={[{ required: true, message: "请选择推送类型" }]}
          initialValue={false}
        >
          <Radio.Group>
            <Radio.Button value={false}>单次</Radio.Button>
            <Radio.Button value>周期</Radio.Button>
          </Radio.Group>
        </FormItem>
        <FormItem
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.timedPushConfig.cycle !== curValues.timedPushConfig.cycle
          }
        >
          {({ getFieldValue }) =>
            getFieldValue(["timedPushConfig", "cycle"]) ? (
              <CyclePushConfig />
            ) : (
              <SinglePushConfig />
            )
          }
        </FormItem>
        <FormItem
          label="默认发送间隔"
          name={["timedPushConfig", "interval"]}
          // rules={[{ required: true, message: '请输入默认发送间隔' }]}
          extra="如果回复消息的发送间隔不填，则使用默认发送间隔"
        >
          <InputNumber addonAfter="秒" />
        </FormItem>
        {/* <FormItem label="推送内容" name={['timedPushConfig', 'content']} rules={[{ required: true, message: '请输入推送内容' }]}>
        <TextArea rows={4} placeholder="" allowClear />
      </FormItem> */}
      </>
    );
  };

  // 违规监测配置
  const ViolationConfig = () => (
    <>
      <Form.List name={["violationConfig", "keys"]} initialValue={[""]}>
        {(fields, { add, remove }, { errors }) => (
          <>
            {fields.map((field, index) => (
              <FormItem
                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                label={index === 0 ? "违规词" : ""}
                key={field.key}
              >
                <FormItem
                  name={[field.name]}
                  style={{ marginBottom: "0px" }}
                  rules={[{ required: true, message: "请输入违规词" }]}
                >
                  <TextArea allowClear />
                </FormItem>
                <CloseCircleOutlined
                  className="msg-delete-button"
                  onClick={() => remove(field.name)}
                />
                {/* {fields.length > 1 && (
                  <CloseCircleOutlined
                    className="msg-delete-button"
                    onClick={() => remove(field.name)}
                  />
                )} */}
              </FormItem>
            ))}
            <FormItem
              {...(!fields.length
                ? formItemLayout
                : formItemLayoutWithOutLabel)}
              label={!fields.length ? "违规词" : ""}
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => add()}
              >
                添加违规词
              </Button>
            </FormItem>
          </>
        )}
      </Form.List>
      <Form.List name={["violationConfig", "whiteHosts"]} initialValue={[""]}>
        {(fields, { add, remove }, { errors }) => (
          <>
            {fields.map((field, index) => (
              <FormItem
                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                label={index === 0 ? "域名白名单" : ""}
                key={field.key}
              >
                <FormItem
                  name={[field.name]}
                  style={{ marginBottom: "0px" }}
                  rules={[{ required: true, message: "请输入域名白名单" }]}
                >
                  <Input allowClear />
                </FormItem>
                <CloseCircleOutlined
                  className="msg-delete-button"
                  onClick={() => remove(field.name)}
                />
                {/* {fields.length > 1 && (
                  <CloseCircleOutlined
                    className="msg-delete-button"
                    onClick={() => remove(field.name)}
                  />
                )} */}
              </FormItem>
            ))}
            <FormItem
              {...(!fields.length
                ? formItemLayout
                : formItemLayoutWithOutLabel)}
              label={!fields.length ? "域名白名单" : ""}
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => add()}
              >
                添加域名白名单
              </Button>
            </FormItem>
          </>
        )}
      </Form.List>
    </>
  );

  return (
    <div className="sopForm">
      <Spin spinning={loading}>
        <Card
          bordered={false}
          title={id ? "编辑客户群SOP" : "新增客户群SOP"}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返 回
            </Button>
          }
        >
          <ul className="scrollList" id="scrollList">
            {types.map((item, index) => (
              <li
                key={index}
                className={
                  selectType == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${selectType != item.type && id ? "#c1c1c1" : "unset"
                    }`,
                  cursor: `${selectType != item.type && id ? "unset" : "pointer"
                    }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>

          <Form {...formItemLayout} ref={formRef}>
            <h2 className="card-title">基础信息</h2>
            <FormItem
              name="type"
              rules={[{ required: true, message: "请选择SOP类型" }]}
              hidden
              initialValue={selectType}
            />

            <FormItem
              label="SOP名称"
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: "请输入SOP名称" }]}
            >
              <Input placeholder="请输入" allowClear />
            </FormItem>

            <FormItem label="SOP组" name="sopGroupId">
              <Select
                placeholder="SOP组"
                fieldNames={{ label: "name", value: "id" }}
                options={SOPGroupOptions}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  // option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  (option?.name ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>

            <FormItem
              label="客户群"
              name="groupIds"
              rules={[{ required: true, message: "请选择客户群" }]}
            >
              {/* <Select
                options={groupOptions}
                allowClear
                mode="multiple"
                placeholder="请选择"
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              /> */}
              <CTypeTransferModal {...CTypeTransferParams} />
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => {
                  setCTypeTransferParams({
                    visible: true,
                    type: "group",
                    checkList: dataSource,
                    onSubmit: (data) => {
                      formRef.current.setFieldsValue({
                        groupIds: data.map((item) => item.id),
                      });
                      setDataSource(data);
                      setPaginations({
                        current: 1,
                        pageSize: 10,
                        total: data.length,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: (total, range) => `共 ${total} 条记录`,
                      });
                      setCTypeTransferParams({ visible: false });
                    },
                    onCancel: () => {
                      setCTypeTransferParams({ visible: false });
                    },
                  });
                }}
              >
                选择客户群
              </Button>
              <Table
                rowKey="id"
                loading={loading}
                dataSource={dataSource}
                columns={columns}
                scroll={{ x: 600 }}
                pagination={paginations}
                onChange={(pagination, filters, sorter) => {
                  setPaginations({
                    ...pagination,
                    showTotal: (total, range) => `共 ${total} 条记录`,
                  });
                }}
              />
            </FormItem>

            <h2 className="card-title">SOP配置</h2>

            <FormItem
              noStyle
              shouldUpdate={(prevValues, curValues) =>
                prevValues.type !== curValues.type
              }
            >
              {({ getFieldValue }) => {
                switch (getFieldValue("type")) {
                  case "GROUP_NEW_MEMBER": // 新人进群
                    return <NewMemberConfig />;
                  case "GROUP_TIMED_PUSH": // 定时推送
                    return <TimedPushConfig />;
                  case "GROUP_VIOLATION":
                    return <ViolationConfig />; // 违规监测
                  case "GROUP_COLLABORATOR": // 气氛烘托
                    return (
                      <CollaboratorConfig
                        formRef={formRef}
                      // data={msgConfigData}
                      />
                    );
                  case "GROUP_LIVE": // 课程讲解
                    return <LiveConfig />;
                  case "GROUP_KEYS": // 关键词回复
                    return <KeysConfig formRef={formRef} type={selectType} />;
                  default:
                    return null;
                }
              }}
            </FormItem>

            {(selectType == "GROUP_NEW_MEMBER" ||
              selectType == "GROUP_TIMED_PUSH" ||
              selectType == "GROUP_VIOLATION" ||
              selectType == "GROUP_LIVE") && (
                <FormItem
                  label={msgText}
                  className="formBtn"
                  wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}
                >
                  {formRef.current && (
                    <MaterialListForm
                      params={{
                        formRef: formRef.current,
                        menuList: ["copyWriter", "image", "material"],
                        // materialAmount: msgList.length,
                        isNickname: false,
                        inserteFlag: true,
                        intervalFlag:
                          selectType == "GROUP_LIVE" ||
                          selectType == "GROUP_TIMED_PUSH",
                        limitNineFlag: false,
                        extra: `请按发送顺序添加${msgText}`,
                      }}
                      // 监听回调
                      callback={(params) => {
                        setMsgList(params.data);
                      }}
                      ref={onRefMaterialListForm}
                    />
                  )}
                </FormItem>
              )}

            <div style={{ display: "flex", justifyContent: "center" }}>
              <Button type="primary" onClick={() => onSubmit()}>
                保 存
              </Button>
            </div>
          </Form>
        </Card>
      </Spin>
    </div>
  );
};

export default GroupSOPForm;
