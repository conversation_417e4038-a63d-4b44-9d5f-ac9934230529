.sopForm {

  .swiper {
    width: 800px;
    height: 160px;
    margin-bottom: 20px;

    .swiper-wrapper {

      .swiper-slide {
        height: 124px !important;
        border: 1px solid #d7d7d7;
        border-radius: 6px;
        padding: 10px;
        overflow: hidden;
        cursor: pointer;

        h2 {
          font-size: 18px;
          font-weight: bold;
          margin: 0;
        }

        p {
          font-size: 14px;
          margin: 0;
        }

        .active {
          border-style: solid;
          border-width: 20px;
          border-top-color: transparent;
          border-bottom-color: transparent;
          border-left-color: transparent;
          border-right-color: #1890ff;
          position: absolute;
          transform: rotate(135deg);
          right: -20px;
          top: -20px;
          display: none;

          span {
            position: absolute;
            color: #fff;
            left: 7px;
            bottom: -8px;
            transform: rotate(-120deg);
            font-size: 13px;
          }
        }

        &.swiper-slide-active {
          border: 1px solid #1890ff;

          .active {
            display: block;
          }
        }

      }
    }
  }

  .scrollList {
    // max-width: 1000px;
    height: 150px;
    // margin: 0 auto 20px;
    padding: 0;
    white-space: nowrap;
    overflow-x: scroll;
    overflow-y: hidden;

    .listItem {
      position: relative;
      width: 250px;
      display: inline-block;
      height: 124px;
      border: 1px solid #d7d7d7;
      border-radius: 6px;
      padding: 10px;
      overflow: hidden;
      cursor: pointer;
      margin-right: 30px;
      white-space: break-spaces;

      h2 {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
      }

      p {
        font-size: 14px;
        margin: 0;
      }

      .active {
        border: 20px solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-right-color: #1890ff;
        position: absolute;
        transform: rotate(135deg);
        right: -20px;
        top: -20px;
        display: none;

        span {
          position: absolute;
          color: #fff;
          left: 6px;
          bottom: -8px;
          transform: rotate(-120deg);
          font-size: 13px;
        }
      }

      &.activeItem {
        border: 1px solid #1890ff;

        .active {
          display: block;
        }
      }
    }

    // 滚动条整体部分
    &::-webkit-scrollbar {
      width: 6px; //对垂直方向滚动条
      height: 6px; //对水平方向滚动条
    }

    //滚动的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: rgba(0, 0, 0, .5) //滚动条的颜色
    }

    //内层滚动槽
    &::-webkit-scrollbar-track-piece {
      background-color: rgba(0, 0, 0, .1);
    }
  }

  .msg-delete-button {
    cursor: pointer;
    font-size: 20px;
    color: #d9d9d9;
    position: absolute;
    top: 0px;
    right: -229px;

    &:hover {
      color: #1989fa;
    }
  }
}