/*
 * @Author: LinQunXun
 * @Date: 2022-04-02 15:52:33
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023/11/21 14:31
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/SOP/home.jsx
 * @Description: '客户管理-客户群SOP'
 */

import {
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  Switch,
  Table,
  Tooltip,
  Select,
  message,
} from 'antd';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import FilterBar from 'components/FilterBar/FilterBar';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import SOPGroupModal from './comps/SOPGroupModal';
import OperateModal from 'components/Modal/OperateModal/index';
import SysDictSelect from 'components/select/SysDictSelect';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const GroupSOP = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [SOPGroupOptions, setSOPGroupOptions] = useState([]);
  const [SOPGroupParams, setSOPGroupParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'SOP名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: 'SOP类型',
      width: '160px',
      dataIndex: 'typeName',
      key: 'typeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: 'SOP组',
      width: '160px',
      dataIndex: 'sopGroupName',
      key: 'sopGroupName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '目标群组',
      width: '160px',
      dataIndex: 'groups',
      key: 'groups',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    // {
    //   title: '发送内容',
    //   width: '160px',
    //   dataIndex: 'config',
    //   key: 'config',
    //   align: 'center',
    //   render: (value, record, index) => <a><Tooltip placement="topLeft" title={value}>预览</Tooltip></a>,
    // },
    {
      title: 'SOP状态',
      width: '160px',
      dataIndex: 'enable',
      key: 'enable',
      align: 'center',
      render: (value, record, index) => (
        <Switch
          checkedChildren="已启用"
          unCheckedChildren="已禁用"
          checked={value}
          onChange={(checked) => onChangePublished(checked, record, index)}
        />
      ),
    },
    {
      title: '下次执行时间',
      width: '160px',
      dataIndex: 'nextTime',
      key: 'nextTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleCopy(record), name: "复制" },
        ];
        if (record.type == 'GROUP_LIVE') {
          opts.push({ onClick: () => handleMonitor(record), name: "运行情况" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getSopGroupOptions();
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  // 获取SOP组
  const getSopGroupOptions = async () => {
    setLoading(true);
    await apiCall('/sopGroup', 'GET')
      .then((res) => {
        setSOPGroupOptions(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.nextTime) {
        formData.minNextTime = moment(formData.nextTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxNextTime = moment(formData.nextTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.nextTime;
      }
      formData.taskTypeList = formData.taskTypeList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/group_sop/page', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/groupSOP/form',
      search: `?id=${id}`,
    });
  };

  const handleCopy = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '复制确认',
      content: `您将复制SOP【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/group_sop/copy/${id}`, 'POST')
          .then((res) => {
            const { id } = res;
            message.success('复制成功！', 1).then(() => {
              props.history.push({
                pathname: '/wecom/groupSOP/form',
                search: `?id=${id}`,
              });
            });
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleMonitor = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/groupSOP/monitor',
      search: `?id=${id}`,
    });
  };

  const handleAdd = () => {
    props.history.push('/wecom/groupSOP/form');
  };

  const onChangePublished = (checked, record) => {
    apiCall('/group_sop/switch', 'PUT', { id: record.id, enable: checked })
      .then((res) => {
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleManageSOPGroup = () => {
    setSOPGroupParams({
      visible: true,
      onCancel: () => {
        setSOPGroupParams({ visible: false });
        getSopGroupOptions();
      },
    });
  };

  return (
    <div className="groupSOP">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="SOP名称" allowClear />
          </FormItem>
          <FormItem name="sopGroupId">
            <Select
              placeholder="SOP组"
              fieldNames={{ label: 'name', value: 'id' }}
              options={SOPGroupOptions}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="taskTypeList">
            <SysDictSelect
              placeholder="SOP类型"
              dataset="SOP_SELECT_TYPE"
              mode="multiple"
              maxTagCount="responsive"
            />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="nextTime" label="下次执行时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleManageSOPGroup()}>
              分组管理
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card
        bordered={false}
      >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <SOPGroupModal params={SOPGroupParams} />
      <OperateModal params={operateParams} />
    </div>
  );
};

export default withRouter(GroupSOP);
