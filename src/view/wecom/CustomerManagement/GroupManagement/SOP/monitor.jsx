/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/11 10:17
 * @LastEditTime: 2022/09/27 17:54
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\GroupManagement\SOP\monitor.jsx
 * @Description: '客户群SOP-运行情况'
 */

import { CheckCircleOutlined, ClockCircleOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import { Avatar, Button, Card, Col, Descriptions, Divider, List, Popover, Row, Space, Spin, Tabs, Timeline, Image, Typography } from 'antd';
import { apiCall, restOPCall } from 'common/utils';
import avatar from 'images/defaulthead.png';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { qs2obj } from 'common/object';
import './monitor.less';
import { FileHOC } from 'components/FileHOC/FileHOC';


const { TabPane } = Tabs;
const { Paragraph } = Typography;

const GroupSOPMonitor = (props) => {
  const [loading, setLoading] = useState(false);
  const [currentGroupId, setCurrentGroupId] = useState(null);
  const [sop, setSop] = useState(null);
  const [task, setTask] = useState({});
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      getInfo(id);
    }
  }, []);

  useEffect(() => {
    if (sop) {
      setCurrentGroupId(sop.groups[0].id + '');
    }
  }, [sop]);

  useEffect(() => {
    if (currentGroupId) {
      fetchGroupMessage();
    }
  }, [currentGroupId]);

  const getInfo = async (id) => {
    setLoading(true);
    await restOPCall('/group_sop', 'get', { id: id }).then((res) => {
      setSop(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchGroupMessage = async () => {
    setLoading(true);
    await apiCall('/message/group', 'GET', { groupId: currentGroupId }).then((res) => {
      setMessages(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });

    await apiCall('/group_sop/latest_task', 'GET', { sopId: sop.id, groupId: currentGroupId }).then((res) => {
      setTask(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleChangeTabs = (groupId) => {
    setCurrentGroupId(groupId);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const handleRefresh = () => {
    fetchGroupMessage();
  };

  const renderMessage = (message) => {
    let sender = message.sender;
    let nameSuffix = null;
    if (sender.identity == 'CUSTOMER') {
      if (sender.type == 'WECHAT') {
        nameSuffix = <span style={{ color: '#73d13d' }}> @ 微信</span>;
      } else {
        nameSuffix = <span style={{ color: '#faad14' }}> @ {sender.corpName || '-'}</span>;
      }
    }
    let title = <p>
      <span>{sender.name || '-'}</span>
      {nameSuffix}
      &nbsp;&nbsp;&nbsp;&nbsp;
      <span>{moment(message.sendTime).format('YYYY-MM-DD HH:mm:ss')}</span>
    </p>;
    let description = '';
    if (message.type == 'TEXT') {
      description = JSON.parse(message.content).content;
    } else if (message.type == 'IMAGE') {
      description = '【图片】';
    } else if (message.type == 'LINK') {
      const data = JSON.parse(message.content);
      description = (
        <div className="link-card">
          <Paragraph strong ellipsis={{ rows: 2 }}>{data.title}</Paragraph>
          <Row justify="space-between" style={{ flexFlow: 'row' }}>
            <Col span={17}>
              <Paragraph style={{ fontSize: '12px', lineHeight: '18px' }} ellipsis={{ rows: 3 }}>
                {data.description}
              </Paragraph>
            </Col>
            <Col>
              <FileHOC src={data.imageUrl || 'error'}>
                {(url) => (
                  <Image
                    width={54}
                    src={url}
                    fallback="images/fallbackImg.png"
                    preview={false}
                  />
                )}
              </FileHOC>
            </Col>
          </Row>
        </div >
      );
    } else if (message.type == 'VIDEO') {
      description = '【视频】';
    }

    return (<List.Item>
      <List.Item.Meta
        avatar={<Avatar src={avatar} style={{ backgroundColor: 'black' }} />}
        title={title}
        description={description}
      />
    </List.Item>);
  };

  const LiveContent = () => {
    if (!task || !task.data) {
      return null;
    }

    let data;
    try {
      data = JSON.parse(task.data);
    } catch (error) {
      console.error(error);
      return null;
    }

    if (data.type != 'MULTIPLE') {
      return null;
    }
    let currentIndex = data.currentIndex;

    return (<Timeline>
      {data.items.map((item, index) => {
        let dot = {};
        if (index < currentIndex || (index == currentIndex && task.status == 'EXECUTED')) {
          dot = <CheckCircleOutlined style={{ color: 'green' }} />;
        } else if (index == currentIndex) {
          dot = <SyncOutlined spin style={{ color: 'blue' }} />;
        } else {
          dot = <ClockCircleOutlined style={{ color: 'gray' }} />;
        }
        if (item.type == 'IMAGE') {
          return <Timeline.Item key={index} dot={dot}>
            <FileHOC src={item.fileId || 'error'}>
              {(url) => (
                <Image
                  width={90}
                  src={url}
                  fallback="images/fallbackImg.png"
                  preview={false}
                />
              )}
            </FileHOC>
          </Timeline.Item>;
        } else if (item.type == 'TEXT') {
          return <Timeline.Item key={index} dot={dot}>{item.content}</Timeline.Item>;
        } else if (item.type == 'VIDEO') {
          return <Timeline.Item key={index} dot={dot}>
            <FileHOC src={item.fileId}>
              {(url) => (
                <video controls src={url} style={{ width: '100px', height: '100px' }} />
              )}
            </FileHOC>
          </Timeline.Item>;
        } else if (item.type == 'NEWS') {
          return <Timeline.Item key={index} dot={dot}>
            <a className="link-card" href={item.link} target="_blank" rel="noreferrer">
              <Paragraph strong ellipsis={{ rows: 2 }}>{item.title}</Paragraph>
              <Row justify="space-between" style={{ flexFlow: 'row' }}>
                <Col span={17}>
                  <Paragraph style={{ fontSize: '12px', lineHeight: '18px' }} ellipsis={{ rows: 3 }}>
                    {item.desc}
                  </Paragraph>
                </Col>
                <Col>
                  <FileHOC src={item.imgUrl || 'error'}>
                    {(url) => (
                      <Image
                        width={54}
                        src={url}
                        fallback="images/fallbackImg.png"
                        preview={false}
                      />
                    )}
                  </FileHOC>
                </Col>
              </Row>
            </a >
          </Timeline.Item>;
        }
      })}
    </Timeline>);
  };

  return (
    <div className='GroupSOPMonitor'>
      <Spin spinning={loading}>
        <Row gutter={20}>
          <Col xs={24} lg={16} >
            <Card bordered={false} title="客户群消息列表" className='basicInfo' extra={
              <Space>
                <Popover content="当SOP由机器人执行时，下发消息会有所延迟！">
                  <Button type="primary" onClick={() => handleRefresh()} icon={<ReloadOutlined />}>刷 新</Button>
                </Popover>
                <Button type="primary" onClick={() => handleGoBack()}>返 回</Button>
              </Space>
            }>
              <div className='group-box'>
                <Tabs activeKey={currentGroupId} tabPosition='left' onChange={handleChangeTabs}>
                  {
                    sop?.groups?.map((item, index) => (<TabPane tab={item.name} key={item.id}>
                      <List className='groupList' dataSource={messages} renderItem={(message) => renderMessage(message)} />
                    </TabPane>))
                  }
                </Tabs>
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card bordered={false} className='dynamicInfo'>
              <Descriptions title="课程讲解配置" column={{ xs: 1, sm: 1 }} bordered>
                <Descriptions.Item label="口令">{sop?.liveConfig?.command}</Descriptions.Item>
                <Descriptions.Item label="内容发送间隔">{sop?.liveConfig?.interval} 秒</Descriptions.Item>
              </Descriptions>
              <Divider />
              <Descriptions layout="vertical" bordered>
                <Descriptions.Item label="课程内容">
                  <LiveContent />
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div >
  );
};

export default GroupSOPMonitor;
