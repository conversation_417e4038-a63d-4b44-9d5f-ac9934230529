/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/28 11:37
 * @LastEditTime: 2023/04/27 11:55
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/GroupManagement/home.jsx
 * @Description: '客户群管理'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import Group from './Group/home';
import GroupSOP from './SOP/home';

const { TabPane } = Tabs;

const GroupManagement = (props) => {

  const [tabsIndex, setTabsIndex] = useState('1');

  useEffect(() => {
    // setTabsIndex(localStorage.getItem('customerTabsIndex') || '1');
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('customerTabsIndex', index);
    setTabsIndex(index);
  };

  return (
    <div className='groupManagement'>
      {/* <Group /> */}
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="客户群" key="1">
            <Group />
          </TabPane>
          {/* <TabPane tab="客户群SOP" key="2">
            <GroupSOP />
          </TabPane> */}
        </Tabs>
      </Card>
    </div>
  );
};

export default GroupManagement;
