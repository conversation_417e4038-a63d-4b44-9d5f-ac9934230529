/*
 * @Author: <PERSON>eiw
 * @Date: 2023/07/24 10:31
 * @LastEditTime: 2024/01/10 18:52
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\Inheritance\comps\BatchForm.jsx
 * @Description: '批量在职继承'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Spin, Button, Card, Form, message, Space, Upload, Input } from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import moment from 'moment';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import WibotEmoji from 'components/WibotEmoji/home';
import '../form.less';

const FormItem = Form.Item;
const { TextArea } = Input;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const InheritanceForm = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [scriptText, setScriptText] = useState('');
  const [fileList, setFileList] = useState([]);

  useEffect(async () => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  const onChangeUpload = (info) => {
    if (!info.file.name.endsWith('xls') && !info.file.name.endsWith('xlsx')) {
      return false;
    }
    if (!info.fileList.length) {
      formForm.setFieldsValue({ file: '' });
    }
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const customFileRequest = (config) => {
    setLoading(true);
    const File = config.file;
    const newFileLIst = fileList;
    let timer = setTimeout(() => {
      newFileLIst[0].status = 'done';
      newFileLIst[0].percent = 100;
      newFileLIst[0].file = File;
      setFileList([...newFileLIst]);
      setLoading(false);
      clearTimeout(timer);
    }, 2000);
  };

  // 下载模板
  const handleDownload = () => {
    setLoading(true);
    const data = {
      fileName: '客户继承模板.xlsx',
    };
    apiCall('/file/template', 'GET', data, null, {
      isExit: true,
      title: `客户继承模板.${moment().format('YYYY-MM-DD')}.xlsx`,
    })
      .then((res) => {
        message.success('下载成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleScriptChange = (e) => {
    const { value } = e.target;
    formForm.setFieldsValue({
      transferSuccessMsg: value,
    });
    setScriptText(value);
  };

  // 添加昵称
  const handleAddFocusContent = async () => {
    const insertHTML = '#新归属人昵称#';
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      scriptText.substring(0, startPos) +
      insertHTML +
      scriptText.substring(endPos);
    formForm.setFieldsValue({
      transferSuccessMsg: text,
    });
    setScriptText(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native;
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos);
    formForm.setFieldsValue({
      transferSuccessMsg: text,
    });
    setScriptText(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    );
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const { file, transferSuccessMsg } = formData;
      const formObj = new FormData();
      console.log(file, 'filefilefile');
      formObj.append('file', file.file.file);
      formObj.append('transferSuccessMsg', transferSuccessMsg);
      const data = formObj;
      apiCall('/customer/takeover/batch_takeover', 'POST', data)
        .then((res) => {
          message.success('分配成功！');
          clearCache(); // 清空路由缓存
          props.history.push('/wecom/customerInherit');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <Spin spinning={loading}>
      <Card>
        <div style={{ marginBottom: '10px' }}>
          上传文件信息需在系统存在，否则不更新
        </div>
        <Form {...layout} form={formForm}>
          <FormItem
            name="transferSuccessMsg"
            label="继承话术"
            rules={[{ required: true, message: '请输入继承话术（300字）' }]}
            extra={
              <div>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddFocusContent()}
                >
                  新归属人昵称
                </Button>
                <div>仅对客户生效，客户群不生效</div>
              </div>
            }
          >
            <div className="textarea-emoji">
              <TextArea
                id="msgTextInput"
                placeholder="请输入继承话术（300字）"
                value={scriptText}
                allowClear
                showCount
                maxLength={300}
                autoSize={{ minRows: 6, maxRows: 10 }}
                onChange={handleScriptChange}
              />

              <WibotEmoji
                iconClassName="textarea-emoji_icon"
                onEmojiSelect={handleEmojiSelect}
              />
            </div>
          </FormItem>

          <FormItem
            label="选择文件"
            name="file"
            rules={[{ required: true, message: '请上传xls、xlsx格式的文件' }]}
            extra={
              <div>
                请选择继承清单文件，格式xls、xlsx
                <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                  下载模板
                </a>
              </div>
            }
          >
            <Upload
              name="file"
              accept=".xls,.xlsx"
              fileList={fileList}
              onChange={onChangeUpload}
              beforeUpload={(file) => {
                if (!file.name.endsWith('xls') && !file.name.endsWith('xlsx')) {
                  message.error('请上传xls、xlsx格式的文件!');
                  return false || Upload.LIST_IGNORE;
                }
              }}
              customRequest={customFileRequest}
            >
              {fileList.length <= 0 ? (
                <Button icon={<UploadOutlined />}>选择文件</Button>
              ) : null}
            </Upload>
          </FormItem>
        </Form>
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Space size={40}>
            <Button
              type="primary"
              onClick={() => {
                props.history.go(-1);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => onSubmit()}>
              确认分配
            </Button>
          </Space>
        </div>
      </Card>
    </Spin>
  );
};

export default withRouter(InheritanceForm);
