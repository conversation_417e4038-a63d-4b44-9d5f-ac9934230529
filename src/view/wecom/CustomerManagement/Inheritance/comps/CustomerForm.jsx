/*
 * @Author: Janaeiw
 * @Date: 2022/11/18 14:38
 * @LastEditTime: 2024/01/10 18:57
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\Inheritance\comps\CustomerForm.jsx
 * @Description: '客户/客户群继承表单'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin,
  Button,
  Card,
  Form,
  Tooltip,
  Avatar,
  Table,
  message,
  Space,
} from 'antd';
import { apiCall } from 'common/utils';
import { PlusOutlined } from '@ant-design/icons';
import { qs2obj } from 'common/object';
import { timeStamp } from 'common/date';
import { clearCache } from 'react-router-cache-route';
import CTypeTransferModal from 'components/TransferModal/CustomerType/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotTableTag from 'components/WibotTableTag/home';
import '../form.less';

const FormItem = Form.Item;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const InheritanceForm = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [CTypeTransferParams, setCTypeTransferParams] = useState({
    visible: false,
  });
  const [takeoverEmployee, setTakeoverEmployee] = useState([]); // 新归属人
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 10,
  });
  const [paginations2, setPaginations2] = useState({
    current: 1,
    pageSize: 10,
  });
  const [dataSource1, setDataSource1] = useState([]);
  const [dataSource2, setDataSource2] = useState([]);
  const columns1 = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '220px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? '#07c160' : '#f59a23' }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    // {
    //   title: 'CIS编号',
    //   width: '200px',
    //   dataIndex: 'cisNumber',
    //   key: 'cisNumber',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip placement="topLeft" title={value}>
    //       {value}
    //     </Tooltip>
    //   ),
    // },
    {
      title: '原归属人',
      width: '200px',
      dataIndex: 'relevanceEmployee',
      key: 'relevanceEmployee',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '负责人',
      width: '200px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '最早添加时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelet(record)}>删除</a>
        </>
      ),
    },
  ];
  const columns2 = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户群名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '原归属人（群主）',
      width: '160px',
      dataIndex: 'leaderName',
      key: 'leaderName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '群人数',
      width: '160px',
      dataIndex: 'memberCount',
      key: 'memberCount',
      ellipsis: 'true',
      align: 'center',
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleGroupDelet(record)}>删除</a>
        </>
      ),
    },
  ];

  useEffect(async () => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  const handleDelet = (record) => {
    const newDataSource1 = dataSource1;
    newDataSource1.splice(newDataSource1.indexOf(record), 1);
    setDataSource1([...newDataSource1]);
    setPaginations1({
      ...paginations1,
      total: newDataSource1.length,
    });
  };

  const handleGroupDelet = (record) => {
    const newDataSource2 = dataSource2;
    newDataSource2.splice(newDataSource2.indexOf(record), 1);
    setDataSource2([...newDataSource2]);
    setPaginations2({
      ...paginations2,
      total: newDataSource2.length,
    });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (dataSource1.length <= 0 && dataSource2.length <= 0) {
        message.warning('请选择继承客户/客户群！');
        return false;
      }
      setLoading(true);
      const { takeoverEmployeeId } = formData;
      const data = {
        id: id ?? null,
        scene: 'CUSTOMER_GROUP',
        type: null,
        ...formData,
        takeoverEmployeeId: takeoverEmployeeId.length
          ? takeoverEmployeeId?.join(',')
          : takeoverEmployeeId,
        customerIdList: dataSource1.map((item) => ({
          employeeId: item.refEmployeeId,
          id: item.id,
        })),
        groupIdList: dataSource2.map((item) => ({
          employeeId: item.leaderId,
          id: item.id,
        })),
      };
      apiCall('/customer/takeover/customer_group', 'POST', data)
        .then((res) => {
          message.success(id ? '修改成功！' : '新增成功！');
          clearCache(); // 清空路由缓存
          props.history.push('/wecom/customerInherit');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <Spin spinning={loading}>
      <Card>
        <h2 className="card-title">继承关系</h2>
        <Form {...layout} form={formForm}>
          <FormItem
            name="takeoverEmployeeId"
            label="新归属人"
            rules={[{ required: true, message: '请选择新归属人' }]}
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              onChange={(value, options) => {
                if (!options.length) {
                  setTakeoverEmployee([]);
                  return false;
                }
                setTakeoverEmployee([...options]);
              }}
            />
          </FormItem>
        </Form>

        <h2 className="card-title">继承内容</h2>
        <Card
          title={
            <div>
              继承客户
              {takeoverEmployee.length > 0 && (
                <>
                  ：{dataSource1.length}个客户将继承给【
                  {takeoverEmployee[0]?.name}】
                </>
              )}
              <Button
                style={{ marginLeft: '8px' }}
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => {
                  setCTypeTransferParams({
                    visible: true,
                    type: 'customer',
                    checkList: dataSource1,
                    onSubmit: (data) => {
                      setDataSource1(data);
                      setPaginations1({
                        current: 1,
                        pageSize: 10,
                        total: data.length,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: (total, range) => `共 ${total} 条记录`,
                      });
                      setCTypeTransferParams({ visible: false });
                    },
                    onCancel: () => {
                      setCTypeTransferParams({ visible: false });
                    },
                  });
                }}
              >
                选择客户
              </Button>
            </div>
          }
        >
          <Table
            rowKey="key"
            loading={loading}
            dataSource={dataSource1}
            columns={columns1}
            scroll={{ x: 1300 }}
            pagination={paginations1}
            onChange={(pagination, filters, sorter) => {
              setPaginations1({
                ...pagination,
                showTotal: (total, range) => `共 ${total} 条记录`,
              });
            }}
          />
        </Card>
        <br />
        <Card
          title={
            <div>
              继承客户群
              {takeoverEmployee.length > 0 && (
                <>
                  ：{dataSource2.length}个客户群将继承给【
                  {takeoverEmployee[0]?.name}】
                </>
              )}
              <Button
                style={{ marginLeft: '8px' }}
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => {
                  setCTypeTransferParams({
                    visible: true,
                    type: 'group',
                    checkList: dataSource2,
                    onSubmit: (data) => {
                      setDataSource2(data);
                      setPaginations2({
                        current: 1,
                        pageSize: 10,
                        total: data.length,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: (total, range) => `共 ${total} 条记录`,
                      });
                      setCTypeTransferParams({ visible: false });
                    },
                    onCancel: () => {
                      setCTypeTransferParams({ visible: false });
                    },
                  });
                }}
              >
                选择客户群
              </Button>
            </div>
          }
        >
          <Table
            rowKey="key"
            loading={loading}
            dataSource={dataSource2}
            columns={columns2}
            scroll={{ x: 1300 }}
            pagination={paginations2}
            onChange={(pagination, filters, sorter) => {
              setPaginations2({
                ...pagination,
                showTotal: (total, range) => `共 ${total} 条记录`,
              });
            }}
          />
        </Card>
        <br />
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Space size={40}>
            <Button
              type="primary"
              onClick={() => {
                props.history.go(-1);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => onSubmit()}>
              确认分配
            </Button>
          </Space>
        </div>
        <div style={{ textAlign: 'center' }}>
          确认后，将推送消息给员工去执行
        </div>
      </Card>
      <CTypeTransferModal {...CTypeTransferParams} />
    </Spin>
  );
};

export default withRouter(InheritanceForm);
