/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/11/18 14:38
 * @LastEditTime: 2024/11/15 09:31
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/Inheritance/comps/EmployeeForm.jsx
 * @Description: '员工继承表单'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Tooltip,
  Avatar,
  TreeSelect,
  Table,
  Radio,
  message,
  DatePicker,
  Select,
  Space
} from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import { PlusOutlined } from '@ant-design/icons';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import {
  recursionTagKeyTreeData,
  recursionKeyEmployeeOption, recursionTagKeyTreeDataTag
} from "common/tree"
import { qs2obj } from 'common/object';
import { timeStamp } from 'common/date';
import { clearCache } from 'react-router-cache-route';
import WibotTableTag from 'components/WibotTableTag/home';
import WibotEmoji from 'components/WibotEmoji/home';
import '../form.less';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;
const { TextArea } = Input;
const { Option } = Select;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const InheritanceForm = (props) => {
  const [formForm] = Form.useForm();
  const [customerForm] = Form.useForm();
  const [groupForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [staffOption1, setStaffOption1] = useState([]);
  const [staffOption2, setStaffOption2] = useState([]);
  const [handoverEmployee, setHandoverEmployee] = useState([]); // 原归属人
  const [takeoverEmployee, setTakeoverEmployee] = useState([]); // 新归属人
  const [scriptText, setScriptText] = useState('');
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [selectedRowKeys1, setSelectedRowKeys1] = useState([]);
  const [selectedRows1, setSelectedRows1] = useState([]);
  const [selectedRowKeys2, setSelectedRowKeys2] = useState([]);
  const [selectedRows2, setSelectedRows2] = useState([]);
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 10,
  });
  const [paginations2, setPaginations2] = useState({
    current: 1,
    pageSize: 10,
  });
  const [dataSource1, setDataSource1] = useState([]);
  const [dataSource2, setDataSource2] = useState([]);
  const columns1 = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '220px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? '#07c160' : '#f59a23' }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    // {
    //   title: 'CIS编号',
    //   width: '180px',
    //   dataIndex: 'cisNumber',
    //   key: 'cisNumber',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
    //   ),
    // },
    {
      title: '负责人',
      width: '180px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '最早添加时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];
  const columns2 = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户群名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '群人数',
      width: '160px',
      dataIndex: 'memberCount',
      key: 'memberCount',
      align: 'center',
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];

  useEffect(async () => {
    const { id } = qs2obj(props.location.search);
    // await getAttributorOption();
    await getEmployeeResign();
    await getTagCategoryTreeTwo();
    if (id) {
      setId(id);
    }
  }, []);

  // 获取归属人选项
  const getAttributorOption = async () => {
    setLoading(true);
    await apiCall('/employee/company_and_employee_option', 'GET')
      .then((res) => {
        setStaffOption1(
          recursionKeyEmployeeOption(res).map((item, index) => ({
            ...item,
            checkable: false,
          }))
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取离职员工选项-> 离职继承(原归属人)
  const getEmployeeResign = async () => {
    setLoading(true);
    await apiCall('/employee/resign', 'GET')
      .then((res) => {
        setStaffOption2(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList1 = (params = {}) => {
    setLoading(true);
    customerForm.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minAddDate = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAddDate = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      // formData.tagIdList = formData.tagIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        paged: false,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/customer/page', 'POST', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource1(records);
          setPaginations1({
            current: current,
            pageSize: 10 || size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const fetchList2 = (params = {}) => {
    setLoading(true);
    groupForm.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        paged: false,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/group/page', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource2(records);
          setPaginations2({
            current: current,
            pageSize: 10 || size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData)
        // setLabelTreeData([
        //   {
        //     title: '全选',
        //     value: 'customer',
        //     key: 'customer',
        //     children: tagTreeData,
        //   },
        // ]);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleScriptChange = (e) => {
    const { value } = e.target;
    formForm.setFieldsValue({
      transferSuccessMsg: value,
    });
    setScriptText(value);
  };

  // 添加昵称
  const handleAddFocusContent = async () => {
    const insertHTML = '#新归属人昵称#';
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      scriptText.substring(0, startPos) +
      insertHTML +
      scriptText.substring(endPos);
    formForm.setFieldsValue({
      transferSuccessMsg: text,
    });
    setScriptText(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };

  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native;
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos);
    formForm.setFieldsValue({
      transferSuccessMsg: text,
    });
    setScriptText(text);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    );
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (selectedRowKeys1.length <= 0 && selectedRowKeys2.length <= 0) {
        message.warning('请选择继承客户/客户群！');
        return false;
      }
      setLoading(true);
      const { handoverEmployeeId, takeoverEmployeeId } = formData;
      const data = {
        id: id ?? null,
        scene: 'EMPLOYEE',
        ...formData,
        handoverEmployeeId: handoverEmployeeId.length
          ? handoverEmployeeId?.join(',')
          : handoverEmployeeId,
        takeoverEmployeeId: takeoverEmployeeId.length
          ? takeoverEmployeeId?.join(',')
          : takeoverEmployeeId,
        customerIdList: selectedRowKeys1,
        groupIdList: selectedRowKeys2,
      };
      apiCall('/customer/takeover/employee', 'POST', data)
        .then((res) => {
          message.success(id ? '修改成功！' : '新增成功！');
          clearCache(); // 清空路由缓存
          props.history.push('/wecom/customerInherit');
        })
        .catch((err) => {
          console.log(err);

        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const rowSelection1 = {
    selectedRowKeys: selectedRowKeys1,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys1(selectedRowKeys);
      setSelectedRows1(selectedRows);
    },
  };

  const rowSelection2 = {
    selectedRowKeys: selectedRowKeys2,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys2(selectedRowKeys);
      setSelectedRows2(selectedRows);
    },
  };

  const locales = {
    selectionAll: '选择全部',
    selectInvert: '反选本页',
    selectNone: '取消全部',
  };

  return (
    <Spin spinning={loading}>
      <Card>
        <h2 className="card-title">继承关系</h2>
        <Form {...layout} form={formForm} initialValues={{ type: 'WORK' }}>
          <FormItem
            name="type"
            label="继承类型"
            rules={[{ required: true, message: '请选择继承类型' }]}
          >
            <Radio.Group
              onChange={(e) => {
                // 离职继承的原归属人api不同,所以切换需要清空
                setDataSource1([]);
                setDataSource2([]);
                setSelectedRowKeys1([]);
                setSelectedRows1([]);
                setSelectedRowKeys2([]);
                setSelectedRows2([]);
                formForm.setFieldsValue({
                  handoverEmployeeId: e.target.value == 'WORK' ? [] : null, // 特殊处理
                });
                setHandoverEmployee([]);
              }}
            >
              <Radio value={'WORK'}>在职继承</Radio>
              <Radio value={'LEAVE'}>离职继承</Radio>
            </Radio.Group>
          </FormItem>

          {formForm.getFieldValue('type') == 'WORK' ? (
            <FormItem
              name="handoverEmployeeId"
              label="原归属人"
              rules={[{ required: true, message: '请选择原归属人' }]}
            >
              <ETypeTransferModal
                title="选择员工"
                onlyEmployee
                multiple={false}
                onChange={(value, options) => {
                  if (!options.length) {
                    setDataSource1([]);
                    setDataSource2([]);
                    setSelectedRowKeys1([]);
                    setSelectedRows1([]);
                    setSelectedRowKeys2([]);
                    setSelectedRows2([]);
                    setHandoverEmployee([]);
                    return false;
                  }
                  fetchList1({
                    query: {
                      customerEmployeeId: value[0],
                    },
                  });
                  fetchList2({
                    query: {
                      leaderIds: value[0],
                    },
                  });
                  setHandoverEmployee([...options]);
                }}
              />
            </FormItem>
          ) : (
            <FormItem
              name="handoverEmployeeId"
              label="原归属人"
              rules={[{ required: true, message: '请选择原归属人' }]}
              extra={
                <div>
                  <span>请选择待转移客户的员工</span>
                </div>
              }
            >
              <Select
                placeholder="请选择原归属人"
                style={{ width: '220px' }}
                allowClear
                showSearch
                optionLabelProp="label"
                filterOption={(input, option) =>
                  (option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                onChange={(value, option) => {
                  if (value) {
                    fetchList1({
                      query: {
                        customerEmployeeId: value,
                      },
                    });
                    fetchList2({
                      query: {
                        leaderIds: value,
                      },
                    });
                  }
                }}
                onClear={() => {
                  setDataSource1([]);
                  setDataSource2([]);
                  setSelectedRowKeys1([]);
                  setSelectedRows1([]);
                  setSelectedRowKeys2([]);
                  setSelectedRows2([]);
                }}
              >
                {staffOption2?.map((item, index) => (
                  <Option value={item.employeeId} label={item.name} key={index}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <span>{item.name}</span>
                      <span>{item.departmentName}</span>
                    </div>
                  </Option>
                ))}
              </Select>
            </FormItem>
          )}

          <FormItem
            name="takeoverEmployeeId"
            label="新归属人"
            rules={[{ required: true, message: '请选择新归属人' }]}
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              onChange={(value, options) => {
                if (!options.length) {
                  setTakeoverEmployee([]);
                  return false;
                }
                setTakeoverEmployee([...options]);
              }}
            />
          </FormItem>

          {formForm.getFieldValue('type') == 'WORK' && (
            <FormItem
              name="transferSuccessMsg"
              label="继承话术"
              rules={[{ required: true, message: '请输入继承话术（300字）' }]}
              extra={
                <div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => handleAddFocusContent()}
                  >
                    新归属人昵称
                  </Button>
                  <div>仅对客户生效，客户群不生效</div>
                </div>
              }
            >
              <div className="textarea-emoji">
                <TextArea
                  id="msgTextInput"
                  placeholder="请输入继承话术（300字）"
                  value={scriptText}
                  allowClear
                  showCount
                  maxLength={300}
                  autoSize={{ minRows: 6, maxRows: 10 }}
                  onChange={handleScriptChange}
                />

                <WibotEmoji
                  iconClassName="textarea-emoji_icon"
                  onEmojiSelect={handleEmojiSelect}
                />
              </div>
            </FormItem>
          )}
        </Form>

        <h2 className="card-title">继承内容</h2>
        <Card
          title={
            <div>
              继承客户
              {takeoverEmployee.length > 0 && (
                <>
                  ：{selectedRowKeys1.length}个客户将继承给【
                  {takeoverEmployee[0]?.name}】
                </>
              )}
            </div>
          }
          className="inlineForm"
        >
          <Form layout={'inline'} form={customerForm}>
            <FormItem
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="客户名称" allowClear />
            </FormItem>
            <CustomTagSelect
              creatable
              style={{ width: '200px' }}
              name="tagNameList"
              placeholder="客户标签"
              useForm={customerForm}
              existTagNameList={formForm.getFieldValue("tagNameList")}
              labelTreeData={labelTreeData}
            />
            {/*<FormItem label="客户标签" name="tagIdList">*/}
            {/*  <TreeSelect*/}
            {/*    treeData={labelTreeData}*/}
            {/*    treeCheckable*/}
            {/*    treeDefaultExpandedKeys={['customer']}*/}
            {/*    allowClear*/}
            {/*    showArrow*/}
            {/*    showSearch*/}
            {/*    maxTagCount="responsive"*/}
            {/*    showCheckedStrategy={SHOW_PARENT}*/}
            {/*    placeholder="客户标签"*/}
            {/*    style={{ width: '200px' }}*/}
            {/*  />*/}
            {/*</FormItem>*/}
            <FormItem name="createTime" label="最早添加时间">
              <RangePicker />
            </FormItem>
            <div style={{ marginBottom: '20px' }}>
              <Button
                type="primary"
                onClick={() => {
                  if (handoverEmployee.length) {
                    fetchList1({
                      query: {
                        customerEmployeeId: handoverEmployee[0].id,
                      },
                    });
                  }
                }}
                style={{ marginRight: '20px' }}
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  if (handoverEmployee.length) {
                    customerForm.resetFields();
                    fetchList1({
                      query: {
                        customerEmployeeId: handoverEmployee[0].id,
                      },
                    });
                  }
                }}
              >
                重置筛选
              </Button>
            </div>
          </Form>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource1}
            columns={columns1}
            scroll={{ x: 1300 }}
            pagination={paginations1}
            onChange={(pagination, filters, sorter) => {
              setPaginations1({
                ...pagination,
                showTotal: (total, range) => `共 ${total} 条记录`,
              });
            }}
            rowSelection={rowSelection1}
            locale={locales}
          />
        </Card>
        <br />
        <Card
          title={
            <div>
              继承客户群
              {takeoverEmployee.length > 0 && (
                <>
                  ：{selectedRowKeys2.length}个客户群将继承给【
                  {takeoverEmployee[0]?.name}】
                </>
              )}
            </div>
          }
        >
          <Form layout={'inline'} form={groupForm}>
            <FormItem
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="客户群名称" allowClear />
            </FormItem>
            <FormItem name="createTime" label="创建时间">
              <RangePicker />
            </FormItem>
            <div style={{ marginBottom: '20px' }}>
              <Button
                type="primary"
                onClick={() => {
                  if (handoverEmployee.length) {
                    fetchList2({
                      query: {
                        leaderIds: handoverEmployee[0].id,
                      },
                    });
                  }
                }}
                style={{ marginRight: '20px' }}
              >
                查询
              </Button>
              <Button
                onClick={() => {
                  if (handoverEmployee.length) {
                    groupForm.resetFields();
                    fetchList2({
                      query: {
                        leaderIds: handoverEmployee[0].id,
                      },
                    });
                  }
                }}
              >
                重置筛选
              </Button>
            </div>
          </Form>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource2}
            columns={columns2}
            scroll={{ x: 1300 }}
            pagination={paginations2}
            onChange={(pagination, filters, sorter) => {
              setPaginations2({
                ...pagination,
                showTotal: (total, range) => `共 ${total} 条记录`,
              });
            }}
            rowSelection={rowSelection2}
            locale={locales}
          />
        </Card>
        <br />
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Space size={40}>
            <Button
              type="primary"
              onClick={() => {
                props.history.go(-1);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => onSubmit()}>
              确认分配
            </Button>
          </Space>
        </div>
        <div style={{ textAlign: 'center' }}>确认后，将推送消息给员工去执行</div>
      </Card>
    </Spin>
  );
};

export default withRouter(InheritanceForm);
