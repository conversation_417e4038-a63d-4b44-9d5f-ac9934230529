/*
 * @Author: <PERSON>eiw
 * @Date: 2022/11/18 14:40
 * @LastEditTime: 2024/01/10 18:56
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\Inheritance\comps\VirtualForm.jsx
 * @Description: '员工虚拟资产转移表单'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Spin, Button, Card, Form, message, Space } from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import '../form.less';

const FormItem = Form.Item;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const InheritanceForm = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [handoverEmployee, setHandoverEmployee] = useState([]); // 原归属人
  const [takeoverEmployee, setTakeoverEmployee] = useState([]); // 新归属人

  useEffect(async () => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const { handoverEmployeeId, takeoverEmployeeId } = formData;
      const data = {
        id: id ?? null,
        type: null,
        scene: 'VIRTUAL_ASSETS',
        ...formData,
        handoverEmployeeId: handoverEmployeeId.length
          ? handoverEmployeeId?.join(',')
          : handoverEmployeeId,
        takeoverEmployeeId: takeoverEmployeeId.length
          ? takeoverEmployeeId?.join(',')
          : takeoverEmployeeId,
      };
      apiCall('/customer/takeover', 'POST', data)
        .then((res) => {
          message.success(id ? '修改成功！' : '新增成功！');
          clearCache(); // 清空路由缓存
          props.history.push('/wecom/customerInherit');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <Spin spinning={loading}>
      <Card>
        <h2 className="card-title">员工虚拟资产转移</h2>
        <div style={{ marginBottom: '10px' }}>
          转移过程无需员工同意，转移成功后新归属人将收到转移成功的通知
          {/* <br />已生成码上赢二维码的图片（如：海报）将无法转移 */}
        </div>
        <Form {...layout} form={formForm}>
          <FormItem
            name="handoverEmployeeId"
            label="原归属人"
            rules={[{ required: true, message: '请选择原归属人' }]}
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              onChange={(value, options) => {
                if (!options.length) {
                  setHandoverEmployee([]);
                  return false;
                }
                setHandoverEmployee([...options]);
              }}
            />
          </FormItem>

          <FormItem
            name="takeoverEmployeeId"
            label="新归属人"
            rules={[{ required: true, message: '请选择新归属人' }]}
          >
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              onChange={(value, options) => {
                if (!options.length) {
                  setTakeoverEmployee([]);
                  return false;
                }
                setTakeoverEmployee([...options]);
              }}
            />
          </FormItem>
        </Form>
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Space size={40}>
            <Button
              type="primary"
              onClick={() => {
                props.history.go(-1);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => onSubmit()}>
              确认转移
            </Button>
          </Space>
        </div>
      </Card>
    </Spin>
  );
};

export default withRouter(InheritanceForm);
