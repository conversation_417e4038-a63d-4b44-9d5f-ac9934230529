/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/07 11:46
 * @LastEditTime: 2024/08/12 11:25
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/Inheritance/detail.jsx
 * @Description: '客户继承-详情'
 */

import React, { useEffect, useState, useRef } from "react";
import {
  Spin,
  Button,
  Card,
  Table,
  Tooltip,
  Input,
  Avatar,
  Tag,
  Popover,
  message,
  Tabs,
  Form,
  DatePicker,
  Select,
} from "antd";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import { qs2obj } from "common/object";
import { removeInputEmpty } from "common/regular";
import FilterBar from "components/FilterBar/FilterBar";
import SysDictSelect from "components/select/SysDictSelect";
import SysDictLabel from "components/select/SysDictLabel";
import "./detail.less";

const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const InheritanceDetails = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeKey, setActiveKey] = useState("CUSTOMER");
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns1 = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.customerRealName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "原归属人",
      width: "200px",
      dataIndex: "handoverEmployeeName",
      key: "handoverEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "新归属人",
      width: "200px",
      dataIndex: "takeoverEmployeeName",
      key: "takeoverEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "客户继承状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TakeoverItemStateEnum" dictkey={value} />
      ),
    },
    {
      title: "分配人/分配时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "继承状态时间",
      width: "160px",
      dataIndex: "takeoverTime",
      key: "takeoverTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.takeoverTime) - timeStamp(b.takeoverTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetail(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const columns2 = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "原归属人",
      width: "200px",
      dataIndex: "handoverEmployeeName",
      key: "handoverEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "新归属人",
      width: "200px",
      dataIndex: "takeoverEmployeeName",
      key: "takeoverEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "客户群继承状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TakeoverItemStateEnum" dictkey={value} />
      ),
    },
    {
      title: "分配人/分配时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "继承状态时间",
      width: "160px",
      dataIndex: "takeoverTime",
      key: "takeoverTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.takeoverTime) - timeStamp(b.takeoverTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetail(record)}>客户群详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      fetchList();
    }
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minTakeoverTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxTakeoverTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      const { id } = qs2obj(props.location.search);
      const { pagination, query, sourceType } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        takeoverId: id,
        sourceType: sourceType || activeKey,
        ...query,
        ...formData,
      };
      apiCall("/customer/takeover/detail", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTabs = (index) => {
    formRef.current.resetFields();
    setActiveKey(index);
    fetchList({ sourceType: index, pagination: { current: 1, pageSize: 10 } });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleDetail = (record) => {
    const { id } = record;
    switch (activeKey) {
      case "CUSTOMER":
        props.history.push({
          pathname: "/wecom/customer/details",
          search: `?id=${id}`,
        });
        break;
      case "GROUP":
        props.history.push({
          pathname: "/wecom/groupManagement/detail",
          search: `?id=${id}`,
        });
        break;
    }
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className="Inheritance-Detail-Container">
      <Spin spinning={loading}>
        <Card
          title="继承详情"
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Tabs
            activeKey={activeKey}
            destroyInactiveTabPane
            onChange={onChangeTabs}
          >
            <TabPane tab="继承客户" key="CUSTOMER">
              <FilterBar bodyStyle={{ padding: "unset" }}>
                <Form layout={"inline"} ref={formRef}>
                  <FormItem
                    name="name"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input placeholder="客户名称" allowClear />
                  </FormItem>
                  <FormItem name="state">
                    <SysDictSelect
                      placeholder="继承状态"
                      dataset="TakeoverItemStateEnum"
                    />
                  </FormItem>
                  <FormItem name="createTime" label="继承状态">
                    <RangePicker />
                  </FormItem>
                </Form>
                <div className="flex flex-space-between">
                  <div>
                    <Button type="primary" onClick={() => handleQuery()}>
                      查询
                    </Button>
                    <Button onClick={() => handleReset()}>重置筛选</Button>
                  </div>
                </div>
              </FilterBar>
              <Table
                rowKey="customerId"
                dataSource={dataSource}
                columns={columns1}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
            <TabPane tab="继承客户群" key="GROUP">
              <FilterBar bodyStyle={{ padding: "unset" }}>
                <Form layout={"inline"} ref={formRef}>
                  <FormItem
                    name="name"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input placeholder="客户群名称" allowClear />
                  </FormItem>
                  <FormItem name="state">
                    <Select
                      showSearch
                      placeholder="继承状态"
                      optionFilterProp="label"
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      options={[
                        {
                          value: "SUCCESS",
                          label: "继承成功",
                        },
                        {
                          value: "FAIL_GROUP",
                          label: "继承失败",
                        },
                      ]}
                    />
                  </FormItem>
                  <FormItem name="createTime" label="继承状态">
                    <RangePicker />
                  </FormItem>
                </Form>
                <div className="flex flex-space-between">
                  <div>
                    <Button type="primary" onClick={() => handleQuery()}>
                      查询
                    </Button>
                    <Button onClick={() => handleReset()}>重置筛选</Button>
                  </div>
                </div>
              </FilterBar>
              <Table
                rowKey="id"
                dataSource={dataSource}
                columns={columns2}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  );
};

export default InheritanceDetails;
