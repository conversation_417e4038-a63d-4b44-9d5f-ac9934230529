/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/07 14:29
 * @LastEditTime: 2023/09/19 11:27
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\Inheritance\form.jsx
 * @Description: '客户继承-新增编辑'
 */

import React, { useEffect, useState } from "react";
import { Spin, Button, Card } from "antd";
import { CheckOutlined } from "@ant-design/icons";
import { qs2obj } from "common/object";
import EmployeeForm from "./comps/EmployeeForm";
import CustomerForm from "./comps/CustomerForm";
import VirtualForm from "./comps/VirtualForm";
import BatchForm from "./comps/BatchForm";
import "./form.less";

const InheritanceForm = (props) => {
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const tabsData = [
    {
      title: "员工继承",
      describe: "从在职或离职员工维度，对客户/客户群进行员工间调配",
      type: "EMPLOYEE",
    },
    {
      title: "客户/客户群继承",
      describe: "对指定的客户/客户群进行员工间调配",
      type: "CUSTOMER_GROUP",
    },
    {
      title: "员工虚拟资产转移",
      describe:
        "可一次性将员工的活码虚拟资产转移给其他员工，防止员工离职后资产无归属",
      type: "VIRTUAL_ASSETS",
    },
    {
      title: "批量继承",
      describe: "通过excel表格去发起客户继承",
      type: "BATCH_FILE",
    },
  ];
  const [activeKey, setActiveKey] = useState("EMPLOYEE");

  useEffect(async () => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  // 切换继承方式
  const handleScrollItem = (item) => {
    if (id) {
      return false;
    }
    setActiveKey(item.type);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className="InheritanceForm-Container">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑继承" : "新增继承"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card>
          <h2 className="card-title">继承方式</h2>
          <ul className="scrollList">
            {tabsData?.map((item, index) => (
              <li
                key={index}
                className={
                  activeKey == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${
                    activeKey != item.type && id ? "#c1c1c1" : "unset"
                  }`,
                  cursor: `${
                    activeKey != item.type && id ? "unset" : "pointer"
                  }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>
        </Card>
        <br />
        {activeKey == "EMPLOYEE" && <EmployeeForm />}
        {activeKey == "CUSTOMER_GROUP" && <CustomerForm />}
        {activeKey == "VIRTUAL_ASSETS" && <VirtualForm />}
        {activeKey == "BATCH_FILE" && <BatchForm />}
      </Spin>
    </div>
  );
};

export default InheritanceForm;
