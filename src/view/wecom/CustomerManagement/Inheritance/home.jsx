/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/07 11:14
 * @LastEditTime: 2023/11/21 17:08
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/Inheritance/home.jsx
 * @Description: '客户继承'
 */

import React, { useEffect, useRef, useState } from 'react';
import {Button, Card, Form, Table, DatePicker, Tooltip, message} from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import './home.less';
import {QuestionCircleOutlined} from "@ant-design/icons";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const Inheritance = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '原归属人',
      width: '160px',
      dataIndex: 'handoverEmployeeNameList',
      key: 'handoverEmployeeNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '新归属人',
      width: '160px',
      dataIndex: 'takeoverEmployeeNameList',
      key: 'takeoverEmployeeNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '继承场景',
      width: '160px',
      dataIndex: 'scene',
      key: 'scene',
      align: 'center',
      render: (value, record, index) => <div><SysDictLabel dataset="TakeoverSceneEnum" dictkey={value} />{value == 'EMPLOYEE' && (record.type == 'WORK' ? <>(在职)</> : <>(离职)</>)}</div>,
    },
    {
      title: '继承状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => <SysDictLabel dataset="TakeoverStateEnum" dictkey={value} />,
    },
    {
      title: '继承数据',
      width: '160px',
      dataIndex: 'statVO',
      key: 'statVO',
      align: 'center',
      render: (value, record, index) => <div style={{ textAlign: 'left' }}>
        {
          record.scene == 'VIRTUAL_ASSETS' && '活码' || <>
            {record?.customerAllCount > 0 && <>客户数：{record.customerSuccessCount}/{record?.customerAllCount}</>}
            <br />
            {record?.groupAllCount > 0 && <>客户群数：{record.groupSuccessCount}/{record?.groupAllCount}</>}
          </>
        }
      </div>,
    },
    {
      title: '继承状态时间',
      width: '160px',
      dataIndex: 'takeoverTime',
      key: 'takeoverTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.takeoverTime) - timeStamp(b.takeoverTime)
    },
    {
      title: '分配人/分配时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime)
    },
    {
      title: () => (
        <Tooltip title="批量继承场景，可在操作列下载上传清单批量创建继承的执行情况">
          操作<QuestionCircleOutlined/>
        </Tooltip>
      ),
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.scene != 'VIRTUAL_ASSETS') {
          opts.push({ onClick: () => handleDetails(record), name: "继承详情" });
        }
          if (value.uploadResult) {
              opts.push({
                  onClick: () => {
                      apiCall("/customer/takeover/downloadUploadResult", "GET", {fileId: value.uploadResult}, null, {
                          isExit: true,
                          title: '客户继承.xlsx',
                      }).then((res) => {
                          message.success("下载成功！");
                      })

                  }, name: "下载"
              });
          }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxCreateTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      formData.handoverDeptIdList = formData.handoverDeptIdList?.join(',') || null;
      formData.takeoverDeptIdList = formData.takeoverDeptIdList?.join(',') || null;
      formData.createDeptIdList = formData.createDeptIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/customer/takeover', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    props.history.push('/wecom/customerInherit/form');
  };

  const handleDetails = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/customerInherit/detail',
      search: `?id=${id}`
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='Inheritance-Container'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="handoverDeptIdList" style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}>
            <ETypeTransferModal title="原归属人" />
          </FormItem>
          <FormItem name="takeoverDeptIdList" style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}>
            <ETypeTransferModal title="新归属人" />
          </FormItem>
          <FormItem name="createDeptIdList" style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}>
            <ETypeTransferModal title="分配人" />
          </FormItem>
          <FormItem name="state">
            <SysDictSelect placeholder="继承状态" dataset="TakeoverStateEnum" />
          </FormItem>
          <FormItem name="scene">
            <SysDictSelect placeholder="继承方式" dataset="TakeoverSceneEnum" />
          </FormItem>
          <FormItem name="type">
            <SysDictSelect placeholder="员工继承类型" dataset="TakeoverTypeEnum" />
          </FormItem>
          <FormItem name="createTime" label="分配时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增继承
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default Inheritance;
