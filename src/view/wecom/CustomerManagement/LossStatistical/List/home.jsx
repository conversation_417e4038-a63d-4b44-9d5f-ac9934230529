/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/15 10:05
 * @LastEditTime: 2024/07/03 09:41
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/LossStatistical/List/home.jsx
 * @Description: '流失列表'
 */

import React, { useState, useEffect } from "react";
import { withRouter } from "react-router-dom";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { removeInputEmpty } from "common/regular";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import moment from "moment";
import { timeStamp } from "common/date";
import { QuestionCircleOutlined } from "@ant-design/icons";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import {
  Form,
  Input,
  Button,
  Table,
  Card,
  Tooltip,
  Row,
  Col,
  Select,
  DatePicker,
  Avatar,
  TreeSelect,
} from "antd";
import WibotTableTag from 'components/WibotTableTag/home';
import "./home.less";
import { debounce } from "common/fn"
import { findWithDeep } from "@/utils"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;

const List = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [labelOption, setLabelOption] = useState([]);
  const [infoData, setInfoData] = useState({});
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "添加员工",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "好友关系",
      width: "160px",
      dataIndex: "stateType",
      key: "stateType",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "添加时间",
      width: "160px",
      dataIndex: "firstAddTime",
      key: "firstAddTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstAddTime) - timeStamp(b.firstAddTime),
    },
    {
      title: "最近流失时间",
      width: "160px",
      dataIndex: "lastRemovalTime",
      key: "lastRemovalTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastRemovalTime) - timeStamp(b.lastRemovalTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    getTagGroupTree();
    fetchList();
  }, []);

  // 客户标签
  const getTagGroupTree = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelOption([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelOption(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 统计数据
  const getStatisticalData = async () => {
    await apiCall("/customer/statLostCustomer", "GET")
      .then((res) => {
        const data = { ...res };
        data.todayLostDate = moment(data.todayLostDate).format("MM/DD");
        data.weekLostDate = moment(data.weekLostDate).format("MM/DD");
        data.monthLostDate = moment(data.monthLostDate).format("MM/DD");
        data.startAllLostDate = moment(data.startAllLostDate).format(
          "YYYY/MM/DD"
        );
        data.endAllLostDate = moment(data.endAllLostDate).format("YYYY/MM/DD");
        setInfoData(data);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    getStatisticalData();
    setLoading(true);
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startFirstAddTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endFirstAddTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      if (formData.delTime) {
        formData.startLastRemovalTime = moment(formData.delTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endLastRemovalTime = moment(formData.delTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.delTime;
      }
      // formData.tagNameList = formData.tagIdList?.join(",") || null;
      // formData.deptIdList = formData.deptIdList?.join(",") || null;
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall("/customer/lostCustomerList", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      // state: { id: customerId }
      search: `?id=${customerId}`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const [treeExpandedKeys, setTreeExpandedKeys] = useState(["customer"])
  const onKeyDown = (e) => {
    if (e.key === "Enter") {
      // continue
      if (!e.target.value) { return }
      const find = findWithDeep(labelOption, 'title', e.target.value)
      if (!find) {
        onSearch(e.target.value)
        const value = formForm.getFieldValue('tagNameList');
        const newValue = Array.isArray(value) ? [...value, e.target.value] : [e.target.value];
        formForm.setFieldValue('tagNameList', newValue);
        e.target.value = ''
      }
    }
  }
  const onSearch = (label) => {
    if (!label) {
      return
    }
    handleOnSearch(label)
  }

  const handleOnSearch = debounce((label) => {
    setLabelOption((prevState) => {
      // 创建新数组并更新第一个节点的 children
      const newChildren = [
        {
          ...prevState[0],
          children: [
            ...prevState[0].children,
            {
              children: [],
              key: label,
              parentid: null,
              value: label,
              title: label,
            },
          ],
        },
        ...prevState.slice(1), // 保留其他项不变
      ]
      setTreeExpandedKeys((prevState) => [...prevState, label])
      return newChildren
    })
  }, 100)

  const onTreeExpand = (expandedKeys) => {
    setTreeExpandedKeys(prevState => expandedKeys);
  };

  const getTagNameList = (keys = [], data = []) => {
    if (keys.length === 0 || data.length === 0) { return [] }
    const result = [];

    function collectChildren (node) {
      if (node.children.length) {
        node.children.forEach(child => {
          collectChildren(child);
        });
      } else {
        result.push(node.key);
      }
    }

    function traverse (node) {
      if (keys.includes(node.key)) {
        collectChildren(node)
      } else {
        node.children.forEach(child => {
          traverse(child);
        });
      }
    }

    traverse(data && data[0]);
    return result
  }

  return (
    <div className="LossStatistical-List">
      <Card style={{ marginBottom: "20px" }}>
        <Row>
          <Col span={6} className="statistical-item">
            <div className="nums">{infoData?.todayLost || 0}</div>
            <div className="text">
              <span style={{ marginRight: "5px" }}>今日流失</span>
              <Tooltip title="今日内“删除了员工的客户数”与“员工删除的客户数”的总和（去重）">
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div className="date">{infoData?.todayLostDate || '-'}</div>
          </Col>
          <Col span={6} className="statistical-item">
            <div className="nums">{infoData?.weekLost || 0}</div>
            <div className="text">
              <span style={{ marginRight: "5px" }}>近7日流失</span>
              <Tooltip title="7日内（含今日）“删除了员工的客户数”与“员工删除的客户数”的总和（去重）">
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div className="date">
              {infoData.weekLostDate}-{infoData.todayLostDate}
            </div>
          </Col>
          <Col span={6} className="statistical-item">
            <div className="nums">{infoData?.monthLost || 0}</div>
            <div className="text">
              <span style={{ marginRight: "5px" }}>近30日流失</span>
              <Tooltip title="30日内（含今日）“删除了员工的客户数”与“员工删除的客户数”的总和（去重）">
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div className="date">
              {infoData.monthLostDate}-{infoData.todayLostDate}
            </div>
          </Col>
          <Col span={6} className="statistical-item">
            <div className="nums">{infoData?.allLost || 0}</div>
            <div className="text">
              <span style={{ marginRight: "5px" }}>总共流失</span>
              <Tooltip title="“删除了员工的客户数”与“员工删除的客户数”的总和（去重）">
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div className="date">
              {infoData.startAllLostDate}-{infoData.endAllLostDate}
            </div>
          </Col>
        </Row>
      </Card>

      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} form={formForm}>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="客户名称" allowClear />
          </FormItem>
          <FormItem
            name="deptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="员工名称" />
          </FormItem>
          <CustomTagSelect
            name="tagNameList"
            placeholder="客户标签"
            useForm={formForm}
            creatable
            labelTreeData={labelOption}
          />
          <FormItem name="state">
            <Select placeholder="删除类型" allowClear>
              <Option value={"DELETE_BY_CUSTOMER"}>客户删员工</Option>
              <Option value={"DELETE_BY_EMPLOYEE"}>员工删客户</Option>
            </Select>
          </FormItem>
          <FormItem name="time" label="添加时间">
            <RangePicker />
          </FormItem>
          <FormItem name="delTime" label="删除时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(List);
