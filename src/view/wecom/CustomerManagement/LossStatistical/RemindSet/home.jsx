/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/15 10:06
 * @LastEditTime: 2023/06/29 14:39
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/LossStatistical/RemindSet/home.jsx
 * @Description: '流失提醒设置'
 */

import React, { useState, useEffect } from 'react';
import { apiCall } from 'common/utils';
import { Form, Button, message, Switch, Spin, InputNumber, Space } from 'antd';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 21 },
  },
};

const RemindSet = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [deleteFlag, setDeleteFlag] = useState(false);
  const [forbiddenFlag, setForbiddenFlag] = useState(false);

  useEffect(() => {
    init();
  }, []);

  const init = async () => {
    fetchList();
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    await apiCall('/customer/getLostRemind', 'GET')
      .then((res) => {
        formForm.setFieldsValue({
          ...res,
        });
        setDeleteFlag(res.deleteByEmployeeFlag);
        setForbiddenFlag(res.forbiddenFlag);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      console.log(formData, 'formData');
      setLoading(true);
      const data = {
        ...formData,
      };
      apiCall('/customer/addOrUpdate', 'POST', data)
        .then((res) => {
          message.success('设置成功！');
        })
        .catch((err) => {

        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <div className="LossStatistical-RemindSet">
      <Spin spinning={loading}>
        <Form
          {...layout}
          form={formForm}
          initialValues={{
            deleteByCustomerFlag: false,
            deleteByEmployeeFlag: false,
            forbiddenFlag: false,
          }}
        >
          <h2>客户删员工</h2>
          <FormItem
            name="deleteByCustomerFlag"
            label="删除提醒"
            valuePropName="checked"
            extra="当员工被客户删除时，被删除的员工将收到一条消息提醒"
          >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <h2>员工删客户</h2>
          <FormItem label="删除提醒">
            <div>
              <FormItem
                name="deleteByEmployeeFlag"
                valuePropName="checked"
                extra="将按推送设置推送员工删除好友提醒"
                style={{ marginBottom: '0' }}
              >
                <Switch
                  checkedChildren="开"
                  unCheckedChildren="关"
                  onChange={(value) => {
                    setDeleteFlag(value);
                  }}
                />
              </FormItem>
              {deleteFlag && (
                <div
                  style={{
                    width: 'fit-content',
                    padding: '10px',
                    background: '#f2f2f2',
                  }}
                >
                  <FormItem label="推送设置">
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span>当一个员工删除好友数大于或等于</span>
                      <FormItem
                        name="pushThresholdValue"
                        style={{ margin: '0 6px' }}
                      >
                        <InputNumber min={0} precision={0} />
                      </FormItem>
                      <span>时，该员工每次删除好友都推送提醒</span>
                    </div>
                  </FormItem>
                  <FormItem label="推送对象" name="pushEmployeeIds" extra={<>员工本人默认会收到提醒。<br />推送对象仅支持选择一个员工标签，提醒对象仅为该员工主部门及上级部门中符合该标签的所有员工，不再向上查找。</>}>
                    <ETypeTransferModal title="员工标签" onlyTag multiple={false} />
                  </FormItem>
                </div>
              )}
            </div>
          </FormItem>
          <FormItem label="禁用设置">
            <div>
              <FormItem
                name="forbiddenFlag"
                valuePropName="checked"
                extra="此功能需开启企业微信通讯录编辑权限才可使用"
                style={{ marginBottom: '0' }}
              >
                <Switch
                  checkedChildren="开"
                  unCheckedChildren="关"
                  onChange={(value) => {
                    setForbiddenFlag(value);
                  }}
                />
              </FormItem>
              {forbiddenFlag && (
                <div
                  style={{
                    width: 'fit-content',
                    padding: '10px',
                    background: '#f2f2f2',
                  }}
                >
                  <FormItem label="禁用条件">
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span>当一个员工删除</span>
                      <FormItem
                        name="forbiddenThresholdValue"
                        style={{ margin: '0 6px' }}
                      >
                        <InputNumber min={0} precision={0} />
                      </FormItem>
                      <span>
                        个好友时，如该员工在禁用对象内，其账号将被禁用而无法登录企业微信，需由具备操作【系统管理-组织管理】的角色解除禁用
                      </span>
                    </div>
                  </FormItem>
                  <FormItem label="禁用对象" name='forbiddenEmployeeIds'>
                    <ETypeTransferModal title="组织架构" />
                  </FormItem>
                </div>
              )}
            </div>
          </FormItem>

          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button type="primary" onClick={() => onSubmit()}>
                保存
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </div>
  );
};

export default RemindSet;
