/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/15 09:59
 * @LastEditTime: 2023/03/16 14:30
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\LossStatistical\home.jsx
 * @Description: '客户流失统计'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import List from './List/home';
import RemindSet from './RemindSet/home';
import './home.less';

const { TabPane } = Tabs;

const LossStatistical = (props) => {
  const [tabsIndex, setTabsIndex] = useState('');

  useEffect(() => {
    setTabsIndex(localStorage.getItem('TabsLossStatisticalActiveKey') || '1');
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('TabsLossStatisticalActiveKey', index);
    setTabsIndex(index);
  };

  return (
    <div className='LossStatistical'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="流失列表" key="1">
            <List />
          </TabPane>
          <TabPane tab="流失提醒设置" key="2">
            <RemindSet />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default LossStatistical;
