/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/19 10:44
 * @LastEditTime: 2022/05/30 17:23
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\PointVerify\comps\FormModal.jsx
 * @Description: '积分审核对话框'
 */

import React, { useState, memo, useEffect } from 'react';
import { Modal, Form, Input, Radio } from 'antd';
import { removeInputEmpty } from 'common/regular';

const FormItem = Form.Item;
const { TextArea } = Input;

const FormModal = (props) => {
  const { visible, dataSource, onOk, onCancel } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      console.log(dataSource);
    }
  }, [visible]);

  const handleSubmit = () => {
    formForm.validateFields().then((formData) => {
      console.log(formData);
      setConfirmLoading(true);
      onOk?.(formData);
      handleCancel();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };

  const handleCancel = () => {
    formForm.setFieldsValue({});
    setConfirmLoading(false);
    onCancel?.();
  };

  return (
    <Modal visible={visible}
      title={'积分审核'}
      maskClosable={false}
      afterClose={null}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      destroyOnClose
      width={450}>
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} form={formForm}>
        <FormItem name="state" label="审核意见" rules={[{ required: true }]} initialValue={'PASS'}>
          <Radio.Group>
            <Radio value={'PASS'}>通过</Radio>
            <Radio value={'REFUSE'}>拒绝</Radio>
          </Radio.Group>
        </FormItem>
        <FormItem name="reviewRemark" label="审核备注" getValueFromEvent={(e) => removeInputEmpty(e)} rules={[{ required: true, message: '请输入50字以内的文本' }]}>
          <TextArea showCount maxLength={50} placeholder="50字以内的文本" allowClear />
        </FormItem>
      </Form>
    </Modal>
  );
};
export default memo(FormModal);
