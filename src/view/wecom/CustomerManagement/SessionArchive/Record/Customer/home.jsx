/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2023/05/09 10:52
 * @LastEditTime: 2024/10/22 09:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/SessionArchive/Record/Customer/home.jsx
 * @Description: '客户记录'
 */

import React, { useState, useEffect } from 'react';
import {
  Spin,
  Form,
  Input,
  Button,
  Select,
  Avatar,
  Tabs,
  DatePicker,
  Empty,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { getTimeOrDate } from 'common/date';
import ChatContent from '../comps/ChatContent/home';
import CustomerFormModal from './comps/CustomerFormModal/home';
import InfiniteScroll from 'react-infinite-scroll-component';
import './home.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const CustomerRecord = (props) => {
  const [formLeft] = Form.useForm();
  const [formMidStaff] = Form.useForm();
  const [formMidGroup] = Form.useForm();
  const [formRight] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [customerList, setCustomerList] = useState([]);
  const [staffList, setStaffList] = useState([]);
  const [groupList, setGroupList] = useState([]);
  const [messageList, setMessageList] = useState([]);
  const [customerInfo, setCustomerInfo] = useState(null);
  const [tabsIndex, setTabsIndex] = useState('2');
  const [chatInfo, setChatInfo] = useState(null);
  const [typeOption, setTypeOption] = useState([]);
  const [customerParams, setCustomerParams] = useState({ visible: false });
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations2, setPaginations2] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations3, setPaginations3] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations4, setPaginations4] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [activeKey1, setActiveKey1] = useState(null);
  const [activeKey2, setActiveKey2] = useState(null);
  const [activeKey3, setActiveKey3] = useState(null);
  const [disagreeCustomerCount, setDisagreeCustomerCount] = useState(0);
  const [currentMessageList, setCurrentMessageList] = useState([]);

  useEffect(() => {
    fetchList();
    getMessageTypeOptions();
    getDisagreeCustomerCount();
  }, []);

  // 监听客户列表切换客户
  useEffect(() => {
    if (activeKey1) {
      const infoData = customerList.find(
        (item) => item.customerId == activeKey1
      );
      fetchStaffConversationByPage({
        customerId: infoData.customerId,
        isInitGroup: true,
      });
      setCustomerInfo(infoData);
    }
  }, [activeKey1]);

  // 查询消息选项
  const getMessageTypeOptions = () => {
    apiCall('/messageArchive/options', 'GET')
      .then((res) => {
        setTypeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取未授权客户数
  const getDisagreeCustomerCount = () => {
    apiCall('/messageArchive/disagreeConversationCustomerList', 'GET')
      .then((res) => {
        setDisagreeCustomerCount(res.total);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 会话记录客户列表分页
  const fetchList = (params = {}) => {
    formLeft.validateFields().then((formData) => {
      setLoading(true);
      const { query, pagination, isQuery = false } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/messageArchive/listCustomerInfoByAgreePage', 'GET', data)
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setCustomerList(
                current == 1 ? [...records] : [...customerList, ...records]
              );
              // 查询同一个客户多次
              if (activeKey1 == records[0].customerId && current == 1) {
                fetchStaffConversationByPage({
                  isQuery: true,
                  customerId: records[0].customerId,
                  isInitGroup: true,
                });
              }
            } else {
              setCustomerList([...customerList, ...records]);
            }
            console.log(current, records[0].customerId);
            // current == 1 && setActiveKey1(records[0].customerId);
          } else {
            setCustomerList([]);
            setActiveKey1(null);
          }
          setPaginations1({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 会话记录客户会话列表分页
  // 员工单聊
  const fetchStaffConversationByPage = (params = {}) => {
    formMidStaff.validateFields().then((formData) => {
      setLoading(true);
      const {
        query,
        pagination,
        isQuery = false,
        customerId,
        isInitGroup = false,
      } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: '2',
        ...query,
        ...formData,
      };
      apiCall(
        `/messageArchive/listConversationByPage/${activeKey1}`,
        'GET',
        data
      )
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setStaffList(
                current == 1 ? [...records] : [...staffList, ...records]
              );
            } else {
              setStaffList([...staffList, ...records]);
            }

            if (current == 1 && tabsIndex == '2') {
              // setActiveKey2(records[0].id);
              // setChatInfo(records[0]);
              fetchListConversationRecordsByPage({
                isQuery: true,
                dataObj: {
                  employeeId: records[0].id,
                  customerId: customerId || customerInfo.customerId,
                },
              });
            }
          } else {
            setStaffList([]);
            setActiveKey2(null);
            tabsIndex == '2' && setChatInfo(null);
          }
          setPaginations2({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
          isInitGroup && fetchGroupConversationByPage({ isQuery: true });
        });
    });
  };

  // 外部群聊
  const fetchGroupConversationByPage = (params = {}) => {
    formMidGroup.validateFields().then((formData) => {
      setLoading(true);
      const { query, pagination, isQuery = false } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: '1',
        ...query,
        ...formData,
      };
      apiCall(
        `/messageArchive/listConversationByPage/${activeKey1}`,
        'GET',
        data
      )
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setGroupList(
                current == 1 ? [...records] : [...groupList, ...records]
              );
            } else {
              setGroupList([...groupList, ...records]);
            }

            if (current == 1 && tabsIndex == '1') {
              // setActiveKey3(records[0].id);
              // setChatInfo(records[0]);
              fetchListConversationRecordsByPage({
                isQuery: true,
                dataObj: { groupId: records[0].id },
              });
            }
          } else {
            setGroupList([]);
            setActiveKey3(null);
            tabsIndex == '1' && setChatInfo(null);
          }
          setPaginations3({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 会话记录分页
  const fetchListConversationRecordsByPage = (params = {}) => {
    formRight.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.messageStartTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.messageEndTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      const { pagination, dataObj = {}, isQuery = false } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...dataObj,
        ...formData,
      };
      apiCall('/messageArchive/listConversationRecordsByPage', 'POST', data)
        .then((res) => {
          const { current, size, total, records } = res;
          setCurrentMessageList([...records]);
          if (isQuery) {
            setMessageList(current == 1 ? [...records] : [...messageList, ...records]);
          } else {
            setMessageList([...messageList, ...records]);
          }
          setPaginations4({
            current,
            pageSize: size,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = (type) => {
    if (type == 'customer') {
      setStaffList([]);
      setGroupList([]);
      setMessageList([]);
      fetchList({ isQuery: true });
    } else if (type == 'staff') {
      fetchStaffConversationByPage({ isQuery: true });
    } else if (type == 'group') {
      fetchGroupConversationByPage({ isQuery: true });
    } else {
      const dataObj =
        tabsIndex == '1'
          ? { groupId: chatInfo.id }
          : { employeeId: chatInfo.id, customerId: customerInfo.customerId };
      fetchListConversationRecordsByPage({
        dataObj,
        isQuery: true,
      });
    }
  };

  // 未授权客户弹窗
  const handleAccredit = () => {
    setCustomerParams({
      visible: true,
      onCancel: () => {
        setCustomerParams({ visible: false });
      },
    });
  };

  // 客户(员工)(群)下拉加载
  const loadMoreData = (e, type) => {
    if (type == 'customer') {
      if (customerList.length < paginations1.total) {
        const pageInfo = {
          ...paginations1,
          current: paginations1.current + 1,
        };
        fetchList({ pagination: pageInfo });
      }
    } else if (type == 'staff') {
      if (staffList.length < paginations2.total) {
        const pageInfo = {
          ...paginations2,
          current: paginations2.current + 1,
        };
        fetchStaffConversationByPage({ pagination: pageInfo });
      }
    } else if (type == 'group') {
      if (groupList.length < paginations3.total) {
        const pageInfo = {
          ...paginations3,
          current: paginations3.current + 1,
        };
        fetchGroupConversationByPage({ pagination: pageInfo });
      }
    }
  };

  useEffect(() => {
    if (activeKey2) {
      setMessageList([]);
      const params = {
        employeeId: activeKey2,
        customerId: customerInfo.customerId,
      };
      fetchListConversationRecordsByPage({
        dataObj: params,
      });
    }
  }, [activeKey2]);

  useEffect(() => {
    if (activeKey3) {
      setMessageList([]);
      fetchListConversationRecordsByPage({ dataObj: { groupId: activeKey3 } });
    }
  }, [activeKey3]);

  // 点击选择客户(员工)(群)
  const handleChoose = (data, type) => {
    if (type == 'customer' && data.customerId != activeKey1) {
      console.log("customer")
      setActiveKey1(data.customerId);
      setActiveKey2(null);
      setActiveKey3(null);
      setChatInfo(null)
      setStaffList([]);
      setGroupList([]);
      setMessageList([]);
    } else if (type == 'staff' && data.id != activeKey2) {
      setActiveKey2(data.id);
      setChatInfo(data);
      setMessageList([]);
    } else if (type == 'group' && data.id != activeKey3) {
      setActiveKey3(data.id);
      setChatInfo(data);
      setMessageList([]);
    }
  };

  // 切换群聊单聊tab
  // useEffect(() => {
  //   if (tabsIndex == '1' && groupList.length > 0) {
  //     fetchListConversationRecordsByPage({
  //       dataObj: { groupId: groupList[0].id },
  //     });
  //   } else if (tabsIndex == '2' && staffList.length > 0) {
  //     fetchListConversationRecordsByPage({
  //       dataObj: {
  //         employeeId: staffList[0].id,
  //         customerId: customerInfo.customerId,
  //       },
  //     });
  //   }
  // }, [tabsIndex]);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
    // formMidStaff.resetFields();
    // formMidGroup.resetFields();
    setMessageList([]);
    if (index == '1') {
      // setChatInfo(groupList.length > 0 ? groupList[0] : null);
      setChatInfo(null);
      // setActiveKey3(groupList.length > 0 ? groupList[0].id : null);
      setActiveKey3(null);
    } else {
      // setChatInfo(staffList.length > 0 ? staffList[0] : null);
      setChatInfo(null);
      // setActiveKey2(staffList.length > 0 ? staffList[0].id : null);
      setActiveKey2(null);
    }
  };

  return (
    <Spin spinning={loading}>
      <div className="CustomerRecord">
        <div className="record-left">
          <div className="flex-between">
            <h2>客户列表（{paginations1.total}）</h2>
            <a onClick={handleAccredit}>未授权（{disagreeCustomerCount}）</a>
          </div>
          <Form layout={'inline'} form={formLeft}>
            <FormItem
              name="filterName"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input
                placeholder="客户名称"
                allowClear
                style={{ width: '180px' }}
              />
            </FormItem>
            <Button type="primary" onClick={() => handleQuery('customer')}>
              搜索
            </Button>
          </Form>
          <div className="record-left-info" id="scrollableDiv1">
            {customerList.length > 0 ? (
              <InfiniteScroll
                dataLength={customerList.length}
                next={(e) => {
                  loadMoreData(e, 'customer');
                }}
                hasMore={customerList.length < paginations1.total}
                // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                scrollableTarget="scrollableDiv1"
              >
                {customerList.map((item, index) => (
                  <div
                    className={`ant-tree-title-nodeData ${activeKey1 == item.customerId ? 'active' : ''
                      }`}
                    key={index}
                    onClick={() => {
                      handleChoose(item, 'customer');
                    }}
                  >
                    <Avatar size={30} src={item.avatar} />
                    <div className="ant-tree-title-nodeData-left">
                      <div>
                        {item.name}
                        <span
                          style={{
                            color: item.type == 1 ? '#07c160' : '#f59a23',
                          }}
                        >
                          {item.companyName}
                        </span>
                      </div>
                      <span>{item.realName}</span>
                    </div>
                  </div>
                ))}
              </InfiniteScroll>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>
        {activeKey1 && customerInfo && (
          <div className="record-mid">
            <div className="ant-tree-title-nodeData">
              <Avatar size={30} src={customerInfo.avatar} />
              <div className="ant-tree-title-nodeData-left">
                <div>
                  {customerInfo.name}
                  <span
                    style={{
                      color: customerInfo.type == 1 ? '#07c160' : '#f59a23',
                    }}
                  >
                    {customerInfo.companyName}
                  </span>
                </div>
                <span>{customerInfo.realName}</span>
              </div>
            </div>
            <Tabs
              activeKey={tabsIndex}
              destroyInactiveTabPane
              onChange={onChangeTabs}
            >
              <TabPane tab={`员工单聊（${paginations2.total}）`} key="2">
                <Form layout={'inline'} form={formMidStaff}>
                  <FormItem
                    name="filterName"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input
                      placeholder="员工名称"
                      allowClear
                      style={{ width: '180px' }}
                    />
                  </FormItem>
                  <Button type="primary" onClick={() => handleQuery('staff')}>
                    搜索
                  </Button>
                </Form>
                <div className="record-mid-info" id="scrollableDiv2">
                  {staffList.length > 0 ? (
                    <InfiniteScroll
                      dataLength={staffList.length}
                      next={(e) => {
                        loadMoreData(e, 'staff');
                      }}
                      hasMore={staffList.length < paginations2.total}
                      // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                      // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                      scrollableTarget="scrollableDiv2"
                    >
                      {staffList.map((item, index) => (
                        <div
                          className={`ant-tree-title-nodeData ${activeKey2 == item.id ? 'active' : ''
                            }`}
                          key={index}
                          onClick={() => {
                            handleChoose(item, 'staff');
                          }}
                        >
                          <Avatar size={30} src={item.avatar} />
                          <div className="ant-tree-title-nodeData-left">
                            <div>{item.name}</div>
                            <span>{item.departmentName}</span>
                          </div>
                          {item.lastConversationTime && (
                            <div className="ant-tree-title-nodeData-center">
                              {getTimeOrDate(item.lastConversationTime)}
                            </div>
                          )}
                        </div>
                      ))}
                    </InfiniteScroll>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </div>
              </TabPane>
              <TabPane tab={`外部群聊（${paginations3.total}）`} key="1">
                <Form layout={'inline'} form={formMidGroup}>
                  <FormItem
                    name="filterName"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input
                      placeholder="客户群名称"
                      allowClear
                      style={{ width: '180px' }}
                    />
                  </FormItem>
                  <Button type="primary" onClick={() => handleQuery('group')}>
                    搜索
                  </Button>
                </Form>
                <div className="record-mid-info" id="scrollableDiv2">
                  {groupList.length > 0 ? (
                    <InfiniteScroll
                      dataLength={groupList.length}
                      next={(e) => {
                        loadMoreData(e, 'group');
                      }}
                      hasMore={groupList.length < paginations3.total}
                      // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                      // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                      scrollableTarget="scrollableDiv2"
                    >
                      {groupList.map((item, index) => (
                        <div
                          className={`ant-tree-title-nodeData ${activeKey3 == item.id ? 'active' : ''
                            }`}
                          key={index}
                          onClick={() => {
                            handleChoose(item, 'group');
                          }}
                        >
                          <div className="ant-tree-title-nodeData-left">
                            <div>
                              {item.groupDeleted == '1' && ['已解散']}
                              {item.name}（{item.groupMemberCount}）
                            </div>
                            <span>
                              群主：{item.leaderEmployeeName}(
                              {item.departmentName})
                            </span>
                          </div>
                          {item.lastConversationTime && (
                            <div className="ant-tree-title-nodeData-center">
                              {getTimeOrDate(item.lastConversationTime)}
                            </div>
                          )}
                        </div>
                      ))}
                    </InfiniteScroll>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}
        {activeKey1 && chatInfo ? (
          <div className="record-right">
            <div className="record-right-top">
              <div className="ant-tree-title-nodeData">
                <div className="ant-tree-title-nodeData-left">
                  {tabsIndex == '1' ? (
                    <>
                      <div>
                        {chatInfo.groupDeleted == '1' && ['已解散']}
                        {chatInfo.name}（{chatInfo.groupMemberCount}）
                      </div>
                      <span>
                        群主：{chatInfo.leaderEmployeeName}(
                        {chatInfo.departmentName})
                      </span>
                    </>
                  ) : (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar size={30} src={chatInfo.avatar} />
                      <div style={{ marginLeft: '8px' }}>
                        <div>{chatInfo.name}</div>
                        <span>{chatInfo.departmentName}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <Form layout={'inline'} form={formRight}>
                <FormItem name="messageType" style={{ marginBottom: '10px' }}>
                  <Select
                    placeholder="全部"
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={typeOption}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                <FormItem
                  name="messageContent"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  style={{ marginBottom: '10px' }}
                >
                  <Input
                    placeholder="聊天内容"
                    allowClear
                    style={{ width: '140px' }}
                  />
                </FormItem>
                <FormItem name="time" style={{ marginBottom: '10px' }}>
                  <RangePicker style={{ width: '220px' }} />
                </FormItem>
                <Button type="primary" onClick={() => handleQuery()}>
                  搜索
                </Button>
              </Form>
            </div>
            <ChatContent
              params={{
                data: messageList,
                hasMore: currentMessageList.length > 0,
                showTips: tabsIndex == 2,
                customerId: customerInfo.customerId,
                employeeId: chatInfo.id,
                changeId: chatInfo.id,
                chatLoadMoreData: (e) => {
                  if (currentMessageList.length > 0) {
                    const pageInfo = {
                      ...paginations4,
                      current: paginations4.current + 1,
                    };
                    const dataObj =
                      tabsIndex == '1'
                        ? { groupId: chatInfo.id }
                        : {
                          employeeId: chatInfo.id,
                          customerId: customerInfo.customerId,
                        };
                    fetchListConversationRecordsByPage({
                      dataObj,
                      pagination: pageInfo,
                    });
                  }
                },
              }}
            />
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: 'auto' }}
          />
        )}
        <CustomerFormModal params={customerParams} />
      </div>
    </Spin>
  );
};

export default CustomerRecord;
