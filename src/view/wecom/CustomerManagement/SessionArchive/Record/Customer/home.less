.CustomerRecord {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;

  .record-left {
    width: 290px;
    box-sizing: border-box;
    border-right: 2px solid #e5e5e5;
    padding-right: 10px;

    .flex-between {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h2 {
        margin-bottom: 0px;
      }
    }

    .ant-tree-switcher-noop {
      display: none;
    }

    .record-left-info {
      padding-top: 10px;
      height: 646px;
      overflow: auto;
    }
  }

  .ant-tree-title-nodeData {
    display: flex;
    font-size: 13px;
    align-items: center;
    // margin-bottom: 8px;
    min-height: 24px;
    line-height: 24px;
    cursor: pointer;
    padding: 8px 5px;
    border-radius: 2px;
    transition: all 0.3s, border 0s, line-height 0s, box-shadow 0s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      background-color: #bed1ee;
    }

    .ant-tree-title-nodeData-left {
      margin-left: 8px;
      flex: 1;
      line-height: 15px;
    }
  }

  .record-mid {
    width: 290px;
    box-sizing: border-box;
    padding-left: 10px;
    border-right: 2px solid #e5e5e5;

    .ant-tree-switcher-noop {
      display: none;
    }

    .record-mid-info {
      padding-top: 10px;
      height: 580px;
      overflow: auto;
    }

    // .mid-top{
    //   display: flex;
    //   align-items: center;
    // }
  }

  .record-right {
    flex: 1;
    background-color: #f5f5f5;

    .record-right-top {
      padding: 0 10px 10px 10px;
      background-color: #fff;
    }

    // .record-right-content{
    //   padding: 10px;
    //   // background-color: #f5f5f5;
    //   border-top: 2px solid #e5e5e5;

    // }
  }
}