/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/10 14:59
 * @LastEditTime: 2024/02/26 15:23
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\SessionArchive\Record\Group\comps\CustomerFormModal\home.jsx
 * @Description: '未同意存档客户'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Form,
  message,
  Modal,
  Spin,
  Button,
  Table,
  Avatar,
  Tooltip,
} from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import AddLabelModal from "components/Modal/AddLabelModal/AddLabelModal";

const FormItem = Form.Item;

const CustomerFormModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [labelVisible, setLabelVisible] = useState(false);
  const [labelConfirmLoading, setLabelConfirmLoading] = useState(false);

  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "关联员工",
      width: "200px",
      dataIndex: "employeeName",
      key: "employeeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "员工所属部门",
      width: "160px",
      dataIndex: "departmentName",
      key: "departmentName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      let timer = setTimeout(() => {
        fetchList();
        clearTimeout(timer);
      }, 100);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      const { query, pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };

      formData.employeeIds = formData.employeeIds?.join(",") || null;
      formData.deptIds = formData.deptIds?.join(",") || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/message/disagreeConversationCustomerList", "GET", data)
        .then((res) => {
          const { current, size, total, pages, records } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customerId}`,
    });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startDate = moment(formData.date[0]._d).format("YYYY-MM-DD");
        formData.endDate = moment(formData.date[1]._d).format("YYYY-MM-DD");
        delete formData.date;
      }
      const data = {
        ...formData,
        type: "CUSTOMER_STATISTICS",
      };
      apiCall("/customer/export", "POST", data, null, {
        isExit: true,
        title: `客户统计报表.${moment().format("YYYY-MM-DD")}.xlsx`,
      })
        .then((res) => {
          message.success("导出成功！");
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    // fetchList();
  };

  const handleAddLabelSubmit = (data) => {
    setLabelConfirmLoading(true);
    const customerIdList = getFilterSelectedData().map(
      (item) => item.customerId
    );
    const tagIdList = data.filter((item) => !!Number(item));
    const queryData = {
      customerIdList,
      tagIdList,
    };
    apiCall("/customer/tag", "POST", queryData)
      .then((res) => {
        message.success("打标签成功");
        setSelectedRowKeys([]);
        setSelectedRows([]);
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLabelConfirmLoading(false);
        setLabelVisible(false);
      });
  };

  const handleLabel = () => {
    setLabelVisible(true);
  };

  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      formData.employeeIds = formData.employeeIds?.join(",") || null;
      formData.deptIds = formData.deptIds?.join(",") || null;
      const data = {
        ...formData,
      };
      apiCall(
        "/message/disagreeConversationCustomerExport",
        "POST",
        data,
        null,
        {
          isExit: true,
          title: `未同意会话存档的客户.${moment().format("YYYY-MM-DD")}.xlsx`,
        }
      )
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };
  const onChangeTable = (pagination, filters, sorter) => {
    setPaginations({
      ...pagination,
      showTotal: (total, range) => `共 ${total} 条记录`,
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  };

  //所选客户去重
  const getFilterSelectedData = () => {
    let newArr = [];
    const newList = JSON.parse(JSON.stringify(selectedRows));
    if (newList.length > 0) {
      for (let i = 0; i < newList.length; i++) {
        if (newArr.indexOf(newList[i].customerId) == -1) {
          newArr.push(newList[i].customerId);
        } else {
          newList.splice(i, 1);
          i--;
        }
      }
    }
    return newList;
  };

  return (
    <Modal
      className="CustomerFormModal"
      width={900}
      visible={visible}
      title={`未同意会话存档的客户（${dataSource.length}人）`}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef} layout={"inline"}>
          <FormItem
            name="employeeIds"
            style={{
              display: "inline-block",
              minWidth: "unset",
              maxWidth: "200px",
            }}
          >
            <ETypeTransferModal title="客户负责人" />
          </FormItem>
          <FormItem
            name="deptIds"
            style={{
              display: "inline-block",
              minWidth: "unset",
              maxWidth: "200px",
            }}
          >
            <ETypeTransferModal
              title="所属部门"
              onlyDepartment
              multiple={false}
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between" style={{ margin: "20px 0" }}>
          <div>
            <Button
              type="primary"
              onClick={() => handleQuery()}
              style={{ marginRight: "20px" }}
            >
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button
              type="primary"
              onClick={() => handleLabel()}
              style={{ marginRight: "20px" }}
              disabled={!selectedRowKeys.length > 0}
            >
              批量打标签
            </Button>
            <Button onClick={() => handleExport()}>导出Excel</Button>
          </div>
        </div>
        <Table
          rowKey="id"
          dataSource={dataSource}
          columns={columns}
          // scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
        <AddLabelModal
          visible={labelVisible}
          selectedData={getFilterSelectedData()}
          confirmLoading={labelConfirmLoading}
          onCancel={() => {
            setLabelVisible(false);
          }}
          handleSubmit={(data) => {
            handleAddLabelSubmit(data);
          }}
        />
      </Spin>
    </Modal>
  );
};

export default withRouter(CustomerFormModal);
