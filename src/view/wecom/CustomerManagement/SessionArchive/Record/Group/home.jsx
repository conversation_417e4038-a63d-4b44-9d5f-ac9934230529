/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2023/05/09 10:52
 * @LastEditTime: 2024/02/26 15:17
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\SessionArchive\Record\Group\home.jsx
 * @Description: '群聊记录'
 */

import React, { useState, useEffect } from 'react';
import {
  Spin,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Empty,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { getTimeOrDate } from 'common/date';
import ChatContent from '../comps/ChatContent/home';
import InfiniteScroll from 'react-infinite-scroll-component';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const GroupRecord = (props) => {
  const [formLeft] = Form.useForm();
  const [formRight] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [listData1, setListData1] = useState([]);
  const [messageList, setMessageList] = useState([]);
  const [groupInfo, setGroupInfo] = useState(null);
  const [typeOption, setTypeOption] = useState([]);
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations4, setPaginations4] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [activeKey1, setActiveKey1] = useState(null);
  const [currentMessageList, setCurrentMessageList] = useState([]);

  useEffect(() => {
    fetchList();
    getMessageTypeOptions();
  }, []);

  // 监听客户列表切换客户
  useEffect(() => {
    if (activeKey1) {
      setMessageList([]);
      const infoData = listData1.find((item) => item.id == activeKey1);
      fetchListConversationRecordsByPage({ groupId: infoData.id });
      setGroupInfo(infoData);
    }
  }, [activeKey1]);

  // 查询消息选项
  const getMessageTypeOptions = () => {
    apiCall('/messageArchive/options', 'GET')
      .then((res) => {
        setTypeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 会话记录客户群列表分页
  const fetchList = (params = {}) => {
    formLeft.validateFields().then((formData) => {
      setLoading(true);
      const { query, pagination, isQuery = false } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: '1',
        ...query,
        ...formData,
      };
      apiCall('/messageArchive/listConversationGroupByPage', 'GET', data)
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setListData1(
                current == 1 ? [...records] : [...listData1, ...records]
              );
            } else {
              setListData1([...listData1, ...records]);
            }
            // current == 1 && setActiveKey1(records[0].id);
          } else {
            setListData1([]);
          }
          setPaginations1({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 会话记录分页
  const fetchListConversationRecordsByPage = (params = {}) => {
    formRight.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.messageStartTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.messageEndTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      const { pagination, isQuery = false, groupId = null } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        groupId: groupId || groupInfo.id,
        ...formData,
      };
      apiCall('/messageArchive/listConversationRecordsByPage', 'POST', data)
        .then((res) => {
          const { current, size, total, records } = res;
          setCurrentMessageList([...records]);
          if (isQuery) {
            setMessageList(current == 1 ? [...records] : [...messageList, ...records]);
          } else {
            setMessageList([...messageList, ...records]);
          }
          setPaginations4({
            current,
            pageSize: size,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList({ isQuery: true });
  };

  // 客户(员工)(群)下拉加载
  const loadMoreData = (e) => {
    if (listData1.length < paginations1.total) {
      const pageInfo = {
        ...paginations1,
        current: paginations1.current + 1,
      };
      fetchList({ pagination: pageInfo });
    }
  };

  // 点击选择客户(员工)(群)
  const handleChoose = (data) => {
    if (data.id != activeKey1) {
      setMessageList([]);
      setActiveKey1(data.id);
    }
  };

  return (
    <Spin spinning={loading}>
      <div className="GroupRecord">
        <div className="record-left">
          <div className="flex-between">
            <h2>客户群列表（{paginations1.total}）</h2>
          </div>
          <Form layout={'inline'} form={formLeft}>
            <FormItem
              name="filterName"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input
                placeholder="客户群名称"
                allowClear
                style={{ width: '180px' }}
              />
            </FormItem>
            <Button type="primary" onClick={() => handleQuery()}>
              搜索
            </Button>
          </Form>
          <div className="record-left-info" id="scrollableDiv1">
            {listData1.length > 0 ? (
              <InfiniteScroll
                dataLength={listData1.length}
                next={(e) => {
                  loadMoreData(e);
                }}
                hasMore={listData1.length < paginations1.total}
                // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                scrollableTarget="scrollableDiv1"
              >
                {listData1.map((item, index) => (
                  <div
                    className={`ant-tree-title-nodeData ${activeKey1 == item.id ? 'active' : ''
                      }`}
                    key={index}
                    onClick={() => {
                      handleChoose(item);
                    }}
                  >
                    <div className="ant-tree-title-nodeData-left">
                      <div>
                        {item.groupDeleted == '1' && ['已解散']}
                        {item.name}（{item.groupMemberCount}）
                      </div>
                      <span>
                        群主：{item.leaderEmployeeName}({item.departmentName})
                      </span>
                    </div>
                    {item.lastConversationTime && (
                      <div className="ant-tree-title-nodeData-center">
                        {getTimeOrDate(item.lastConversationTime)}
                      </div>
                    )}
                  </div>
                ))}
              </InfiniteScroll>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>
        {groupInfo ? (
          <div className="record-right">
            <div className="record-right-top">
              <div className="ant-tree-title-nodeData">
                <div className="ant-tree-title-nodeData-left">
                  <div>
                    {groupInfo.groupDeleted == '1' && ['已解散']}
                    {groupInfo.name}（{groupInfo.groupMemberCount}）
                  </div>
                  <span>
                    群主：{groupInfo.leaderEmployeeName}(
                    {groupInfo.departmentName})
                  </span>
                </div>
              </div>
              <Form layout={'inline'} form={formRight}>
                <FormItem name="messageType" style={{ marginBottom: '10px' }}>
                  <Select
                    placeholder="全部"
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={typeOption}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                <FormItem
                  name="messageContent"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  style={{ marginBottom: '10px' }}
                >
                  <Input
                    placeholder="聊天内容"
                    allowClear
                    style={{ width: '140px' }}
                  />
                </FormItem>
                <FormItem name="time" style={{ marginBottom: '10px' }}>
                  <RangePicker style={{ width: '220px' }} />
                </FormItem>
                <Button
                  type="primary"
                  onClick={() => {
                    fetchListConversationRecordsByPage({
                      isQuery: true,
                    });
                  }}
                >
                  搜索
                </Button>
              </Form>
            </div>
            <ChatContent
              params={{
                data: messageList,
                hasMore: currentMessageList.length > 0,
                showTips: false,
                chatLoadMoreData: (e) => {
                  if (currentMessageList.length > 0) {
                    const pageInfo = {
                      ...paginations4,
                      current: paginations4.current + 1,
                    };
                    fetchListConversationRecordsByPage({
                      pagination: pageInfo,
                    });
                  }
                },
              }}
            />
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: 'auto' }}
          />
        )}
      </div>
    </Spin>
  );
};

export default GroupRecord;
