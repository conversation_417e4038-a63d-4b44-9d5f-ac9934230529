/*
 * @Author: <PERSON>xiaoyan
 * @Date: 2023/05/09 10:52
 * @LastEditTime: 2024/11/14 17:19
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/SessionArchive/Record/Staff/home.jsx
 * @Description: '员工记录'
 */

import React, { useState, useEffect } from 'react';
import {
  Spin,
  Form,
  Input,
  Button,
  Select,
  Avatar,
  Tabs,
  DatePicker,
  Empty,
  Tree,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { getTimeOrDate } from 'common/date';
import ChatContent from '../comps/ChatContent/home';
import CustomerFormModal from './comps/CustomerFormModal/home';
import InfiniteScroll from 'react-infinite-scroll-component';
import { recursionOrgOptionDepData, flatten } from 'common/tree';
import './home.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const StaffRecord = (props) => {
  const [formLeft] = Form.useForm();
  const [formMid1] = Form.useForm();
  const [formMid2] = Form.useForm();
  const [formRight] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [customerList, setCustomerList] = useState([]);
  const [initStaffList, setInitStaffList] = useState([]);
  const [treeDataList, setTreeDataList] = useState([]);
  const [groupList, setGroupList] = useState([]);
  const [messageList, setMessageList] = useState([]);
  const [staffInfo, setStaffInfo] = useState(null);
  const [tabsIndex, setTabsIndex] = useState('2');
  const [chatInfo, setChatInfo] = useState(null);
  const [typeOption, setTypeOption] = useState([]);
  const [customerParams, setCustomerParams] = useState({ visible: false });
  const [paginations1, setPaginations1] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations2, setPaginations2] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations3, setPaginations3] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [paginations4, setPaginations4] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [activeKey2, setActiveKey2] = useState(null);
  const [activeKey3, setActiveKey3] = useState(null);
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState('');
  const [staffCount, setStaffCount] = useState(0);
  const [employeeInfo, setEmployeeInfo] = useState('');
  const [currentMessageList, setCurrentMessageList] = useState([]);

  useEffect(() => {
    fetchList();
    getMessageTypeOptions();
  }, []);

  // 查询消息选项
  const getMessageTypeOptions = () => {
    apiCall('/messageArchive/options', 'GET')
      .then((res) => {
        setTypeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 会话记录员工列表分页
  const fetchList = (params = {}) => {
    formLeft.validateFields().then(async (formData) => {
      setLoading(true);
      const data = {
        ...formData,
      };
      // 获取员工没有权限的部门编号列表(禁用员工列表)
      const disableList = await apiCall(
        '/employee/getEmployeeDisableDepartmentIds',
        'GET'
      );
      await apiCall('/employee/org_option', 'GET', data)
        .then((res) => {
          if (res.length > 0) {
            const list = recursionOrgOptionDepData(res, disableList);
            const defaultObj = generateList(list, []).find(
              (item) => item.type != 'dep' && !item.disabled
            );
            if (defaultObj) {
              // setStaffInfo(defaultObj);
              // setDefaultSelectedKeys(defaultObj.key);
              // getEmployeeInfoById(defaultObj.id);
              // fetchCustomerConversationByPage({ employeeId: defaultObj.id });
              // fetchGroupConversationByPage({ employeeId: defaultObj.id });
            }

            setStaffCount(
              generateList(res, []).filter((item) => item.type == 'emp').length
            );
            setInitStaffList(list);
            setTreeDataList(list);
          } else {
            setStaffInfo(null);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 通过员工id获取对应信息
  const getEmployeeInfoById = (id) => {
    apiCall(`/employee/${id}`, 'GET')
      .then((res) => {
        setEmployeeInfo(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 将树形节点改为一维数组
  const generateList = (data, dataList) => {
    for (let i = 0; i < data.length; i++) {
      const nodeItem = data[i];
      dataList.push({ ...nodeItem });
      if (nodeItem.children) {
        generateList(nodeItem.children, dataList);
      }
    }
    return dataList;
  };

  // 会话记录客户会话列表分页
  // 客户单聊
  const fetchCustomerConversationByPage = (params = {}) => {
    formMid1.validateFields().then((formData) => {
      setLoading(true);
      const {
        query,
        pagination,
        isQuery = false,
        employeeId,
        isInitGroup = false,
      } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: '2',
        ...query,
        ...formData,
      };
      apiCall(
        `/messageArchive/listConversationCustomerByPage/${employeeId ||
        staffInfo.id}`,
        'GET',
        data
      )
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setCustomerList(
                current == 1 ? [...records] : [...customerList, ...records]
              );
            } else {
              setCustomerList([...customerList, ...records]);
            }

            if (current == 1 && tabsIndex == '2') {
              // setActiveKey2(records[0].id);
              // setChatInfo(records[0]);
              fetchListConversationRecordsByPage({
                dataObj: {
                  employeeId: employeeId || staffInfo.id,
                  customerId: records[0].id,
                },
              });
            }
          } else {
            setCustomerList([]);
            setActiveKey2(null);
            tabsIndex == '2' && setChatInfo(null);
          }
          setPaginations2({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
          isInitGroup && fetchGroupConversationByPage({ employeeId });
        });
    });
  };

  // 外部群聊
  const fetchGroupConversationByPage = (params = {}) => {
    formMid2.validateFields().then((formData) => {
      setLoading(true);
      const { query, pagination, isQuery = false, employeeId } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: '1',
        ...query,
        ...formData,
      };
      apiCall(
        `/messageArchive/listConversationCustomerByPage/${employeeId ||
        staffInfo.id}`,
        'GET',
        data
      )
        .then((res) => {
          const { current, size, total, records } = res;
          if (records.length > 0) {
            if (isQuery) {
              setGroupList(
                current == 1 ? [...records] : [...groupList, ...records]
              );
            } else {
              setGroupList([...groupList, ...records]);
            }

            if (current == 1 && tabsIndex == '1') {
              // setActiveKey3(records[0].id);
              // setChatInfo(records[0]);
              fetchListConversationRecordsByPage({
                dataObj: { groupId: records[0].id },
              });
            }
          } else {
            setGroupList([]);
            setActiveKey3(null);
            tabsIndex == '1' && setChatInfo(null);
          }
          setPaginations3({
            current,
            pageSize: size,
            total,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 会话记录分页
  const fetchListConversationRecordsByPage = (params = {}) => {
    formRight.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.messageStartTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.messageEndTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      const { pagination, dataObj = {}, isQuery = false } = params;
      const pageInfo = pagination || { current: 1, pageSize: 20 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...dataObj,
        ...formData,
      };
      apiCall('/messageArchive/listConversationRecordsByPage', 'POST', data)
        .then((res) => {
          const { current, size, total, records } = res;
          setCurrentMessageList([...records]);
          if (isQuery) {
            setMessageList(current == 1 ? [...records] : [...messageList, ...records]);
          } else {
            setMessageList([...messageList, ...records]);
          }
          // if (records.length > 0) {
          //   if (isQuery) {
          //     setMessageList(
          //       current == 1 ? [...records] : [...messageList, ...records]
          //     );
          //   } else {
          //     const list = JSON.parse(JSON.stringify(messageList));
          //     setMessageList([...list, ...records]);
          //   }
          // } else {
          //   setMessageList([]);
          // }
          setPaginations4({
            current,
            pageSize: size,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = (type) => {
    if (type == 'staff') {
      const value = formLeft.getFieldValue('filterName');
      if (!value) {
        setTreeDataList([...initStaffList]);
        setDefaultSelectedKeys(staffInfo.key);
        return false;
      }
      const flattenTagTreeData = flatten(initStaffList);
      const indexOfTagTreeData = flattenTagTreeData
        .filter((item) => item.type == 'emp')
        .filter((item) => item.name.indexOf(value) > -1);
      setTreeDataList([...indexOfTagTreeData]);
    } else if (type == 'customer') {
      fetchCustomerConversationByPage({ isQuery: true });
    } else if (type == 'group') {
      fetchGroupConversationByPage({ isQuery: true });
    } else {
      const dataObj =
        tabsIndex == '1'
          ? { groupId: chatInfo.id }
          : { employeeId: staffInfo.id, customerId: chatInfo.id };
      fetchListConversationRecordsByPage({
        dataObj,
        isQuery: true,
      });
    }
  };

  // 客户(员工)(群)下拉加载
  const loadMoreData = (e, type) => {
    if (type == 'staff') {
      if (initStaffList.length < paginations1.total) {
        const pageInfo = {
          ...paginations1,
          current: paginations1.current + 1,
        };
        fetchList({ pagination: pageInfo });
      }
    } else if (type == 'customer') {
      if (customerList.length < paginations2.total) {
        const pageInfo = {
          ...paginations2,
          current: paginations2.current + 1,
        };
        fetchCustomerConversationByPage({ pagination: pageInfo });
      }
    } else if (type == 'group') {
      if (groupList.length < paginations3.total) {
        const pageInfo = {
          ...paginations3,
          current: paginations3.current + 1,
        };
        fetchGroupConversationByPage({ pagination: pageInfo });
      }
    }
  };

  useEffect(() => {
    if (activeKey2) {
      setMessageList([]);
      const params = {
        employeeId: staffInfo.id,
        customerId: activeKey2,
      };
      fetchListConversationRecordsByPage({
        dataObj: params,
      });
    }
  }, [activeKey2]);

  useEffect(() => {
    if (activeKey3) {
      setMessageList([]);
      fetchListConversationRecordsByPage({ dataObj: { groupId: activeKey3 } });
    }
  }, [activeKey3]);

  useEffect(() => {
    if (staffInfo) {
      fetchCustomerConversationByPage({
        employeeId: staffInfo.id,
        isInitGroup: true,
      });
      // fetchGroupConversationByPage({ employeeId: staffInfo.id });
    }
  }, [staffInfo]);

  // 点击选择客户(员工)(群)
  const handleChoose = (data, type) => {
    console.log(data);
    if (type == 'staff') {
      setCustomerList([]);
      setGroupList([]);
      setMessageList([]);
      setActiveKey2(null);
      setActiveKey3(null);
      setChatInfo(null);
      if (data.selected) {
        const node = data.node;
        setStaffInfo(node);
        getEmployeeInfoById(node.id);
      } else {
        setStaffInfo(null);
      }
    } else if (type == 'customer' && activeKey2 != data.id) {
      setMessageList([]);
      setActiveKey2(data.id);
      setChatInfo(data);
      formRight.resetFields();
    } else if (type == 'group' && activeKey3 != data.id) {
      setMessageList([]);
      setActiveKey3(data.id);
      setChatInfo(data);
      formRight.resetFields();
    }
  };

  // 切换群聊单聊tab
  useEffect(() => {
    if (tabsIndex == '1' && groupList.length > 0) {
      fetchListConversationRecordsByPage({
        dataObj: { groupId: groupList[0].id },
      });
    } else if (tabsIndex == '2' && customerList.length > 0) {
      fetchListConversationRecordsByPage({
        dataObj: { employeeId: staffInfo.id, customerId: customerList[0].id },
      });
    }
  }, [tabsIndex]);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
    // formMid1.resetFields();
    // formMid2.resetFields();
    formRight.resetFields();
    setMessageList([]);
    if (index == '1') {
      setChatInfo(null);
      // setActiveKey3(groupList.length > 0 ? groupList[0].id : null);
      setActiveKey3(null);
    } else {
      setChatInfo(null);
      // setActiveKey2(customerList.length > 0 ? customerList[0].id : null);
      setActiveKey2(null);
    }
  };

  return (
    <Spin spinning={loading}>
      <div className="StaffRecord">
        <div className="record-left">
          <div className="flex-between">
            <h2>员工列表（{staffCount}）</h2>
          </div>
          <Form layout={'inline'} form={formLeft}>
            <FormItem
              name="filterName"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input
                placeholder="员工名称"
                allowClear
                style={{ width: '180px' }}
              />
            </FormItem>
            <Button type="primary" onClick={() => handleQuery('staff')}>
              搜索
            </Button>
          </Form>
          <div className="record-left-info">
            {treeDataList?.length > 0 ? (
              <Tree
                blockNode
                checkStrictly
                treeData={treeDataList}
                defaultSelectedKeys={[defaultSelectedKeys]}
                defaultExpandedKeys={[treeDataList[0]?.key]}
                onSelect={(selectedKeys, info) => {
                  handleChoose(info, 'staff');
                }}
                fieldNames={{
                  title: 'name',
                  key: 'key',
                  children: 'children',
                }}
                style={{ marginTop: '10px' }}
              />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
        </div>
        {staffInfo && (
          <div className="record-mid">
            <div className="ant-tree-title-nodeData">
              <Avatar size={30} src={employeeInfo.avatar} />
              <div className="ant-tree-title-nodeData-left">
                <div>{staffInfo.name}</div>
                <span>{employeeInfo.departmentName}</span>
              </div>
            </div>
            <Tabs
              activeKey={tabsIndex}
              destroyInactiveTabPane
              onChange={onChangeTabs}
            >
              <TabPane tab={`客户单聊（${paginations2.total}）`} key="2">
                <Form layout={'inline'} form={formMid1}>
                  <FormItem
                    name="filterName"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input
                      placeholder="客户名称"
                      allowClear
                      style={{ width: '180px' }}
                    />
                  </FormItem>
                  <Button
                    type="primary"
                    onClick={() => handleQuery('customer')}
                  >
                    搜索
                  </Button>
                </Form>
                <div className="record-mid-info" id="scrollableDiv2">
                  {customerList.length > 0 ? (
                    <InfiniteScroll
                      dataLength={customerList.length}
                      next={(e) => {
                        loadMoreData(e, 'customer');
                      }}
                      hasMore={customerList.length < paginations2.total}
                      // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                      // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                      scrollableTarget="scrollableDiv2"
                    >
                      {customerList.map((item, index) => (
                        <div
                          className={`ant-tree-title-nodeData ${activeKey2 == item.id ? 'active' : ''
                            }`}
                          key={index}
                          onClick={() => {
                            handleChoose(item, 'customer');
                          }}
                        >
                          <Avatar size={30} src={item.avatar} />
                          <div className="ant-tree-title-nodeData-left">
                            <div>{item.name}</div>
                            <span>{item.departmentName}</span>
                          </div>
                          {item.lastConversationTime && (
                            <div className="ant-tree-title-nodeData-center">
                              {getTimeOrDate(item.lastConversationTime)}
                            </div>
                          )}
                        </div>
                      ))}
                    </InfiniteScroll>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </div>
              </TabPane>
              <TabPane tab={`外部群聊（${paginations3.total}）`} key="1">
                <Form layout={'inline'} form={formMid2}>
                  <FormItem
                    name="filterName"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input
                      placeholder="客户群名称"
                      allowClear
                      style={{ width: '180px' }}
                    />
                  </FormItem>
                  <Button type="primary" onClick={() => handleQuery('group')}>
                    搜索
                  </Button>
                </Form>
                <div className="record-mid-info" id="scrollableDiv2">
                  {groupList.length > 0 ? (
                    <InfiniteScroll
                      dataLength={groupList.length}
                      next={(e) => {
                        loadMoreData(e, 'group');
                      }}
                      hasMore={groupList.length < paginations3.total}
                      // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                      // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
                      scrollableTarget="scrollableDiv2"
                    >
                      {groupList.map((item, index) => (
                        <div
                          className={`ant-tree-title-nodeData ${activeKey3 == item.id ? 'active' : ''
                            }`}
                          key={index}
                          onClick={() => {
                            handleChoose(item, 'group');
                          }}
                        >
                          <div className="ant-tree-title-nodeData-left">
                            <div>
                              {item.groupDeleted == '1' && ['已解散']}
                              {item.name}（{item.groupMemberCount}）
                            </div>
                            <span>
                              群主：{item.leaderEmployeeName}(
                              {item.departmentName})
                            </span>
                          </div>
                          {item.lastConversationTime && (
                            <div className="ant-tree-title-nodeData-center">
                              {getTimeOrDate(item.lastConversationTime)}
                            </div>
                          )}
                        </div>
                      ))}
                    </InfiniteScroll>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}
        {chatInfo ? (
          <div className="record-right">
            <div className="record-right-top">
              <div className="ant-tree-title-nodeData">
                <div className="ant-tree-title-nodeData-left">
                  {tabsIndex == '1' ? (
                    <>
                      <div>
                        {chatInfo.groupDeleted == '1' && ['已解散']}
                        {chatInfo.name}（{chatInfo.groupMemberCount}）
                      </div>
                      <span>
                        群主：{chatInfo.leaderEmployeeName}(
                        {chatInfo.departmentName})
                      </span>
                    </>
                  ) : (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar size={30} src={chatInfo.avatar} />
                      <div style={{ marginLeft: '8px' }}>
                        <div>{chatInfo.name}</div>
                        <span>{chatInfo.departmentName}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <Form layout={'inline'} form={formRight}>
                <FormItem name="messageType" style={{ marginBottom: '10px' }}>
                  <Select
                    placeholder="全部"
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={typeOption}
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>
                <FormItem
                  name="messageContent"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  style={{ marginBottom: '10px' }}
                >
                  <Input
                    placeholder="聊天内容"
                    allowClear
                    style={{ width: '140px' }}
                  />
                </FormItem>
                <FormItem name="time" style={{ marginBottom: '10px' }}>
                  <RangePicker style={{ width: '220px' }} />
                </FormItem>
                <Button type="primary" onClick={() => handleQuery()}>
                  搜索
                </Button>
              </Form>
            </div>
            <ChatContent
              params={{
                data: messageList,
                hasMore: currentMessageList.length > 0,
                showTips: tabsIndex == 2,
                changeId: chatInfo.id,
                customerId: chatInfo.id,
                employeeId: staffInfo.id,
                chatLoadMoreData: (e) => {
                  if (currentMessageList.length > 0) {
                    const pageInfo = {
                      ...paginations4,
                      current: paginations4.current + 1,
                    };
                    const dataObj =
                      tabsIndex == '1'
                        ? { groupId: chatInfo.id }
                        : { employeeId: staffInfo.id, customerId: chatInfo.id };
                    fetchListConversationRecordsByPage({
                      dataObj,
                      pagination: pageInfo,
                    });
                  }
                },
              }}
            />
          </div>
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ margin: 'auto' }}
          />
        )}
        <CustomerFormModal params={customerParams} />
      </div>
    </Spin>
  );
};

export default StaffRecord;
