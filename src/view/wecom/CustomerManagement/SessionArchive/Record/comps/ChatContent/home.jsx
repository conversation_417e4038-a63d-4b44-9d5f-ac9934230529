/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/10 11:24
 * @LastEditTime: 2024/06/14 11:01
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/CustomerManagement/SessionArchive/Record/comps/ChatContent/home.jsx
 * @Description: '聊天对话内容'
 */

import React, { useEffect, memo, useState, useRef } from 'react';
import { Image, Avatar, Empty, Row, Col, Typography } from 'antd';
import { FileTextOutlined, CloseOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import { getChatTimeOrDate } from 'common/date';
import { apiCall } from 'common/utils';
import { scrollToBottom } from "common/scroll";
import './home.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { Paragraph } = Typography;

const ChatContent = (props) => {
  const wiBodyRef = useRef();
  const {
    data = [],
    employeeId = null,
    customerId = null,
    changeId,
    // total = 0,
    hasMore = false,
    showTips = false,
    chatLoadMoreData,
  } = props.params;
  const [showFlag, setShowFlag] = useState(true);
  const [isOpen, setIsOpen] = useState(true);
  const [time, setTime] = useState(null);

  useEffect(() => {
    setShowFlag(showTips);
    if (showTips) {
      apiCall('/messageArchive/disagreeConversationByCustomer', 'GET', {
        employeeId: employeeId,
        customerId: customerId,
      })
        .then((res) => {
          const { customerAgree, updateTime } = res;
          setIsOpen(customerAgree);
          setTime(updateTime);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    }
  }, [changeId]);

  useEffect(() => {
    if (data.length) {
      if (data.length <= 20) {
        let timer = setTimeout(() => {
          scrollToBottom("ChatContent");
          clearTimeout(timer)
        }, 500);
      } else {
        let locateBox = document.querySelectorAll(`.ChatContent>div`)[20];
        wiBodyRef.current.scrollTop = locateBox.offsetTop - locateBox.clientHeight; // 滚动条位置 = 当前消息偏移量-当前
      }
    }
  }, [data]);

  const getHTMLTypeDom = (item) => {
    let htmlDom = '';
    switch (item.messageType) {
      case 'TEXT':
        htmlDom = <div className="type-text">{item.messageContent}</div>;
        break;
      case 'IMAGE':
        htmlDom = <FileHOC src={item.fileUrl}>
          {(url) => (
            <Image src={url} width={150} />
          )}
        </FileHOC>;
        break;
      case 'VIDEO':
        htmlDom = (
          <FileHOC src={item.fileUrl}>
            {(url) => (
              <video
                style={{ maxWidth: '200px', maxHeight: '100px' }}
                controls
                src={url}
              ></video>
            )}
          </FileHOC>
        );
        break;
      case 'FILE':
        htmlDom = (
          <a
            className="file-card"
            onClick={() => {
              saveAs(item.fileUrl, item.fileName);
            }}
          >
            <Row justify="space-between" align={'middle'}>
              <Col span={17}>
                <Paragraph strong ellipsis={{ rows: 2 }}>
                  {item.fileName}
                </Paragraph>
                <span style={{ color: '#999999' }}>139.8k</span>
              </Col>
              <Col>
                <FileTextOutlined style={{ fontSize: '30px' }} />
              </Col>
            </Row>
          </a>
        );
        break;
      default:
        htmlDom = (
          <div className="type-text">
            [内容无法识别：视频号、红包、转账、聊天记录]
          </div>
        );
        break;
    }
    return htmlDom;
  };

  return (
    <div className="ChatContentBox">
      {showFlag && (
        <div className="operationTop">
          <span>
            该客户{time ? `于${time}` : ''}
            {isOpen ? '开启' : '拒绝'}会话存档
          </span>
          <CloseOutlined
            style={{ cursor: 'pointer' }}
            onClick={() => {
              setShowFlag(false);
            }}
          />
        </div>
      )}
      <div
        className="ChatContent"
        ref={wiBodyRef}
        onScroll={(e) => {
          const { scrollTop } = e.target;
          if (scrollTop == 0 && data.length) {
            chatLoadMoreData?.(e);
          }
        }}
      >
        {data.length > 0 ? (
          [...data].reverse().map((item, index) => (
            <div key={index} className={showFlag ? 'archiveItem' : ''}>
              <div className="contentTime">
                {getChatTimeOrDate(item.senderTime)}
              </div>
              {item.messageType == 'SESSION_AGREE' ||
                item.messageType == 'SESSION_DISAGREE' ? (
                <div className="type-operation" style={{ color: item.messageType == 'SESSION_AGREE' ? '#49bb18' : '#ff4347' }}>
                  {item.messageType == 'SESSION_AGREE'
                    ? '对方同意存档会话内容，你可以继续提供服务'
                    : '对方不同意存档会话内容，你将无法继续提供服务'}
                </div>
              ) : item.senderIdType == 2 ? (
                <div className="contentRight">
                  <div className="contentRight-type">
                    {getHTMLTypeDom(item)}
                  </div>
                  <Avatar size={30} src={item.avatar} />
                </div>
              ) : (
                <div className="contentLeft">
                  <Avatar size={30} src={item.avatar} />
                  <div className="contentLeft-type">
                    {getHTMLTypeDom(item)}
                  </div>
                </div>
              )}
            </div>
          ))
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="archiveEmpty"
          />
        )}
      </div>
    </div>
  );
};

export default memo(ChatContent);
