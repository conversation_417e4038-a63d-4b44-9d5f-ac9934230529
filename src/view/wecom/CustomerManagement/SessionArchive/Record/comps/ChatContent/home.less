.ChatContentBox {
  position: relative;

  .operationTop {
    // position: sticky;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    // text-align: center;
    width: 95%;
    background: #facd91;
    z-index: 999;
    padding: 5px;
  }

  .ChatContent {
    padding: 10px;
    // background-color: #f5f5f5;
    border-top: 2px solid #e5e5e5;
    // height: 540px;
    height: 600px;
    overflow: auto;

    .archiveItem:last-child {
      margin-top: 30px;
    }

    // 滚动条整体部分
    &::-webkit-scrollbar {
      // width: 6px; //对垂直方向滚动条
      // height: 6px; //对水平方向滚动条
    }

    //滚动的滑块
    &::-webkit-scrollbar-thumb {
      // height: 27px;
      border-radius: 6px;
      background-color: #f2f2f2; //滚动条的颜色
    }

    //内层滚动槽
    &::-webkit-scrollbar-track-piece {
      background-color: #f2f2f2;
    }

    .contentTime {
      color: #999999;
      text-align: center;
    }

    .contentLeft {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .contentLeft-type {
        margin-left: 10px;

        .type-text {
          position: relative;
          display: inline-block;
          background-color: #fff;
          max-width: 325px;
          border-radius: 5px;
          padding: 10px;
          word-break: break-all;

          &::before {
            content: "";
            position: absolute;
            top: 18px;
            left: -7px;
            border-style: solid;
            border-width: 5px 5px 0 5px;
            border-radius: 5px;
            border-color: #fff transparent transparent transparent;
            // transform: translateX(-90%);
            transform: rotate(90deg);
          }
        }
      }
    }

    .contentRight {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin: 10px 0;

      .contentRight-type {
        margin-right: 10px;

        .type-text {
          position: relative;
          display: inline-block;
          background-color: #fff;
          max-width: 325px;
          border-radius: 5px;
          padding: 10px;
          word-break: break-all;

          &::after {
            content: "";
            position: absolute;
            top: 18px;
            right: -7px;
            border-style: solid;
            border-width: 5px 5px 0 5px;
            border-radius: 5px;
            border-color: #fff transparent transparent transparent;
            // transform: translateX(-90%);
            transform: rotate(-90deg);
          }
        }
      }
    }

    .type-operation {
      text-align: center;
      margin: 10px 0;
      color: #999999;
    }

    .file-card {
      display: inline-block;
      width: 198px;
      padding: 10px;
      // border: 1px solid #ccc;
      background-color: #fff;
      border-radius: 6px;
      text-align: left;
      white-space: break-spaces;
      overflow: hidden;

      .ant-typography {
        margin-bottom: 0px;
      }
    }

    .archiveEmpty {
      position: absolute;
      top: 40px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}