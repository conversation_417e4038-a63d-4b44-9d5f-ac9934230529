/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/09 10:52
 * @LastEditTime: 2023/05/19 11:45
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\CustomerManagement\SessionArchive\Record\home.jsx
 * @Description: '会话记录'
 */

import React, { useState, useEffect } from "react";
import { Card, Tabs } from "antd";
// 模块组件
import CustomerRecord from "./Customer/home";
import StaffRecord from "./Staff/home";
import GroupRecord from "./Group/home";
import "./home.less";

const { TabPane } = Tabs;

const Record = (props) => {
  const [tabsIndex, setTabsIndex] = useState("");

  useEffect(() => {
    setTabsIndex("2");
  }, []);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
  };

  return (
    <div className="ConversationRecord">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          <TabPane tab="员工记录" key="2">
            <StaffRecord />
          </TabPane>
          <TabPane tab="客户记录" key="1">
            <CustomerRecord />
          </TabPane>

          <TabPane tab="群聊记录" key="3">
            <GroupRecord />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Record;
