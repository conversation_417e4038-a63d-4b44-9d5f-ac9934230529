/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/07/06 16:48
 * @LastEditTime: 2024/01/09 16:21
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DataBoard\CustomReport\comps\ExportModal.jsx
 * @Description: '自定义表单-导出对话框'
 */

import React, { useEffect, useState } from 'react';
import {
  Input,
  Form,
  Button,
  Modal,
  Spin,
  message,
  Typography,
  DatePicker,
  TreeSelect,
} from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import SysDictSelect from 'components/select/SysDictSelect';
import './ExportModal.less';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { Paragraph } = Typography;
const { SHOW_PARENT } = TreeSelect;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const ExportModal = (props) => {
  const { visible, id, name, params = [] } = props.params;
  const [formRef] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [existTagNameList, setExistTagNameList] = useState([]);
  useEffect(() => {
    if (visible) {
      if (params.some((item) => item.type == 'CUSTOMER_TAG')) {
        getTagCategoryTreeTwo();
        console.log(`[params]: `, params)
        const default1 = params.find((item) => item.type == 'CUSTOMER_TAG').default
        console.log(`[default1]: `, default1)
        setExistTagNameList([default1])
      }
      let obj = {};
      params.forEach((item) => {
        if (item.type == 'DATE' || item.type == 'DATETIME') {
          // obj[item.name] = item.default
          //   ? isStrDate(item.default)
          //     ? moment(item.default)
          //     : ""
          //   : item.default;
          obj[item.name] = item.fn ? moment(eval(item.fn)) : (item.default ? moment(item.default) : item.default);
        } else if (
          item.type == 'DEP_EMP' ||
          item.type == 'EMP' ||
          item.type == 'CUSTOMER_TAG'
        ) {
          obj[item.name] = item.default.split(',');
        } else if (item.type == 'STRING') {
          obj[item.name] = item.default;
        } else if (item.type == 'SELECT') {
          obj[item.name] = item.default ? item.default.split(',') : [];
        }
      });
      formRef.setFieldsValue({ ...obj });
    }
  }, [visible]);

  // 判断字符串是否为日期（日期格式为：YYYY-MM-DD）
  // function isStrDate(str) {
  //   var reg = reg =/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/;
  //   if (reg.test(str) == false) {
  //     console.log("时间日期格式错误");
  //     return false;
  //   } else {
  //     return true;
  //   }
  // }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([
        //   {
        //     title: '全选',
        //     value: 'customer',
        //     key: 'customer',
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const onOk = () => {
    formRef.validateFields().then((formData) => {
      let idx = 0;
      for (let i in formData) {
        if (formData[i]._isAMomentObject) {
          console.log(params[idx], params, idx, 'params[idx]');
          formData[i] = moment(formData[i]._d).format(
            params[idx].type == 'DATETIME'
              ? 'YYYY-MM-DD HH:mm:ss'
              : 'YYYY-MM-DD'
          );
        } else if (Array.isArray(formData[i])) {
          formData[i] = formData[i].join(',');
        }
        idx++;
      }
      setConfirmLoading(true);
      const data = {
        ...formData,
      };
      apiCall(`/stat/customReport/scriptExport/${id}`, 'POST', data, null, {
        isExit: true,
        title: `${name}.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success('导出成功！');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    params.forEach((item) => {
      if (
        item.type == 'DEP_EMP' ||
        item.type == 'EMP' ||
        item.type == 'CUSTOMER_TAG'
      ) {
        formRef.setFieldsValue({ [item.name]: [] });
      } else {
        formRef.setFieldsValue({ [item.name]: '' });
      }
    });
    formRef.resetFields();
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  const getDomHtml = (item, index) => {
    let htmlDom = '';
    switch (item.type) {
      case 'DATE':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: '请选择日期' }]}
          // initialValue={item.default ? moment(item.default) : ""}
          >
            <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
          </FormItem>
        );
        break;
      case 'DATETIME':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: '请选择日期时间' }]}
          // initialValue={item.default ? moment(item.default) : ""}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              style={{ width: '100%' }}
            />
          </FormItem>
        );
        break;
      case 'DEP_EMP':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: '请选择组织或员工' }]}
          // initialValue={item.default ? item.default.split(",") : []}
          >
            <ETypeTransferModal title="选择组织或员工" />
          </FormItem>
        );
        break;
      case 'CUSTOMER_TAG':
        htmlDom = (
          <CustomTagSelect
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: "请选择客户标签" }]}
            placeholder="客户标签"
            useForm={formRef}
            existTagNameList={existTagNameList}
            labelTreeData={labelTreeData}
          />
        )
          // <FormItem
          //   key={index}
          //   label={<Paragraph>{item.desc}</Paragraph>}
          //   name={item.name}
          //   className="customSheetFormItem"
          //   rules={[{ required: item.required, message: '请选择客户标签' }]}
          // // initialValue={item.default ? item.default.split(",") : []}
          // >
          //   <TreeSelect
          //     treeData={labelTreeData}
          //     treeCheckable
          //     treeDefaultExpandedKeys={['customer']}
          //     allowClear
          //     showArrow
          //     showSearch
          //     treeNodeFilterProp="title"
          //     maxTagCount="responsive"
          //     showCheckedStrategy={SHOW_PARENT}
          //     placeholder="客户标签"
          //   // style={{ width: "220px" }}
          //   />
          // </FormItem>

        break;
      case 'EMP':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: '请选择员工' }]}
          // initialValue={item.default ? item.default.split(",") : []}
          >
            <ETypeTransferModal
              title="选择员工"
              mode={['emp', 'tag']}
              multiple
            />
          </FormItem>
        );
        break;
      case 'STRING':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            // getValueFromEvent={(e) => removeInputEmpty(e)}
            className="customSheetFormItemCenter"
            rules={[{ required: item.required, message: '请输入内容' }]}
          // initialValue={item.default ? item.default : ""}
          >
            <TextArea
              placeholder="请输入"
              allowClear
            // style={{ width: "220px" }}
            />
            {/* <Input placeholder="请输入" allowClear style={{ width: '220px' }} /> */}
          </FormItem>
        );
        break;
      case 'SELECT':
        htmlDom = (
          <FormItem
            key={index}
            label={<Paragraph>{item.desc}</Paragraph>}
            name={item.name}
            className="customSheetFormItem"
            rules={[{ required: item.required, message: '请选择选项' }]}
          >
            <SysDictSelect
              placeholder={item.desc || ''}
              dataset={item.dict}
              mode={item.multi ? 'multiple' : false}
            // style={{ width: "220px" }}
            />
          </FormItem>
        );
        break;
      default:
        break;
    }
    return htmlDom;
  };

  return (
    <Modal
      className="CustomSheet-ExportModal"
      visible={visible}
      title={`导出${name}报表`}
      width={600}
      destroyOnClose
      centered
      onCancel={onCancel}
      footer={
        <Button type="primary" loading={confirmLoading} onClick={onOk}>
          导出
        </Button>
      }
    >
      <Spin spinning={loading}>
        <h3>参数列表</h3>
        {params.length > 0 ? (
          <>
            <Form {...layout} form={formRef}>
              {params.map(
                (item, index) => getDomHtml(item, index)
                // <FormItem
                //   key={index}
                //   label={
                //     // ellipsis={{ rows: 1, tooltip: true }}
                //     <Paragraph>{item.desc}</Paragraph>
                //   }
                //   name={item.name}
                //   getValueFromEvent={(e) => removeInputEmpty(e)}
                //   className="customSheetFormItem"
                // >
                //   <Input placeholder="请输入" allowClear />
                // </FormItem>
              )}
            </Form>
            <div style={{ color: '#aaaaaa' }}>
              日期请使用固定格式“年-月-日”，例如：2023-07-06
              <br />
              参数值有多个时请使用半角逗号分隔
            </div>
          </>
        ) : (
          <div>
            <p>如果报表没有参数，请直接点击导出；</p>
            <p>如果报表有参数，请核对报表定义格式是否正确。</p>
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default ExportModal;
