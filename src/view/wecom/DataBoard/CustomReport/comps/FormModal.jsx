/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/07/06 16:48
 * @LastEditTime: 2023/07/11 18:01
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DataBoard\CustomReport\comps\FormModal.jsx
 * @Description: '自定义表单-新增编辑对话框'
 */

import React, { useEffect, useState } from 'react';
import { Input, Form, message, Modal, Spin } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import './FormModal.less';

const FormItem = Form.Item;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 24 },
};

const FormModal = (props) => {
  const { visible, id } = props.params;
  const [formRef] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible, data } = props.params;
    if (visible) {
      if (data) {
        formRef.setFieldsValue({ ...data });
      }
    }
  }, [visible]);

  const onOk = () => {
    formRef.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ...formData,
      };
      const apiUrl = id
        ? `/stat/customReport/update/${id}`
        : '/stat/customReport';
      apiCall(apiUrl, 'POST', data)
        .then((res) => {
          message.success(id ? '编辑成功！' : '新增成功！');
          props.params.onSubmit();
          onCancel();
        })
        .catch((err) => {
          console.log(err);

        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    formRef.resetFields();
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="CustomSheet-FormModal"
      visible={visible}
      width={600}
      title={id ? '编辑报表' : '新增报表'}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} form={formRef}>
          <FormItem
            label="名称"
            name="name"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入报表名称' }]}
          >
            <Input
              placeholder="请输入报表名称"
              // maxLength={20}
              allowClear
            />
          </FormItem>
          <FormItem name="purpose" label="用途">
            <TextArea
              placeholder="请输入报表用途（255字）"
              allowClear
              showCount
              maxLength={255}
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
          </FormItem>
          <FormItem
            name="definition"
            label="定义"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            className="textArea-mid"
          >
            <TextArea
              placeholder="请输入报表定义"
              allowClear
              // showCount
              // maxLength={100}
              autoSize={{ minRows: 2, maxRows: 7 }}
            // style={{ height: "100px" }}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
