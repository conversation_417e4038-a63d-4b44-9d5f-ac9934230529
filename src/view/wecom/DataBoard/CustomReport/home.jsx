/*
 * @Author: <PERSON>xiaoyan
 * @Date: 2023/07/06 15:02
 * @LastEditTime: 2023/11/21 14:35
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/CustomReport/home.jsx
 * @Description: '自定义报表'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import { Form, Input, Button, Table, Card, Tooltip, message } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import OperateModal from 'components/Modal/OperateModal/index';
import ExportModal from './comps/ExportModal';
import FormModal from './comps/FormModal';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;

const CustomSheet = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [exportParams, setExportParams] = useState({ visible: false });
  const [formParams, setFormParams] = useState({ visible: false });
  const paramsType = [
    'DATE',
    'DATETIME',
    'DEP_EMP',
    'EMP',
    'CUSTOMER_TAG',
    'STRING',
    'SELECT'
  ];

  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '报表名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '报表用途',
      width: '160px',
      dataIndex: 'purpose',
      key: 'purpose',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    // {
    //   title: '报表定义',
    //   width: '160px',
    //   dataIndex: 'definition',
    //   key: 'definition',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip title={value} placement="topLeft">
    //       {value}
    //     </Tooltip>
    //   ),
    // },
    {
      title: '更新人/更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleExport(record), name: "导出" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall('/stat/customReport', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 导出
  const handleExport = (record) => {
    const { id, name, definition } = record;
    const params = definition ? JSON.parse(definition).params : [];

    if (params) {
      if (Array.isArray(params)) {
        for (let i = 0; i < params.length; i++) {
          if (!paramsType.includes(params[i].type)) {
            message.error(`参数类型错误:${params[i].type}`);
            return false;
          }
        }
      } else {
        message.error('params参数错误！');
        return false;
      }
    }

    setExportParams({
      visible: true,
      id,
      name,
      params,
      onCancel: () => {
        setExportParams({ visible: false });
      },
    });
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      onSubmit: () => {
        // setFormParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id,
      data: record,
      onSubmit: () => {
        // setFormParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除报表【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/stat/customReport/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onTableChange = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="CustomSheet">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input
              placeholder="报表名称"
              allowClear
              style={{ width: '240px' }}
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onTableChange}
        />
      </Card>
      <OperateModal params={operateParams} />
      <FormModal params={formParams} />
      {/* 导出对话框 */}
      <ExportModal params={exportParams} />
    </div>
  );
};

export default withRouter(CustomSheet);
