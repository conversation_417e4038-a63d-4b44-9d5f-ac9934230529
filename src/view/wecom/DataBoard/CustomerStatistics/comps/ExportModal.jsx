/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2024/10/22 09:12
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/CustomerStatistics/comps/ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  DatePicker,
  Button,
  Image,
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './ExportModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState('1');
  const [imageVisible, setImageVisible] = useState(false);
  const ImagePreviewList = [
    require('images/部门客户统计.png'),
    require('images/员工获客明细.png'),
    require('images/渠道获客统计.png'),
    require('images/员工推荐码统计.png'),
  ];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startTime = moment(formData.date[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.date[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.date;
      }
      const data = {
        ...formData,
      };

      let apiUrl = '/statistic/export/departmentCustomerStat';
      let title = '部门客户统计';
      if (TabsActiveKey == '2') {
        apiUrl = '/statistic/export/employeeCustomerStat';
        title = '员工获客明细';
      } else if (TabsActiveKey == '3') {
        apiUrl = '/statistic/export/channelCustomerStat';
        title = '渠道获客统计';
      } else if (TabsActiveKey == '4') {
        apiUrl = '/statistic/export/recommend';
        title = '员工推荐码统计';
      }
      apiCall(apiUrl, 'POST', data, null, {
        isExit: true,
        title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success('导出成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="CustomerStatistics-ExportModal"
      visible={visible}
      title="导出客户数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
                style={{ display: 'none' }}
                preview={{
                  visible: imageVisible,
                  src: url,
                  onVisibleChange: (value) => {
                    setImageVisible(value);
                  },
                }}
              />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={onOk}
          >
            导出
          </Button>
        </div>,
      ]}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="部门客户统计" key="1">
              <p className="tips">统计部门客户数量</p>
              <FormItem
                label="统计时间"
                name="date"
                rules={[{ required: true, message: '请选择统计时间' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" onlyDepartment />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="员工获客明细" key="2">
              <p className="tips">根据部门来统计每个员工的获客明细数据</p>
              <FormItem
                label="获客时间"
                name="date"
                rules={[{ required: true, message: '请选择获客时间' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="直接选择员工，或通过部门选择员工"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="渠道获客统计" key="3">
              <p className="tips">根据部门来统计每个渠道的获客数据</p>
              <FormItem
                label="统计时间"
                name="date"
                rules={[{ required: true, message: '请选择统计时间' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" onlyDepartment />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="员工推荐码统计" key="4">
              <p className="tips">统计进行认领员工推荐码的数据</p>
              <FormItem
                label="认领日期"
                name="date"
                rules={[{ required: true, message: '请选择认领日期' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择员工、部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" />
                {/* mode={['dep', 'emp']} */}
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
