/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 17:48
 * @LastEditTime: 2024/10/21 16:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/CustomerStatistics/home.jsx
 * @Description: '客户统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Form, DatePicker, Row, Col, Spin, Card, Table, Tooltip, Empty, TreeSelect } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { apiCall } from 'common/utils';
import { Column, Line, Pie } from '@ant-design/plots';
import { getDay, disabledAfterDate } from 'common/date';
import ExportModal from './comps/ExportModal';
import WibotStatistic from 'components/WibotStatistic/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const CustomerStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData, setStatisticData] = useState([
    {
      title: '累计好友人数',
      value: 0,
      describe: `全行所有员工的企微外部好友总人数（不去重）
      本统计项支持按机构、不支持按时间筛选`,
      suffix: '人'
    },
    {
      title: '累计客户人数',
      value: 0,
      describe: `全行客户总人数（去重），含全部及部分流失客户、全部及部分被删客户，不含访客。
      本统计项支持按机构、不支持按时间筛选`,
      suffix: '人'
    },
    {
      title: '识别客户人数',
      value: 0,
      describe: `所选机构在所选日期截止时，所有客户识别状态为已识别的客户人数
      本统计项支持按机构、时间筛选（只取截止时间）`,
      suffix: '人'
    },
    {
      title: ' 累计授权客人数',
      value: 0,
      describe: `全行在所选日期截止时，在网页进行微信昵称头像授权的客户数，含访客
      本统计项支持按时间筛选（只取截止时间）、不支持按机构筛选`,
      suffix: '人'
    },
    {
      title: ' 管户客户人数',
      value: 0,
      describe: `所选机构在所选日期截止时，被标识为管户的客户数量
      本统计项支持按机构、时间筛选（只取截止时间）`,
      suffix: '人'
    }
  ]);
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsData4, setChartsData4] = useState(null);
  const [chartsData5, setChartsData5] = useState(null);
  const [chartsData7, setChartsData7] = useState(null);
  const [chartsData8, setChartsData8] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [chartsTotal4, setChartsTotal4] = useState(0);
  const [chartsTotal5, setChartsTotal5] = useState(0);
  const [chartsTotal7, setChartsTotal7] = useState(0);
  const [chartsTotal8, setChartsTotal8] = useState(0);
  const [dataSource1, setDataSource1] = useState([]);
  const columns1 = [
    {
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户标签排序',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '人数',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const [preTime, setPreTime] = useState(null);
  const [exportParams, setExportParams] = useState({ visible: false });

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then(async (res) => {
      setOrganOption(res);
      await formRef?.current?.setFieldsValue({
        departmentId: res[0].id,
        createTime: [moment(getDay(-30)), moment(getDay(-1))]
      });
      await getStatisticData();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format('YYYY-MM-DD');
        formData.endDate = moment(formData.createTime[1]._d).format('YYYY-MM-DD');
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall('/statistic/customerData', 'GET', data).then((res) => {
        let { adminCustomerCount, allFriendCustomerCount, allCustomerCount, authorizedCustomerCount, collectPreference, contactCustomerChart, customerTag, employeeCustomerCount, femaleCount, identifyCustomerCount, unknownGenderCount, lostCustomerChart, maleCount, newCustomerChart, visitorChart, deletedFriendChart } = res || {};
        let newStatisticData = statisticData;
        newStatisticData[0].value = allFriendCustomerCount;
        newStatisticData[1].value = allCustomerCount;
        newStatisticData[2].value = identifyCustomerCount;
        newStatisticData[3].value = authorizedCustomerCount;
        newStatisticData[4].value = adminCustomerCount;
        setStatisticData(newStatisticData);
        setChartsTotal1(newCustomerChart?.list.length && newCustomerChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal2(contactCustomerChart?.list.length && contactCustomerChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal3(visitorChart?.list.length && visitorChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal4(lostCustomerChart?.list.length && lostCustomerChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal5(deletedFriendChart?.list.length && deletedFriendChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal7(femaleCount + maleCount + unknownGenderCount); // 性别总数
        setChartsTotal8(employeeCustomerCount.length > 0 && employeeCustomerCount?.map((item) => item.count).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsData1(newCustomerChart?.list || []);
        setChartsData2(contactCustomerChart?.list || []);
        setChartsData3(visitorChart?.list || []);
        setChartsData4(lostCustomerChart?.list || []);
        setChartsData5(deletedFriendChart?.list || []);
        setDataSource1(customerTag);
        setChartsData7({ maleCount, femaleCount, unknownGenderCount });
        setChartsData8(employeeCustomerCount.length && employeeCustomerCount || null);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (data, fieldKey = { xField: 'date', yField: 'number' }) => {
    const config = {
      data,
      xField: fieldKey.xField,
      yField: fieldKey.yField,
      label: {
        // 可手动配置 label 数据标签位置
        position: 'middle', // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
        count: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  const DemoPieGender = (data) => {
    data = [
      {
        type: '男',
        value: data.maleCount,
      },
      {
        type: '女',
        value: data.femaleCount,
      },
      {
        type: '未知',
        value: data.unknownGenderCount,
      },
    ];
    const config = {
      color: ['#4ebbf9', '#ff7cb6', '#ffd700'],
      appendPadding: 10,
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 0.75,
      label: {
        type: 'spider',
        labelHeight: 28,
        content: '{name}\n{percentage}',
      },
      interactions: [
        {
          type: 'element-selected',
        },
        {
          type: 'element-active',
        },
      ],
    };
    return <Pie {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == 'yesterday') {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == 'week') {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == 'month') {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue('createTime');
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre = moment(value[0]._d).format('YYYY-MM-DD') == moment(preTime[0]._d).format('YYYY-MM-DD');
      const next = moment(value[1]._d).format('YYYY-MM-DD') == moment(preTime[1]._d).format('YYYY-MM-DD');
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false });
      }
    });
  };

  return (
    <Spin spinning={loading}>
      <div className='CustomerStatistics'>
        <FilterBar>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="departmentId" label="机构" >
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间" >
              <RangePicker allowClear={false} disabledDate={disabledAfterDate} format="YYYY-MM-DD" onOpenChange={onOpenChange} />
            </FormItem>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('yesterday')}>
              昨日
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('week')}>
              近7天
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('month')}>
              近30天
            </Button>
            <Button type="primary" onClick={() => handleExport()}>导出</Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData}
          flex="auto"
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下每天新增的好友人数（不去重）按照日期排列，含全部及部分流失客户、全部及部分被删客户，不含访客。
本统计项支持按机构、时间筛选">
                新增好友人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal1} 人</span>
            </div>}>
              {chartsData1 && DemoColumn(chartsData1) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下的员工与好友每天的聊天总数（不去重）按照日期排列，数据来源于企业微信官方接口。
本统计项支持按机构、时间筛选">
                员工联系好友人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              {/* <span>累计：{chartsTotal2} 人</span> */}
            </div>}>
              {chartsData2 && DemoColumn(chartsData2) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="全行在所选日期内，统计每天没有添加任何员工的客户人数（去重）按照日期排列，包括公众号访客、企微群访客。
本统计项不支持按机构筛选，只支持按时间筛选">
                累计非好友访客数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal3} 人</span>
            </div>}>
              {chartsData3 && DemoLine(chartsData3) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="全行在所选日期内，每天客户删掉全行员工企业微信好友的人数按日期排列，同一客户一天删除全部员工企微好友多次记为1次。
本统计项不支持按机构、时间筛选">
                全部流失客户人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal4} 人</span>
            </div>}>
              {chartsData4 && DemoLine(chartsData4) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]} >
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下的员工，每天删除已添加的好友人数（不去重）按照日期排列，同一好友一天被一个员工删除多次记为1次。
本统计项支持按机构、时间筛选">
                被删好友人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal5} 人</span>
            </div>}>
              {chartsData5 && DemoLine(chartsData5) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12} >
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构在所选日期内，所有客户的按客户标签数量倒叙排列，取前50个客户标签进行排行榜展示，不含访客
本统计项支持按机构、时间筛选">
                客户标签排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="name" columns={columns1} dataSource={dataSource1} pagination={false} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下所有客户的性别分布情况，含全部及部分流失客户、全部及部分被删客户，不含访客
本统计项支持按机构、时间筛选">
                客户性别分布<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              {/* <span>累计：{chartsTotal7} 人</span> */}
            </div>}>
              {chartsData7 && DemoPieGender(chartsData7) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构及其子孙机构中所有员工在所选日期截止时，员工的好友人数（不去重）排行榜，按好友人数倒序，当存在多个人数相同或为0时按机构名称正序排列，只统计前20
本统计项不支持按时间筛选，只支持按机构筛选（只取截止时间）">
                员工好友人数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal8} 人</span>
            </div>}>
              {chartsData8 && DemoColumn(chartsData8, { xField: 'name', yField: 'count' }) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
      </div>
    </Spin >
  );
};

export default CustomerStatistics;
