/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 15:10
 * @LastEditTime: 2023/12/04 16:17
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/DataBoard/home.jsx
 * @Description: '数据统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Form, DatePicker, Row, Col, Spin, Tooltip, Table, Card, Empty, TreeSelect } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { WordCloud, Column, Pie } from '@ant-design/plots';
import moment from 'moment';
import { apiCall } from 'common/utils';
import { getDay, disabledAfterDate } from 'common/date';
import WibotStatistic from 'components/WibotStatistic/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const DataBoard = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [dataSource1, setDataSource1] = useState([]);
  const [dataSource2, setDataSource2] = useState([]);
  const columns1 = [
    {
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const columns2 = [
    {
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
    },
    {
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const [statisticData1, setStatisticData1] = useState([
    {
      title: '累计好友人数',
      describe: '全行所有员工的企微外部好友总人数（不去重）本统计项支持按机构、不支持时间筛选',
      value: 0,
      suffix: "人"
    },
    {
      title: '累计客户人数',
      describe: '全行客户总人数（去重），含全部及部分流失客户、全部及部分被删客户，不含访客。本统计项支持按机构、不支持时间筛选',
      value: 0,
      suffix: "人"
    },
  ]);
  const [statisticData2, setStatisticData2] = useState([
    {
      title: '新增客户人数',
      describe: '所选机构在所选日期内，机构名下新增的客户人数（去重），含全部及部分流失客户、全部及部分被删客户，不含访客。本统计项支持按机构、时间筛选',
      value: 0,
      suffix: "人"
    },
    {
      title: '累计访客人数',
      describe: '全行在所选日期内，统计没有添加过任何员工的客户，包括公众号访客、企微群访客。本统计项不支持按机构筛选，只支持按时间筛选',
      value: 0,
      suffix: "人"
    },
  ]);
  const [statisticData3, setStatisticData3] = useState([
    {
      title: '员工联系好友人数',
      describe: '所选机构在所选日期截止时，机构名下的员工与好友的聊天总数（不去重），数据来源于企业微信官方接口。本统计项支持按机构、时间筛选（只取截止时间）',
      value: 0,
      suffix: "人"
    },
    {
      title: '外部客户群个数',
      describe: '所选机构在所选日期内，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群数量。本统计项支持按机构、时间筛选（只取截止时间）',
      value: 0,
      suffix: "个"
    },
  ]);
  const [preStatisticData1, setPreStatisticData1] = useState({});
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [preTime, setPreTime] = useState(null);

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then(async (res) => {
      setOrganOption(res);
      await formRef?.current?.setFieldsValue({
        departmentId: res[0].id,
        createTime: [moment(getDay(-30)), moment(getDay(-1))]
      });
      await getStatisticData();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format('YYYY-MM-DD');
        formData.endDate = moment(formData.createTime[1]._d).format('YYYY-MM-DD');
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall('/statistic/statisticData', 'GET', data).then((res) => {
        let { collectPreference, companyCustomerCount, customerCount, customerTag, employeeCustomerCount, groupActiveChart, prevCustomerCount, } = res || {};
        let newStatisticData1 = statisticData1;
        newStatisticData1[0].value = customerCount.allFriendCustomerCount;
        newStatisticData1[1].value = customerCount.allCustomerCount;
        setStatisticData1(newStatisticData1)
        let newStatisticData2 = statisticData2;
        newStatisticData2[0].value = customerCount.newCustomerCount;
        newStatisticData2[1].value = customerCount.visitorCount;
        setStatisticData2(newStatisticData2)
        let newStatisticData3 = statisticData3;
        newStatisticData3[0].value = customerCount.contactCustomerCount;
        newStatisticData3[1].value = customerCount.groupCount;
        setStatisticData3(newStatisticData3)
        setPreStatisticData1(prevCustomerCount);
        setChartsTotal1(collectPreference.length && collectPreference?.map((item) => item.count).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal2(groupActiveChart?.list.length && groupActiveChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal3(customerTag.length || 0);
        setChartsData1(collectPreference?.map((item) => ({
          type: item.name,
          value: item.count,
        })) || []);
        setChartsData2(groupActiveChart?.list || []);
        setChartsData3(customerTag.length && customerTag || null);
        setDataSource1(companyCustomerCount);
        setDataSource2(employeeCustomerCount);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (data, fieldKey = { xField: 'date', yField: 'number' }) => {
    const config = {
      data,
      xField: fieldKey.xField,
      yField: fieldKey.yField,
      label: {
        // 可手动配置 label 数据标签位置
        position: 'middle', // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const DemoWordCloud = (data) => {
    const config = {
      data,
      wordField: 'name',
      weightField: 'count',
      colorField: 'name',
      wordStyle: {
        fontFamily: 'Verdana',
        fontSize: [16, 32],
        rotation: [10, 20],
      },
      // 返回值设置成一个 [0, 1) 区间内的值，
      // 可以让每次渲染的位置相同（前提是每次的宽高一致）。
      random: () => 0.5,
    };
    return <WordCloud {...config} />;
  };

  const DemoPieGender = (data) => {
    const config = {
      appendPadding: 10,
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 1,
      // 设置圆弧起始角度
      startAngle: Math.PI,
      endAngle: Math.PI * 1.5,
      label: {
        type: 'inner',
        offset: '-8%',
        content: '{name}\n{percentage}',
        style: {
          fontSize: 18,
        },
      },
      interactions: [
        {
          type: 'element-active',
        },
      ],
      pieStyle: {
        lineWidth: 0,
      },
    };
    return <Pie {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == 'yesterday') {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == 'week') {
      // createTime = [moment().weekday(0), moment().weekday(6)];
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == 'month') {
      // createTime = [moment().startOf('month'), moment().endOf('month')];
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue('createTime');
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre = moment(value[0]._d).format('YYYY-MM-DD') == moment(preTime[0]._d).format('YYYY-MM-DD');
      const next = moment(value[1]._d).format('YYYY-MM-DD') == moment(preTime[1]._d).format('YYYY-MM-DD');
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  return (
    <Spin spinning={loading}>
      <div className='DataBoard'>
        <FilterBar>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="departmentId" label="机构" >
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间" >
              <RangePicker allowClear={false} disabledDate={disabledAfterDate} format="YYYY-MM-DD" onOpenChange={onOpenChange} />
            </FormItem>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('yesterday')}>
              昨日
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('week')}>
              近7天
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('month')}>
              近30天
            </Button>
          </Form>
        </FilterBar>

        <Row gutter={[16, 0]}
        >
          <Col xs={24} xl={8}>
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构在所选日期截止时，机构名下的直接子机构（不统计子孙机构）中所有员工的企微外部好友人数（不去重），按人数由大到小倒序排列，当存在多个人数相同或为0时按机构名称正序排列。
本统计项支持按机构、时间筛选（只取截止时间）。">机构好友人数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 258 }} showHeader={false} rowKey="id" dataSource={dataSource1} columns={columns1}
                pagination={false} />
            </Card>
          </Col>
          <Col xs={24} xl={8} >
            <WibotStatistic
              list={statisticData1}
              span={12}
            />
            <WibotStatistic
              list={statisticData2}
              span={12}
            />
            <WibotStatistic
              list={statisticData3}
              span={12}
            />
          </Col>
          <Col xs={24} xl={8}>
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构及其子孙机构在所选日期截止时，所有员工的好友人数（不去重）排行榜，按好友数倒序，当存在多个人数相同或为0时按机构名称正序排列，只统计前20
本统计项不支持按时间筛选，只支持按机构筛选（只取截止时间）">
                员工好友人数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 258 }} showHeader={false} rowKey="id" dataSource={dataSource2} columns={columns2}
                pagination={false} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={8}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，客户参与活动集锦问卷最后提交的反馈，所有集锦标签按数量排列取前10+其他集锦标签总数，共统计11份数据，不含访客。
本统计项支持按机构、时间筛选">
                客户集锦偏好分布<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              {chartsData1 && DemoPieGender(chartsData1) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={8}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，取「发过消息的客户群数量」÷「客户群总数量」的百分比，这2个维度的数据来自于企微官方接口
本统计项支持按机构、时间筛选（仅可查询今天往前180天内数据）">
                外部客户群活跃度<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              {chartsData2 && DemoColumn(chartsData2) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={8}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，所有客户的按客户标签数量倒叙排列，取前50个客户标签进行词云展示，不含访客
本统计项支持按机构、时间筛选">
                客户标签分布<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              {/* <span>累计：{chartsTotal3} 个</span> */}
            </div>}>
              {chartsData3 && DemoWordCloud(chartsData3) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

      </div>
    </Spin >
  );
};

export default DataBoard;
