/*
 * @Author: <PERSON><PERSON>w
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2023/08/09 15:40
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DataBoard\EmployeeTaskStatistics\comps\ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  DatePicker,
  Button,
  Image,
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './ExportModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState('1');
  const [imageVisible, setImageVisible] = useState(false);
  const ImagePreviewList = [require('images/群发任务统计.png')];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startTime = moment(formData.date[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.date[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.date;
      }
      const data = {
        ...formData,
      };
      apiCall('/statistic/export/taskStat', 'POST', data, null, {
        isExit: true,
        title: `群发任务统计.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success('导出成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="EmployeeTaskStatistics-ExportModal"
      visible={visible}
      title="导出任务数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
                style={{ display: 'none' }}
                preview={{
                  visible: imageVisible,
                  src: url,
                  onVisibleChange: (value) => {
                    setImageVisible(value);
                  },
                }}
              />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={onOk}
          >
            导出
          </Button>
        </div>,
      ]}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="群发任务统计" key="1">
              <p className="tips">
                统计全部群发任务的任务执行统计（客户、客户群、朋友圈）
              </p>
              <FormItem
                label="任务时间"
                name="date"
                rules={[{ required: true, message: '请选择任务时间' }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择员工、部门"
                rules={[{ required: true, message: '请选择统计对象' }]}
              >
                <ETypeTransferModal title="选择对象" mode={[['dep', 'emp']]} />
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
