/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 17:48
 * @LastEditTime: 2024/10/21 16:21
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/EmployeeTaskStatistics/home.jsx
 * @Description: '员工任务统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Form, DatePicker, Row, Col, Spin, Tooltip, Card, Table, Empty, TreeSelect } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { Line } from '@ant-design/plots';
import { apiCall } from 'common/utils';
import { getDay, disabledAfterDate } from 'common/date';
import ExportModal from './comps/ExportModal';
import WibotStatistic from 'components/WibotStatistic/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const EmployeeTaskStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData1, setStatisticData1] = useState([
    {
      title: '员工待办任务个数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数，待办任务由「任务提醒」、「关怀问候」组成
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: '员工完成任务个数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，完成部分的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: '员工逾期未完成任务数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，逾期未完成部分的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: ' 群发客户任务数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，群发任务中群发客户部分所有任务状态的任务个数，含群发集锦、群发集锦问卷
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    }
  ]);
  const [statisticData2, setStatisticData2] = useState([
    {
      title: '群发客户群任务数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，群发任务中群发客户群部分所有任务状态的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: '群发朋友圈任务数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，群发任务中群发客户朋友圈部分所有任务状态的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: '阅知任务阅知公告任务个数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，阅知任务中阅知公告部分所有任务状态的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    },
    {
      title: ' 阅知任务信息报送任务个数',
      value: 0,
      describe: `所选机构在所选日期内，机构名下所有员工所有待办任务状态的任务个数中，阅知任务中信息报送部分所有任务状态的任务个数
      本统计项支持按机构、时间筛选`,
      suffix: '个'
    }
  ]);
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [dataSource1, setDataSource1] = useState([]);
  const [dataSource2, setDataSource2] = useState([]);
  const [preTime, setPreTime] = useState(null);
  const columns1 = [
    {
      width: '80px',
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const columns2 = [
    {
      width: '80px',
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      width: '100px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const [exportParams, setExportParams] = useState({ visible: false });

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then(async (res) => {
      setOrganOption(res);
      await formRef?.current?.setFieldsValue({
        departmentId: res[0].id,
        createTime: [moment(getDay(-30)), moment(getDay(-1))]
      });
      await getStatisticData();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format('YYYY-MM-DD');
        formData.endDate = moment(formData.createTime[1]._d).format('YYYY-MM-DD');
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall('/statistic/employeeTaskData', 'GET', data).then((res) => {
        const { finishChart, finishCount, finishList, infoPushCount, massSendCustomerCount, massSendGroupCount, massSendMomentCount, noticeCount, overDueCount, toExecChart, toExecCount, toExecList, } = res || {};
        let newStatisticData1 = statisticData1;
        newStatisticData1[0].value = toExecCount;
        newStatisticData1[1].value = finishCount;
        newStatisticData1[2].value = overDueCount;
        newStatisticData1[3].value = massSendCustomerCount;
        setStatisticData1(newStatisticData1);
        let newStatisticData2 = statisticData2;
        newStatisticData2[0].value = massSendGroupCount;
        newStatisticData2[1].value = massSendMomentCount;
        newStatisticData2[2].value = noticeCount;
        newStatisticData2[3].value = infoPushCount;
        setStatisticData2(newStatisticData2);
        setChartsTotal1(toExecChart?.list.length && toExecChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal2(finishChart?.list.length && finishChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsData1(toExecChart?.list || []);
        setChartsData2(finishChart?.list || []);
        setDataSource1(toExecList);
        setDataSource2(finishList);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == 'yesterday') {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == 'week') {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == 'month') {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue('createTime');
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre = moment(value[0]._d).format('YYYY-MM-DD') == moment(preTime[0]._d).format('YYYY-MM-DD');
      const next = moment(value[1]._d).format('YYYY-MM-DD') == moment(preTime[1]._d).format('YYYY-MM-DD');
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false });
      }
    });
  };

  return (
    <Spin spinning={loading}>
      <div className='EmployeeTaskStatistics'>
        <FilterBar>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="departmentId" label="机构" >
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间" >
              <RangePicker allowClear={false} disabledDate={disabledAfterDate} format="YYYY-MM-DD" onOpenChange={onOpenChange} />
            </FormItem>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('yesterday')}>
              昨日
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('week')}>
              近7天
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('month')}>
              近30天
            </Button>
            <Button type="primary" onClick={() => handleExport()}>导出</Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData1}
          span={6}
        />

        <WibotStatistic
          list={statisticData2}
          span={6}
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下所有员工每天所有待办任务状态的任务个数按照日期排列，待办任务由「任务提醒」、「关怀问候」组成
本统计项支持按机构、时间筛选">
                员工待办任务个数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal1} 个</span>
            </div>}>
              {chartsData1 && DemoLine(chartsData1) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期内，机构名下所有员工每天的待办任务状态为已完成的任务个数按照日期排列
本统计项支持按机构、时间筛选">
                员工完成任务个数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal2} 个</span>
            </div>}>
              {chartsData2 && DemoLine(chartsData2) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-card-table' title={<Tooltip title="所选机构在所选日期内，机构名下的直接子机构（不统计子孙机构）中，所有员工的所有待办任务状态的任务个数按照顺序排列，取前50名，按人数由大到小倒序排列，当存在多个人数相同或为0时按机构名称正序排列。
本统计项支持按机构、时间筛选">机构员工待办任务数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="id" dataSource={dataSource1} columns={columns1} pagination={false} />
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-card-table' title={<Tooltip title="所选机构在所选日期内，所选机构及其子孙机构中，所有员工的待办任务状态为已完成的任务个数按照顺序排列，取前50名，按人数由大到小倒序排列，当存在多个人数相同或为0时按机构名称正序排列。
本统计项支持按机构、时间筛选">员工完成任务数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="id" dataSource={dataSource2} columns={columns2} pagination={false} />
            </Card>
          </Col>
        </Row>

        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
      </div>
    </Spin >
  );
};

export default EmployeeTaskStatistics;
