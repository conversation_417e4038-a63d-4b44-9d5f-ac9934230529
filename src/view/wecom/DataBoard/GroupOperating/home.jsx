/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 17:48
 * @LastEditTime: 2024/10/21 16:21
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/GroupOperating/home.jsx
 * @Description: '群运营统计'
 */

import React, { useState, useEffect, useRef } from "react";
import FilterBar from "components/FilterBar/FilterBar";
import {
  Button,
  Form,
  DatePicker,
  Row,
  Col,
  Spin,
  Tooltip,
  Card,
  Table,
  Empty,
  TreeSelect,
  Avatar,
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import moment from "moment";
import { timeStamp, getDay, disabledAfterDate } from "common/date";
import { Column, Line } from "@ant-design/plots";
import { apiCall } from "common/utils";
import ExportModal from "./comps/ExportModal";
import WibotStatistic from 'components/WibotStatistic/home';
import "./home.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const GroupOperating = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData, setStatisticData] = useState([
    {
      title: "外部客户群个数",
      value: 0,
      describe: `所选机构在所选日期截止时，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群数量。
      本统计项支持按机构、时间筛选（只取截止时间）`,
      suffix: "个",
    },
    {
      title: "群客户总人数",
      value: 0,
      describe: `所选机构在所选日期截止时，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群，对所有群内的群客户汇总去重，再减去员工人数
      本统计项支持按机构、时间筛选（只取截止时间）`,
      suffix: "人",
    },
    {
      title: "群内发送消息条数",
      value: 0,
      describe: `所选机构在所选日期内，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群，汇总所有客户群的消息总数，数据来自企微官方接口每天相加
      本统计项支持按机构、时间筛选（仅可查询今天往前180天内数据）`,
      suffix: "条",
    },
    {
      title: " 群内发送违规警告次数",
      value: 0,
      describe: `所选机构在所选日期内，机构名下作为群主的员工所管理企微外部客户群（必须已开通会话存档且由客户群SOP接管），客户群会话中存在客户群SOP定义的违规消息内容，且被SOP自动触发警告的次数，数据每天相加
      本统计项支持按机构、时间筛选`,
      suffix: "次",
    },
  ]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户群名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip lacement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "客户群群主",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex", alignItems: "center" }}>
            {record.avatar && <Avatar size={40} src={record.avatar} />}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return record.avatar && value && record.departmentName ? ( //头像,群主名,机构必须同时有才显示
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      title: "建群时间",
      width: "160px",
      dataIndex: "groupCreateTime",
      key: "groupCreateTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.groupCreateTime) - timeStamp(b.groupCreateTime),
    },
    {
      title: "累计发送消息数",
      width: "100px",
      dataIndex: "totalMessageCount",
      key: "totalMessageCount",
      align: "center",
      sorter: (a, b) => a.totalMessageCount - b.totalMessageCount,
    },
    {
      title: "群状态",
      width: "100px",
      dataIndex: "deleted",
      key: "deleted",
      align: "center",
      render: (value, record, index) => {
        return !value ? "正常" : "已解散";
      },
    },
  ];
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsData4, setChartsData4] = useState(null);
  const [chartsData5, setChartsData5] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [chartsTotal4, setChartsTotal4] = useState(0);
  const [chartsTotal5, setChartsTotal5] = useState(0);
  const [preTime, setPreTime] = useState(null);
  const [exportParams, setExportParams] = useState({ visible: false });

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall("/dept/option", "GET")
      .then(async (res) => {
        setOrganOption(res);
        await formRef?.current?.setFieldsValue({
          departmentId: res[0].id,
          createTime: [moment(getDay(-30)), moment(getDay(-1))],
        });
        await getStatisticData();
        await fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD"
        );
        formData.endDate = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD"
        );
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall("/statistic/groupData", "GET", data)
        .then((res) => {
          const {
            customerInviteChart,
            groupActiveChart,
            groupCount,
            groupCustomerChart,
            memberCount,
            messageChart,
            messageCount,
            warningCount,
            groupChart,
          } = res || {};
          let newStatisticData = statisticData;
          newStatisticData[0].value = groupCount;
          newStatisticData[1].value = memberCount;
          newStatisticData[2].value = messageCount;
          newStatisticData[3].value = warningCount;
          setStatisticData(newStatisticData);
          setChartsTotal1(
            (groupCustomerChart?.list.length &&
              groupCustomerChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal2(
            (customerInviteChart?.list.length &&
              customerInviteChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal3(
            (groupChart?.list.length &&
              groupChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal4(
            (messageChart?.list.length &&
              messageChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal5(
            (groupActiveChart?.list.length &&
              groupActiveChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsData1(groupCustomerChart?.list || []);
          setChartsData2(customerInviteChart?.list || []);
          setChartsData3(groupChart?.list || []);
          setChartsData4(messageChart?.list || []);
          setChartsData5(groupActiveChart?.list || []);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD"
        );
        formData.endDate = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD"
        );
        delete formData.createTime;
      }
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        // ...query,
        // ...formData,
      };
      apiCall("/statistic/groupData/page", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (data) => {
    const config = {
      data,
      xField: "date",
      yField: "number",
      label: {
        // 可手动配置 label 数据标签位置
        position: "middle", // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: "#FFFFFF",
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5);
              return `${value.slice(0, 4)}...`;
            }
            return value;
          },
        },
      },
      meta: {
        number: {
          alias: "总数",
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: "date",
      yField: "number",
      label: {},
      point: {
        size: 5,
        shape: "diamond",
        style: {
          fill: "white",
          stroke: "#5B8FF9",
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: "#000",
            fill: "red",
          },
        },
      },
      interactions: [
        {
          type: "marker-active",
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5);
              return `${value.slice(0, 4)}...`;
            }
            return value;
          },
        },
      },
      meta: {
        number: {
          alias: "总数",
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == "yesterday") {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == "week") {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == "month") {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
    await fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue("createTime");
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre =
        moment(value[0]._d).format("YYYY-MM-DD") ==
        moment(preTime[0]._d).format("YYYY-MM-DD");
      const next =
        moment(value[1]._d).format("YYYY-MM-DD") ==
        moment(preTime[1]._d).format("YYYY-MM-DD");
      if (pre && next) {
        return;
      }
      await getStatisticData();
      await fetchList();
    }
  };

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false });
      },
    });
  };

  return (
    <Spin spinning={loading}>
      <div className="GroupOperating">
        <FilterBar>
          <Form layout={"inline"} ref={formRef}>
            <FormItem name="departmentId" label="机构">
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                  await fetchList();
                }}
                treeData={organOption}
                fieldNames={{
                  label: "name",
                  value: "id",
                  children: "childList",
                }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间">
              <RangePicker
                allowClear={false}
                disabledDate={disabledAfterDate}
                format="YYYY-MM-DD"
                onOpenChange={onOpenChange}
              />
            </FormItem>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("yesterday")}
            >
              昨日
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("week")}
            >
              近7天
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("month")}
            >
              近30天
            </Button>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData}
          span={6}
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip
                    title="所选机构在所选日期截止时，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群，对所有群内的群客户汇总去重，再减去员工人数
本统计项支持按机构、时间筛选（只取截止时间）"
                  >
                    群客户总人数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  {/* <span>累计：{chartsTotal1} 人</span> */}
                </div>
              }
            >
              {(chartsData1 && DemoLine(chartsData1)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip
                    title="所选机构在所选日期内，机构名下作为群主的员工所管理过的所有企微外部客户群（含已解散、未解散客户群），客户群内每天客户主动邀请客户进群的人数按日期排列
本统计项支持按机构、时间筛选"
                  >
                    客户邀请人数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  <span>累计：{chartsTotal2} 人</span>
                </div>
              }
            >
              {(chartsData2 && DemoLine(chartsData2)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip
                    title="所选机构在所选日期截止时，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群数量。
本统计项支持按机构、时间筛选（只取截止时间）"
                  >
                    外部客户群个数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  {/* <span>累计：{chartsTotal3} 个</span> */}
                </div>
              }
            >
              {(chartsData3 && DemoLine(chartsData3)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip
                    title="所选机构在所选日期内，机构名下作为群主的员工所管理的处于未解散状态的所有企微外部客户群，汇总每天所有客户群的消息总数按日期排列，数据来自企微官方接口每天相加
本统计项支持按机构、时间筛选（仅可查询今天往前180天内数据）"
                  >
                    群内发送消息条数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  <span>累计：{chartsTotal4} 条</span>
                </div>
              }
            >
              {(chartsData4 && DemoColumn(chartsData4)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip
                    title="所选机构在所选日期内，取每天「发过消息的客户群数量」÷「客户群总数量」的百分比按照日期排列，这2个维度的数据来自于企微官方接口
                    多天算法：若m个成员在n日内，一共有x个客户主动发起会话，其中在当天回复的会话一共有y个。则这m个成员在n日内的已回复聊天占比为：y/x。
                    本统计项支持按机构、时间筛选（仅可查询今天往前180天内数据）"
                  >
                    外部客户群活跃度
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  {/* <span>累计：{chartsTotal5.toFixed(2)} %</span> */}
                </div>
              }
            >
              {(chartsData5 && DemoColumn(chartsData5)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card
              className="ant-card-table"
              title={
                <Tooltip title="全行所有已解散及未解散的企微外部客户群，本统计项不支持按机构、时间筛选">
                  群组明细
                  <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                </Tooltip>
              }
            >
              <Table
                rowKey="id"
                dataSource={dataSource}
                columns={columns}
                scroll={{ y: 281, x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </Card>
          </Col>
        </Row>

        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
      </div>
    </Spin>
  );
};

export default GroupOperating;
