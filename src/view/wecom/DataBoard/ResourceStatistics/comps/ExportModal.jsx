/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/03/07 10:53
 * @LastEditTime: 2024/10/22 09:13
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/ResourceStatistics/comps/ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  DatePicker,
  Button,
  Image,
  Radio,
} from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './ExportModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState('1');
  const [dimensionValue, setDimensionValue] = useState('dep');
  const [imageVisible, setImageVisible] = useState(false);
  const ImagePreviewList = [
    require('images/资源总览.png'),
    dimensionValue == 'dep'
      ? require('images/资源转发汇总（部门）.png')
      : require('images/资源转发汇总（员工）.png'),
    require('images/资源转发访问统计（员工）.png'),
    require('images/资源转发访问明细（员工）.png'),
    require('images/资源转发访问统计（部门）.png'),
  ];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.shareStartTime = moment(formData.date[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.shareEndTime = moment(formData.date[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.date;
      }
      if (formData.visitDate) {
        formData.visitStartTime = moment(formData.visitDate[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.visitEndTime = moment(formData.visitDate[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.visitDate;
      }
      const data = {
        ...formData,
      };
      let apiUrl = '/statistic/export/resourceOverview';
      let title = '资源总览';
      if (TabsActiveKey == '2') {
        apiUrl =
          dimensionValue == 'dep'
            ? '/statistic/export/resourceShareDeptOverView'
            : '/statistic/export/resourceShareEmployeeOverView';
        title = '资源转发汇总';
      } else if (TabsActiveKey == '3') {
        apiUrl = '/statistic/export/resourceShareEmployeeStat';
        title = '资源转发访问统计（员工）';
      } else if (TabsActiveKey == '4') {
        apiUrl = '/statistic/export/resourceShareEmployeeDetail';
        title = '资源转发访问明细（员工）';
      } else if (TabsActiveKey == '5') {
        apiUrl = '/statistic/export/resourceShareDepartmentStat';
        title = '资源转发访问统计（部门）';
      }
      apiCall(apiUrl, 'POST', data, null, {
        isExit: true,
        title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success('导出成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setDimensionValue('dep');
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="ResourceStatistics-ExportModal"
      visible={visible}
      title="导出资源数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
                style={{ display: 'none' }}
                preview={{
                  visible: imageVisible,
                  src: url,
                  onVisibleChange: (value) => {
                    setImageVisible(value);
                  },
                }}
              />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={onOk}
          >
            导出
          </Button>
        </div>,
      ]}
    >
      <Spin spinning={loading}>
        <Form
          ref={formRef}
          initialValues={{
            dimension: 'dep',
          }}
        >
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="资源总览" key="1">
              <p className="tips">
                统计全部资源历史总数及今日新增数
                <br />
                资源类型包括：海报、推文、网页文章、产品、小程序、资讯、集锦问卷、活动集锦、表单工具
              </p>
            </Tabs.TabPane>

            <Tabs.TabPane tab="资源转发汇总" key="2">
              {
                TabsActiveKey === '2' && <>
                  <p className="tips">
                    根据员工转发的时间来计算统计对象的资源数量及访问数量
                    <br />
                    资源类型包括：海报、推文、网页文章、产品、小程序、资讯、集锦问卷、活动集锦、表单工具
                  </p>
                  <FormItem
                    label="转发时间"
                    name="date"
                    rules={[{ required: true, message: '请选择转发时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="访问时间"
                    name="visitDate"
                    rules={[{ required: true, message: '请选择访问时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem label="统计对象" required>
                    <div>
                      <FormItem style={{ margin: '0' }} name="dimension">
                        <Radio.Group
                          onChange={(e) => {
                            setDimensionValue(e.target.value);
                            formRef?.current?.setFieldValue(
                              'depEmployeeIdList',
                              []
                            );
                          }}
                        >
                          <Radio value={'dep'}>部门维度</Radio>
                          <Radio value={'emp'}>员工维度</Radio>
                        </Radio.Group>
                      </FormItem>
                      {dimensionValue == 'dep' && (
                        <FormItem
                          name="depEmployeeIdList"
                          extra="支持选择部门"
                          rules={[{ required: true, message: '请选择统计对象' }]}
                        >
                          <ETypeTransferModal title="选择对象" onlyDepartment />
                        </FormItem>
                      )}
                      {dimensionValue == 'emp' && (
                        <FormItem
                          name="depEmployeeIdList"
                          extra="支持选择员工"
                          rules={[{ required: true, message: '请选择统计对象' }]}
                        >
                          <ETypeTransferModal
                            title="选择对象"
                            mode={['emp', 'tag']}
                            // onlyEmployee
                          />
                        </FormItem>
                      )}
                    </div>
                  </FormItem>
                </>
              }
            </Tabs.TabPane>

            <Tabs.TabPane tab="资源转发访问统计（员工）" key="3">
              {
                TabsActiveKey === '3' && <>
                  <p className="tips">
                    根据员工转发的时间来计算统计对象的资源转发数据，有哪些客户访问了什么资源
                  </p>
                  <FormItem
                    label="转发时间"
                    name="date"
                    rules={[{ required: true, message: '请选择转发时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="访问时间"
                    name="visitDate"
                    rules={[{ required: true, message: '请选择访问时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="统计对象"
                    name="depEmployeeIdList"
                    extra="直接选择员工，或通过部门选择员工"
                    rules={[{ required: true, message: '请选择统计对象' }]}
                  >
                    <ETypeTransferModal title="选择对象" />
                  </FormItem>
                </>
              }
            </Tabs.TabPane>

            <Tabs.TabPane tab="资源转发访问明细（员工）" key="4">
              {
                TabsActiveKey === '4' && <>
                  <p className="tips">
                    根据员工转发的时间来计算统计对象的资源转发数据，有哪些客户访问了什么资源
                  </p>
                  <FormItem
                    label="转发时间"
                    name="date"
                    rules={[{ required: true, message: '请选择转发时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="访问时间"
                    name="visitDate"
                    rules={[{ required: true, message: '请选择访问时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="统计对象"
                    name="depEmployeeIdList"
                    extra="直接选择员工，或通过部门选择员工"
                    rules={[{ required: true, message: '请选择统计对象' }]}
                  >
                    <ETypeTransferModal title="选择对象" />
                  </FormItem>
                </>
              }
            </Tabs.TabPane>

            <Tabs.TabPane tab="资源转发访问统计（部门）" key="5">
              {
                TabsActiveKey === '5' && <>
                  <p className="tips">
                    根据员工转发的时间来计算统计对象的资源转发数据，有哪些客户访问了什么资源
                  </p>
                  <FormItem
                    label="转发时间"
                    name="date"
                    rules={[{ required: true, message: '请选择转发时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="访问时间"
                    name="visitDate"
                    rules={[{ required: true, message: '请选择访问时间' }]}
                  >
                    <RangePicker />
                  </FormItem>
                  <FormItem
                    label="统计对象"
                    name="depEmployeeIdList"
                    extra="支持选择部门"
                    rules={[{ required: true, message: '请选择统计对象' }]}
                  >
                    <ETypeTransferModal title="选择部门" onlyDepartment />
                  </FormItem>
                </>
              }
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
