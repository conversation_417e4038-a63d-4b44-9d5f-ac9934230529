/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 17:48
 * @LastEditTime: 2024/10/21 16:21
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/ResourceStatistics/home.jsx
 * @Description: '资源使用统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Form, DatePicker, Row, Col, Spin, Tooltip, Card, Table, Empty, TreeSelect } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { Column, Line, WordCloud } from '@ant-design/plots';
import { apiCall } from 'common/utils';
import SysDictSelect from 'components/select/SysDictSelect';
import { getDay, disabledAfterDate } from 'common/date';
import ExportModal from './comps/ExportModal';
import WibotStatistic from 'components/WibotStatistic/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ResourceStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData1, setStatisticData1] = useState([
    {
      title: '当前在架资源数量',
      value: 0,
      describe: `所选机构「数据权限+可见范围」名下的资源，所选资源类型中资源状态为已上架的资源个数。
      本统计项不支持时间筛选，支持按机构、类型`,
      suffix: '个'
    },
    {
      title: '新增资源数量',
      value: 0,
      describe: `所选机构名下的资源，所选时间新增的，所选资源类型中全部资源状态的资源个数。
      本统计项支持按机构、类型、时间筛选`,
      suffix: '个'
    },
    {
      title: '资源访问次数',
      value: 0,
      describe: `所选机构名下的资源，所选资源类型为推文、网页文章、视频链接、海报链接等链接形式，被任何用户在所选日期内访问的次数，微信用户授权后才被统计。
      本统计项支持按机构、类型、时间筛选`,
      suffix: '次'
    },
  ]);
  const [statisticData2, setStatisticData2] = useState([
    {
      title: '资源访问人数',
      value: 0,
      describe: `所选机构名下的资源，所选资源类型为推文、网页文章、视频链接、海报链接等链接形式，被任何用户在所选日期内访问的人数，微信用户授权后才被统计。
      本统计项支持按机构、类型、时间筛选`,
      suffix: '人'
    },
    {
      title: '员工发送资源人数',
      value: 0,
      describe: `所选机构名下的资源，累计发送所选资源类型的员工人数。
      本统计项不支持按时间筛选，支持按机构、类型筛选`,
      suffix: '人'
    },
    {
      title: '员工发送资源次数',
      value: 0,
      describe: `所选机构名下的资源，累计员工发送所选资源类型的次数。
      本统计项不支持按时间筛选，支持按机构、类型筛选`,
      suffix: '次'
    },
  ]);

  const [dataSource1, setDataSource1] = useState([]);
  const [dataSource2, setDataSource2] = useState([]);
  const [dataSource3, setDataSource3] = useState([]);
  const columns1 = [
    {
      width: '80px',
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const columns2 = [
    {
      width: '80px',
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];
  const columns3 = [
    {
      width: '80px',
      height: '40',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      width: '100px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
    },
    {
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
  ];

  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsData4, setChartsData4] = useState(null);
  const [chartsData5, setChartsData5] = useState(null);
  const [chartsData6, setChartsData6] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [chartsTotal4, setChartsTotal4] = useState(0);
  const [chartsTotal5, setChartsTotal5] = useState(0);
  const [chartsTotal6, setChartsTotal6] = useState(0);
  const [preTime, setPreTime] = useState(null);
  const [exportParams, setExportParams] = useState({ visible: false });

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then(async (res) => {
      setOrganOption(res);
      await formRef?.current?.setFieldsValue({
        departmentId: res[0].id,
        createTime: [moment(getDay(-30)), moment(getDay(-1))],
      });
      await getStatisticData();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format('YYYY-MM-DD');
        formData.endDate = moment(formData.createTime[1]._d).format('YYYY-MM-DD');
        delete formData.createTime;
      }
      formData.typeList = formData.typeList?.join(',') || '';
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall('/statistic/resourceData', 'GET', data).then((res) => {
        const { resourceTag, employeeShareResourceList, onResourceCount, resourceCount, resourceCountByType, shareChart, shareCount, shareResourceList, sharerChart, sharerCount, visitChart, visitCount, visitorChart, visitorCount, onResourceChart } = res || {};
        let newStatisticData1 = statisticData1;
        newStatisticData1[0].value = resourceCount;
        newStatisticData1[1].value = onResourceCount;
        newStatisticData1[2].value = visitCount;
        setStatisticData1(newStatisticData1);
        let newStatisticData2 = statisticData2;
        newStatisticData2[0].value = visitorCount;
        newStatisticData2[1].value = sharerCount;
        newStatisticData2[2].value = shareCount;
        setStatisticData2(newStatisticData2);
        setChartsTotal1(resourceTag.length || 0);
        setChartsTotal2(onResourceChart?.list.length && onResourceChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal3(visitorChart?.list.length && visitorChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal4(visitChart?.list.length && visitChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal5(sharerChart?.list.length && sharerChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal6(shareChart?.list.length && shareChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsData1(resourceTag.length && resourceTag || null);
        setChartsData2(onResourceChart?.list || []);
        setChartsData3(visitorChart?.list || []);
        setChartsData4(visitChart?.list || []);
        setChartsData5(sharerChart?.list || []);
        setChartsData6(shareChart?.list || []);
        setDataSource1(resourceCountByType);
        setDataSource2(shareResourceList);
        setDataSource3(employeeShareResourceList);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {
        // 可手动配置 label 数据标签位置
        position: 'middle', // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  const DemoWordCloud = (data) => {
    const config = {
      data,
      height: 128,
      wordField: 'name',
      weightField: 'count',
      colorField: 'name',
      tooltip: {
        formatter: (datum) => ({ name: datum.text, value: datum.value + '个' })
      },
      wordStyle: {
        fontFamily: 'Verdana',
        fontSize: [16, 32],
        rotation: [10, 20],
      },
      // 返回值设置成一个 [0, 1) 区间内的值，
      // 可以让每次渲染的位置相同（前提是每次的宽高一致）。
      random: () => 0.5,
    };
    return <WordCloud {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == 'yesterday') {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == 'week') {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == 'month') {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue('createTime');
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre = moment(value[0]._d).format('YYYY-MM-DD') == moment(preTime[0]._d).format('YYYY-MM-DD');
      const next = moment(value[1]._d).format('YYYY-MM-DD') == moment(preTime[1]._d).format('YYYY-MM-DD');
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  const handleExport = () => {
    setExportParams({
      visible: true,
      onCancel: () => {
        setExportParams({ visible: false });
      }
    });
  };

  return (
    <Spin spinning={loading}>
      <div className='ResourceStatistics'>
        <FilterBar>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="departmentId" label="机构" >
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="typeList">
              <SysDictSelect
                dataset="Resource_Type"
                placeholder="资源类型"
                mode="multiple"
                maxTagCount="responsive"
                onChange={async () => {
                  await getStatisticData();
                }} />
            </FormItem>
            <FormItem name="createTime" label="统计时间" >
              <RangePicker allowClear={false} disabledDate={disabledAfterDate} format="YYYY-MM-DD" onOpenChange={onOpenChange} />
            </FormItem>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('yesterday')}>
              昨日
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('week')}>
              近7天
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('month')}>
              近30天
            </Button>
            <Button type="primary" onClick={() => handleExport()}>导出</Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData1}
          span={8}
        />
        <WibotStatistic
          list={statisticData2}
          span={8}
        />

        <Row gutter={[16, 0]}>
          <Col flex="auto">
            <Card title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下的资源，所选资源类型中全部资源所关联的资源标签个数，所有资源标签按数量排列取靠前的50个标签。
本统计项不支持按时间筛选，支持按机构、类型筛选">
                资源标签分布<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              {/* <span>累计：{chartsTotal1} 个</span> */}
            </div>}>
              {chartsData1 && DemoWordCloud(chartsData1) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构名下，所选时间新增的，所选资源类型中全部资源状态的资源个数。
本统计项支持按机构、类型、时间筛选">
                资源数量<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="name" dataSource={dataSource1} columns={columns1} pagination={false} />
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构名下子机构的所有员工，在所选时间内对所选资源类型的分享次数
本统计项支持按机构、类型、时间筛选">
                机构发送资源次数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="departmentId" dataSource={dataSource2} columns={columns2} pagination={false} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-card-table' title={<div>
              <Tooltip title="所选机构名下子机构及子孙机构的所有员工，在所选时间内对所选资源类型的分享次数
本统计项支持按机构、类型、时间筛选">
                员工发送资源次数排行榜<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
            </div>}>
              <Table scroll={{ y: 400 }} showHeader={false} rowKey="employeeId" dataSource={dataSource3} columns={columns3} pagination={false} />
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下，所选日期内，所选资源类型中资源状态为已上架的资源个数。
本统计项支持按机构、类型、时间筛选">
                在架资源趋势<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal2} 个</span>
            </div>}>
              {chartsData2 && DemoLine(chartsData2) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下的资源，所选资源类型为推文、网页文章、视频链接、海报链接等链接形式，被任何用户在所选日期内访问的次数，微信用户授权后才被统计。
本统计项支持按机构、类型、时间筛选">
                资源访问人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal3} 人</span>
            </div>}>
              {chartsData3 && DemoLine(chartsData3) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下的资源，所选资源类型为推文、网页文章、视频链接、海报链接等链接形式，被任何用户在所选日期内访问的人数，微信用户授权后才被统计。
本统计项支持按机构、类型、时间筛选">
                资源访问次数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal4} 次</span>
            </div>}>
              {chartsData4 && DemoLine(chartsData4) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下，所选时间发送所选资源类型的员工人数。
本统计项支持按机构、类型、时间筛选">
                员工发送资源人数<QuestionCircleOutlined style={{ marginLeft: '4px' }} />
              </Tooltip>
              <span>累计：{chartsTotal5} 人</span>
            </div>}>
              {chartsData5 && DemoColumn(chartsData5) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构名下，所选时间员工发送的所选资源类型的次数。
本统计项支持按机构、类型、时间筛选">
                员工发送资源次数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal6} 次</span>
            </div>}>
              {chartsData6 && DemoColumn(chartsData6) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        {/* 导出对话框 */}
        <ExportModal params={exportParams} />
      </div>
    </Spin >
  );
};

export default ResourceStatistics;
