/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/06 15:14
 * @LastEditTime: 2024/10/21 16:21
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/SessionStatistics/GroupChat/home.jsx
 * @Description: '群聊统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Form, DatePicker, Row, Col, Spin, Card, Tooltip, Empty, TreeSelect } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { apiCall } from 'common/utils';
import { Column } from '@ant-design/plots';
import { getDay, disabledAfterDate } from 'common/date';
import WibotStatistic from 'components/WibotStatistic/home';
import './home.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const GroupChatStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData, setStatisticData] = useState([
    {
      title: '会话存档群聊个数',
      value: 0,
      describe: `所选机构在所选日期段内，机构名下所有开通会话存档的员工所创建的企微外部客户群数`,
      suffix: '个'
    },
    {
      title: '群聊消息总数',
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量，包含员工及客户发言`,
      suffix: '条'
    },
    {
      title: '群聊客户消息条数',
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量中客户发言的部分`,
      suffix: '条'
    },
    {
      title: ' 群聊员工消息条数',
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量中员工发言的部分`,
      suffix: '条'
    }
  ]);
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsData4, setChartsData4] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [chartsTotal4, setChartsTotal4] = useState(0);
  const [preTime, setPreTime] = useState(null);

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then(async (res) => {
      setOrganOption(res);
      await formRef?.current?.setFieldsValue({
        departmentId: res[0].id,
        createTime: [moment(getDay(-30)), moment(getDay(-1))]
      });
      await getStatisticData();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format('YYYY-MM-DD');
        formData.endDate = moment(formData.createTime[1]._d).format('YYYY-MM-DD');
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall('/statistic/sessionGroupData', 'GET', data).then((res) => {
        let { sessionGroupNumber, sendMessageCount, customerSendMessageCount, employeeSendMessageCount, sessionGroupNumberChart, sendMessageCountChart, customerSendMessageCountChart, employeeSendMessageCountChart } = res || {};
        let newStatisticData = statisticData;
        newStatisticData[0].value = sessionGroupNumber;
        newStatisticData[1].value = sendMessageCount;
        newStatisticData[2].value = customerSendMessageCount;
        newStatisticData[3].value = employeeSendMessageCount;
        setStatisticData(newStatisticData);
        setChartsTotal1(sessionGroupNumberChart?.list.length && sessionGroupNumberChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal2(sendMessageCountChart?.list.length && sendMessageCountChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal3(customerSendMessageCountChart?.list.length && customerSendMessageCountChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsTotal4(employeeSendMessageCountChart?.list.length && employeeSendMessageCountChart?.list.map((item) => item.number).reduce((accVal, curVal) => accVal + curVal) || 0);
        setChartsData1(sessionGroupNumberChart?.list || []);
        setChartsData2(sendMessageCountChart?.list || []);
        setChartsData3(customerSendMessageCountChart?.list || []);
        setChartsData4(employeeSendMessageCountChart?.list || []);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (data, fieldKey = { xField: 'date', yField: 'number' }) => {
    const config = {
      data,
      xField: fieldKey.xField,
      yField: fieldKey.yField,
      label: {
        // 可手动配置 label 数据标签位置
        position: 'middle', // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`;
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
        count: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == 'yesterday') {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == 'week') {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == 'month') {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue('createTime');
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre = moment(value[0]._d).format('YYYY-MM-DD') == moment(preTime[0]._d).format('YYYY-MM-DD');
      const next = moment(value[1]._d).format('YYYY-MM-DD') == moment(preTime[1]._d).format('YYYY-MM-DD');
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  return (
    <Spin spinning={loading}>
      <div className='GroupChatStatistics'>
        <FilterBar bodyStyle={{ padding: "unset" }}>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="departmentId" label="机构" >
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间" >
              <RangePicker allowClear={false} disabledDate={disabledAfterDate} format="YYYY-MM-DD" onOpenChange={onOpenChange} />
            </FormItem>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('yesterday')}>
              昨日
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('week')}>
              近7天
            </Button>
            <Button type="primary" htmlType="submit" onClick={() => handleQuery('month')}>
              近30天
            </Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData}
          span={6}
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期段内，机构名下所有开通会话存档的员工所创建的企微外部客户群数">
                会话存档群聊个数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal1} 个</span>
            </div>}>
              {chartsData1 && DemoColumn(chartsData1) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量，包含员工及客户发言">
                群聊消息总数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal2} 条</span>
            </div>}>
              {chartsData2 && DemoColumn(chartsData2) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量中客户发言的部分">
                群聊客户消息条数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal3} 条</span>
            </div>}>
              {chartsData3 && DemoColumn(chartsData3) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className='ant-chartCard' title={<div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工所创建的企微外部客户群，群内发生的所有聊天消息数量中员工发言的部分">
                群聊员工消息条数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
              <span>累计：{chartsTotal4} 条</span>
            </div>}>
              {chartsData4 && DemoColumn(chartsData4) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
      </div>
    </Spin >
  );
};

export default GroupChatStatistics;
