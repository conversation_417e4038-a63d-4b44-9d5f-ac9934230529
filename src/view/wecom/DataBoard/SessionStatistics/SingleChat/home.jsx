/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/06 15:14
 * @LastEditTime: 2023/12/04 16:09
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/SessionStatistics/SingleChat/home.jsx
 * @Description: '单聊统计'
 */

import React, { useState, useEffect, useRef } from "react";
import FilterBar from "components/FilterBar/FilterBar";
import {
  Button,
  Form,
  DatePicker,
  Row,
  Col,
  Spin,
  Card,
  Tooltip,
  Empty,
  TreeSelect,
} from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";
import moment from "moment";
import { apiCall } from "common/utils";
import { Column, Pie } from "@ant-design/plots";
import { getDay, disabledAfterDate } from "common/date";
import WibotStatistic from 'components/WibotStatistic/home';
import "./home.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const SingleChatStatistics = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [organOption, setOrganOption] = useState([]);
  const [statisticData, setStatisticData] = useState([
    {
      title: "会话好友人数",
      value: 0,
      describe: `所选机构在所选日期段内，机构名下所有开通会话存档的员工与好友的聊天人数，仅包括单聊，不去重`,
      suffix: "人",
    },
    {
      title: "发送消息条数",
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工与好友的聊天数量，包含员工及好友发言，仅包括单聊`,
      suffix: "条",
    },
    {
      title: "已回复消息占比",
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工，在好友主动发起聊天后，员工在一个自然日内有回复过消息的聊天数/好友主动发起的聊天人数，一个好友一天发送多次算一次，仅包括单聊。支持机构、日期筛选`,
      suffix: "%",
    },
    {
      title: " 平均首次回复时长",
      value: 0,
      describe: `所选机构在所选日期段内，该机构名下所有开通会话存档的员工，在好友主动发起聊天后，员工在一个自然日内首次回复的时长间隔为首次回复时长，所有聊天的首次回复总时长/已回复的聊天总数即为平均首次回复时长，仅包括单聊。在该好友主动发起的聊天中，好友一开始有主动发送多条消息，则以发送的第一条消息的时间为准进行首次回复时长的计算`,
      suffix: "秒",
    },
  ]);
  const [chartsData1, setChartsData1] = useState(null);
  const [chartsData2, setChartsData2] = useState(null);
  const [chartsData3, setChartsData3] = useState(null);
  const [chartsData4, setChartsData4] = useState(null);
  const [chartsTotal1, setChartsTotal1] = useState(0);
  const [chartsTotal2, setChartsTotal2] = useState(0);
  const [chartsTotal3, setChartsTotal3] = useState(0);
  const [chartsTotal4, setChartsTotal4] = useState(0);
  const [preTime, setPreTime] = useState(null);

  useEffect(async () => {
    getDepOptions();
  }, []);

  const getDepOptions = async () => {
    setLoading(true);
    await apiCall("/dept/option", "GET")
      .then(async (res) => {
        setOrganOption(res);
        await formRef?.current?.setFieldsValue({
          departmentId: res[0].id,
          createTime: [moment(getDay(-30)), moment(getDay(-1))],
        });
        await getStatisticData();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const getStatisticData = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startDate = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD"
        );
        formData.endDate = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD"
        );
        delete formData.createTime;
      }
      const { query } = params;
      const data = {
        ...query,
        ...formData,
      };
      apiCall("/statistic/sessionData", "GET", data)
        .then((res) => {
          let {
            averageFirstResponseTime,
            repliedMessagePercentage,
            sendMessageCount,
            sessionPeopleNumber,
            sessionPeopleNumberChart,
            sendMessageCountChart,
            repliedMessagePercentageChart,
            averageFirstResponseTimeChart,
          } = res || {};
          let newStatisticData = statisticData;
          newStatisticData[0].value = sessionPeopleNumber;
          newStatisticData[1].value = sendMessageCount;
          newStatisticData[2].value = repliedMessagePercentage;
          newStatisticData[3].value = averageFirstResponseTime;
          setStatisticData(newStatisticData);
          setChartsTotal1(
            (sessionPeopleNumberChart?.list.length &&
              sessionPeopleNumberChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal2(
            (sendMessageCountChart?.list.length &&
              sendMessageCountChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsTotal3(repliedMessagePercentageChart?.totalCount);
          setChartsTotal4(
            (averageFirstResponseTimeChart?.list.length &&
              averageFirstResponseTimeChart?.list
                .map((item) => item.number)
                .reduce((accVal, curVal) => accVal + curVal)) ||
            0
          );
          setChartsData1(sessionPeopleNumberChart?.list || []);
          setChartsData2(sendMessageCountChart?.list || []);
          setChartsData3({
            repliedCount: repliedMessagePercentageChart?.repliedCount,
            unRepliedCount: repliedMessagePercentageChart?.unRepliedCount,
          });
          setChartsData4(averageFirstResponseTimeChart?.list || []);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const DemoColumn = (
    data,
    fieldKey = { xField: "date", yField: "number" },
    type = "count"
  ) => {
    const config = {
      data,
      xField: fieldKey.xField,
      yField: fieldKey.yField,
      label: {
        // 可手动配置 label 数据标签位置
        position: "middle", // 'top', 'bottom', 'middle',
        // 配置样式
        style: {
          fill: "#FFFFFF",
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5);
              return `${value.slice(0, 4)}...`;
            }
            return value;
          },
        },
      },
      tooltip: {
        formatter: (datum) => {
          return {
            name: type == "time" ? "时长" : "总数",
            value: type == "time" ? datum.number + "秒" : datum.number,
          };
        },
      },
      meta: {
        number: {
          alias: type == "time" ? "时长" : "总数",
        },
        count: {
          alias: type == "time" ? "时长" : "总数",
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Column {...config} />;
  };

  const DemoPieGender = (data) => {
    data = [
      {
        type: "已回复",
        value: data.repliedCount,
      },
      {
        type: "未回复",
        value: data.unRepliedCount,
      },
    ];
    const config = {
      appendPadding: 10,
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 0.75,
      label: {
        type: 'spider',
        labelHeight: 28,
        content: '{name}\n{percentage}',
      },
      interactions: [
        {
          type: 'element-selected',
        },
        {
          type: 'element-active',
        },
      ],
    };
    return <Pie {...config} />;
  };

  const handleQuery = async (date) => {
    let createTime = [];
    if (date == "yesterday") {
      createTime = [moment(getDay(-1)), moment(getDay(-1))];
    } else if (date == "week") {
      createTime = [moment(getDay(-7)), moment(getDay(-1))];
    } else if (date == "month") {
      createTime = [moment(getDay(-30)), moment(getDay(-1))];
    }
    await formRef.current.setFieldsValue({ createTime });
    await getStatisticData();
  };

  const onOpenChange = async (open) => {
    const value = formRef.current.getFieldValue("createTime");
    if (open) {
      setPreTime(value);
      return;
    }
    if (!open && value) {
      const pre =
        moment(value[0]._d).format("YYYY-MM-DD") ==
        moment(preTime[0]._d).format("YYYY-MM-DD");
      const next =
        moment(value[1]._d).format("YYYY-MM-DD") ==
        moment(preTime[1]._d).format("YYYY-MM-DD");
      if (pre && next) {
        return;
      }
      await getStatisticData();
    }
  };

  return (
    <Spin spinning={loading}>
      <div className="SingleChatStatistics">
        <FilterBar bodyStyle={{ padding: "unset" }}>
          <Form layout={"inline"} ref={formRef}>
            <FormItem name="departmentId" label="机构">
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                onChange={async () => {
                  await getStatisticData();
                }}
                treeData={organOption}
                fieldNames={{
                  label: "name",
                  value: "id",
                  children: "childList",
                }}
              />
            </FormItem>
            <FormItem name="createTime" label="统计时间">
              <RangePicker
                allowClear={false}
                disabledDate={disabledAfterDate}
                format="YYYY-MM-DD"
                onOpenChange={onOpenChange}
              />
            </FormItem>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("yesterday")}
            >
              昨日
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("week")}
            >
              近7天
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleQuery("month")}
            >
              近30天
            </Button>
          </Form>
        </FilterBar>

        <WibotStatistic
          list={statisticData}
          span={6}
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip title="所选机构在所选日期段内，机构名下所有开通会话存档的员工与好友的聊天人数，仅包括单聊，不去重">
                    会话好友人数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  <span>累计：{chartsTotal1} 人</span>
                </div>
              }
            >
              {(chartsData1 && DemoColumn(chartsData1)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工与好友的聊天数量，包含员工及好友发言，仅包括单聊">
                    发送消息条数
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                </div>
              }
            >
              {(chartsData2 && DemoColumn(chartsData2)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工，在好友主动发起聊天后，员工在一个自然日内有回复过消息的聊天数/好友主动发起的聊天人数，一个好友一天发送多次算一次，仅包括单聊。支持机构、日期筛选">
                    已回复消息占比
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                  <span>累计：{chartsTotal3} 人</span>
                </div>
              }
            >
              {(chartsData3 && DemoPieGender(chartsData3)) || (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card
              className="ant-chartCard"
              title={
                <div
                  style={{ display: "flex", justifyContent: "space-between" }}
                >
                  <Tooltip title="所选机构在所选日期段内，该机构名下所有开通会话存档的员工，在好友主动发起聊天后，员工在一个自然日内首次回复的时长间隔为首次回复时长，所有聊天的首次回复总时长/已回复的聊天总数即为平均首次回复时长，仅包括单聊。在该好友主动发起的聊天中，好友一开始有主动发送多条消息，则以发送的第一条消息的时间为准进行首次回复时长的计算">
                    平均首次回复时长
                    <QuestionCircleOutlined style={{ marginLeft: "4px" }} />
                  </Tooltip>
                </div>
              }
            >
              {(chartsData4 &&
                DemoColumn(
                  chartsData4,
                  { xField: "date", yField: "number" },
                  "time"
                )) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default SingleChatStatistics;
