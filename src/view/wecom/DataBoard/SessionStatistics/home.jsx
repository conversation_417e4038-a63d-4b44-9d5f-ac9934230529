/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/05 17:26
 * @LastEditTime: 2023/05/06 16:03
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DataBoard/SessionStatistics/home.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import SingleChat from './SingleChat/home';
import GroupChat from './GroupChat/home';

const { TabPane } = Tabs;

const SessionStatistics = (props) => {

  const [tabsIndex, setTabsIndex] = useState('1');

  useEffect(() => { }, []);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
  };

  return (
    <div className='SessionStatistics'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="单聊统计" key="1">
            <SingleChat />
          </TabPane>
          <TabPane tab="群聊统计" key="2">
            <GroupChat />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SessionStatistics;
