import { Descriptions, message, Modal } from 'antd';
import { apiCall } from 'common/utils';
import SysDictLabel from 'components/select/SysDictLabel';
import React, { useState } from 'react';

const FormModal = (props) => {
  const { visible = false, data = {}, onOk, onCancel } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleOK = () => {
    setConfirmLoading(true);
    const { id, state } = props.params.data;
    const data = {
      state: stateSwitchMap[state],
    };
    apiCall(`/deviceApply/update/${id}`, 'POST', data)
      .then((res) => {
        message.success('操作成功！');
        onOk?.();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };

  const handleCancel = () => {
    setConfirmLoading(false);
    onCancel?.();
  };

  const stateTitleMap = {
    APPLYING: '托管处理',
    EXITING: '退出处理',
  };

  const stateSwitchMap = {
    APPLYING: 'HOSTING',
    EXITING: 'END',
  };

  return (
    <Modal
      visible={visible}
      width={500}
      title={stateTitleMap[data?.state]}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOK}
    >
      <Descriptions title='' column={1}>
        <Descriptions.Item label='发起人'>{data?.createEmployeeName}</Descriptions.Item>
        <Descriptions.Item label='企微账号'>{data?.wecomId}</Descriptions.Item>
        <Descriptions.Item label='备注'>{data?.remark || '-'}</Descriptions.Item>
        <Descriptions.Item label='状态'>
          <SysDictLabel dataset='DeviceApplyStateEnum' dictkey={data?.state} color />
        </Descriptions.Item>
        <Descriptions.Item label='状态更新时间'>{data?.updateTime}</Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};

export default FormModal;
