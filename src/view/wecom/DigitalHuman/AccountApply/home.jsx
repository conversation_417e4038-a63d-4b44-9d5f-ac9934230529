/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/04/21 09:42
 * @LastEditTime: 2025/04/21 14:18
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountApply/home.jsx
 * @Description: '集约管理申请'
 */

import { Button, Card, Form, Input, Table, Tooltip } from 'antd';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import FilterBar from 'components/FilterBar/FilterBar';
import ListOperation from 'components/ListOperation/home';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import React, { useEffect, useRef, useState } from 'react';
import FormModal from './comps/FormModal';

const AccountApply = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
    },
    {
      title: '发起人',
      width: '160px',
      dataIndex: 'createEmployeeName',
      key: 'createEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement='topLeft' title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '企微账号',
      width: '160px',
      dataIndex: 'wecomId',
      key: 'wecomId',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement='topLeft' title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '备注',
      width: '160px',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement='topLeft'>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '操作人',
      width: '160px',
      dataIndex: 'operateEmployeeName',
      key: 'operateEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement='topLeft'>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      ellipsis: 'true',
      align: 'center',
      render: (value) => <SysDictLabel dataset='DeviceApplyStateEnum' dictkey={value} color />,
    },
    {
      title: '状态更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    // {
    //   title: '处理意见',
    //   width: '160px',
    //   dataIndex: 'opinion',
    //   key: 'opinion',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip title={value} placement='topLeft'>
    //       {value}
    //     </Tooltip>
    //   ),
    // },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      render: (value, record, index) => {
        let opts = [];
        if (record.state == 'APPLYING') {
          opts.push({ onClick: () => handleOperate(record), name: '托管处理' });
        }
        if (record.state == 'EXITING') {
          opts.push({ onClick: () => handleOperate(record), name: '退出处理' });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        selfFlag: true,
        ...query,
        ...formData,
      };
      apiCall('/deviceApply', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleOperate = (record) => {
    setFormParams({
      visible: true,
      data: record,
      onCancel: () => {
        setFormParams({ visible: false });
      },
      onOk: () => {
        setFormParams({ visible: false });
        fetchList();
      },
    });
  };

  return (
    <div className='AccountApply'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <Form.Item name='wecomId' getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder='企微账号' allowClear />
          </Form.Item>
          <Form.Item name='state'>
            <SysDictSelect placeholder='流转状态' dataset='DeviceApplyStateEnum' />
          </Form.Item>
        </Form>
        <div className='flex flex-space-between'>
          <div>
            <Button type='primary' onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey='id'
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formParams} />
    </div>
  );
};

export default AccountApply;
