/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/06 11:23
 * @LastEditTime: 2023/10/26 10:03
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountEscrow/comps/FormModal.jsx
 * @Description: '账户托管-编辑'
 */

import { Form, Input, message, Modal } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import React, { useEffect, useRef, useState } from 'react';

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [deviceData, setDeviceData] = useState({});
  useEffect(() => {
    const { visible, id, data } = props.params;
    if (visible) {
      setVisible(true);
      if (id) {
        setId(id);
        setDeviceData(data);
        let timer = setTimeout(() => {
          formRef.current &&
            formRef.current.setFieldsValue({ nickName: data.nickName });
          clearTimeout(timer);
        }, 100);
      }
    }
  }, [props]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ...formData,
      };
      apiCall(`/device/update/${id}`, 'POST', data)
        .then((res) => {
          message.success('修改成功！');
          setVisible(false);
          setConfirmLoading(false);
          setId(null);
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={500}
      title={`编辑设备（${deviceData.name}）`}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form ref={formRef} {...layout}>
        <FormItem
          label="设备备注"
          name="nickName"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="请输入设备备注(10字内)" maxLength={10} allowClear />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default FormModal;
