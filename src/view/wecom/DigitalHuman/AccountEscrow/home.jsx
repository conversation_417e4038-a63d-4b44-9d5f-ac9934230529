/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/06 11:20
 * @LastEditTime: 2023/11/21 16:22
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountEscrow/home.jsx
 * @Description: '集约运营'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Button, Card, Form, Input, Table, Tooltip, Avatar } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import FilterBar from 'components/FilterBar/FilterBar';
import FormModal from './comps/FormModal';
import SdkCloud from '../comps/SdkCloud/home';
import ListOperation from 'components/ListOperation/home';
import './home.less';

const FormItem = Form.Item;
let UnDeviceData = [];

const AccountEscrow = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [deviceData, setDeviceData] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
    },
    {
      title: '设备名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const content = (
          <div>
            <div>{record.nickName}</div>
            <span>{value}</span>
          </div>
        );
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
      },
    },
    {
      title: '登录账号',
      width: '160px',
      dataIndex: 'manageEmployeeName',
      key: 'manageEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '编码',
      width: '160px',
      dataIndex: 'code',
      key: 'code',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '上次控制时间',
      width: '160px',
      dataIndex: 'lastUseTime',
      key: 'lastUseTime',
      align: 'center',
    },
    {
      title: '上次控制员工',
      width: '200px',
      dataIndex: 'lastUseEmployeeName',
      key: 'lastUseEmployeeName',
      align: 'center',
      render: (value, record, index) => {
        const companyName = <div>{record.lastUseEmployeeDeptName}</div>;
        const title = (
          <div
            style={{
              width: '120px',
              textAlign: 'left',
              whiteSpace: 'normal',
            }}
          >
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            <Avatar
              style={{ marginRight: '6px' }}
              size={40}
              src={record.lastUseEmployeeAvatar}
            />
            {title}
          </div>
        );
        return (
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: '播流状态',
      width: '120px',
      dataIndex: 'lock',
      key: 'lock',
      align: 'center',
      render: (value, record, index) => (
        <span style={{ color: value ? '#08b855' : '#d5001c' }}>
          {value ? '播流中' : '空闲'}
        </span>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      render: (value, record, index) => {
        let opts = [];
        if (!record.lock) {
          opts.push({ onClick: () => handleOpenCloud(record, index), name: "控制" });
        }
        opts.push({ onClick: () => handleEdit(record), name: "编辑" });
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
    return () => {
      if (UnDeviceData && UnDeviceData.length) {
        const deviceIds = UnDeviceData?.map((item) => item.code).join(',');
        apiCall(`/device/unlock?codes=${deviceIds}`, 'POST')
          .then((res) => { })
          .catch((err) => {
            console.log(err);
          });
      }
    };
  }, []);

  useEffect(() => {
    UnDeviceData = deviceData;
  }, [deviceData]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        selfFlag: true,
        ...query,
        ...formData,
      };
      apiCall('/device', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id,
      data: record,
      onCancel: () => {
        setFormParams({ visible: false });
      },
      onSubmit: () => {
        setFormParams({ visible: false });
        fetchList();
      },
    });
  };

  // 打开云手机
  const handleOpenCloud = (record, index) => {
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    newDataSource[index].lock = true;
    setDataSource(newDataSource);

    const newDeviceData = JSON.parse(JSON.stringify(deviceData));
    const filterDeviceData = newDeviceData.filter((item) => item != null);
    record.x =
      filterDeviceData.length > 0
        ? filterDeviceData[filterDeviceData.length - 1].x + 50
        : 0;
    newDeviceData.push(record);
    setDeviceData(newDeviceData);
  };

  // 关闭云手机
  const handleCloseCloud = (item, index) => {
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    let itemIndex = newDataSource.findIndex((atem) => atem.id == item.id);
    newDataSource[itemIndex].lock = false;
    setDataSource(newDataSource);

    const newDeviceData = JSON.parse(JSON.stringify(deviceData));
    newDeviceData[index] = null;
    setDeviceData(newDeviceData);
  };

  return (
    <div className="AccountEscrow">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="设备名称" allowClear />
          </FormItem>
          <FormItem name="code" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="编码" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formParams} />
      <SdkCloud
        params={{
          deviceData: deviceData,
          onClose: handleCloseCloud,
          onChange: (data) => {
            // setDeviceData([...data]);
          },
        }}
      />
    </div>
  );
};

export default AccountEscrow;
