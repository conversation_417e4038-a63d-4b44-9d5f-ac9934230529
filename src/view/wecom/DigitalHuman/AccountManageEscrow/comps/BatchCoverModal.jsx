/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/12/20 15:23
 * @LastEditTime: 2023/12/26 15:28
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\AccountManageEscrow\comps\BatchCoverModal.jsx
 * @Description: '批量覆盖弹窗'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin, Button, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { saveAs } from 'file-saver';
import moment from 'moment';

const FormItem = Form.Item;

const BatchCoverModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [fileData, setFileData] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  // 下载模板
  const handleDownload = () => {
    const data = {
      fileName: '批量覆盖可见范围员工模版.xlsx',
    };
    apiCall('/file/template', 'GET', data, null, {
      isExit: true,
      title: `批量覆盖可见范围员工模版.${moment().format('YYYY-MM-DD')}.xlsx`,
    })
      .then((res) => {
        message.success('下载成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 选择文件
  const customFileRequest = (config) => {
    const File = config.file;
    setFileData([
      {
        name: File.name,
        status: 'uploading',
      },
    ]);
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    setFileData([
      {
        name: File.name,
        fileUrl: data,
        status: 'done',
      },
    ]);
    formRef.current.setFieldsValue({
      file: File.name,
    });
  };

  const onRemoveUpload = (file) => {
    setFileData([]);
    formRef.current.setFieldsValue({
      file: '',
    });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      apiCall('/device/employee/import', 'POST', fileData[0].fileUrl)
        .then((res) => {
          if (res) {
            saveAs(
              res,
              `批量覆盖可见范围员工错误数据.${moment().format(
                'YYYY-MM-DD'
              )}.xlsx`
            );
            message.error('部分数据上传失败，失败数据已下载！');
          } else {
            message.success('批量覆盖成功！');
          }
          onCancel();
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setFileData([]);
    formRef.current.setFieldsValue({
      file: '',
    });
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AccountManageEscrow-BatchCoverModal"
      visible={visible}
      title="批量覆盖"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <p className="tips">
            通过excel批量覆盖集约账户的信息，当可见范围有多个时，请用逗号分隔。
          </p>
          <FormItem
            label="选择文件"
            name="file"
            extra={
              <div>
                请选择批量覆盖的文件，格式为xls、xlsx
                <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                  下载模板
                </a>
              </div>
            }
            rules={[{ required: true, message: '请上传文件' }]}
          >
            <Upload
              name="file"
              fileList={fileData}
              customRequest={customFileRequest}
              beforeUpload={(file) => {
                const isFile = file.type.includes('application');
                if (!isFile) {
                  message.error('请上传文档！');
                }
                return isFile;
              }}
              onRemove={onRemoveUpload}
            >
              {fileData.length <= 0 ? (
                <Button icon={<UploadOutlined />}>选择文件</Button>
              ) : null}
            </Upload>
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default BatchCoverModal;
