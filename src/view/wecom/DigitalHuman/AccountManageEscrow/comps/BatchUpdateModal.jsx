/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/12/20 15:23
 * @LastEditTime: 2023/12/26 15:33
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\AccountManageEscrow\comps\BatchUpdateModal.jsx
 * @Description: '批量更新弹窗'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin } from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;

const BatchUpdateModal = (props) => {
  const { idList, dto } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ...formData,
        idList,
        dto
      };
      apiCall('/device/employee/batchUpdate', 'POST', data)
        .then((res) => {
          message.success('批量更新成功！');
          onCancel();
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AccountManageEscrow-BatchUpdateModal"
      visible={visible}
      title="批量更新可见范围员工"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <p className="tips">进行批量更新已选择设备的可见范围员工信息</p>
          <FormItem
            label="可见范围"
            name="employeeIdList"
            extra="支持选择员工"
            rules={[{ required: true, message: '请选择可见范围' }]}
          >
            <ETypeTransferModal
              title="可见范围"
              onlyEmployee
              multiple
              onChange={(value, options) => {
                // handleDeptIdChange(value, options, "EMPLOYEE");
              }}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default BatchUpdateModal;
