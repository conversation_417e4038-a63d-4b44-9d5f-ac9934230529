/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/06 11:23
 * @LastEditTime: 2023/10/24 15:04
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountManageEscrow/comps/FormModal.jsx
 * @Description: '账户管理-编辑'
 */

import { Form, Input, message, Modal, Select } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import React, { useEffect, useRef, useState } from 'react';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import {isV1} from "config";

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [deviceData, setDeviceData] = useState({});
  const [deviceGroup, setDeviceGroup] = useState([]);
  useEffect(() => {
    const { visible, id, data } = props.params;
    if (visible) {
      setVisible(true);
      getDeviceGroup();
      if (id) {
        setId(id);
        setDeviceData(data);
        let timer = setTimeout(() => {
          formRef.current && formRef.current.setFieldsValue({ ...data });
          clearTimeout(timer);
        }, 100);
      }
    }
  }, [props]);

  // 分组管理
  const getDeviceGroup = async () => {
    await apiCall('/device/deviceGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        const { records } = res;
        setDeviceGroup(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        managerFlag: true,
        ...formData,
        // chargeEmployeeId: formData.chargeEmployeeId.join(","),
        // manageEmployeeId: formData.manageEmployeeId.join(",")
      };
      apiCall(`/device/update/${id}`, 'POST', data)
        .then((res) => {
          message.success('修改成功！');
          setVisible(false);
          setConfirmLoading(false);
          setId(null);
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={500}
      title={`编辑设备（${deviceData.name}）`}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form ref={formRef} {...layout}>
        <FormItem
          label="设备名称"
          name="name"
          getValueFromEvent={(e) => removeInputEmpty(e)}
          rules={[{ required: true, message: '请输入设备名称' }]}
        >
          <Input placeholder="请输入" allowClear />
        </FormItem>
        <FormItem
          name="groupId"
          label="所属分组"
          rules={[{ required: true, message: '请选择所属分组' }]}
        >
          <Select
            placeholder="请选择分组管理"
            fieldNames={{ label: 'name', value: 'id' }}
            options={deviceGroup}
            allowClear
            showSearch
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </FormItem>
        {/* <FormItem
          name="chargeEmployeeId"
          rules={[{ required: true, message: '请选择负责人' }]}
          label="负责人"
        >
          <ETypeTransferModal title="负责人" onlyEmployee multiple={false} />
        </FormItem> */}
        {/* <FormItem
          name="manageEmployeeId"
          rules={[{ required: true, message: '请选择登录账号' }]}
          label="登录账号"
        >
          <ETypeTransferModal title="登录账号" onlyEmployee multiple={false} />
        </FormItem> */}
        {
          isV1()?
              <>
                <FormItem
                    name="employeeIdList"
                    rules={[{required: true, message: '请选择可见范围'}]}
                    label="可见范围"
                >
                  <ETypeTransferModal title="可见范围" onlyEmployee/>
                </FormItem>
              </>:
              null
        }
      </Form>
    </Modal>
  );
};

export default FormModal;
