/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/09/18 14:33
 * @LastEditTime: 2023/11/21 16:23
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountManageEscrow/comps/GroupModal.jsx
 * @Description: '集约分组管理'
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal, Tooltip, Button, Table, Input, message } from 'antd';
import { PlusOutlined, MenuOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import ListOperation from 'components/ListOperation/home';

const type = 'DraggableBodyRow';
const DraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};
const GroupModal = (props) => {
  const { visible, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [initDataSource, setInitDataSource] = useState([]);
  const [isdrag, setIsdrag] = useState(true);
  const columns = [
    {
      title: '序号',
      width: '80px',
      dataIndex: 'sort',
      align: 'center',
      render: (value, record, index) => (
        <>
          {/* <MenuOutlined style={{ marginRight: '10px' }} /> */}
          {index + 1}
        </>
      ),
    },
    {
      title: '名称',
      width: '170px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Input
              value={value}
              maxLength={20}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(dataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, '');
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入20字内"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
        </>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      const params = {
        list: data.map((item) => item.id),
      };
      apiCall('/info/microScriptScene/sort', 'POST', params)
        .then((res) => {
          message.success('修改成功！');
          setDataSource(
            update(dataSource, {
              $splice: [
                [dragIndex, 1],
                [hoverIndex, 0, dragRow],
              ],
            })
          );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource]
  );

  useEffect(() => {
    if (visible) {
      getGroupOptions();
    }
  }, [visible]);

  useEffect(() => {
    const isEdit = dataSource.some(item => item.isEdit)
    setIsdrag(!isEdit)
  }, [dataSource]);

  const getGroupOptions = () => {
    setLoading(true);
    apiCall('/device/deviceGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        const { records } = res;
        setDataSource(records);
        setInitDataSource(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = () => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource.unshift({
      name: '',
      isEdit: true,
    });
    setDataSource([...newDataSource]);
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { id, name } = record;
    if (name == '') {
      message.warning('名称不能为空！');
      return false;
    }
    setLoading(true);
    const data = {
      id: id ?? null,
      name: name,
    };
    apiCall('/device/deviceGroup', 'POST', data)
      .then((res) => {
        message.success('保存成功！');
        getGroupOptions();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/device/deviceGroup/delete/${id}`, 'POST')
      .then((res) => {
        message.success('删除成功！');
        let newDataSource = dataSource;
        newDataSource.splice(index, 1);
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index) => {
    const { id } = record;
    let newDataSource = dataSource;
    if (id) {
      newDataSource[index].isEdit = false;
      const findData = initDataSource.find(item => item.id == id);
      findData && (newDataSource[index].name = findData.name);
    } else {
      newDataSource.splice(index, 1);
    }
    setDataSource([...newDataSource]);
  };

  return (
    <Modal
      className="GroupModal-Container"
      visible={visible}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          集约账户分组
          <Button
            style={{ marginRight: '20px' }}
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd()}
          >
            新增
          </Button>
        </div>
      }
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      onCancel={() => {
        setLoading(false);
        setDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <DndProvider backend={HTML5Backend}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          scroll={{ y: 500 }}
        // components={isdrag && components}
        // onRow={isdrag ? (record, index) => ({ index, moveRow }) : null}
        />
      </DndProvider>
    </Modal>
  );
};

export default GroupModal;
