/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/11/01 10:57
 * @LastEditTime: 2023/11/03 10:46
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AccountManageEscrow/history.jsx
 * @Description: '集约账户管理-控制历史'
 */

import React, { useState, useEffect, useRef } from 'react'; import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
} from 'antd';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { qs2obj } from 'common/object';

const FormItem = Form.Item;

const AccountManageEscrowHistory = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [deviceName, setDeviceName] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '控制开始时间',
      width: '160px',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.startTime) - timeStamp(b.startTime),
    },
    {
      title: '控制结束时间',
      width: '160px',
      dataIndex: 'endTime',
      key: 'endTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.endTime) - timeStamp(b.endTime),
    },
    {
      title: '控制员工',
      width: '120px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      render: (value, record, index) => {
        const companyName = <div>{record.departmentName}</div>;
        const title = (
          <div
            style={{
              width: '120px',
              textAlign: 'left',
              whiteSpace: 'normal',
            }}
          >
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            {title}
          </div>
        );
        return (
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        );
      },
    },
  ];

  useEffect(() => {
    const { id, name } = qs2obj(props.location.search);
    setDeviceId(id);
    setDeviceName(name);
  }, []);

  useEffect(() => {
    deviceId && fetchList();
  }, [deviceId]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      formData.employeeId = formData.employeeId?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        deviceId: deviceId
      };
      apiCall('/device/deviceLog', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='AccountManageEscrowHistory-Container'>
      <Card title={`【${deviceName}】的控制历史`} extra={<Button type="primary" onClick={() => {
        props.history.go(-1);
      }}>返回</Button>} bordered={false}>
        <FilterBar bodyStyle={{ padding: 'unset' }}>
          <Form layout={'inline'} ref={formRef}>
            <FormItem name="employeeId">
              <ETypeTransferModal title="控制员工" onlyEmployee multiple={false} />
            </FormItem>
          </Form>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </FilterBar>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Card>
    </div>
  );
};

export default AccountManageEscrowHistory;
