/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/06 11:20
 * @LastEditTime: 2024/01/11 11:43
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\AccountManageEscrow\home.jsx
 * @Description: '集约账户管理'
 */

import React, {useEffect, useRef, useState} from 'react';
import {Avatar, Button, Card, Dropdown, Form, Input, Menu, message, Select, Table, Tooltip,} from 'antd';
import {removeInputEmpty} from 'common/regular';
import {apiCall} from 'common/utils';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import FormModal from './comps/FormModal';
import GroupModal from './comps/GroupModal';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SdkCloud from '../comps/SdkCloud/home';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import BatchUpdateModal from './comps/BatchUpdateModal';
import BatchCoverModal from './comps/BatchCoverModal';
import OperateModal from 'components/Modal/OperateModal/index';
import './home.less';
import {isV1, isV2} from "config";
import {WibotModal} from "components/WibotModal";

const FormItem = Form.Item;
let UnDeviceData = [];

const AccountManageEscrow = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [deviceData, setDeviceData] = useState([]);
  const [groupParams, setGroupParams] = useState({ visible: false });
  const [deviceGroup, setDeviceGroup] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchUpdateParams, setBatchUpdateParams] = useState({
    visible: false,
  });
  const [batchCoverParams, setBatchCoverParams] = useState({
    visible: false,
  });
  const [operateParams, setOperateParams] = useState({ visible: false });
  let columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
    },
    {
      title: '设备名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    // {
    //   title: '负责人',
    //   width: '160px',
    //   dataIndex: 'chargeEmployeeName',
    //   key: 'chargeEmployeeName',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => (
    //     <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
    //   ),
    // },
    {
      title: '登录账号',
      width: '160px',
      dataIndex: 'manageEmployeeName',
      key: 'manageEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '编码',
      width: '160px',
      dataIndex: 'code',
      key: 'code',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '所属分组',
      width: '160px',
      dataIndex: 'groupName',
      key: 'groupName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '可见范围',
      width: '160px',
      dataIndex: 'employeeNameList',
      key: 'employeeNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '上次控制时间',
      width: '160px',
      dataIndex: 'lastUseTime',
      key: 'lastUseTime',
      align: 'center',
    },
    {
      title: '上次控制员工',
      width: '200px',
      dataIndex: 'lastUseEmployeeName',
      key: 'lastUseEmployeeName',
      align: 'center',
      render: (value, record, index) => {
        const companyName = <div>{record.lastUseEmployeeDeptName}</div>;
        const title = (
          <div
            style={{
              width: '120px',
              textAlign: 'left',
              whiteSpace: 'normal',
            }}
          >
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            <Avatar
              style={{ marginRight: '6px' }}
              size={40}
              src={record.lastUseEmployeeAvatar}
            />
            {title}
          </div>
        );
        return (
          <Tooltip placement="topLeft" title={title}>
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: '播流状态',
      width: '120px',
      dataIndex: 'lock',
      key: 'lock',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <span style={{ color: value ? '#08b855' : '#d5001c' }}>
          {value ? '播流中' : '空闲'}
        </span>
      ),
    },
    {
      title: '状态',
      width: '120px',
      dataIndex: 'enable',
      key: 'enable',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
          <span style={{color: value ? '#08b855' : '#d5001c'}}>
          {value ? '启用' : '禁用'}
        </span>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      render: (value, record, index) => {
        let opts = [];
        if (!record.lock) {
          if (isV1()) {
            opts.push({
              onClick: () => handleOpenCloud(record, index),
              name: '控制',
            });
          }

        }
        if (isV1()) {
          opts.push({onClick: () => handleHistory(record), name: '控制历史'});
          opts.push({onClick: () => handleDelete(record), name: '删除'});
        }
        opts.push({ onClick: () => handleEdit(record), name: '编辑' });
        opts.push({onClick: () => handleToggle(record), name: record.enable ? '禁用' : '启用'});
        return <ListOperation opts={opts} />;
      },
    },
  ];

  if (isV2()) {
    let spliceKey = ['employeeNameList', 'lastUseTime', 'lastUseEmployeeName', 'lock']
    columns = columns.filter(col => {
      return !spliceKey.includes(col.key)
    })
  }

  useEffect(() => {
    fetchList();
    getDeviceGroup();
    return () => {
      if (UnDeviceData && UnDeviceData.length) {
        const deviceIds = UnDeviceData?.map((item) => item.code).join(',');
        apiCall(`/device/unlock?codes=${deviceIds}`, 'POST')
          .then((res) => { })
          .catch((err) => {
            console.log(err);
          });
      }
    };
  }, []);

  useEffect(() => {
    UnDeviceData = deviceData;
  }, [deviceData]);
  const statusOptions = [
    {
      label: '全部',
      value: null
    },
    {
      label: '启用',
      value: true,
    },
    {
      label: '禁用',
      value: false,
    }

  ]

  // 所属分组
  const getDeviceGroup = async () => {
    await apiCall('/device/deviceGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        const { records } = res;
        setDeviceGroup(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      formData.deptIdList = formData.deptIdList?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/device', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 所属分组
  const handleGroup = () => {
    setGroupParams({
      visible: true,
      onCancel: () => {
        setGroupParams({ visible: false });
        getDeviceGroup();
      },
    });
  };

  // 处理批量操作
  const handleOprate = (type) => {
    const obj = {
      ...formRef.current.getFieldsValue(),
    };

    if (type == '0') {
      const params = {
        idList: selectedRowKeys.length > 0 ? selectedRowKeys : [],
        dto: selectedRowKeys.length > 0 ? null : obj,
      };
      if (selectedRowKeys.length > 0) {
        apiCall('/device/employee/export', 'POST', params, null, {
          isExit: true,
          title: `集约账户管理信息.${moment().format('YYYY-MM-DD')}.xlsx`,
        })
          .then((res) => {
            message.success('下载成功！');
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => { });
        return false;
      }
      setOperateParams({
        visible: true,
        title: '批量下载确认',
        content: `${paginations.total > 2000
          ? `当前查询结果${paginations.total}行已超出批量操作【批量下载】的限制2000行，请调整筛选条件后再试！`
          : `即将为当前查询结果的${paginations.total}行数据进行批量操作【批量下载】，确定这样做吗？`
          }`,
        onSubmit: () => {
          if (paginations.total > 2000) {
            setOperateParams({
              visible: false,
            });
          } else {
            apiCall('/device/employee/export', 'POST', params, null, {
              isExit: true,
              title: `集约账户管理信息.${moment().format('YYYY-MM-DD')}.xlsx`,
            })
              .then((res) => {
                message.success('下载成功！');
              })
              .catch((err) => {
                console.log(err);
              })
              .finally(() => {
                setOperateParams({
                  visible: false,
                });
              });
          }
        },
        onCancel: () => {
          setOperateParams({
            visible: false,
          });
        },
      });
    } else if (type == '1') {
      if (selectedRowKeys.length > 0) {
        setBatchUpdateParams({
          visible: true,
          idList: selectedRowKeys,
          onSubmit: () => {
            fetchList();
          },
          onCancel: () => {
            setBatchUpdateParams({ visible: false });
          },
        });
        return false;
      }
      setOperateParams({
        visible: true,
        title: '批量更新确认',
        content: `${paginations.total > 2000
          ? `当前查询结果${paginations.total}行已超出批量操作【批量更新】的限制2000行，请调整筛选条件后再试！`
          : `即将为当前查询结果的${paginations.total}行数据进行批量操作【批量更新】，确定这样做吗？`
          }`,
        onSubmit: () => {
          if (paginations.total > 2000) {
            setOperateParams({
              visible: false,
            });
          } else {
            setOperateParams({
              visible: false,
            });
            setBatchUpdateParams({
              visible: true,
              dto: obj,
              idList: [],
              onSubmit: () => {
                fetchList();
              },
              onCancel: () => {
                setBatchUpdateParams({ visible: false });
              },
            });
          }
        },
        onCancel: () => {
          setOperateParams({
            visible: false,
          });
        },
      });
    } else if (type == '2') {
      setBatchCoverParams({
        visible: true,
        onSubmit: () => {
          fetchList();
        },
        onCancel: () => {
          setBatchCoverParams({ visible: false });
        },
      });
    }
  };

  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id,
      data: {
        ...record,
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
      onSubmit: () => {
        setFormParams({ visible: false });
        fetchList();
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/device/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const handleHistory = (record) => {
    const { id, name } = record;
    props.history.push({
      pathname: '/wecom/digitalAccountMultiManageEscrow/history',
      search: `?id=${id}&name=${name}`,
    });
  };

  // 打开云手机
  const handleOpenCloud = (record, index) => {
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    newDataSource[index].lock = true;
    setDataSource(newDataSource);

    const newDeviceData = JSON.parse(JSON.stringify(deviceData));
    const filterDeviceData = newDeviceData.filter((item) => item != null);
    record.x =
      filterDeviceData.length > 0
        ? filterDeviceData[filterDeviceData.length - 1].x + 50
        : 0;
    newDeviceData.push(record);
    setDeviceData(newDeviceData);
  };

  // 关闭云手机
  const handleCloseCloud = (item, index) => {
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    let itemIndex = newDataSource.findIndex((atem) => atem.id == item.id);
    newDataSource[itemIndex].lock = false;
    setDataSource(newDataSource);

    const newDeviceData = JSON.parse(JSON.stringify(deviceData));
    newDeviceData[index] = null;
    setDeviceData(newDeviceData);
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const onSelectChange = (keys, rows, info) => {
    setSelectedRowKeys(keys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleToggle = (record) => {
    WibotModal.open({
      title: '提示',
      children: `是否${record.enable ? '禁用' : '启用'}该设备？`,
      onOk: () => {
        apiCall(`/device/update/enable/${record.id}`, 'POST', {enable: !record.enable}).then(() => {
          message.success('操作成功')
          fetchList()
        })
      },
    })
  }
  return (
    <div className="AccountManageEscrow">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="设备名称" allowClear />
          </FormItem>
          <FormItem name="code" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="编码" allowClear />
          </FormItem>
          <FormItem name="groupId">
            <Select
                placeholder="所属分组"
              fieldNames={{ label: 'name', value: 'id' }}
              options={deviceGroup}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="enable">
            <Select
                placeholder="状态"
                options={statusOptions}
                allowClear
                showSearch
                filterOption={(input, option) =>
                    (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
                }
            />
          </FormItem>
          {
            isV2() ?
                null :
                <FormItem
                    name="deptIdList"
                    style={{minWidth: 'unset', maxWidth: '200px', marginRight: '0px'}}
                >
                  <ETypeTransferModal title="可见范围" mode={['dep', 'emp']}/>
                </FormItem>
          }
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            {
              isV1()?
                  <>
                    <Button
                        type="primary"
                        onClick={() => {
                          handleOprate('2');
                        }}
                    >
                      批量覆盖
                    </Button>
                    <Dropdown
                        overlay={
                          <Menu>
                            <div
                                style={{
                                  color: '#AAAAAA',
                                  padding: '5px',
                                  borderBottom: '1px solid #e5e5e5',
                                }}
                            >
                              {selectedRowKeys.length > 0 ? (
                                  <>
                                    操作已选的
                                    <span style={{color: '#fd0100'}}>
                          &nbsp;{selectedRowKeys.length}&nbsp;
                        </span>
                                    行数据
                                  </>
                              ) : (
                                  <>
                                    操作查询结果的
                                    <span style={{color: '#fd0100'}}>
                          &nbsp;{paginations.total || 0}&nbsp;
                        </span>
                                    行数据
                                  </>
                              )}
                            </div>
                            <Menu.Item
                                key="0"
                                onClick={() => {
                                  handleOprate('0');
                                }}
                                style={{textAlign: 'center'}}
                            >
                              批量下载
                            </Menu.Item>
                            <Menu.Item
                                key="1"
                                onClick={() => {
                                  handleOprate('1');
                                }}
                                style={{textAlign: 'center'}}
                            >
                              批量更新
                            </Menu.Item>

                            {/* <Menu.Item
                    key="2"
                    onClick={() => {
                      handleOprate("2");
                    }}
                    style={{ textAlign: "center" }}
                  >
                    批量覆盖
                  </Menu.Item> */}
                          </Menu>
                        }
                        placement="bottomLeft"
                        arrow
                    >
                      <Button type="primary">批量操作</Button>
                    </Dropdown>
                  </>:
                  null
            }
            <Button type="primary" onClick={() => handleGroup()}>
              所属分组
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <FormModal params={formParams} />
      <GroupModal params={groupParams} />
      <BatchUpdateModal params={batchUpdateParams} />
      <BatchCoverModal params={batchCoverParams} />
      <OperateModal params={operateParams} />
      <SdkCloud
        params={{
          deviceData: deviceData,
          onClose: handleCloseCloud,
          onChange: (data) => {
            // setDeviceData([...data]);
          },
        }}
      />
    </div>
  );
};

export default AccountManageEscrow;
