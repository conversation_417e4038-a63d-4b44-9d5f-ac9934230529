/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/11/14 11:44
 * @LastEditTime: 2025/07/04 11:12
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/CollectMessageModal.jsx
 * @Description: '收件箱-收藏消息'
 */

import { DeleteOutlined, SendOutlined } from "@ant-design/icons"
import { Empty, Input, message, Modal } from "antd"
import { debounce, throttle } from "common/fn"
import { highLightText } from "common/regular"
import { apiCall } from "common/utils" 
import OperateModal from 'components/Modal/OperateModal/index';
import WibotEditorView from "components/WibotEditorView/home"
import { useEffect, useState } from "react"
import Draggable from "react-draggable"
import "./CollectMessageModal.less"

const CollectMessageModal = (props) => {
  const { onAddMessage } = props
  const { visible, onCancel } = props.params
  const [messageList, setMessageList] = useState([])
  // 存储搜索值
  const [searchValue, setSearchValue] = useState("")
  const [matchedOptions, setMatchedOptions] = useState([])
  const [operateParams, setOperateParams] = useState({ visible: false })

  useEffect(() => {
    if (visible) {
      fetchEmployeeMessage()
    } else {
      setSearchValue("")
    }
  }, [visible])

  const fetchEmployeeMessage = () => {
    apiCall("/agentSeat/employeeMsg", "GET")
      .then((res) => {
        setMessageList(res)
        setMatchedOptions(res)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 防抖搜索查询会话内容
  const handleSearch = (e) => {
    const newMessageList = JSON.parse(JSON.stringify(messageList))
    if (e.target.value) {
      setSearchValue(e.target.value)
      const list = newMessageList.filter((option) =>
        option.content.includes(e.target.value)
      )
      setMatchedOptions(list)
    } else {
      setMatchedOptions(newMessageList)
      setSearchValue("")
    }
  }

  // 删除确认
  const handleDelete = (value) => {
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: "确认取消收藏该消息吗？",
      onSubmit: () => {
        apiCall(`/agentSeat/employeeMsg/delete/${value.id}`, "POST")
          .then((res) => {
            fetchEmployeeMessage()
          })
          .catch((err) => {
            console.log(err)
          })
        setOperateParams({
          visible: false,
        })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  return (
    <Modal
      className="AgentSeat-CollectMessageModal"
      title="收藏消息"
      style={{ cursor: "move" }}
      bodyStyle={{ cursor: "default" }}
      visible={visible}
      destroyOnClose
      onCancel={() => {
        onCancel()
      }}
      footer={null}
      modalRender={(modal) => (
        <Draggable handle=".ant-modal-header">
          <div>{modal}</div>
        </Draggable>
      )}
    >
      <Input
        allowClear
        placeholder="请输入关键词"
        onChange={throttle(function (e) {
          handleSearch(e)
        }, 500)}
      />
      <div className="messageBox custom-scrollbar">
        {matchedOptions.length ? (
          matchedOptions.map((item, index) => (
            <div className="messageItem" key={index}>
              <WibotEditorView
                style={{ flex: 1 }}
                html={highLightText(item.content, searchValue)}
              />
              <div className="messageIcons">
                <SendOutlined
                  className="sendIcon"
                  onClick={debounce(function () {
                    message.success("已追加到消息框！")
                    onAddMessage(item)
                  }, 500)}
                />
                <DeleteOutlined
                  onClick={() => {
                    handleDelete(item)
                  }}
                />
              </div>
            </div>
          ))
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
      <OperateModal params={operateParams} />
    </Modal>
  )
}

export default CollectMessageModal;
