/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/06/28 17:30
 * @LastEditTime: 2024/07/04 15:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/Sider/comps/TicketDrawer.jsx
 * @Description: '工单抽屉'
 */

import React, {useEffect, useState} from 'react';
import {Drawer, Table, Tooltip} from 'antd';
import {apiCall} from 'common/utils';
import {timeStamp} from "common/date";
import SysDictLabel from 'components/select/SysDictLabel';
import {versionFnMap} from "config";

const TicketDrawer = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, id = null, title = `我的${workOrderTitle}`, placement = 'right', onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: `${workOrderTitle}编号`,
      width: "160px",
      dataIndex: "no",
      key: "no",
      align: "center",
    },
    {
      title: "客户昵称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TICKET_STATE" dictkey={value} />
      ),
    },
    {
      title: "紧急程度",
      width: "160px",
      dataIndex: "urgencyType",
      key: "urgencyType",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TICKET_URGENCY_TYPE" dictkey={value} />
      ),
    },
    {
      title: `${workOrderTitle}内容`,
      width: '160px',
      dataIndex: 'content',
      key: 'content',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "提出时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];

  useEffect(() => {
    if (visible) {
      fetchList()
    } else {
      setLoading(false);
      setPaginations({ current: 1, pageSize: 10 })
      setDataSource([])
    }
  }, [visible, id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    apiCall("/agentSeat/ticket/current", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <Drawer
      className='AgentSeat-Sider-TicketDrawer'
      bodyStyle={{ padding: 'unset' }}
      title={title}
      placement={placement}
      width={1000}
      open={visible}
      destroyOnClose
      onClose={() => {
        onCancel?.();
      }}
    >
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        scroll={{ x: 800 }}
        pagination={paginations}
        onChange={onChangeTable}
      />
    </Drawer>
  );
};

export default TicketDrawer;
