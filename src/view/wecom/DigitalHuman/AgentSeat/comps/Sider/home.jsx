/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/06/28 15:20
 * @LastEditTime: 2024/07/01 09:26
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/Sider/home.jsx
 * @Description: ''
 */

import React, {useEffect, useState} from 'react';
import {Avatar} from 'antd';
import {FileTextOutlined, LeftCircleOutlined,} from "@ant-design/icons";
import TicketDrawer from "./comps/TicketDrawer"
import "./home.less";
import {versionFnMap} from "config";

const Sider = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle;
  const [ticketParams, setTicketParams] = useState({ visible: false });
  const [list, setList] = useState([
    {
      title: `我的${workOrderTitle}`,
      type: "ticket",
      icon: <FileTextOutlined />
    },
  ]);

  useEffect(() => { }, []);

  const handleMenu = (type) => {
    switch (type) {
      case 'ticket':
        setTicketParams({
          visible: true,
          onCancel: () => {
            setTicketParams({ visible: false });
          },
        })
        break;
    }
  };

  return (
    <div className='AgentSeat-Sider'>
      <div className="more">
        更
        <LeftCircleOutlined />
        多
      </div>
      <ul className='menuBar custom-scrollbar'>
        {
          list.map((item, index) => <li key={index} onClick={() => handleMenu(item.type)}>
            <Avatar style={{ color: '#fff', backgroundColor: '#5a76c7' }} size={30} icon={item.icon} />
            <span>{item.title}</span>
          </li>)
        }
      </ul>
      <TicketDrawer params={ticketParams} />
    </div>
  );
};

export default Sider;
