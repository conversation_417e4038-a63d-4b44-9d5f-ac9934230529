.AgentSeat-Sider {
  display: flex;
  position: fixed;
  top: 200px;
  right: -66px;
  transition: right 0.3s ease-in-out;
  z-index: 1;

  &:hover {
    right: 0px;
  }

  .more {
    width: 26px;
    height: 200px;
    border-radius: 60px 0 0 60px;
    background-color: #b1b1b1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    line-height: 50px;
    cursor: pointer;

    .anticon {
      font-size: 20px;
    }
  }

  .menuBar {
    width: 66px;
    height: 200px;
    background-color: #b1b1b1;
    list-style-type: none;
    margin: 0;
    padding: 10px 0 10px 10px;

    li {
      cursor: pointer;
      list-style: none;
      margin: 0;
      padding: 0;
      color: #fff;
      display: flex;
      flex-direction: column;
      text-align: center;
      margin-top: 10px;

      &:first-child {
        margin: 0;
      }

      .ant-avatar {
        margin: 0 auto 4px;

        .anticon {
          font-size: 18px;
        }
      }

      span {
        font-size: 10px;
      }
    }
  }
}