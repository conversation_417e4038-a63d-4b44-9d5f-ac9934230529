/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/14 14:27
 * @LastEditTime: 2024/09/23 10:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/SwitchSessionModal.jsx
 * @Description: '转接对话弹窗'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Form, message, Button, Input, TreeSelect, Popconfirm, Switch } from 'antd';
import { apiCall } from 'common/utils';
import Draggable from 'react-draggable';

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const SwitchSessionModal = (props) => {
  const { visible = false, sessionClientInfo = null, x = 380 } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [bigClassifyMenu, setBigClassifyMenu] = useState([]);
  const [smallClassifyMenu, setSmallClassifyMenu] = useState([]);
  const [switch1, setSwitch1] = useState(false);
  const [agentGroupAllOptions, setAgentGroupAllOptions] = useState([]);

  useEffect(() => {
    if (visible) {
      getBigClassifyMenu()
      getAgentGroupAllOptions()
      if (sessionClientInfo) {
        formForm.setFieldsValue({
          customerName: sessionClientInfo.customerName,
          employeeName: sessionClientInfo.employeeName,
        });
      }
    } else {
      setConfirmLoading(false);
      setSwitch1(false);
      setBigClassifyMenu([]);
      setSmallClassifyMenu([]);
      formForm?.resetFields();
    }
  }, [visible]);

  // 获取资讯分类-大类
  const getBigClassifyMenu = () => {
    const data = {};
    apiCall("/inbox/sessionBusinessType/tree", "GET", data)
      .then((res) => {
        setBigClassifyMenu(res.map((item => ({
          ...item,
          children: []
        }))));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取资讯分类-小类
  const getSmallClassifyMenu = (parentId) => {
    const data = {
      parentId: parentId
    };
    apiCall("/inbox/sessionBusinessType", "GET", data)
      .then((res) => {
        setSmallClassifyMenu(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取坐席组
  const getAgentGroupAllOptions = () => {
    const data = {
      signIn: true
    };
    apiCall("/agentSeat/agentGroup/all/options", "GET", data)
      .then((res) => {
        setAgentGroupAllOptions(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        customerId: sessionClientInfo.customerId,
        employeeId: sessionClientInfo.employeeId,
        conversationId: sessionClientInfo.conversationId,
        ...formData,
        endFlag: false,
      };
      apiCall('/agentSeat/conversationSummary', 'POST', data)
        .then((res) => {
          message.success('转接成功！');
          props.params?.onSubmit?.({
            summaryFlag: formData.summaryFlag
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AgentSeat-SwitchSessionModal"
      title="转接对话"
      style={{ cursor: 'move' }}
      bodyStyle={{ cursor: 'default' }}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      footer={
        <div>
          <Popconfirm
            title="取消后已添加的转接内容将丢失，确定这样做吗？"
            onConfirm={onCancel}
            okText="确 定"
            cancelText="取 消"
          >
            <Button type="primary">
              取消
            </Button>
          </Popconfirm>
          <Button type="primary" onClick={onOk} loading={confirmLoading}>
            确定
          </Button>
        </div>
      }
      modalRender={(modal) => (
        <Draggable defaultPosition={{ x: x, y: 0 }} handle=".ant-modal-header">
          <div>{modal}</div>
        </Draggable>
      )}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item
          name="groupOrAgentId"
          label="转接目标"
          rules={[{ required: true, message: "请选择转接目标" }]}
        >
          <TreeSelect
            allowClear
            showArrow
            showSearch
            treeData={agentGroupAllOptions}
            placeholder="请选择转接目标"
            fieldNames={{ label: 'title', value: 'key', children: 'children' }}
            treeNodeFilterProp="title"
          />
        </Form.Item>

        <Form.Item
          name="remark"
          label="转接备注"
        >
          <TextArea
            placeholder="请输入转接备注（200字）"
            allowClear
            showCount
            maxLength={200}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </Form.Item>

        <Form.Item
          name="summaryFlag"
          label="记录小结"
          valuePropName="checked"
        >
          <Switch
            onChange={(checked) => {
              setSwitch1(checked)
            }}
          />
        </Form.Item>

        {
          switch1 && <>
            <Form.Item
              name="sessionBusinessTypeParentId"
              label="业务大类"
              rules={[{ required: true, message: "请选择业务类型大类" }]}
            >
              <TreeSelect
                allowClear
                showArrow
                showSearch
                treeData={bigClassifyMenu}
                placeholder="请选择业务类型大类"
                fieldNames={{ label: 'title', value: 'key', children: 'children' }}
                treeNodeFilterProp="title"
                onChange={(value, label) => {
                  formForm.setFieldValue('sessionBusinessTypeId', null)
                  if (!value) {
                    setSmallClassifyMenu([])
                  } else {
                    getSmallClassifyMenu(value)
                  }
                }}
              />
            </Form.Item>

            {smallClassifyMenu.length ? <Form.Item
              name="sessionBusinessTypeId"
              label="业务小类"
              rules={[{ required: true, message: "请选择业务类型小类" }]}
            >
              <TreeSelect
                allowClear
                showArrow
                showSearch
                treeData={smallClassifyMenu}
                placeholder="请选择业务类型小类"
                fieldNames={{ label: 'name', value: 'id' }}
                treeNodeFilterProp="name"
              />
            </Form.Item> : ''}

            <Form.Item
              name="content"
              label="小结内容"
              rules={[{ required: true, message: '请输入小结内容（500字）' }]}
            >
              <TextArea
                placeholder="请输入小结内容（500字）"
                allowClear
                showCount
                maxLength={500}
                autoSize={{ minRows: 4, maxRows: 7 }}
              />
            </Form.Item>

          </> || null
        }
      </Form>
    </Modal>
  );
};

export default SwitchSessionModal;
