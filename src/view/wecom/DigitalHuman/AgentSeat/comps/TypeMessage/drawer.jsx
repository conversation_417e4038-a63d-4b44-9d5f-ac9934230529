/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 14:40
 * @LastEditTime: 2025/07/11 17:47
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/TypeMessage/drawer.jsx
 * @Description: '抽屉-类型消息'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import { Button, Card, Collapse, Drawer, Empty, Space, Spin } from "antd"
import { apiCall } from "common/utils"
import { highLightText } from "common/regular"
import { qs2obj } from "common/object"
import TypeMessage from "./home"
import "./drawer.less"
import { CaretRightOutlined } from "@ant-design/icons"
import WibotEditorView from "components/WibotEditorView/home"

const { Panel } = Collapse
const TypeMessageDrawer = (props) => {
  const {
    visible = false,
    conversationId = null,
    title = "标题",
    placement = "right",
    onCancel,
    qualityFlag = false,
    formRef = null,
  } = props.params
  const [loading, setLoading] = useState(false)
  const [nowTitle, setNowTitle] = useState("标题")
  const [messageList, setMessageList] = useState([])
  const [hitList, setHitList] = useState([])
  const [msgId, setMsgId] = useState("")
  const [qualityIdList, setQualityIdList] = useState([])
  const [qualityId, setQualityId] = useState(null)
  // 存储语音对象
  const [amrObj, setAmrObj] = useState(null)
  const [dialogList, setDialogList] = useState([])
  const [dialogActiveKey, setDialogActiveKey] = useState([])
  const historyMsg = useRef([])

  const [msgLoading, setMsgLoading] = useState(false)
  const isFirst = useRef(false)

  function setupScrollToBottom(el) {
    const element = document.querySelector(el)
    if (!element) return
    // 初始滚动
    scrollToBottom(element)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          // 新增节点，滚动到底部
          scrollToBottom(element)
        }
      })
    })
    observer.observe(element, {
      childList: false,
      subtree: false,
    })
  }
  function scrollToBottom(el) {
    const element = document.querySelector(el)
    element.scrollTo({
      top: element.scrollHeight,
      behavior: "smooth", // 可选：平滑滚动
    })
    // console.log(`[timer]: `, timer)
    // clearTimeout(timer)
    // timer = setTimeout(() => {
    //   if (element.scrollHeight - element.scrollTop > 100) {
    //     scrollToBottom(el)
    //   }
    // }, 1000)
  }

  function getMsgDateByConversionId(conversationId) {
    const data = {
      conversationId: conversationId || props.params.conversationId,
    }
    apiCall("/agentSeat/msg/listConversationBase", "GET", data)
      .then((res) => {
        setDialogList(res)
        const lastIndex2 = res.length - 2
        const lastIndex1 = res.length - 1
        setDialogActiveKey([data.conversationId])
        // isFirst.current = true
      })
      .finally((_) => {
        setTimeout(() => {
          // isFirst.current = false
        }, 5000)
      })
  }

  // 获取某个对话的消息列表
  const fetchSessionMessage = (params = {}) => {
    console.log(`fetchSessionMessage [params]: `, params)

    const { conversationId } = params
    const data = {
      conversationId: conversationId,
    }
    const messageList = dialogList.find(
      (c) => c.conversationId.toString() === conversationId.toString()
    )?.messageList
    console.log(`[messageList]: `, messageList)
    if (messageList?.length) {
      return
    }
    setMsgLoading(true)
    apiCall("/agentSeat/msg/listMsg", "GET", data)
      .then((res) => {
        console.log(`[res]: `, res)
        const newDialogList = JSON.parse(JSON.stringify(dialogList))
        newDialogList.forEach((item) => {
          if (String(item.conversationId) === String(conversationId)) {
            item.messageList = res
          }
          console.log(`[item]: `, item)
        })
        setDialogList(newDialogList)
        setTimeout(() => {
          console.log(`#dialogPanel${conversationId}`)
          document
            .querySelector(`#dialogPanel${conversationId}`)
            .scrollIntoView()
        }, 2000)
      })
      .finally((_) => {
        setMsgLoading(false)
        // if (isFirst.current) {
        //   isFirst.current = false
        //   setTimeout(() => {
        //     // scrollToBottom(".ant-drawer-body")
        //   }, 1000)
        // }
      })
  }

  useEffect(() => {
    if (!dialogActiveKey?.length) {
      return
    }
    console.log(`[dialogActiveKey]: `, dialogActiveKey)
    fetchSessionMessage({
      conversationId: dialogActiveKey[dialogActiveKey.length - 1],
    })
  }, [dialogActiveKey])

  useEffect(async () => {
    if (visible) {
      getMsgDateByConversionId()
      setNowTitle(title)
      const { qualityId } = props.params
      if (qualityId && conversationId) {
        await getListMsg(conversationId)
      }
      if (qualityFlag && qualityId) {
        getQualityIdList()
        await getQualityResult(qualityId)
      }
    } else {
      setLoading(false)
      setQualityId(null)
      setMessageList([])
      setHitList([])
      setMsgId("")
      setQualityIdList([])
    }
  }, [visible])

  const getListMsg = async (conversationId) => {
    setLoading(true)
    setMessageList([])
    const data = {
      conversationId,
    }
    await apiCall("/agentSeat/msg/listMsg", "GET", data)
      .then((res) => {
        setMessageList(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取质检结果
  const getQualityResult = async (qualityId) => {
    setLoading(true)
    setHitList([])
    await apiCall(`/agentSeat/qualityControlTaskResult/${qualityId}`, "GET")
      .then((res) => {
        const { hitDetail } = res
        setQualityId(qualityId)
        setHitList(hitDetail)
        setMsgId(hitDetail[0].hitItemList[0].msgId)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取对话列表
  const getQualityIdList = async () => {
    const { id } = qs2obj(props.location.search)
    await formRef.validateFields().then(async (formData) => {
      formData.hitRuleNameList = formData.hitRuleNameList?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      const data = {
        paged: false,
        taskId: id,
        ...formData,
      }
      await apiCall(
        `/agentSeat/qualityControlTaskResult/getTaskResultIdList`,
        "GET",
        data
      )
        .then((res) => {
          setQualityIdList(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    })
  }

  // 消息定位
  const onLocate = (item) => {
    const { msgId } = item
    setMsgId(msgId)
  }

  useEffect(() => {
    if (msgId && !loading) {
      // 初始化
      let locateBoxList1 = document.querySelectorAll(".wi-messages-wrap")
      let locateBoxList2 = document.querySelectorAll(".wi-type-system")
      for (let index = 0; index < locateBoxList1.length; index++) {
        const item = locateBoxList1[index]
        item.style.background = "unset"
      }
      for (let index = 0; index < locateBoxList2.length; index++) {
        const item = locateBoxList2[index]
        item.style.background = "unset"
      }
      let locateBox = document.querySelector(`.msgId-${msgId}`)
      if (!locateBox) {
        setTimeout(() => {
          locateBox = document.querySelector(`.msgId-${msgId}`)
          if (locateBox) {
            locateBox.style.background = "#f5cb97"
            document.querySelector(
              ".TypeMessage-Drawer-Container .ant-drawer-body"
            ).scrollTop = locateBox.offsetTop - locateBox.clientHeight - 200
          }
        }, 1500)
      } else {
        locateBox = document.querySelector(`.msgId-${msgId}`)
        if (locateBox) {
          locateBox.style.background = "#f5cb97"
          document.querySelector(
            ".TypeMessage-Drawer-Container .ant-drawer-body"
          ).scrollTop = locateBox.offsetTop - locateBox.clientHeight - 200
        }
      }
    }
  }, [msgId, loading])

  // 更新质检结果
  const updateQualityResult = async (qualityId) => {
    setLoading(true)

    await apiCall(`/agentSeat/qualityControlTaskResult/${qualityId}`, "GET")
      .then(async (res) => {
        const { id, conversationId, conversationVO } = res
        fetchSessionMessage({ conversationId })
        getMsgDateByConversionId(conversationId)
        await getListMsg(conversationId)
        await getQualityResult(id)
        setNowTitle(
          `对话详情（${conversationVO.customerName}-${conversationVO.employeeName}）`
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const prevDisabled = () => {
    const findIndex = qualityIdList.findIndex((item) => item == qualityId)
    return findIndex === 0 ? true : false
  }

  const nextDisabled = () => {
    const findIndex = qualityIdList.findIndex((item) => item == qualityId)
    return findIndex === qualityIdList.length - 1 ? true : false
  }

  return (
    <Drawer
      className="TypeMessage-Drawer-Container"
      title={nowTitle}
      placement={placement}
      width={550}
      open={visible}
      destroyOnClose
      mask={false}
      push={{ distance: 378 }}
      onClose={() => {
        onCancel?.()
      }}
      footer={
        qualityFlag ? (
          <Space>
            <Button
              type="primary"
              loading={loading}
              disabled={prevDisabled()}
              onClick={() => {
                const findIndex = qualityIdList.findIndex(
                  (item) => item == qualityId
                )
                const prevIndex = findIndex - 1
                updateQualityResult(qualityIdList[prevIndex])
              }}
            >
              上一条
            </Button>
            <Button
              type="primary"
              loading={loading}
              disabled={nextDisabled()}
              onClick={() => {
                const findIndex = qualityIdList.findIndex(
                  (item) => item == qualityId
                )
                const prevIndex = findIndex + 1
                console.log(`[qualityIdList]: `, qualityIdList)
                updateQualityResult(qualityIdList[prevIndex])
              }}
            >
              下一条
            </Button>
          </Space>
        ) : null
      }
    >
      <Spin spinning={loading}>
        {/* { */}
        {/*   messageList.length ? <> */}
        {/*     {messageList.map((item, index) => ( */}
        {/*       <TypeMessage */}
        {/*         key={index} */}
        {/*         item={item} */}
        {/*         placement="bottomLeft" */}
        {/*         preview={true} */}
        {/*         amrObj={amrObj} */}
        {/*         onVoice={(data) => { */}
        {/*           setAmrObj({ ...data }) */}
        {/*         }} */}
        {/*       /> */}
        {/*     ))} */}
        {/*   </> : (<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />) */}
        {/* } */}
        <Collapse
          className="wi-messages-dialog-list"
          expandIcon={({ isActive }) => (
            <CaretRightOutlined rotate={isActive ? 90 : 0} />
          )}
          ghost
          destroyInactivePanel
          defaultActiveKey={dialogActiveKey}
          activeKey={dialogActiveKey}
          onChange={(key) => {
            historyMsg.current.push(...key)
            setDialogActiveKey(key)
          }}
        >
          {dialogList.map((item, index) => (
            <Panel
              id={`dialogPanel${item.conversationId}`}
              header={`对话 ${item.time}`}
              key={item.conversationId}
            >
              <Spin spinning={msgLoading} />
              {item.messageList?.length ? (
                <>
                  {item.messageList.map((atem, andex) => (
                    <TypeMessage
                      key={andex}
                      item={atem}
                      preview={true}
                      onTogether={null}
                      amrObj={amrObj}
                      onVoice={(data) => {
                        setAmrObj({ ...data })
                      }}
                    />
                  ))}
                </>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Panel>
          ))}
        </Collapse>
      </Spin>

      {qualityFlag ? (
        <Drawer
          className="HitDetail-Drawer-Container"
          title="命中规则详情"
          placement="right"
          open={visible}
          mask={false}
          closable={false}
        >
          <Spin spinning={loading}>
            {hitList.length ? (
              <Space direction="vertical" size={14} style={{ display: "flex" }}>
                {hitList.map((item, index) => (
                  <Card
                    key={index}
                    size="small"
                    title={item.ruleName}
                    extra={<a>共{item.hitItemList.length}处</a>}
                  >
                    <p style={{ color: "#6e6e6e" }}>命中消息</p>
                    <ul>
                      <Space direction="vertical" style={{ display: "flex" }}>
                        {item.hitItemList.map((atem, andex) => (
                          <li
                            className={`${
                              msgId == atem.msgId ? "selected" : ""
                            }`}
                            key={andex}
                            onClick={() => onLocate(atem)}
                          >
                            <WibotEditorView
                              html={`${andex + 1}.${
                                item.type == "AUTO_CLOSE" ||
                                item.type == "AUTO_DISTRIBUTION"
                                  ? `${highLightText(
                                      `${atem.content} ${atem.sendTime}`,
                                      `${atem.content} ${atem.sendTime}`
                                    )}`
                                  : `${highLightText(
                                      atem.content,
                                      item.keywordList
                                    )}`
                              }`}
                            />
                          </li>
                        ))}
                      </Space>
                    </ul>
                  </Card>
                ))}
              </Space>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </Spin>
        </Drawer>
      ) : null}
    </Drawer>
  )
}

export default withRouter(TypeMessageDrawer)
