/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/18 11:46
 * @LastEditTime: 2025/08/19 14:27
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/TypeMessage/home.jsx
 * @Description: '类型消息'
 */

import { useEffect, useState } from "react"
import {
  Avatar,
  Col,
  Divider,
  Dropdown,
  Empty,
  Form,
  Image,
  Input,
  Menu,
  message,
  Popover,
  Row,
  Typography,
} from 'antd';
import {
  FileTextOutlined,
  FlagOutlined,
  ReadOutlined,
  ReloadOutlined,
  TagOutlined,
  RedoOutlined,
  createFromIconfontCN,
} from "@ant-design/icons"
import moment from "moment"
import LinkCard from "components/LinkCard/home"
import AppletCard from "components/AppletCard/home"
import BenzAMRRecorder from "benz-amr-recorder"
import { saveAs } from "file-saver"
import { apiCall } from "common/utils"
import { throttle } from "common/fn"
import AppStore from "stores/AppStore"
import "./home.less"
import { isV1, versionFnMap } from "config"
import customerAvatar from "assets/images/avatar/客户头像.png"
import employeeAvatar from "assets/images/avatar/客服头像.png"
import { WibotModal } from "components/WibotModal"
import { FileHOC } from "components/FileHOC/FileHOC"

const { Paragraph } = Typography
const { TextArea } = Input
const IconFont = createFromIconfontCN({})

const TypeMessage = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const {
    item = {},
    placement = "rightTop",
    preview = false,
    amrObj = null,
    onVoice,
    onTogether,
    updateSstContent,
  } = props
  const [formForm] = Form.useForm()
  const [errStatus, setErrStatus] = useState(false)
  const [loading, setLoading] = useState(false)
  const [sttContent, setSttContent] = useState(null)

  useEffect(
    () =>
      // 页面销毁
      () => {
        amrObj?.amr.stop() // 停止语音播放
      },
    [amrObj]
  )

  const handleCollect = () => {
    const data = {
      msgId: item.msgId,
      content: item.messageMap.content,
    }
    apiCall("/agentSeat/employeeMsg", "POST", data)
      .then((res) => {
        message.success("收藏成功！")
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const [childrenOfMenus, setChildrenOfMenus] = useState([])
  const [switchTypeHtmlDom, setSwitchTypeHtmlDom] = useState("")

  const TTS = ({ children }) => {
    const isTts = AppStore.state.isEnableTTS
    if (isTts) {
      return children || null
    }
    return null // 或者返回其他占位符，例如 <></>
  }
  useEffect(() => {
    if (item.type === "VOICE") {
      setSttContent(item.sttContent || null)
    }
  }, [])

  const handleRetry = (item) => {
    // setLoading(true)
    setSttContent("转文字中...")
    apiCall("/agentSeat/msgVoiceText/byMsgId?msgId=" + item.msgId, "GET")
      .then((res) => {
        setSttContent(res?.content || "转文字处理中，请稍后再试")
        // updateSstContent({
        //   conversationId: item.conversationId,
        //   msgId: item.msgId,
        //   content: res?.content || "转文字处理中，请稍后再试",
        // })
      })
      .catch(() => {
        setSttContent("转换失败，请稍后再试")
      })
      .finally(() => {
        // setTimeout(() => {
        //   setLoading(false)
        // }, 1000)
      })
  }

  const scrollToRevokeMsg = (msgId) => {
    document.querySelector('#wi-body').querySelector(`#m_${msgId}`).scrollIntoView({
      behavior: "smooth",
      block: "end",
    })
  }

  const handleSensitive = () => {
    WibotModal.open({
      title: "提示",
      children: "是否将此消息标记为敏感？",
      onOk: () => {
        console.log("onok")
        apiCall("/riskControl/riskControlLog/manualAdd", "POST", {
          msgId: item.msgId,
          conversationId: item.conversationId,
        }).then((rse) => {
          message.info("操作成功")
        })
      },
      onCancel() {
        console.log("cancel")
      },
    })
  }
  useEffect(() => {
    let timer
    let childOfMenus = []
    const switchTypeHtmlDom = () => {
      let htmlDom = ""
      switch (item.type) {
        case "TEXT":
          if (item.senderType === "CUSTOMER" && isV1()) {
            childOfMenus.push(
              <Menu.Item
                icon={<ReadOutlined />}
                onClick={throttle(function () {
                  onTogether(item)
                }, 1000)}
              >
                随行
              </Menu.Item>,
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          if (item.senderType !== "CUSTOMER") {
            childOfMenus.push(
              <Menu.Item
                icon={<TagOutlined />}
                onClick={() => {
                  handleCollect()
                }}
              >
                收藏
              </Menu.Item>
            )
          }
          htmlDom = (
            <div
              className={`wi-type-text ${
                item.senderType === "EMPLOYEE" ? "sender" : ""
              }`}
            >
              {item.messageMap?.content || ""}
            </div>
          )
          break
        case "LINK":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          htmlDom = (
            <LinkCard
              isLink={false}
              data={{
                ...item.messageMap,
                image: item.messageMap?.imageUrl,
              }}
            />
          )
          break
        case "WE_APP":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          htmlDom = <AppletCard data={item.messageMap || {}} />
          break
        case "IMAGE": {
          isV1() &&
            item.senderType !== "EMPLOYEE" &&
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          htmlDom = (
            <>
              {errStatus ? (
                <Empty
                  className="wi-type-empty"
                  image={<ReloadOutlined />}
                  description="重新加载"
                  onClick={() => {
                    setErrStatus(false)
                  }}
                />
              ) : (
                <FileHOC src={item.fileUrl}>
                  {(url) => (
                    <Image
                      style={{ objectFit: "contain" }}
                      src={url}
                      fallback={url}
                      height={100}
                      onError={(e) => {
                        console.log("onError [e]: ", e)
                        setErrStatus(true)
                      }}
                    />
                  )}
                </FileHOC>
              )}
            </>
          )
          break
        }
        case "VIDEO":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          htmlDom = (
            <>
              {errStatus ? (
                <Empty
                  className="wi-type-empty"
                  image={<ReloadOutlined />}
                  description="重新加载"
                  onClick={() => {
                    setErrStatus(false)
                  }}
                />
              ) : (
                <FileHOC src={item.fileUrl}>
                  {(url) => (
                    <video
                      style={{ height: "200px" }}
                      src={url}
                      preload="auto"
                      controls
                      onError={(e) => {
                        setErrStatus(true)
                      }}
                    />
                  )}
                </FileHOC>
              )}
            </>
          )
          break
        case "FILE":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          htmlDom = (
            <FileHOC src={item.fileUrl}>
              {(url) => (
                <a
                  className="wi-type-file"
                  onClick={() => {
                    saveAs(url, item.fileName)
                  }}
                >
                  <Row justify="space-between" align={"middle"}>
                    <Col span={19}>
                      <Paragraph strong ellipsis={{ rows: 2 }}>
                        {item.fileName}
                      </Paragraph>
                      <span style={{ color: "#999999" }}>{item.fileSize}</span>
                    </Col>
                    <Col>
                      <FileTextOutlined style={{ fontSize: "35px" }} />
                    </Col>
                  </Row>
                </a>
              )}
            </FileHOC>
          )
          break
        case "VOICE":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          if (AppStore.state.isEnableTTS) {
            childOfMenus.push(
              <Menu.Item
                icon={<IconFont type="icon-exchange" />}
                onClick={() => handleRetry(item)}
              >
                转文字
              </Menu.Item>
            )
          }
          htmlDom = (
            <div className={`wi-type-voice-box`}>
              <FileHOC src={item.fileUrl}>
                {(url) => (
                  <div class="wi-type-voice-wrapper">
                    <div>
                      <div
                        id={`wi-type-voice${item.msgId}`}
                        className={`wi-type-voice ${
                          item.senderType == "EMPLOYEE" ? "sender" : ""
                        }`}
                        onClick={() => {
                          item.fileUrl = url
                          handleVoice(item)
                        }}
                      >
                        <div className="voice1"></div>
                        <div className="voice2"></div>
                        <div className="voice3"></div>
                        {item.messageMap?.playLength && (
                          <div className="time">
                            {item.messageMap.playLength}"
                          </div>
                        )}
                      </div>
                      {/* <TTS>
                      {
                        sttContent === null &&
                        (
                          <span className="retry" onClick={() => handleRetry(item)}>
                            <RedoOutlined spin={loading} />
                          </span>
                        )
                      }
                    </TTS> */}
                    </div>
                  </div>
                )}
              </FileHOC>
              <TTS>
                {sttContent && (
                  <div
                    class={`wi-type-voice-text wi-type-voice-text-${item.msgId}`}
                  >
                    {sttContent}
                  </div>
                )}
              </TTS>
            </div>
          )
          break
        case "EMOTION":
          if (item.senderType !== "EMPLOYEE" && isV1()) {
            childOfMenus.push(
              <Menu.Item icon={<FlagOutlined />} onClick={handleSensitive}>
                敏感
              </Menu.Item>
            )
          }
          htmlDom = (
            <>
              {errStatus ? (
                <Empty
                  className="wi-type-empty"
                  image={<ReloadOutlined />}
                  description="重新加载"
                  onClick={() => {
                    setErrStatus(false)
                  }}
                />
              ) : (
                <FileHOC src={item.fileUrl}>
                  {(url) => (
                    <Image
                      style={{ objectFit: "contain" }}
                      src={url}
                      height={100}
                      onError={(e) => {
                        setErrStatus(true)
                      }}
                    />
                  )}
                </FileHOC>
              )}
            </>
          )
          break
        case "CUSTOM_SYSTEM":
          const {
            employeeName,
            content,
            type,
            remain_min,
            distributionRemark,
            agentGroupName,
            agentName,
            fromAgentName,
            collaborateRemark,
            invitedAgentNameList,
            remark,
          } = item.messageMap
          let typeContent = ""
          switch (type) {
            case "START_TICKET":
              typeContent = (
                <>
                  {employeeName}&nbsp;主动发起
                  <Popover
                    destroyTooltipOnHide
                    arrowPointAtCenter
                    trigger="click"
                    placement={placement}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={
                      <Form form={formForm}>
                        <Form.Item
                          label={`${workOrderTitle}内容`}
                          name="content"
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                        <Form.Item label="处理意见" name="describe">
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                      </Form>
                    }
                  >
                    <span
                      style={{ cursor: "pointer", color: "#0088ff" }}
                      onClick={() => {
                        handleTackle()
                      }}
                    >
                      &nbsp;{workOrderTitle}处理&nbsp;
                    </span>
                  </Popover>
                  对话
                </>
              )
              break
            case "COUNTDOWN_DISTRIBUTION":
              typeContent = `对话将在${remain_min}分钟后重新分配`
              break
            case "COUNTDOWN_CLOSE":
              typeContent = `对话将在${remain_min}分钟后自动结束`
              break
            case "COLLABORATE_COUNTDOWN_CLOSE":
              typeContent = `协同对话将在${remain_min}分钟后自动结束`
              break
            case "DISTRIBUTION":
              typeContent = (
                <>
                  {fromAgentName}
                  <Popover
                    destroyTooltipOnHide
                    arrowPointAtCenter
                    trigger="click"
                    placement={placement}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={
                      <Form form={formForm}>
                        <Form.Item
                          label="转接备注"
                          name="content"
                          initialValue={distributionRemark}
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                      </Form>
                    }
                  >
                    <span style={{ cursor: "pointer", color: "#0088ff" }}>
                      &nbsp;转接对话&nbsp;
                    </span>
                  </Popover>
                  给&nbsp;{agentGroupName || ""}
                  {agentName ? "-" + agentName : ""}
                </>
              )
              break
            case "START_COLLABORATE":
              typeContent = (
                <>
                  {agentGroupName}-{agentName}&nbsp;发起
                  <Popover
                    destroyTooltipOnHide
                    arrowPointAtCenter
                    trigger="click"
                    placement={placement}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={
                      <Form form={formForm}>
                        <Form.Item
                          label="协同邀请目标"
                          name="invitedAgentNameList"
                          initialValue={invitedAgentNameList.join("、")}
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                        <Form.Item
                          label="协同备注"
                          name="content"
                          initialValue={collaborateRemark}
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                      </Form>
                    }
                  >
                    <span style={{ cursor: "pointer", color: "#0088ff" }}>
                      &nbsp;协同邀请&nbsp;
                    </span>
                  </Popover>
                </>
              )
              break
            case "INITIATIVE":
              typeContent = (
                <>
                  <Popover
                    destroyTooltipOnHide
                    arrowPointAtCenter
                    trigger="click"
                    placement={placement}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={
                      <Form form={formForm}>
                        <Form.Item
                          label="对话备注"
                          name="content"
                          initialValue={remark}
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                      </Form>
                    }
                  >
                    <span style={{ cursor: "pointer", color: "#0088ff" }}>
                      {content}&nbsp;
                    </span>
                  </Popover>
                </>
              )
              break
            case "MOMENT_TOUCH":
              typeContent = (
                <>
                  {employeeName}&nbsp;主动发起
                  <Popover
                    destroyTooltipOnHide
                    arrowPointAtCenter
                    trigger="click"
                    placement={placement}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={
                      <Form form={formForm}>
                        <Form.Item
                          label="跟进意见"
                          name="remark"
                          initialValue={remark}
                        >
                          <TextArea bordered={false} readOnly autoSize />
                        </Form.Item>
                      </Form>
                    }
                  >
                    <span style={{ cursor: "pointer", color: "#0088ff" }}>
                      &nbsp;朋友圈触客对话&nbsp;
                    </span>
                  </Popover>
                </>
              )
              break
            case "REVOKE":
              typeContent = (
                <>
                    <span style={{ cursor: "pointer", color: "#0088ff" }} onClick={() => scrollToRevokeMsg(item?.messageMap?.msgId)}>
                      &nbsp;{item.senderType === 'CUSTOMER' ? item.customerName: item.agentEmployeeName}撤回了一条消息&nbsp;
                    </span>
                </>
              )
              break
            default:
              typeContent = content
              break
          }
          htmlDom = (
            <Divider
              className={`wi-type-system ${"msgId-" + item.msgId}`}
              plain
            >
              {typeContent}
              &nbsp;{formatDateTime(item.sendTime)}
            </Divider>
          )
          break
        case "SESSION_DISAGREE":
          htmlDom = (
            <div
              className={`wi-type-text ${
                item.senderType == "EMPLOYEE" ? "sender" : ""
              }`}
              style={{ color: "#ff4347" }}
            >
              对方不同意存档会话内容，你将无法继续提供服务
            </div>
          )
          break
        case "SESSION_AGREE":
          htmlDom = (
            <div
              className={`wi-type-text ${
                item.senderType == "EMPLOYEE" ? "sender" : ""
              }`}
              style={{ color: "#49bb18" }}
            >
              对方同意存档会话内容，你可以继续提供服务
            </div>
          )
          break
      }
      if (item.type == "CUSTOM_SYSTEM") {
        return <div style={{ margin: "0 auto" }}>{htmlDom}</div>
      } else {
        return (
          <div
            className={`wi-messages-wrap ${"msgId-" + item.msgId} ${
              item.senderType == "EMPLOYEE" ? " sender" : ""
            } ${preview ? "preview" : ""}`}
          >
            {item.senderType == "EMPLOYEE" ? (
              <Avatar
                className="avatar-wrap"
                src={
                  <FileHOC src={item.agentEmployeeAvatar || employeeAvatar}>
                    {(url) => (
                      <Image
                        style={{ objectFit: "cover" }}
                        src={url}
                        alt={item.agentEmployeeName}
                        fallback={
                          versionFnMap.system_ui().avatar ? employeeAvatar : ""
                        }
                      />
                    )}
                  </FileHOC>
                }
              />
            ) : (
              <Avatar
                className="avatar-wrap"
                src={
                  <FileHOC src={item.customerAvatar}>
                    {(url) => (
                      <Image
                        style={{ objectFit: "cover" }}
                        src={url}
                        alt={item.customerName}
                        fallback={
                          versionFnMap.system_ui().avatar ? customerAvatar : ""
                        }
                      />
                    )}
                  </FileHOC>
                }
              />
            )}

            <div className="message-wrap">
              <p
                className={`createTime ${
                  item.senderType == "EMPLOYEE" ? " sender" : ""
                }`}
              >
                {formatDateTime(item.sendTime)}
                &nbsp;
                {item.senderType == "EMPLOYEE" && item.agentEmployeeName}
              </p>
              {htmlDom}
              {
                item.revoked && <div class="revoke">(已撤回)</div>
              }
            </div>
          </div>
        )
      }
    }
    timer = setTimeout(() => {
      setChildrenOfMenus(childOfMenus)
      setSwitchTypeHtmlDom(switchTypeHtmlDom())
    }, 1000)
    return () => {
      clearTimeout(timer)
    }
  }, [item, errStatus, sttContent, loading])

  // 点出处理工单
  const handleTackle = () => {
    apiCall(`/agentSeat/ticket/link/${item.messageMap.ticketId}`, "GET")
      .then((res) => {
        formForm.setFieldsValue({
          content: res.ticket?.content,
          describe: res.processTicketFollow?.content,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const handleVoice = (data) => {
    const activeVoice = document.getElementById(`wi-type-voice${data.msgId}`)
    if (amrObj) {
      if (amrObj.msgId == data.msgId) {
        if (amrObj.amr.isPlaying()) {
          console.log("播放着")
          activeVoice.classList.remove("pressed")
          amrObj.amr.pause()
        } else {
          console.log("暂停了")
          activeVoice.classList.add("pressed")
          amrObj.amr.play()
        }
        return false
      } else {
        const activeVoices = document.querySelectorAll(".wi-type-voice")
        activeVoices.forEach((item) => {
          item.classList.remove("pressed")
        })
        amrObj.amr.pause()
      }
    }
    data.amr = new BenzAMRRecorder()
    data.amr.initWithUrl(data.fileUrl).then(function () {
      activeVoice.classList.add("pressed")
      data.amr.play()
    })
    data.amr.onEnded(function () {
      activeVoice.classList.remove("pressed")
      console.log("播放完毕")
    })
    onVoice({ ...data })
  }

  const formatDateTime = (time) =>
    preview
      ? moment(time).format("YYYY-MM-DD HH:mm:ss")
      : moment(time).format("YYYY/MM/DD") == moment().format("YYYY/MM/DD")
      ? moment(time).format("HH:mm:ss")
      : moment(time).format("YYYY/MM/DD HH:mm:ss")

  return (
    <div
      className={`msg-item-box ${
        item.senderType === "EMPLOYEE" ? "user" : "customer"
      } ${item.type === "CUSTOM_SYSTEM" ? "system" : ""}`}
    >
      {childrenOfMenus.length ? (
        <Dropdown
          placement={item.senderType === "EMPLOYEE" ? "bottom" : "bottom"}
          arrow={!preview}
          overlayStyle={{ minWidth: "92px" }}
          overlay={
            preview ? (
              <div />
            ) : (
              <Menu selectable={false}>{childrenOfMenus}</Menu>
            )
          }
        >
          <div id={"m_" + item.msgId}>{switchTypeHtmlDom}</div>
        </Dropdown>
      ) : (
        <div id={"m_" + item.msgId}>{switchTypeHtmlDom}</div>
      )}
    </div>
  )
}

export default TypeMessage;
