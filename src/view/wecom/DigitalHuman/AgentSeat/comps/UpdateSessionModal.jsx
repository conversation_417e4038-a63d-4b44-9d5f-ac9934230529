/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/13 17:21
 * @LastEditTime: 2024/11/14 17:30
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/UpdateSessionModal.jsx
 * @Description: '更新会话弹窗'
 */

import React, { useEffect, useRef, useState } from "react"
import {Button, Form, Radio, Input, message, Modal, Popconfirm, TreeSelect} from 'antd';
import {apiCall} from 'common/utils';
import Draggable from 'react-draggable';
import AppStore from 'stores/AppStore';
import { isV1, isV2, versionFnMap } from "config"

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

let title = '';
let closeDescribe = '取消后已添加的小结内容将丢失，确定这样做吗？';
let apiUrl = '';
let messageText = '';

const UpdateSessionModal = (props) => {
  const { visible = false, sessionClientInfo = null, x = 380 } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [bigClassifyMenu, setBigClassifyMenu] = useState([]);
  const [smallClassifyMenu, setSmallClassifyMenu] = useState([]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const [isRequired, setIsRequired]  =useState(true)

  useEffect(() => {
    if (visible) {
      getBigClassifyMenu()
      if (sessionClientInfo) {
        const { customerName, employeeName, conversationState, conversationId, proposeFlag,
          conversationCollaborationState } = sessionClientInfo;
        console.log(`[sessionClientInfo]: `, sessionClientInfo);
        formForm.setFieldsValue({
          customerName: customerName,
          employeeName: employeeName,
        });
        switch (conversationState) {
          case 'PROCESSING':
            title = '结束对话';
            apiUrl = '/agentSeat/conversationSummary';
            messageText = `结束成功！`;
            if (versionFnMap.agentSeat_endSession_content()) {
              getQrySummary(conversationId)
            }
            break;
          case (proposeFlag === true && conversationCollaborationState === 'CONFIRMED' && conversationState === 'COLLABORATING') && conversationState:
            title = '索回对话';
            apiUrl = '/agentSeat/conversationSummary/collaboration/recallCollaboration';
            messageText = `操作成功！`;
            setIsRequired(false)
            break;
          case 'COLLABORATING':
            title = '完成协同';
            apiUrl = '/agentSeat/conversationSummary/collaboration/finish';
            messageText = `操作成功！`;
            break;
          case 'END':
            title = '修改对话小结';
            getByConversationId();
            apiUrl = `/agentSeat/conversationSummary/updateByConversationId/${conversationId}`;
            messageText = `修改成功！`;
            break;
        }
      }
    } else {
      setConfirmLoading(false);
      setBigClassifyMenu([]);
      setSmallClassifyMenu([]);
      formForm?.resetFields();
    }
  }, [visible]);

  // 获取资讯分类-大类
  const getBigClassifyMenu = () => {
    const data = {};
    apiCall("/inbox/sessionBusinessType/tree", "GET", data)
      .then((res) => {
        setBigClassifyMenu(res.map((item => ({
          ...item,
          children: []
        }))));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取资讯分类-小类
  const getSmallClassifyMenu = (parentId) => {
    const data = {
      parentId: parentId
    };
    apiCall("/inbox/sessionBusinessType", "GET", data)
      .then((res) => {
        setSmallClassifyMenu(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };
  const [endFlag, setEndFlag] = useState(false)
  // 通过对话id获取对话小结
  const getByConversationId = () => {
    apiCall(`/agentSeat/conversationSummary/byConversationId/${sessionClientInfo.conversationId}`, "GET")
      .then((res) => {
        const { sessionBusinessTypeParentId, sessionBusinessTypeId, content, solvedFlag } = res;
        setEndFlag(res.endFlag)
        formForm.setFieldsValue({
          solvedFlag,
          sessionBusinessTypeParentId: sessionBusinessTypeParentId,
          sessionBusinessTypeId: sessionBusinessTypeId,
          content: content,
        });
        sessionBusinessTypeParentId && getSmallClassifyMenu(sessionBusinessTypeParentId)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 通过对话id获取对话小结
  const getQrySummary = (conversationId) => {
    const data = {
      conversationId: conversationId
    };
    apiCall("/icbc_sz/knowledge/qrySummary", "GET", data)
      .then((res) => {
        formForm.setFieldsValue({
          content: res.answer.substring(0, 500),
        });
        if (isV2()) {
          apiCall(`/icbc_sz/knowledge/actionCollectV2`, "POST", {
            sessionId: conversationId,
            actionType: 11,
            content: res.answer,
          })
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      const { customerId, employeeId, conversationId, conversationState } = sessionClientInfo;
      setConfirmLoading(true);
      const data = {
        customerId: customerId,
        employeeId: employeeId,
        conversationId: conversationId,
        ...formData,
        summaryFlag: true,
        endFlag: true,
      };
      apiCall(apiUrl, 'POST', data)
        .then(async (res) => {
          const { sessionBusinessTypeParentName, sessionBusinessTypeName } = res;
          message.success(messageText);
          props.params?.onSubmit?.({
            content: `${sessionBusinessTypeParentName}-${sessionBusinessTypeName}`,
            text: formData.content
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const isShowResolvedFlag = () => {
    // 已结束的对话并且是当前用户结束的才显示
    const processing = sessionClientInfo?.conversationState === 'PROCESSING'
    const done = sessionClientInfo?.conversationState === 'END' && endFlag
    return processing || done
  }

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AgentSeat-UpdateSessionModal"
      title={title}
      style={{ cursor: 'move' }}
      bodyStyle={{ cursor: 'default' }}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      footer={
        <div>
          <Popconfirm
            title={closeDescribe}
            onConfirm={onCancel}
            okText="确 定"
            cancelText="取 消"
          >
            <Button type="primary">
              取消
            </Button>
          </Popconfirm>
          <Button type="primary" onClick={onOk} loading={confirmLoading}>
            确定
          </Button>
        </div>
      }
      modalRender={(modal) => (
        <Draggable defaultPosition={{ x: x, y: 0 }} handle=".ant-modal-header">
          <div>{modal}</div>
        </Draggable>
      )}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>
        {
          isV1() && isShowResolvedFlag() ?
            <Form.Item
              name="solvedFlag"
              label="客户问题"
              rules={[{ required: isRequired, message: "请选择解决类型" }]}
              initialValue={true}
            >
              <Radio.Group>
                <Radio value={true}>已解决</Radio>
                <Radio value={false}>未解决</Radio>
              </Radio.Group>
            </Form.Item>:
            null
        }
        <Form.Item
          name="sessionBusinessTypeParentId"
          label="业务大类"
          rules={[{ required: isRequired ? true : false, message: "请选择业务类型大类" }]}
        >
          <TreeSelect
            allowClear
            showArrow
            showSearch
            treeCheckable={false}
            treeData={bigClassifyMenu}
            placeholder="请选择业务类型大类"
            fieldNames={{ label: 'title', value: 'key', children: 'children' }}
            treeNodeFilterProp="title"
            onChange={(value, label) => {
              formForm.setFieldValue('sessionBusinessTypeId', null)
              if (!value) {
                setSmallClassifyMenu([])
              } else {
                getSmallClassifyMenu(value)
              }
            }}
          />
        </Form.Item>

        {smallClassifyMenu.length ? <Form.Item
          name="sessionBusinessTypeId"
          label="业务小类"
          rules={[{ required: isRequired ? true : false, message: "请选择业务类型小类" }]}
        >
          <TreeSelect
            allowClear
            showArrow
            showSearch
            treeData={smallClassifyMenu}
            placeholder="请选择业务类型小类"
            fieldNames={{ label: 'name', value: 'id' }}
            treeNodeFilterProp="name"
          />
        </Form.Item> : ''}

        <Form.Item
          name="content"
          label="小结内容"
          rules={[{ required: isRequired ? true : false, message: '请输入小结内容（500字）' }]}
        >
          <TextArea
            placeholder="请输入小结内容（500字）"
            allowClear
            showCount
            maxLength={500}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateSessionModal;
