/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/18 14:05
 * @LastEditTime: 2024/09/06 11:41
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/comps/WorkOrderModal.jsx
 * @Description: '创建工单弹窗'
 */

import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal, Radio,} from 'antd';
import {apiCall} from 'common/utils';
import SysDictSelect from 'components/select/SysDictSelect';
import Draggable from 'react-draggable';
import {versionFnMap} from "config";

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const WorkOrderModal = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, sessionClientInfo = null, x = 380 } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (sessionClientInfo) {
        formForm.setFieldsValue({
          customerName: sessionClientInfo.customerName,
          employeeName: sessionClientInfo.employeeName,
        });
      }
    } else {
      setConfirmLoading(false);
      formForm?.resetFields();
    }
  }, [visible]);

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        conversationId: sessionClientInfo.conversationId,
        ...formData,
      };
      apiCall('/agentSeat/ticket', 'POST', data)
        .then((res) => {
          message.success('创建成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = (e) => {
    console.log(e, 'handleCancel');

    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AgentSeat-WorkOrderModal"
      title={`创建${workOrderTitle}`}
      style={{ cursor: 'move' }}
      bodyStyle={{ cursor: 'default' }}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      okText="保存"
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      modalRender={(modal) => (
        <Draggable defaultPosition={{ x: x, y: 0 }} handle=".ant-modal-header">
          <div>{modal}</div>
        </Draggable>
      )}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item name="urgencyType" label="紧急程度" required initialValue={'NORMAL'}>
          <Radio.Group>
            <Radio value="NORMAL"> 一般 </Radio>
            <Radio value="EMERGENCY"> 紧急 </Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item name="type" label={`${workOrderTitle}类型`} rules={[{ required: true, message: `请选择${workOrderTitle}类型` }]}>
          <SysDictSelect dataset="TICKET_TYPE" placeholder={`请选择${workOrderTitle}类型`} />
        </Form.Item>

        <Form.Item
          name="content"
          label={`${workOrderTitle}内容`}
          rules={[{ required: true, message: `请输入${workOrderTitle}内容（200字）` }]}
        >
          <TextArea
            placeholder={`请输入${workOrderTitle}}内容（200字）`}
            allowClear
            showCount
            maxLength={200}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WorkOrderModal;
