/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/06/13 09:54
 * @LastEditTime: 2025/08/26 09:33
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeat/home.jsx
 * @Description: '坐席工作台'
 */

import { openFileSelector } from "@/utils/selectFile"
import {
  CaretRightOutlined,
  FileTextOutlined,
  FolderOutlined,
  MessageOutlined,
  PoweroffOutlined,
  SwapOutlined,
  UsergroupAddOutlined
} from "@ant-design/icons"
import {
  Avatar,
  Badge,
  Button,
  Card,
  Collapse,
  Divider,
  Empty,
  Form,
  Image,
  Input,
  List,
  message,
  Modal,
  notification,
  Popconfirm,
  Select,
  Space,
  Spin,
  Statistic,
  Tabs,
  Tooltip,
  Typography
} from "antd"
import customerAvatar from "assets/images/avatar/客户头像.png"
import systemAvatar from "assets/images/avatar/托管企微账号头像.png"
import { getLastSuccessfulConditionIndex } from "common/array"
import { secondsToTime, timeStamp } from "common/date"
import { debounce, throttle, useDebounce } from "common/fn"
import { useLatest } from "common/hooks"
import { transferProtocol } from "common/regular"
import { scrollToBottom } from "common/scroll"
import { apiCall } from "common/utils"
import { FileHOC } from "components/FileHOC/FileHOC"
import MaterialModal from "components/Modal/MaterialModal/home"
import SysDictLabel from "components/select/SysDictLabel"
import WibotEmoji from "components/WibotEmoji/home"
import { WibotModal } from "components/WibotModal"
import { isV1, isV2 } from "config"
import { getApiUrl, getSideBarBaseUrlOfH5, versionFnMap } from "config/index"
import Cookie from "js-cookie"
import moment from "moment"
import React, { useCallback, useEffect, useRef, useState } from "react"
import AppStore from "stores/AppStore"
import CollaborativeInviteModal from "./comps/CollaborativeInviteModal"
import CollectMessageModal from "./comps/CollectMessageModal"
import Sider from "./comps/Sider/home"
import SwitchSessionModal from "./comps/SwitchSessionModal"
import TypeMessage from "./comps/TypeMessage/home"
import UpdateSessionModal from "./comps/UpdateSessionModal"
import WorkOrderModal from "./comps/WorkOrderModal"
import "./home.less"

const { Search } = Input;
const { TextArea } = Input
const { Text, Paragraph } = Typography
const { TabPane } = Tabs
const { Panel } = Collapse
const { Countdown } = Statistic

let timeInit = null
let timeoutWs = null
let timerErrorWs = null
let sessionLockReconnect = false
let timeAudio1 = null
let timeAudio2 = null
let isScrollTo = true
let wsCeiling = 0
let wsObj = {}

const AgentSeat = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const chatFormRef = useRef()
  const wiBodyRef = useRef()
  const textAreaRef = useRef()
  const sessionWsRef = useRef()
  // 全局loading
  const [globalLoading, setGlobalLoading] = useState(true)
  // ws-----------
  const [initWsUrl, setInitWsUrl] = useState(null)
  const [sessionWs, setSessionWs] = useState(null)
  // 坐席状态选项
  const [seatStateOptions, setSeatStateOptions] = useState([
    {
      value: "SIGN_IN",
      label: (
        <Badge
          status="success"
          text={versionFnMap.system_ui().agentSeat_stateName[0]}
        />
      ),
      seconds: 0,
    },
    {
      value: "REST",
      label: <Badge status="warning" text="小休" />,
      seconds: 0,
    },
    {
      value: "SIGN_OUT",
      label: (
        <Badge
          status="default"
          text={versionFnMap.system_ui().agentSeat_stateName[1]}
        />
      ),
    },
  ])
  // 坐席信息-默认签出状态
  const [seatInfo, setSeatInfo] = useState({ state: "SIGN_OUT" })
  // 会话列表loading
  const [sessionListLoading, setSessionListLoading] = useState(false)
  // 当前会话列表
  const [currentSessionList, setCurrentSessionList] = useState([])
  // 协同中会话列表
  const [collaborationSessionList, setCollaborationSessionList] = useState([])
  // 已处理会话列表
  const [endSessionList, setEndSessionList] = useState([])
  // 已处理分页参数
  const [endPaginations, setEndPaginations] = useState({
    current: 1,
    pageSize: 10,
  })
  // 会话客户信息
  const [sessionClientInfo, setSessionClientInfo] = useState(null)
  // 会话内容loading
  const [chatLoading, setChatLoading] = useState(false)
  // 对话列表
  const [dialogList, setDialogList] = useState([])
  // 当前激活 对话面板的 key
  const [dialogActiveKey, setDialogActiveKey] = useState([])
  const latest_dialogActiveKey = useLatest(dialogActiveKey)
  // 发送按钮状态
  const [sendBtnStatus, setSendBtnStatus] = useState(true)
  // 监听是否有新消息
  const [listenSendMsg, setListenSendMsg] = useState(null)
  // 资源库
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  })
  // 收藏消息弹窗
  const [collectMessageParams, setCollectMessageParams] = useState({
    visible: false,
  })
  // 更新对话弹窗
  const [updateSessionParams, setUpdateSessionParams] = useState({
    visible: false,
  })
  // 转接对话弹窗
  const [switchSessionParams, setSwitchSessionParams] = useState({
    visible: false,
  })
  // 协同邀请对话弹窗
  const [collaborativeInviteParams, setCollaborativeInviteParams] = useState({
    visible: false,
  })
  // 创建工单弹窗
  const [workOrderParams, setWorkOrderParams] = useState({ visible: false })
  // 存储语音对象
  const [amrObj, setAmrObj] = useState(null)
  // 客户员工状态
  const [customerEmployeeState, setCustomerEmployeeState] = useState(null)
  // 快捷回复文案列表
  const [quickReplyList, setQuickReplyList] = useState([])
  // 基础域名地址
  const [baseUrl, setBaseUrl] = useState(
    process.env.NODE_ENV === "development"
      ? "http://localhost:9021"
      : getSideBarBaseUrlOfH5()
  )
  // 右侧标签页
  const [tabs, setTabs] = useState([])
  // 右侧标签页索引
  const [tabIndex, setTabIndex] = useState("")
  const moduleVersionMap = AppStore.state.User.moduleVersionMap
  const agentSeat_title =
    moduleVersionMap[`agentSeat_title`] === "v2" ? "服务列表" : "坐席工作台"

  // 初始化加载
  useEffect(() => {
    // notification的全局配置方法，在调用前提前配置，全局一次生效。
    notification.config({
      duration: 0,
      maxCount: 2,
    })
    timeInit = setTimeout(() => {
      fetchSeatInfo().then(() => {
        fetchWsBackendUrl()
      })
      clearTimeout(timeInit)
    }, 1000)
    // 页面销毁
    return () => {
      clearTimeout(timeInit)
      sessionWsRef.current?.close()
      heartCheck.reset()
      clearTimeout(timeoutWs)
      clearTimeout(timerErrorWs)
      timeoutWs = null
      timerErrorWs = null
      sessionLockReconnect = false
      wsCeiling = 0
      clearInterval(timeAudio1)
      clearInterval(timeAudio2)
      notification.destroy()
      Modal.destroyAll()
    }
  }, [])

  useEffect(() => {
    window.addEventListener("message", onMessage)
    return () => {
      window.removeEventListener("message", onMessage)
    }
  }, [baseUrl])

  const onMessage = (e) => {
    // 检查消息来源-忽略不可信的消息
    if (e.origin != baseUrl && e.data.operationType === undefined) return false
    const { data } = e
    const { operationType, conversationId, selected, text } = data
    if (
      sessionClientInfo &&
      sessionClientInfo.conversationId !== conversationId
    ) {
      return false
    }
    switch (operationType) {
      case "onLocate":
        isScrollTo = false
        let locateBox = document.querySelector(
          `#dialogPanel${conversationId} .ant-collapse-header`
        )
        wiBodyRef.current.scrollTop =
          locateBox.offsetTop - locateBox.clientHeight - 12 // 滚动条位置 = 当前消息偏移量-当前消息高度
        // 展开对话面板
        const newKey = JSON.parse(
          JSON.stringify(latest_dialogActiveKey.current)
        )
        if (!newKey.includes(conversationId)) {
          newKey.push(conversationId)
          setDialogActiveKey(newKey)
        }
        break
      case "onSendResources":
        setChatLoading(true)
        const newSelected = selected.map((item, index) => {
          switch (item.type) {
            case "copyWriter":
              item.content = item.copyWriter
              break
            case "pageArticle":
              item.imageUrl = (item.images && item.images[0]) || ""
              item.image = (item.images && item.images[0]) || ""
              item.fileId = item.images
              item.url = item.transitUrl || item.url
              break
            case "Picture":
              item.imageUrl = (item.fileId && item.fileId[0]) || ""
              break
            case "Article":
              item.imageUrl = (item.images && item.images[0]) || ""
              item.image = (item.images && item.images[0]) || ""
              item.fileId = item.images
              item.url = item.transitUrl || item.url
              break
            case "Video":
              item.videoUrl = (item.videos && item.videos[0]) || ""
              item.videos = item.videos || []
              item.fileId = item.videos
              item.url = item.transitUrl || item.url
              break
            case "Poster":
              item.imageUrl = (item.fileId && item.fileId[0]) || ""
              break
            case "MINI_PROGRAM": // 小程序
              item.fileId = item.miniProgram && [item.miniProgram.fileId]
              item.url = item.miniProgram?.page || item.miniProgram?.url
              item.appId = item.miniProgram?.appId
              break
            case "FORM": // 表单
              item.image = item.shareImage || item.image
              item.title = item.shareTitle
              item.description = item.shareDescription
              item.imageUrl = item.image || ""
              item.url = item.transitUrl || item.url
              item.fileId = [item.image]
              break
            case "Product":
              item.imageUrl = (item.images && item.images[0]) || ""
              item.fileId = item.images
              item.url = item.transitUrl || item.url
              break
          }
          return { ...item }
        })
        apiCall(
          `/agentSeat/msg/send?conversationId=${conversationId}`,
          "POST",
          newSelected
        )
          .then((res) => {
            message.warning("信息来源自会话存档，展示会有延迟！")
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setChatLoading(false)
          })
        break
      case "onAddMessage":
        onAddMessage(text)
        break
    }
  }

  // 重置全局弹窗
  const resetPopup = () => {
    setMaterialModalParams({ visible: false })
    setCollectMessageParams({ visible: false })
    setUpdateSessionParams({ visible: false })
    setSwitchSessionParams({ visible: false })
    setWorkOrderParams({ visible: false })
    setCollaborativeInviteParams({ visible: false })
  }

  // 监听ws会话消息内容标记变化
  useEffect(() => {
    if (listenSendMsg) {
      let {
        inboxType,
        conversationId,
        readFlag,
        agentState,
        senderType,
        sendTime,
        snowflakeId,
        customerName,
        type,
        conversationState,
        proposeFlag,
        agentCollaborationState,
        conversationCollaborationState,
      } = listenSendMsg
      const newCurrentSessionList = JSON.parse(
        JSON.stringify(currentSessionList)
      )
      const newCollaborationSessionList = JSON.parse(
        JSON.stringify(collaborationSessionList)
      )
      conversationId && console.log(listenSendMsg, "listenSendMsg")
      // 消息确认
      if (snowflakeId) {
        sessionWs?.send?.(
          JSON.stringify({
            snowflakeId: snowflakeId,
            type: "ACK",
          })
        )
      }
      switch (inboxType) {
        case "READ_EVENT":
          setCurrentSessionList((prevState) => {
            return prevState.map((item) => {
              if (item.conversationId == conversationId) {
                item.readFlag = true
              }
              return item
            })
          })
          break
        case "MESSAGE": // 新会话、消息
          // 新消息语音提示
          let currentHour = new Date().getHours() // 获取当前时间的小时数
          if (senderType == "CUSTOMER" && currentHour >= 0 && currentHour < 6) {
            // 当前时间在指定范围内
            clearInterval(timeAudio1)
            clearInterval(timeAudio2)
            let sign = 1
            timeAudio1 = setInterval(() => {
              if (sign == 1) {
                document.title = "🔴 您有一条新消息"
                sign = 0
              } else {
                document.title = "⚪ 您有一条新消息"
                sign = 1
              }
            }, 500)
            notification.info({
              message: "收到新消息！",
              description: (
                <>
                  <audio
                    type="audio/mpeg"
                    src={"audio/msgTip.mp3"}
                    autoPlay
                    loop
                  />
                </>
              ),
              onClose: () => {
                clearInterval(timeAudio1)
                timeAudio2 = setInterval(() => {
                  document.title = versionFnMap.system_ui().system_title
                  clearInterval(timeAudio2)
                }, 500)
              },
            })
          }
          // 如果在当前会话收到消息
          if (sessionClientInfo?.conversationId == conversationId) {
            senderType == "CUSTOMER" &&
              type == "TEXT" &&
              useDebounceTogetherInfo(listenSendMsg)
            const newDialogList = JSON.parse(JSON.stringify(dialogList))
            for (let index = 0; index < newDialogList.length; index++) {
              const item = newDialogList[index]
              if (item.conversationId == conversationId && item.messageList) {
                const isEven = (item) =>
                  timeStamp(sendTime) > timeStamp(item.sendTime)
                const lastEvenIndex = getLastSuccessfulConditionIndex(
                  item.messageList,
                  isEven
                )
                // 重复消息判重
                if (
                  newDialogList[index].messageList?.some(
                    (item) => item.snowflakeId == snowflakeId
                  )
                ) {
                  return false
                }
                // 只有最新的消息才回到底部
                if (item.messageList?.length == lastEvenIndex + 1) {
                  let timer = setTimeout(() => {
                    scrollToBottom("wi-body")
                    clearTimeout(timer)
                  }, 1000)
                }
                newDialogList[index].messageList?.splice(lastEvenIndex + 1, 0, {
                  ...listenSendMsg,
                })
                setDialogList(newDialogList)
                // 新消息设置为已读
                readFlag = true
                sessionWs?.send?.(
                  JSON.stringify({
                    conversationId: conversationId,
                    type: "READ",
                  })
                )
                break
              }
            }
          }
          switch (conversationState) {
            case "PROCESSING":
              // 收到新会话
              if (
                newCurrentSessionList.some(
                  (item) => item.conversationId == conversationId
                )
              ) {
                // 只有最新的消息才更新会话列表
                for (
                  let index = 0;
                  index < newCurrentSessionList.length;
                  index++
                ) {
                  const item = newCurrentSessionList[index]
                  if (
                    item.conversationId == conversationId &&
                    timeStamp(sendTime) > timeStamp(item.sendTime)
                  ) {
                    newCurrentSessionList.splice(index, 1)
                    newCurrentSessionList.unshift({
                      ...listenSendMsg,
                      readFlag: readFlag,
                    })
                    setCurrentSessionList([...newCurrentSessionList])
                    break
                  }
                }
              } else {
                newCurrentSessionList.unshift({
                  ...listenSendMsg,
                  readFlag: readFlag,
                })
                setCurrentSessionList([...newCurrentSessionList])
              }
              break
            case "COLLABORATING":
              // 收到新会话
              if (
                newCollaborationSessionList.some(
                  (item) => item.conversationId == conversationId
                )
              ) {
                // 只有最新的消息才更新会话列表
                for (
                  let index = 0;
                  index < newCollaborationSessionList.length;
                  index++
                ) {
                  const item = newCollaborationSessionList[index]
                  if (
                    item.conversationId == conversationId &&
                    timeStamp(sendTime) > timeStamp(item.sendTime)
                  ) {
                    if (!Object.keys(wsObj).length > 0) {
                      wsObj = {
                        agentCollaborationState: item.agentCollaborationState,
                        conversationCollaborationState:
                          item.conversationCollaborationState,
                        proposeFlag: item.proposeFlag,
                      }
                    }
                    newCollaborationSessionList.splice(index, 1)
                    newCollaborationSessionList.unshift({
                      ...listenSendMsg,
                      ...wsObj,
                      readFlag: readFlag,
                    })
                    setCollaborationSessionList([
                      ...newCollaborationSessionList,
                    ])
                    break
                  }
                }
              } else {
                newCollaborationSessionList.unshift({
                  ...listenSendMsg,
                  ...wsObj,
                  readFlag: readFlag,
                })
                setCollaborationSessionList([...newCollaborationSessionList])
              }
              wsObj = {}
              break
          }
          break
        case "END": // 结束会话、消息
        case "COLLABORATION_FINISH": // 完成协同
          // 如果在当前会话收到
          if (sessionClientInfo?.conversationId == conversationId) {
            setSessionClientInfo(null)
          }
          fetchSeatInfo()
          fetchEndSessionList({ noPush: true })
          setCurrentSessionList(
            newCurrentSessionList.filter(
              (item) => item.conversationId != conversationId
            )
          )
          setCollaborationSessionList(
            newCollaborationSessionList.filter(
              (item) => item.conversationId != conversationId
            )
          )
          resetPopup()
          break
        case "AGENT_STATE": // 坐席状态
          const newObj = JSON.parse(JSON.stringify(seatInfo))
          newObj.state = agentState
          setSeatInfo({ ...newObj })
          break
        case "BOT_ERROR": // 故障提示
          message.warning(
            `给${customerName}客户发送消息失败，详情请查看客户对话“最近发送消息”！`
          )
          break
        case "COLLABORATION_ADD": // 邀请协同
          // 如果在当前会话收到
          if (sessionClientInfo?.conversationId == conversationId) {
            setSessionClientInfo(null)
          }
          setCurrentSessionList(
            newCurrentSessionList.filter(
              (item) => item.conversationId != conversationId
            )
          )
          wsObj = {
            agentCollaborationState,
            conversationCollaborationState,
            proposeFlag,
          }
          break
        case "COLLABORATION_CONFIRM": // 确认协同
          switch (agentCollaborationState) {
            case "NOT_CONFIRMED":
              // 如果在当前会话收到
              if (sessionClientInfo?.conversationId == conversationId) {
                setSessionClientInfo(null)
              }
              setCollaborationSessionList(
                newCollaborationSessionList.filter(
                  (item) => item.conversationId != conversationId
                )
              )
              break
            case "CONFIRMED":
              // 如果在当前会话收到
              if (sessionClientInfo?.conversationId == conversationId) {
                let newInfo = {
                  ...sessionClientInfo,
                  agentCollaborationState,
                  conversationCollaborationState,
                }
                reloadSideBarMenu(newInfo)
                setSessionClientInfo(newInfo)
              }
              wsObj = {
                agentCollaborationState,
                conversationCollaborationState,
                proposeFlag,
              }
              break
          }
          break
        case "REVOKE":
          setDialogList(dialogList.map(dialog => {
            dialog.messageList?.map(message => {
              if (message.msgId === listenSendMsg.msgId) {
                message.revoked = true
              }
              return message
            })
            return dialog
          }))
          break
      }
      let timer = setTimeout(() => {
        setListenSendMsg(null)
        clearTimeout(timer)
      }, 100)
    }
  }, [listenSendMsg])

  // 监听ws会话消息-收到客户消息时自动触发随行操作（3秒防抖）
  const useDebounceTogetherInfo = useDebounce((term) => {
    const message = {
      origin: window.origin,
      baseUrl: baseUrl,
      disabled: disabledFlag(),
      ...term,
      updateFlag: false,
    }
    console.log(`useDebounceTogetherInfo [message]: `, message)
    if (!versionFnMap.agentSeat_knowledge()) {
      document
        .querySelector("#iframe_knowledge")
        ?.contentWindow?.postMessage(message, baseUrl)
    }
  }, 3000)

  // 获取ws后端地址前缀
  const fetchWsBackendUrl = () => {
    const url = `${transferProtocol()}//${
      process.env.NODE_ENV == "development" ? "test.wizone.work" : location.host
    }${getApiUrl()}/wecom/agentSeat/agent/ws?token=${Cookie.get(
      "weebot_cloud_token"
    )}`
    setInitWsUrl(url)
    fetchSessionWs(url)
  }

  // webSocket心跳机制-防止自动断开连接
  const heartCheck = {
    timeout: 10000,
    timeoutObj: null,
    reset: function () {
      clearInterval(this.timeoutObj)
      return this
    },
    start: function (webSocket) {
      this.timeoutObj = setInterval(function () {
        // 这里发送一个心跳，后端收到后，返回一个心跳消息，
        // onmessage拿到返回的心跳就说明连接正常
        webSocket?.send?.("HeartBeat")
      }, this.timeout)
    },
  }
  // 连接webSocket获取会话新消息
  const fetchSessionWs = (url = initWsUrl) => {
    setGlobalLoading(true)
    const webSocket = new WebSocket(url)
    webSocket.onopen = () => {
      console.log("连接上 webSocket 服务端了 - fetchSessionWs")
      if (!location.pathname.includes("/agentSeat")) {
        webSocket.close()
        return false
      }
      setSessionWs(webSocket)
      sessionWsRef.current = webSocket
      clearTimeout(timeoutWs)
      clearTimeout(timerErrorWs)
      timeoutWs = null
      timerErrorWs = null
      sessionLockReconnect = false
      wsCeiling = 0
      Modal.destroyAll()
      setGlobalLoading(false)
      heartCheck.reset().start(webSocket)
      // ws连接成功，初始化对话列表
      fetchCurrentSessionList()
      fetchCollaborationSessionList()
      fetchEndSessionList({ noPush: true })
    }
    webSocket.onerror = (error) => {
      console.log(error, "error")
      if (sessionLockReconnect) {
        return
      }
      // 没连接上会一直重连，设置延迟避免请求过多
      timerErrorWs = setTimeout(function () {
        wsCeiling += 1
        fetchSessionWs(url)
        sessionLockReconnect = false
        clearTimeout(timerErrorWs)
      }, 4000)
    }
    webSocket.onclose = (event) => {
      let errMsg =
        "fetchSessionWs - webSocket 断开: " +
        event.code +
        " " +
        event.reason +
        " " +
        event.wasClean
      console.log(errMsg)
      // 停止ws心跳
      heartCheck.reset()
      // 重连超过5次后，捕获报错信息并上报到后端
      wsCeiling == 5 && apiCall("/frontend/insert", "POST", { error: errMsg })
      if (event.code == "1000") {
        // 正常关闭连接
        return false
      } else if (event.code == "4401") {
        // token过期跳往登录页
        apiCall("/verifytoken", "POST", {})
        return false
      } else if (event.code == "4402") {
        // 下线通知
        notification.error({
          message: "下线通知",
          description: `账号已经在其他地方登入${agentSeat_title}，请关闭当前页面。`,
        })
        return false
      }
      !timeoutWs &&
        Modal.warning({
          width: 600,
          title: "断线提醒",
          content: (
            <div>
              <p>网络连接已断开，无法接受新消息，正在重连中......</p>
              <p>如一直重连不成功，请手动刷新页面。</p>
            </div>
          ),
          okText: "刷新页面",
          okType: "default",
          onOk() {
            location.reload()
          },
        })
      // ws断开链接，触发重连机制
      timeoutWs = setTimeout(function () {
        fetchSessionWs(url)
        clearTimeout(timeoutWs)
      }, 4000)
    }
    webSocket.onmessage = (event) => {
      // 闭包,无法获取外部变量
      sessionLockReconnect = true
      // event 为服务端传输的消息，在这里可以处理
      // console.log(event, 'onmessage');
      const { data } = event
      const parseData = JSON.parse(data)
      parseData.inboxType != "HEART_BEAT" && setListenSendMsg(parseData)
    }
  }

  // 获取当前座席信息
  const fetchSeatInfo = async () => {
    return new Promise(async (resolve, reject) => {
      await apiCall("/agentSeat/agent", "GET")
        .then((res) => {
          setSeatInfo(res)
          resolve()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    })
  }

  // 获取当前座席对话列表
  const fetchCurrentSessionList = async (params = {}) => {
    setSessionListLoading(true)
    const {} = params
    const data = {}
    await apiCall("/agentSeat/msg/listConversation", "GET", data)
      .then((res) => {
        setCurrentSessionList(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setSessionListLoading(false)
      })
  }

  // 获取当前坐席协同中对话列表
  const fetchCollaborationSessionList = async (params = {}) => {
    setSessionListLoading(true)
    const {} = params
    const data = {
      paged: false,
    }
    await apiCall("/agentSeat/msg/listConversation/collaboration", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        setCollaborationSessionList(records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setSessionListLoading(false)
      })
  }

  const [customerName, setCustomerName] = useState("")

  // 使用 useCallback 缓存防抖函数
  const debouncedFetch = useCallback(
    debounce((value) => {
      fetchEndSessionList({ customerName: value, noPush: true });
    }, 200),
    [] // 依赖项为空，确保只创建一次 debouncedFetch 函数
  );
  const handleCustomerChange = e => {
    setCustomerName(e.target.value)
    debouncedFetch(e.target.value); // 调用防抖函数，传递 value
  }
  // 获取当前座席已处理对话列表
  const fetchEndSessionList = async (params = {}) => {
    setSessionListLoading(true)
    const { pagination, noPush, customerName } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      customerName: customerName
    }
    await apiCall("/agentSeat/msg/listConversation/end", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res
        if (noPush) {
          setEndSessionList(records)
        } else {
          setEndSessionList([...endSessionList, ...records])
        }
        setEndPaginations({
          current: current,
          pageSize: size,
          total: total,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setSessionListLoading(false)
      })
  }

  // 转换消息类型文案
  const switchTypeHtmlDom = (item = {}) => {
    let htmlDom = ""
    switch (item.type) {
      case "TEXT":
        htmlDom = <span>{item.messageMap?.content || ""}</span>
        break
      case "LINK":
        htmlDom = <span>[链接] {item.messageMap?.title || ""}</span>
        break
      case "WE_APP":
        htmlDom = <span>[小程序] {item.messageMap?.title || ""}</span>
        break
      case "IMAGE":
        htmlDom = "[图片]"
        break
      case "VIDEO":
        htmlDom = "[视频]"
        break
      case "VOICE":
        htmlDom = "[语音]"
        break
      case "FILE":
        htmlDom = "[文件]"
        break
      case "EMOTION":
        htmlDom = "[表情]"
        break
      case "CUSTOM_SYSTEM":
        const { content, type, endTime, agentGroupName, agentName } =
          item.messageMap
        let typeContent = ""
        switch (type) {
          case "COUNTDOWN_DISTRIBUTION":
          case "COUNTDOWN_CLOSE":
          case "COLLABORATE_COUNTDOWN_CLOSE":
            let title =
              type == "COUNTDOWN_DISTRIBUTION"
                ? "[即将超时重新分配]"
                : "[即将自动结束对话]"
            if (item.conversationState == "END") {
              typeContent = <span>{title}</span>
            } else {
              typeContent = (
                <span style={{ display: "flex", color: "#ff4347" }}>
                  {title}{" "}
                  <Countdown
                    valueStyle={{
                      marginLeft: "6px",
                      fontSize: "14px",
                      color: "#ff4347",
                    }}
                    value={endTime}
                    format="mm:ss"
                  />
                </span>
              )
            }
            break
          case "START_COLLABORATE":
            typeContent = (
              <span>
                {agentGroupName}-{agentName}&nbsp;发起&nbsp;协同邀请
              </span>
            )
            break
          case "REVOKE":
            typeContent = (
              <span style={{ color: "#ff4347" }}>撤回了一条消息</span>
            )
            break
          default:
            typeContent = <span>{content || ""}</span>
            break
        }
        htmlDom = typeContent
        break
      case "SESSION_DISAGREE":
        htmlDom = <span>对方不同意存档会话内容，你将无法继续提供服务</span>
        break
      case "SESSION_AGREE":
        htmlDom = <span>对方同意存档会话内容，你可以继续提供服务</span>
        break
    }
    return htmlDom
  }

  // 点击选择会话
  const handleSelectSession = debounce(async (item) => {
    if (sessionClientInfo && sessionClientInfo.uniqueId == item.uniqueId)
      return false

    await reloadSideBarMenu(item)
    setSessionClientInfo({ ...item })
  }, 200)

  // 加载侧边栏菜单
  const reloadSideBarMenu = async (info = sessionClientInfo) => {
    setTabs([])

    const res = await apiCall("/agentSeat/sideBarMenu", "GET")
    const iframeParams = buildIframeParams(info)
    const queryParams = new URLSearchParams({
      ts: Date.now(),
      accessToken: Cookie.get("weebot_cloud_token"),
      kitMapList: JSON.stringify(AppStore.state.User.kitMapList),
      moduleVersionMap: JSON.stringify(AppStore.state.User.moduleVersionMap),
      ...iframeParams,
    })
    const isDev = process.env.NODE_ENV === "development"
    const localUrl = "http://localhost:9021/frp"
    const defaultBaseUrl = getSideBarBaseUrlOfH5()

    const data = res.map((item, index) => {
      const base = item.url.startsWith("http")
        ? item.url
        : (isDev ? localUrl : defaultBaseUrl) + item.url

      const fullSrc =
        base + (base.includes("?") ? "&" : "?") + queryParams.toString()

      if (item.defaultFlag) {
        setTabIndex(tabIndex || item.code)
      }

      return {
        ...item,
        key: item.code,
        // isDefault: i === 0, // 默认显示
        // isExternal: item.src.startsWith('http'), // 是否外部链接
        src: fullSrc,
      }
    })

    setTabs(data)
  }

  const buildIframeParams = (info = null) => {
    if (!info) return {}

    return {
      origin: window.origin,
      baseUrl: baseUrl,
      disabled: disabledFlag(info),
      customerId: info.customerId,
      employeeId: info.employeeId,
      conversationId: info.conversationId,
      conversationState: info.conversationState,
      msgId: "",
      updateFlag: true,
    }
  }

  useEffect(async () => {
    if (sessionClientInfo) {
      const { conversationId, conversationState } = sessionClientInfo
      setDialogList([])
      isScrollTo = true
      chatFormRef.current?.resetFields()
      // 在点进客户会话时就将该客户未读变已读，防止点进该客户会话就马上进行客户列表下拉分页的数据异步问题
      sessionWs?.send?.(
        JSON.stringify({
          conversationId: conversationId,
          type: "READ",
        })
      )
      const newCurrentSessionList = JSON.parse(
        JSON.stringify(currentSessionList)
      )
      newCurrentSessionList.forEach((item) => {
        if (item.conversationId == conversationId) {
          item.readFlag = true
        }
      })
      setCurrentSessionList(newCurrentSessionList)
      const newCollaborationSessionList = JSON.parse(
        JSON.stringify(collaborationSessionList)
      )
      newCollaborationSessionList.forEach((item) => {
        if (item.conversationId == conversationId) {
          item.readFlag = true
        }
      })
      setCollaborationSessionList(newCollaborationSessionList)
      getCustomerEmployeeState({ conversationId })
      fetchSessionDialogList({ conversationId, conversationState })
      getGlobalSettingAgentConfig()
    }
  }, [sessionClientInfo, sessionWs])

  // 获取客户员工状态
  const getCustomerEmployeeState = async (params = {}) => {
    const { conversationId } = params
    const data = {
      conversationId: conversationId,
    }
    apiCall("/agentSeat/msg/getCustomerEmployeeState", "GET", data)
      .then((res) => {
        setCustomerEmployeeState(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取某个对话的全部列表
  const fetchSessionDialogList = async (params = {}) => {
    setChatLoading(true)
    const { conversationId, conversationState } = params
    const data = {
      conversationId: conversationId,
    }
    apiCall("/agentSeat/msg/listConversationBase", "GET", data)
      .then((res) => {
        if (conversationState == "END") {
          // 已处理默认定位并展开一个对话
          setDialogActiveKey([conversationId])
        } else {
          // 进行中、协同中默认展开最新两个对话
          const lastIndex = res.length - 1 // 获取数组最后一个元素的索引
          const secondLastIndex = lastIndex - 1 // 获取倒数第二个元素的索引
          let keys = []
          res.forEach((item, index) => {
            if (index == secondLastIndex || index == lastIndex) {
              keys.push(item.conversationId)
            }
          })
          setDialogActiveKey([...keys])
        }
        setDialogList(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setChatLoading(false)
      })
  }

  // 获取坐席通用配置
  const getGlobalSettingAgentConfig = async () => {
    const data = {}
    apiCall("/globalSetting/agent_config", "GET", data)
      .then((res) => {
        const { quickReplyList } = res
        setQuickReplyList(quickReplyList)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  useEffect(async () => {
    if (dialogActiveKey.length && dialogList.length) {
      const { conversationState } = sessionClientInfo
      for (let i = 0; i < dialogList.length; i++) {
        // 在特定条件下停止迭代
        if (
          dialogActiveKey.includes(dialogList[i].conversationId) &&
          !dialogList[i].messageList
        ) {
          await fetchSessionMessage({
            conversationId: dialogList[i].conversationId,
          })
          break
        }
      }
      if (!isScrollTo) {
        return false
      }
      if (conversationState == "END") {
        // 已处理的会话
        let conversationId = dialogActiveKey[dialogActiveKey.length - 1]
        let locateBox = document.querySelector(
          `#dialogPanel${conversationId} .ant-collapse-header`
        )
        setTimeout(() => {
          wiBodyRef.current.scrollTop =
            locateBox.offsetTop - locateBox.clientHeight - 12 // 滚动条位置 = 当前消息偏移量-当前消息高度
        }, 1000)
      } else {
        // 进行中、协同中的会话
        setTimeout(() => {
          scrollToBottom("wi-body")
        }, 1000)
      }
      // 限制滚动到指定会话。已处理的会话最多1次，初始化的1次。进行中、协同中的会话最多2次，初始化的2次。
      let maxScrollTo = conversationState == "END" ? 1 : 2
      let currentLength = dialogList.filter((item) => item.messageList).length
      if (currentLength > 0 && currentLength <= maxScrollTo) {
        currentLength >= maxScrollTo && (isScrollTo = false)
      }
    }
  }, [dialogActiveKey, dialogList])

  // 获取某个对话的消息列表
  const fetchSessionMessage = async (params = {}) => {
    setChatLoading(true)
    const { conversationId } = params
    const data = {
      conversationId: conversationId,
    }
    await apiCall("/agentSeat/msg/listMsg", "GET", data)
      .then((res) => {
        // 如果客户最后一条消息是不同意会话存档
        // if (res.findLastIndex(item => item.type == 'SESSION_DISAGREE') == (res.length - 1)) {
        if (
          getLastSuccessfulConditionIndex(
            res,
            (item) => item.type == "SESSION_DISAGREE"
          ) ==
          res.length - 1
        ) {
          message.warning(
            "客户不同意会话存档，等待客户同意后才能接收客户的消息"
          )
        }
        const newDialogList = JSON.parse(JSON.stringify(dialogList))
        newDialogList.forEach((item) => {
          if (item.conversationId == conversationId) {
            item.messageList = res
          }
        })
        setDialogList(newDialogList)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setChatLoading(false)
      })
  }

  // 会话记录下拉加载
  const loadMoreData = (type) => {
    switch (type) {
      case "endSession":
        fetchEndSessionList({
          pagination: {
            ...endPaginations,
            current: endPaginations.current + 1,
          },
          customerName
        })
        break
    }
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const content = chatFormRef.current.getFieldValue("content") || ""
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      content.substring(0, startPos) + insertEmoji + content.substring(endPos)
    chatFormRef.current.setFieldsValue({
      content: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
    setSendBtnStatus(false)
  }

  // 点击工具栏
  const handleToolbar = (type = "") => {
    const { conversationId } = sessionClientInfo
    switch (type) {
      case "message": // 收藏消息
        setCollectMessageParams({
          visible: true,
          onCancel: (obj) => {
            resetPopup()
          },
        })
        break
      case "workOrder": // 创建工单
        setWorkOrderParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: () => {
            resetPopup()
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "switchSession": // 转接对话
        setSwitchSessionParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "endSession": // 结束对话
        setUpdateSessionParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
            if (isV2()) {
              apiCall(`/icbc_sz/knowledge/actionCollectV2`, "POST", {
                sessionId: conversationId,
                actionType: 12,
                content: data.text,
              })
            }
            const message = {
              origin: window.origin,
              baseUrl: baseUrl,
              disabled: disabledFlag(),
              operationType: "addTrack",
              userAction: 10,
              content: data.content,
            }
            if (!versionFnMap.agentSeat_knowledge()) {
              document
                .querySelector("#iframe_knowledge")
                ?.contentWindow?.postMessage(message, baseUrl)
            }
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "updateSummary": // 修改小结
        setUpdateSessionParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
            const message = {
              origin: window.origin,
              baseUrl: baseUrl,
              disabled: disabledFlag(),
              operationType: "addTrack",
              check: true,
              userAction: 10,
              content: data.content,
            }
            if (!versionFnMap.agentSeat_knowledge()) {
              document
                .querySelector("#iframe_knowledge")
                ?.contentWindow?.postMessage(message, baseUrl)
            }
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "collaborativeInvite": // 协同邀请
        setCollaborativeInviteParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
            fetchSeatInfo()
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "joinSession": // 接入对话
        apiCall(
          `/agentSeat/conversation/confirmCollaboration?conversationId=${conversationId}`,
          "POST"
        )
        break
      case "completeCollaborating": // 完成协同
        setUpdateSessionParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
      case "forceEndCompleteCollaborating":
        setUpdateSessionParams({
          visible: true,
          sessionClientInfo: sessionClientInfo,
          onSubmit: (data) => {
            resetPopup()
          },
          onCancel: () => {
            resetPopup()
          },
        })
        break
    }
  }

  // 监听textArea内容变化
  const onChangeTextArea = (event) => {
    const { value } = event.target
    if (!value || value.match(/^\s*$/)) {
      setSendBtnStatus(true)
    } else {
      setSendBtnStatus(false)
    }
  }

  // 处理粘贴事件
  const handlePaste = (event) => {
    if (isV1()) {
      return
    }
    const items = event.clipboardData.items

    for (let item of items) {
      if (item.type.startsWith("image/")) {
        // 如果是图片类型
        const file = item.getAsFile()
        console.log("获取到的图片文件:", file)
        file.url = URL.createObjectURL(file)

        // 上传图片
        WibotModal.open({
          title: `发送给${sessionClientInfo.customerName}`,
          children: (
            <div>
              <Image src={file.url} />
            </div>
          ),
          onOk: () => {
            const data = new FormData()
            data.append("file", file)
            apiCall("/file/image?uploadWx=0", "POST", data).then((file) => {
              const { mediaUrl, fileUrl } = file
              apiCall(
                `/agentSeat/msg/send?conversationId=${sessionClientInfo.conversationId}`,
                "POST",
                [
                  {
                    fileId: [fileUrl],
                    type: "Picture",
                  },
                ]
              ).then((res) => {
                message.warning("信息来源自会话存档，展示会有延迟！")
              })
            })
          },
          onCancel: () => {},
        })
        event.preventDefault() // 阻止默认行为（避免图片以 base64 粘贴到文本框中）
        return
      }
    }
  }

  // 监听键盘enter事件,shift+enter换行
  const onPressEnterTextArea = (event) => {
    if (!event.shiftKey && event.keyCode == 13) {
      event.cancelBubble = true // ie阻止冒泡行为
      event.stopPropagation() // Firefox阻止冒泡行为
      event.preventDefault() // 取消事件的默认动作*换行
    }
    if (!event.shiftKey && event.keyCode == 13) {
      // 以下处理发送消息代码
      sessionMessageSend()
    }
  }

  // 会话发送消息
  const sessionMessageSend = useDebounce(() => {
    chatFormRef.current.validateFields().then((formData) => {
      const { conversationId } = sessionClientInfo
      const { content } = formData
      if (!content || content.match(/^\s*$/)) {
        message.warning("不能发送空白消息")
        // 空白内容主动清空文本域
        chatFormRef.current.resetFields()
        return false
      }
      setChatLoading(true)
      // 发送前取消聚焦，避免继续输入内容。
      textAreaRef.current.blur()
      const data = [
        {
          type: "copyWriter",
          content,
        },
      ]
      if (isV2()) {
        apiCall(`/icbc_sz/knowledge/actionCollectV2`, "POST", {
          sessionId: conversationId,
          actionType: 10,
          content: content,
        })
      }
      apiCall(
        `agentSeat/msg/checkBeforeSend?conversationId=${conversationId}`,
        "POST",
        data
      )
        .then((res) => {
          console.log(`checkBeforeSend [res]: `, res)
          if (!res) {
            send()
            return
          }
          const words = res.hitMonitorObject?.sensitiveWordList?.join?.(",")
          if (res.actionType === "WARN_EMPLOYEE") {
            const close = WibotModal.open({
              title: "提示",
              children: (
                <div>本次发送的消息中包含"{words}"等敏感词, 是否继续发送?</div>
              ),
              footer: [
                <Button
                  key="back"
                  type="primary"
                  onClick={() => {
                    console.log("已取消")
                    close()
                  }}
                >
                  取消
                </Button>,
                <Button
                  key="submit"
                  onClick={() => {
                    console.log("已发送")
                    send()
                    close()
                  }}
                >
                  继续发送
                </Button>,
              ],
            })
          }
          if (res.actionType === "WARN_AND_BLOCK") {
            message.error(
              `本次发送的消息中包合"${words}"等敏惑词，请删除后重发！`
            )
            // 空白内容主动清空文本域
            // chatFormRef.current.resetFields();
          }
        })
        .finally(() => {
          setChatLoading(false)
        })

      function send() {
        apiCall(
          `/agentSeat/msg/send?conversationId=${conversationId}`,
          "POST",
          data
        )
          .then((res) => {
            message.warning("信息来源自会话存档，展示会有延迟！")
            // 发送成功清空文本域
            chatFormRef.current.resetFields()
            textAreaRef.current.focus({ cursor: "start" })
            setSendBtnStatus(true)
            const messageObj = {
              origin: window.origin,
              baseUrl: baseUrl,
              disabled: disabledFlag(),
              operationType: "addTrack",
              userAction: 3,
              content: content,
            }
            if (!versionFnMap.agentSeat_knowledge()) {
              document
                .querySelector("#iframe_knowledge")
                ?.contentWindow?.postMessage(messageObj, baseUrl)
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setChatLoading(false)
          })
      }
    })
  }, 10)

  // 快捷回复发送
  const quickReplySend = (content) => {
    setChatLoading(true)
    const { conversationId } = sessionClientInfo
    const data = [
      {
        type: "copyWriter",
        content: content,
      },
    ]
    apiCall(
      `/agentSeat/msg/send?conversationId=${conversationId}`,
      "POST",
      data
    )
      .then((res) => {
        message.warning("信息来源自会话存档，展示会有延迟！")
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setChatLoading(false)
      })
  }

  // 将收藏消息追加到消息框
  const onAddMessage = async (content = "") => {
    const fieldContent = chatFormRef.current.getFieldValue("content") || ""
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) return
    chatFormRef.current.setFieldsValue({
      content:
        fieldContent.substring(0, startPos) +
        content +
        fieldContent.substring(endPos),
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + content.length,
      endPos + content.length
    )
    setSendBtnStatus(false)
  }

  // 点击文本消息随行
  const handleTogether = (data) => {
    setTabIndex("knowledge")
    const message = {
      origin: window.origin,
      baseUrl: baseUrl,
      disabled: disabledFlag(),
      ...data,
      updateFlag: false,
    }
    if (!versionFnMap.agentSeat_knowledge()) {
      document
        .querySelector("#iframe_knowledge")
        ?.contentWindow?.postMessage(message, baseUrl)
    }
  }

  // 监听切换坐席状态
  const onChangeSeatState = (value) => {
    setGlobalLoading(true)
    apiCall(`/agentSeat/agent/update/${seatInfo.id}?state=${value}`, "POST")
      .then((res) => {
        const newObj = JSON.parse(JSON.stringify(seatInfo))
        newObj.state = value
        setSeatInfo({ ...newObj })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setGlobalLoading(false)
      })
  }

  const formatDateTime = (time) => {
    return moment(time).format("YYYY/MM/DD") == moment().format("YYYY/MM/DD")
      ? moment(time).format("HH:mm:ss")
      : moment(time).format("YYYY/MM/DD")
  }

  const disabledFlag = (info = sessionClientInfo) => {
    if (!info) return true

    const {
      conversationState,
      proposeFlag,
      conversationCollaborationState,
      agentCollaborationState,
    } = info

    const isProcessing = conversationState === "PROCESSING"
    const isCollaboratingNotConfirmed =
      conversationState === "COLLABORATING" &&
      proposeFlag &&
      conversationCollaborationState === "NOT_CONFIRMED"
    const isAgentConfirmed =
      !proposeFlag && agentCollaborationState === "CONFIRMED"

    const shouldEnable =
      isProcessing || isCollaboratingNotConfirmed || isAgentConfirmed

    return !shouldEnable
  }

  const handleSelectImage = () => {
    console.log(`[sessionClientInfo]: `, sessionClientInfo)
    openFileSelector({ accept: "image/*" }).then((files) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        WibotModal.open({
          title: `发送给${sessionClientInfo.customerName}`,
          children: (
            <div>
              <FileHOC src={e.target.result}>
                {(url) => <Image src={url} />}
              </FileHOC>
            </div>
          ),
          onOk: () => {
            const data = new FormData()
            data.append("file", files[0])
            apiCall("/file/image?uploadWx=0", "POST", data).then((file) => {
              const { mediaUrl, fileUrl } = file
              apiCall(
                `/agentSeat/msg/send?conversationId=${sessionClientInfo.conversationId}`,
                "POST",
                [
                  {
                    fileId: [fileUrl],
                    type: "Picture",
                  },
                ]
              ).then((res) => {
                message.warning("信息来源自会话存档，展示会有延迟！")
              })
            })
          },
          onCancel: () => {},
        })
      }
      reader.readAsDataURL(files[0]) // 假设只读取第一个文件
    })
  }

  const updateSstContent = ({conversationId,msgId, content}) => {
    setDialogList(prevState => {
      return prevState.map(i => {
        i.messageList = i.messageList?.map(msg => {
          if (msg.msgId === msgId) {
            msg.sstContent = content
          }
          return msg
        })
        return i
      })
    })
    setTimeout(() => {
      document.querySelector('.wi-type-voice-text-' + msgId).scrollIntoView({
        behavior: 'smooth', // 可选：平滑滚动
        block: 'end',       // 垂直方向滚动到元素的底部
      })
    }, 1000)
  }

  return (
    <div className="AgentSeat-Container">
      <Spin spinning={globalLoading}>
        <div className="globalRow">
          {/* 左侧边栏 */}
          <Card
            className="wi-chat-left-sidebar"
            bordered={false}
            title={agentSeat_title}
            extra={
              <Select
                popupClassName="wi-chat-left-sidebar-state"
                bordered={false}
                dropdownMatchSelectWidth={false}
                optionLabelProp="label"
                value={seatInfo.state}
                onChange={(value, option) => {
                  onChangeSeatState(value)
                }}
                onDropdownVisibleChange={(open) => {
                  if (open) {
                    apiCall("/agentSeat/agentLog", "GET", {
                      agentId: seatInfo.id,
                    })
                      .then((res) => {
                        const { restTotalTime, signInTotalTime } = res
                        const newArr = [...seatStateOptions]
                        newArr[0].seconds = signInTotalTime
                        newArr[1].seconds = restTotalTime
                        setSeatStateOptions([...newArr])
                      })
                      .catch((err) => {
                        console.log(err)
                      })
                  }
                }}
              >
                {seatStateOptions.map((item, index) => (
                  <Select.Option
                    key={index}
                    value={item.value}
                    label={item.label}
                  >
                    {item.label}
                    {item.value != "SIGN_OUT"
                      ? `（${secondsToTime(item.seconds, "HH:mm")}）`
                      : ""}
                  </Select.Option>
                ))}
              </Select>
            }
            actions={["今日处理量：" + (seatInfo?.summaryCount || 0)]}
          >
            <Spin spinning={sessionListLoading}>
              <Collapse defaultActiveKey={["1"]} ghost accordion>
                <Panel
                  header={"进行中（" + currentSessionList.length + "）"}
                  key="1"
                >
                  {currentSessionList.length ? (
                    <List
                      split={false}
                      itemLayout="horizontal"
                      dataSource={currentSessionList}
                      renderItem={(item, index) => (
                        <List.Item
                          key={index}
                          onClick={() => handleSelectSession(item)}
                          className={
                            sessionClientInfo &&
                            sessionClientInfo.uniqueId == item.uniqueId &&
                            "selected"
                          }
                        >
                          <List.Item.Meta
                            avatar={
                              // readFlag:为true为已读
                              <Badge
                                count={
                                  !item.readFlag ? (
                                    <span
                                      style={{
                                        width: 10,
                                        height: 10,
                                        borderRadius: "50%",
                                        background: "red",
                                      }}
                                    />
                                  ) : null
                                }
                                offset={[-4, 4]}
                              >
                                {/*<Badge dot={!item.readFlag} count={} offset={[-4, 4]}>*/}
                                <Avatar
                                  size={40}
                                  src={
                                    <FileHOC src={item.customerAvatar}>
                                      {(url) => (
                                        <Image
                                          style={{ objectFit: "cover" }}
                                          src={url}
                                          alt={item.customerName}
                                          fallback={
                                            versionFnMap.system_ui().avatar
                                              ? customerAvatar
                                              : ""
                                          }
                                        />
                                      )}
                                    </FileHOC>
                                  }
                                />
                              </Badge>
                            }
                            title={
                              <>
                                <div className="name">{item.customerName}</div>
                                <div className="last-updated">
                                  {formatDateTime(item.sendTime)}
                                </div>
                              </>
                            }
                            description={switchTypeHtmlDom(item)}
                          />
                        </List.Item>
                      )}
                    />
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Panel>

                <Panel
                  header={"协同中（" + collaborationSessionList.length + "）"}
                  key="2"
                >
                  {collaborationSessionList.length ? (
                    <List
                      split={false}
                      itemLayout="horizontal"
                      dataSource={collaborationSessionList}
                      renderItem={(item, index) => (
                        <List.Item
                          key={index}
                          onClick={() => handleSelectSession(item)}
                          className={
                            sessionClientInfo &&
                            sessionClientInfo.uniqueId == item.uniqueId &&
                            "selected"
                          }
                        >
                          <List.Item.Meta
                            avatar={
                              // readFlag:为true为已读
                              <Badge
                                count={
                                  !item.readFlag ? (
                                    <span
                                      style={{
                                        width: 10,
                                        height: 10,
                                        borderRadius: "50%",
                                        background: "red",
                                      }}
                                    />
                                  ) : null
                                }
                                offset={[-4, 4]}
                              >
                                <Avatar
                                  size={40}
                                  src={
                                    <FileHOC src={item.customerAvatar}>
                                      {(url) => (
                                        <Image
                                          style={{ objectFit: "cover" }}
                                          src={url}
                                          alt={item.customerName}
                                          fallback={
                                            versionFnMap.system_ui().avatar
                                              ? customerAvatar
                                              : ""
                                          }
                                        />
                                      )}
                                    </FileHOC>
                                  }
                                />
                              </Badge>
                            }
                            title={
                              <>
                                <div className="name">{item.customerName}</div>
                                <div className="last-updated">
                                  {formatDateTime(item.sendTime)}
                                </div>
                              </>
                            }
                            description={
                              <>
                                {switchTypeHtmlDom(item)}
                                {!item.proposeFlag &&
                                item.agentCollaborationState ==
                                  "NOT_CONFIRMED" ? (
                                  <div style={{ color: "#ff4347" }}>
                                    (待确认)
                                  </div>
                                ) : (
                                  ""
                                )}
                              </>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Panel>

                <Panel header="已处理" key="3">
                  <div style={{margin: 4}}>
                    <Search value={customerName} onChange={(e) => setCustomerName(e.target.value)} onSearch={debouncedFetch} />
                  </div>
                  {endSessionList.length ? (
                    <List
                      split={false}
                      itemLayout="horizontal"
                      loadMore={
                        endPaginations.current * endPaginations.pageSize >=
                        endPaginations.total ? (
                          <Divider plain>已经到底了</Divider>
                        ) : (
                          <Button
                            style={{ display: "flex", margin: "12px auto" }}
                            type="primary"
                            onClick={(e) => loadMoreData("endSession")}
                          >
                            加载更多
                          </Button>
                        )
                      }
                      dataSource={endSessionList}
                      renderItem={(item, index) => (
                        <List.Item
                          key={index}
                          onClick={() => handleSelectSession(item)}
                          className={
                            sessionClientInfo &&
                            sessionClientInfo.uniqueId == item.uniqueId &&
                            "selected"
                          }
                        >
                          <List.Item.Meta
                            avatar={
                              // readFlag:为true为已读
                              <Badge dot={false} size={60} offset={[-4, 4]}>
                                <Avatar
                                  size={40}
                                  src={
                                    <FileHOC src={item.customerAvatar}>
                                      {(url) => (
                                        <Image
                                          style={{ objectFit: "cover" }}
                                          src={url}
                                          alt={item.customerName}
                                          fallback={
                                            versionFnMap.system_ui().avatar
                                              ? customerAvatar
                                              : ""
                                          }
                                        />
                                      )}
                                    </FileHOC>
                                  }
                                />
                              </Badge>
                            }
                            title={
                              <>
                                <div className="name">{item.customerName}</div>
                                <div className="last-updated">
                                  {formatDateTime(item.sendTime)}
                                </div>
                              </>
                            }
                            description={switchTypeHtmlDom(item)}
                          />
                        </List.Item>
                      )}
                    />
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </Panel>
              </Collapse>
            </Spin>
          </Card>

          {/* 会话盒子 */}
          {sessionClientInfo ? (
            <div className="wi-chat-session-box">
              <Spin spinning={chatLoading}>
                <div className="wi-chat-session">
                  {/* 中间会话内容 */}
                  <Card
                    className="wi-chat-content"
                    title={
                      <div style={{ display: "flex" }}>
                        <Avatar
                          src={
                            <FileHOC
                              src={
                                sessionClientInfo.customerAvatar ||
                                (versionFnMap.system_ui().avatar &&
                                  customerAvatar)
                              }
                            >
                              {(url) => (
                                <Image
                                  style={{ objectFit: "cover" }}
                                  src={url}
                                  alt={sessionClientInfo.customerName}
                                  fallback={
                                    versionFnMap.system_ui().avatar
                                      ? customerAvatar
                                      : ""
                                  }
                                />
                              )}
                            </FileHOC>
                          }
                        />
                        <div style={{ marginLeft: "10px" }}>
                          <Paragraph ellipsis={{ tooltip: true }}>
                            {sessionClientInfo.customerName}
                          </Paragraph>
                          {customerEmployeeState && (
                            <Paragraph ellipsis={{ tooltip: true }}>
                              <SysDictLabel
                                style={{ display: "inline", color: "#ff4347" }}
                                dataset="CUSTOMER_EMPLOYEE_STATE_AS"
                                dictkey={customerEmployeeState}
                              />
                            </Paragraph>
                          )}
                        </div>
                      </div>
                    }
                    extra={
                      <div style={{ display: "flex" }}>
                        <div style={{ marginRight: "10px" }}>
                          <Paragraph
                            style={{ textAlign: "right" }}
                            ellipsis={{ tooltip: true }}
                          >
                            {sessionClientInfo.employeeName}
                          </Paragraph>
                          <Paragraph
                            style={{ textAlign: "right" }}
                            ellipsis={{ tooltip: true }}
                          >
                            {sessionClientInfo.departmentName}
                          </Paragraph>
                        </div>
                        <Avatar
                          src={
                            <FileHOC
                              src={
                                sessionClientInfo.employeeAvatar ||
                                (versionFnMap.system_ui().avatar
                                  ? systemAvatar
                                  : "")
                              }
                            >
                              {(url) => (
                                <Image
                                  style={{ objectFit: "cover" }}
                                  src={url}
                                  alt={sessionClientInfo.employeeName}
                                  fallback={
                                    versionFnMap.system_ui().avatar
                                      ? systemAvatar
                                      : ""
                                  }
                                />
                              )}
                            </FileHOC>
                          }
                        />
                      </div>
                    }
                  >
                    <div
                      id="wi-body"
                      className="wi-body custom-scrollbar"
                      ref={wiBodyRef}
                    >
                      <div className="wi-messages-list">
                        {dialogList.length ? (
                          <Collapse
                            className="wi-messages-dialog-list"
                            expandIcon={({ isActive }) => (
                              <CaretRightOutlined rotate={isActive ? 90 : 0} />
                            )}
                            ghost
                            destroyInactivePanel
                            defaultActiveKey={dialogActiveKey}
                            activeKey={dialogActiveKey}
                            onChange={(key) => {
                              setDialogActiveKey([
                                ...key.map((str) => parseInt(str)),
                              ])
                            }}
                          >
                            {dialogList.map((item, index) => (
                              <Panel
                                id={`dialogPanel${item.conversationId}`}
                                header={`对话 ${item.time}`}
                                key={item.conversationId}
                              >
                                {item.messageList?.length ? (
                                  <>
                                    {item.messageList.map((atem, andex) => (
                                      <TypeMessage
                                        key={andex}
                                        item={atem}
                                        updateSstContent={updateSstContent}
                                        preview={disabledFlag()}
                                        onTogether={handleTogether}
                                        amrObj={amrObj}
                                        onVoice={(data) => {
                                          setAmrObj({ ...data })
                                        }}
                                      />
                                    ))}
                                  </>
                                ) : (
                                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                                )}
                              </Panel>
                            ))}
                          </Collapse>
                        ) : (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                        )}
                      </div>
                    </div>
                    {quickReplyList.length ? (
                      <div className="wi-quickReply-list">
                        <Space>
                          {quickReplyList.map((item, index) => (
                            <Popconfirm
                              key={index}
                              icon={false}
                              disabled={disabledFlag()}
                              title={<p style={{ width: "300px" }}>{item}</p>}
                              cancelText="取消"
                              okText="发送"
                              onConfirm={() => {
                                quickReplySend(item)
                              }}
                            >
                              <Button
                                disabled={disabledFlag()}
                                key={index}
                                type="dashed"
                                shape="round"
                              >
                                <Text
                                  disabled={disabledFlag()}
                                  style={{ maxWidth: 63 }}
                                  ellipsis={{
                                    tooltip: {
                                      title: item,
                                      placement: "bottomLeft",
                                    },
                                  }}
                                >
                                  {item}
                                </Text>
                              </Button>
                            </Popconfirm>
                          ))}
                        </Space>
                      </div>
                    ) : (
                      ""
                    )}

                    <div className="wi-footer">
                      <div className="wi-footer-top-bar">
                        <div className="left-fun-bar">
                          <WibotEmoji onEmojiSelect={handleEmojiSelect} />

                          <Tooltip title="收藏消息">
                            <MessageOutlined
                              onClick={() => {
                                handleToolbar("message")
                              }}
                            />
                          </Tooltip>

                          {isV2() ? (
                            <Tooltip title="选择图片">
                              <FolderOutlined
                                onClick={() => {
                                  handleSelectImage()
                                }}
                              />
                            </Tooltip>
                          ) : null}
                        </div>
                        <div className="right-fun-bar">
                          {sessionClientInfo.conversationState ==
                          "COLLABORATING" ? (
                            <>
                              <Tooltip title="完成协同">
                                <PoweroffOutlined
                                  onClick={() => {
                                    handleToolbar("completeCollaborating")
                                  }}
                                />
                              </Tooltip>
                            </>
                          ) : (
                            <>
                              <Tooltip title="协同邀请">
                                <UsergroupAddOutlined
                                  onClick={() => {
                                    handleToolbar("collaborativeInvite")
                                  }}
                                />
                              </Tooltip>
                              <Tooltip title={`创建${workOrderTitle}`}>
                                <FileTextOutlined
                                  onClick={() => {
                                    handleToolbar("workOrder")
                                  }}
                                />
                              </Tooltip>
                              <Tooltip title="转接对话">
                                <SwapOutlined
                                  onClick={() => {
                                    handleToolbar("switchSession")
                                  }}
                                />
                              </Tooltip>
                              <Tooltip title="结束对话">
                                <PoweroffOutlined
                                  onClick={() => {
                                    handleToolbar("endSession")
                                  }}
                                />
                              </Tooltip>
                            </>
                          )}
                        </div>
                      </div>

                      <Form layout={"inline"} ref={chatFormRef}>
                        <Form.Item name="content" style={{ width: "100%" }}>
                          <TextArea
                            id="msgTextInput"
                            className="custom-scrollbar"
                            ref={textAreaRef}
                            placeholder="输入点什么"
                            bordered={false}
                            onPaste={handlePaste}
                            autoSize={{ minRows: 4, maxRows: 10 }}
                            onPressEnter={onPressEnterTextArea}
                            onChange={onChangeTextArea}
                          />
                        </Form.Item>
                      </Form>

                      <div className="wi-footer-operate-box">
                        <Button
                          disabled={sendBtnStatus}
                          type="primary"
                          onClick={() => {
                            sessionMessageSend()
                          }}
                        >
                          发送
                        </Button>
                      </div>

                      {/* 对话遮罩层 */}
                      {disabledFlag() ? (
                        <div className="mask">
                          <Space>
                            {sessionClientInfo.conversationState == "END" && (
                              <>
                                <Button
                                  shape="round"
                                  onClick={() => {
                                    handleToolbar("updateSummary")
                                  }}
                                >
                                  修改小结
                                </Button>
                                <Button
                                  shape="round"
                                  onClick={() => {
                                    handleToolbar("workOrder")
                                  }}
                                >
                                  创建{workOrderTitle}
                                </Button>
                              </>
                            )}
                            {sessionClientInfo.conversationState ==
                              "COLLABORATING" &&
                              !sessionClientInfo.proposeFlag &&
                              sessionClientInfo.agentCollaborationState ==
                                "NOT_CONFIRMED" && (
                                <>
                                  <Popconfirm
                                    title="请确认是否立即接入对话?"
                                    cancelText="取 消"
                                    okText="确 定"
                                    onConfirm={() => {
                                      handleToolbar("joinSession")
                                    }}
                                  >
                                    <Button shape="round">接入对话</Button>
                                  </Popconfirm>
                                </>
                              )}
                            {sessionClientInfo.conversationState ==
                              "COLLABORATING" &&
                              sessionClientInfo.proposeFlag &&
                              sessionClientInfo.conversationCollaborationState ==
                                "CONFIRMED" && (
                                <Button
                                  shape="round"
                                  onClick={() => {
                                    handleToolbar(
                                      "forceEndCompleteCollaborating"
                                    )
                                  }}
                                >
                                  索回对话
                                </Button>
                              )}
                          </Space>
                        </div>
                      ) : (
                        ""
                      )}
                    </div>
                  </Card>

                  {/* 右侧标签页 */}
                  <div className="wi-chat-right-tabs">
                    <Tabs
                      activeKey={tabIndex}
                      onChange={(activeKey) => setTabIndex(activeKey)}
                    >
                      {tabs.map((item, index) => (
                        <TabPane
                          tab={item.name}
                          key={item.code}
                          style={{ position: "relative" }}
                        >
                          <Button
                            onClick={() => reloadSideBarMenu()}
                            style={{ position: "absolute", zIndex: 0 }}
                          >
                            刷新
                          </Button>
                          <iframe
                            style={{ position: "absolute", zIndex: 1 }}
                            id={`iframe_${item.code}`}
                            src={`${item.src}`}
                            key={`${item.key}`} // 强制重新加载
                            onError={(e) => {
                              console.log(e, "超级无敌大错误，需要重新loading")
                            }}
                          />
                        </TabPane>
                      ))}
                    </Tabs>
                    {tabs.length === 0 && <div>加载中...</div>}
                  </div>
                </div>
              </Spin>
            </div>
          ) : (
            <div className="noInfo">
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            </div>
          )}

          {/* 右侧功能栏 */}
          <Sider />
        </div>
      </Spin>
      <MaterialModal params={materialModalParams} />
      <CollectMessageModal
        params={collectMessageParams}
        onAddMessage={(data) => {
          onAddMessage(data.content)
        }}
      />
      <UpdateSessionModal params={updateSessionParams} />
      <SwitchSessionModal params={switchSessionParams} />
      <WorkOrderModal params={workOrderParams} />
      <CollaborativeInviteModal params={collaborativeInviteParams} />
    </div>
  )
}

export default AgentSeat
