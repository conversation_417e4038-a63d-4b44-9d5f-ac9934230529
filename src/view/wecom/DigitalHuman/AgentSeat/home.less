.AgentSeat-Container {
  min-width: 1328px;

  p {
    margin: 0;
  }

  .globalRow {
    height: 826px;
    display: flex;
    flex-direction: row;
    background: #fff;

    // 左侧边栏
    .wi-chat-left-sidebar {
      width: 300px;
      min-width: 300px;
      height: 100%;
      background: #fff;
      display: flex;
      flex-direction: column;

      .ant-card-head {
        padding: 0 14px;
        height: 57px;

        .ant-card-head-wrapper {
          height: 100%;
        }

        .ant-select:not(.ant-select-disabled):hover .ant-select-selector,
        .ant-select-focused:not(.ant-select-disabled) .ant-select:not(.ant-select-customize-input) .ant-select-selector,
        .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
          box-shadow: unset !important;
          border-color: #fff !important;
        }
      }

      .ant-card-body {
        padding: 0;
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow-y: scroll;

        /*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
        &::-webkit-scrollbar {
          width: 6px;
        }

        /*定义滚动条的轨道颜色、内阴影及圆角*/
        &::-webkit-scrollbar-track {
          border-radius: 6px;
        }

        /*定义滑块颜色、内阴影及圆角*/
        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background-color: #c0c0c0;

          &:hover {
            background-color: #7c7c7c;
          }
        }

        .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
          padding: 0 10px 0 16px;
        }

        .ant-list {

          .ant-list-items {
            // min-height: 200px;

            .ant-list-item {
              cursor: pointer;
              border-radius: 6px;
              padding: 12px 8px;

              &:hover {
                background-color: #d9d8d8;
              }

              &.selected {
                background-color: #c5c4c5;
              }

              .ant-list-item-meta-content {
                .ant-list-item-meta-title {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .name {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    flex: 1;
                  }

                  .last-updated {
                    color: #9aa8bd;
                    font-size: 12px;
                  }
                }

                .ant-list-item-meta-description {
                  display: flex;

                  span {
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #928f8f;
                    flex: 1;
                  }
                }
              }

              .ant-dropdown {
                pointer-events: unset !important;
              }
            }
          }
        }

      }

      .ant-card-actions {
        li {
          color: rgba(0, 0, 0, 0.85);

          span:hover {
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
    }

    // 会话盒子
    .wi-chat-session-box {
      flex: 1;

      // 中间会话内容
      .wi-chat-session {
        height: 826px;
        display: flex;

        .wi-chat-content {
          width: 550px;
          border-top: unset;
          border-bottom: unset;
          padding: 0;
          display: flex;
          flex-direction: column;

          .ant-card-head {
            padding: 0 14px;
            height: 57px;

            .ant-card-head-wrapper {
              height: 100%;
            }

            .ant-typography {
              margin: unset;
              width: 200px;
            }

            .ant-card-head-title,
            .ant-card-extra {
              font-weight: bold;
              font-size: 14px;

              .ant-avatar {
                width: 36px;
                height: 36px;
              }
            }
          }

          .ant-card-body {
            padding: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            background: #f5f5f5;
          }

          .wi-body {
            display: flex;
            flex: 1 1 0%;
            margin: 0 0 7px;

            .wi-messages-list {
              display: block;
              width: 100%;
              height: 425px;
              background-color: #f5f5f5;

              .wi-messages-dialog-list {
                .ant-collapse-item {
                  .ant-collapse-header {
                    background: #d1d1d1;
                    // position: sticky;
                    // top: 0px;
                    // z-index: 11;
                  }
                }
              }

              .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content {
                &:hover {
                  background-color: #edebeb;
                }
              }
            }
          }

          .wi-quickReply-list {
            margin: 0 14px;
          }

          .wi-footer {
            margin: 7px 14px 14px;
            padding: 14px;
            padding-top: 5px;
            border: 1px solid #cccccc;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;

            .wi-footer-top-bar {
              position: relative;
              text-align: left;
              display: flex;
              justify-content: space-between;

              .anticon {
                font-size: 20px;
                margin-right: 10px;
                color: #6f7072;
                cursor: pointer;

                &:hover {
                  color: #333;
                }
              }

              .right-fun-ba {
                .anticon {
                  margin-left: 10px;
                }
              }
            }

            .ant-form {
              flex: 0.9;

              .ant-input {
                padding: 0;

                &:focus {
                  box-shadow: unset !important;
                }
              }
            }

            .wi-footer-operate-box {
              position: relative;
              display: flex;
              flex-direction: row-reverse;

              .ant-btn {
                border-radius: 6px;
              }
            }

            .mask {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              z-index: 1000;
              height: 100%;
              background-color: rgba(0, 0, 0, 0.45);
              border-radius: 6px;
              -webkit-overflow-scrolling: touch;

              .ant-space {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
              }
            }
          }
        }
      }

      // 右侧边栏
      .wi-chat-right-tabs {
        width: 100%;
        flex: 1;
        background: #fff;
        padding: 8px 14px;

        .ant-tabs {
          height: 100%;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
            }
          }
        }
      }
    }
  }

  .noInfo {
    position: relative;
    flex: 1;
    border-left: 1px solid #f0f0f0;

    .ant-empty-normal {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: #fff;
  }
}