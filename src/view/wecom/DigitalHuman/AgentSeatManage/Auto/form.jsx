/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/29 15:45
 * @LastEditTime: 2024/09/11 17:39
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/Auto/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select,
  DatePicker, Spin, Card, Button, Space, message, TimePicker, Checkbox, InputNumber, TreeSelect } from 'antd';
import { PlusOutlined, QuestionCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import moment from 'moment';
import MaterialListForm from 'components/MaterialListForm/home';
import SysDictRadio from 'components/select/SysDictRadio';
import './form.less';
import { value } from "text-loader"
import { isV1 } from "config"
import SysDictSelect from "components/select/SysDictSelect"

const { RangePicker } = DatePicker;
const { TextArea } = Input;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const layoutList = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  }
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};

const numberValidator = (rule, value) => {
  if (typeof value === "number") {
    return Promise.resolve();
  }
  return Promise.reject('请输入一个数字！');
}

const MessageAutoForm = (props) => {
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [msgList, setMsgList] = useState([]);
  const [strategyType, setStrategyType] = useState('AUTOMATIC_REPLY');
  const [manageEmployeeOption, setManageEmployeeOption] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    getAgentSeatRoutingRuleOptions();
    getManageEmployeeOption();
    queryHasFirstReply(id);
    if (id) {
      setId(id)
      getInfoData(id);
    } else {
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: 'copyWriter',
          content: '',
        },
      ]);
    }
  }, []);

  const [disabledKeys, setDisabledKeys] = useState([])
  const queryHasFirstReply = (id) => {
    apiCall('/agentSeat/msgAuto/existFirstNoReply', 'GET').then(isExist => {
      if (isExist && !id) {
        setDisabledKeys(['FIRST_NO_REPLY'])
      }
    })
  }
  // 查询所有座席
  const getAgentGroupOptions = () => {
    apiCall("/agentSeat/agentGroup/options", "GET")
      .then((res) => {
        setClassifyMenu(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getAgentSeatRoutingRuleOptions = () => {
    apiCall("/agentSeat/routingRule", "GET", { paged: false })
      .then((res) => {
        const { records } = res;
        setClassifyMenu(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getManageEmployeeOption = () => {
    setLoading(true);
    apiCall("/device/deviceGroup/listDeviceGroupTree", "GET", { paged: false, type: "agentSeat" }).then((res) => {
      setManageEmployeeOption(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/agentSeat/msgAuto/${id}`, 'GET').then((res) => {
      const { name, routingRuleIdList, manageEmployeeIdList, strategy } = res;
      setStrategyType(strategy.type)
      formForm.setFieldsValue({
        name,
        routingRuleIdList,
        manageEmployeeIdList,
        strategy: {
          ...strategy,
          specificDate: strategy.startTime && strategy.endTime && [moment(strategy.startTime, 'YYYY-MM-DD HH:mm:ss'), moment(strategy.endTime, 'YYYY-MM-DD HH:mm:ss')] || null,
          dateList: strategy.dateList?.split(",") || [],
          time: (strategy.minTime && strategy.maxTime) && [moment(strategy.minTime, 'HH:mm'), moment(strategy.maxTime, 'HH:mm')] || null
        },
      });
      onRefMaterialListForm.current.getInitMsgList(strategy.list);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      const { strategy } = formData;
      if (!formData.routingRuleIdList.length && !formData.manageEmployeeIdList.length) {
        message.error("请选择“分流规则”或“接管账号”以确定规则生效范围！")
        return false
      }
      if (strategy.type == 'AUTOMATIC_REPLY' && (!strategy.weekdayList || !strategy.weekdayList.length) && (!strategy.dateList || !strategy.dateList.length) && !strategy.time) {
        message.error("自动回复的规则中，特定星期、特定日期、特定时段三者不能同时为空")
        return false
      }
      setLoading(true);
      if (strategy.time) {
        formData.strategy = {
          ...strategy,
          minTime: moment(strategy.time[0]._d).format('HH:mm'),
          maxTime: moment(strategy.time[1]._d).format('HH:mm'),
        }
      }
      if (strategy.specificDate) {
        strategy.startTime = moment(strategy.specificDate[0]._d).format('YYYY-MM-DD HH:mm:ss')
        strategy.endTime = moment(strategy.specificDate[1]._d).format('YYYY-MM-DD HH:mm:ss')
        delete strategy.specificDate
      }

      formData.strategy.dateList = strategy.dateList?.join(",") || "";
      const data = {
        ...formData,
        type: strategy.type,
        strategy: {
          ...formData.strategy,
          list: onRefMaterialListForm.current.getModifyMsgList(),
        },
        enable: id ? null : true
      }
      const apiUrl = id ? `/agentSeat/msgAuto/update/${id}` : `/agentSeat/msgAuto`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        // clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };
  const validateRange = (rule, value, cb) => {
    if (!value) {
      return Promise.resolve()
    }
    if (value[1].diff(value[0], "days") <= 7) {
      return Promise.resolve()
    }else {
      return Promise.reject(new Error('特定日期范围不能超过7天'))
    }
  }
  const [dates, setDates] = useState(null);
  const disabledDate = (current) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 7;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 7;
    return !!tooEarly || !!tooLate;
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='MessageAuto-Form-Container'>
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + '自动化'}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="name"
              label="名称"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入名称" allowClear maxLength={50} />
            </Form.Item>

            <Form.Item
              name="routingRuleIdList"
              label="分流规则"
              rules={[{ required: false, message: '请选定自动化规则生效的分流规则范畴' }]}
              initialValue={[]}
            >
              <Select
                mode="multiple"
                placeholder="请选定自动化规则生效的分流规则范畴"
                fieldNames={{ label: 'name', value: 'id' }}
                options={classifyMenu}
                showSearch
                showArrow
                maxTagCount="responsive"
                filterOption={(input, option) =>
                  (option?.name ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item
              name="manageEmployeeIdList"
              label="接管账号"
              rules={[{ required: false, message: '请选定自动化规则生效的接管账号范畴' }]}
              extra="请在“分流规则”和“接管账号”中选择其一圈定生效范围后保存。若同时选择两者，需同时满足才会触发。"
              initialValue={[]}
            >
              <TreeSelect
                showSearch
                showArrow
                placeholder="请选定自动化规则生效的接管账号范畴"
                allowClear
                multiple
                treeDefaultExpandAll
                showCheckedStrategy={TreeSelect.SHOW_ALL}
                fieldNames={{ label: 'title', value: 'key', children: 'children' }}
                treeNodeFilterProp='title'
                treeData={manageEmployeeOption}
                maxTagCount="responsive"
              />
            </Form.Item>

            <Form.Item
              label="类型"
              required
              tooltip={isV1() ? <div>
                <div>1. 当对话同时满足多条规则时，系统将按照“关键词回复> 自动回复 > 首条未读回复” 的优先级顺序，取首个条件完全匹配的规则进行回复。</div>
                <div>2. 首条未读回复规则，仅允许创建一条。</div>
              </div>: null}
            // tooltip={{ title: '针对待处理对话的自动回复规则。', icon: <QuestionCircleOutlined /> }}
            >
              <Form.Item
                name={['strategy', 'type']}
                rules={[{ required: true, message: '请选择类型' }]}
                initialValue={'AUTOMATIC_REPLY'}
                style={{ marginBottom: '0' }}
              >
                {
                  isV1() ?
                    <SysDictRadio dataset="REPLY_TYPE_V1" onChange={(e) => {
                      const { value } = e.target;
                      setStrategyType(value)
                    }} />:
                    <SysDictRadio dataset="REPLY_TYPE" onChange={(e) => {
                      const { value } = e.target;
                      setStrategyType(value)
                    }} />
                }
              </Form.Item>

              <div className='strategyList'>

                {
                  strategyType == 'AUTOMATIC_REPLY' && <>

                    <Form.Item
                      {...layoutList}
                      name={['strategy', 'weekdayList']}
                      label="特定星期"
                    >
                      <Checkbox.Group>
                        <Checkbox value={1}>
                          星期一
                        </Checkbox>
                        <Checkbox value={2}>
                          星期二
                        </Checkbox>
                        <Checkbox value={3}>
                          星期三
                        </Checkbox>
                        <Checkbox value={4}>
                          星期四
                        </Checkbox>
                        <Checkbox value={5}>
                          星期五
                        </Checkbox>
                        <Checkbox value={6}>
                          星期六
                        </Checkbox>
                        <Checkbox value={7}>
                          星期天
                        </Checkbox>
                      </Checkbox.Group>
                    </Form.Item>

                    <Form.Item
                      {...layoutList}
                      name={['strategy', 'dateList']}
                      label={"特定日期"}
                      extra="日期请使用固定格式“年-月-日”，例如：2023-07-06。"
                    >
                      <Select
                        placeholder={"请输入特定日期"}
                        allowClear
                        showSearch
                        showArrow
                        mode="tags"
                        maxTagCount="responsive"
                        optionFilterProp="name"
                      />
                    </Form.Item>

                    <Form.Item
                      {...layoutList}
                      name={['strategy', 'time']}
                      label={"特定时段"}
                      extra="支持跨天设置，例如20:00～08:00。"
                    >
                      <TimePicker.RangePicker format="HH:mm" order={false} />
                    </Form.Item>

                    <Form.Item
                      {...layoutList}
                      name={['strategy', 'waitTime']}
                      label={"等待时间"}
                      tooltip={{ title: '触发自动回复前的等待时间。', icon: <QuestionCircleOutlined /> }}
                      initialValue={0}
                    >
                      <InputNumber
                        min={0}
                        max={120}
                        addonAfter="分钟"
                        // formatter={value => `${value}分钟`}
                        // parser={value => (value || '0')?.replace('分钟', '')}
                      />
                    </Form.Item>

                    <Form.Item
                      {...layoutList}
                      name={['strategy', 'coolDownTime']}
                      label={"冷却时间"}
                      tooltip={{ title: '两次自动回复之间的最小间隔。', icon: <QuestionCircleOutlined /> }}
                      initialValue={0}
                    >
                      <InputNumber
                        min={0}
                        max={120}
                        addonAfter="分钟"
                        // formatter={value => `${value}分钟`}
                        // parser={value => (value || '0')?.replace('分钟', '')}
                      />
                    </Form.Item>
                  </>
                }
                {
                  strategyType == 'KEY_WORD_REPLY' && <>
                    <Form.List name={['strategy', 'keys']} initialValue={[""]}>
                      {(fields, { add, remove }, { errors }) => (
                        <>
                          {fields.map((field, index) => (
                            <Form.Item
                              {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                              label={index === 0 ? '关键词' : ''}
                              required
                              key={field.key}
                              tooltip="仅当完全匹配时才会触发回复！"
                            >
                              <Form.Item
                                {...field}
                                rules={[{ required: true, message: "请输入关键词" }]}
                                noStyle
                              >
                                <TextArea allowClear style={{ width: '80%' }} />
                              </Form.Item>
                              {fields.length > 1 && (
                                <MinusCircleOutlined
                                  className="dynamic-delete-button"
                                  onClick={() => remove(field.name)}
                                />
                              )}
                            </Form.Item>
                          ))}
                          <Form.Item
                            {...formItemLayoutWithOutLabel}
                          >
                            <Button
                              type="primary"
                              onClick={() => add()}
                              icon={<PlusOutlined />}
                            >
                              添加关键词
                            </Button>
                          </Form.Item>
                        </>
                      )}
                    </Form.List>
                  </>
                }
                {
                  isV1() && strategyType == 'FIRST_NO_REPLY' && <>
                    <Form.Item name={['strategy', 'specificDate']} label="特定时段" required {...layoutList} rules={[{
                      type: 'array',
                      required: true,
                      message: '请输入特定时段',
                    }]}>
                      <RangePicker disabledDate={disabledDate} onCalendarChange={val => setDates(val)} showTime format={"YYYY-MM-DD HH:mm:ss"} />
                    </Form.Item>
                    <Form.Item name={['strategy', 'waitTime']} label="等待时间" tooltip={'客户主动发起对话，首条消息未读触发回复预设内容前的等待时间'} initialValue={0} required {...layoutList} rules={[{
                      type: 'number',
                      required: true,
                      message: '请输入等待时间',
                      validator: numberValidator
                    }]}>
                      <InputNumber min={0} max={999} precision={0} addonAfter="分钟" />
                    </Form.Item>
                    <Form.Item  name={['strategy', 'noSolveWaitTime']} label="未解决等待时间" tooltip={'同一企微账号针对同一客户，近一条对话客户问题未解决，触发 “首条未读回复” 规则前的等待时间'} initialValue={0} required {...layoutList} rules={[{
                      type: 'number',
                      required: true,
                      message: '请输入未解决等待时间',
                      validator: numberValidator
                    }]}>
                      <InputNumber min={0} max={999} precision={0} addonAfter="小时" />
                    </Form.Item>
                    <Form.Item name={['strategy', 'coolDownTime']} label="冷却时间" tooltip={'同一企微账号针对同一客户，两次首条未读回复的最小间隔时间'} initialValue={0} required {...layoutList} rules={[{
                      type: 'number',
                      required: true,
                      message: '请输入冷却时间',
                      validator: numberValidator
                    }]}>
                      <InputNumber min={0} max={999} precision={0} addonAfter="小时" />
                    </Form.Item>
                  </>
                }
                <Form.Item
                  {...layoutList}
                  name="msgList"
                  label="回复内容"
                  rules={[{
                    required: true,
                    validator: (_, value) => {
                      if (msgList.length) {
                        return Promise.resolve()
                      } else {
                        return Promise.reject(new Error('回复内容不能为空'))
                      }
                    }
                  }]}
                >
                  <MaterialListForm
                    params={{
                      formRef: formForm,
                      menuList: ["copyWriter", 'image', 'material'],
                      materialTabList: [
                        'Article',
                        'pageArticle',
                        'Video',
                        'copyWriter',
                        'MINI_PROGRAM',
                        'Picture',
                        'Poster',
                        'FORM',
                        'Product',
                      ],
                      needScriptFlag: false,
                      isNickname: false,
                      materialAmount: msgList.length,
                      maxLength: 3,
                    }}
                    // 监听回调
                    callback={(params) => {
                      setMsgList(params.data);
                    }}
                    ref={onRefMaterialListForm}
                  />
                </Form.Item>
              </div>
            </Form.Item>
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button onClick={() => handleGoBack()}>取消</Button>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default MessageAutoForm;
