/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/28 10:16
 * @LastEditTime: 2024/09/11 17:39
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/Auto/home.jsx
 * @Description: '自动化'
 */

import React, { useState, useEffect, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  message,
  Select,
  TreeSelect,
} from "antd"
import FilterBar from "components/FilterBar/FilterBar"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import { removeInputEmpty } from "common/regular"
import OperateModal from "components/Modal/OperateModal/index"
import WibotTableTag from "components/WibotTableTag/home"
import ListOperation from "components/ListOperation/home"
import SysDictSelect from "components/select/SysDictSelect"
import SysDictLabel from "components/select/SysDictLabel"
import { usePageCacheLifeCycle } from "common/hooks"
import { isV1 } from "config"

const FormItem = Form.Item

const AgentSeatAuto = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [classifyMenu, setClassifyMenu] = useState([])
  const [manageEmployeeOption, setManageEmployeeOption] = useState([])
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => {
        return isV1() ? (
          <SysDictLabel dataset="REPLY_TYPE_V1" dictkey={value} />
        ) : (
          <SysDictLabel dataset="REPLY_TYPE" dictkey={value} />
        )
      },
    },
    {
      title: "分流规则",
      width: "160px",
      dataIndex: "routingRuleNameList",
      key: "routingRuleNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "接管账号",
      width: "160px",
      dataIndex: "groupOrManageEmployeeNameList",
      key: "groupOrManageEmployeeNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "enable",
      key: "enable",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        switch (value) {
          case true:
            value = "启用"
            break
          case false:
            value = "停用"
            break
        }
        return (
          <Tooltip placement="topLeft" title={value}>
            {value}
          </Tooltip>
        )
      },
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
        ]
        if (record.enable) {
          opts.push({ onClick: () => handleChangeEnable(record), name: "停用" })
        } else {
          opts.push({ onClick: () => handleChangeEnable(record), name: "启用" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    getAgentSeatRoutingRuleOptions()
    getManageEmployeeOption()
    fetchList()
  }, [])
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    },
  })

  // 查询所有座席
  const getAgentGroupOptions = () => {
    apiCall("/agentSeat/agentGroup/options", "GET")
      .then((res) => {
        setClassifyMenu(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const getAgentSeatRoutingRuleOptions = () => {
    apiCall("/agentSeat/routingRule", "GET", { paged: false })
      .then((res) => {
        const { records } = res
        setClassifyMenu(records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const getManageEmployeeOption = () => {
    setLoading(true)
    apiCall("/device/deviceGroup/listDeviceGroupTree", "GET", {
      paged: false,
      type: "agentSeat",
    })
      .then((res) => {
        setManageEmployeeOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      formData.manageEmployeeIdList =
        formData.manageEmployeeIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/agentSeat/msgAuto", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/agentSeatManage/auto/form")
  }

  // 编辑
  const handleEdit = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/agentSeatManage/auto/form",
      search: `?id=${id}`,
    })
  }

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `确定要删除自动化【${name}】吗？`,
      onSubmit: () => {
        apiCall(`/agentSeat/msgAuto/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  // 启用/停用
  const handleChangeEnable = (record) => {
    const { id, enable, name } = record
    setOperateParams({
      visible: true,
      title: enable ? "停用确认" : "启用确认",
      content: `您将为【${name}】进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          enable: !enable,
        }
        apiCall(`/agentSeat/msgAuto/update/enable/${id}`, "POST", data)
          .then((res) => {
            message.success("修改成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="AgentSeatManage-Auto-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="名称" allowClear />
          </FormItem>

          <FormItem name="routingRuleIdList">
            <Select
              placeholder="分流规则"
              fieldNames={{ label: 'name', value: 'id' }}
              options={classifyMenu}
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>

          <FormItem name="type">
            {
              isV1() ? (
                <SysDictSelect placeholder="类型" dataset="REPLY_TYPE_V1" disabledKey={'FIRST_NO_REPLY'} />
              ) : (
                <SysDictSelect placeholder="类型" dataset="REPLY_TYPE" />
              )
            }
          </FormItem>

          <FormItem name="manageEmployeeIdList">
            <TreeSelect
              showSearch
              showArrow
              placeholder="接管账号"
              allowClear
              multiple
              treeDefaultExpandAll
              showCheckedStrategy={TreeSelect.SHOW_ALL}
              fieldNames={{
                label: "title",
                value: "key",
                children: "children",
              }}
              treeNodeFilterProp="title"
              treeData={manageEmployeeOption}
              maxTagCount="responsive"
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
    </div>
  )
}

export default withRouter(AgentSeatAuto)
