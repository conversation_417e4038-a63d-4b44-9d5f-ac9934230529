/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/18 10:02
 * @LastEditTime: 2024/11/06 10:44
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/Config/home.jsx
 * @Description: '通用配置'
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { apiCall } from 'common/utils';
import { Form, Button, message, Spin, TreeSelect, Space, Card, Input, Divider, InputNumber } from 'antd';
import { MinusCircleOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import './home.less';
import AppStore from 'stores/AppStore';

const { TextArea } = Input;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

const AgentSeatConfig = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [manageEmployeeOption, setManageEmployeeOption] = useState([]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(async () => {
    await getManageEmployeeOption()
    await getAgentConfig()
  }, []);

  const getManageEmployeeOption = async () => {
    setLoading(true);
    await apiCall("/device/deviceGroup/listDeviceGroupTree", "GET").then((res) => {
      setManageEmployeeOption(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getAgentConfig = async () => {
    setLoading(true);
    await apiCall("/globalSetting/agent_config", "GET").then((res) => {
      formForm.setFieldsValue({ ...res })
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const data = {
        ...formData,
      }
      apiCall(`/globalSetting/agent_config`, 'POST', data).then((res) => {
        message.success('保存成功！');
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  return (
    <div className="AgentSeatManage-Config-Container">
      <Spin spinning={loading}>
        <Card
          bordered={false}
          bodyStyle={{ padding: '0' }}
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="manageEmployeeIdList"
              label="接管账号"
              // rules={[{ required: true, message: '请选择接管账号' }]}
              extra={`选中的托管账号的客户消息将由${moduleVersionMap[`agentSeat_title`] === 'v2' ? '客户服务' : '坐席工作台'}统一处理，调整后将在五分钟后生效。`}
            >
              <TreeSelect
                showSearch
                showArrow
                placeholder="请选择接管账号"
                allowClear
                multiple
                treeDefaultExpandAll
                showCheckedStrategy={TreeSelect.SHOW_ALL}
                fieldNames={{ label: 'title', value: 'key', children: 'children' }}
                treeNodeFilterProp='title'
                treeData={manageEmployeeOption}
                maxTagCount="responsive"
              />
            </Form.Item>

            <Form.Item label="协同客户超时未回复" required>
              <div style={{ display: 'flex', alignItems: 'baseline' }}>
                <span>客户超过</span>
                <Form.Item
                  name="collaborationCustomerTimeoutTime"
                  initialValue={30}
                  rules={[{ required: true, message: '请输入超时自动结束协同' }]}
                  style={{ margin: '0 6px' }}
                >
                  <InputNumber min={1} precision={0} />
                </Form.Item>
                <span>分钟不回复时，自动结束协同</span>
              </div>
            </Form.Item>

            <Form.Item label={`协同${agentSeat_title}超时未处理`} required>
              <div style={{ display: 'flex', alignItems: 'baseline' }}>
                <span>{agentSeat_title}超过</span>
                <Form.Item
                  name="collaborationAgentTimeoutTime"
                  initialValue={30}
                  rules={[{ required: true, message: '请输入超时自动结束协同' }]}
                  style={{ margin: '0 6px' }}
                >
                  <InputNumber min={1} precision={0} />
                </Form.Item>
                <span>分钟不回复时，自动结束协同</span>
              </div>
            </Form.Item>

            <Form.Item
              name="timeoutRemindTime"
              label="即将超时回复提醒设置"
              tooltip={{ title: '设置对话超时机制的预警提醒时间', icon: <QuestionCircleOutlined /> }}
              initialValue={0}
              rules={[{ required: true, message: '请输入超时回复提醒时间' }]}
            >
              <InputNumber
                min={0}
                max={120}
                addonAfter="分钟"
              />
            </Form.Item>

            <Form.List name="quickReplyList">
              {(fields, { add, remove }) => (
                <>
                  <Form.Item label="快捷回复">
                    <Button type="primary" onClick={() => {
                      if (fields.length >= 5) {
                        message.warning("快捷回复话术不得超过5条！")
                        return false
                      }
                      add()
                    }} icon={<PlusOutlined />}>
                      文案
                    </Button>
                  </Form.Item>

                  {fields.map((field, index) => (
                    <div key={field.key}>
                      <Form.Item
                        label={'文案'}
                        required
                      >
                        <Space align="flexStart">
                          <Form.Item
                            noStyle
                            name={[field.name]}
                            rules={[{ required: true, message: '请输入200字以内的文案' }]}
                          >
                            <TextArea
                              showCount
                              maxLength={200}
                              allowClear
                              autoSize={{ minRows: 4, maxRows: 6 }}
                              placeholder={'请输入200字以内的文案'}
                            />
                          </Form.Item>

                          {/* {fields.length > 1 ? ( */}
                          <MinusCircleOutlined
                            className="dynamic-delete-button"
                            onClick={() => remove(field.name)}
                          />
                          {/* ) : null} */}
                        </Space>
                      </Form.Item>

                      {fields.length > 1 ? (<Divider />) : null}
                    </div>
                  ))}
                </>
              )}
            </Form.List>

            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Space size={40}>
                <Button type="primary" onClick={() => onSubmit()}>
                  保存
                </Button>
              </Space>
            </div>
          </Form>
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(AgentSeatConfig);
