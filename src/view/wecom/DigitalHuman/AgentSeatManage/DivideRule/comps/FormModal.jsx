/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/04 11:02
 * @LastEditTime: 2024/11/07 17:47
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/DivideRule/comps/FormModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  message,
  Form,
  Input,
  Select,
  Spin,
  TreeSelect,
  Checkbox,
  TimePicker,
  Button,
  Space,
  Divider,
  Switch
} from 'antd'
import { PlusOutlined, CloseCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import moment from 'moment';
import SysDictSelect from 'components/select/SysDictSelect';
import AppStore from 'stores/AppStore';
import { versionFnMap } from "config";
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
};

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};
const { SHOW_PARENT } = TreeSelect;

const FormModal = (props) => {
  const { visible = false, id = null } = props.params;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [assignmentRuleValue, setAssignmentRuleValue] = useState('SEQUENTIAL_PRIORITY');
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";
  const [manageEmployeeOption, setManageEmployeeOption] = useState([]);
  const [existTagNameList, setExistTagNameList] = useState([])
  useEffect(async () => {
    if (visible) {
      await getTagGroupTree()
      await getAgentGroupOptions()
      await getManageEmployeeOption()
      id && getInfoData();
    } else {
      setLoading(false);
      setConfirmLoading(false);
      setAssignmentRuleValue('SEQUENTIAL_PRIORITY');
      formForm.resetFields();
    }
  }, [visible]);

  const getManageEmployeeOption = async () => {
    setLoading(true);
    await apiCall("/device/deviceGroup/listDeviceGroupTree", "GET")
      .then((res) => {
        setManageEmployeeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取坐席组
  const getAgentGroupOptions = () => {
    apiCall("/agentSeat/agentGroup/options", "GET")
      .then((res) => {
        setClassifyMenu(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取客户标签
  const getTagGroupTree = async () => {
    setLoading(true);
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = () => {
    setLoading(true);
    setExistTagNameList([])
    apiCall(`/agentSeat/routingRule/${id}`, 'GET').then((res) => {
      const { name, agentGroupIdList, assignmentRule, customerTagIdList, strategy, deviceIdList, checkAgentSignIn,
        tagNameList} = res;
      setAssignmentRuleValue(assignmentRule)
      console.log(`[tagNameList]: `, tagNameList)
      setExistTagNameList(tagNameList)
      formForm.setFieldsValue({
        name,
        agentGroupIdList,
        assignmentRule,
        customerTagIdList,
        deviceIdList,
        checkAgentSignIn,
        tagNameList,
        strategy: {
          ...strategy,
          dateList: strategy.dateList?.split(",") || [],
          time: (strategy.minTime && strategy.maxTime) && [moment(strategy.minTime, 'HH:mm'), moment(strategy.maxTime, 'HH:mm')] || null
        },
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const { assignmentRule, strategy } = formData;
      if (assignmentRule !== 'CUSTOM') {
        if (strategy.time) {
          formData.strategy.minTime = moment(strategy.time[0]._d).format('HH:mm')
          formData.strategy.maxTime = moment(strategy.time[1]._d).format('HH:mm')
        }
        formData.strategy.dateList = strategy.dateList?.join(",") || "";
      }
      const data = {
        ...formData,
        strategy: {
          ...formData.strategy,
        },
        enable: id ? null : true
      }
      console.log(formData, 'formData');
      console.log(data, 'data');
      // return false;
      const apiUrl = id ? `/agentSeat/routingRule/update/${id}` : `/agentSeat/routingRule`
      apiCall(apiUrl, 'POST', data)
        .then((res) => {
          message.success(id ? '编辑成功！' : '新增成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    })
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AgentSeatManage-DivideRule-FormModal"
      title={'新增分流规则'}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form
          {...layout}
          form={formForm}
        >
          <Form.Item
            name="name"
            label="规则名称"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: '请输入规则名称' }]}
          >
            <Input placeholder="请输入规则名称" allowClear maxLength={50} />
          </Form.Item>

          <Form.Item
            name="assignmentRule"
            label="分配规则"
            rules={[{ required: true, message: '请选择分配规则' }]}
            tooltip={{
              title: `组间轮流分配：对话按可分配${agentSeat_title}组顺序轮流分配，分配${agentSeat_title}遵循组内规则。

${agentSeat_title}轮流分配：规则内所有${agentSeat_title}组可分配${agentSeat_title}轮流分配对话。

顺序优先分配：对话按${agentSeat_title}组添加顺序优先分配，仅当顺序在前的${agentSeat_title}组工作量饱和时，对话才会流入下一个可分配${agentSeat_title}组，分配${agentSeat_title}遵循组内规则。

${versionFnMap.routingRule_assignmentRule_customFlag() ? '自定义分配：对话按自定义映射表中员工和客户的关系进行分配，如果找不到客户对应的员工或者客户对应的员工不在签入状态时，则按优先级继续匹配其他分流规则。' : ''}
`
            }}
            initialValue={'SEQUENTIAL_PRIORITY'}
          >
            <SysDictSelect placeholder="请选择分配规则" dataset="ASSIGNMENT_RULE" exclude={moduleVersionMap[`routingRule_assignmentRule_customFlag`] === 'v2' ? [] : ['CUSTOM']} onChange={(value) => {
              setAssignmentRuleValue(value)
            }} />
          </Form.Item>

          <Form.Item
            name="checkAgentSignIn"
            label={`校验${agentSeat_title}状态`}
            rules={[{ required: false, message: '请选择校验规则范围' }]}
            tooltip={{
              title: `若分流规则中的处理${agentSeat_title}均不在线，该规则将失效，对话会按顺序匹配后续分流规则。`
            }}
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          {
            assignmentRuleValue === 'CUSTOM' ? null : <>
              <Form.Item
                name="agentGroupIdList"
                label={`处理${agentSeat_title}组`}
                rules={[{ required: true, message: `请选择处理${agentSeat_title}组` }]}
              >
                <Select
                  mode="multiple"
                  placeholder={`请选择处理${agentSeat_title}组`}
                  fieldNames={{ label: 'name', value: 'id' }}
                  options={classifyMenu}
                  allowClear
                  showSearch
                  showArrow
                  filterOption={(input, option) =>
                    (option?.name ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                name="deviceIdList"
                label="接管账号"
                // rules={[{ required: true, message: '请选择接管账号' }]}
              >
                <TreeSelect
                  showSearch
                  showArrow
                  placeholder="请选择接管账号"
                  allowClear
                  multiple
                  treeDefaultExpandAll
                  showCheckedStrategy={TreeSelect.SHOW_ALL}
                  fieldNames={{label: 'title', value: 'key', children: 'children'}}
                  treeNodeFilterProp='title'
                  treeData={manageEmployeeOption}
                  maxTagCount="responsive"
                />
              </Form.Item>
              {/* <Form.Item */}
              {/*   name="customerTagIdList" */}
              {/*   label="客户标签" */}
              {/* > */}
              {/*   <TreeSelect */}
              {/*     treeData={labelTreeData} */}
              {/*     treeCheckable */}
              {/*     treeDefaultExpandedKeys={[""]} */}
              {/*     allowClear */}
              {/*     showArrow */}
              {/*     showSearch */}
              {/*     treeNodeFilterProp="title" */}
              {/*     fieldNames={{ label: 'title', value: 'key' }} */}
              {/*     maxTagCount="responsive" */}
              {/*     showCheckedStrategy={SHOW_PARENT} */}
              {/*     placeholder="请选择客户标签" */}
              {/*   /> */}
              {/* </Form.Item> */}
              <CustomTagSelect
                label="客户标签"
                name="tagNameList"
                placeholder="客户标签"
                useForm={formForm}
                existTagNameList={existTagNameList}
                labelTreeData={labelTreeData}
              />
              <Form.Item
                name={['strategy', 'weekdayList']}
                label="特定星期"
              >
                <Checkbox.Group>
                  <Checkbox value={1}>
                    星期一
                  </Checkbox>
                  <Checkbox value={2}>
                    星期二
                  </Checkbox>
                  <Checkbox value={3}>
                    星期三
                  </Checkbox>
                  <Checkbox value={4}>
                    星期四
                  </Checkbox>
                  <Checkbox value={5}>
                    星期五
                  </Checkbox>
                  <Checkbox value={6}>
                    星期六
                  </Checkbox>
                  <Checkbox value={7}>
                    星期天
                  </Checkbox>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item
                name={['strategy', 'dateList']}
                label={"特定日期"}
                extra="日期请使用固定格式“年-月-日”，例如：2023-07-06。"
                initialValue={[]}
              >
                <Select
                  placeholder={"请输入特定日期"}
                  allowClear
                  showSearch
                  showArrow
                  mode="tags"
                  maxTagCount="responsive"
                  optionFilterProp="name"
                />
              </Form.Item>

              <Form.Item
                name={['strategy', 'time']}
                label={"特定时段"}
                extra="支持跨天设置，例如20:00～08:00。"
              >
                <TimePicker.RangePicker format="HH:mm" order={false} />
              </Form.Item>
            </>
          }

          {
            (versionFnMap.routingRule_icbcCustomerTagFlag() && formForm.getFieldValue('assignmentRule') !== 'CUSTOM')
                ? <Form.List name={['strategy', 'icbcTagList']}>
            {(fields, { add, remove }) => (
              <>
                <Form.Item label="工行客户标签">
                  <Button type="primary" onClick={() => {
                    add()
                  }} icon={<PlusOutlined />}>
                    添加标签
                  </Button>
                </Form.Item>

                {fields.map((field, index) => (
                  <div key={field.key}>
                    <Form.Item
                      label=' '
                      colon={false}
                      style={{ margin: "0" }}
                    >
                      <Space align="flexStart">
                        <Form.Item
                          name={[field.name, 'tagName']}
                          rules={[{ required: true, message: '请输入标签名称' }]}
                        >
                          <Input allowClear placeholder={'请输入标签名称'} />
                        </Form.Item>

                        <Form.Item
                          name={[field.name, 'tagValueText']}
                          rules={[{ required: true, message: '请输入标签值' }]}
                        >
                          <Input allowClear placeholder="请输入标签值" />
                        </Form.Item>

                        <MinusCircleOutlined
                          className="dynamic-delete-button"
                          onClick={() => remove(field.name)}
                        />
                      </Space>
                    </Form.Item>
                  </div>
                ))}
              </>
            )}
          </Form.List>
                : null}
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
