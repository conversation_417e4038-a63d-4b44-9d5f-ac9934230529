/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/04 10:47
 * @LastEditTime: 2024/11/06 10:49
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/DivideRule/home.jsx
 * @Description: '分流规则'
 */

import React, { useState, useEffect, useRef, useCallback } from "react";
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Table,
  Tooltip,
  message,
  Typography,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { getWeekday } from "common/date";
import OperateModal from "components/Modal/OperateModal/index";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import FormModal from "./comps/FormModal.jsx"
import { MenuOutlined } from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import SysDictLabel from 'components/select/SysDictLabel';
import AppStore from 'stores/AppStore';

const { Text } = Typography;

const type = 'DraggableBodyRow';
const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const AgentSeatDivideRule = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [formParams, setFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (value, record, index) => <>
        <MenuOutlined />
        <span style={{ marginLeft: '10px' }}>{index + 1}</span>
      </>,
    },
    {
      title: "规则名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "enable",
      key: "enable",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        switch (value) {
          case true:
            value = "启用";
            break;
          case false:
            value = "停用";
            break;
        }
        return <Tooltip placement="topLeft" title={value}>{value}</Tooltip>;
      },
    },
    {
      title: `处理坐席组`,
      width: "160px",
      dataIndex: "agentGroupNameList",
      key: "agentGroupNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '分配规则',
      width: '160px',
      dataIndex: 'assignmentRule',
      key: 'assignmentRule',
      align: 'center',
      render: (value, record, index) => (<SysDictLabel dataset="ASSIGNMENT_RULE" dictkey={value} />),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "工行客户标签",
      width: "160px",
      dataIndex: "icbcTagList",
      key: "icbcTagList",
      align: "center",
      render: (value, record, index) => {
        const { icbcTagList } = record.strategy
        return <WibotTableTag tagList={icbcTagList.map((item) => `${item.tagName}：${item.tagValueText}`)} />
      },
    },
    {
      title: "特定星期",
      width: "160px",
      dataIndex: "strategy",
      key: "strategy",
      align: "center",
      render: (value, record, index) => (value.weekdayList?.sort()?.map((item) => getWeekday(item, '星期')).join('、')),
    },
    {
      title: "特定日期",
      width: "160px",
      dataIndex: "strategy",
      key: "strategy",
      align: "center",
      render: (value, record, index) => (value.dateList?.split(",").join("、")),
    },
    {
      title: "特定时间段",
      width: "160px",
      dataIndex: "strategy",
      key: "strategy",
      align: "center",
      render: (value, record, index) => (value.minTime ? <>{value.minTime}~{value.maxTime}</> : ''),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        if (record.enable) {
          opts.push({ onClick: () => handleChangeEnable(record), name: "停用" });
        } else {
          opts.push({ onClick: () => handleChangeEnable(record), name: "启用" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;

  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      const params = {
        list: data.map((item) => item.id),
        current: paginations.current,
        size: paginations.pageSize,
      }
      apiCall('/agentSeat/routingRule/sort', 'POST', params).then((res) => {
        message.success('修改成功！');
        setDataSource(
          update(dataSource, {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, dragRow],
            ],
          }),
        );
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource],
  );

  useEffect(() => {
    if (moduleVersionMap[`routingRule_assignmentRule_customFlag`] === 'v2') {
      columns.splice(6, 1)
      setColumns([...columns])
    }
    if (moduleVersionMap[`agentSeat_title`] === 'v2') {
      columns[3].title = '处理客服组'
      setColumns([...columns])
    }
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      paged: false,
      ...query,
    };
    apiCall("/agentSeat/routingRule", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    setFormParams({
      visible: true,
      onSubmit: (data) => {
        setFormParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id: id,
      onSubmit: (data) => {
        setFormParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `确定要删除规则【${name}】吗？`,
      onSubmit: () => {
        apiCall(`/agentSeat/routingRule/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  // 启用/停用
  const handleChangeEnable = (record) => {
    const { id, enable, name } = record;
    setOperateParams({
      visible: true,
      title: enable ? "停用确认" : "启用确认",
      content: `您将为【${name}】进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          enable: !enable,
        };
        apiCall(`/agentSeat/routingRule/update/enable/${id}`, "POST", data)
          .then((res) => {
            message.success("保存成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="AgentSeatManage-DivideRule-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <div className="flex flex-space-between">
          <div>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>

      <Text type="secondary">符合以下多条分流规则情形，默认按列表顺序执行首条命中分流规则。</Text>

      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <DndProvider backend={HTML5Backend}>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={false}
            onChange={onChangeTable}
            components={components}
            onRow={(record, index) => ({
              index,
              moveRow,
            })}
          />
        </DndProvider>
      </Card>
      <OperateModal params={operateParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default withRouter(AgentSeatDivideRule);
