/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/15 16:25
 * @LastEditTime: 2024/11/06 10:51
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/Group/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Spin, Card, Button, Space, message, InputNumber } from 'antd'; import { QuestionCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SysDictSelect from 'components/select/SysDictSelect';
import AppStore from 'stores/AppStore';

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

const AgentSeatManageForm = (props) => {
  const [formForm] = Form.useForm();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);;
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id)
      getInfoData(id);
    }
  }, []);

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/agentSeat/agentGroup/${id}`, 'GET').then((res) => {
      formForm.setFieldsValue({ ...res });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const data = {
        ...formData,
      }
      const apiUrl = id ? `/agentSeat/agentGroup/update/${id}` : `/agentSeat/agentGroup`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        // clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='AgentSeatManage-Group-Form-Container'>
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + `${agentSeat_title}组`}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="name"
              label="名称"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入名称" allowClear maxLength={50} />
            </Form.Item>

            <Form.Item
              name="depEmpList"
              label={`${agentSeat_title}人员`}
              tooltip={{ title: `${agentSeat_title}人员只能同时存在一个${agentSeat_title}组之中`, icon: <QuestionCircleOutlined /> }}
              rules={[{ required: true, message: `请选择${agentSeat_title}人员` }]}
            >
              <ETypeTransferModal
                title={`${agentSeat_title}人员`}
                needExcludeDepFlag={false}
                onlyEmployee
              />
            </Form.Item>

            <Form.Item
              name="rule"
              label="分配规则"
              rules={[{ required: true, message: '请选择分配规则' }]}
            >
              <SysDictSelect dataset="AGENT_GROUP_RULE" placeholder="请选择分配规则" />
            </Form.Item>

            <Form.Item
              name="conversationLimit"
              label="单人同时处理量"
              initialValue={20}
              rules={[{ required: true, message: '请输入单人同时处理量' }]}
            >
              <InputNumber min={1} />
            </Form.Item>

            <Form.Item label="自动结束对话" required>
              <div style={{ display: 'flex', alignItems: 'baseline' }}>
                <span>客户超过</span>
                <Form.Item
                  name="autoCloseTime"
                  initialValue={30}
                  rules={[{ required: true, message: '请输入自动结束对话' }]}
                  style={{ margin: '0 6px' }}
                >
                  <InputNumber min={1} precision={0} />
                </Form.Item>
                <span>分钟不回复时，自动结束对话</span>
              </div>
            </Form.Item>

            <Form.Item label="超时重新分配" required>
              <div style={{ display: 'flex', alignItems: 'baseline' }}>
                <span>{agentSeat_title}超过</span>
                <Form.Item
                  name="autoDistributionTime"
                  initialValue={30}
                  rules={[{ required: true, message: '请输入超时重新分配' }]}
                  style={{ margin: '0 6px' }}
                >
                  <InputNumber min={1} precision={0} />
                </Form.Item>
                <span>分钟不回复时，重新分配对话</span>
              </div>
            </Form.Item>
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button onClick={() => handleGoBack()}>取消</Button>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default AgentSeatManageForm;
