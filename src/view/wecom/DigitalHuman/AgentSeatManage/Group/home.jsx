/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/12 09:32
 * @LastEditTime: 2024/11/06 10:53
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/Group/home.jsx
 * @Description: '坐席组'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  message,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { removeInputEmpty } from "common/regular";
import OperateModal from "components/Modal/OperateModal/index";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import SysDictLabel from 'components/select/SysDictLabel';
import AppStore from 'stores/AppStore';
import {usePageCacheLifeCycle} from "common/hooks";

const AgentSeatGroup = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "坐席人员",
      width: "160px",
      dataIndex: "employeeNameList",
      key: "employeeNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "分配规则",
      width: "160px",
      dataIndex: "rule",
      key: "rule",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="AGENT_GROUP_RULE" dictkey={value} />
      ),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (moduleVersionMap[`agentSeat_title`] === 'v2') {
      columns[2].title = '客服人员'
      setColumns([...columns])
    }
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.employeeId = formData.employeeId?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/agentSeat/agentGroup", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/agentSeatManage/group/form");
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/agentSeatManage/group/form",
      search: `?id=${id}`,
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `确定要删除${agentSeat_title}组【${name}】吗？`,
      onSubmit: () => {
        apiCall(`/agentSeat/agentGroup/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="AgentSeatManage-Group-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="名称" allowClear />
          </Form.Item>

          <Form.Item
            name="employeeId"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal
              onlyEmployee
              multiple={false}
              needExcludeDepFlag={false}
              title={`${agentSeat_title}人员`}
            />
          </Form.Item>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
    </div>
  );
};

export default withRouter(AgentSeatGroup);
