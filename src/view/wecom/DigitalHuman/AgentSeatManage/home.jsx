/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/18 09:25
 * @LastEditTime: 2024/11/06 10:38
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AgentSeatManage/home.jsx
 * @Description: '坐席配置'
 */

import React, { useEffect, useState } from "react";
import { Card, Tabs } from "antd";
// 模块组件
import Group from "./Group/home";
import Config from "./Config/home";
import Auto from "./Auto/home";
import DivideRule from "./DivideRule/home";
import AppStore from 'stores/AppStore';

const { TabPane } = Tabs;

const AgentSeatManage = (props) => {
  const [tabsIndex, setTabsIndex] = useState("");
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    setTabsIndex(localStorage.getItem("AgentSeatManageTabsIndex") || "1");
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem("AgentSeatManageTabsIndex", index);
    setTabsIndex(index);
  };

  return (
    <div className="AgentSeatManage-Container">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          <TabPane tab={agentSeat_title + '组'} key="1">
            <Group />
          </TabPane>
          <TabPane tab="通用配置" key="2">
            <Config />
          </TabPane>
          <TabPane tab="分流规则" key="4">
            <DivideRule />
          </TabPane>
          <TabPane tab="自动化" key="3">
            <Auto />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AgentSeatManage;
