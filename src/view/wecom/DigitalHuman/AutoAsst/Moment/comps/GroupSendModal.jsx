/*
 * @Author: <PERSON>ei<PERSON>
 * @Date: 2024/03/29 14:48
 * @LastEditTime: 2025/07/04 11:20
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AutoAsst/Moment/comps/GroupSendModal.jsx
 * @Description: '群发弹窗'
 */

import React, { useState, useEffect } from 'react';
import { Modal, message } from 'antd';
import { apiCall } from 'common/utils';
import WibotEditorView from "components/WibotEditorView/home"

const GroupSendModal = (props) => {
  const { visible = false, title = '', describe = '', list = [] } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [uniqueList, setUniqueList] = useState([]);

  useEffect(() => {
    if (visible) {
    } else {
      setConfirmLoading(false);
    }
  }, [visible]);

  useEffect(() => {
    if (list.length) {
      const uniqueData = list.reduce((acc, curr) => {
        const existingItem = acc.find(item => item.momentId === curr.momentId);
        if (existingItem) {
          existingItem.deviceName = Array.isArray(existingItem.deviceName) ? [...new Set([...existingItem.deviceName, curr.deviceName])] : [existingItem.deviceName, curr.deviceName];
          existingItem.deviceId = Array.isArray(existingItem.deviceId) ? [...new Set([...existingItem.deviceId, curr.deviceId])] : [existingItem.deviceId, curr.deviceId];
        } else {
          acc.push({
            ...curr,
            deviceName: [curr.deviceName],
            deviceId: [curr.deviceId]
          });
        }
        return acc;
      }, []);
      setUniqueList([...uniqueData])
    }
  }, [list]);

  const onOk = () => {
    setConfirmLoading(true);
    const data = {
      map: uniqueList.reduce((acc, item) => {
        acc[item.momentId] = item.deviceId;
        return acc;
      }, {})
    };
    apiCall('/moment/momentAuto', 'POST', data)
      .then((res) => {
        message.success('发表成功！');
        props.params?.onSubmit?.();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AutoAsst-GroupSend-Modal"
      title={title}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      okText="执行"
      onCancel={onCancel}
      onOk={onOk}
    >
      <WibotEditorView html={describe} />
      {uniqueList.map((item, index) => (
        <p key={index} style={{ color: "#d5001c" }}>
          <p style={{ margin: "0" }}>{item.momentName}</p>
          <p style={{ margin: "0", textIndent: "3ch" }}>
            {item.deviceName.join("、")}
          </p>
        </p>
      ))}
    </Modal>
  )
};

export default GroupSendModal;
