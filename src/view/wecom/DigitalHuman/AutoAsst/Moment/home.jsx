/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 10:39
 * @LastEditTime: 2025/07/04 14:19
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AutoAsst/Moment/home.jsx
 * @Description: '客户朋友圈'
 */

import {
  Button,
  Card,
  Form,
  Input,
  message,
  Select,
  Table,
  Tooltip,
  Typography,
} from "antd"
import { timeStamp } from "common/date"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import FilterBar from "components/FilterBar/FilterBar"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import React, { useEffect, useRef, useState } from "react"
import GroupSendModal from "./comps/GroupSendModal"

const { Paragraph } = Typography

const AutoAsstMoment = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [deviceGroup, setDeviceGroup] = useState([])
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [groupSendParams, setGroupSendParams] = useState({ visible: false })
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectedRows, setSelectedRows] = useState([])
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "朋友圈名称",
      width: "160px",
      dataIndex: "momentName",
      key: "momentName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "朋友圈内容",
      width: "160px",
      dataIndex: "content",
      key: "content",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <>
          <Paragraph ellipsis style={{ margin: "0" }}>
            {record.copyWriteContent}
          </Paragraph>
          <a onClick={() => handlePreview(record)}>预览</a>
        </>
      ),
    },
    {
      title: "朋友圈发表时间",
      width: "160px",
      dataIndex: "momentExecuteTime",
      key: "momentExecuteTime",
      align: "center",
      sorter: (a, b) =>
        timeStamp(a.momentExecuteTime) - timeStamp(b.momentExecuteTime),
    },
    {
      title: "设备名称",
      width: "160px",
      dataIndex: "deviceName",
      key: "deviceName",
      align: "center",
    },
    {
      title: "登入账号",
      width: "160px",
      dataIndex: "manageEmployeeName",
      key: "manageEmployeeName",
      align: "center",
    },
    {
      title: "账号分组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
    },
    {
      title: "上次执行情况",
      width: "160px",
      dataIndex: "executeTime",
      key: "executeTime",
      align: "center",
      render: (value, record, index) =>
        value ? (
          <>
            {value}
            <br />
            <span
              style={{
                whiteSpace: "break-spaces",
                textAlign: "left",
                display: "inline-block",
              }}
            >
              {record.reason}
            </span>
          </>
        ) : (
          ""
        ),
      sorter: (a, b) => timeStamp(a.executeTime) - timeStamp(b.executeTime),
    },
  ]

  useEffect(() => {
    getDeviceGroup()
    fetchList()
  }, [])

  useEffect(() => {
    if (dataSource.length) {
      setSelectedRows([...dataSource])
      setSelectedRowKeys(dataSource.map((item) => item.uniqueId))
    }
  }, [dataSource])

  // 分组管理
  const getDeviceGroup = async () => {
    await apiCall("/device/deviceGroup", "GET", {
      paged: false,
    })
      .then((res) => {
        const { records } = res
        setDeviceGroup(records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      formData.deviceGroupIdList = formData.deviceGroupIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/moment/momentAuto", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  // 群发
  const handleGroupSend = () => {
    if (selectedRowKeys.length > 10) {
      message.warning("一次最多执行10个设备的任务！")
      return false
    }
    setGroupSendParams({
      visible: true,
      title: "发表朋友圈",
      describe:
        "<p style=margin-top:0px;>即将发表下列朋友圈，该操作无法撤回，确认这样做吗？</p>",
      list: selectedRows,
      onSubmit: (data) => {
        setGroupSendParams({ visible: false })
        fetchList({
          pagination: paginations,
        })
      },
      onCancel: () => {
        setGroupSendParams({ visible: false })
      },
    })
  }

  const handlePreview = (record) => {
    const { content } = record
    setResourcePreviewParams({
      visible: true,
      listData: content,
      type: "moment",
      onCancel: () => {
        setResourcePreviewParams({
          visible: false,
        })
      },
    })
  }

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    console.log(selectedRowKeys, selectedRows, "selectedRowKeys, selectedRows")
    setSelectedRowKeys(selectedRowKeys)
    setSelectedRows(selectedRows)
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  }

  const hasSelected = selectedRowKeys.length > 0

  return (
    <div className="AutoAsst-Moment-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item
            name="deviceName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="设备名称" allowClear />
          </Form.Item>
          <Form.Item
            name="manageEmployeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="登入账号" allowClear />
          </Form.Item>

          <Form.Item name="deviceGroupIdList">
            <Select
              allowClear
              showSearch
              maxTagCount="responsive"
              placeholder="账号分组"
              mode="multiple"
              options={deviceGroup}
              fieldNames={{ label: "name", value: "id" }}
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item
            name="momentName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="朋友圈名称" allowClear />
          </Form.Item>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button
              disabled={!hasSelected}
              type="primary"
              onClick={() => {
                handleGroupSend()
              }}
            >
              发表朋友圈
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="uniqueId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <GroupSendModal params={groupSendParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default AutoAsstMoment
