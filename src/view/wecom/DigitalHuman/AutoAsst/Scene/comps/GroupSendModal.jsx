/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/29 14:48
 * @LastEditTime: 2025/07/04 11:17
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AutoAsst/Scene/comps/GroupSendModal.jsx
 * @Description: '群发弹窗'
 */

import React, { useState, useEffect } from 'react';
import { Modal, message } from 'antd';
import { apiCall } from 'common/utils';
import WibotEditorView from "components/WibotEditorView/home"

const GroupSendModal = (props) => {
  const { visible = false, title = '', describe = '', list = [], type = '' } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
    } else {
      setConfirmLoading(false);
    }
  }, [visible]);

  const onOk = () => {
    setConfirmLoading(true);
    const data = {
      deviceIdList: list?.map(item => item.deviceId),
      type: type,
    };
    apiCall('/employeeTaskAuto', 'POST', data)
      .then((res) => {
        message.success(type == 'GROUP_MSG' ? '群发成功！' : '发表成功！');
        props.params?.onSubmit?.();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="AutoAsst-GroupSend-Modal"
      title={title}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      okText="同意"
      onCancel={onCancel}
      onOk={onOk}
    >
      <WibotEditorView html={describe} />
      {list.map((item, index) => (
        <p key={index} style={{ color: "#d5001c" }}>
          {item.groupName} - {item.deviceName}
        </p>
      ))}
    </Modal>
  )
};

export default GroupSendModal;
