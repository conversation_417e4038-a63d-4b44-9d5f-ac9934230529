/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/28 11:31
 * @LastEditTime: 2025/07/04 11:19
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AutoAsst/Scene/home.jsx
 * @Description: '通用场景'
 */

import React, {useEffect, useRef, useState} from "react";
import {Button, Card, Form, Input, message, Select, Table, Tooltip,} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import {apiCall} from "common/utils";
import {removeInputEmpty} from "common/regular";
import {timeStamp} from 'common/date';
import GroupSendModal from './comps/GroupSendModal';
import SysDictSelect from 'components/select/SysDictSelect';
import {WibotModal} from "components/WibotModal";
import {isV2} from "config";

const AutoAsstScene = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [deviceGroup, setDeviceGroup] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 5 });
  const [groupSendParams, setGroupSendParams] = useState({ visible: false });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "分组名称",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
    },
    {
      title: "设备名称",
      width: "160px",
      dataIndex: "deviceName",
      key: "deviceName",
      align: "center",
    },
    {
      title: "登入账号",
      width: "160px",
      dataIndex: "manageEmployeeName",
      key: "manageEmployeeName",
      align: "center",
    },
    {
      title: '发表朋友圈',
      width: '160px',
      dataIndex: 'momentExecuteTime',
      key: 'momentExecuteTime',
      // ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        let content = (record.momentSuccess ? '执行成功' : '执行失败') + (record.momentReason ? `（${record.momentReason}） ` : '');
        return value ? <>
          <Tooltip placement="topLeft" title={content} >
            {value}
            <br />
            {record.momentSuccess ? '执行成功' : '执行失败'}
          </Tooltip>
        </> : ''
      },
      sorter: (a, b) => timeStamp(a.momentExecuteTime) - timeStamp(b.momentExecuteTime),
    },
    {
      title: '群发客户/客户群',
      width: '160px',
      dataIndex: 'groupExecuteTime',
      key: 'groupExecuteTime',
      // ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        let content = (record.groupSuccess ? '执行成功' : '执行失败') + (record.groupReason ? `（${record.groupReason}）` : '');
        return value ? <>
          <Tooltip placement="topLeft" title={content}>
            {value}
            <br />
            {record.groupSuccess ? '执行成功' : '执行失败'}
          </Tooltip>
        </> : ''
      },
      sorter: (a, b) => timeStamp(a.groupExecuteTime) - timeStamp(b.groupExecuteTime),
    },
  ];
  const [taskName, setTaskName] = useState("");

  useEffect(() => {
    getDeviceGroup();
    fetchList();
  }, []);

  useEffect(() => {
    if (dataSource.length) {
      setSelectedRows([...dataSource])
      setSelectedRowKeys(dataSource.map((item) => item.deviceId))
    }
  }, [dataSource]);

  // 分组管理
  const getDeviceGroup = async () => {
    await apiCall('/device/deviceGroup', 'GET', {
      paged: false,
    })
      .then((res) => {
        const { records } = res;
        setDeviceGroup(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.deviceGroupIdList = formData.deviceGroupIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 5 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/employeeTaskAuto", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    setTaskName("")
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  // 群发
  const handleGroupSend = (type) => {
    if (selectedRowKeys.length > 5) {
      message.warning('一次最多执行5个设备的任务！')
      return false;
    }
    switch (type) {
      case 'group':
        setGroupSendParams({
          visible: true,
          title: "群发客户/客户群",
          describe: `<p style=margin-top:0px;>即将执行下列设备的最新一条<span style=color:#d5001c;font-weight:bold;font-size:16px>“群发客户/客户群”</span>任务，该操作无法撤回，您同意这样做吗？</p>`,
          list: selectedRows,
          type: "GROUP_MSG",
          onSubmit: (data) => {
            setGroupSendParams({ visible: false })
            fetchList({
              pagination: paginations,
            })
          },
          onCancel: () => {
            setGroupSendParams({ visible: false })
          },
        })
        break;
      case 'circle':
        setGroupSendParams({
          visible: true,
          title: "发表朋友圈",
          describe:
            "<p style=margin-top:0px;>即将执行下列设备的最新一条<span style=color:#d5001c;font-weight:bold;font-size:16px>“客户朋友圈”</span>任务，该操作无法撤回，您同意这样做吗？</p>",
          list: selectedRows,
          type: "MOMENT",
          onSubmit: (data) => {
            setGroupSendParams({ visible: false })
            fetchList({
              pagination: paginations,
            })
          },
          onCancel: () => {
            setGroupSendParams({ visible: false })
          },
        })
        break;
    }
  };

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    console.log(selectedRowKeys, selectedRows, 'selectedRowKeys, selectedRows');
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  };

  const hasSelected = selectedRowKeys.length > 0;


  const handleSendToday = () => {
    if (selectedRowKeys.length > 5) {
      message.warning('一次最多执行5个设备的任务！')
      return false;
    }

    const modalClose = WibotModal.open({
      title: "执行今日待群发任务",
      children: <>
        <div>
          即将执行下列设备的今日全部待群发消息，包括客户群发和客户群群发，该
          操作无法撤回，您同意这样做吗？
        </div>
        {
          selectedRows.map(row => (
              <div style={{color: 'red'}}>{row.groupName} - {row.deviceName}</div>
          ))
        }
      </>,
      onOk: () => {
        const data = {
          deviceIdList: selectedRows?.map(item => item.deviceId),
          type: 'GROUP_MSG',
          executeTodayTask: true
        };
        apiCall('/employeeTaskAuto', 'POST', data)
            .then((res) => {
              message.success('群发成功！');
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {

            });

      },
    })
  }
  return (
    <div className="AutoAsst-Scene-Container">
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="设备名称" allowClear />
          </Form.Item>
          <Form.Item name="manageEmployeeName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="登入账号" allowClear />
          </Form.Item>
          <Form.Item name="deviceGroupIdList">
            <Select
              allowClear
              showSearch
              maxTagCount="responsive"
              placeholder="分组"
              mode="multiple"
              options={deviceGroup}
              fieldNames={{ label: 'name', value: 'id' }}
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>

          <Form.Item name="taskName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="群发任务名称" allowClear onChange={(e) => {
              const { value } = e.target;
              if (!value) {
                formRef.current.setFieldValue("issueStatus", null)
              }
              setTaskName(value)
            }} />
          </Form.Item>

          {
            isV2() ?
                <Form.Item name="todayTaskOnly">
                  <Select
                      allowClear
                      showSearch
                      maxTagCount="responsive"
                      placeholder="仅包含今日待群发的账号"
                      options={[{label: '是', value: true}, {label: '否', value: false}]}
                      filterOption={(input, option) =>
                          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                  />
                </Form.Item> :
                null
          }

          {taskName ? <Form.Item name="issueStatus">
            <SysDictSelect dataset="TASK_BATCH_EXECUTOR_STATUS" placeholder="任务发送状态" />
          </Form.Item> : ''}
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            {
              isV2() ?
                  <Button
                      disabled={!hasSelected}
                      type="primary"
                      onClick={handleSendToday}
                      style={{marginRight: "100px"}}
                  >
                    执行今日待群发任务
                  </Button> :
                  null
            }
            <Button
              disabled={!hasSelected}
              type="primary"
              onClick={() => {
                handleGroupSend('group');
              }}
              style={{ marginRight: "100px" }}
            >
              群发客户/客户群
            </Button>
            <Button
              disabled={!hasSelected}
              type="primary"
              onClick={() => {
                handleGroupSend('circle');
              }}
            >
              发表朋友圈
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="deviceId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={{
            ...paginations,
            pageSizeOptions: [5, 10, 20, 50, 100]
          }}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <GroupSendModal params={groupSendParams} />
    </div>
  );
};

export default AutoAsstScene;
