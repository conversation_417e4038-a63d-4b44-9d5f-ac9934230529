/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/11 10:31
 * @LastEditTime: 2024/07/16 10:01
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/AutoAsst/home.jsx
 * @Description: '托管辅助'
 */

import React, { useEffect, useState } from "react";
import { Card, Tabs } from "antd";
// 模块组件
import Scene from "./Scene/home";
import Moment from "./Moment/home";

const { TabPane } = Tabs;

const AutoAsst = (props) => {
  const [tabsIndex, setTabsIndex] = useState("");

  useEffect(() => {
    setTabsIndex(localStorage.getItem("AutoAsstTabsIndex") || "1");
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem("AutoAsstTabsIndex", index);
    setTabsIndex(index);
  };

  return (
    <div className="AutoAsst-Container">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          <TabPane tab="通用场景" key="1">
            <Scene />
          </TabPane>
          <TabPane tab="客户朋友圈" key="2">
            <Moment />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AutoAsst;
