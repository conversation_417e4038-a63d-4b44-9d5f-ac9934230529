/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 11:12
 * @LastEditTime: 2024/11/06 15:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/ConversationManage/comps/AllocationModal.jsx
 * @Description: '分配对话'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Form, message, Input, TreeSelect } from 'antd';
import { apiCall } from 'common/utils';
import AppStore from 'stores/AppStore';

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const AllocationModal = (props) => {
  const { visible = false, info = null } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (visible) {
      getAgentGroupOptions()
      if (info) {
        formForm.setFieldsValue({
          customerName: info.customerName,
          employeeName: info.employeeName,
          content1: info.content,
        });
      }
    } else {
      setConfirmLoading(false);
      formForm?.resetFields();
    }
  }, [visible]);

  // 查询所有座席
  const getAgentGroupOptions = () => {
    const data = {
      signIn: true
    }
    apiCall("/agentSeat/agentGroup/all/options", "GET", data)
      .then((res) => {
        setClassifyMenu(res.map((item => ({
          ...item,
          disabled: true
        }))));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        conversationId: info.id,
        ...formData,
      };
      apiCall('/agentSeat/conversation/distributionAgent', 'POST', data)
        .then((res) => {
          message.success('分配成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="ConversationManage-AllocationModal"
      title="分配对话"
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item
          name="agentId"
          label={`目标${agentSeat_title}`}
          rules={[{ required: true, message: `请选择目标${agentSeat_title}` }]}
        >
          <TreeSelect
            allowClear
            showArrow
            showSearch
            treeDefaultExpandAll
            treeData={classifyMenu}
            placeholder={`请选择目标${agentSeat_title}`}
            fieldNames={{ label: 'title', value: 'key', children: 'children' }}
            treeNodeFilterProp="title"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AllocationModal;
