/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/27 14:01
 * @LastEditTime: 2025/04/25 14:24
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/ConversationManage/home.jsx
 * @Description: '对话管理'
 */

import { QuestionCircleOutlined } from "@ant-design/icons"
import {
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  Select,
  Table,
  Tooltip,
  TreeSelect,
} from "antd"
import { secondsToTime, timeStamp } from "common/date"
import { removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData } from "common/tree"
import { apiCall } from "common/utils"
import FilterBar from "components/FilterBar/FilterBar"
import ListOperation from "components/ListOperation/home"
import SysDictLabel from "components/select/SysDictLabel"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotTableTag from "components/WibotTableTag/home"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import AppStore from "stores/AppStore"
import SwitchSessionModal from "../AgentSeat/comps/SwitchSessionModal"
import TypeMessageDrawer from "../AgentSeat/comps/TypeMessage/drawer"
import TypeFormModal from "../SessionSummary/comps/TypeFormModal"
import AllocationModal from "./comps/AllocationModal"

const { RangePicker } = DatePicker
const { SHOW_PARENT } = TreeSelect

const ConversationManage = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [manageEmployeeOption, setManageEmployeeOption] = useState([])
  const [allocationParams, setAllocationParams] = useState({ visible: false })
  const [drawerParams, setDrawerParams] = useState({ visible: false })
  const [typeFormParams, setTypeFormParams] = useState({ visible: false })
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
    },
    {
      title: "托管账号",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
    },
    {
      title: "分流规则",
      width: "160px",
      dataIndex: "routingRuleName",
      key: "routingRuleName",
      align: "center",
    },
    {
      title: "参与坐席",
      width: "160px",
      dataIndex: "agentEmployeeNameList",
      key: "agentEmployeeNameList",
      align: "center",
      render: (value, record, index) => (
        <WibotTableTag tagList={value} maxLength={9999} />
      ),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="PROCESS_STATE" dictkey={value} />
      ),
    },
    {
      title: "业务类型",
      width: "160px",
      dataIndex: "sessionBusinessTypeId",
      key: "sessionBusinessTypeId",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        const content =
          (record.sessionBusinessTypeId && (
            <>
              {record.sessionBusinessTypeParentName}-
              {record.sessionBusinessTypeName}
            </>
          )) ||
          null
        return (
          <Tooltip placement="topLeft" title={content}>
            {content}
          </Tooltip>
        )
      },
    },
    {
      title: "对话总结",
      width: "160px",
      dataIndex: "summaryList",
      key: "summaryList",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value?.join("\n")}>
          {value?.join("\n")}
        </Tooltip>
      ),
    },
    {
      title: "开始时间",
      width: "160px",
      dataIndex: "startTime",
      key: "startTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.startTime) - timeStamp(b.startTime),
    },
    {
      title: "结束时间",
      width: "160px",
      dataIndex: "endTime",
      key: "endTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.endTime) - timeStamp(b.endTime),
    },
    {
      title: "耗时",
      width: "160px",
      dataIndex: "costTime",
      key: "costTime",
      align: "center",
      render: (value, record, index) =>
        secondsToTime(value, "HH小时mm分ss秒", true),
      sorter: (a, b) => a.costTime - b.costTime,
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [{ onClick: () => handleView(record), name: "查看" }]
        if (record.state == "WAITING") {
          opts.push({ onClick: () => handleAllocation(record), name: "分配" })
        }
        if (record.state == "PROCESSING") {
          opts.push({
            onClick: () => handleSwitchSession(record),
            name: "转接",
          })
        }
        return <ListOperation opts={opts} />
      },
    },
  ])
  const [switchSessionParams, setSwitchSessionParams] = useState({
    visible: false,
  })
  const moduleVersionMap = AppStore.state.User.moduleVersionMap
  const agentSeat_title =
    moduleVersionMap[`agentSeat_title`] === "v2" ? "客服" : "坐席"
  const [classifyMenu, setClassifyMenu] = useState([])
  const [routingRuleOptions, setRoutingRuleOptions] = useState([])

  useEffect(() => {
    if (moduleVersionMap[`agentSeat_title`] === "v2") {
      columns[4].title = "参与客服"
      setColumns([...columns])
    }
    formRef.current?.setFieldsValue({ collaborated: undefined })
    getSessionBusinessTypeTree()
    getManageEmployeeOption()
    getRoutingRuleOptions()
    fetchList()
  }, [])

  const getSessionBusinessTypeTree = () => {
    const data = {}
    apiCall("/inbox/sessionBusinessType/tree", "GET", data)
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res)
        setClassifyMenu([
          {
            title: "全选",
            value: "",
            key: "",
            children: treeData,
          },
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const getManageEmployeeOption = () => {
    apiCall("/device/deviceGroup/listDeviceGroupTree", "GET", { paged: false })
      .then((res) => {
        setManageEmployeeOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const getRoutingRuleOptions = () => {
    apiCall("/agentSeat/routingRule/select", "GET")
      .then((res) => {
        setRoutingRuleOptions(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD HH:mm:00"
        )
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD HH:mm:59"
        )
        delete formData.createTime
      }
      formData.manageEmployeeIdList =
        formData.manageEmployeeIdList?.join(",") || null
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null
      formData.sessionBusinessTypeIdList =
        formData.sessionBusinessTypeIdList?.join(",") || null
      formData.routingRuleIdList = formData.routingRuleIdList?.join(",") || null
      if (formData.collaborated !== undefined) {
        formData.collaborated = !!formData.collaborated // 将非空字符串转换为布尔值
      }
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/agentSeat/conversation", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 查看对话
  const handleView = (record) => {
    const { id, customerName, employeeName } = record
    setDrawerParams({
      visible: true,
      title: `对话详情（${customerName}-${employeeName}）`,
      conversationId: id,
      onCancel: () => {
        setDrawerParams({ visible: false })
      },
    })
  }

  // 分配
  const handleAllocation = (record) => {
    setAllocationParams({
      visible: true,
      info: record,
      onSubmit: (data) => {
        setAllocationParams({ visible: false })
        fetchList()
      },
      onCancel: () => {
        setAllocationParams({ visible: false })
      },
    })
  }

  // 转接
  const handleSwitchSession = (record) => {
    setSwitchSessionParams({
      visible: true,
      sessionClientInfo: {
        ...record,
        conversationId: record.id,
      },
      x: 0,
      onSubmit: (data) => {
        setSwitchSessionParams({ visible: false })
        fetchList()
      },
      onCancel: () => {
        setSwitchSessionParams({ visible: false })
      },
    })
  }

  const handleClassManage = () => {
    setTypeFormParams({
      visible: true,
      onCancel: () => {
        setTypeFormParams({ visible: false })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="ConversationManage-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="客户名称" allowClear />
          </Form.Item>

          <Form.Item name="manageEmployeeIdList">
            <TreeSelect
              showSearch
              showArrow
              allowClear
              multiple
              treeDefaultExpandAll
              showCheckedStrategy={TreeSelect.SHOW_ALL}
              placeholder="托管账号"
              maxTagCount="responsive"
              treeData={manageEmployeeOption}
              fieldNames={{
                label: "title",
                value: "key",
                children: "children",
              }}
              treeNodeFilterProp="title"
            />
          </Form.Item>

          <Form.Item
            name="depEmployeeIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal
              title={`参与${agentSeat_title}`}
              onlyEmployee
              needExcludeDepFlag={false}
            />
          </Form.Item>

          <Form.Item name="state" initialValue="WAITING">
            <SysDictSelect dataset="PROCESS_STATE" placeholder="状态" />
          </Form.Item>

          <Form.Item name="createTime" label="对话时间">
            <RangePicker
              showTime={{ format: "HH:mm" }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>

          <Form.Item
            name="summaryList"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="对话总结" allowClear />
          </Form.Item>

          <Form.Item name="collaborated">
            <Select
              allowClear
              placeholder="协同处理"
              dropdownRender={(menu) => (
                <>
                  <div style={{ padding: "4px 8px" }}>
                    协同处理
                    <Tooltip title="对话服务过程中，若员工发起协同邀请且有被确认接入，相关对话记录为 “是”。">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </div>
                  {menu}
                </>
              )}
            >
              <Select.Option value={1}>是</Select.Option>
              <Select.Option value={0}>否</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="sessionBusinessTypeIdList"
            style={{ width: "300px" }}
          >
            <TreeSelect
              placeholder="业务类型"
              treeData={classifyMenu}
              treeCheckable
              treeDefaultExpandedKeys={[""]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </Form.Item>

          <Form.Item name="routingRuleIdList">
            <Select
              allowClear
              mode="multiple"
              placeholder="分流规则"
              fieldNames={{ label: "name", value: "id" }}
              options={routingRuleOptions}
              showSearch
              showArrow
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleClassManage()}>
              业务类型管理
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <AllocationModal params={allocationParams} />
      <TypeFormModal params={typeFormParams} />
      <TypeMessageDrawer params={drawerParams} />
      <SwitchSessionModal params={switchSessionParams} />
    </div>
  )
}

export default ConversationManage
