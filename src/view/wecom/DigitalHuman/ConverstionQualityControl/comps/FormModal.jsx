/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/10/14 09:56
 * @LastEditTime: 2024/10/14 11:29
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/ConverstionQualityControl/comps/FormModal.jsx
 * @Description: '创建质检任务'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Form, message, Input, DatePicker, Select } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from "common/regular";
import moment from "moment";

const { RangePicker } = DatePicker;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const FormModal = (props) => {
  const { visible = false } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [schemeOption, setSchemeOption] = useState([]);
  const [datesLog, setDatesLog] = useState([null, null]);

  useEffect(() => {
    if (visible) {
      getSchemeOption()
    } else {
      setConfirmLoading(false);
      setSchemeOption([]);
      setDatesLog([null, null]);
      formForm?.resetFields();
    }
  }, [visible]);

  const getSchemeOption = () => {
    apiCall('/agentSeat/qualityControlScheme', 'GET', {
      paged: false,
    })
      .then((res) => {
        setSchemeOption(res.records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format("YYYY-MM-DD");
        formData.endDate = moment(formData.time[1]._d).format("YYYY-MM-DD");
        delete formData.time;
      }
      setConfirmLoading(true);
      const data = {
        ...formData,
      };
      apiCall('/agentSeat/qualityControlTask', 'POST', data)
        .then((res) => {
          message.success('创建成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = (e) => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="QualityControlRule-FormModal"
      title='创建质检任务'
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form form={formForm} {...layout}>
        <Form.Item
          name="name"
          label="任务名称"
          getValueFromEvent={(e) => removeInputEmpty(e)}
          rules={[{ required: true, message: "请输入任务名称" }]}
        >
          <Input
            placeholder="请输入任务名称"
            allowClear
            maxLength={20}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="time"
          label="筛选范围"
          rules={[{ required: true, message: "请选择筛选范围" }]}
        >
          <RangePicker
            disabledDate={(current) => {
              const dates = formForm?.getFieldValue('time')
              if (!dates) {
                return false;
              }
              const tooLate = dates[0] && current.diff(dates[0], 'days') > 30;
              const tooEarly = dates[1] && dates[1].diff(current, 'days') > 30;
              return !!tooEarly || !!tooLate;
            }}
            onCalendarChange={val => formForm.setFieldValue('time', val)}
            onChange={val => setDatesLog(val)}
            onOpenChange={(open) => {
              if (open) {
                formForm.setFieldValue('time', [null, null])
              } else {
                let timer = setTimeout(() => {
                  formForm.setFieldValue('time', datesLog)
                  clearTimeout(timer)
                }, 300);
              }
            }}
          />
        </Form.Item>

        <Form.Item
          name="schemeId"
          label="质检方案"
          rules={[{ required: true, message: '请选择质检方案', }]}
        >
          <Select
            placeholder="请选择质检方案"
            fieldNames={{ label: 'name', value: 'id' }}
            options={schemeOption}
            allowClear
            showSearch
            showArrow
            maxTagCount="responsive"
            filterOption={(input, option) =>
              (option?.name ?? '')
                .toLowerCase()
                .includes(input.toLowerCase())
            }
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FormModal;
