/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/10/14 11:14
 * @LastEditTime: 2024/11/08 16:14
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/ConverstionQualityControl/detail.jsx
 * @Description: '对话质检-详情'
 */

import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
  Spin,
  Select,
  Space,
  message,
} from "antd";
import {
  QuestionCircleOutlined
} from '@ant-design/icons';
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp, secondsToTime } from "common/date";
import { qs2obj } from 'common/object';
import ListOperation from 'components/ListOperation/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotTableTag from 'components/WibotTableTag/home';
import TypeMessageDrawer from "../AgentSeat/comps/TypeMessage/drawer";
import AppStore from 'stores/AppStore';

const QualityControlRuleDetail = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [infoData, setInfoData] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={record.conversationVO.customerName}>{record.conversationVO.customerName}</Tooltip>
      ),
    },
    {
      title: "托管账号",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={record.conversationVO.employeeName}>{record.conversationVO.employeeName}</Tooltip>
      ),
    },
    {
      title: "分流规则",
      width: "160px",
      dataIndex: "routingRuleName",
      key: "routingRuleName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={record.conversationVO.routingRuleName}>{record.conversationVO.routingRuleName}</Tooltip>
      ),
    },
    {
      title: "参与坐席",
      width: "160px",
      dataIndex: "agentEmployeeNameList",
      key: "agentEmployeeNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={record.conversationVO.agentEmployeeNameList} />,
    },
    {
      title: "对话总结",
      width: "160px",
      dataIndex: "summaryList",
      key: "summaryList",
      ellipsis: 'true',
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={record.conversationVO.summaryList?.join("\n")}>
          {record.conversationVO.summaryList?.join("\n")}
        </Tooltip>
      ),
    },
    {
      title: "开始/结束时间",
      width: "220px",
      dataIndex: "startTime",
      key: "startTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.conversationVO.startTime}
          <br />～<br />
          {record.conversationVO.endTime}
        </>
      ),
    },
    {
      title: "耗时",
      width: "160px",
      dataIndex: "costTime",
      key: "costTime",
      align: "center",
      render: (value, record, index) => (secondsToTime(record.conversationVO.costTime, 'HH小时mm分ss秒', true)),
      // sorter: (a, b) => a.costTime - b.costTime,
    },
    {
      title: "是否命中质检方案",
      width: "160px",
      dataIndex: "hit",
      key: "hit",
      align: "center",
      render: (value, record, index) => (value ? '是' : '否'),
    },
    {
      title: "命中质检规则",
      width: "160px",
      dataIndex: "hitRuleNameList",
      key: "hitRuleNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "命中规则数",
      width: "160px",
      dataIndex: "hitRuleNameList",
      key: "hitRuleNameList",
      align: "center",
      render: (value, record, index) => (value.length),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: "详情" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ]);
  const [hitRuleOptions, setHitRuleOptions] = useState([]);
  const [drawerParams, setDrawerParams] = useState({ visible: false });
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (moduleVersionMap[`agentSeat_title`] === 'v2') {
      columns[4].title = '参与客服'
      setColumns([...columns])
    }
    const { id } = qs2obj(props.location.search);
    setId(id)
  }, []);

  useEffect(async () => {
    if (id) {
      await getInfoData();
      await fetchList();
    }
  }, [id]);

  const getInfoData = async () => {
    setLoading(true);
    await apiCall(`/agentSeat/qualityControlTask/${id}`, "GET")
      .then((res) => {
        const { ruleNameList } = res;
        setInfoData(res)
        setHitRuleOptions(ruleNameList.map((item) => ({
          name: item,
          id: item,
        })))
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    await formRef.current.validateFields().then((formData) => {
      formData.hitRuleNameList = formData.hitRuleNameList?.join(",") || null;
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        taskId: id,
        ...query,
        ...formData,
      };
      apiCall("/agentSeat/qualityControlTaskResult", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleDetail = (record) => {
    const { id, customerName, employeeName } = record.conversationVO;
    setDrawerParams({
      visible: true,
      title: `对话详情（${customerName}-${employeeName}）`,
      qualityFlag: true,
      qualityId: record.id,
      conversationId: id,
      formRef: formRef.current,
      onCancel: () => {
        setDrawerParams({ visible: false });
      },
    })
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleExport = () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.hitRuleNameList = formData.hitRuleNameList?.join(",") || null;
      formData.depEmployeeIdList = formData.depEmployeeIdList?.join(",") || null;
      const data = {
        taskId: id,
        // ...formData
      };
      apiCall('/agentSeat/qualityControlTaskResult/export', 'GET', data, null, { isExit: true, title: `${infoData?.name}（筛选范围：${infoData?.startDate}至${infoData?.endDate}）.xlsx` }).then((res) => {
        message.success('导出成功！');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <div className="QualityControlRule-Detail-Container">
      <Card
        title={
          <Card.Meta
            title={infoData?.name || '-'}
            description={infoData ? (infoData?.schemeName + "：" + infoData?.ruleNameList.join("、")) : ''}
          />
        }
        extra={
          <Button
            type="primary"
            onClick={() => {
              props.history.go(-1);
            }}>返回</Button>
        }
        bordered={false}
      >
        <Spin spinning={loading}>
          <FilterBar bodyStyle={{ padding: 'unset' }}>
            <Form layout={"inline"} ref={formRef}>
              <Form.Item
                name="depEmployeeIdList"
                style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
              >
                <ETypeTransferModal
                  title={`参与${agentSeat_title}`}
                  onlyEmployee
                  needExcludeDepFlag={false}
                />
              </Form.Item>

              <Form.Item name="hit">
                <Select
                  placeholder="是否命中质检方案"
                  allowClear
                >
                  <Option value={true}>是</Option>
                  <Option value={false}>否</Option>
                </Select>
              </Form.Item>

              <Form.Item name="hitRuleNameList" style={{ margin: "0" }} extra="附：多选时，仅筛满足所有规则的对话">
                <Select
                  placeholder="命中质检规则"
                  fieldNames={{ label: 'name', value: 'id' }}
                  options={hitRuleOptions}
                  allowClear
                  showSearch
                  showArrow
                  mode="multiple"
                  maxTagCount="responsive"
                  filterOption={(input, option) =>
                    (option?.name ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Form>
            <div className="flex flex-space-between">
              <div>
                <Button type="primary" onClick={() => handleQuery()}>
                  查询
                </Button>
                <Button onClick={() => handleReset()}>重置筛选</Button>
              </div>
              <div>
                <Button type="primary" onClick={() => handleExport()}>
                  导出
                </Button>
              </div>
            </div>
          </FilterBar>
          <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
            <Table
              rowKey="id"
              dataSource={dataSource}
              columns={columns}
              scroll={{ x: 1300 }}
              pagination={paginations}
              onChange={onChangeTable}
            />
          </Card>
        </Spin>
      </Card>
      <TypeMessageDrawer params={drawerParams} />
    </div>
  );
};

export default QualityControlRuleDetail;
