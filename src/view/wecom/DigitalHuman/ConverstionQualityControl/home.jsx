/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/10/12 14:01
 * @LastEditTime: 2024/11/07 14:12
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/ConverstionQualityControl/home.jsx
 * @Description: '对话质检'
 */

import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { removeInputEmpty } from "common/regular";
import ListOperation from 'components/ListOperation/home';
import SysDictLabel from 'components/select/SysDictLabel';
import FormModal from './comps/FormModal';
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;

const QualityControlRule = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "质检方案",
      width: "160px",
      dataIndex: "schemeName",
      key: "schemeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "筛选范围",
      width: "160px",
      dataIndex: "startDate",
      key: "startDate",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startDate}
          <br />～<br />
          {record.endDate}
        </>
      ),
    },
    {
      title: "对话数",
      width: "160px",
      dataIndex: "conversationCount",
      key: "conversationCount",
      align: "center",
    },
    {
      title: "命中对话数",
      width: "160px",
      dataIndex: "hitConversationCount",
      key: "hitConversationCount",
      align: "center",
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      // render: (value, record, index) => (
      //   <>
      //     {record.createEmployeeName}
      //     <br />
      //     {value}
      //   </>
      // ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "执行情况",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="QUALITY_CONTROL_TASK_STATE" dictkey={value} />
      ),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [];
        if (record.state == 'FINISHED') {
          opts.push({ onClick: () => handleDetail(record), name: "任务结果" })
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/agentSeat/qualityControlTask", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    setFormParams({
      visible: true,
      onSubmit: () => {
        setFormParams({
          visible: false,
        });
        fetchList();
      },
      onCancel: () => {
        setFormParams({
          visible: false,
        });
      },
    });
  };

  const handleDetail = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/converstionQualityControl/detail",
      search: `?id=${id}`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="QualityControlRule-Container">
      <FilterBar >
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="任务名称" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              创建任务
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formParams} />
    </div>
  );
};

export default QualityControlRule;
