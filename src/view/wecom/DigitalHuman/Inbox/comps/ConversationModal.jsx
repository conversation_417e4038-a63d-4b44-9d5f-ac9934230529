/*
 * @Author: <PERSON>eiw
 * @Date: 2023/01/16 09:41
 * @LastEditTime: 2024/02/26 15:09
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\Inbox\comps\ConversationModal.jsx
 * @Description: '收件箱-当前会话员工'
 */

import React, { useEffect, useState } from "react";
import { Modal, Avatar } from "antd";

const ConversationModal = (props) => {
  const { data = [] } = props.params;
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="ConversationModal"
      width={400}
      visible={visible}
      title="当前会话中的其他员工"
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      footer={null}
    >
      {data.map((item, index) => (
        <div style={{ display: "flex", marginBottom: "10px" }} key={index}>
          <Avatar size={40} src={item.employeeAvatar} />
          <div
            style={{
              marginLeft: "6px",
              width: "100%",
              textAlign: "left",
              whiteSpace: "normal",
              flex: 1,
            }}
          >
            {item.employeeName}
            <span>{item.departmentName}</span>
          </div>
        </div>
      ))}
    </Modal>
  );
};

export default ConversationModal;
