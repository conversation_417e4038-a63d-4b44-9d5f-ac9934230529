/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date:  2023/12/28 16:01
 * @LastEditTime: 2024/03/28 10:18
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/EndSessionModal.jsx
 * @Description: '结束会话弹窗'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Button, Input, TreeSelect, Popconfirm } from 'antd';
import { apiCall } from 'common/utils';
import SysDictSelect from 'components/select/SysDictSelect';
import { sceneRecursionTreeData } from "common/tree";

const FormItem = Form.Item;
const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const EndSessionModal = (props) => {
  const { sessionClientInfo } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getSessionBusinessTypeTree()
      if (sessionClientInfo) {
        let timer = setTimeout(() => {
          formRef.current.setFieldsValue({
            customerName: sessionClientInfo.customerName,
            employeeName: sessionClientInfo.manageEmployeeName,
          });
          clearTimeout(timer);
        }, 100);
      }
    }
  }, [props]);

  // 获取资讯分类
  const getSessionBusinessTypeTree = () => {
    const data = {};
    apiCall("/inbox/sessionBusinessType/tree", "GET", data)
      .then((res) => {
        const treeData = sceneRecursionTreeData(res);
        setClassifyMenu(treeData);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        sessionId: sessionClientInfo.sessionId,
        ...formData,
      };
      apiCall('/message/endSession', 'POST', data)
        .then((res) => {
          message.success('保存成功！');
          props.params?.onSubmit?.();
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={456}
      title="填写会话小结"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      onCancel={onCancel}
      className="EndSessionModal"
      footer={
        <div>
          <Popconfirm
            title="取消后已添加的小结内容将丢失，确定这样做吗？"
            onConfirm={onCancel}
            okText="确 定"
            cancelText="取 消"
          >
            <Button type="primary">
              取消
            </Button>
          </Popconfirm>
          <Button type="primary" onClick={onOk} loading={confirmLoading}>
            保存
          </Button>
        </div>
      }
    >
      <Form ref={formRef} {...layout}>
        <FormItem label="客户昵称" name="customerName">
          <Input allowClear disabled />
        </FormItem>

        <FormItem label="账号名称" name="employeeName">
          <Input allowClear disabled />
        </FormItem>

        <FormItem name="state" label="会话状态" rules={[{ required: true, message: '请选择会话状态' }]} initialValue={'FINISHED'}>
          <SysDictSelect dataset="SESSION_SUMMARY_STATE" placeholder="会话状态" />
        </FormItem>

        <FormItem
          name="sessionBusinessTypeId"
          label="业务类型"
          rules={[{ required: true, message: "请选择业务类型" }]}
        >
          <TreeSelect
            allowClear
            showArrow
            showSearch
            treeData={classifyMenu}
            // treeDefaultExpandAll
            placeholder="请选择业务类型"
            treeNodeFilterProp="title"
          />
        </FormItem>

        <FormItem
          name="content"
          label="小结内容"
          rules={[{ required: true, message: '请输入小结内容（200字）' }]}
        >
          <TextArea
            placeholder="请输入小结内容（200字）"
            allowClear
            showCount
            maxLength={200}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default EndSessionModal;
