/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/11/14 11:44
 * @LastEditTime: 2025/07/04 11:13
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/MessageModal.jsx
 * @Description: '收件箱-收藏消息'
 */

import React, { useEffect, useState } from 'react';
import { Modal, Input, Empty, message } from 'antd';
import { SendOutlined, DeleteOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import OperateModal from 'components/Modal/OperateModal/index';
import Draggable from 'react-draggable';
import { highLightText } from 'common/regular';
import WibotEditorView from "components/WibotEditorView/home"
import './MessageModal.less';

const MessageModal = (props) => {
  const { visible } = props.params;
  const [messageList, setMessageList] = useState([]);
  // 存储搜索值
  const [searchValue, setSearchValue] = useState('');
  const [matchedOptions, setMatchedOptions] = useState([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [operateParams, setOperateParams] = useState({ visible: false });

  useEffect(() => {
    if (visible) {
      fetchEmployeeMessage();
    } else {
      setSearchValue("")
    }
  }, [visible]);

  const fetchEmployeeMessage = () => {
    apiCall('/inbox/employeeMessage', 'GET')
      .then((res) => {
        setMessageList(res);
        setMatchedOptions(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  let timeout = null;
  // 防抖搜索查询会话内容
  const handleSearch = (e) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    const newMessageList = JSON.parse(JSON.stringify(messageList));
    if (e.target.value) {
      timeout = setTimeout(() => {
        setSearchValue(e.target.value);
        const list = newMessageList.filter(
          (option) =>
            option.content.includes(e.target.value) ||
            new RegExp(e.target.value, 'i').test(option.content)
        );
        console.log(list);
        setMatchedOptions(list);
      }, 400);
    } else {
      setMatchedOptions(newMessageList);
      setSearchValue('');
    }
  };

  // 删除确认
  const handleDelete = (value) => {
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: '确认取消收藏该消息吗？',
      onSubmit: () => {
        apiCall(`/inbox/employeeMessage/delete/${value.id}`, 'POST')
          .then((res) => {
            fetchEmployeeMessage();
          })
          .catch((err) => {
            console.log(err);
          });
        setOperateParams({
          visible: false,
        });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const onOk = () => { };

  const onCancel = () => {
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="MessageModal"
      width={460}
      visible={visible}
      title="收藏消息"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      footer={null}
      modalRender={(modal) => (
        <Draggable>
          <div>{modal}</div>
        </Draggable>
      )}
    >
      <Input
        placeholder="请输入关键词"
        onChange={(e) => {
          handleSearch(e)
        }}
        allowClear
      />
      <div className="messageBox custom-scrollbar">
        {matchedOptions.length > 0 ? (
          matchedOptions.map((item, index) => (
            <div className="messageItem" key={index}>
              <WibotEditorView
                style={{ flex: 1 }}
                html={highLightText(item.content, searchValue)}
              />
              <div className="messageIcons">
                <SendOutlined
                  className="sendIcon"
                  onClick={() => {
                    message.success("已追加到消息框！")
                    props.handleAddMessageSession(item)
                  }}
                />
                <DeleteOutlined
                  onClick={() => {
                    handleDelete(item)
                  }}
                />
              </div>
            </div>
          ))
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>
      <OperateModal params={operateParams} />
    </Modal>
  )
};

export default MessageModal;
