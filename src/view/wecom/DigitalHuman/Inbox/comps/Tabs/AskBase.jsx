/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/11/13 17:12
 * @LastEditTime: 2025/07/04 11:41
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/Tabs/AskBase.jsx
 * @Description: '收件箱问答库'
 */

import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Spin,
  Button,
  Form,
  Input,
  Tag,
  Typography,
  Divider,
  Empty,
} from "antd";
import { apiCall } from "common/utils";
import { replaceSpace, highLightText } from "common/regular";
import InfiniteScroll from "react-infinite-scroll-component";
import WibotEditorView from "components/WibotEditorView/home"
import "./AskBase.less";

const FormItem = Form.Item;
const { Paragraph } = Typography;

const AskBaseTab = forwardRef((props, ref) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [answerList, setAnswerList] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });

  useImperativeHandle(ref, () => ({
    fetchKnowledgeQuestions,
    handleResetData,
    handleScrollToTop,
  }));

  useEffect(() => { }, []);

  const handleResetData = () => {
    formForm.resetFields();
    setSearchValue("");
    setAnswerList([]);
    setPaginations({ current: 1, pageSize: 10 });
  };

  const handleScrollToTop = () => {
    document.getElementById("scrollableDivAnswer").scrollTo(0, 0);
  };

  const fetchKnowledgeQuestions = (params = {}) => {
    setLoading(true);
    const { pagination = paginations, keyword } = params;
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      keyword,
    };
    apiCall("/knowledge/question/inbox/page", "GET", data)
      .then((res) => {
        const { records, current, size, total } = res;
        if (params.push) {
          setAnswerList([...answerList, ...records]);
        } else {
          setAnswerList(records);
        }
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = async () => {
    const value = formForm.getFieldValue("keyword");
    setAnswerList([]);
    await fetchKnowledgeQuestions({
      keyword: value,
      pagination: {
        current: 1,
        pageSize: 10,
      },
    });
    setSearchValue(value ?? "");
  };

  // 问答题下拉加载
  const loadMoreData = () => {
    if (answerList.length < paginations.total) {
      const pageInfo = {
        ...paginations,
        current: paginations.current + 1,
      };
      fetchKnowledgeQuestions({
        pagination: pageInfo,
        keyword: searchValue,
        push: true,
      });
    }
  };

  return (
    <div className="Inbox-AskBase">
      <Spin spinning={loading}>
        <Form layout={"inline"} form={formForm}>
          <FormItem name="keyword" style={{ width: "100%", flex: 1 }}>
            <Input
              placeholder="请输入关键词"
              allowClear
              onPressEnter={handleQuery}
            />
          </FormItem>
          <Button type="primary" onClick={() => handleQuery()}>
            搜索
          </Button>
        </Form>
        <div className="answerBox custom-scrollbar" id="scrollableDivAnswer">
          {answerList.length > 0 ? (
            <InfiniteScroll
              dataLength={answerList.length}
              next={(e) => {
                loadMoreData(e)
              }}
              hasMore={answerList.length < paginations.total}
              scrollableTarget="scrollableDivAnswer"
            >
              {answerList.map((item, index) => (
                <div className="input-content-mask" key={index}>
                  <WibotEditorView
                    html={highLightText(
                      replaceSpace(item.content),
                      searchValue
                    )}
                  />
                  {item.tagList.length > 0 && (
                    <div className="answer-tags" style={{ marginTop: "14px" }}>
                      {item.tagList.map((atem, andex) => (
                        <Tag key={andex}> {atem.name}</Tag>
                      ))}
                    </div>
                  )}
                  <Divider style={{ fontWeight: "bold" }}>答案</Divider>
                  {item.answerList.length > 0 ? (
                    item.answerList.map((atem, andex) => (
                      <div className="answerInfo" key={andex}>
                        <Paragraph copyable={{ text: atem.content }}>
                          <WibotEditorView
                            html={highLightText(
                              replaceSpace(atem.content),
                              searchValue
                            )}
                          />
                        </Paragraph>
                        <div className="answer-tags">
                          {atem.tagList.map((btem, bndex) => (
                            <Tag key={bndex}> {btem.name}</Tag>
                          ))}
                        </div>
                      </div>
                    ))
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </div>
              ))}
            </InfiniteScroll>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
      </Spin>
    </div>
  )
});

export default AskBaseTab;
