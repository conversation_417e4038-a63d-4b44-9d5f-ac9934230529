/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/12/28 16:56
 * @LastEditTime: 2024/03/28 10:17
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/Tabs/EndSession.jsx
 * @Description: '会话小结侧边栏'
 */

import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Spin, Descriptions, Empty, Divider } from 'antd';
import { apiCall } from 'common/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import './EndSession.less';

const EndSessionTab = forwardRef((props, ref) => {
  const { customerId } = props.params;
  const [loading, setLoading] = useState(false);
  const [endSessionList, setEndSessionList] = useState([]);
  const [paginations, setPaginations] = useState({
    current: 1,
    pageSize: 10,
  });

  useImperativeHandle(ref, () => ({
    initSelectChange,
    handleResetData,
  }));

  useEffect(() => {
    fetchEndSessionLists();
  }, []);

  const initSelectChange = (id) => {
    fetchEndSessionLists({ id });
  };

  const handleResetData = () => {
    setEndSessionList([]);
    setPaginations({ current: 1, pageSize: 10 });
  };

  const fetchEndSessionLists = (params = {}) => {
    setLoading(true);
    const { pagination = paginations, id = null } = params;
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      customerId: id || customerId,
    };
    apiCall('/inbox/sessionSummary', 'GET', data)
      .then((res) => {
        const { records, current, size, total } = res;
        if (params.push) {
          setEndSessionList([...endSessionList, ...records]);
        } else {
          setEndSessionList(records);
        }
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 下拉加载
  const loadMoreData = () => {
    if (endSessionList.length < paginations.total) {
      const pageInfo = {
        ...paginations,
        current: paginations.current + 1,
      };
      fetchEndSessionLists({
        pagination: pageInfo,
        push: true,
      });
    }
  };
  return (
    <div className="Inbox-EndSession custom-scrollbar" id="scrollableDivEndSession">
      <Spin spinning={loading}>
        {endSessionList.length > 0 ? (
          <InfiniteScroll
            dataLength={endSessionList.length}
            next={(e) => {
              loadMoreData(e);
            }}
            hasMore={endSessionList.length < paginations.total}
            scrollableTarget="scrollableDivEndSession"
          >
            {endSessionList.map((item, index) => (
              <div key={index}>
                <Descriptions layout="vertical" colon={false}>
                  <Descriptions.Item label="账号名称" span={6}>
                    <span style={{ marginBottom: '16px' }}>
                      {item.employeeName}
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="提交人" span={6}>
                    <span style={{ marginBottom: '16px' }}>
                      {item.createEmployeeName}
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="提交时间" span={6}>
                    <span style={{ marginBottom: '16px' }}>
                      {item.createTime}
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="业务类型" span={6}>
                    <span style={{ marginBottom: '16px' }}>
                      {item.sessionBusinessTypeParentName}-{item.sessionBusinessTypeName}
                    </span>
                  </Descriptions.Item>
                  <Descriptions.Item label="会话小结" span={6}>
                    <span style={{ marginBottom: '16px' }}>{item.content}</span>
                  </Descriptions.Item>
                </Descriptions>
                <Divider style={{ margin: '16px 0' }}></Divider>
              </div>
            ))}
          </InfiniteScroll>
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </Spin>
    </div>
  );
});

export default EndSessionTab;
