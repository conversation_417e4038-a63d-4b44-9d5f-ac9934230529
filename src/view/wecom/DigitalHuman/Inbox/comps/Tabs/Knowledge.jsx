/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/02/26 14:34
 * @LastEditTime: 2025/07/04 11:30
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/Tabs/Knowledge.jsx
 * @Description: '知识随行'
 */

import React, { useState, useEffect, forwardRef, useImperativeHandle, } from 'react';
import { Button, Form, Input, Typography, Radio, Space, Spin, Tabs, Empty } from 'antd';
import {
  DoubleRightOutlined,
  CopyOutlined,
} from "@ant-design/icons";
import { removeInputEmpty, replaceSpace } from 'common/regular';
import { debounce, throttle, once } from 'common/fn';
import { copyToClipboard } from 'common/clipboard';
import { apiCall } from "common/utils";
import WibotEditorView from "components/WibotEditorView/home"
import "./Knowledge.less";

const { Text, Link } = Typography;
const layout = {
  labelCol: {
    xs: { span: 24 },
    xl: { span: 24 },
    xxl: { span: 2 },
  },
};

const Knowledge = forwardRef((props, ref) => {
  const { togetherInfo = null, onAddContent } = props;
  const [loading, setLoading] = useState(false);
  const [formForm] = Form.useForm();
  const [ellipsis, setEllipsis] = useState(true);
  const [searchWordInfo, setSearchWordInfo] = useState(null);
  const [answersInfo, setAnswersInfo] = useState(null);
  const [disabled, setDisabled] = useState(false);
  const [activeKey, setActiveKey] = useState('1');

  useImperativeHandle(ref, () => ({
    reset,
    addKnowledgeTrack,
  }));

  useEffect(async () => {
    if (togetherInfo) {
      reset()
      const { messageId = '', sessionId = '' } = togetherInfo;
      getCache({ messageId, sessionId })
    }
  }, [togetherInfo]);

  // 重置
  const reset = () => {
    setEllipsis(true)
    setSearchWordInfo(null)
    setAnswersInfo(null)
    setDisabled(false)
    setActiveKey('1')
    formForm.resetFields()
  }

  // 获取提问、搜索词
  const getSearchWordInfo = async (messageId) => {
    setLoading(true);
    const data = {
      messageId: messageId
    }
    await apiCall("/icbc/knowledge/searchWord", "GET", data)
      .then((res) => {
        res.searchTerms = res.searchTerms?.map(item => ({
          label: item,
          value: item
        }))
        setSearchWordInfo(res)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取参考答案、引导语
  const getAnswerInfo = (params = {}) => {
    const { source, name } = params;
    formForm.validateFields([name]).then((formData = {}) => {
      const { radio, input } = formData;
      setDisabled(true)
      setLoading(true);
      const data = {
        traceId: searchWordInfo.traceId,
        searchTerm: source == '0' ? radio : input,
        searchTermSource: source
      }
      apiCall("/icbc/knowledge/answer", "POST", data)
        .then((res) => {
          const { answers = [] } = res;
          // 数组重组对象
          const newObj = answers?.reduce((obj, item) => {
            obj[item.type] = { ...item };
            return obj;
          }, {});
          setAnswersInfo(newObj)
          // 获取成功添加搜索词缓存
          addCache({
            formData: data,
            answersInfo: newObj
          })
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    })
  };

  // 添加搜索词缓存
  const addCache = (params = {}) => {
    setLoading(true);
    const data = {
      searchWordInfo: searchWordInfo,
      answersInfo: answersInfo,
      ...params
    }
    apiCall(`/icbc/knowledge/cache?sessionId=${togetherInfo.sessionId}`, "POST", data)
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取搜索词缓存
  const getCache = async (params = {}) => {
    const { messageId, sessionId } = params;
    if (messageId) {
      getSearchWordInfo(messageId)
      return false;
    }
    setLoading(true);
    const data = {
      sessionId: sessionId
    }
    await apiCall("/icbc/knowledge/cache", "GET", data)
      .then((res) => {
        if (res) {
          const { searchWordInfo, answersInfo, formData } = res;
          setSearchWordInfo(searchWordInfo)
          setAnswersInfo(answersInfo)
          formForm.setFieldsValue({
            [formData.searchTermSource == '0' ? 'radio' : 'input']: formData.searchTerm,
          });
          setDisabled(true)
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 添加知识随行埋点
  const addKnowledgeTrack = (params) => {
    if (searchWordInfo) {
      const { userAction, content } = params;
      const data = {
        traceId: searchWordInfo.traceId,
        userAction: userAction,
        content: content,
      }
      apiCall(`/icbc/knowledge/track`, "POST", data)
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    }
  };

  // 获取选中的文本
  const getSelectedText = () => {
    let selectedText = '';

    if (window.getSelection) {
      selectedText = window.getSelection().toString();
    } else if (document.selection && document.selection.type != 'Control') {
      selectedText = document.selection.createRange().text;
    }

    return selectedText;
  }

  return (
    <div className="Inbox-Knowledge custom-scrollbar">
      <Spin spinning={loading}>
        {/* 提问、搜索词 */}
        <Form {...layout} form={formForm} colon={false} labelAlign="left">
          <Form.Item label="提问">
            <div
              className={ellipsis ? "question-box open" : "question-box close"}
            >
              {searchWordInfo?.chatMsg
                .slice(0, ellipsis ? 1 : 7)
                .map((item, index) => (
                  <p key={index}>
                    {item.senderRole}： {item.content}
                  </p>
                ))}

              {searchWordInfo && (
                <span
                  className={ellipsis ? "ellipsis open" : "ellipsis close"}
                  onClick={() => {
                    setEllipsis(!ellipsis)
                  }}
                >
                  {ellipsis ? "全部内容" : "收起"}
                  <DoubleRightOutlined />
                </span>
              )}
            </div>
          </Form.Item>

          <Form.Item label="搜索词">
            <div className="condition-box">
              {searchWordInfo && (
                <>
                  <Form.Item name="radio">
                    <Radio.Group
                      options={searchWordInfo.searchTerms}
                      optionType="button"
                      buttonStyle="solid"
                      disabled={disabled}
                      onChange={(e) => {
                        getAnswerInfo({
                          source: "0",
                          name: "radio",
                        })
                      }}
                    />
                  </Form.Item>

                  <Space align="baseline">
                    <Form.Item
                      name="input"
                      getValueFromEvent={(e) => removeInputEmpty(e)}
                      style={{ width: "200px", margin: "0" }}
                      rules={[
                        { required: true, message: "请输入需要的搜索词" },
                      ]}
                    >
                      <Input
                        placeholder="请输入需要的搜索词"
                        disabled={disabled}
                        allowClear
                      />
                    </Form.Item>
                    <Button
                      disabled={disabled}
                      type="primary"
                      onClick={() => {
                        getAnswerInfo({
                          source: "1",
                          name: "input",
                        })
                      }}
                    >
                      确 定
                    </Button>
                  </Space>
                </>
              )}
            </div>
          </Form.Item>
        </Form>

        {/* 参考答案、引导语 */}
        <Tabs
          activeKey={activeKey}
          tabBarGutter={200}
          centered
          onChange={(key) => {
            setActiveKey(key)
            addKnowledgeTrack({
              userAction: 5,
            })
          }}
        >
          <Tabs.TabPane tab="参考答案" key="1">
            {answersInfo ? (
              <>
                <div className="referto-box">
                  <div className="header">
                    <Text strong>工小智</Text>
                    {answersInfo.xiaozhi?.content && (
                      <div
                        className="copy"
                        onClick={throttle(function (event) {
                          onAddContent(
                            document.getElementsByClassName("body")[0].innerText
                          )
                          addKnowledgeTrack({
                            userAction: 0,
                            content: answersInfo.xiaozhi.content,
                          })
                        }, 500)}
                      >
                        <CopyOutlined />
                        复制
                      </div>
                    )}
                  </div>
                  <div className="body">
                    {answersInfo.xiaozhi?.content ? (
                      <>
                        <WibotEditorView
                          html={replaceSpace(answersInfo.xiaozhi.content)}
                        />
                      </>
                    ) : (
                      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                  </div>
                  {(answersInfo.xiaozhi?.relatedKnowledge?.length && (
                    <div className="footer">
                      <Text italic strong>
                        参考来源：
                      </Text>
                      <div className="links">
                        {answersInfo.xiaozhi.relatedKnowledge.map(
                          (item, index) => (
                            <Link
                              key={index}
                              underline
                              italic
                              href={item.url}
                              target="_blank"
                            >
                              {item.knowledgeTitle}
                            </Link>
                          )
                        )}
                      </div>
                    </div>
                  )) ||
                    ""}
                </div>

                <div className="referto-box">
                  <div className="header">
                    <Text strong>知识库</Text>
                    {answersInfo.aiBksKnowledge?.content && (
                      <div
                        className="copy"
                        onClick={throttle(function () {
                          onAddContent(
                            document
                              .getElementsByClassName("body")[1]
                              .getElementsByTagName("span")[1].innerText
                          )
                          addKnowledgeTrack({
                            userAction: 1,
                            content: answersInfo.aiBksKnowledge.content,
                          })
                        }, 500)}
                      >
                        <CopyOutlined />
                        复制
                      </div>
                    )}
                  </div>
                  <div className="body">
                    {answersInfo.aiBksKnowledge?.content ? (
                      <>
                        <Text strong>
                          {answersInfo.aiBksKnowledge.contentTitle}
                        </Text>
                        <WibotEditorView
                          html={replaceSpace(
                            answersInfo.aiBksKnowledge.content
                          )}
                        />
                      </>
                    ) : (
                      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                  </div>
                </div>
              </>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </Tabs.TabPane>
          <Tabs.TabPane tab="引导语" key="2">
            {answersInfo?.aiPrompt?.content ? (
              <>
                <div
                  className="guide-box"
                  // 释放鼠标按键时触发，将已选内容复制。
                  onMouseUp={(event) => {
                    const selectedText = getSelectedText()
                    if (selectedText !== "") {
                      copyToClipboard(selectedText, event)
                      addKnowledgeTrack({
                        userAction: 4,
                        content: selectedText,
                      })
                    }
                  }}
                >
                  <div className="guides">
                    <WibotEditorView
                      html={replaceSpace(answersInfo.aiPrompt.content)}
                    />
                  </div>
                </div>
              </>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </Tabs.TabPane>
        </Tabs>
      </Spin>
    </div>
  )
});

export default Knowledge;
