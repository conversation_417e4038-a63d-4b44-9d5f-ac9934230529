.Inbox-Knowledge {
  min-width: 350px;
  height: 100%;
  padding-right: 4px;

  .ant-form {
    .question-box {
      border-radius: 8px;
      padding: 14px 20px;

      &.open {
        display: flex;

        p {
          width: 100px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      &.close {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        p {
          width: 100%;
        }
      }

      p {
        word-break: break-word;
        margin-bottom: 8px;

        &:nth-last-child(2) {
          margin: 0;
        }
      }

      .ellipsis {
        cursor: pointer;

        .anticon {
          margin-left: 4px;
          font-size: 12px;
        }

        &.open {
          .anticon {
            transform: rotate(90deg);
          }
        }

        &.close {
          .anticon {
            transform: rotate(-90deg);
          }
        }
      }
    }

    .condition-box {
      border: 1px dashed #cacaca;
      border-radius: 8px;
      padding: 14px 20px;

      .ant-radio-group {
        .ant-radio-button-wrapper {
          display: inline-table;
          margin-right: 10px;
        }
      }
    }

    .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
      background-color: #9e9e9e;
    }
  }

  .ant-tabs {
    padding: 4px 20px 20px;
    background: #f7f8fa;

    .ant-tabs-tabpane {
      .referto-box {
        padding: 14px;
        background: #fff;
        margin-bottom: 20px;

        &:last-child {
          margin: 0;
        }

        .header {
          display: flex;
          justify-content: space-between;

          .title {
            margin: 0;
          }

          .copy {
            cursor: pointer;

            .anticon {
              margin-right: 4px;
            }
          }
        }

        .body {
          margin-top: 10px;
          padding: 14px;
          background: #f6f6f6;
          color: #6a6a6a;
          word-break: break-word;

          .ant-typography {
            display: block;
            margin-bottom: 6px
          }
        }

        .footer {
          margin: 20px 0 0 14px;
          display: flex;

          .links {
            display: flex;
            flex-direction: column;
            flex: 1;
            margin-left: 10px;

            .ant-typography {
              width: fit-content;
              margin-bottom: 4px;

              &:last-child {
                margin: 0;
              }
            }
          }
        }
      }

      .guide-box {
        padding: 14px;
        background: #fff;

        .guides {
          display: flex;
          flex-direction: column;
          flex: 1;

          .ant-typography {
            margin-bottom: 4px;

            &:last-child {
              margin: 0;
            }
          }
        }
      }
    }
  }

  .ant-empty-normal {
    margin: 0;
  }
}