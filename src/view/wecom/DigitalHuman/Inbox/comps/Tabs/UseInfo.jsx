/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/11/23 17:23
 * @LastEditTime: 2024/03/05 16:19
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/Tabs/UseInfo.jsx
 * @Description: '收件箱客户详情'
 */

import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Spin, Timeline, Descriptions, Empty, Button, Skeleton } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import Icon from 'components/base/Icon';
import AppStore from 'stores/AppStore';
import "./UseInfo.less";

const UseInfoTab = forwardRef((props, ref) => {
  const { customerId } = props.params;
  const [loading, setLoading] = useState(false);
  // 客户详情
  const [userInfo, setUserInfo] = useState({});
  // 客户动态
  const [dynamicDataSource, setDynamicDataSource] = useState([]);
  const [paginations2, setPaginations2] = useState({
    current: 1,
    pageSize: 10,
  });

  useImperativeHandle(ref, () => ({
    initSelectChange,
    handleResetData,
  }));

  useEffect(() => {
    getCustomerInfo();
    fetchDynamicsList();
  }, []);

  const initSelectChange = (id) => {
    getCustomerInfo(id);
    fetchDynamicsList({ id });
  };

  const handleResetData = () => {
    setUserInfo({});
    setDynamicDataSource([]);
    setPaginations2({ current: 1, pageSize: 10 });
  };

  // 获取客户详情
  const getCustomerInfo = (id = null) => {
    setLoading(true);
    const data = {
      customerId: id || customerId,
    };
    apiCall('/customer/getInfo', 'GET', data)
      .then((res) => {
        setUserInfo(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 客户动态
  const fetchDynamicsList = async (params = {}) => {
    setLoading(true);
    const { id, pagination = paginations2 } = params;
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      customerId: id || customerId,
    };
    await apiCall('/customer/operationLog/page', 'GET', data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        if (params.push) {
          setDynamicDataSource(dynamicDataSource.concat(records));
        } else {
          setDynamicDataSource(records);
        }
        setPaginations2({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 客户动态
  const loadMoreBtn = () => (
    <Button
      style={{ display: 'flex', margin: '0px auto 0' }}
      type="primary"
      onClick={(e) => handleLoadMoreDynamic()}
    >
      加载更多
    </Button>
  );

  const handleLoadMoreDynamic = () => {
    let params = {};
    params = {
      push: true,
      pagination: {
        current: paginations2.current + 1,
        pageSize: paginations2.pageSize,
      },
    };
    fetchDynamicsList(params);
  };

  // 分享-客户详情
  const handleShare = () => {
    const { id } = userInfo;
    window.open(`/wecom/customer/details?id=${id}`, '_blank');
  };

  return (
    <div className="Inbox-UserInfo">
      <Spin spinning={loading}>
        {JSON.stringify(userInfo) == '{}' ? (
          <Skeleton active />
        ) : (
          <>
            <Descriptions
              title={
                <div className="wi-icon-info">
                  <span className="icon-info-text">{userInfo.name}</span>
                  <Icon
                    iconid="external-link"
                    size={18}
                    onClick={handleShare}
                  />
                </div>
              }
              layout="vertical"
              colon={false}
            >
              <Descriptions.Item label="性别" span={6}>
                <span
                >
                  {userInfo.gender == 1
                    ? '男'
                    : userInfo.gender == 0
                      ? '女'
                      : '未知'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="识别状态" span={6}>
                <span
                >
                  {userInfo.authFlag ? (
                    <span>
                      {moment(userInfo.authTime).format('YYYY-MM-DD')} 已识别
                    </span>
                  ) : (
                    '未识别'
                  )}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="上次沟通时间" span={6}>
                <span
                >
                  {userInfo.lastContactTime || '-'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="负责人" span={6}>
                <span
                >
                  {userInfo.employeeName || '-'}
                </span>
              </Descriptions.Item>
            </Descriptions>
            <div className="dynamic-title">
              客户动态
            </div>
            <div className="wi-dynamic custom-scrollbar">
              {dynamicDataSource.length > 0 ? (
                <Timeline mode="left">
                  {dynamicDataSource.map((item, index) => (
                    <Timeline.Item key={index}>
                      <span className="time">
                        {moment(item.logTime).format('YYYY-MM-DD HH:mm:ss')}
                      </span>
                      <ul className="content">
                        {/* 信息变动 */}
                        {item.type == 1 &&
                          item.detailVO?.newContentList?.map((atem, andex) => (
                            <li key={andex}>
                              <span className="title">{atem.type}</span>
                              <p>{atem.details}</p>
                            </li>
                          ))}

                        {/* 标签变动 */}
                        {item.type == 2 &&
                          item.detailVO.tagLog?.contentList?.map(
                            (atem, andex) => (
                              <li key={andex}>
                                <span className="title">
                                  {atem.type == 'ADD' ? '增加' : '删除'}
                                </span>
                                {atem.list?.map((btem, bndex) => (
                                  <p key={bndex}>{btem}</p>
                                ))}
                              </li>
                            )
                          )}

                        {/* 轨迹变动 */}
                        {item.type == 3 &&
                          item.detailVO?.contentList?.map((atem, andex) => (
                            <li key={andex}>
                              <span className="title">
                                {
                                  AppStore.state.g_sysdict.ACCESSED_TYPE.find(
                                    (btem, bndex) =>
                                      item.detailVO.accessType == btem[0]
                                  )[1]
                                }
                              </span>
                              {atem.list?.map((btem, bndex) => (
                                <p key={bndex}>{btem}</p>
                              ))}
                            </li>
                          ))}

                        {/* 群组变动 */}
                        {item.type == 4 &&
                          item.detailVO?.newContentList?.map((atem, andex) => (
                            <li key={andex}>
                              <span className="title">{atem.type}</span>
                              <p>{atem.details}</p>
                            </li>
                          ))}

                        {/* 好友状态变动 */}
                        {item.type == 6 &&
                          item.detailVO?.contentList?.map((atem, andex) => (
                            <li key={andex}>
                              {atem.list?.map((btem, bndex) => (
                                <p key={bndex}>{btem}</p>
                              ))}
                            </li>
                          ))}

                        {/* 资源标签变动 */}
                        {item.type == 7 &&
                          item.detailVO.tagLog?.contentList?.map(
                            (atem, andex) => (
                              <li key={andex}>
                                <span className="title">
                                  {atem.type == 'ADD' ? '增加' : '删除'}
                                </span>
                                {atem.list?.map((btem, bndex) => (
                                  <p key={bndex}>{btem}</p>
                                ))}
                              </li>
                            )
                          )}

                        {/* 朋友圈变动 */}
                        {item.type == 5 &&
                          item.detailVO?.contentList?.map((atem, andex) => (
                            <li key={andex}>
                              {atem.list?.map((btem, bndex) => (
                                <p key={bndex}>{btem}</p>
                              ))}
                            </li>
                          ))}
                      </ul>
                    </Timeline.Item>
                  ))}
                  {paginations2.current * paginations2.pageSize >=
                    paginations2.total
                    ? null
                    : loadMoreBtn()}
                </Timeline>
              ) : (
                <Empty
                  description="暂无客户动态"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )}
            </div>
          </>
        )}
      </Spin>
    </div>
  );
});

export default UseInfoTab;
