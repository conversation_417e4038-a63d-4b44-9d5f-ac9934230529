.Inbox-UserInfo {
  min-width: 350px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .ant-descriptions {
    .wi-icon-info {
      font-size: 18px;
      display: flex;
      align-items: center;

      .icon-info-text {
        display: inline-block;
        max-width: 264px;
        min-height: 28px;
        white-space: normal;
        word-break: break-all;
      }

      .icon {
        margin-left: 10px;
        cursor: pointer;

        svg {
          color: #000;
        }
      }
    }

    .ant-descriptions-row>th {
      padding: 0;
    }

    .ant-descriptions-item-label {
      color: #a5a5a5;
    }
  }

  .dynamic-title {
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;
    font-size: 18px;
  }

  .wi-dynamic {
    height: 423px;
    overflow-y: auto;
    padding-top: 10px;
    padding-right: 4px;

    .title {
      color: rgba(0, 0, 0, 0.85);
      font-weight: bold;
      font-size: 18px;
    }

    .time {
      color: #a5a5a5;
    }

    .ant-timeline {
      width: 100%;

      .ant-timeline-item {
        .ant-timeline-item-content {

          .title {
            font-weight: bold;
            display: flex;

            .name {
              font-weight: initial;
              color: #aaa;
              margin-left: 6px;
            }
          }

          .content {
            margin: 0;
            padding: 10px;
            border-radius: 6px;
            background: #f2f2f2;

            li {
              display: block;
            }

            p {
              margin: 0;
              display: flex;
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}