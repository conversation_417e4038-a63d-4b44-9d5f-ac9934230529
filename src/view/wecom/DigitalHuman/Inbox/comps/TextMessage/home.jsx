/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/07/19 13:58
 * @LastEditTime: 2024/06/14 11:09
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/comps/TextMessage/home.jsx
 * @Description: '文本消息'
 */

import React, { useState } from "react";
import {
  Avatar,
  Image,
  Row,
  Col,
  Typography,
  message,
  Tag,
  Empty,
} from "antd";
import {
  FileTextOutlined,
  TagOutlined,
  ReadOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import moment from "moment";
import LinkCard from "components/LinkCard/home";
import AppletCard from "components/AppletCard/home";
import { saveAs } from "file-saver";
import { apiCall } from "common/utils";
import { throttle } from 'common/fn';
import "./home.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const { Paragraph } = Typography;

const TextMessage = (props) => {
  const { data = {}, searchValue = "", handleVoice, onTogether } = props;
  const [errStatus, setErrStatus] = useState(false);

  const handleOperate = () => {
    apiCall("/inbox/employeeMessage", "POST", {
      messageId: data.messageId,
      content: data.message.content,
    })
      .then((res) => {
        message.success("收藏成功！");
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getHTMLTypeDom = () => {
    let htmlDom = "";
    switch (data.type) {
      case "TEXT":
        const strContent = data.message?.content;
        const index = strContent.indexOf(searchValue);
        const beforeStr = strContent.substring(0, index);
        const afterStr = strContent.slice(index + searchValue.length);
        htmlDom = (
          <div
            className={
              data.senderType == "EMPLOYEE"
                ? "wi-textMessage sender"
                : "wi-textMessage"
            }
            id={`textMessage${data.messageId}`}
          >
            {index > -1 ? (
              <span>
                {beforeStr}
                <span className="Text-Highlight">{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              <span>{strContent}</span>
            )}
          </div>
        );
        break;
      case "LINK":
        htmlDom = (
          <LinkCard
            data={{
              ...data.message,
              image: data.message?.imageUrl,
            }}
            isLink={false}
          />
        );
        break;
      case "WE_APP":
        htmlDom = <AppletCard data={data.message || {}} />;
        break;
      case "IMAGE":
        htmlDom = (
          <>
            {errStatus ?
              <Empty
                className="wi-type-empty"
                image={<ReloadOutlined />}
                description='重新加载'
                onClick={() => {
                  setErrStatus(false);
                }}
              />
              :
              <FileHOC src={data.fileUrl}>
                {(url) => (
                  <Image
                    id={`image${data.messageId}`}
                    src={url}
                    height={100}
                    style={{ objectFit: "contain" }}
                    onError={(e) => {
                      setErrStatus(true);
                    }}
                  />
                )}
              </FileHOC>
            }
          </>
        );
        break;
      case "VIDEO":
        htmlDom = (
          <>
            {errStatus ?
              <Empty
                className="wi-type-empty"
                image={<ReloadOutlined />}
                description='重新加载'
                onClick={() => {
                  setErrStatus(false);
                }}
              />
              :
              <FileHOC src={data.fileUrl}>
                {(url) => (
                  <video
                    id={`video${data.messageId}`}
                    style={{ height: "200px" }}
                    controls
                    src={url}
                    preload="auto"
                    onError={(e) => {
                      setErrStatus(true);
                    }}
                  />
                )}
              </FileHOC>
            }
          </>
        );
        break;
      case "FILE":
        htmlDom = (
          <FileHOC src={data.fileUrl}>
            {(url) => (
              <a
                className="file-card"
                onClick={() => {
                  saveAs(url, data.fileName);
                }}
              >
                <Row justify="space-between" align={"middle"}>
                  <Col span={19}>
                    <Paragraph strong ellipsis={{ rows: 2 }}>
                      {data.fileName}
                    </Paragraph>
                    <span style={{ color: "#999999" }}>{data.fileSize}</span>
                  </Col>
                  <Col>
                    <FileTextOutlined style={{ fontSize: "35px" }} />
                  </Col>
                </Row>
              </a>
            )}
          </FileHOC>
          
        );
        break;
      case "VOICE":
        htmlDom = (
          <div
            className={
              data.senderType == "EMPLOYEE" ? "voice-wrap sender" : "voice-wrap"
            }
            id={`voice${data.messageId}`}
            onClick={() => {
              handleVoice(data);
            }}
          >
            <div className="voice1"></div>
            <div className="voice2"></div>
            <div className="voice3"></div>
            {data.message?.playLength && (
              <div className="time">{data.message.playLength}"</div>
            )}
          </div>
        );
        break;
      case "EMOTION":
        htmlDom = (
          <>
            {errStatus ?
              <Empty
                className="wi-type-empty"
                image={<ReloadOutlined />}
                description='重新加载'
                onClick={() => {
                  setErrStatus(false);
                }}
              />
              :
              <FileHOC src={data.fileUrl}>
                {(url) => (
                  <Image
                    id={`emotion${data.messageId}`}
                    src={url}
                    height={100}
                    style={{ objectFit: "contain" }}
                    onError={(e) => {
                      setErrStatus(true)
                    }}
                  />
                )}
              </FileHOC>
            }
          </>
        );
        break;
      case "SESSION_DISAGREE":
        htmlDom = (
          <div
            className={data.senderType == "EMPLOYEE" ? "wi-textMessage sender" : "wi-textMessage"}
            style={{ color: '#ff4347' }}
          >
            对方不同意存档会话内容，你将无法继续提供服务
          </div>
        );
        break;
      case "SESSION_AGREE":
        htmlDom = (
          <div
            className={data.senderType == "EMPLOYEE" ? "wi-textMessage sender" : "wi-textMessage"}
            style={{ color: '#49bb18' }}
          >
            对方同意存档会话内容，你可以继续提供服务
          </div>
        );
        break;
    }
    return htmlDom;
  };

  return (
    <div
      className={
        data.senderType == "EMPLOYEE"
          ? "wi-messages-wrap sender"
          : "wi-messages-wrap"
      }
      id={data.messageId}
    >
      <Avatar
        className="avatar-wrap"
        src={
          data.senderType == "EMPLOYEE"
            ? data.employeeAvatar
            : data.customerAvatar
        }
      />
      <div className="message-wrap">
        <p
          className={
            data.senderType == "EMPLOYEE" ? "createTime sender" : "createTime"
          }
        >
          {moment(data.messageTime).format("YYYY/MM/DD") ==
            moment().format("YYYY/MM/DD")
            ? moment(data.messageTime).format("HH:mm:ss")
            : moment(data.messageTime).format("YYYY/MM/DD HH:mm:ss")}
          &nbsp;
          {data.senderType == "EMPLOYEE" && data.employeeName}
        </p>
        {getHTMLTypeDom()}
        {
          data.type == 'TEXT' && <div className="textMsgTags animated" style={data.senderType == "EMPLOYEE" ? { animationName: "fadeInRight" } : { animationName: "fadeInLeft" }}>
            {
              data.senderType == 'EMPLOYEE' && <Tag
                color="gold"
                icon={<TagOutlined />}
                onClick={() => {
                  handleOperate();
                }}>
                收藏
              </Tag>
            }
            <Tag
              color="purple"
              icon={<ReadOutlined />}
              onClick={throttle(function () {
                onTogether(data);
              }, 1000)}
            >
              随行
            </Tag>
          </div>
        }
      </div>
    </div>
  );
};

export default TextMessage;
