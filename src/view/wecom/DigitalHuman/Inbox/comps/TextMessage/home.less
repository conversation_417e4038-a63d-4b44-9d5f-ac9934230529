.wi-messages-wrap {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;

  .avatar-wrap {
    width: 36px;
    height: 36px;
    overflow: hidden;
    margin-right: 20px;
    margin-top: 18px;
  }

  .message-wrap {
    max-width: 80%;


    // 文本消息标签
    .textMsgTags {
      padding-top: 6px;
      display: none;

      .ant-tag {
        cursor: pointer;

        &:last-child {
          margin: 0;
        }
      }

      &:hover {
        display: block;
      }
    }

    .wi-textMessage {
      padding: 8px;
      border-radius: 6px;
      background: #fff;
      color: #232323;
      font-size: 14px;
      text-align: left;
      position: relative;
      white-space: pre-line;
      word-break: break-all;
      display: inline-block;

      &::before {
        content: "";
        width: 0px;
        height: 0px;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #fff;
        position: absolute;
        top: 8px;
        left: -12px;
        transform: rotate(-90deg);
      }

      &.sender {

        &::before {
          left: unset;
          right: -12px;
          transform: rotate(90deg);
        }
      }

      p {
        margin: 0;
      }

      &:hover {
        ~.textMsgTags {
          display: block;
        }
      }
    }

    .createTime {
      color: #a5a5a5;
      font-size: 14px;
      margin-bottom: 2px;

      &.sender {
        text-align: right;
      }
    }

    &.sender {
      flex-direction: row-reverse;

      .avatar-wrap {
        margin: 18px 0 0 20px;
      }

      .message-wrap {
        text-align: right;
      }
    }

    .file-card {
      display: inline-block;
      width: 230px;
      padding: 10px;
      background-color: #fff;
      border-radius: 6px;
      text-align: left;
      white-space: break-spaces;
      overflow: hidden;

      .ant-typography {
        margin-bottom: 0px;
      }
    }

    .voice-wrap {
      position: relative;
      display: inline-block;
      width: 100px;
      background-color: #fff;
      height: 30px;
      border-radius: 6px;
      cursor: pointer;

      &::before {
        content: "";
        width: 0px;
        height: 0px;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid #fff;
        position: absolute;
        top: 8px;
        left: -12px;
        transform: rotate(-90deg);
      }

      &.sender {
        &::before {
          left: unset;
          right: -12px;
          transform: rotate(90deg);
        }

        .voice1 {
          border-color: transparent transparent transparent #000;
          right: 0;
          left: unset;
        }

        .voice2 {
          border-color: transparent transparent transparent #000;
          right: -1px;
          left: unset;
        }

        .voice3 {
          border-color: transparent transparent transparent #000;
          right: -2px;
          left: unset;
        }

        .time {
          right: 25px;
          left: unset;
        }
      }

      .voice1 {
        width: 10px;
        height: 10px;
        border: 2px solid;
        border-radius: 50%;
        opacity: 1;
        border-color: transparent #000 transparent transparent;
        position: absolute;
        top: 9px;
      }

      .voice2 {
        width: 15px;
        height: 15px;
        border: 2px solid;
        border-radius: 50%;
        opacity: 1;
        border-color: transparent #000 transparent transparent;
        position: absolute;
        top: 7px;
        left: -1px;
      }

      .voice3 {
        width: 20px;
        height: 20px;
        border: 2px solid;
        border-radius: 50%;
        opacity: 1;
        border-color: transparent #000 transparent transparent;
        position: absolute;
        top: 5px;
        left: -2px;
      }

      &.pressed .voice2 {
        -webkit-animation: voice2 1.2s ease-in-out infinite;
      }

      &.pressed .voice3 {
        -webkit-animation: voice3 1.2s ease-in-out infinite;
      }

      @-webkit-keyframes voice2 {
        0% {
          opacity: 0;
        }

        20%,
        100% {
          opacity: 1;
        }
      }

      @-webkit-keyframes voice3 {
        0% {
          opacity: 0;
        }

        70%,
        100% {
          opacity: 1;
        }
      }

      .time {
        position: absolute;
        left: 25px;
        top: 3px;
      }
    }

    .link-card {
      border: unset;
      background: #fff;
    }
  }

  &.sender {
    flex-direction: row-reverse;

    .avatar-wrap {
      margin: 18px 0 0 20px;
    }

    .message-wrap {
      text-align: right;
    }
  }
}