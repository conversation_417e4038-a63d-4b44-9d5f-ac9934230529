/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/06/13 09:54
 * @LastEditTime: 2024/11/22 14:24
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Inbox/home.jsx
 * @Description: '收件箱'
 */

import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  Tooltip,
  message,
  Avatar,
  List,
  Spin,
  Empty,
  Typography,
  Dropdown,
  Tabs,
  notification,
} from "antd";
import moment from "moment";
import {
  FolderAddOutlined,
  OrderedListOutlined,
  StarOutlined,
  UserOutlined,
  ClockCircleOutlined,
  VerticalAlignTopOutlined,
  DownOutlined,
  MobileOutlined,
  MessageOutlined,
  PoweroffOutlined,
} from "@ant-design/icons";
import { scrollToBottom } from "common/scroll";
import { apiCall } from "common/utils";
import { timeStamp } from 'common/date';
import { getApiUrl } from 'config/index';
import { getLastSuccessfulConditionIndex } from 'common/array';
import { transferProtocol } from 'common/regular';
import Cookie from "js-cookie";
import MaterialModal from "components/Modal/MaterialModal/home";
import OperateModal from "components/Modal/OperateModal/index";
import ConversationModal from "./comps/ConversationModal";
import InfiniteScroll from "react-infinite-scroll-component";
import BenzAMRRecorder from "benz-amr-recorder";
import SdkCloud from "../comps/SdkCloud/home";
import MessageModal from "./comps/MessageModal";
import EndSessionModal from "./comps/EndSessionModal";
import TextMessage from "./comps/TextMessage/home";
import AskBaseTab from "./comps/Tabs/AskBase";
import EndSessionTab from "./comps/Tabs/EndSession";
import UseInfoTab from "./comps/Tabs/UseInfo";
import KnowledgeTab from "./comps/Tabs/Knowledge";
import WibotEmoji from "components/WibotEmoji/home";

import "./home.less";

const { TextArea } = Input;
const FormItem = Form.Item;
const { Paragraph } = Typography;
const { TabPane } = Tabs;
let timerInterval = null;
let timeAudio1 = null;
let timeAudio2 = null;
let timeoutWs = null;
let timerErrorWs = null;

const ConvenientReply = (props) => {
  const chatFormRef = useRef();
  const wiBodyRef = useRef();
  const [formForm] = Form.useForm();
  const textAreaRef = useRef();
  const askBaseRef = useRef();
  const useInfoRef = useRef();
  const knowledgeRef = useRef();
  const endSessionRef = useRef();
  const sessionListWSRef = useRef();
  const [customerLoading, setCustomerLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [globalLoading, setGlobalLoading] = useState(true);
  // ws
  const [initSessionWsUrl, setInitSessionWsUrl] = useState(null);
  const [sessionListWS, setSessionListWS] = useState(null);
  const [sessionListLockReconnect, setSessionListLockReconnect] = useState(false);
  const [sessionList, setSessionList] = useState([]);
  const [sessionSelected, setSessionSelected] = useState(null);
  // 会话客户信息
  const [sessionClientInfo, setSessionClientInfo] = useState(null);
  // 消息内容列表
  const [messageList, setMessageList] = useState([]);
  const [sendBtnStatus, setSendBtnStatus] = useState(true);
  // 监听是否有新消息
  const [listenSendMsg, setListenSendMsg] = useState(null);
  // 资源库
  const [materialModalParams, setMaterialModalParams] = useState({ visible: false, });
  const [paginationsEmp, setPaginationsEmp] = useState({ current: 1, pageSize: 10, });
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 20 });
  const [paginations1, setPaginations1] = useState({ current: 1, pageSize: 10, });
  // 查看当前会话其他员工
  const [conversationParams, setConversationParams] = useState({ visible: false, });
  // 当前会话员工列表
  const [sessionEmployee, setSessionEmployee] = useState([]);
  // 存储搜索值
  const [searchValue, setSearchValue] = useState("");
  // 存储排序值
  const [orderEnumSort, setOrderEnumSort] = useState("");
  // 存储语音对象
  const [amrObj, setAmrObj] = useState(null);
  // 选择的员工
  const [employeeSelected, setEmployeeSelected] = useState(null);
  // 集约员工列表
  const [employeeList, setEmployeeList] = useState([]);
  // 收件箱会话未读消息数
  const [unreadCountType, setUnreadCountType] = useState({
    allUnreadCount: 0,
    COLLECT: 0,
    TOP: 0,
    WAIT: 0,
  });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [tabsIndex, setTabsIndex] = useState("1");
  const [messageParams, setMessageParams] = useState({ visible: false });
  const [endSessionParams, setEndSessionParams] = useState({ visible: false });
  // 云手机
  const [deviceData, setDeviceData] = useState([]);
  // 已选的随行信息
  const [togetherInfo, setTogetherInfo] = useState(null);

  useEffect(() => {
    // notification的全局配置方法，在调用前提前配置，全局一次生效。
    notification.config({
      duration: 0,
      maxCount: 1,
    });
    // 获取ws后端地址前缀
    fetchWsBackendUrl();
    // // 初始化会话列表
    fetchIntensiveEmployeeList();
    // 收件箱会话未读消息数
    fetchCustomerUnreadCount();
    return () => {
      clearInterval(timeAudio1);
      clearInterval(timeAudio2);
      document.title = '企微支持系统后台';
      notification.destroy();
      if (amrObj) {
        amrObj.amr.stop(); // 停止语音播放
      }
      clearInterval(timerInterval);
      // 关闭播流状态
      if (deviceData.length > 0) {
        fetchMobileUnlock(deviceData[0].code);
      }
    };
  }, []);

  // 监听会话消息内容标记变化
  useEffect(() => {
    if (listenSendMsg) {
      let { senderType } = listenSendMsg;
      const newEmployeeList = JSON.parse(JSON.stringify(employeeList));
      const newSessionList = JSON.parse(JSON.stringify(sessionList));
      const newMessageList = JSON.parse(JSON.stringify(messageList));
      console.log(listenSendMsg, "listenSendMsg");
      if (
        listenSendMsg.inboxType == "MESSAGE" ||
        listenSendMsg.inboxType == "READ_EVENT"
      ) {
        // 监听所属员工的未读消息数
        newEmployeeList.forEach((item) => {
          if (item.id == listenSendMsg.employeeId) {
            item.unreadCount = listenSendMsg.unreadCount;
          }
        });
        setEmployeeList(newEmployeeList);
        setUnreadCountType({
          allUnreadCount: listenSendMsg.allUnreadCount,
          ...listenSendMsg.typeCount,
        });
      }
      switch (listenSendMsg.inboxType) {
        case "MESSAGE": // 新消息
          // 新消息语音提示
          let currentHour = new Date().getHours(); // 获取当前时间的小时数
          if (senderType == 'CUSTOMER' && currentHour >= 0 && currentHour < 6) { // 当前时间在指定范围内
            clearInterval(timeAudio1);
            clearInterval(timeAudio2);
            let sign = 1;
            timeAudio1 = setInterval(() => {
              if (sign == 1) {
                document.title = '🔴 您有一条新消息';
                sign = 0;
              } else {
                document.title = '⚪ 您有一条新消息';
                sign = 1;
              }
            }, 500);
            notification.info({
              message: '收到新消息！',
              description: <>
                <audio
                  type="audio/mpeg"
                  src={'audio/msgTip.mp3'}
                  autoPlay
                  loop
                />
              </>,
              onClose: () => {
                clearInterval(timeAudio1)
                timeAudio2 = setInterval(() => {
                  document.title = '企微支持系统后台';
                  document.title.includes("企微支持系统后台") && clearInterval(timeAudio2)
                }, 500);
              },
            });
          }
          // 置顶
          if (listenSendMsg.sessionTypeList.includes("TOP")) {
            listenSendMsg.isTop = true;
          }
          // 收藏
          if (listenSendMsg.sessionTypeList.includes("COLLECT")) {
            listenSendMsg.isCollect = true;
          }
          // 待跟进
          if (listenSendMsg.sessionTypeList.includes("WAIT")) {
            listenSendMsg.isWait = true;
          }
          if (
            !searchValue &&
            (listenSendMsg.employeeId == employeeSelected ||
              employeeSelected == "all" ||
              (employeeSelected == "TOP" && listenSendMsg.isTop) ||
              (employeeSelected == "COLLECT" && listenSendMsg.isCollect) ||
              (employeeSelected == "WAIT" && listenSendMsg.isWait))
          ) {
            newSessionList.forEach((item, index) => {
              if (item.sessionId == listenSendMsg.sessionId) {
                if (senderType == 'CUSTOMER') {
                  newSessionList.splice(index, 1);
                } else if (senderType == 'EMPLOYEE' && timeStamp(listenSendMsg.messageTime) > timeStamp(item.messageTime)) {
                  newSessionList.splice(index, 1, { ...listenSendMsg });
                }
              }
            });
            // 将来新信息客户置顶
            if (senderType == 'CUSTOMER') {
              if (listenSendMsg.isTop) {
                newSessionList.unshift({ ...listenSendMsg });
              } else {
                const findIdx = newSessionList.findIndex((item) => !item.isTop);
                newSessionList.splice(findIdx, 0, { ...listenSendMsg });
              }
            }
            setSessionList(newSessionList);
          }
          // 如果在当前会话页收到新消息
          if (
            listenSendMsg.sessionId == sessionSelected &&
            sessionClientInfo &&
            sessionClientInfo.current == 1
          ) {
            listenSendMsg.message = JSON.parse(listenSendMsg.message);
            const isEven = (item) => timeStamp(listenSendMsg.messageTime) > timeStamp(item.messageTime);
            const lastEvenIndex = getLastSuccessfulConditionIndex(newMessageList, isEven);
            newMessageList.splice(lastEvenIndex + 1, 0, { ...listenSendMsg });

            setSessionClientInfo({
              prevCurrent: sessionClientInfo.prevCurrent,
              nextCurrent: sessionClientInfo.nextCurrent,
              ...listenSendMsg,
            });
            setMessageList(newMessageList);
            sessionListWS.send(
              JSON.stringify({
                messageId: listenSendMsg.messageId,
                type: "READ",
              })
            );
          }
          break;
        case "READ_EVENT": // 已读消息
          // 监听客户已读信息取消红点
          newSessionList.forEach((item, index) => {
            if (item.sessionId == listenSendMsg.sessionId) {
              item.readFlag = true;
            }
          });
          setSessionList(newSessionList);
          break;
        case "WAIT_FOLLOW": // 跟进消息
          const newUnreadCountType = JSON.parse(
            JSON.stringify(unreadCountType)
          );
          // 同步待跟进未读数
          setUnreadCountType({
            ...newUnreadCountType,
            WAIT: listenSendMsg.typeCount.WAIT,
          });
          // 更改当前客户列表中的该客户跟进状态
          newSessionList.forEach((item, index) => {
            if (item.sessionId == listenSendMsg.sessionId) {
              // 如果是在待跟进客户列表并且当前广播客户是非未跟进则直接删掉该客户
              if (employeeSelected == "WAIT" && !listenSendMsg.waitFollow) {
                newSessionList.splice(index, 1);
              } else {
                item.isWait = listenSendMsg.waitFollow; // waitFollow为true表示待跟进
              }
            }
          });
          setSessionList(newSessionList);
          break;
      }
      let timer = setTimeout(() => {
        setListenSendMsg(null);
        clearTimeout(timer);
      }, 100);
    }
  }, [listenSendMsg]);

  useEffect(
    () => () => {
      sessionListWSRef.current && sessionListWSRef.current.close();
      heartCheck.reset();
      clearTimeout(timeoutWs);
      timeoutWs = null;
      clearTimeout(timerErrorWs);
      timerErrorWs = null;
    },
    []
  );

  // 获取ws后端地址前缀
  const fetchWsBackendUrl = () => {
    const url = `${transferProtocol()}//${process.env.NODE_ENV == 'development' ? 'test.wizone.work' : location.host}${getApiUrl()}/wecom/message/inbox/ws?token=${Cookie.get("weebot_cloud_token")}`;
    setInitSessionWsUrl(url);
    fetchSessionList(url);
  };

  useEffect(() => {
    if (sessionClientInfo) {
      clearInterval(timerInterval);
      heartRead();
    }
  }, [sessionClientInfo, sessionListWS]);

  const heartRead = (info = sessionClientInfo) => {
    if (info) {
      // 需要定时器告诉后端某个客户正在进行当前会话(10秒一次)
      timerInterval = setInterval(function () {
        sessionListWS?.send(
          JSON.stringify({
            messageId: info.messageId,
            type: "READ",
          })
        );
      }, 10000);
    }
  };

  // webSocket心跳机制-防止自动断开连接
  const heartCheck = {
    timeout: 40000, // 40秒
    timeoutObj: null,
    reset: function () {
      clearInterval(this.timeoutObj);
      return this;
    },
    start: function (webSocket) {
      this.timeoutObj = setInterval(function () {
        // 这里发送一个心跳，后端收到后，返回一个心跳消息，
        // onmessage拿到返回的心跳就说明连接正常
        webSocket.send("HeartBeat");
      }, this.timeout);
    },
  };
  // 连接webSocket获取会话新消息
  const fetchSessionList = (url = initSessionWsUrl) => {
    setGlobalLoading(true);
    const webSocket = new WebSocket(url);
    setSessionListWS(webSocket);
    sessionListWSRef.current = webSocket;
    webSocket.onopen = () => {
      console.log("连接上 webSocket 服务端了 - fetchSessionList");
      clearTimeout(timeoutWs);
      timeoutWs = null;
      setGlobalLoading(false);
      heartCheck.reset().start(webSocket);
    };
    webSocket.onerror = (error) => {
      console.log(error, "error");
      if (sessionListLockReconnect) {
        return;
      }
      // 没连接上会一直重连，设置延迟避免请求过多
      timerErrorWs = setTimeout(function () {
        fetchSessionList(url);
        setSessionListLockReconnect(false);
        clearTimeout(timerErrorWs);
      }, 4000);
    };
    webSocket.onclose = (event) => {
      console.log(
        "fetchSessionList - webSocket 断开: " +
        event.code +
        " " +
        event.reason +
        " " +
        event.wasClean
      );
      clearInterval(timerInterval);
      if (event.code == "1000") {
        return false;
      }
      if (event.code == "4401") {
        // token过期跳往登录页
        apiCall("/verifytoken", "POST", {})
          .then((retdata) => { })
          .catch((result) => {
            sessionListWSRef.current && sessionListWSRef.current.close();
            heartCheck.reset();
            clearTimeout(timeoutWs);
            timeoutWs = null;
          });
        return false;
      }

      if (timeoutWs) {
        return;
      }
      // 没连接上会一直重连，设置延迟避免请求过多
      timeoutWs = setTimeout(function () {
        fetchSessionList(url);
        clearTimeout(timeoutWs);
      }, 4000);
    };
    webSocket.onmessage = (event) => {
      // 闭包,无法获取外部变量
      setSessionListLockReconnect(true);
      // event 为服务端传输的消息，在这里可以处理
      // console.log(event, 'onmessage');
      const { data } = event;
      const parseData = JSON.parse(data);
      parseData.inboxType != "HEART_BEAT" && setListenSendMsg(parseData);
    };
  };

  // 收件箱会话未读消息数
  const fetchCustomerUnreadCount = () => {
    apiCall("/message/inbox/session/unread/count", "GET")
      .then((res) => {
        setUnreadCountType({
          allUnreadCount: res.allUnreadCount,
          ...res.typeCount,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 收件箱集约运营账号分页
  const fetchIntensiveEmployeeList = async (params = {}) => {
    setGlobalLoading(true);
    const { pagination = paginationsEmp } = params;
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
    };
    await apiCall("/message/inbox/intensiveEmployee", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        if (params.push) {
          setEmployeeList([...employeeList, ...records]);
        } else {
          setEmployeeList(records);
        }
        setPaginationsEmp({
          current: current,
          pageSize: size,
          total: total,
        });
        if (current == 1 && records.length > 0) {
          setEmployeeSelected("all");
          fetchCustomerSessionList({
            pagination: {
              current: 1,
              pageSize: 20,
            },
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setGlobalLoading(false);
      });
  };

  // 收件箱客户会话记录分页列表
  const fetchCustomerSessionList = async (params = {}) => {
    setCustomerLoading(true);
    const {
      pagination = paginations,
      deptIdList = employeeSelected,
      orderEnum,
      message,
      type,
    } = params;
    const data = {
      current: pagination.current,
      size: pagination.pageSize,
      deptIdList,
      orderEnum: orderEnum || orderEnumSort || null,
      message,
      type,
    };
    if (
      data.deptIdList == "all" ||
      data.deptIdList == "COLLECT" ||
      data.deptIdList == "TOP" ||
      data.deptIdList == "WAIT"
    ) {
      data.deptIdList = null;
    }
    await apiCall("/inbox/session/inbox/session", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        records.forEach((item) => {
          item.message = JSON.parse(item.message);
          // 相关操作添加标识
          // 置顶
          if (!message && item.sessionTypeList.includes("TOP")) {
            item.isTop = true;
          }
          // 收藏
          if (item.sessionTypeList.includes("COLLECT")) {
            item.isCollect = true;
          }
          // 待跟进
          if (item.sessionTypeList.includes("WAIT")) {
            item.isWait = true;
          }
        });
        if (params.push) {
          const newSessionList = JSON.parse(JSON.stringify(sessionList));
          const newSessionIds = newSessionList.map((item) => item.sessionId);
          if (orderEnum) {
            // 筛选最新/未读会话
            setSessionList(records);
          } else {
            // 正常下拉加载并且排除掉由于新消息而置顶下拉才出现的同一客户
            records.forEach((item) => {
              if (!newSessionIds.includes(item.sessionId)) {
                newSessionList.push(item);
              }
            });
            setSessionList(newSessionList);
          }
        } else {
          setSessionList(records);
        }

        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setCustomerLoading(false);
      });
  };

  // 会话消息记录分页
  const fetchSessionMessage = async (params = {}) => {
    setLoading(true);
    const { sessionId, pagination = paginations1, type = "" } = params;
    const data = {
      sessionId,
      current: pagination.current,
      size: pagination.pageSize,
    };
    apiCall("/message/inbox/message", "GET", data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        records.forEach((item) => {
          item.message = JSON.parse(item.message);
        });
        if (type == "prev") {
          const elementId = document.getElementsByClassName(
            "wi-messages-wrap"
          )[0].id; // 获取第一条数据id，用于处理会话消息定位
          setSessionClientInfo({
            ...sessionClientInfo,
            prevCurrent: current,
            pages: pages,
            messageId: elementId,
          });
          setMessageList([...records].concat([...messageList]));
        } else if (type == "next") {
          const elementId = document.getElementsByClassName("wi-messages-wrap")[
            document.getElementsByClassName("wi-messages-wrap").length - 1
          ].id; // 获取第一条数据id，用于处理会话消息定位
          setSessionClientInfo({
            ...sessionClientInfo,
            nextCurrent: current,
            current: current, // 防止搜索到的消息不是第一页然后下拉到第一页之后收不到ws新消息
            pages: pages,
            messageId: elementId,
          });
          setMessageList([...messageList].concat([...records]));
        } else {
          setMessageList([...messageList, ...records]);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 点击选择员工
  const handleSelectEmployee = async (item) => {
    if (employeeSelected && item == employeeSelected) {
      return false;
    }

    if (amrObj) {
      amrObj.amr.stop(); // 停止语音播放
    }
    // 关闭播流状态
    if (deviceData.length > 0) {
      fetchMobileUnlock(deviceData[0].code);
      setDeviceData([]);
    }
    // 切换会话客户主动清除会话内容
    formForm.resetFields();
    setSearchValue("");
    setOrderEnumSort("");
    clearInterval(timerInterval);
    setSessionList([]);
    setMessageList([]);
    setSessionSelected(null);
    setSessionClientInfo(null);
    setEmployeeSelected(item);
    askBaseRef.current?.handleResetData();
    useInfoRef.current?.handleResetData();
    endSessionRef.current?.handleResetData();
    const obj = {
      deptIdList: item,
      orderEnum: "LATEST",
      pagination: {
        current: 1,
        pageSize: 20,
      },
    };
    if (item == "COLLECT" || item == "TOP" || item == "WAIT") {
      obj.type = item;
    }
    await fetchCustomerSessionList(obj);
  };

  // 点击选择会话客户
  const handleSelectSessionClient = async (item) => {
    if (sessionSelected && item.sessionId == sessionSelected && !searchValue) {
      return false;
    }
    if (item.sessionId == sessionSelected && searchValue) {
      setMessageList([]);
    }
    if (amrObj) {
      amrObj.amr.stop(); // 停止语音播放
    }
    // 关闭播流状态
    if (deviceData.length > 0) {
      fetchMobileUnlock(deviceData[0].code);
      setDeviceData([]);
    }
    // 切换会话客户主动清除会话内容
    chatFormRef.current?.resetFields();
    setMessageList([]);
    setSessionSelected(item.sessionId);
    setSessionClientInfo({
      ...item,
      prevCurrent: item.current,
      nextCurrent: item.current,
    });
    sessionListWS.send(
      JSON.stringify({
        messageId: item.messageId,
        type: "READ",
      })
    );
    // 在点进客户会话时就将该客户未读变已读，防止点进该客户会话就马上进行客户列表下拉分页的数据异步问题
    // 监听客户已读信息取消红点
    const newSessionList = JSON.parse(JSON.stringify(sessionList));
    newSessionList.forEach((atem) => {
      if (atem.sessionId == item.sessionId) {
        atem.readFlag = true;
      }
    });
    setSessionList(newSessionList);
    askBaseRef.current?.handleScrollToTop();
    await askBaseRef.current?.handleResetData();
    await askBaseRef.current?.fetchKnowledgeQuestions();
    await useInfoRef.current?.handleResetData();
    await useInfoRef.current?.initSelectChange(item.customerId);
    await endSessionRef.current?.handleResetData();
    await endSessionRef.current?.initSelectChange(item.customerId);
    // 切换客户，查询知识随行缓存。
    setTogetherInfo({
      time: new Date().getTime(),
      sessionId: item.sessionId
    })
  };

  // 监听是否选择会话列表客户sessionKey
  useEffect(() => {
    if (sessionSelected) {
      fetchSessionMessage({
        sessionId: sessionSelected,
        pagination: {
          current: sessionClientInfo.current,
          pageSize: 10,
        },
      });
      getCurrentSessionEmployee(sessionSelected);
    }
  }, [sessionSelected]);

  useEffect(() => {
    if (messageList.length) {
      let {
        messageId,
        current,
        prevCurrent = null,
        nextCurrent = null,
      } = sessionClientInfo;
      // 处理会话消息定位
      let messageIdBox = document.getElementById(`${messageId}`);
      if (messageIdBox) {
        wiBodyRef.current.scrollTop =
          messageIdBox.offsetTop - messageIdBox.clientHeight; // 滚动条位置= 当前消息偏移量-当前消息高度
        setSessionClientInfo({
          ...sessionClientInfo,
          topHeight: 0, // 默认顶部距离为0
          bottomHeight:
            wiBodyRef.current.scrollHeight - wiBodyRef.current.clientHeight, // scrollHeight属性返回元素的内容高度（包括溢出部分），而clientHeight属性返回元素的可视高度。两者之差就是可以滚动的滚动条的总高度。
          prevCurrent: prevCurrent || current,
          nextCurrent: nextCurrent || current,
        });
      }

      if (
        messageList.length < 10 &&
        sessionClientInfo.current > 1 &&
        searchValue
      ) {
        fetchSessionMessage({
          sessionId: sessionClientInfo.sessionId,
          pagination: {
            current: sessionClientInfo.current - 1,
            pageSize: 10,
          },
          type: "next",
        });
      }
    }

    if (messageList.length == 0 && searchValue) {
      fetchSessionMessage({
        sessionId: sessionClientInfo.sessionId,
        pagination: {
          current: sessionClientInfo.current,
          pageSize: 10,
        },
      });
    }
  }, [messageList]);

  // 收件箱同在当前会话员工列表
  const getCurrentSessionEmployee = (sessionId) => {
    apiCall(
      `/message/inbox/message/sessionEmployee?sessionId=${sessionId}`,
      "GET"
    )
      .then((res) => {
        setSessionEmployee(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 会话记录下拉加载
  const loadMoreData = (e, type = null) => {
    if (type == "customer") {
      if (sessionList.length < paginations.total) {
        const pageInfo = {
          ...paginations,
          current: paginations.current + 1,
        };
        fetchCustomerSessionList({
          pagination: pageInfo,
          push: true,
          message: searchValue,
          type:
            employeeSelected == "COLLECT" ||
              employeeSelected == "TOP" ||
              employeeSelected == "WAIT"
              ? employeeSelected
              : null,
        });
      }
    } else if (type == "emp") {
      if (employeeList.length < paginationsEmp.total) {
        const pageInfo = {
          ...paginationsEmp,
          current: paginationsEmp.current + 1,
        };
        fetchIntensiveEmployeeList({ pagination: pageInfo, push: true });
      }
    }
  };

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native;
    const content = chatFormRef.current.getFieldValue("content") || "";
    const inputIndex = document.getElementById("msgTextInput"); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      content.substring(0, startPos) + insertEmoji + content.substring(endPos);
    chatFormRef.current.setFieldsValue({
      content: text,
    });
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    );
    // setIsShowEmoji(false);
    setSendBtnStatus(false);
  };

  // 资源库
  const handleResource = () => {
    setMaterialModalParams({
      visible: true,
      okText: "发送",
      onSubmit: (data) => {
        setMaterialModalParams({ visible: false });
        console.log(data, "datadatadata");
        data.forEach((item) => {
          let msg = "";
          switch (item.type) {
            case "copyWriter":
              item.content = item.copyWriter;
              break;
            case "pageArticle":
              msg = (item.images && item.images[0]) || "";
              item.imageUrl = msg;
              item.image = msg;
              item.fileId = item.images;
              item.url = item.transitUrl || item.url;
              break;
            case "Picture":
              msg = (item.fileId && item.fileId[0]) || "";
              item.imageUrl = msg;
              break;
            case "Article":
              msg = (item.images && item.images[0]) || "";
              item.imageUrl = msg;
              item.image = msg;
              item.fileId = item.images;
              item.url = item.transitUrl || item.url;
              break;
            case "Video":
              msg = (item.videos && item.videos[0]) || "";
              item.videoUrl = msg;
              item.videos = item.videos || [];
              item.fileId = item.videos;
              item.url = item.transitUrl || item.url;
              break;
            case "Poster":
              msg = (item.fileId && item.fileId[0]) || "";
              item.imageUrl = msg;
              break;
            case "MINI_PROGRAM": // 小程序
              msg = (item.miniProgram && item.miniProgram.fileId) || "";
              item.fileId = item.miniProgram && [item.miniProgram.fileId];
              item.url = item.miniProgram?.page || item.miniProgram?.url;
              item.appId = item.miniProgram?.appId;
              break;
            case "FORM": // 表单
              item.image = item.shareImage || item.image;
              item.title = item.shareTitle;
              item.description = item.shareDescription;
              msg = item.image || "";
              item.imageUrl = msg;
              item.url = item.transitUrl || item.url;
              item.fileId = [item.image];
              break;
            case "Product":
              msg = (item.images && item.images[0]) || "";
              item.imageUrl = msg;
              item.fileId = item.images;
              item.url = item.transitUrl || item.url;
              break;
          }
        });
        setLoading(true);
        apiCall(
          `/message/inbox/message/send?sessionId=${sessionSelected}`,
          "POST",
          data
        )
          .then((res) => {
            // 将当前发送消息的客户置顶
            if (!searchValue && employeeSelected != "WAIT") {
              const newSessionList = JSON.parse(JSON.stringify(sessionList));
              const findItem = newSessionList.find(
                (item) => item.sessionId == sessionSelected
              );
              newSessionList.forEach((item, index) => {
                if (item.sessionId == sessionSelected) {
                  newSessionList.splice(index, 1);
                }
              });
              const findIdx = newSessionList.findIndex((item) => !item.isTop);
              newSessionList.splice(findIdx, 0, { ...findItem });
              setSessionList(newSessionList);
            }
            // 发送成功清空文本域
            scrollToBottom("wi-messages-list");
            message.warning("信息来源自会话存档，展示会有延迟！");
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setLoading(false);
          });
      },
      onCancel: () => {
        setMaterialModalParams({ visible: false });
      },
    });
  };

  // 播流状态
  const fetchMobileUnlock = (code) => {
    apiCall(`/device/unlock?codes=${code}`, "POST")
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      });
  };

  // 播流云手机
  const handleMobile = () => {
    setOperateParams({
      visible: true,
      title: "打开控制窗口确认",
      content: "打开控制窗口将会中断收件箱发送消息，确定这样做吗？",
      onSubmit: () => {
        setGlobalLoading(false);
        apiCall("/inbox/session/device", "GET", {
          sessionId: sessionClientInfo.sessionId,
        })
          .then((res) => {
            setDeviceData([{ ...res, x: 600 }]);
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setGlobalLoading(false);
          });
        setOperateParams({
          visible: false,
        });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  // 关闭云手机
  const handleCloseCloud = (item, index) => {
    setDeviceData([]);
  };

  // 结束会话
  const handleEndSession = () => {
    console.log(sessionClientInfo);
    setEndSessionParams({
      visible: true,
      sessionClientInfo: sessionClientInfo,
      onSubmit: async () => {
        handleOperate("WAIT", sessionClientInfo, false);
        setEndSessionParams({ visible: false });
        await endSessionRef.current?.handleResetData();
        await endSessionRef.current?.initSelectChange(
          sessionClientInfo.customerId
        );
      },
      onCancel: () => {
        setEndSessionParams({ visible: false });
      },
    });
  };

  // 监听textArea内容变化
  const onChangeTextArea = (event) => {
    const { value } = event.target;
    if (!value || value.match(/^\s*$/)) {
      setSendBtnStatus(true);
    } else {
      setSendBtnStatus(false);
    }
  };

  // 监听键盘enter事件,shift+enter换行
  let timeoutEnter = null;
  const onPressEnterTextArea = (event) => {
    if (!event.shiftKey && event.keyCode == 13) {
      event.cancelBubble = true; // ie阻止冒泡行为
      event.stopPropagation(); // Firefox阻止冒泡行为
      event.preventDefault(); // 取消事件的默认动作*换行
    }
    if (timeoutEnter) {
      clearTimeout(timeoutEnter);
    }
    timeoutEnter = setTimeout(() => {
      if (!event.shiftKey && event.keyCode == 13) {
        // 以下处理发送消息代码
        sessionMessageSend();
      }
    }, 400);
  };

  // 会话发送消息
  const sessionMessageSend = () => {
    chatFormRef.current.validateFields().then((formData) => {
      const { content } = formData;
      if (!content || content.match(/^\s*$/)) {
        message.warning("不能发送空白消息");
        // 空白内容主动清空文本域
        chatFormRef.current.resetFields();
        return false;
      }
      setLoading(true);
      const data = [
        {
          type: "copyWriter",
          content,
        },
      ];
      apiCall(`/message/inbox/message/send?sessionId=${sessionSelected}`, "POST", data).then((res) => {
        // 将当前发送消息的客户置顶
        if (!searchValue && sessionList.some((item) => item.sessionId == sessionClientInfo.sessionId)) {
          const newSessionList = JSON.parse(JSON.stringify(sessionList));
          const findItem = newSessionList.find((item) => item.sessionId == sessionSelected);
          for (let index = 0; index < sessionList.length; index++) {
            const item = newSessionList[index];
            if (item.sessionId == sessionSelected) {
              newSessionList.splice(index, 1);
              if (item.isTop) {
                newSessionList.splice(0, 0, { ...findItem });
              } else {
                const findIdx = newSessionList.findIndex((item) => !item.isTop);
                newSessionList.splice(findIdx, 0, { ...findItem });
              }
              setSessionList(newSessionList);
              break;
            }
          }
        }
        // 发送成功清空文本域
        message.warning("信息来源自会话存档，展示会有延迟！");
        scrollToBottom("wi-messages-list");
        chatFormRef.current.resetFields();
        textAreaRef.current.focus({
          cursor: "start",
        });
        setSendBtnStatus(true);
        // 知识随行内容发送成功后记录埋点。
        knowledgeRef.current?.addKnowledgeTrack({
          userAction: 3,
          content: content,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 查看当前会话中的其他员工
  const handleConversation = () => {
    setConversationParams({
      visible: true,
      data: sessionEmployee,
      onCancel: () => {
        setConversationParams({ visible: false });
      },
    });
  };

  let timeout = null;
  // 防抖搜索查询会话内容
  const handleSearch = (e) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      setSearchValue(e.target.value);
      const obj = {
        message: e.target.value,
        deptIdList: employeeSelected == "all" ? null : employeeSelected,
        pagination: {
          current: 1,
          pageSize: 20,
        },
      };
      if (
        employeeSelected == "COLLECT" ||
        employeeSelected == "TOP" ||
        employeeSelected == "WAIT"
      ) {
        obj.type = employeeSelected;
        obj.deptIdList = null;
      }
      fetchCustomerSessionList({
        ...obj,
      });
    }, 1000);
  };

  // 排序
  const handleSort = (key) => {
    setSessionList([]);
    setOrderEnumSort(key);
    const obj = {
      orderEnum: key,
      message: searchValue,
      pagination: {
        current: 1,
        pageSize: 20,
      },
    };
    if (
      employeeSelected == "COLLECT" ||
      employeeSelected == "TOP" ||
      employeeSelected == "WAIT"
    ) {
      obj.type = employeeSelected;
    }
    fetchCustomerSessionList({ ...obj });
  };

  const onChangeTabs = (index) => {
    setTabsIndex(index);
    askBaseRef.current?.handleScrollToTop();
  };

  // 客户操作
  const handleOperate = (type, data, isNeedFetch = true) => {
    const newSessionList = JSON.parse(JSON.stringify(sessionList));
    if (
      (employeeSelected == "TOP" && type == "TOP") ||
      (employeeSelected == "COLLECT" && type == "COLLECT") ||
      (employeeSelected == "WAIT" && type == "WAIT")
    ) {
      newSessionList.forEach((item, index) => {
        if (item.sessionId == data.sessionId) {
          newSessionList.splice(index, 1);
        }
      });
    } else {
      if (type == "TOP" && !searchValue) {
        data.isTop = !data.isTop;
        newSessionList.forEach((item, index) => {
          if (item.sessionId == data.sessionId) {
            newSessionList.splice(index, 1);
          }
        });
        const findIdx = newSessionList.findIndex((item) => !item.isTop);
        if (findIdx < 0) {
          newSessionList.push(data)
        } else {
          newSessionList.splice(findIdx, 0, data);
        }
      } else if (type == "COLLECT") {
        newSessionList.forEach((item) => {
          if (item.sessionId == data.sessionId) {
            item.isCollect = !data.isCollect;
          }
        });
      } else {
        newSessionList.forEach((item) => {
          if (item.sessionId == data.sessionId) {
            item.isWait = isNeedFetch ? !data.isWait : false;
          }
        });
      }
    }
    setSessionList(newSessionList);
    isNeedFetch &&
      fetchEmployeeSessionType({ sessionId: data.sessionId, type });
  };

  // 客户置顶收藏跟进相关操作
  const fetchEmployeeSessionType = (params) => {
    setCustomerLoading(true);
    apiCall("/inbox/employeeSession", "POST", params)
      .then((res) => {
        const newCountType = JSON.parse(JSON.stringify(unreadCountType));
        setUnreadCountType({
          ...newCountType,
          ...res.typeCount,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setCustomerLoading(false);
      });
  };

  // 会话列表根据消息类型显示描述
  const getDescriptionType = (data = {}) => {
    let msg = "";
    switch (data.type) {
      case "TEXT":
        const strContent =
          data.message?.content || data.messageMap?.content || "";
        const index = strContent.indexOf(searchValue);
        const beforeStr = strContent.substring(0, index);
        const afterStr = strContent.slice(index + searchValue.length);
        msg =
          index > -1 ? (
            <span>
              {beforeStr}
              <span className="Text-Highlight">{searchValue}</span>
              {afterStr}
            </span>
          ) : (
            <span>{strContent}</span>
          );
        break;
      case "LINK":
        const strtTitle1 = data.message?.title || data.messageMap?.title || "";
        const index1 = strtTitle1.indexOf(searchValue);
        const beforeStr1 = strtTitle1.substring(0, index1);
        const afterStr1 = strtTitle1.slice(index1 + searchValue.length);
        msg =
          index1 > -1 ? (
            <span>
              [链接] {beforeStr1}
              <span className="Text-Highlight">{searchValue}</span>
              {afterStr1}
            </span>
          ) : (
            <span>[链接] {strtTitle1}</span>
          );
        break;
      case "WE_APP":
        const strtTitle2 = data.message?.title || data.messageMap?.title || "";
        const index2 = strtTitle2.indexOf(searchValue);
        const beforeStr2 = strtTitle2.substring(0, index2);
        const afterStr2 = strtTitle2.slice(index2 + searchValue.length);
        msg =
          index2 > -1 ? (
            <span>
              [小程序] {beforeStr2}
              <span className="Text-Highlight">{searchValue}</span>
              {afterStr2}
            </span>
          ) : (
            <span>[小程序] {strtTitle2}</span>
          );
        break;
      case "IMAGE":
        msg = "[图片]";
        break;
      case "VIDEO":
        msg = "[视频]";
        break;
      case "VOICE":
        msg = "[语音]";
        break;
      case "FILE":
        msg = "[文件]";
        break;
      case "EMOTION":
        msg = "[表情]";
        break;
      case "SESSION_DISAGREE":
        msg = <span>对方不同意存档会话内容，你将无法继续提供服务</span>
        break;
      case "SESSION_AGREE":
        msg = <span>对方同意存档会话内容，你可以继续提供服务</span>
        break;
    }
    return msg;
  };

  const handleVoice = (data) => {
    const activeVoice = document.getElementById(`voice${data.messageId}`);
    if (amrObj) {
      if (amrObj.messageId == data.messageId) {
        if (amrObj.amr.isPlaying()) {
          console.log("播放着");
          activeVoice.classList.remove("pressed");
          amrObj.amr.pause();
        } else {
          console.log("暂停了");
          activeVoice.classList.add("pressed");
          amrObj.amr.play();
        }
        return false;
      } else {
        const activeVoices = document.querySelectorAll(".voice-wrap");
        activeVoices.forEach((item) => {
          item.classList.remove("pressed");
        });
        amrObj.amr.pause();
      }
    }
    console.log("还在下来?");
    data.amr = new BenzAMRRecorder();
    data.amr.initWithUrl(data.fileUrl).then(function () {
      activeVoice.classList.add("pressed");
      data.amr.play();
    });
    data.amr.onEnded(function () {
      activeVoice.classList.remove("pressed");
      console.log("播放完毕");
    });
    setAmrObj({ ...data });
  };

  // 收藏消息
  const handleMessage = () => {
    setMessageParams({
      visible: true,
      onCancel: async (obj) => {
        setMessageParams({ visible: false });
      },
    });
  };

  // 将收藏消息追加到消息框
  const handleAddMessageSession = async (data) => {
    const content = chatFormRef.current.getFieldValue("content") || "";
    const inputIndex = document.getElementById("msgTextInput"); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      content.substring(0, startPos) + data.content + content.substring(endPos);
    chatFormRef.current.setFieldsValue({
      content: text,
    });
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + data.content.length,
      endPos + data.content.length
    );
    setSendBtnStatus(false);
  };

  // 点击文本消息随行
  const handleTogether = (data) => {
    setTabsIndex('4');
    askBaseRef.current?.handleScrollToTop();
    setTogetherInfo({
      time: new Date().getTime(),
      ...data,
    })
  }

  return (
    <div className="ConvenientReply">
      <Spin spinning={globalLoading}>
        <div className="globalRow">
          <div className="employeeList custom-scrollbar" id="scrollableDivEmp">
            {employeeList.length > 0 ? (
              <InfiniteScroll
                dataLength={employeeList.length}
                next={(e) => {
                  loadMoreData(e, "emp");
                }}
                hasMore={employeeList.length < paginationsEmp.total}
                scrollableTarget="scrollableDivEmp"
              >
                <div
                  className={`employeeItem ${employeeSelected == "all" &&
                    "selected"}`}
                  onClick={() => handleSelectEmployee("all")}
                >
                  <div className="avatarDot">
                    <UserOutlined className="avatarIcon" />
                    {unreadCountType.allUnreadCount > 0 &&
                      (unreadCountType.allUnreadCount < 100 ? (
                        <div className="avatarDotBox">
                          {unreadCountType.allUnreadCount}
                        </div>
                      ) : (
                        <div className="avatarDotBox dots">...</div>
                      ))}
                  </div>
                  <div className="employeeItem-name">全部账号</div>
                </div>
                <div
                  className={`employeeItem ${employeeSelected == "COLLECT" &&
                    "selected"}`}
                  onClick={() => handleSelectEmployee("COLLECT")}
                >
                  <div className="avatarDot">
                    <StarOutlined className="avatarIcon" />
                    {unreadCountType.COLLECT > 0 &&
                      (unreadCountType.COLLECT < 100 ? (
                        <div className="avatarDotBox">
                          {unreadCountType.COLLECT}
                        </div>
                      ) : (
                        <div className="avatarDotBox dots">...</div>
                      ))}
                  </div>
                  <div className="employeeItem-name">收藏</div>
                </div>
                <div
                  className={`employeeItem ${employeeSelected == "TOP" &&
                    "selected"}`}
                  onClick={() => handleSelectEmployee("TOP")}
                >
                  <div className="avatarDot">
                    <VerticalAlignTopOutlined className="avatarIcon" />
                    {unreadCountType.TOP > 0 &&
                      (unreadCountType.TOP < 100 ? (
                        <div className="avatarDotBox">
                          {unreadCountType.TOP}
                        </div>
                      ) : (
                        <div className="avatarDotBox dots">...</div>
                      ))}
                  </div>
                  <div className="employeeItem-name">置顶</div>
                </div>
                <div
                  className={`employeeItem ${employeeSelected == "WAIT" &&
                    "selected"}`}
                  onClick={() => handleSelectEmployee("WAIT")}
                >
                  <div className="avatarDot">
                    <ClockCircleOutlined className="avatarIcon" />
                    {unreadCountType.WAIT > 0 &&
                      (unreadCountType.WAIT < 100 ? (
                        <div className="avatarDotBox">
                          {unreadCountType.WAIT}
                        </div>
                      ) : (
                        <div className="avatarDotBox dots">...</div>
                      ))}
                  </div>
                  <div className="employeeItem-name">待跟进</div>
                </div>
                {employeeList.map((item, index) => (
                  <div
                    key={index}
                    className={`employeeItem ${employeeSelected == item.id &&
                      "selected"}`}
                    onClick={() => handleSelectEmployee(item.id)}
                  >
                    <div className="avatarDot">
                      <Avatar src={item.avatar} />
                      {item.unreadCount > 0 &&
                        (item.unreadCount < 100 ? (
                          <div className="avatarDotBox">{item.unreadCount}</div>
                        ) : (
                          <div className="avatarDotBox dots">...</div>
                        ))}
                    </div>
                    <div className="employeeItem-name">{item.name}</div>
                  </div>
                ))}
              </InfiniteScroll>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            )}
          </div>
          <div className="wi-chat-box wi-chat-sidebar">
            <Spin spinning={customerLoading}>
              <Form layout={"inline"} form={formForm}>
                <FormItem name="message" style={{ width: "100%", flex: '1' }}>
                  <Input
                    placeholder="搜索会话内容"
                    allowClear
                    onChange={(e) => {
                      handleSearch(e);
                    }}
                  />
                </FormItem>

                <Dropdown
                  arrow
                  menu={{
                    items: [
                      {
                        key: 'UNREAD',
                        label: '未读会话在前',
                      },
                      {
                        key: 'LATEST',
                        label: '最新会话在前',
                      }
                    ],
                    selectable: true,
                    selectedKeys: [orderEnumSort],
                    onClick: ({ key }) => {
                      handleSort(key)
                    }
                  }}
                >
                  <OrderedListOutlined
                    className="sidebar-icon"
                  />
                </Dropdown>
              </Form>

              <div
                className="sidebarList custom-scrollbar"
                id="scrollableDiv1"
              >
                {sessionList?.length > 0 ? (
                  <InfiniteScroll
                    dataLength={sessionList.length}
                    next={(e) => {
                      loadMoreData(e, "customer");
                    }}
                    hasMore={sessionList.length < paginations.total}
                    scrollableTarget="scrollableDiv1"
                  >
                    <List
                      split={false}
                      itemLayout="horizontal"
                      dataSource={sessionList}
                      renderItem={(item, index) => (
                        <List.Item
                          key={index}
                          onClick={() => handleSelectSessionClient(item)}
                          className={[
                            sessionSelected == item.sessionId && "selected",
                            item.isTop && "top",
                          ]}
                          id={`listItem${index}`}
                        >
                          <List.Item.Meta
                            avatar={
                              // readFlag:为true为已读
                              <div
                                className={!item.readFlag ? "avatarBox" : ""}
                              >
                                <Avatar size={40} src={item.customerAvatar} />
                              </div>
                            }
                            title={
                              <div className="title">
                                <div className="name">{item.customerName}</div>
                                <div className="last-updated">
                                  {moment(item.messageTime).format(
                                    "YYYY/MM/DD"
                                  ) == moment().format("YYYY/MM/DD")
                                    ? moment(item.messageTime).format(
                                      "HH:mm:ss"
                                    )
                                    : moment(item.messageTime).format(
                                      "YYYY/MM/DD"
                                    )}
                                </div>
                              </div>
                            }
                            description={
                              <>
                                {getDescriptionType(item)}
                                <span className="customerIcons animated">
                                  {item.isWait && <ClockCircleOutlined />}
                                  {item.isCollect && <StarOutlined />}
                                  <Dropdown
                                    getPopupContainer={() =>
                                      document.getElementById(
                                        `listItem${index}`
                                      )
                                    }
                                    arrow
                                    menu={{
                                      style: { width: "100px" },
                                      items: [
                                        {
                                          key: "TOP",
                                          label: <>
                                            <VerticalAlignTopOutlined
                                              style={{ marginRight: "5px" }}
                                            />
                                            {item.isTop ? "取消置顶" : "置顶"}
                                          </>
                                        },
                                        {
                                          key: "COLLECT",
                                          label: <>
                                            <StarOutlined
                                              style={{ marginRight: "5px" }}
                                            />
                                            {item.isCollect ? "取消收藏" : "收藏"}
                                          </>
                                        },
                                        {
                                          key: "WAIT",
                                          label: <>
                                            <ClockCircleOutlined
                                              style={{ marginRight: "5px" }}
                                            />
                                            {item.isWait ? "已跟进" : "待跟进"}
                                          </>
                                        }
                                      ],
                                      onClick: ({ key, domEvent }) => {
                                        domEvent.stopPropagation(); // 阻止事件冒泡
                                        handleOperate(key, item);
                                      }
                                    }}
                                  >
                                    <DownOutlined
                                      className="downIcon"
                                      onClick={(e) => {
                                        e.stopPropagation(); // 阻止事件冒泡
                                      }}
                                    />
                                  </Dropdown>
                                </span>
                              </>
                            }
                          />
                        </List.Item>
                      )}
                    />
                  </InfiniteScroll>
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </div>
            </Spin>
          </div>
          {sessionSelected ? (
            <div className="sessionSpinning">
              <Spin spinning={loading}>
                <div className="wi-chat-session">
                  <Card
                    className="wi-chat-box wi-chat-content"
                    title={
                      <div>
                        <Avatar src={sessionClientInfo.customerAvatar} />
                        <span className="chat-content-name">
                          {sessionClientInfo.customerName}
                        </span>
                      </div>
                    }
                    extra={
                      <div className="wi-chat-extra">
                        <div className="wi-chat-extraLeft">
                          <Paragraph className="extraLeft-name">
                            {sessionClientInfo.manageEmployeeName}
                          </Paragraph>
                          <Paragraph
                            className="extraLeft-name"
                            style={{ textAlign: "right" }}
                          >
                            {sessionClientInfo.manageEmployeeDepartmentName}
                          </Paragraph>
                        </div>
                        <Avatar src={sessionClientInfo.manageEmployeeAvatar} />
                      </div>
                    }
                  >
                    <div
                      className="wi-body custom-scrollbar"
                      id="wi-body"
                      ref={wiBodyRef}
                      onScroll={(e) => {
                        if (!loading) {
                          const { scrollTop } = e.target;
                          let {
                            topHeight,
                            bottomHeight,
                            prevCurrent,
                            nextCurrent,
                            pages,
                          } = sessionClientInfo;
                          bottomHeight =
                            wiBodyRef.current.scrollHeight -
                            wiBodyRef.current.clientHeight;
                          if (prevCurrent < pages && scrollTop == topHeight) {
                            fetchSessionMessage({
                              sessionId: sessionSelected,
                              pagination: {
                                size: 10,
                                current: prevCurrent + 1,
                              },
                              type: "prev",
                            });
                          } else if (
                            nextCurrent > 1 &&
                            Math.abs(scrollTop - bottomHeight) <= 50
                          ) {
                            fetchSessionMessage({
                              sessionId: sessionSelected,
                              pagination: {
                                size: 10,
                                current: nextCurrent - 1,
                              },
                              type: "next",
                            });
                          }
                        }
                      }}
                    >
                      <div className="wi-messages-list">
                        {messageList.length > 0 ? (
                          <>
                            {messageList.map((item, index) => (
                              <TextMessage
                                key={index}
                                data={item}
                                searchValue={searchValue}
                                handleVoice={handleVoice}
                                onTogether={handleTogether}
                              />
                            ))}
                          </>
                        ) : (
                          <Empty
                            description="暂无数据"
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                          />
                        )}
                      </div>
                    </div>
                    <div className="wi-footer">
                      <div className="wiFooter-top">
                        <div>
                          <WibotEmoji
                            onEmojiSelect={handleEmojiSelect}
                          />

                          <Tooltip
                            title="资源库"
                          >
                            <FolderAddOutlined
                              onClick={handleResource}
                            />
                          </Tooltip>

                          <Tooltip
                            title="打开控制窗口"
                          >
                            <MobileOutlined
                              onClick={handleMobile}
                            />
                          </Tooltip>

                          <Tooltip
                            title="收藏消息"
                          >
                            <MessageOutlined
                              onClick={handleMessage}
                            />
                          </Tooltip>
                        </div>
                        {sessionEmployee.length > 0 && (
                          <div
                            className="wiFooter-top-right"
                            onClick={handleConversation}
                          >
                            【{sessionEmployee[0].employeeName}】等
                            {sessionEmployee.length}位员工也在当前会话中
                          </div>
                        )}
                      </div>
                      <Form layout={"inline"} ref={chatFormRef}>
                        <FormItem name="content" style={{ width: "100%" }}>
                          <TextArea
                            id="msgTextInput"
                            ref={textAreaRef}
                            placeholder="输入点什么"
                            bordered={false}
                            autoSize={{ minRows: 4, maxRows: 10 }}
                            onPressEnter={onPressEnterTextArea}
                            onChange={onChangeTextArea}
                          />
                        </FormItem>
                      </Form>
                      <div className="wi-operate-box">
                        <div
                          className="wi-operate-follow"
                          onClick={handleEndSession}
                        >
                          <PoweroffOutlined /> 结束会话
                        </div>
                        <Button
                          disabled={sendBtnStatus}
                          type="primary"
                          onClick={() => {
                            sessionMessageSend();
                          }}
                        >
                          发送
                        </Button>
                      </div>
                    </div>
                  </Card>
                  <div className="wi-chat-box wi-chat-userInfo">
                    <Tabs activeKey={tabsIndex} onChange={onChangeTabs}>
                      <TabPane tab="问答库" key="1">
                        <AskBaseTab ref={askBaseRef} />
                      </TabPane>
                      <TabPane tab="会话小结" key="2">
                        <EndSessionTab
                          ref={endSessionRef}
                          params={{ customerId: sessionClientInfo.customerId }}
                        />
                      </TabPane>
                      <TabPane tab="客户详情" key="3">
                        <UseInfoTab
                          ref={useInfoRef}
                          params={{ customerId: sessionClientInfo.customerId }}
                        />
                      </TabPane>
                      <TabPane tab="知识随行" key="4">
                        <KnowledgeTab
                          ref={knowledgeRef}
                          togetherInfo={togetherInfo}
                          onAddContent={(content) => {
                            let chatContent = chatFormRef.current?.getFieldValue("content") || "";
                            if (chatContent.length > 0) {
                              chatContent += '\n'; // 换行添加
                            }
                            chatFormRef.current?.setFieldsValue({
                              content: chatContent + content,
                            });
                            message.success('复制成功！')
                            setSendBtnStatus(false);
                          }}
                        />
                      </TabPane>
                    </Tabs>
                  </div>
                </div>
              </Spin>
            </div>
          ) : (
            <div className="noInfo">
              <Empty
                description="暂无数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          )}
        </div>
      </Spin>
      <MaterialModal params={materialModalParams} />
      <ConversationModal params={conversationParams} />
      <OperateModal params={operateParams} />
      <MessageModal
        params={messageParams}
        handleAddMessageSession={handleAddMessageSession}
      />
      <EndSessionModal params={endSessionParams} />
      <SdkCloud
        params={{
          deviceData: deviceData,
          onClose: handleCloseCloud,
          onChange: (data) => { },
        }}
      />
    </div>
  );
};

export default ConvenientReply;
