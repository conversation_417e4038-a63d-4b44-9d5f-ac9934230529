.ConvenientReply {
  min-width: 1328px;
  height: 100%;

  p {
    margin: 0;
  }

  .globalRow {
    display: flex;
    flex-direction: row;
    background: #fff;

    .employeeList {
      width: 100px;
      min-width: 100px;
      height: 826px;
      text-align: center;
      background: #fff;
      padding: 8px 6px;
      border-right: 1px solid #f0f0f0;

      .employeeItem {
        cursor: pointer;
        border-radius: 6px;
        padding: 12px 0px;

        .employeeItem-name {
          width: 100%;
        }

        .avatarDot {
          position: relative;
          width: 32px;
          margin: auto;

          .avatarIcon {
            font-size: 20px;
          }

          .avatarDotBox {
            position: absolute;
            right: -13px;
            top: -9px;
            width: 18px;
            height: 18px;
            font-size: 12px;
            color: #fff;
            background-color: red;
            border-radius: 50%;
            /*设置圆角*/
            z-index: 999;

            &.dots {
              line-height: 11px;
            }
          }
        }

        &:hover {
          background-color: #d9d8d8;
        }

        &.selected {
          background-color: #c5c4c5;
        }
      }
    }

    .sessionSpinning {
      flex: 1;
    }

    .wi-chat-box {
      background: #fff;
      padding: 8px 14px;
      width: 300px;
      height: 100%;
    }

    .wi-chat-sidebar {
      width: 300px;
      min-width: 300px;
      padding: 8px 0 8px 14px;

      .ant-form {
        align-items: center;
      }

      .sidebar-icon {
        font-size: 24px;
        color: #000;
        cursor: pointer;
        margin-right: 14px;
      }

      .sidebarList {
        margin-top: 16px;
        height: 762px;
        padding-right: 8px;

        .infinite-scroll-component {
          overflow-x: hidden !important;
        }

        .ant-list {

          .ant-list-items {
            min-height: 200px;

            .ant-list-item {
              cursor: pointer;
              border-radius: 6px;
              padding: 12px 8px;

              &.top {
                background-color: #f5f5f5;
              }

              &:hover {
                background-color: #d9d8d8;

                .ant-list-item-meta-content .ant-list-item-meta-description .customerIcons {
                  display: inline-block;
                  animation-name: fadeInRight;

                  .downIcon {
                    display: inline-block;
                  }
                }
              }

              &.selected {
                background-color: #c5c4c5;
              }

              .ant-list-item-meta-avatar {
                .avatarBox {
                  position: relative;

                  &::after {
                    content: " ";
                    border: 4px solid red;
                    /*设置红色*/
                    border-radius: 4px;
                    /*设置圆角*/
                    position: absolute;
                    z-index: 999;
                    right: 0;
                  }
                }
              }

              .ant-list-item-meta-content {
                .ant-list-item-meta-title {
                  .title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .name {
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      flex: 1;
                    }

                    .last-updated {
                      color: #9aa8bd;
                      font-size: 12px;
                    }
                  }
                }

                .ant-list-item-meta-description {
                  width: 140px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  color: #928f8f;

                  .customerIcons {
                    position: absolute;
                    right: 0px;
                    color: #555555;

                    .anticon {
                      margin-right: 5px;
                    }

                    .downIcon {
                      display: none;
                    }

                    .iconHight {
                      color: #5d8dd4;
                    }
                  }
                }
              }

              .ant-dropdown {
                pointer-events: unset !important;
              }
            }
          }
        }
      }
    }

    .wi-chat-session {
      display: flex;
      height: 826px;
    }

    .wi-chat-content {
      width: 550px;
      padding: 0;
      display: flex;
      flex-direction: column;

      .ant-card-head {
        padding: 0 14px;
      }

      .ant-card-head-title {
        padding: 10px 0;
        font-weight: bold;

        .ant-avatar {
          width: 36px;
          height: 36px;
        }

        .chat-content-name {
          margin-left: 5px;
          display: inline-block;
          width: 100px;
          overflow: hidden;
          vertical-align: middle;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .ant-card-extra {
        padding: 5px 0;
      }

      .wi-chat-extra {
        display: flex;
        flex-direction: row;
        align-items: center;

        .wi-chat-extraLeft {
          margin-right: 10px;
          font-weight: bold;

          .extraLeft-name {
            margin-bottom: 0px;
          }
        }

        .ant-avatar {
          width: 36px;
          height: 36px;
        }
      }

      .ant-card-body {
        padding: 0;
        display: flex;
        flex-direction: column;
        flex: 1;
        background: #f5f5f5;
      }

      .wi-body {
        display: flex;
        flex: 1 1 0%;

        .wi-messages-list {
          display: block;
          width: 100%;
          padding: 14px;
          padding-left: 17px;
          height: 425px;
          background-color: #f5f5f5;
        }
      }

      .wi-footer {
        margin: 14px;
        padding: 14px;
        padding-top: 5px;
        border: 1px solid #cccccc;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .wiFooter-top {
          position: relative;
          text-align: left;
          display: flex;
          justify-content: space-between;

          .wiFooter-top-right {
            cursor: pointer;
            text-decoration: underline;
          }

          .anticon {
            font-size: 20px;
            margin-right: 10px;
            color: #6f7072;
            cursor: pointer;

            &:hover {
              color: #333;
            }
          }
        }

        .ant-form {
          flex: 0.9;

          .ant-input {
            padding: 0;

            &:focus {
              box-shadow: unset !important;
            }
          }
        }

        .wi-operate-box {
          display: flex;
          flex-direction: row-reverse;
          position: relative;

          .wi-operate-follow {
            position: absolute;
            left: 0px;
            bottom: 0px;
            cursor: pointer;
            text-decoration: underline;
          }

          .ant-btn {
            border-radius: 6px;
          }
        }
      }
    }

    .wi-chat-userInfo {
      width: 100%;
      flex: 1;

      .ant-tabs {
        height: 100%;

        .ant-tabs-content {
          height: 100%;

          .ant-tabs-tabpane {
            height: 100%;
          }
        }
      }
    }
  }

  .noInfo {
    position: relative;
    flex: 1;
    background-color: #fff; //#f5f5f5;
    border-left: 1px solid #f0f0f0;

    .ant-empty-normal {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}