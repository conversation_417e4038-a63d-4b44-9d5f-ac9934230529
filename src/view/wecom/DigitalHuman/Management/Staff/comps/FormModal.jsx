
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2024/10/22 09:16
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Management/Staff/comps/FormModal.jsx
 * @Description: '数字员工管理对话框-表单(编辑)'
 */

import { Form, Input, message, Modal, Select, Spin } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { restOPCall } from 'common/utils';
import React, { useEffect, useRef, useState } from 'react';

const FormItem = Form.Item;

const FormModal = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const dutyOpts = [
    { label: '新人进群', value: 'GROUP_NEW_MEMBER' },
    { label: '定时推送', value: 'GROUP_TIMED_PUSH' },
    { label: '违规监测', value: 'GROUP_VIOLATION' },
    { label: '气氛烘托', value: 'GROUP_COLLABORATOR' },
    { label: '课程讲解', value: 'GROUP_LIVE' },
    { label: '关键词回复', value: 'GROUP_KEYS' }
  ];

  useEffect(() => {
    const { visible, id } = props.params;
    if (visible) {
      setVisible(true);
      setLoading(true);
      if (id) {
        setId(id);
        getInfo(id);
      }
    }
  }, [props]);

  const getInfo = (id) => {
    setLoading(true);
    const data = {
      id: id
    };
    restOPCall('/digital_human', 'get', data).then((res) => {
      formRef.current.setFieldsValue({ ...res });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        id: id,
        ...formData
      };
      restOPCall('/digital_human', 'update', data).then((res) => {
        message.success('修改成功！');
        setVisible(false);
        setLoading(false);
        setConfirmLoading(false);
        setId(null);
        props.params?.onSubmit?.();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={500}
      title='编辑数字员工信息'
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>

          <FormItem label="名称" name="name"
            rules={[{ required: true, message: '请输入数字员工的名称' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入" allowClear />
          </FormItem>

          <FormItem label="职责" name="duty">
            <Select
              mode="multiple"
              allowClear
              options={dutyOpts}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
