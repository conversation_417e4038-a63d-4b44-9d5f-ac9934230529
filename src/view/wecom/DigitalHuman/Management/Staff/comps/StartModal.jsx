/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/13 10:25
 * @LastEditTime: 2022/04/13 10:59
 * @LastEditors: <PERSON>hong<PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\Management\comps\StartModal.jsx
 * @Description: '启动对话框-登录员工微信'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Spin, Image } from 'antd';
import { apiCall } from 'common/utils';
import { QrCodeBase } from 'common/qrcode';

import './StartModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const StartModal = (props) => {
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [qrCode, setQrCode] = useState('');
  const color_aaa = { color: '#aaaaaa' };

  useEffect(() => {
    const { visible, id } = props.params;
    if (visible) {
      setVisible(true);
      setLoading(true);
      if (id) {
        setId(id);
        getInfo(id);
      }
    }
  }, [props]);

  const getInfo = (id) => {
    setLoading(true);
    const data = {
      id: 386 || id
    };
    apiCall('/materiallibrary/get', 'GET', data).then((res) => {
      const image = QrCodeBase({ url: '机器人正在启动。。。' });
      setQrCode(image);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setQrCode('');
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="StartModal"
      visible={visible}
      width={500}
      title='登录员工微信'
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      footer={null}
      onCancel={onCancel}
    >
      <Spin spinning={loading}>
        <p>请使用助手的微信扫码并且点击登录！</p>
        <FileHOC src={qrCode}>
          {(url) => (
            <Image preview={false} src={url} placeholder />
          )}
        </FileHOC>
        <p style={color_aaa}>注意事项：</p>
        <p style={color_aaa}>1、请使用手机微信扫码登录，长按识别二维码无效。</p>
        <p style={color_aaa}>2、请勿登录电脑版/网页版微信或在手机上退出登录</p>
      </Spin>
    </Modal>
  );
};

export default StartModal;
