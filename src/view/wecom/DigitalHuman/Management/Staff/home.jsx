/*
 * @Author: LinQunXun
 * @Date: 2022/04/02 09:00
 * @LastEditTime: 2024/11/14 17:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Management/Staff/home.jsx
 * @Description: '数字员工管理'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Button, Card, Form, Input, Table, Tooltip, message } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall, restOPCall } from 'common/utils';
import FilterBar from 'components/FilterBar/FilterBar';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal';
import StartModal from './comps/StartModal';
import SdkCloud from '../../comps/SdkCloud/home';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;

const StaffManagement = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [startParams, setStartParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [deviceData, setDeviceData] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      // sorter: (a, b) => a.id - b.id,
    },
    {
      title: '名称',
      width: '250px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        let identity;
        if (record.identity == 'COMPANY') {
          identity = <span style={{ color: '#f59a23' }}> @ 企微</span>;
        } else {
          identity = <span style={{ color: '#07c160' }}> @ 微信</span>;
        }
        return (
          <>
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
            {identity}
          </>
        );
      },
    },
    {
      title: '职责',
      width: '160px',
      dataIndex: 'dutyValue',
      key: 'dutyValue',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '登录信息',
      width: '160px',
      dataIndex: 'accountName',
      key: 'accountName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '登录状态',
      width: '160px',
      dataIndex: 'accountState',
      key: 'accountState',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value == 1 ? '在线' : '离线'}>
          {value == 1 ? (
            <span style={{ color: '#07c160' }}>在线</span>
          ) : (
            <span style={{ color: '#d9001b' }}>离线</span>
          )}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      // fixed: "right",
      render: (value, record, index) => {
        let opts = [
          // { onClick: () => handleStart(record), name: "启动" },
          // { onClick: () => handleStop(record), name: "停止" },
          // { onClick: () => handleChangeNmber(record), name: "换号" },
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleCloud(record), name: "登录管理" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      restOPCall('/digital_human', 'getlist', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleStart = (record) => {
    const { id } = record;
    setStartParams({
      visible: true,
      id: id,
      onCancel: () => {
        setStartParams({ visible: false });
      },
    });
  };

  const handleStop = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '停止助手确认',
      content: `您将停止助手【${name}】，停止后对应的微信也会退出登录，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id: id,
        };
        apiCall('/materiallibrary/del', 'DELETE', data)
          .then((res) => {
            message.success('停止成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleChangeNmber = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '助手换号确认',
      content: `您将为助手【${name}】进行换号，换号后之前的微信信息将被清空，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id: id,
        };
        apiCall('/materiallibrary/del', 'DELETE', data)
          .then((res) => {
            message.success('换号成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id: id,
      onCancel: () => {
        setFormParams({ visible: false });
      },
      onSubmit: () => {
        setFormParams({ visible: false });
        fetchList();
      },
    });
  };

  // 云手机控制
  const handleCloud = (record) => {
    const data = JSON.parse(JSON.stringify(deviceData));
    const filterData = data.filter((item) => item != null);
    record.x =
      filterData.length > 0 ? filterData[filterData.length - 1].x + 50 : 0;
    data.push(record);
    setDeviceData(data);
    // const data = JSON.parse(JSON.stringify(deviceData));
    // data.push(record);
    // setDeviceData(data);
  };

  // 关闭云手机
  const handleCloseIframe = (item, index) => {
    const data = JSON.parse(JSON.stringify(deviceData));
    data[index] = null;
    setDeviceData(data);
  };

  return (
    <div className="StaffManagement">
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="名称" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formParams} />
      <StartModal params={startParams} />
      <OperateModal params={operateParams} />
      <SdkCloud
        params={{
          deviceData: deviceData,
          onClose: handleCloseIframe,
          type: 'staff',
          handleChangeData: (data) => {
            setDeviceData([...data]);
          },
        }}
      />
      {/* {sdkCloudParams.visible && <SdkCloudModal params={sdkCloudParams} />} */}
    </div>
  );
};

export default StaffManagement;
