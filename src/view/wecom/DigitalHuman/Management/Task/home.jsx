/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/21 11:46
 * @LastEditors: <PERSON>eiw
 * @LastEditTime: 2025/05/14 13:51
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/Management/Task/home.jsx
 * @Description: '任务管理'
 */

import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import { Button, Card, Form, Input, Table, Tooltip, Typography } from "antd"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import FilterBar from "components/FilterBar/FilterBar"
import SysDictSelect from "components/select/SysDictSelect"
import SysDictLabel from "components/select/SysDictLabel"
import WibotStatistic from "components/WibotStatistic/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import "./home.less"

const FormItem = Form.Item
const { Paragraph } = Typography

const TaskManagement = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "任务名称",
      width: "160px",
      dataIndex: "taskName",
      key: "taskName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "任务预览",
      width: "160px",
      dataIndex: "content",
      key: "content",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: "任务所属人",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "数字员工",
      width: "160px",
      dataIndex: "robotName",
      key: "robotName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "任务状态",
      width: "160px",
      dataIndex: "taskStatus",
      key: "taskStatus",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="ROBOT_TASK_STATUS" dictkey={value} />
      ),
    },
    {
      title: "任务结果",
      width: "160px",
      dataIndex: "taskResult",
      key: "taskResult",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) =>
        value ? (
          <Tooltip title={value} placement="topLeft">
            <Paragraph ellipsis={{ rows: 3 }} style={{ whiteSpace: "normal" }}>
              {value}
            </Paragraph>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      title: "执行时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "完成时间",
      width: "160px",
      dataIndex: "completeTime",
      key: "completeTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.completeTime) - timeStamp(b.completeTime),
    },
  ]
  const [statisticData, setStatisticData] = useState([
    {
      title: "任务平均耗时(秒)",
      value: 0,
      describe: "只统计已执行完成的任务",
    },
    {
      title: "已完成任务数",
      value: 0,
      // describe: '所有客户群的所有群成员数量',
    },
    {
      title: "运行中任务数",
      value: 0,
      // describe: '所有客户群中，有成员发言的群数量',
    },
  ])
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    fetchList()
  }, [])

  const fetchList = (params = {}) => {
    getStatisticData()
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      formData.tagIdList = formData.tagIdList?.join(",") || null
      formData.leaderDeptIdList = formData.leaderDeptIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/digital_human/task", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const getStatisticData = () => {
    apiCall("/digital_human/task/stat", "GET")
      .then((res) => {
        const { completeCount, avgTime, executingCount } = res
        let newStatisticData = statisticData
        newStatisticData[0].value = avgTime
        newStatisticData[1].value = completeCount
        newStatisticData[2].value = executingCount
        setStatisticData(newStatisticData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  const handlePreview = (record) => {
    const { employeeTaskId } = record
    setLoading(true)
    const data = {
      id: employeeTaskId,
    }
    apiCall("/employee_task/getById", "GET", data)
      .then((res) => {
        const { messages } = res
        setResourcePreviewParams({
          visible: true,
          title: "任务预览",
          listData: messages,
          type: "session",
          onCancel: () => {
            setResourcePreviewParams({
              visible: false,
            })
          },
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="digitalHumanTask">
      <div style={{ marginBottom: "20px" }}>
        <WibotStatistic list={statisticData} span={6} />
      </div>

      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="employeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="任务所属人" allowClear />
          </FormItem>
          <FormItem
            name="robotName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="数字员工" allowClear />
          </FormItem>
          <FormItem name="taskStatus">
            <SysDictSelect dataset="ROBOT_TASK_STATUS" placeholder="任务状态" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>

      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="taskId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default withRouter(TaskManagement)
