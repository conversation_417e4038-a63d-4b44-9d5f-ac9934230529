/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/21 11:26
 * @LastEditTime: 2023/07/27 14:16
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\DigitalHuman\Management\home.jsx
 * @Description: '数字员工'
 */
import React, { useState, useEffect } from "react";
import { Card, Tabs } from "antd";
// 模块组件
import Staff from "./Staff/home";
import Task from "./Task/home";

const { TabPane } = Tabs;

const DigitalHuman = (props) => {
  const [tabsIndex, setTabsIndex] = useState("");

  useEffect(() => {
    setTabsIndex(localStorage.getItem("digitalHumanTabsIndex") || "1");
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem("digitalHumanTabsIndex", index);
    setTabsIndex(index);
  };

  return (
    <div className="DigitalHuman">
      <Card bordered={false}>
        <Tabs
          activeKey={tabsIndex}
          destroyInactiveTabPane
          onChange={onChangeTabs}
        >
          <TabPane tab="员工管理" key="1">
            <Staff />
          </TabPane>
          <TabPane tab="任务管理" key="2">
            <Task />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DigitalHuman;
