/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/15 16:25
 * @LastEditTime: 2024/10/22 09:18
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/MessageAuto/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select, Spin, Card, Button, Space, message, TreeSelect, Switch, TimePicker, Checkbox, InputNumber } from 'antd';
import { PlusOutlined, QuestionCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import moment from 'moment';
import MaterialListForm from 'components/MaterialListForm/home';
import SysDictRadio from 'components/select/SysDictRadio';
import './form.less';

const { TextArea } = Input;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const layoutList = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  }
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};

const MessageAutoForm = (props) => {
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [manageEmployeeOption, setManageEmployeeOption] = useState([]);
  const [msgList, setMsgList] = useState([]);
  const [strategyType, setStrategyType] = useState('AUTOMATIC_REPLY');

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    getManageEmployeeOption();
    if (id) {
      setId(id)
      getInfoData(id);
    } else {
      onRefMaterialListForm?.current?.getInitMsgList([
        {
          type: 'copyWriter',
          content: '',
        },
      ]);
    }
  }, []);

  const getManageEmployeeOption = () => {
    setLoading(true);
    apiCall("/device/deviceGroup/listDeviceGroupTree", "GET", { paged: false }).then((res) => {
      setManageEmployeeOption(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/inbox/messageAuto/${id}`, 'GET').then((res) => {
      const { name, manageEmployeeIdList, strategy } = res;
      setStrategyType(strategy.type)
      formForm.setFieldsValue({
        name,
        manageEmployeeIdList,
        strategy: {
          ...strategy,
          dateList: strategy.dateList?.split(",") || [],
          time: (strategy.minTime && strategy.maxTime) && [moment(strategy.minTime, 'HH:mm'), moment(strategy.maxTime, 'HH:mm')] || null
        },
      });
      onRefMaterialListForm.current.getInitMsgList(strategy.list);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      const { strategy } = formData;
      if (strategy.type == 'AUTOMATIC_REPLY' && (!strategy.weekdayList || !strategy.weekdayList.length) && (!strategy.dateList || !strategy.dateList.length) && !strategy.time) {
        message.error("自动回复的规则中，特定星期、特定日期、特定时段三者不能同时为空")
        return false
      }
      setLoading(true);
      if (strategy.time) {
        formData.strategy = {
          ...strategy,
          minTime: moment(strategy.time[0]._d).format('HH:mm'),
          maxTime: moment(strategy.time[1]._d).format('HH:mm'),
        }
      }
      formData.strategy.dateList = strategy.dateList?.join(",") || "";
      const data = {
        ...formData,
        type: strategy.type,
        strategy: {
          ...formData.strategy,
          list: onRefMaterialListForm.current.getModifyMsgList(),
        },
        enable: id ? null : true
      }
      const apiUrl = id ? `/inbox/messageAuto/update/${id}` : `/inbox/messageAuto`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        // clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='MessageAuto-Form-Container'>
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + '自动化'}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="name"
              label="名称"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入名称" allowClear maxLength={50} />
            </Form.Item>

            <Form.Item
              name="manageEmployeeIdList"
              label="生效范围"
              rules={[{ required: true, message: '请选择生效范围' }]}
            >
              <TreeSelect
                showSearch
                showArrow
                placeholder="请选择生效范围"
                allowClear
                multiple
                treeDefaultExpandAll
                showCheckedStrategy={TreeSelect.SHOW_ALL}
                fieldNames={{ label: 'title', value: 'key', children: 'children' }}
                treeNodeFilterProp='title'
                treeData={manageEmployeeOption}
                maxTagCount="responsive"
              />
            </Form.Item>

            <Form.Item label="类型" required>
              <Form.Item
                name={['strategy', 'type']}
                rules={[{ required: true, message: '请选择类型' }]}
                initialValue={'AUTOMATIC_REPLY'}
                style={{ marginBottom: '0' }}
              >
                <SysDictRadio dataset="REPLY_TYPE" onChange={(e) => {
                  const { value } = e.target;
                  setStrategyType(value)
                }} />
              </Form.Item>

              <div className='strategyList'>
                {
                  strategyType == 'AUTOMATIC_REPLY' ?
                    <>
                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'weekdayList']}
                        label="特定星期"
                      >
                        <Checkbox.Group>
                          <Checkbox value={1}>
                            星期一
                          </Checkbox>
                          <Checkbox value={2}>
                            星期二
                          </Checkbox>
                          <Checkbox value={3}>
                            星期三
                          </Checkbox>
                          <Checkbox value={4}>
                            星期四
                          </Checkbox>
                          <Checkbox value={5}>
                            星期五
                          </Checkbox>
                          <Checkbox value={6}>
                            星期六
                          </Checkbox>
                          <Checkbox value={7}>
                            星期天
                          </Checkbox>
                        </Checkbox.Group>
                      </Form.Item>

                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'dateList']}
                        label={"特定日期"}
                        extra="日期请使用固定格式“年-月-日”，例如：2023-07-06。"
                      >
                        <Select
                          placeholder={"请输入特定日期"}
                          allowClear
                          showSearch
                          showArrow
                          mode="tags"
                          maxTagCount="responsive"
                          optionFilterProp="name"
                        />
                      </Form.Item>

                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'time']}
                        label={"特定时段"}
                        extra="支持跨天设置，例如20:00～08:00。"
                      >
                        <TimePicker.RangePicker format="HH:mm" order={false} />
                      </Form.Item>

                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'noReplyIfRead']}
                        label="已读不回"
                        valuePropName="checked"
                        initialValue={true}
                        tooltip={{ title: '开启后，在自动回复发送之前，判断触发自动回复的消息是否已读，如已读则取消发送。', icon: <QuestionCircleOutlined /> }}
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'waitTime']}
                        label={"等待时间"}
                        tooltip={{ title: '触发自动回复前的等待时间。', icon: <QuestionCircleOutlined /> }}
                        initialValue={0}
                        rules={[{ required: true, message: '请输入等待时间' }]}
                      >
                        <InputNumber
                          min={0}
                          max={120}
                          addonAfter="分钟"
                        // formatter={value => `${value}分钟`}
                        // parser={value => (value || '0')?.replace('分钟', '')}
                        />
                      </Form.Item>

                      <Form.Item
                        {...layoutList}
                        name={['strategy', 'coolDownTime']}
                        label={"冷却时间"}
                        tooltip={{ title: '两次自动回复之间的最小间隔。', icon: <QuestionCircleOutlined /> }}
                        initialValue={0}
                        rules={[{ required: true, message: '请输入冷却时间' }]}
                      >
                        <InputNumber
                          min={0}
                          max={120}
                          addonAfter="分钟"
                        // formatter={value => `${value}分钟`}
                        // parser={value => (value || '0')?.replace('分钟', '')}
                        />
                      </Form.Item>
                    </>
                    :
                    <>
                      <Form.List name={['strategy', 'keys']} initialValue={[""]}>
                        {(fields, { add, remove }, { errors }) => (
                          <>
                            {fields.map((field, index) => (
                              <Form.Item
                                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                                label={index === 0 ? '关键词' : ''}
                                required
                                key={field.key}
                                tooltip="仅当完全匹配时才会触发回复！"
                              >
                                <Form.Item
                                  {...field}
                                  rules={[{ required: true, message: "请输入关键词" }]}
                                  noStyle
                                >
                                  <TextArea allowClear style={{ width: '80%' }} />
                                </Form.Item>
                                {fields.length > 1 && (
                                  <MinusCircleOutlined
                                    className="dynamic-delete-button"
                                    onClick={() => remove(field.name)}
                                  />
                                )}
                              </Form.Item>
                            ))}
                            <Form.Item
                              {...formItemLayoutWithOutLabel}
                            >
                              <Button
                                type="primary"
                                onClick={() => add()}
                                icon={<PlusOutlined />}
                              >
                                添加关键词
                              </Button>
                            </Form.Item>
                          </>
                        )}
                      </Form.List>
                    </>
                }
                <Form.Item
                  {...layoutList}
                  name="msgList"
                  label="回复内容"
                  rules={[{
                    required: true,
                    validator: (_, value) => {
                      if (msgList.length) {
                        return Promise.resolve()
                      } else {
                        return Promise.reject(new Error('回复内容不能为空'))
                      }
                    }
                  }]}
                >
                  <MaterialListForm
                    params={{
                      formRef: formForm,
                      menuList: ["copyWriter", 'image', 'material'],
                      materialTabList: [
                        'Article',
                        'pageArticle',
                        'Video',
                        'copyWriter',
                        'Picture',
                        'Poster',
                        'FORM',
                        'Product',
                      ],
                      needScriptFlag: false,
                      isNickname: false,
                      materialAmount: msgList.length,
                      maxLength: 3,
                    }}
                    // 监听回调
                    callback={(params) => {
                      setMsgList(params.data);
                    }}
                    ref={onRefMaterialListForm}
                  />
                </Form.Item>
              </div>
            </Form.Item>
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button onClick={() => handleGoBack()}>取消</Button>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default MessageAutoForm;
