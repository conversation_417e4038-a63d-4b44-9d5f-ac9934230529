/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/29 15:45
 * @LastEditTime: 2024/11/14 17:31
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/QualityControlRule/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Spin, Card, Button, Space, message, Divider, Switch, } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import './form.less';

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const layoutList = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  }
};

const QualityControlRuleForm = (props) => {
  const [formForm] = Form.useForm();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id)
      getInfoData(id);
    }
  }, []);

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/agentSeat/qualityControlScheme/${id}`, 'GET').then((res) => {
      const { name, checkAutoClose, checkAutoDistribution, keywordRuleList } = res;
      formForm.setFieldsValue({
        name,
        checkAutoClose,
        checkAutoDistribution,
        keywordRuleList: keywordRuleList?.map((item) => ({
          ...item,
          keywordList: item.keywordList?.map((atem) => ({
            label: atem,
            value: atem,
          })) || []
        })) || [],
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      formData.keywordRuleList = formData.keywordRuleList?.map((item) => ({
        ...item,
        keywordList: item.keywordList?.map((atem) => (atem.label || atem.value)) || []
      })) || []
      const data = {
        ...formData,
      }
      const apiUrl = id ? `/agentSeat/qualityControlScheme/update/${id}` : `/agentSeat/qualityControlScheme`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='QualityControlRule-Form-Container'>
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + '质检方案'}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="name"
              label="方案名称"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入方案名称' }]}
            >
              <Input placeholder="请输入方案名称" allowClear showCount maxLength={20} />
            </Form.Item>

            <Form.Item
              label="质检规则"
              required
              tooltip="可添加多个规则，各规则间为‘或’关系"
            >

              <Form.Item
                name="checkAutoClose"
                label="自动结束对话检查"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="checkAutoDistribution"
                label="超时重新分配检查"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch />
              </Form.Item>

              <div className="strategyList">
                <Form.List name="keywordRuleList" initialValue={[]}>
                  {(fields, { add, remove }, { errors }) => (
                    <>
                      <Form.Item
                        className='title'
                        label="关键词检查"
                        colon={false}
                        tooltip="各关键词间为 “或” 关系"
                      />

                      {fields.map((field, index) => (
                        <>
                          <div key={field.key} style={{ display: "flex", alignItems: "baseline", marginBottom: "24px" }}>
                            <div style={{ flex: "1" }}>
                              <Form.Item
                                {...layoutList}
                                {...field}
                                name={[field.name, 'name']}
                                label='规则名称'
                                rules={[{ required: true, message: "请输入规则名称" }]}
                              >
                                <Input placeholder="请输入规则名称" allowClear showCount maxLength={20} />
                              </Form.Item>
                              <Form.Item
                                style={{ margin: "unset" }}
                                {...layoutList}
                                {...field}
                                name={[field.name, 'keywordList']}
                                label='关键词'
                                rules={[{ required: true, message: "请输入关键词" }]}
                                extra={"规则内含多个关键词，匹配其一即命中规则；"}
                              >
                                <Select
                                  placeholder="请输入关键词"
                                  fieldNames={{ label: 'name', value: 'id' }}
                                  allowClear
                                  showSearch
                                  showArrow
                                  mode="tags"
                                  labelInValue
                                  maxTagCount="responsive"
                                  optionFilterProp="name"
                                  tokenSeparators={['，']}
                                />
                              </Form.Item>
                            </div>
                            <MinusCircleOutlined
                              className="dynamic-delete-button"
                              onClick={() => remove(field.name)}
                            />
                          </div>
                          {(fields.length - 1 > index) ? <Divider /> : null}
                        </>
                      ))}

                      <Button
                        type="primary"
                        onClick={() => add()}
                        icon={<PlusOutlined />}
                      >
                        添加关键词检查
                      </Button>
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button onClick={() => handleGoBack()}>取消</Button>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default QualityControlRuleForm;
