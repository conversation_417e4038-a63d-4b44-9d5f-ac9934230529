/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/12/29 17:11
 * @LastEditTime: 2024/06/04 16:46
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/SessionSummary/home.jsx
 * @Description: '会话小结'
 */

import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  TreeSelect,
} from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { recursionTagKeyTreeData } from "common/tree";
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import ListOperation from 'components/ListOperation/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import TypeFormModal from "./comps/TypeFormModal";
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;

const SessionSummary = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户',
      width: '160px',
      dataIndex: 'customerName',
      key: 'customerName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '托管账号名称',
      width: '160px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '客户昵称',
      width: '160px',
      dataIndex: 'customerName',
      key: 'customerName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '提交人',
      width: '160px',
      dataIndex: 'createEmployeeName',
      key: 'createEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '提交时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '业务类型',
      width: '160px',
      dataIndex: 'sessionBusinessTypeId',
      key: 'sessionBusinessTypeId',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const content =
          (record.sessionBusinessTypeId && (
            <>
              {record.sessionBusinessTypeParentName}-{record.sessionBusinessTypeName}
            </>
          )) ||
          null;
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
      },
    },
    {
      title: '会话状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <SysDictLabel dataset="SESSION_SUMMARY_STATE" dictkey={value} />
      ),
    },
    {
      title: '小结内容',
      width: '160px',
      dataIndex: 'content',
      key: 'content',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
  ];
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [classifyMenu, setClassifyMenu] = useState([]);

  useEffect(() => {
    getSessionBusinessTypeTree();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      formData.sessionBusinessTypeIdList = formData.sessionBusinessTypeIdList?.join(',') || null;
      formData.createEmployeeIdList = formData.createEmployeeIdList?.join(',') || null;
      formData.depEmpList = formData.depEmpList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/inbox/sessionSummary', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getSessionBusinessTypeTree = () => {
    const data = {};
    apiCall("/inbox/sessionBusinessType/tree", "GET", data)
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res);
        setClassifyMenu([
          {
            title: "全选",
            value: "",
            key: "",
            children: treeData,
          },
        ]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleClassManage = () => {
    setTypeFormParams({
      visible: true,
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getSessionBusinessTypeTree();
      },
    });
  };

  const handleExport = () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      formData.sessionBusinessTypeIdList = formData.sessionBusinessTypeIdList?.join(',') || null;
      formData.createEmployeeIdList = formData.createEmployeeIdList?.join(',') || null;
      formData.depEmpList = formData.depEmpList?.join(',') || null;
      const data = {
        ...formData,
      };
      apiCall("/inbox/sessionSummary/export", "GET", data, null, { isExit: true, title: `会话小结.${moment().format("YYYY-MM-DD")}.xlsx`, }).then((res) => {
        message.success("导出成功！");
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  return (
    <div className="SessionSummary">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="content"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="小结内容" allowClear />
          </FormItem>
          <FormItem name="state">
            <SysDictSelect dataset="SESSION_SUMMARY_STATE" placeholder="会话状态" />
          </FormItem>
          <FormItem name="sessionBusinessTypeIdList" style={{ width: '300px' }}>
            <TreeSelect
              placeholder="业务类型"
              treeData={classifyMenu}
              treeCheckable
              treeDefaultExpandedKeys={[""]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          <FormItem
            name="createEmployeeIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal
              title="提交人"
              onlyEmployee
              multiple
            />
          </FormItem>
          <FormItem
            name="depEmpList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal
              title="托管账号"
              multiple
            />
          </FormItem>
          <FormItem name="createTime" label="提交时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleClassManage()}>
              业务类型管理
            </Button>
            <Button type="primary" onClick={() => handleExport()}>
              导出查询结果
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <TypeFormModal params={typeFormParams} />
    </div>
  );
};

export default withRouter(SessionSummary);
