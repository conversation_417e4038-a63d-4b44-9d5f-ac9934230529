/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 14:40
 * @LastEditTime: 2024/11/06 15:38
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketManage/comps/DetailModal.jsx
 * @Description: '工单详情弹窗'
 */

import React, {useEffect, useState} from 'react';
import {Descriptions, Divider, Empty, Modal, Spin} from 'antd';
import {apiCall} from 'common/utils';
import TypeMessageDrawer from "../../AgentSeat/comps/TypeMessage/drawer";
import SysDictLabel from 'components/select/SysDictLabel';
import AppStore from 'stores/AppStore';
import {versionFnMap} from "config";

const DetailModal = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, id = null } = props.params;
  const [loading, setLoading] = useState(false);
  const [info, setInfo] = useState(null);
  const [drawerParams, setDrawerParams] = useState({ visible: false });
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  // const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";
  const agentSeat_title = versionFnMap.system_ui().agentSeat_title

  useEffect(() => {
    if (visible) {
      getInfo()
    } else {
      setLoading(false);
      setInfo(null)
      setDrawerParams({ visible: false });
    }
  }, [visible]);

  const getInfo = () => {
    setLoading(true);
    apiCall(`/agentSeat/ticket/link/${id}`, "GET")
      .then((res) => {
        setInfo(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleViewDialog = (params) => {
    const { conversationId, customerName, employeeName } = params;
    setDrawerParams({
      visible: true,
      title: `对话详情（${customerName}-${employeeName}）`,
      conversationId: conversationId,
      onCancel: () => {
        setDrawerParams({ visible: false });
      },
    })
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="TicketManage-DetailModal"
      title={<div style={{ display: "flex" }}>{workOrderTitle}详情（{info && <SysDictLabel dataset="TICKET_STATE" dictkey={info.ticket.state} /> || ''}）</div>}
      visible={visible}
      width={400}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      footer={null}
      onCancel={onCancel}
    >
      <Spin spinning={loading}>
        {
          info ? <>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label={`${workOrderTitle}编号`}>{info.ticket.no}</Descriptions.Item>
              <Descriptions.Item label="客户昵称">{info.ticket.customerName}</Descriptions.Item>
              <Descriptions.Item label="账号名称">{info.ticket.employeeName}</Descriptions.Item>
              <Descriptions.Item label={`提出${agentSeat_title}`}>
                {info.ticket.createEmployeeName}
                {info.ticket.conversationId && <a style={{ marginLeft: '10px' }} onClick={() => { handleViewDialog(info.ticket) }}>查看对话</a> || ''}
              </Descriptions.Item>
              <Descriptions.Item label="提出时间">{info.ticket.createTime}</Descriptions.Item>
              <Descriptions.Item label="紧急程度">{<SysDictLabel dataset="TICKET_URGENCY_TYPE" dictkey={info.ticket.urgencyType} />}</Descriptions.Item>
              <Descriptions.Item label={`${workOrderTitle}类型`}>{<SysDictLabel dataset="TICKET_TYPE" dictkey={info.ticket.type} />}</Descriptions.Item>
              <Descriptions.Item label={`${workOrderTitle}内容`}>{info.ticket.content}</Descriptions.Item>
            </Descriptions>

            {info.pendingTicketFollow &&
              <>
                <Divider />
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="挂起人">{info.pendingTicketFollow.createEmployeeName}</Descriptions.Item>
                  <Descriptions.Item label="挂起时间">{info.pendingTicketFollow.createTime}</Descriptions.Item>
                  <Descriptions.Item label="挂起原因">{info.pendingTicketFollow.content}</Descriptions.Item>
                </Descriptions>
              </>
            }

            {info.processTicketFollow &&
              <>
                <Divider />
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="处理人">{info.processTicketFollow.createEmployeeName}</Descriptions.Item>
                  <Descriptions.Item label="处理时间">{info.processTicketFollow.createTime}</Descriptions.Item>
                  <Descriptions.Item label="处理意见">{info.processTicketFollow.content}</Descriptions.Item>
                  <Descriptions.Item label={`跟进${agentSeat_title}`}>
                    {info.processTicketFollow.agentEmployeeName}
                    {info.processTicketFollow.conversationId && <a style={{ marginLeft: '10px' }} onClick={() => { handleViewDialog(info.processTicketFollow) }}>查看对话</a> || ''}
                  </Descriptions.Item>
                </Descriptions>
              </>
            }

            {info.endTicketFollow &&
              <>
                <Divider />
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="办结人">{info.endTicketFollow.createEmployeeName}</Descriptions.Item>
                  <Descriptions.Item label="办结时间">{info.endTicketFollow.createTime}</Descriptions.Item>
                  <Descriptions.Item label="办结备注">{info.endTicketFollow.content}</Descriptions.Item>
                </Descriptions>
              </>
            }

            <TypeMessageDrawer params={drawerParams} />
          </> : (<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />)
        }
      </Spin>
    </Modal>
  );
};

export default DetailModal;
