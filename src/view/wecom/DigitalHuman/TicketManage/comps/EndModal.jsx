/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 11:12
 * @LastEditTime: 2024/11/06 15:39
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketManage/comps/EndModal.jsx
 * @Description: '办结工单弹窗'
 */

import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal, Spin} from 'antd';
import {apiCall} from 'common/utils';
import TypeMessageDrawer from "../../AgentSeat/comps/TypeMessage/drawer";
import AppStore from 'stores/AppStore';
import {versionFnMap} from "config";

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const EndModal = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, info = null } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [drawerParams, setDrawerParams] = useState({ visible: false });
  const [loading, setLoading] = useState(false);
  const [newInfo, setNewInfo] = useState(null);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  // const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";
  const agentSeat_title = versionFnMap.system_ui().agentSeat_title

  useEffect(() => {
    if (visible) {
      if (info) {
        getConversation()
        formForm.setFieldsValue({
          customerName: info.customerName,
          employeeName: info.employeeName,
          content1: info.content,
          processContent: info.processContent,
          createEmployeeName: info.createEmployeeName,
        });
      }
    } else {
      setConfirmLoading(false);
      setNewInfo(null);
      formForm?.resetFields();
    }
  }, [visible]);

  const getConversation = () => {
    const { id } = info;
    setLoading(true);
    const data = {
      ticketId: id
    }
    apiCall(`/agentSeat/ticket/conversation`, "GET", data)
      .then((res) => {
        setNewInfo(res)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleViewDialog = () => {
    const { customerName, employeeName } = info;
    const { id } = newInfo;
    setDrawerParams({
      visible: true,
      title: `对话详情（${customerName}-${employeeName}）`,
      conversationId: id,
      onCancel: () => {
        setDrawerParams({ visible: false });
      },
    })
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ticketId: info.id,
        type: 'END',
        ...formData,
      };
      apiCall('/agentSeat/ticket/process', 'POST', data)
        .then((res) => {
          message.success('办结成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="TicketManage-EndModal"
      title={`${workOrderTitle}办结`}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form form={formForm} {...layout}>
          <Form.Item label="客户昵称" name="customerName">
            <Input bordered={false} readOnly />
          </Form.Item>

          <Form.Item label="账号名称" name="employeeName">
            <Input bordered={false} readOnly />
          </Form.Item>

          <Form.Item label={`${workOrderTitle}内容`} name="content1">
            <TextArea bordered={false} readOnly autoSize />
          </Form.Item>

          <Form.Item label="处理意见" name="processContent">
            <TextArea bordered={false} readOnly autoSize />
          </Form.Item>

          <Form.Item label={`跟进${agentSeat_title}`} >
            <div style={{ marginLeft: '10px' }}>
              {info?.createEmployeeName}
              <a style={{ marginLeft: '10px' }} onClick={() => { handleViewDialog() }}>查看对话</a>
              {(newInfo && newInfo.state != 'END') ? <span style={{ color: "#d5001c" }}> （对话处理中）</span> : ''}
            </div>
          </Form.Item>

          <Form.Item
            name="content"
            label="办结备注"
          >
            <TextArea
              placeholder="请输入办结备注（200字）"
              allowClear
              showCount
              maxLength={200}
              autoSize={{ minRows: 4, maxRows: 7 }}
            />
          </Form.Item>
        </Form>
      </Spin>

      <TypeMessageDrawer params={drawerParams} />
    </Modal>
  );
};

export default EndModal;
