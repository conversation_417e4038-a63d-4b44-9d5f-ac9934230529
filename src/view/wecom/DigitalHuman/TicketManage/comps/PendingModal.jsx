/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 11:12
 * @LastEditTime: 2024/06/26 14:38
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketManage/comps/PendingModal.jsx
 * @Description: '挂起工单弹窗'
 */

import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal} from 'antd';
import {apiCall} from 'common/utils';
import {versionFnMap} from "config";

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const PendingModal = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, info = null } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (info) {
        formForm.setFieldsValue({
          customerName: info.customerName,
          employeeName: info.employeeName,
          content1: info.content,
        });
      }
    } else {
      setConfirmLoading(false);
      formForm?.resetFields();
    }
  }, [visible]);

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ticketId: info.id,
        type: 'PENDING',
        openConversationFlag: false,
        ...formData,
      };
      apiCall('/agentSeat/ticket/process', 'POST', data)
        .then((res) => {
          message.success('挂起成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="TicketManage-PendingModal"
      title={`${workOrderTitle}挂起`}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label={`${workOrderTitle}内容`} name="content1">
          <TextArea bordered={false} readOnly autoSize />
        </Form.Item>

        <Form.Item
          name="content"
          label="挂起原因"
          rules={[{ required: true, message: "请输入挂起原因（200字）" }]}
        >
          <TextArea
            placeholder="请输入挂起原因（200字）"
            allowClear
            showCount
            maxLength={200}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PendingModal;
