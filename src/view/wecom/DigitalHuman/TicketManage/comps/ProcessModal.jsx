/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 11:12
 * @LastEditTime: 2024/11/06 15:39
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketManage/comps/ProcessModal.jsx
 * @Description: '处理工单弹窗'
 */

import React, {useEffect, useState} from 'react';
import {Form, Input, message, Modal, Switch, TreeSelect} from 'antd';
import {QuestionCircleOutlined} from '@ant-design/icons';
import {apiCall} from 'common/utils';
import AppStore from 'stores/AppStore';
import {versionFnMap} from "config";

const { TextArea } = Input;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const ProcessModal = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const { visible = false, info = null } = props.params;
  const [formForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [switch1, setSwitch1] = useState(true);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (visible) {
      getAgentGroupOptions()
      if (info) {
        formForm.setFieldsValue({
          customerName: info.customerName,
          employeeName: info.employeeName,
          content1: info.content,
        });
      }
    } else {
      setConfirmLoading(false);
      setSwitch1(true);
      formForm?.resetFields();
    }
  }, [visible]);

  // 查询所有座席
  const getAgentGroupOptions = () => {
    const data = {
      signIn: true
    }
    apiCall("/agentSeat/agentGroup/all/options", "GET", data)
      .then((res) => {
        setClassifyMenu(res.map((item => ({
          ...item,
          disabled: true
        }))));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ticketId: info.id,
        type: 'PROCESS',
        ...formData,
      };
      apiCall('/agentSeat/ticket/process', 'POST', data)
        .then((res) => {
          message.success('处理成功！');
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="TicketManage-ProcessModal"
      title={`${workOrderTitle}处理`}
      visible={visible}
      confirmLoading={confirmLoading}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form form={formForm} {...layout}>
        <Form.Item label="客户昵称" name="customerName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label="账号名称" name="employeeName">
          <Input bordered={false} readOnly />
        </Form.Item>

        <Form.Item label={`${workOrderTitle}内容`} name="content1">
          <TextArea bordered={false} readOnly autoSize />
        </Form.Item>

        <Form.Item
          name="content"
          label="处理意见"
          rules={[{ required: true, message: "请输入处理意见（200字）" }]}
        >
          <TextArea
            placeholder="请输入处理意见（200字）"
            allowClear
            showCount
            maxLength={200}
            autoSize={{ minRows: 4, maxRows: 7 }}
          />
        </Form.Item>

        <Form.Item
          name="openConversationFlag"
          label="发起对话"
          valuePropName="checked"
          initialValue={true}
          tooltip={{ title: `当${workOrderTitle}需要进一步跟进时，发起与客户的新对话进行处理`, icon: <QuestionCircleOutlined /> }}
        >
          <Switch
            onChange={(checked) => {
              setSwitch1(checked)
            }}
          />
        </Form.Item>

        {
          switch1 && <>
            <Form.Item
              name="agentId"
              label={`跟进${agentSeat_title}`}
              rules={[{ required: true, message: `请选择跟进${agentSeat_title}` }]}
            >
              <TreeSelect
                allowClear
                showArrow
                showSearch
                treeDefaultExpandAll
                treeData={classifyMenu}
                placeholder={`请选择跟进${agentSeat_title}`}
                fieldNames={{ label: 'title', value: 'key', children: 'children' }}
                treeNodeFilterProp="title"
              />
            </Form.Item>
          </> || null
        }
      </Form>
    </Modal>
  );
};

export default ProcessModal;
