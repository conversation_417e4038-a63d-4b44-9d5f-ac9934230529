/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/25 09:48
 * @LastEditTime: 2024/11/06 15:36
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketManage/home.jsx
 * @Description: '工单/事件管理'
 */

import React, {useEffect, useRef, useState} from "react";
import {Button, Card, DatePicker, Form, Input, Table, Tooltip, TreeSelect,} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import {apiCall} from "common/utils";
import {timeStamp} from "common/date";
import {removeInputEmpty} from "common/regular";
import OperateModal from "components/Modal/OperateModal/index";
import ListOperation from 'components/ListOperation/home';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import ProcessModal from './comps/ProcessModal';
import EndModal from './comps/EndModal';
import DetailModal from './comps/DetailModal';
import PendingModal from './comps/PendingModal';
import WibotStatistic from 'components/WibotStatistic/home';
import moment from 'moment';
import AppStore from 'stores/AppStore';
import {versionFnMap} from "config";

const { RangePicker } = DatePicker;


const TicketManage = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [processParams, setProcessParams] = useState({ visible: false });
  const [endParams, setEndParams] = useState({ visible: false });
  const [detailParams, setDetailParams] = useState({ visible: false });
  const [pendingParams, setPendingParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title:  workOrderTitle + "编号",
      width: "160px",
      dataIndex: "no",
      key: "no",
      align: "center",
    },
    {
      title: "客户昵称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
    },
    {
      title: "托管账号",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
    },
    {
      title: "提出坐席/提出时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TICKET_STATE" dictkey={value} />
      ),
    },
    {
      title: "紧急程度",
      width: "160px",
      dataIndex: "urgencyType",
      key: "urgencyType",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TICKET_URGENCY_TYPE" dictkey={value} />
      ),
    },
    {
      title:workOrderTitle + "类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="TICKET_TYPE" dictkey={value} />
      ),
    },
    {
      title: workOrderTitle +'内容',
      width: '160px',
      dataIndex: 'content',
      key: 'content',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "跟进坐席",
      width: "160px",
      dataIndex: "processEmployeeName",
      key: "processEmployeeName",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: "详情" },
        ];
        if (record.state == 'WAITING' || record.state == 'PENDING') {
          opts.push({ onClick: () => handleProcess(record), name: "处理" })
        }
        if (record.state == 'WAITING') {
          opts.push({ onClick: () => handlePending(record), name: "挂起" })
        }
        if (record.state == 'PROCESSING') {
          opts.push({ onClick: () => handleEnd(record), name: "办结" })
        }
        return <ListOperation opts={opts} />;
      },
    },
  ]);
  const [statisticData, setStatisticData] = useState([
    {
      title: `待处理${workOrderTitle}数`,
      value: 0,
      suffix: "个",
      describe: `统计${workOrderTitle}状态为“待处理”的${workOrderTitle}数量，与列表查询无关。`,
    },
    {
      title: `挂起中${workOrderTitle}数`,
      value: 0,
      suffix: "个",
      describe: `统计${workOrderTitle}状态为“挂起中”的${workOrderTitle}数量，与列表查询无关。`,
    },
    {
      title: `处理中${workOrderTitle}数`,
      value: 0,
      suffix: "个",
      describe: `统计${workOrderTitle}状态为“处理中”的${workOrderTitle}数量，与列表查询无关。`,
    },
  ]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (moduleVersionMap[`agentSeat_title`] === 'v2') {
      columns[4].title = '提出客服/提出时间'
      columns[9].title = '跟进客服'
      setColumns([...columns])
    }
    getAgentGroupOptions();
    fetchList();
  }, []);

  const getStatisticData = () => {
    apiCall("/agentSeat/ticket/count/state", "GET")
      .then((res) => {
        const { WAITING, PENDING, PROCESSING } = res;
        let newStatisticData = statisticData;
        newStatisticData[0].value = WAITING;
        newStatisticData[1].value = PENDING;
        newStatisticData[2].value = PROCESSING;
        setStatisticData(newStatisticData);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 查询所有座席
  const getAgentGroupOptions = () => {
    apiCall("/agentSeat/agentGroup/all/options", "GET")
      .then((res) => {
        setClassifyMenu(res.map((item => ({
          ...item,
          disabled: true
        }))));
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    getStatisticData();
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/agentSeat/ticket", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 详情
  const handleDetail = (record) => {
    const { id } = record;
    setDetailParams({
      visible: true,
      id: id,
      onSubmit: (data) => {
        setDetailParams({ visible: false });
      },
      onCancel: () => {
        setDetailParams({ visible: false });
      },
    });
  };

  // 处理
  const handleProcess = (record) => {
    setProcessParams({
      visible: true,
      info: record,
      onSubmit: (data) => {
        setProcessParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setProcessParams({ visible: false });
      },
    });
  };

  // 挂起
  const handlePending = (record) => {
    setPendingParams({
      visible: true,
      info: record,
      onSubmit: (data) => {
        setPendingParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setPendingParams({ visible: false });
      },
    });
  };

  // 办结
  const handleEnd = (record) => {
    setEndParams({
      visible: true,
      info: record,
      onSubmit: (data) => {
        setEndParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setEndParams({ visible: false });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TicketManage-Container">
      <FilterBar>
        <WibotStatistic
          list={statisticData}
          span={6}
        />
      </FilterBar>

      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <Form.Item
            name="createAgentId"
          >
            <TreeSelect
              allowClear
              showArrow
              showSearch
              treeDefaultExpandAll
              treeData={classifyMenu}
              placeholder={`提出${agentSeat_title}`}
              fieldNames={{ label: 'title', value: 'key', children: 'children' }}
              treeNodeFilterProp="title"
            />
          </Form.Item>

          <Form.Item
            name="processAgentId"
          >
            <TreeSelect
              allowClear
              showArrow
              showSearch
              treeDefaultExpandAll
              treeData={classifyMenu}
              placeholder={`跟进${agentSeat_title}`}
              fieldNames={{ label: 'title', value: 'key', children: 'children' }}
              treeNodeFilterProp="title"
            />
          </Form.Item>

          <Form.Item
            name="state"
            initialValue={'WAITING'}
          >
            <SysDictSelect dataset="TICKET_STATE" placeholder="状态" />
          </Form.Item>

          <Form.Item name="content" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="内容" allowClear />
          </Form.Item>

          <Form.Item name="createTime" label="提出时间">
            <RangePicker />
          </Form.Item>

          <Form.Item
            name="urgencyType"
          >
            <SysDictSelect dataset="TICKET_URGENCY_TYPE" placeholder="紧急程度" />
          </Form.Item>

          <Form.Item
            name="type"
          >
            <SysDictSelect dataset="TICKET_TYPE" placeholder={workOrderTitle +"类型"} />
          </Form.Item>

          <Form.Item name="no" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder={workOrderTitle + "编号"} allowClear maxLength={11} />
          </Form.Item>
        </Form>

        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>

      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <ProcessModal params={processParams} />
      <EndModal params={endParams} />
      <DetailModal params={detailParams} />
      <PendingModal params={pendingParams} />
    </div>
  );
};

export default TicketManage;
