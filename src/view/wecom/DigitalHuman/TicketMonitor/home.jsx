/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/03/26 14:55
 * @LastEditTime: 2024/11/11 10:36
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/TicketMonitor/home.jsx
 * @Description: '坐席监控'
 */

import React, {useEffect, useRef, useState} from "react";
import {Card, Col, Form, message, Row, Select, Spin, Table, Tooltip,} from "antd";
import {QuestionCircleOutlined} from '@ant-design/icons';
import FilterBar from "components/FilterBar/FilterBar";
import {apiCall} from "common/utils";
import {secondsToTime, timeStamp} from 'common/date';
import OperateModal from "components/Modal/OperateModal/index";
import ListOperation from 'components/ListOperation/home';
import SysDictLabel from 'components/select/SysDictLabel';
import WibotStatistic from 'components/WibotStatistic/home';
import AppStore from 'stores/AppStore';
import {versionFnMap} from "config";

const TicketMonitor = (props) => {
  const workOrderTitle = versionFnMap.system_ui().workOrderTitle
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [statisticData, setStatisticData] = useState([
    {
      title: "总人数",
      value: 0,
      suffix: "人",
    },
    {
      title: "签入人数",
      value: 0,
      suffix: "人",
    },
    {
      title: "小休人数",
      value: 0,
      suffix: "人",
    },
    {
      title: "签出人数",
      value: 0,
      suffix: "人",
    },
    {
      title: <Tooltip title="非准实时数据，每两小时刷新一次">平均响应时间<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      value: '-',
    },
    {
      title: <Tooltip title="非准实时数据，每两小时刷新一次">平均耗时<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      value: '-',
    },
    {
      title: `办结${workOrderTitle}数`,
      value: 0,
      suffix: "个",
    },
    {
      title: <>新增{workOrderTitle}数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></>,
      value: 0,
      suffix: "个",
    },
    {
      title: "待处理对话",
      value: 0,
      suffix: "个",
    },
    {
      title: "协同中对话",
      value: 0,
      suffix: "个",
    },
  ]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [filteredInfo, setFilteredInfo] = useState({});
  const [columns, setColumns] = useState([
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "坐席",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      align: "center",
      render: (value, record, index) => (<>{value}-{record.name}</>),
    },
    {
      title: "状态",
      width: "100px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="AGENT_SEAT_STATE" dictkey={value} />
      ),
      filterMultiple: false,
      filters: AppStore.state.g_sysdict.AGENT_SEAT_STATE.map((item) => ({
        text: item[1],
        value: item[0],
      })),
    },
    {
      title: "处理中的对话",
      width: "120px",
      dataIndex: "processingCount",
      key: "processingCount",
      align: "center",
    },
    {
      title: "协同中的对话",
      width: "120px",
      dataIndex: "collaborationCount",
      key: "collaborationCount",
      align: "center",
    },
    {
      title: "已处理的对话",
      width: "120px",
      dataIndex: "endCount",
      key: "endCount",
      align: "center",
    },
    {
      title: <Tooltip title="单位：(时:分)">签入总时长<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      width: "120px",
      dataIndex: "signInTotalTime",
      key: "signInTotalTime",
      align: "center",
      render: (value, record, index) => (secondsToTime(value, 'HH:mm', true)),
    },
    {
      title: <Tooltip title="单位：(时:分)">小休总时长<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      width: "120px",
      dataIndex: "restTotalTime",
      key: "restTotalTime",
      align: "center",
      render: (value, record, index) => (secondsToTime(value, 'HH:mm', true)),
    },
    {
      title: '最近一次签入时间',
      width: '160px',
      dataIndex: 'lastSignInTime',
      key: 'lastSignInTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.lastSignInTime) - timeStamp(b.lastSignInTime)
    },
    {
      title: <Tooltip title="非准实时数据，每两小时刷新一次">平均响应时间<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      width: "140px",
      dataIndex: "avgResponseTime",
      key: "avgResponseTime",
      align: "center",
      render: (value, record, index) => (secondsToTime(value, 'HH:mm:ss', true)),
    },
    {
      title: <Tooltip title="非准实时数据，每两小时刷新一次">平均耗时<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>,
      width: "120px",
      dataIndex: "avgTotalTime",
      key: "avgTotalTime",
      align: "center",
      render: (value, record, index) => (secondsToTime(value, 'HH:mm:ss', true)),
    },
    {
      title: "自动结束对话",
      width: "120px",
      dataIndex: "autoCloseCount",
      key: "autoCloseCount",
      align: "center",
    },
    {
      title: "超时处理对话",
      width: "120px",
      dataIndex: "autoDistributionCount",
      key: "autoDistributionCount",
      align: "center",
    },
    {
      title: "转接对话",
      width: "120px",
      dataIndex: "transferToCount",
      key: "transferToCount",
      align: "center",
    },
    {
      title: "被转接对话",
      width: "120px",
      dataIndex: "transferFromCount",
      key: "transferFromCount",
      align: "center",
    },
    {
      title: "协同客户超时未回复",
      width: "120px",
      dataIndex: "collaborationCustomerTimeoutCount",
      key: "collaborationCustomerTimeoutCount",
      align: "center",
    },
    {
      title: "协同坐席超时未处理",
      width: "120px",
      dataIndex: "collaborationAgentTimeoutCount",
      key: "collaborationAgentTimeoutCount",
      align: "center",
    },
    {
      title: `创建${workOrderTitle}数`,
      width: "120px",
      dataIndex: "newTickCount",
      key: "newTickCount",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [];
        if (record.state == 'SIGN_IN' || record.state == 'REST') {
          opts.push({ onClick: () => handleExit(record), name: `强制${versionFnMap.system_ui().agentSeat_stateName[1]}` })
        }
        return <ListOperation opts={opts} />;
      },
    },
  ]);
  const [dataSourceSeat, setDataSourceSeat] = useState([]);
  const [columnsSeat, setColumnsSeat] = useState([
    {
      title: "序号",
      width: "40px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "坐席组名称",
      dataIndex: "name",
      key: "name",
      align: "center",
    },
    {
      title: "数量",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
  ]);
  const moduleVersionMap = AppStore.state.User.moduleVersionMap;
  const agentSeat_title = moduleVersionMap[`agentSeat_title`] === 'v2' ? '客服' : "坐席";

  useEffect(() => {
    if (versionFnMap.system_ui().version === 'v2') {
      columns[1].title = '客服'
      columns[16].title = '协同客服超时未处理'
      setColumns([...columns])
      columnsSeat[1].title = '客服组名称'
      setColumnsSeat([...columnsSeat])
    }
    if (versionFnMap.system_ui().version === 'v2')  {
      statisticData[1].title = '在线人数';
      statisticData[3].title = '离线人数';
      setStatisticData([...statisticData])
      columns[6].title = <Tooltip title="单位：(时:分)">在线总时长<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>
      columns[8].title = '最近一次在线时间'
      setColumns([...columns])
    }
    getAgentGroupOptions();
  }, []);

  // 查询所有座席
  const getAgentGroupOptions = () => {
    apiCall("/agentSeat/agentGroup/options", "GET")
      .then((res) => {
        setClassifyMenu([
          {
            name: `全部${agentSeat_title}组`,
            id: ""
          },
          ...res
        ]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  useEffect(async () => {
    if (classifyMenu.length) {
      await getAgentSeatMonitor();
      await getGroupDistribution();
      await fetchList();
    }
  }, [classifyMenu]);

  // 获取坐席监控统计
  const getAgentSeatMonitor = async () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const data = {
        ...formData,
      };
      apiCall("/agentSeat/agent/monitor/stat", "GET", data)
        .then((res) => {
          const {
            restCount,
            signInCount,
            signOutCount,
            totalCount,
            avgResponseTime,
            avgTotalTime,
            endTicketCount,
            consultTickCount,
            complaintTicketCount,
            systemErrorTicketCount,
            newTickCount,
            waitingConversationCount,
            collaboratingConversationCount,
          } = res;
          let newStatisticData = statisticData;
          newStatisticData[0].value = totalCount;
          newStatisticData[1].value = signInCount;
          newStatisticData[2].value = restCount;
          newStatisticData[3].value = signOutCount;
          newStatisticData[4].value = avgResponseTime ? secondsToTime(avgResponseTime, 'HH:mm:ss') : '-';
          newStatisticData[5].value = avgTotalTime ? secondsToTime(avgTotalTime, 'HH:mm:ss') : '-';
          newStatisticData[6].value = endTicketCount;
          newStatisticData[7].title = <Tooltip title={`咨询：${consultTickCount}
            投诉：${complaintTicketCount}
            系统故障：${systemErrorTicketCount}`}>新增{workOrderTitle}数<QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip>;
          newStatisticData[7].value = newTickCount;
          newStatisticData[8].value = waitingConversationCount;
          newStatisticData[9].value = collaboratingConversationCount;
          setStatisticData(newStatisticData);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          // setLoading(false);
        });
    })
  };

  // 各组对话分配情况
  const getGroupDistribution = async () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const data = {
        ...formData,
      };
      apiCall("/agentSeat/agent/getGroupDistribution", "GET", data)
        .then((res) => {
          setDataSourceSeat(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          // setLoading(false);
        });
    })
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/agentSeat/agent/monitor", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  useEffect(() => {
    setColumns(
      columns.map((item) => {
        item.filteredValue = filteredInfo[item.dataIndex] || null; // <- 添加默认值 null
        return item;
      })
    );
  }, [filteredInfo]); // <- 添加 filteredInfo 依赖

  const onChangeTable = (pagination, filters, sorter) => {
    setFilteredInfo(filters);
    fetchList({
      pagination,
      query: {
        state: filters.state?.join(",") || ''
      }
    });
  };

  // 强制签出
  const handleExit = (record) => {
    const { name, id } = record;
    let stateName = versionFnMap.system_ui().agentSeat_stateName[1]
    // if (moduleVersionMap[`agentSeat_stateName`] === 'v2') {
    //   stateName = '离线'
    // }
    setOperateParams({
      visible: true,
      title: `强制${stateName}确认`,
      content: `强制${stateName}将导致${agentSeat_title}无法接收新对话，确认强制${stateName}${agentSeat_title}【${name}】吗？`,
      onSubmit: () => {
        apiCall(`/agentSeat/agent/update/${id}?state=SIGN_OUT`, 'POST').then(async (res) => {
          message.success(`强制${stateName}成功！`);
          await getAgentSeatMonitor();
          await getGroupDistribution();
          await fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  }

  return (
    <div className="TicketMonitor-Container">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} xl={18}>
            <FilterBar>
              <Form layout={"inline"} ref={formRef}>
                <Form.Item name="groupId" initialValue={''}>
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={classifyMenu}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.name ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    onChange={async () => {
                      setFilteredInfo({})
                      await getAgentSeatMonitor();
                      await getGroupDistribution();
                      await fetchList();
                    }}
                  />
                </Form.Item>
              </Form>

              <WibotStatistic
                gutter={[16, 16]}
                list={statisticData}
                span={6}
              />
            </FilterBar>
          </Col>
          <Col xs={24} xl={6}>
            <Card style={{ height: "471px", marginBottom: "20px" }} title='各组对话分配情况' bordered={false} >
              <Table
                showHeader={false}
                rowKey="id"
                dataSource={dataSourceSeat}
                columns={columnsSeat}
                scroll={{ y: 311 }}
                pagination={false}
              />
            </Card>
          </Col>
        </Row>
        <Card bordered={false}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
        <OperateModal params={operateParams} />
      </Spin>
    </div>
  );
};

export default TicketMonitor;
