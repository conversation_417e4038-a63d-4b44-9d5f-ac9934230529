/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/05/08 15:59
 * @LastEditTime: 2024/11/21 17:26
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/DigitalHuman/comps/SdkCloud/home.jsx
 * @Description: '多账户sdk'
 */

import React, { useEffect, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import <PERSON><PERSON> from 'js-cookie';
import AppStore from 'stores/AppStore';
import Draggable from 'react-draggable';
import { apiCall } from 'common/utils';
import { getApiUrl } from "config/index";

import './home.less';

const SdkCloud = (props) => {
  const { deviceData, type = 'device' } = props.params;
  const [apiPath, setApiPath] = useState('');
  const AppSteUser = AppStore.state.User;

  useEffect(() => {
    const kitItem = AppSteUser.kitMapList.find((item) => location.pathname.includes(item.kitKey));
    if (kitItem) {
      const { kitUrl, kitKey } = kitItem;
      if (kitKey == 'syswb' || kitKey == 'comwb') {
        setApiPath(getApiUrl());
      } else {
        setApiPath(`${getApiUrl()}/${kitKey}`);
      }
    }
  }, []);

  return (
    <div className="SdkCloudiframe-box">
      <div>
        {deviceData.map(
          (item, index) =>
            item && (
              <Draggable
                handle=".iframeItem-top"
                defaultPosition={{ x: item.x, y: 0 }}
                key={index}
                onDrag={() => {
                  const iframeItems = document.getElementsByClassName('iframeItem');
                  for (let i = 0; i < iframeItems.length; i++) {
                    iframeItems[i].style.zIndex = 0;
                  }
                  document.getElementById(`iframeItem${index}`).style.zIndex = 999;
                }}
              >
                <div
                  className="iframeItem"
                  id={`iframeItem${index}`}
                >
                  <div
                    className="iframeItem-top"
                  >
                    <div className="nickName">
                      {item.nickName || item.name || item.code}
                    </div>
                  </div>
                  <CloseOutlined
                    className="iframeCloseIcon"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      apiCall(`/device/unlock?codes=${item.code}`, 'POST').then((res) => {
                        props.params.onClose(item, index);
                      })
                        .catch((err) => {
                          console.log(err);
                        });
                    }}
                  />
                  <iframe
                    src={`WebDemo/index.html?id=${item.id}&code=${item.code}&token=${Cookie.get('weebot_cloud_token')}&type=${type}&apiPath=${apiPath}`}
                    width="282px"
                    height="498px"
                    frameBorder={0}
                  ></iframe>
                </div>
              </Draggable>
            )
        )}
      </div>
    </div>
  );
};

export default SdkCloud;
