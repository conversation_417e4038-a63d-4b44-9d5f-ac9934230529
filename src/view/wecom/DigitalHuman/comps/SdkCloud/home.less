.SdkCloudiframe-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  // z-index: 9999;
  width: 100%;
  height: 100%;
  pointer-events: none;
  .iframeItem {
    // width: 100%;
    // height: 100%;
    display: flex;
    flex-direction: column;
    pointer-events: auto;
    position: absolute;
    border: 1px solid #e5e5e5;
    box-shadow: 1px 2px 3px 0px #ccc;
    // padding: 10px;
    // background: #c1c1c1;

    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .iframeItem-top {
      position: relative;
      background: #a8a8a8;
      padding: 10px;
      cursor: move;
      .nickName {
        width: 230px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      // .iframeCloseIcon {
      //   font-size: 18px;
      //   cursor: pointer;
      //   position: absolute;
      //   top: 12px;
      //   right: 18px;
      //   color: #000;
      //   // &:hover{
      //   //   color: #5d8dd4;
      //   // }
      // }
    }
    .iframeCloseIcon {
      font-size: 18px;
      cursor: pointer;
      position: absolute;
      top: 12px;
      right: 18px;
      color: #000;
      // z-index: 9999;
      // &:hover{
      //   color: #5d8dd4;
      // }
    }
  }
}
