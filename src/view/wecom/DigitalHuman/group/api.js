import {apiCall} from "common/utils";


export function queryAccounts() {
    return apiCall("/groupChatMessage/inbox/intensiveEmployee", "GET", {paged: false})
}

export function queryGroups(employeeId) {
    return apiCall("/groupChatMessage/inbox/group", "GET", {paged: false, employeeId})
}


export function queryMessageByGroupId({groupId, current, size}) {
    return apiCall('/groupChatMessage/inbox/group/message', 'GET', {groupId, current, size})
}


export function queryGroupInfoByGroupId(groupId) {
    return apiCall('/group/getGroupInfo', 'GET', {groupId})
}

export function queryGroupMemberInfo(groupId) {
    return apiCall('/group/getGroupMemberInfo', 'GET', {groupId, current: 1, size: 1000, inGroup: true})
}

export function queryGroupMemberActivities({groupId, current, size}) {
    return apiCall('/group/log/groupLog/pageByGroupId', 'GET', {groupId, current, size})
}


export function postSendMessageByGroup({groupId, deviceEmployeeId, content}) {
    return apiCall(`/groupChatMessage/inbox/group/message/send?groupId=${groupId}&deviceEmployeeId=${deviceEmployeeId}`, 'POST', [{
        "content": content,
        "type": "copyWriter",
    }])
}
