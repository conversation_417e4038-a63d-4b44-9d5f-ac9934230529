import { <PERSON><PERSON>, Badge, But<PERSON>, Card, Collapse, Empty, Input, Layout, List, message, Spin, Tabs } from 'antd'
import moment from 'moment'
import { useCallback, useEffect, useRef, useState } from "react"

import {
    postSendMessageByGroup,
    queryAccounts,
    queryGroups,
    queryMessageByGroupId
} from '@/view/wecom/DigitalHuman/group/api'
import useWebSocket from '@/view/wecom/DigitalHuman/group/groupWs'
import Message from '@/view/wecom/DigitalHuman/group/message'
import { ellipsis } from '@/view/wecom/DigitalHuman/group/utils'
import { UserOutlined } from '@ant-design/icons'
import WibotEmoji from 'components/WibotEmoji/home'
import './group.less'
import GroupInfo from './groupInfo'

const {Panel} = Collapse;
const {Content, Sider} = Layout;
const {TextArea} = Input

const messageType = {
    'TEXT': null,
    'IMAGE': '[图片]',
    'VOICE': '[语音]',
    'EMOTION': '[表情]',
    'VIDEO': '[视频]',
    'FILE': '[文件]',
    'LOCATION': '[位置]',
    'LINK': '[链接]',
    'WE_APP': '[小程序]',
    'CUSTOM_SYSTEM': '[系统消息]',
}
const getMessageOfType =(message) => {
    return messageType[message.type] === null ? ellipsis(message?.messageMap?.content): messageType[message.type]
}

function formatRelativeTime (dateTime) {
    if (!dateTime) { return }
    const [date, time] = dateTime.split(' ')
    const today = moment().format('YYYY-MM-DD')
    if (date === today) {
        return time
    } else {
        return date.replace(/-/g, '/')
    }
}
const Group = (props) => {

    const [accountsOptions, setAccountsOptions] = useState([
        {
            avatar: null, departmentId: null, departmentName: '', id: null, name: '全部帐号', unreadCount: 0
        }
    ])
    const [sessionsOptions, setSessionsOptions] = useState([])
    const [convationOptions, setConversationOptions] = useState([])
    const [messagesOptions, setMessagesOptions] = useState([])
    const [isSending, setIsSending] = useState(false)
    const Account = ({account}) => (
        <div onClick={() => handleClickAccount(account)} key={account.id}>
            {account.name}
        </div>
    );


    const Session = ({session}) => {
        return <div onClick={() => handleClickSession(session)} key={session.id}>
            {session.name}
        </div>
    }
    // const Message = ({message}) => {
    //     return <div key={message.id}>
    //         {message.name}
    //     </div>
    // }
    const {messages, sendMessage, error} = useWebSocket('')

    function updateReadMessage (messages = []) {
        if (!messages[0]) { return }
        const result = {}
        for (let id in messages[0]?.unReadCountMap) {
            const find = accountsOptions.find((account) => account.id === Number(id))
            if (find) {
                result[id] = messages[0].unReadCountMap[id]
            }
        }
        // account
        const count = Object.values(result).reduce((acc, cur) => {
            acc += cur
            return acc
        }, 0)
        setAccountsOptions(prevState => {
            return prevState.map(a => {
                a.unreadCount = messages[0].unReadCountMap[a.id]
                if (a.name === '全部帐号') {
                    a.unreadCount = count
                }
                return a
            })
        })
        // session
        setSessionsOptions(prevState => {
            return prevState.map(a => {
                if (a.id === messages[0].id) {
                    a.readFlag = false
                }
                return a
            })
        })
    }
    useEffect(() => {
        if (!messages[0]) { return }

        if (messages[0]?.inboxType === 'READ_EVENT') {
            updateReadMessage(messages)
            return
        }

        if (messages[0]?.inboxType === "HEART_BEAT") {
            return
        }
        updateReadMessage(messages)
        if (messages[0].id === currentSession?.id) {
            sendMessage({ 'groupId': messages[0].groupId, 'type': 'READ', enter: 'updateUseEffect' })
        }
        console.log(`[messages[0]]: `, messages[0])
        const newSessions = sessionsOptions.map(s => {
            if (s.id === messages[0].id) {
                return {
                    ...s,
                    ...messages[0],
                    groupName: s.groupName
                }
            }
            return s
        })
        setSessionsOptions((prevState) => {
          const beforeSession = prevState.find(
            (session) => session.id === messages[0].id,
          )
          if (!beforeSession) {
            return prevState
          }
          console.log(`[beforeSession]: `, beforeSession)
          const updatedSessions = prevState.filter(
            (session) => session.id !== messages[0].id,
          )
          updatedSessions.unshift({
            ...beforeSession,
            ...messages[0],
            groupName: beforeSession?.groupName,
          })
          return updatedSessions
        })
        if (currentSession?.id === messages[0]?.id) {
            setMessagesOptions(prevState => {
                return [...prevState, ...messages]
            })
        }
        setTimeout(() => {
          if (currentSession.id !== messages[0].id) {
            // 只处理当前会话的消息
            return
          }
          const scrollableDiv = document.querySelector('#scrollableDiv')
          console.log(`[scrollableDiv]: `, messages[0], scrollableDiv)
          if (scrollableDiv) {
              scrollableDiv.scrollTop = scrollableDiv.scrollHeight + 100
          }
        }, 100)

    }, [messages, currentSession])

    const [currentAccount, setCurrentAccount] = useState(null)
    const [currentSession, setCurrentSession] = useState(null)
    const [currentCollapses, setCurrentCollapses] = useState([])
    const collapseRef = useRef(null)
    const [isInit, setIsInit] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [messagePage, setMessagePage] = useState({
        current: 1,
        size: 10,
        hasMore: true
    })

    const handleClickAccount = (account) => {
        console.log(account)
        setCurrentAccount(account)
        setCurrentSession(null)
        setMessagesOptions([])
        queryGroups(account.id).then(res => {
            setSessionsOptions(res.records)
        })

    }
    const handleClickSession = async (group) => {
        console.log(group)
        setCurrentSession(group)
        setMessagesOptions([])
        setMessagePage(prevState => {
            return {
                ...prevState,
                current: 1,
                hasMore: true
            }
        })
        setIsInit(false)
        fetchTimer.current = 'stopWatchDom'
        sendMessage({ 'groupId': group.groupId, 'type': 'READ', enter: 'session' })
    }

    const fetchMessage = useCallback((current) => {
        console.log('fetchMessage')
      const container = document.querySelector('#scrollableDiv')
      const scrollHeightBefore = container?.scrollHeight;
      const scrollTopBefore = container?.scrollTop;
      setIsLoading(true)
      queryMessageByGroupId({
        groupId: currentSession.groupId,
        current: current || messagePage.current,
        size: messagePage.size
      }).then(res => {
        setMessagePage(prevState => {
          return {
            ...prevState,
            hasMore: res.total > (res.size * res.current)
          }
        })
        setMessagesOptions(prevState => {
          return [...res.records.reverse(), ...prevState]
        })
        setTimeout(() => {
          const heightAdded = container.scrollHeight - scrollHeightBefore;
          container.scrollTop = scrollTopBefore + heightAdded;
          setIsLoading(false)
        }, 160)

        if (isInit) {
          return
        }

        setTimeout(() => {
          const scrollableDiv = document.querySelector('#scrollableDiv')
          const { left, bottom } = scrollableDiv.getBoundingClientRect()
          scrollableDiv.scrollTop = 9999999
          setIsInit(true)
        }, 100)
      })
    }, [currentSession, messagePage])

    useEffect(() => {
      if (!currentSession?.groupId) {
        return
      }
      console.log('加载消息: ', messagePage.current)
      fetchMessage(messagePage.current)
    }, [currentSession?.id, messagePage.current])

    // useEffect(() => {
    //     if (!currentSession?.groupId) {
    //         return
    //     }
    //     sendMessage({ "groupId": currentSession.groupId, "type": "READ", enter: 'session' })
    //     // setMessagePage(prevState => {
    //     //     return {
    //     //         ...prevState,
    //     //         hasMore: false
    //     //     }
    //     // })
    //     if (!messagePage.hasMore) {
    //         console.log('没有更多数据')
    //         return
    //     }
    //
    //     fetchMessage()
    // }, [currentSession?.groupId])

    const [inputValue, setInputValue] = useState('')
    const inputRef = useRef(null)

    const handleEmojiSelect = e => {
        console.log(e)

        const textToInsert = e.native
        const textArea = inputRef.current.resizableTextArea.textArea; // 获取 TextArea 元素
        if (!textArea) return;
        const start = textArea.selectionStart;
        const end = textArea.selectionEnd;
        const newText = inputValue.substring(0, start) + textToInsert + inputValue.substring(end);
        setInputValue(newText)

        // 移动光标到插入文本之后
        textArea.selectionStart = start + textToInsert.length;
        textArea.selectionEnd = start + textToInsert.length;

        setTimeout(() => {
            textArea.focus(); // 重新获取焦点，确保光标可见
        }, 500)
    }

    const onInputChange = e => {
        const value = e.target.value;
        setInputValue(value)
    }

    const handleKeyDown = e => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleClickSendMessage()
        }
    }

    const handleClickSendMessage = e => {
        if (isSending) { return }
        if (!inputValue) { return }
        setIsSending(true)
        message.warning("信息来源自会话存档，展示会有延迟！");
        sendMessage({ "groupId": currentSession.groupId, "type": "READ", enter: 'submit' })
        console.log(`[currentSession]: `, currentSession)
        postSendMessageByGroup({
            groupId: currentSession.groupId,
            deviceEmployeeId: currentSession.manageEmployeeId,
            content: inputValue
        }).then(res => {
            setInputValue('')
        }).finally(() => {
            setIsSending(false)
        })
    }

    useEffect(() => {
        queryAccounts().then(res => {
            console.log(`[res]: `, res)
            setAccountsOptions(prevState => {
                const allCount = res.records.reduce((acc, cur) => {
                    acc += cur.unreadCount
                    return acc
                }, 0)
                return [...prevState, ...res.records].map((item,index) => {
                    if (item.name === '全部帐号') {
                        item.unreadCount = allCount
                    }
                  return {
                    ...item,
                  }
                })
            })
        })
    }, [])

    const hasMoreRef = useRef(null)
    const fetchTimer = useRef(null)
    const scrollableDivRef = useRef(null)
    useEffect(() => {
        if (!hasMoreRef.current) {
            return
        }
        if (!messagePage.hasMore) {
            return
        }
        const observer = new IntersectionObserver( // 改用 IntersectionObserver 更合理
          ([entry]) => {
              if (entry.isIntersecting) {
                console.log("isIntersecting")
                if (fetchTimer.current) {
                  clearTimeout(fetchTimer.current)
                  fetchTimer.current = null
                  return
                }
                  fetchTimer.current = setTimeout(() => {
                    console.log('触发加载更多')
                    setMessagePage((prevState) => {
                      const newState = {
                        ...prevState,
                        current: prevState.current + 1,
                      }

                      return newState
                    })
                    fetchTimer.current = null
                  }, 16)
              }
          },
          {
              root: document.querySelector('#scrollableDiv'), // 指定滚动容器
              threshold: 0.1 // 当元素 10% 可见时触发
          }
        )

        observer.observe(hasMoreRef.current)
        // return () => observer.disconnect() // 清理 observer
    }, [hasMoreRef.current, messagePage.hasMore])


    return (<>
        <Layout className="group">
            <Sider theme="light" className="account" width={100}>
                <List
                    split={false}
                    itemLayout="horizontal"
                    dataSource={accountsOptions}
                    renderItem={(item, index) => (
                        <List.Item
                            key={index}
                            className="account-item"
                        >
                            <div className={`wrapper ${(currentAccount?.id === item.id) ? "selected" : ''}`}
                                 onClick={() => handleClickAccount(item)}>
                                <List.Item.Meta
                                    avatar={
                                        <Badge count={item.unreadCount}>
                                            <Avatar size="large" shape="circle" src={item.avatar}
                                                    icon={<UserOutlined />} />
                                        </Badge>
                                    }
                                />
                                <div className="name">{item.name}</div>
                            </div>
                        </List.Item>
                    )}
                />
            </Sider>
            <Sider theme="light" className="session" width={300}>
                <List
                    split={false}
                    itemLayout="horizontal"
                    dataSource={sessionsOptions}
                    renderItem={(item, index) => (
                        <List.Item
                            className={`session-item ${(currentSession && currentSession.id === item.id) ? "selected" : ''}`}
                            key={index}
                            onClick={() => handleClickSession(item)}
                        >
                            <List.Item.Meta
                                title={<span>
                                    { item.groupName }
                                    {
                                        item.readFlag ? <Badge style={{ marginLeft: 4 }} status="error" /> : null
                                    }

                                </span>}
                                description={getMessageOfType(item)}
                            />
                            <div style={{
                                color: '#9aa8bd',
                                fontSize: '12px'
                            }}>{formatRelativeTime(item.messageTime)}</div>
                        </List.Item>
                    )}
                />
            </Sider>
            {
                currentSession ?
                    <>
                        <div style={{ padding: 0 }} className="message">
                            <Spin spinning={isLoading} delay={20}>
                                <Card className="message-card"
                                  title={<div><span>{currentSession?.groupName || '请选择一个群聊'}</span></div>}
                                  extra={<div style={{display: 'flex', alignItems: 'center'}}>
                                      <div className="info" style={{marginRight: 8}}>
                                          <div>{currentSession.manageEmployeeName}</div>
                                          <div>{currentSession.manageEmployeeDepartmentName}</div>
                                      </div>
                                      <Avatar shape="circle" size={36} src={currentSession.manageEmployeeAvatar} icon={<UserOutlined/>}></Avatar>
                                  </div>}
                                      style={{ borderTop: '0', background: 'unset' }}>
                                <div
                                    id="scrollableDiv"
                                    ref={scrollableDivRef}
                                >
                                    <div ref={hasMoreRef} style={{height: 10}}></div>

                                        {
                                            messagesOptions.map((message, i) => {
                                                return <Message key={i} message={message} />
                                            })
                                        }
                                </div>
                            </Card>
                                <div className="message-action">
                                    <div className="header">
                                        <WibotEmoji
                                          style={{ color: '#333' }}
                                          onEmojiSelect={handleEmojiSelect}
                                        />
                                    </div>
                                    <TextArea
                                      ref={inputRef}
                                      value={inputValue}
                                      style={{ resize: 'none', outline: 'none' }}
                                      onChange={onInputChange}
                                      onKeyDown={handleKeyDown}
                                      placeholder=""
                                    />
                                    <div className="footer">
                                        <Button disabled={isSending || inputValue.trim() === ''}
                                                onClick={handleClickSendMessage}>{isSending}发送</Button>
                                    </div>
                                </div>
                            </Spin>


                        </div>
                        <div theme="light" className="tabs">
                            <Tabs defaultActiveKey='1' items={[
                                {
                                    label: '客户群详情',
                                    key: '1',
                                    children: <GroupInfo session={currentSession} />
                                }
                            ]} />
                        </div>
                    </>:
                    <div style={{flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', background: '#fff', borderLeft: '1px solid #eee'}}>
                        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>
                    </div>
            }
        </Layout>
    </>);
};

export default Group
