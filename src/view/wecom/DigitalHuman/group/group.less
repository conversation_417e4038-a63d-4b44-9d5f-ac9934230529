
.group {
  overflow-y: hidden;
  min-width: 1600px;
  .ant-list-item {

  }

  .selected {
    background-color: #c5c4c5;
  }

  .ant-layout-sider {
    overflow: auto;
    height: calc(100vh - 64px - 42px);
    //position: fixed;
    //left: 0;
    //top: 0;
    //bottom: 0;
  }

  .account {
    border-right: 1px solid #eee;

    .account-item {
      padding: 3px 6px;
      justify-content: center;
      &:first-child{
        padding-top: 6px;
      }
    }

    .wrapper {
      min-height: 90px;
      padding: 6px;
      padding-top: 12px;
      flex: 1;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      &:hover {
        background-color: #d9d8d8;
      }
      .name {
        text-align: center;
      }
      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 0;
        }
      }

    }
  }

  .session {
    padding: 8px;
    .session-item {
      border-radius: 6px;
      padding: 12px;
      //margin-bottom: 8px;
      align-items: start;
      min-height: 72px;

      &:hover {
        background-color: #d9d8d8;
      }
    }
  }

  .message {
    width: 550px;
    display: flex;
    flex-direction: row;
    background-color: #f5f5f5;
    height: calc(100vh - 64px - 50px);
    //overflow: hidden!important;

    .message-card {
      width: 550px;
      display: flex;
      flex-direction: column;
      border-bottom: none;

      .ant-card-head {
        background-color: #fff;
        .ant-card-extra {
          padding: 0;
        }
      }

      .ant-card-body {
        flex: 1;
        overflow: auto;
        padding: 0;
      }

      .ant-collapse {
        border: none;

        .ant-collapse-item {
          border-bottom: none;

          .ant-collapse-header {
            background-color: #d1d1d1;
          }

          .ant-collapse-content {
            border-top: none;
            background-color: transparent;
          }
        }

      }
    }
    .message-action {
    border: 1px solid #ccc;
    border-radius: 6px;
    box-sizing: border-box;
    margin: 0px 14px 14px 14px;
    height: 100%;
    position: relative;
    .header {
      height: 32px;
      padding: 4px 8px;
      display: flex;
      align-items: center;
      .EmojiIcon {
        color: #333333;
      }
    }

    textarea {
      resize: none;
      outline: none;
      border: none;
      background-color: unset;
      height: 122px;
      width: 100%;

      &.ant-input:hover, &.ant-input:focus, &.ant-input-focused{
        outline: none !important;
        border: none!important;
        box-shadow: none!important;
      }
    }

    .footer {
      position: absolute;
      right: 0;
      bottom: 0;
      //right: 10px;
      //bottom: -12px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      // padding: 0 12px 12px 0;
      .ant-btn {
        margin-bottom: 12px;
        margin-right: 12px;
      }
    }
  }
  }

  .tabs {
    background: #fff;
    width: 100%;
    min-width: 300px;
    padding: 12px;
    .ant-tabs {
      height: 100%;
      .ant-tabs-content {
        overflow: auto;
        height: 100%;
      }
    }
  }


}

#scrollableDiv {
  height: calc(100vh - 64px - 42px - 58px - 184px);
  overflow: auto;
  display: flex;
  flex-direction: column;
  padding: 14px;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: #c0c0c0;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #7c7c7c;
  }
}
