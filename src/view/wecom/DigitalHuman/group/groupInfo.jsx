import {
  Avatar,
  Card,
  Descriptions,
  List,
  Spin,
  Timeline,
  Typography,
} from "antd"
import React, { useCallback, useEffect, useRef, useState } from "react"
import {
  CaretDownOutlined,
  CaretUpOutlined,
  UserOutlined,
} from "@ant-design/icons"
import "./groupInfo.less"
import {
  queryGroupInfoByGroupId,
  queryGroupMemberActivities,
  queryGroupMemberInfo,
} from "@/view/wecom/DigitalHuman/group/api"
import SysDictLabel from "components/select/SysDictLabel"

const { Title, Text } = Typography

const GroupInfo = ({ session: group }) => {
  const [loading, setLoading] = useState(false)
  const [state, setState] = useState({
    groupInfo: null,
    groupMembers: [],
    groupActivities: [],
    activitesPage: {
      current: 1,
      size: 20,
      total: 0,
      records: [],
      hasMore: true,
    },
  })

  const groupInfoRef = useRef()

  useEffect(() => {
    const fetchData = async () => {
      if (!group) {
        setState((prev) => ({
          ...prev,
          groupInfo: null,
          groupMembers: [],
          groupActivities: [],
          activitesPage: { ...prev.activitesPage, current: 1, hasMore: true },
        }))
        return
      }

      setLoading(true)
      try {
        const groupId = group.groupId
        const [groupInfoRes, memberInfoRes] = await Promise.all([
          queryGroupInfoByGroupId(groupId),
          queryGroupMemberInfo(groupId),
        ])

        setState((prev) => ({
          ...prev,
          groupInfo: groupInfoRes,
          groupMembers: memberInfoRes.records,
          activitesPage: { ...prev.activitesPage, current: 1, hasMore: true },
        }))

        // 获取初始活动数据
        const activitiesRes = await queryGroupMemberActivities({
          groupId,
          current: 1,
          size: state.activitesPage.size,
        })
        setState((prev) => ({
          ...prev,
          groupActivities: activitiesRes.records,
          activitesPage: {
            ...prev.activitesPage,
            total: activitiesRes.total,
            hasMore:
              activitiesRes.total > activitiesRes.size * activitiesRes.current,
          },
        }))
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [group])

  const handleLoadMore = useCallback(() => {
    if (!state.activitesPage.hasMore || loading) return

    setState((prev) => {
      const nextCurrent = prev.activitesPage.current + 1
      const groupId = group?.groupId
      if (!groupId) return prev

      queryGroupMemberActivities({
        groupId,
        current: nextCurrent,
        size: prev.activitesPage.size,
      }).then((res) => {
        setState((innerPrev) => ({
          ...innerPrev,
          groupActivities: innerPrev.groupActivities.concat(...res.records),
          activitesPage: {
            ...innerPrev.activitesPage,
            current: nextCurrent,
            hasMore: res.total > res.size * nextCurrent,
          },
        }))
      })

      return prev
    })
  }, [state.activitesPage.hasMore, loading, group])

  const [showAllMembers, setShowAllMembers] = useState(false)
  const displayedMembers = showAllMembers
    ? state.groupMembers
    : state.groupMembers.slice(0, 2)

  const toggleMembers = () => {
    setShowAllMembers(!showAllMembers)
  }

  return (
    <div className="group-info" ref={groupInfoRef}>
      <Spin spinning={loading}>
        {state.groupInfo && (
          <Card
            title={
              <div>
                <span>客户群详情</span>
              </div>
            }
            style={{ marginBottom: 24 }}
          >
            <Descriptions title={state.groupInfo.name} column={1}>
              <Descriptions.Item label="群主">
                <Avatar
                  size="small"
                  src={state.groupInfo.leader?.avatar}
                  icon={<UserOutlined />}
                  style={{ marginRight: 8 }}
                />
                <Text>{state.groupInfo.leader?.name}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="管理员">
                <Avatar
                  size="small"
                  src={state.groupInfo.managerList?.[0]?.avatar}
                  icon={<UserOutlined />}
                  style={{ marginRight: 8 }}
                />
                <Text>{state.groupInfo.managerList?.[0]?.name || "-"}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <Text>{state.groupInfo.createTime}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="总人数">
                {state.groupInfo.memberStatVO.totalNumber}
              </Descriptions.Item>
              <Descriptions.Item label="总客户数">
                {state.groupInfo.memberStatVO.totalCustomerNumber}
              </Descriptions.Item>
              <Descriptions.Item label="今日进群人数">
                {state.groupInfo.memberStatVO.todayAddNumber}
              </Descriptions.Item>
              <Descriptions.Item label="今日退群人数">
                {state.groupInfo.memberStatVO.todayRemovalNumber}
              </Descriptions.Item>
              <Descriptions.Item label="今日活跃人数">
                {state.groupInfo.memberStatVO.todayActiveNumber}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </Spin>

      <Card
        title={<Title level={5}>群公告</Title>}
        style={{ marginBottom: 24 }}
      >
        <Text>{state.groupInfo?.notice}</Text>
      </Card>

      <Card
        title={<Title level={5}>群成员</Title>}
        style={{ marginBottom: 24 }}
      >
        <List
          dataSource={displayedMembers}
          renderItem={(member) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Avatar size="large" shape="circle" src={member.avatar} />
                }
                title={
                  <div>
                    {member.name}{" "}
                    {member.companyName && (
                      <span style={{ color: "green" }}>
                        {member.companyName}
                      </span>
                    )}
                  </div>
                }
                description={"加入时间: " + member.joinTime}
              />
            </List.Item>
          )}
        />
        <div style={{ textAlign: "center" }} onClick={toggleMembers}>
          {showAllMembers ? <CaretUpOutlined /> : <CaretDownOutlined />}
        </div>
      </Card>

      <Card title={<Title level={5}>群动态</Title>}>
        <Timeline mode="left">
          {state.groupActivities.map((activity, index) => (
            <Timeline.Item key={index} label={activity.createTime}>
              {activity.type === "ADD_MANAGER" && (
                <AddManager activity={activity} />
              )}
              {activity.type === "DEL_MANAGER" && (
                <DelManager activity={activity} />
              )}
              {activity.type === "ADD_MEMBER" && (
                <AddMember activity={activity} />
              )}
              {activity.type === "DEL_MEMBER" && (
                <DelMember activity={activity} />
              )}
              {activity.type === "CHANGE_NAME" && (
                <ChangeName activity={activity} />
              )}
              {activity.type === "CHANGE_NOTICE" && (
                <ChangeNotice activity={activity} />
              )}
              {activity.type === "CREATE" && <Create activity={activity} />}
            </Timeline.Item>
          ))}
          <HasMore load={handleLoadMore} />
        </Timeline>
      </Card>
    </div>
  )
}

export default GroupInfo

// 定义缺失的组件
const AddManager = ({ activity }) => (
  <div>
    <Title level={5}>添加管理员</Title>
    <Text strong>{activity.detail.name}</Text>
  </div>
)

const DelManager = ({ activity }) => (
  <div>
    <Title level={5}>删除管理员</Title>
    <Text strong>{activity.detail.name}</Text>
  </div>
)

const AddMember = ({ activity }) => (
  <div>
    <Title level={5}>成员入群</Title>
    <div>
      <Text>{activity.detail.name}</Text>
      <span style={{ color: "green" }}>{activity.detail.suffix}</span>
    </div>
    <div>
      <Text>{activity.detail.joinScene}</Text>
    </div>
  </div>
)

const DelMember = ({ activity }) => (
  <div>
    <Title level={5}>成员退群</Title>
    <div>
      <Text>{activity.detail.name}</Text>
      <span style={{ color: "green" }}>{activity.detail.suffix}</span>
    </div>
    <div>
      <Text>
        <SysDictLabel
          dataset="WECOM_QUITSCENE"
          dictkey={activity.detail.quitScene}
        />
      </Text>
    </div>
  </div>
)

const Create = () => (
  <div>
    <Title level={5}>创建群组</Title>
  </div>
)

const ChangeName = ({ activity }) => (
  <div>
    <Title level={5}>群名变更</Title>
    <div>
      <Text>变更前: {activity.detail.beforeValue}</Text>
    </div>
    <div>
      <Text>变更后: {activity.detail.laterValue}</Text>
    </div>
  </div>
)

const ChangeNotice = ({ activity }) => (
  <div>
    <Title level={5}>群公告变更</Title>
    <div>
      <Text>群公告: {activity.detail.laterValue}</Text>
    </div>
  </div>
)

export const HasMore = ({ load }) => {
  const observer = useRef(null)
  const lastItemElementRef = useCallback(
    (node) => {
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          load()
        }
      })
      if (node) observer.current.observe(node)
    },
    [load],
  )
  return <div style={{ height: 1 }} ref={lastItemElementRef}></div>
}
