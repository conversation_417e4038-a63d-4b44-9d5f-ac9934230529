.group-info {
  overflow: auto;
  // height: calc(100vh - 64px - 42px);
  background: #fff;
  .ant-card-body {
  }

  .ant-descriptions-item-label {
    //min-width: 70px;
    //text-align: justify;
    //text-align-last: justify;
  }
  .ant-descriptions-item-content {
    align-items: center;
  }
  .group-count {
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    background: #eee;
    padding: 12px 0;
    border-radius: 10px;

    .group-count-item {
      flex: 1;
      text-align: center;
    }

  }
}
