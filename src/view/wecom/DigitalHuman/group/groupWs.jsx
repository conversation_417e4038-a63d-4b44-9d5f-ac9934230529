import { transferProtocol } from "common/regular";
import { getApiUrl } from "config";
import <PERSON><PERSON> from "js-cookie";
import { useCallback, useEffect, useRef, useState } from 'react';

const useWebSocket = (url) => {
    const defaultUrl = `${transferProtocol()}//${process.env.NODE_ENV === 'development' ? 'test.wizone.work' : location.host}${getApiUrl()}/wecom/message/group/inbox/ws?token=${Cookie.get("weebot_cloud_token")}`;
    const ws = useRef(null);
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = 10;
    const [error, setError] = useState(null);
    const [messages, setMessages] = useState([]); // 新增消息存储
    const heartbeatInterval = useRef(null);

    const handleError = useCallback((error) => {
        setError(error?.message || error);
    }, []);

    const handleMessage = useCallback((message) => {
        try {
            const parsed = JSON.parse(message);
            console.log('Received message:', parsed);
            setMessages([parsed]); // 更新消息列表
            return parsed;
        } catch (e) {
            handleError(new Error('Invalid message format'));
            return null;
        }
    }, [handleError]);

    const sendMessage = useCallback((message) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
            try {
                ws.current.send(JSON.stringify(message));
            } catch (e) {
                handleError(e);
            }
        }
    }, [handleError]);

    const setupHeartbeat = useCallback(() => {
        heartbeatInterval.current = setInterval(() => {
            sendMessage('HeartBeat');
        }, 30000);
    }, [sendMessage]);

    const connectWebSocket = useCallback(() => {
        ws.current = new WebSocket(url || defaultUrl);

        ws.current.onopen = () => {
            console.log('WebSocket connected');
            reconnectAttempts.current = 0;
            setError(null);
            setupHeartbeat();
        };

        ws.current.onmessage = event => {
            try {
                const newMessage = event.data;
                handleMessage(newMessage);
            } catch (e) {
                handleError(e);
            }
        };

        ws.current.onclose = (event) => {
            console.log(`WebSocket closed: ${event.code}`);
            if (reconnectAttempts.current < maxReconnectAttempts) {
                const timeout = Math.min(30000, Math.pow(2, reconnectAttempts.current) * 1000);
                // console.log(`Reconnecting in ${timeout/1000}s (attempt ${reconnectAttempts.current + 1})`);
                setTimeout(() => {
                    reconnectAttempts.current += 1;
                    connectWebSocket();
                }, timeout);
            }
        };

        ws.current.onerror = error => {
            console.error('WebSocket error:', error);
            handleError(error);
        };
    }, [defaultUrl, handleError, handleMessage, setupHeartbeat, url]);

    useEffect(() => {
        connectWebSocket();
        return () => {
            // console.log('WebSocket cleanup');
            clearInterval(heartbeatInterval.current);
            if (ws.current && [WebSocket.CONNECTING, WebSocket.OPEN].includes(ws.current.readyState)) {
                ws.current.close(1000, 'Component unmount');
            }
        };
    }, [connectWebSocket]);

    return {
        messages,
        sendMessage,
        error
    };
};

export default useWebSocket;
