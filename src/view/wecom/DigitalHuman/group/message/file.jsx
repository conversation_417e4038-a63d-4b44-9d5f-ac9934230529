import {Col, Row, Typography} from "antd";
import {FileTextOutlined} from "@ant-design/icons";
import React from "react";
import {saveAs} from "file-saver";
import './file.less'
import MessageWrapper from "@/view/wecom/DigitalHuman/group/message/messageWrapper";

const {Paragraph} = Typography;

const FileMessage = ({message, onClick}) => {
    const handleClick = () => {
        saveAs(message.fileUrl, message.fileName);
        onClick && onClick();
        return false
    }
    return (
        <MessageWrapper message={message} onClick={handleClick}>
            <div className="file-message" style={{width: 200}}>
                    <Row justify="space-between" align={"middle"}>
                        <Col span={19}>
                            <Paragraph strong ellipsis={{rows: 1}}>
                                {message.fileName}
                            </Paragraph>
                            <span style={{color: "#999999"}}>{message.fileSize}</span>
                        </Col>
                        <Col>
                            <FileTextOutlined style={{fontSize: "35px"}}/>
                        </Col>
                    </Row>
            </div>
        </MessageWrapper>
    );
};

export default FileMessage;

