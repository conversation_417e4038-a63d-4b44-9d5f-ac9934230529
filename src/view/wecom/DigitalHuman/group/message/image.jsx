import {Image} from "antd";
import { FileHOC } from 'components/FileHOC/FileHOC';

const ImageMessage = ({message}) => {
    return (
        <div>
            <FileHOC src={message.fileUrl}>
                {(url) => (
                    <Image
                        style={{ objectFit: 'cover' }}
                        width={100}
                        height={100}
                        src={url}
                    />
                )}
            </FileHOC>
        </div>
    );
};

export default ImageMessage;

