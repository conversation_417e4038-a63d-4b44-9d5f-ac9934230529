import ImageMessage from "@/view/wecom/DigitalHuman/group/message/image";
import TextMessage from "@/view/wecom/DigitalHuman/group/message/text";
import React, {useMemo} from "react";
import {Avatar, Image} from "antd";
import './index.less'
import SystemMessage from "@/view/wecom/DigitalHuman/group/message/system";
import VoiceMessage from "@/view/wecom/DigitalHuman/group/message/voice";
import FileMessage from "@/view/wecom/DigitalHuman/group/message/file";
import WxMiniApp from "@/view/wecom/DigitalHuman/group/message/wxMiniApp";
import LinkMessage from "@/view/wecom/DigitalHuman/group/message/link";
import VideoMessage from "@/view/wecom/DigitalHuman/group/message/video";
import { FileHOC } from 'components/FileHOC/FileHOC';
import { UserOutlined } from '@ant-design/icons'
import moment from 'moment'

const placeholderUserAvatar = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABGdBTUEAA1teXP8meAAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMqADAAQAAAABAAAAMgAAAAB1y6+rAAAFN0lEQVRoBe1YSUhcTRAu933HBQV33BEVV1wTiOYgiQdFxQW9eFG8e/ck4sWjJ0UEFSQuxIO7JBijoOhBjOKCRnFB4777///X8JoZY/S1PRN+ZApmXr/uel31dVVXVbfRP/8RvQIyfgUYGAQDkP+bJQ0WMVhETyvwalzLVE8LxKa9ubmh09NT1ra3tycTExO9idM5kKurK/r27RsNDAzQjx8/tBQPCgqi9+/fU0pKila/Ll6MdJXZsfrz8/PU1dVFS0tL9FTBEBgYSLm5uRQZGUmmprpZS50Aubi4oPb2dhoaGiJYRA3Z2NjQmzdvqLS0VA37szzSy3F3d0efP39mP0jDPnB3d6e4uDiKiYkhb29vpsTm5iZNTk7SzMwM/fz5k87Ozqivr4/Mzc2ZdWQtI22RiYkJamxspNvbW6ZwWloaFRYWkouLC3vX/IO77e/vM+uNj4+zIVtbW6qurqaoqChNVuG2VPg9Ojqizs5ODsLNzY0qKioeBQHNjIyMyNXVlaqqqig6Opopi6gGi8qSFJCFhQXa2dlhOjg5OVFtbS1zFTVK5eXlEb4Bzc7O0sbGhprP/sgjBQTRCdEKBJdydHT8o6CHA35+fhQcHMy7h4eHefslDSkgyipiwyJHiBCCQmJiIv9kenqat1/SkAJycnLCZFpbWwtZQ1HU19dXaTIXVazLOwUaUkDu7+8FRP3OamysLV6JfL9zPt+jPdPz/FocDg4O7P3y8pLXVFoMz7xsbW1xDjs7O7KysuLvog0pIIprIJuvrKyIyibkIIUSEhKU5oueUkACAgLIzMyM1VUoEn/9+qVaicXFRZqbm2P8cLG3b9+q/vYxRikgiFSenp5s3oODA6qrq6Pd3d3H5Gj1nZ+fU0dHBwceERFBKCRlSAoIElpRURGXv7y8TA0NDVxBPqDR2N7eZiUJKmWULChRPn78qMHxsqZ0rQWxbW1trMy4vr5mWiCvJCcns/oJNRdKE1gMuWJqaopgERDcsri4mLKysuhhBGMMAn86AQIA2CPNzc1aogEIOQaEalczT6C/rKyMUlNTdXJylHItKIgyfm9vj5XmeNckAEQAwE8TBHjwvrq6yqphzCFLUhZB5drf309jY2O/bXK4E/IC9gAIvIpLaSqNivndu3fS+0QKSH19PX3//l1TL1bCZ2ZmsnCqJEyFASXN4OAg9fT0MFdT+vFMSkpiQeClFxQvAnJ8fExNTU3sxAclcLoLDQ2ljIwMio+PJwsLC3Q/SV++fKGRkRF2zlcYUUGXl5cTjsGiJAwEvt3a2spcShEGCxQUFHA3UvqfeiL0wt0+ffpEvb29jBXWwFwAI0rCZ3aUFYhQIITPDx8+UH5+vqhcFpJRX+FbAAAgbHrsOSRZ7BuRkCwUtXAabGlp4UdbuAKAyBBCdE5ODqWnp/Npuru7CZcVIiQEBIkP+0OhkpISqYpVmQc5pbKykvz9/VkXLii+fv2qDKt6qgayvr7Obw6xuWtqaniyUyVJBVN2djY/84+Ojqq+I8PUqoGsra3R4eEhUycsLEz6+uYxXLh59PDwYEOQJXL8VQ0E97g4ESLR4Q4KT10TLrrDw8P5tEpQ4R1PNFQDUU5z8GcfH58nppQbio2N5RNg8R6WNnzwQUM1EMWtLC0tCWFTX+Tl5cWnxhkel4BqSHUewQ0hMq6zszPBBfRFmD8kJITlFMhQe8GhOrNjZZCNkaRQCIokK1HQiix8B+urqb9UAxFV5m/zq94jf1sxUXkGIKIrpm9+g0X0vcKi8xssIrpi+ub/F6vEDsL7RXLTAAAAAElFTkSuQmCC`
const UserAvatar = ({src}) => <Avatar size={36} style={{marginRight: 12, backgroundColor: '#eee'}} icon={<UserOutlined />} src={
    <FileHOC src={src}>
        {(url) => (
            <Image src={url} />
        )}
    </FileHOC>
}/>

const EmployeeAvatar = ({ src }) => <Avatar size={36} style={{ marginLeft: 12, backgroundColor: '#eee' }} icon={<UserOutlined />} src={
  <FileHOC src={src}>
    {(url) => (
      <Image src={url} />
    )}
  </FileHOC>
} />

function formatRelativeTime (dateTime) {
    if (!dateTime) { return }
    const [date, time] = dateTime.split(' ')
    const today = moment().format('YYYY-MM-DD')
    if (date === today) {
        return time
    } else {
        return date.replace(/-/g, '/') + ' ' + time
    }
}

const Message = ({message}) => {

    // message.content = useMemo(() => {
    //     try {
    //         return JSON.parse(message.content);
    //     } catch (err) {
    //         return message.content;
    //     }
    // }, [message.content])
    message.content = message.messageMap?.content
    const isForCustomer = message.senderType === 'CUSTOMER'
    const isSystemMessage = message.type === 'CUSTOM_SYSTEM'

    const messageClass = useMemo(() => {
        if (message.type === 'CUSTOM_SYSTEM') {
            return 'system'
        } else {
            return message.senderType === 'CUSTOMER' ? 'customer' : 'employee'
        }
    }, [message])


    let msg = ''
    switch (message.type) {
        case 'TEXT':
            msg = <TextMessage message={message}/>
            break
        case 'IMAGE':
            msg = <ImageMessage message={message}/>
            break
        case 'VOICE':
            msg = <VoiceMessage message={message}/>
            break
        case 'EMOTION':
            msg = <ImageMessage message={message}/>
            break
        case 'VIDEO':
            msg = <VideoMessage message={message}/>
            break
        case 'FILE':
            msg = <FileMessage message={message}/>
            break
        // case 'location':
        //     return <LocationMessage message={message}/>
        case 'LINK':
            msg = <LinkMessage message={message}/>
            break
        case 'WE_APP':
            msg = <WxMiniApp message={message}/>
            break
        case 'CUSTOM_SYSTEM':
            msg = <SystemMessage message={message}/>
            break
        default:
            msg = <div>{message.type}</div>
    }
    return <div
        className={`message-item-wrapper ${messageClass}`}>
        {
            !isSystemMessage &&
            <div className="message-time">{message.senderName} {formatRelativeTime(message.messageTime)}</div>
        }
        {!isSystemMessage && isForCustomer ? <UserAvatar src={message.senderAvatar}/> : ''}
        {/*<div>{message.type}</div>*/}
        <div className="message-item">

            {msg}
        </div>
        {!isSystemMessage && !isForCustomer ? <EmployeeAvatar src={message.senderAvatar || ''}/> : ''}
    </div>
}

export default Message
