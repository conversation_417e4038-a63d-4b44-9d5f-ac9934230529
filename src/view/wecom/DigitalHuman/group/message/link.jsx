import {Col, Row, Typography} from "antd";
import MessageWrapper from "@/view/wecom/DigitalHuman/group/message/messageWrapper";

const {Paragraph} = Typography
const LinkMessage = ({message}) => {
    const {title, description, linkUrl, imageUrl} = message.messageMap
    return <MessageWrapper message={message}>
        <div className="link-message" style={{width: 200}}>
            <Row>
                <Col>
                    <Paragraph strong ellipsis={{rows: 1}}>{title}</Paragraph>
                    <Paragraph ellipsis={{rows: 1}}>{description}</Paragraph>
                </Col>
            </Row>
            <Row>
                <Col offset={18}>
                    <img style={{width: 50, height: 50, objectFit: 'cover'}} src={imageUrl}/>
                </Col>
            </Row>
        </div>
    </MessageWrapper>
}
export default LinkMessage
