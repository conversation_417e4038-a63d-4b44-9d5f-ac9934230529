/* 基础消息包装样式 */
.message-wrapper {
    width: fit-content;
    padding: 8px 12px;
    border-radius: 4px;
    //margin: 2px 12px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    max-width: 250px;
}

/* 客户消息样式 */
.message-wrapper.left-triangle {
    background-color: #fff;
    color: #333;
}

/* 客服消息样式 */
.message-wrapper.right-triangle {
    background-color: #fff;
    //background-color: #c7e2ff !important;
    color: #333;
    align-self: flex-end;
}

/* 向左的三角形样式 */
.message-wrapper.left-triangle::after {
    content: "";
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #fff;
}

/* 向右的三角形样式 */
.message-wrapper.right-triangle::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -8px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #fff;
}
