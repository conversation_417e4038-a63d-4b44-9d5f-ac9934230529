import {Divider, Popover} from "antd";


const CountDown = ({message}) => {
    const {remain_min, time} = message
    return <>
        <Divider plain>
            对话将在 {remain_min} 分钟后重新分配 {_getSendTime(message)}
        </Divider>
    </>
}

const PlainText = ({message}) => {
    const content = message.content.content
    return <Divider plain>{content} {_getSendTime(message)}</Divider>
}


function _getSendTime(message) {
    return message?.sendTime?.split(' ')[1]
}

const Distribution = ({message}) => {
    const {distributionRemark, agentName, agentGroupName, fromAgentName} = message.messageMap
    return <Divider plain>{fromAgentName}
        <Popover content={`转接备注: ${distributionRemark || ''}`} title="" trigger="click">
            <span style={{color: '#0088ff', margin: '0 4px'}}>转接对话</span>
        </Popover>给 {agentGroupName} {agentName} {_getSendTime(message)}</Divider>
}
const SystemMessage = ({message}) => {
    if (!message.messageMap.type) {
        console.error('message.messageMap.type 不存在', message)
        return
    }
    switch (message.messageMap.type) {
        // 普通提示
        case "PLAIN_TEXT":
            return <PlainText message={message}/>
        // 倒计时转接
        case "COUNTDOWN_DISTRIBUTION":
            return <CountDown message={message}/>
        // 转接
        case "DISTRIBUTION":
            return <Distribution message={message}/>
        default:
            return ''
    }
}

export default SystemMessage
