import {useEffect, useRef, useState} from "react";
import {PlayCircleOutlined} from "@ant-design/icons";
import {FileHOC} from 'components/FileHOC/FileHOC';

const VideoMessage = ({message}) => {
    const videoRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const handleClick = () => {
        if (videoRef.current) {
            if (videoRef.current.paused) {
                videoRef.current.play();
            } else {
                videoRef.current.pause();
            }
        }
    }
    useEffect(() => {
        const video = videoRef.current
        if (!video) {
            return
        }
        const handlePlay = () => {
            setIsPlaying(true);
        };
        const handlePause = () => {
            setIsPlaying(false);
        };
        video.addEventListener('play', handlePlay);
        video.addEventListener('pause', handlePause);
        // 组件卸载时移除监听器
        return () => {
            video.removeEventListener('play', handlePlay);
            video.removeEventListener('pause', handlePause);
        }

    }, [])
    return (
        <div style={{position: 'relative'}}>
            {
                isPlaying ?
                    null :
                    <div style={{
                        position: 'absolute',
                        left: '50%',
                        top: '50%',
                        transform: 'translate(-50%, -50%)',
                        color: '#eee'
                    }} className="icon paused" onClick={handleClick}>
                        <PlayCircleOutlined/>
                    </div>
            }
            <FileHOC src={message.fileUrl}>
                {(url) => (
                  <video
                    ref={videoRef}
                    onClick={handleClick}
                    playsInline
                    style={{objectFit: 'cover'}}
                    width={250}
                    src={url}
                  />
                )}
            </FileHOC>
        </div>
    );
};

export default VideoMessage;

