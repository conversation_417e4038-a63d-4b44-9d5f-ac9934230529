import React, {useEffect, useState} from 'react';
import './voice.less';
import BenzAMRRecorder from "benz-amr-recorder";
import MessageWrapper from "@/view/wecom/DigitalHuman/group/message/messageWrapper";
import {formatURL} from "config";

const VoiceMessage = ({message}) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const amr = new BenzAMRRecorder();
    amr.initWithUrl(message.fileUrl)


    useEffect(() => {
        amr.onEnded(() => {
            setIsPlaying(false);
        })
        amr.onPause(() => {
            setIsPlaying(false);
        })
    }, [amr])


    const handlePlayAudio = () => {
        if (isPlaying) {
            amr.pause();
        } else {
            amr.play();
            // if (amr.isInit()) {
            //
            // }else {
            //     amr.initWithUrl(message.fileUrl).then(() => {
            //         amr.play();
            //     });
            // }
        }
        setIsPlaying(!isPlaying);
    };

    return (
        <MessageWrapper message={message} onClick={handlePlayAudio}>
            <div className={`voice-message`}>
                <div className="voice1"></div>
                <div className={`voice2 ${isPlaying ? 'pressed' : ''}`}></div>
                <div className={`voice3 ${isPlaying ? 'pressed' : ''}`}></div>
                <div className="voice-time">
                    {/* 这里可以根据音频时长动态显示，暂时省略实现 */}
                    {message.messageMap?.playLength}
                </div>
            </div>
        </MessageWrapper>
    );
};

export default VoiceMessage;

