.voice-message {
  position: relative;
  display: inline-block;
  width: 100px;
  background-color: #fff;
  border-radius: 6px;
  cursor: pointer;
  left: -6px;

  .voice1 {
    width: 10px;
    height: 10px;
    border: 2px solid;
    border-radius: 50%;
    opacity: 1;
    border-color: transparent #000 transparent transparent;
    position: absolute;
    top: -10px;
  }

  .voice2 {
    width: 15px;
    height: 15px;
    border: 2px solid;
    border-radius: 50%;
    opacity: 1;
    border-color: transparent #000 transparent transparent;
    position: absolute;
    top: -12px;
    left: -1px;
  }

  .voice3 {
    width: 20px;
    height: 20px;
    border: 2px solid;
    border-radius: 50%;
    opacity: 1;
    border-color: transparent #000 transparent transparent;
    position: absolute;
    top: -15px;
    left: -2px;
  }

  .voice-time {
    position: absolute;
    top: -16px;
    left: 24px;

    &::after {
      content: '"';
    }
  }

  .voice2.pressed {
    -webkit-animation: voice2 1.2s ease-in-out infinite;
  }

  .voice3.pressed {
    -webkit-animation: voice3 1.2s ease-in-out infinite;
  }

  @-webkit-keyframes voice2 {
    0% {
      opacity: 0;
    }

    20%,
    100% {
      opacity: 1;
    }
  }

  @-webkit-keyframes voice3 {
    0% {
      opacity: 0;
    }

    70%,
    100% {
      opacity: 1;
    }
  }
}

