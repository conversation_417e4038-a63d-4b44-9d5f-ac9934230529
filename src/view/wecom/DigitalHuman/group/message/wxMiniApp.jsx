import MessageWrapper from "@/view/wecom/DigitalHuman/group/message/messageWrapper";
import {Col, Row, Typography} from "antd";
import React from "react";

const {Paragraph} = Typography;

const WxMiniApp = ({message}) => {
    const {displayName, title} = message.messageMap
    return (
        <MessageWrapper message={message}>
            <div className="wx-mini-app-message" style={{width: 200}}>
                <Row justify="space-between" align={"middle"}>
                    <Col>
                        <Paragraph strong ellipsis={{rows: 2}}>
                            {title}
                        </Paragraph>
                        <span style={{color: "#999999"}}>{displayName}</span>
                    </Col>

                </Row>
                <Row style={{borderTop: '1px solid #eee', paddingTop: 8, marginTop: 8}}>
                    <Col>
                        <img style={{width: 12, height: 12}} src="images/applet.png"/> 小程序
                    </Col>
                </Row>
            </div>
        </MessageWrapper>
    );
}

export default WxMiniApp
