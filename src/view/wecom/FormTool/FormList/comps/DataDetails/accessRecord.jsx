/*
 * @Author: <PERSON>eiw
 * @Date: 2023/05/18 09:58
 * @LastEditTime: 2024/01/08 16:17
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\FormTool\FormList\comps\DataDetails\accessRecord.jsx
 * @Description: '访问记录'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Card, Form, Input, Table, DatePicker, Avatar } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp, secondsToTime } from 'common/date';
import { qs2obj } from 'common/object';
import moment from 'moment';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const AccessRecord = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      fixed: 'left',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '220px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'left',
      fixed: 'left',
      render: (value, record, index) => {
        const content = <div style={{ display: 'flex' }}>
          <Avatar size={40} src={record.customerAvatar} />
          <div style={{ marginLeft: '6px', whiteSpace: 'normal', display: "flex", flexDirection: "column" }}>
            <span>{value}</span>
            <span>{record.customerNickName}</span>
          </div>
        </div>;
        return content;
      }
    },
    {
      title: '访问时间',
      width: '160px',
      dataIndex: 'visitTime',
      key: 'visitTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime)
    },
    {
      title: '第几次访问',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
    },
    {
      title: '首次访问时间',
      width: '160px',
      dataIndex: 'firstVisitTime',
      key: 'firstVisitTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime)
    },
    {
      title: '本次访问时长',
      width: '160px',
      dataIndex: 'duration',
      key: 'duration',
      align: 'center',
      render: (value, record, index) => (secondsToTime(value, 'HH小时mm分ss秒', true)),
    },
    {
      title: '累计访问时长',
      width: '160px',
      dataIndex: 'totalDuration',
      key: 'totalDuration',
      align: 'center',
      render: (value, record, index) => (secondsToTime(value, 'HH小时mm分ss秒', true)),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetail(record)}>客户详情</a>
        </>
      )
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    fetchList();
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.minCreateTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
        id: id,
      };
      apiCall('/base/form/feedback/data/visit', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleExport = () => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.minCreateTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      const { shareTitle } = props.params.formInfo;
      const data = {
        ...formData,
        id: id,
      };
      apiCall('/base/form/feedback/data/visit/export', 'POST', data, null, { isExit: true, title: `${shareTitle}的访问记录.${moment().format('YYYY-MM-DD')}.xlsx` }).then((res) => {
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    })

  };

  const handleDetail = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: '/wecom/customer/details',
      search: `?id=${customerId}`
    });
  };

  return (
    <div className='AccessRecord-Container'>
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="customerName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户名称" allowClear />
          </FormItem>
          <FormItem name="createTime" label="访问时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => { handleExport(); }}>导出</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default withRouter(AccessRecord);
