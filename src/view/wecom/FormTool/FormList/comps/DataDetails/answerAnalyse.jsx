/*
 * @Author: <PERSON>eiw
 * @Date: 2023/05/16 15:48
 * @LastEditTime: 2024/01/08 16:08
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\FormTool\FormList\comps\DataDetails\answerAnalyse.jsx
 * @Description: '答题分析'
 */

import React, { useState, useEffect } from "react";
import { withRouter } from "react-router-dom";
import {
  Spin,
  Button,
  Card,
  Table,
  Avatar,
  Empty,
  Form,
  Input,
  Image,
  Space,
} from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import { qs2obj } from "common/object";
import { saveAs } from "file-saver";
import FilterBar from "components/FilterBar/FilterBar";
import {
  removeInputEmpty,
  unescapeFile,
  checkSuffix,
} from "common/regular";
import { Pie } from "@ant-design/plots";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;

const AnswerAnalyse = (props) => {
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [formRef] = Form.useForm(); // 分析问卷1
  const [dataSource, setDataSource] = useState([]);
  const columns1 = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "答题客户",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "left",
      render: (value, record, index) => {
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                whiteSpace: "normal",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <span>{value}</span>
              <span>{record.customerNickName}</span>
            </div>
          </div>
        );
        return content;
      },
    },
    {
      title: "答题内容",
      width: "340px",
      dataIndex: "feedbackContent",
      key: "feedbackContent",
      align: "left",
    },
  ];
  const columns2 = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "答题客户",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "left",
      render: (value, record, index) => {
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                whiteSpace: "normal",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <span>{value}</span>
              <span>{record.customerNickName}</span>
            </div>
          </div>
        );
        return content;
      },
    },
    {
      title: "答题内容",
      width: "340px",
      dataIndex: "downloadUrl",
      key: "downloadUrl",
      align: "left",
      render: (value, record) => {
        return (
          <Space wrap>
            {value.map((atem, andex) => {
              if (checkSuffix(atem) == "IMAGE") {
                return (
                  <FileHOC src={atem || "error"}>
                    {(url) => (
                      <Image
                        key={andex}
                        width={60}
                        height={60}
                        src={url}
                        fallback="images/fallbackImg.png"
                        preview
                      />
                    )}
                  </FileHOC>
                );
              } else if (checkSuffix(atem) == "VIDEO") {
                return (
                  <div key={andex}>
                    <FileHOC src={atem}>
                      {(url) => (
                        <video
                          style={{ maxWidth: "200px", maxHeight: "100px" }}
                          controls
                          src={url}
                        />
                      )}
                    </FileHOC>
                  </div>
                );
              } else {
                return (
                  <p key={andex} style={{ margin: "0" }}>
                    {unescapeFile(atem)}
                  </p>
                );
              }
            })}
          </Space>
        );
      },
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      render: (value, record, index) => (
        <>
          <a
            onClick={() => {
              const { downloadUrl } = record;
              for (let index = 0; index < downloadUrl.length; index++) {
                saveAs(downloadUrl[index], unescapeFile(downloadUrl[index]));
              }
            }}
          >
            下载
          </a>
        </>
      ),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    id && fetchList();
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.validateFields().then((formData) => {
      const data = {
        formId: id,
        questionMap: {
          ...formData,
        },
      };
      apiCall("/base/form/feedback/data/analyse", "POST", data)
        .then((res) => {
          setDataSource(res);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = (name) => {
    formRef.resetFields([name]);
    fetchList();
  };

  const handleExport = () => {
    setLoading(true);
    const { shareTitle } = props.params.formInfo;
    const data = {
      id: id,
    };
    apiCall("/base/form/feedback/data/export", "POST", data, null, {
      isExit: true,
      title: `${shareTitle}的答题记录.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const filterFormType = (type = "") => {
    if (!type) {
      return "";
    }
    let title = "";
    switch (type) {
      case "input":
        title = "输入框";
        break;
      case "input-textarea":
        title = "文本框";
        break;
      case "radio":
        title = "点击单选";
        break;
      case "checkbox":
        title = "点击多选";
        break;
      case "picker":
        title = "下拉单选";
        break;
      case "datetimePicker":
        title = "日期选择";
        break;
      case "uploader":
        title = "图片/文件";
        break;
      case "input-tel":
        title = "手机号码";
        break;
    }
    return title;
  };

  const DemoPieGender = (data) => {
    const config = {
      appendPadding: 10,
      data: data.map(item => ({
        type: item.optionContent,
        value: item.count,
      })),
      angleField: 'value',
      colorField: 'type',
      radius: 0.75,
      label: {
        type: 'spider',
        labelHeight: 28,
        content: '{name}\n{percentage}',
      },
      interactions: [
        {
          type: 'element-selected',
        },
        {
          type: 'element-active',
        },
      ],
    };
    return <Pie {...config} />;
  };

  return (
    <div className="AnswerAnalyse-Container">
      <Spin spinning={loading}>
        <div style={{ display: "flex", alignItems: "center" }}>
          <p style={{ marginRight: "10px" }}>共 {dataSource.length} 条记录</p>{" "}
          {(dataSource && dataSource.length && (
            <Button
              type="primary"
              style={{ marginBottom: "16px" }}
              onClick={() => {
                handleExport();
              }}
            >
              导出excel
            </Button>
          )) || <></>}
        </div>
        {(dataSource &&
          dataSource.length &&
          dataSource?.map((item, index) => {
            let content = "";
            if (
              item["vc-type"] == "input" ||
              item["vc-type"] == "input-textarea" ||
              item["vc-type"] == "datetimePicker" ||
              item["vc-type"] == "input-tel"
            ) {
              content = (
                <>
                  <FilterBar bodyStyle={{ padding: "unset", margin: "unset" }}>
                    <Form layout={"inline"} form={formRef}>
                      <FormItem
                        name={item.id}
                        getValueFromEvent={(e) => removeInputEmpty(e)}
                      >
                        <Input placeholder="答题内容" allowClear />
                      </FormItem>
                      <div className="flex flex-space-between">
                        <div>
                          <Button type="primary" onClick={() => handleQuery()}>
                            查询
                          </Button>
                          <Button onClick={() => handleReset(item.id)}>
                            重置筛选
                          </Button>
                        </div>
                      </div>
                    </Form>
                  </FilterBar>
                  <Table
                    rowKey="uuid"
                    dataSource={item.itemList}
                    columns={columns1}
                    scroll={{ y: 400 }}
                    pagination={false}
                  />
                </>
              );
            } else if (
              item["vc-type"] == "radio" ||
              item["vc-type"] == "checkbox" ||
              item["vc-type"] == "picker"
            ) {
              content = (
                <>
                  {(item.itemList.length && DemoPieGender(item.itemList)) || (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )}
                </>
              );
            } else if (item["vc-type"] == "uploader") {
              content = (
                <>
                  <FilterBar bodyStyle={{ padding: "unset", margin: "unset" }}>
                    <Form layout={"inline"} form={formRef}>
                      <FormItem
                        name={item.id}
                        getValueFromEvent={(e) => removeInputEmpty(e)}
                      >
                        <Input placeholder="答题内容" allowClear />
                      </FormItem>
                      <div className="flex flex-space-between">
                        <div>
                          <Button type="primary" onClick={() => handleQuery()}>
                            查询
                          </Button>
                          <Button onClick={() => handleReset(item.id)}>
                            重置筛选
                          </Button>
                        </div>
                      </div>
                    </Form>
                  </FilterBar>
                  <Table
                    rowKey="uuid"
                    dataSource={item.itemList}
                    columns={columns2}
                    scroll={{ y: 400 }}
                    pagination={false}
                  />
                </>
              );
            }
            return (
              <Card
                key={index}
                title={
                  <>
                    {index + 1}、[{filterFormType(item["vc-type"])}]{item.title}
                    {item.description && `（${item.description}）`}
                  </>
                }
              >
                {content}
              </Card>
            );
          })) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
      </Spin>
    </div>
  );
};

export default withRouter(AnswerAnalyse);
