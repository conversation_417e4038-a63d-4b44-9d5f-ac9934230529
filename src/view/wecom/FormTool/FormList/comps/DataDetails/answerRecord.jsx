/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/17 19:58
 * @LastEditTime: 2024/07/24 15:19
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/comps/DataDetails/answerRecord.jsx
 * @Description: '答题记录'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Card, Form, Input, Table, Tooltip, DatePicker, Image, Avatar, Space } from 'antd';
import { removeInputEmpty, unescapeFile, checkSuffix } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp, secondsToTime } from 'common/date';
import { qs2obj } from 'common/object';
import { saveAs } from 'file-saver';
import moment from 'moment';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const AnswerRecord = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [tableTrs, setTableTrs] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      fixed: 'left',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '客户名称',
      width: '220px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'left',
      fixed: 'left',
      render: (value, record, index) => {
        const content = <div style={{ display: 'flex' }}>
          <Avatar size={40} src={record.customerAvatar} />
          <div style={{ marginLeft: '6px', whiteSpace: 'normal', display: "flex", flexDirection: "column" }}>
            <span>{value}</span>
            <span>{record.customerNickName}</span>
          </div>
        </div>;
        return content;
      }
    },
    {
      title: '开始时间',
      width: '160px',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.startTime) - timeStamp(b.startTime)
    },
    {
      title: '结束时间',
      width: '160px',
      dataIndex: 'endTime',
      key: 'endTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.endTime) - timeStamp(b.endTime)
    },
    {
      title: '答题用时',
      width: '160px',
      dataIndex: 'duration',
      key: 'duration',
      align: 'center',
      render: (value, record, index) => (secondsToTime(value, 'HH小时mm分ss秒', true)),
      sorter: (a, b) => timeStamp(a.duration) - timeStamp(b.duration)
    },
    {
      title: '答题次数',
      width: '160px',
      dataIndex: 'commitCount',
      key: 'commitCount',
      align: 'center',
    },
    ...tableTrs,
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    fetchList();
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.submiTime) {
        formData.minTime = moment(formData.submiTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxTime = moment(formData.submiTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.submiTime;
      }
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
        id: id,
      };
      apiCall('/base/form/feedback', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res.value;
        const trArr = res.key.map((item, index) => ({
          title: `${item}`,
          width: '340px',
          dataIndex: `th${index + 1}`,
          key: `th${index + 1}`,
          // ellipsis: 'true',
          align: 'left',
          render: (value, record) => {
            let newOptionList = record.optionList[index];
            if (newOptionList instanceof Array) {
              let optionListJoin = newOptionList?.join('、')
              return (newOptionList.length > 0 && !newOptionList.some((atem, andex) => (atem.includes('http')))) ? <Tooltip placement="topLeft" title={optionListJoin}>{optionListJoin}</Tooltip>
                : <Space wrap>
                  {
                    newOptionList.map((atem, andex) => {
                      if (checkSuffix(atem) == 'IMAGE') {
                        return <FileHOC src={atem || 'error'}>
                          {(url) => (
                            <Image
                              key={andex}
                              width={60}
                              height={60}
                              src={url}
                              fallback="images/fallbackImg.png"
                              preview
                            />
                          )}
                        </FileHOC>;
                      } else if (checkSuffix(atem) == 'VIDEO') {
                        return <div key={andex}>
                          <FileHOC src={atem}>
                            {(url) => (
                              <video
                                style={{ maxWidth: '200px', maxHeight: '100px' }}
                                controls
                                src={url}
                              />
                            )}
                          </FileHOC>
                        </div>;
                      } else {
                        // return <p style={{ margin: "0" }}><a key={andex} onClick={() => {
                        //   saveAs(atem, unescapeFile(atem));
                        // }}>{unescapeFile(atem)}</a></p>
                        return <p key={andex} style={{ margin: "0" }}>{unescapeFile(atem)}</p>
                      }
                    })
                  }
                </Space>
            } else {
              return <Tooltip placement="topLeft" title={newOptionList}>{newOptionList}</Tooltip>;
            }
          }
        }));
        setTableTrs(trArr);
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleExport = () => {
    setLoading(true);
    const { shareTitle } = props.params.formInfo;
    const data = {
      id: id,
    };
    apiCall('/base/form/feedback/data/export', 'POST', data, null, { isExit: true, title: `${shareTitle}的答题记录.${moment().format('YYYY-MM-DD')}.xlsx` }).then((res) => {
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className='AnswerRecord-Container'>
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="customerName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="客户名称" allowClear />
          </FormItem>
          <FormItem name="submiTime" label="答题时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => { handleExport(); }}>导出excel</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default withRouter(AnswerRecord);
