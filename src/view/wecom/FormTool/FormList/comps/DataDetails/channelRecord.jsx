/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/18 10:54
 * @LastEditTime: 2023/05/18 11:05
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/comps/DataDetails/channelRecord.jsx
 * @Description: '渠道记录'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Card, Form, Input, Table, DatePicker, Tooltip } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { qs2obj } from 'common/object';
import moment from 'moment';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ChannelRecord = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      fixed: 'left',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '渠道名称',
      width: '160px',
      dataIndex: 'channelName',
      key: 'channelName',
      ellipsis: 'true',
      align: 'center',
      fixed: 'left',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value} </Tooltip>,
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime)
    },
    {
      title: '访问人数',
      width: '160px',
      dataIndex: 'visitorCount',
      key: 'visitorCount',
      align: 'center',
    },
    {
      title: '访问次数',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    fetchList();
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxCreateTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
        formId: id,
      };
      apiCall('/base/form/channel', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  return (
    <div className='ChannelRecord-Container'>
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="channelName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="渠道名称" allowClear />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default withRouter(ChannelRecord);
