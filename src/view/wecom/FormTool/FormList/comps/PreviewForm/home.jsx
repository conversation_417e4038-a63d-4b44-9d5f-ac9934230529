/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/15 16:55
 * @LastEditTime: 2025/07/02 14:19
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/comps/PreviewForm/home.jsx
 * @Description: '问卷预览'
 */

import React, { useRef, useEffect } from "react"
import { Button, Empty, Checkbox, Image } from "antd"
import Generator from "fr-generator"
import {
  defaultSettings,
  defaultCommonSettings,
  defaultWidgets,
} from "../../../x-render/setting"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"

const PreviewForm = (props) => {
  const generatorRef = useRef(null)
  const {
    imageUrl = "",
    commitActionType,
    questionData,
    jsonContent = null,
    msgList = [],
  } = props.params

  useEffect(() => {
    generatorRef?.current?.setValue(JSON.parse(jsonContent))
  }, [jsonContent])

  return (
    <>
      <WibotMobilePreview
        style={{ position: "relative" }}
        title={questionData.pageTitle ?? "网页标题"}
        footer={
          <>
            {(msgList.length && (
              <Checkbox>
                提交问卷前请仔细阅读并勾选协议与政策。勾选即代表您同意并接受
                {msgList.map((item, index) => (
                  <>
                    <a key={index} href="javascript:;">
                      {item.title}
                    </a>
                    {msgList.length > index + 1 ? "、" : null}
                  </>
                ))}
              </Checkbox>
            )) ||
              null}
            <Button type="primary" shape="round">
              提 交
            </Button>
          </>
        }
      >
        {imageUrl && (
          <FileHOC src={imageUrl}>
            {(url) => <Image preview={false} src={url} />}
          </FileHOC>
        )}
        <div className="body">
          <WibotEditorView html={questionData.description} />
          {jsonContent && (
            <Generator
              preview
              ref={generatorRef}
              settings={defaultSettings}
              globalSettings={{}}
              commonSettings={defaultCommonSettings}
              widgets={defaultWidgets}
              extraButtons={[false, false, false, false]}
            />
          )}
        </div>
      </WibotMobilePreview>
      <br />
      <WibotMobilePreview title="提交成功">
        {(commitActionType == "TEXT" && (
          <WibotEditorView html={questionData.thankWord} />
        )) || (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="跳转链接不支持预览"
          />
        )}
      </WibotMobilePreview>
    </>
  )
}

export default PreviewForm
