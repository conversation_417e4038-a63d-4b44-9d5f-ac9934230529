/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/10/25 10:00
 * @LastEditTime: 2023/06/28 15:42
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/comps/SaveTmpModal/home.jsx
 * @Description: '保存问卷模板-对话框'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, Input, message, Modal, Spin } from 'antd';
import { removeInputEmpty, editorIsEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import WibotEditor from "components/WibotEditor/home"

const FormItem = Form.Item;

const FormSaveTmp = (props) => {
  const formRef = useRef(null);
  const WibotEditorRef = useRef(null);
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible, id } = props.params;
    if (visible) {
      setVisible(true);
      if (id) {
        setId(id);
      }
    }
  }, [props.params]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        ...formData
      };
      apiCall(`/base/form/template/${id}`, 'POST', data).then((res) => {
        message.success('保存成功！');
        setVisible(false);
        setLoading(false);
        setConfirmLoading(false);
        setId(null);
        props.params?.onSubmit?.();
      })
        .catch((err) => {
          console.log(err);

        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className='FormSaveTmp-Container'
      visible={visible}
      title='保存为问卷模板'
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem label="模板标题" name="title" rules={[{ required: true, message: '请输入模板标题' }]} getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入模板标题（30字内）" maxLength={30} allowClear />
          </FormItem>

          <FormItem
            label="模板介绍"
            name="description"
            rules={[{ required: true, message: '请输入模板介绍' }]}
          >
            <WibotEditor
              ref={WibotEditorRef}
              toolbarConfig={{
                excludeKeys: [
                  'group-image',
                  'group-video',
                ]
              }}
              editorConfig={{
                placeholder: '请输入内容(建议100字内)...',
              }}
              onChangeHtml={(html) => {
                formRef.current.setFieldValue('description', editorIsEmpty(html) ? '' : html)
              }}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormSaveTmp;
