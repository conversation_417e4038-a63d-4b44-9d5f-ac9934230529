/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/10/24 13:54
 * @LastEditTime: 2024/07/24 15:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/data.jsx
 * @Description: '数据统计'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { Button, Card, Form, Input, Table, Tooltip, DatePicker, Tag, Image } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp, secondsToTime } from 'common/date';
import { qs2obj } from 'common/object';
import { QrCodeBase } from 'common/qrcode';
import { saveAs } from 'file-saver';
import moment from 'moment';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const FormData = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [tableTrs, setTableTrs] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      fixed: 'left',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '开始答题时间',
      width: '160px',
      dataIndex: 'startTime',
      key: 'startTime',
      align: 'center',
      fixed: 'left',
      sorter: (a, b) => timeStamp(a.startTime) - timeStamp(b.startTime)
    },
    {
      title: '结束答题时间',
      width: '160px',
      dataIndex: 'endTime',
      key: 'endTime',
      align: 'center',
      fixed: 'left',
      sorter: (a, b) => timeStamp(a.endTime) - timeStamp(b.endTime)
    },
    {
      title: '答题时长',
      width: '160px',
      dataIndex: 'duration',
      key: 'duration',
      align: 'center',
      fixed: 'left',
      render: (value, record, index) => (secondsToTime(value, 'HH小时mm分ss秒', true)),
      sorter: (a, b) => timeStamp(a.duration) - timeStamp(b.duration)
    },
    {
      title: '提交人',
      width: '200px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'center',
      fixed: 'left',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    ...tableTrs,
    // {
    //   title: '填写设备',
    //   width: '200px',
    //   dataIndex: 'describe',
    //   key: 'describe',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    // {
    //   title: '操作系统',
    //   width: '160px',
    //   dataIndex: 'number',
    //   key: 'number',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    // {
    //   title: '浏览器',
    //   width: '160px',
    //   dataIndex: 'friendState',
    //   key: 'friendState',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    // {
    //   title: '填写地区',
    //   width: '160px',
    //   dataIndex: 'friendState',
    //   key: 'friendState',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    // {
    //   title: 'IP',
    //   width: '160px',
    //   dataIndex: 'friendState',
    //   key: 'friendState',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
    }
  }, []);

  useEffect(() => {
    fetchList();
  }, [id]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.submiTime) {
        formData.minTime = moment(formData.submiTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.maxTime = moment(formData.submiTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.submiTime;
      }
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
        id: id,
      };
      apiCall('/base/form/feedback', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res.value;

        const trArr = res.key.map((item, index) => ({
          title: `${item}`,
          width: '160px',
          dataIndex: `th${index + 1}`,
          key: `th${index + 1}`,
          ellipsis: 'true',
          align: 'center',
          render: (value, record) => {
            let content = '';
            if (record.optionList[index] instanceof Array) {
              content = (record.optionList[index].length > 0 && !record.optionList[index].some((atem, andex) => (atem.includes('http')))) ? <Tooltip placement="topLeft" title={<div>
                {record.optionList[index]?.join('、')}
              </div>}>
                <div>
                  {record.optionList[index]?.join('、')}
                </div>
              </Tooltip>
                : record.optionList[index].map((atem, andex) => {
                  if (checkSuffix(atem) == 'IMAGE') {
                    return <FileHOC src={atem || 'error'}>
                      {(url) => (
                        <Image
                          key={andex}
                          width={60}
                          height={60}
                          src={url}
                          fallback="images/fallbackImg.png"
                          style={{ objectFit: 'cover' }}
                          preview
                        />
                      )}
                    </FileHOC>;
                  } else if (checkSuffix(atem) == 'VIDEO') {
                    return <div key={andex}>
                      <FileHOC src={atem}>
                        {(url) => (
                          <video
                            style={{ maxWidth: '200px', maxHeight: '100px' }}
                            controls
                            src={url}
                          />
                        )}
                      </FileHOC>
                    </div>;
                  } else {
                    return <a key={andex} onClick={() => {
                      saveAs(atem, unescapeFile(atem));
                    }}>{unescapeFile(atem)}</a>;
                  }
                });
            } else {
              content = <Tooltip placement="topLeft" title={record.optionList[index]}>{record.optionList[index]}</Tooltip>;
            }
            return content;
          }
        }));
        // const trArr = res.key.map((item, index) => ({
        //   title: `${index + 1}.${item}`,
        //   width: '200px',
        //   dataIndex: `th${index + 1}`,
        //   key: `th${index + 1}`,
        //   ellipsis: 'true',
        //   align: 'center',
        //   render: (value, record) => {
        //     let content = '';
        //     if (record.optionList[index] instanceof Array) {
        //       content = record.optionList[index].length > 0 && !record.optionList[index].some((atem, andex) => (atem.includes('http'))) ? <div>
        //         {
        //           record.optionList[index].map((atem, andex) => (<Tag style={{ wordBreak: 'break-all', whiteSpace: 'pre-wrap' }} key={andex}>{atem}</Tag>))
        //         }
        //       </div> : '';
        //     } else {
        //       content = record.optionList[index];
        //     }
        //     return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
        //   }
        // }));
        setTableTrs(trArr);
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 判断文件类型
  const checkSuffix = (url) => {
    const imageRegex = /\.(jpg|png|gif|bmp|jpeg|psd)$/;
    const videoRegex = /\.(avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv|mp4)$/;
    if (imageRegex.test(url.toLowerCase())) {
      return 'IMAGE';
    } else if (videoRegex.test(url.toLowerCase())) {
      return 'VIDEO';
    } else {
      return 'FILE';
    }
  };

  // 编码文件链接中的中文
  const unescapeFile = (url) => {
    const reg = /[\u4e00-\u9fa5]/g;
    if (url.match(reg)) {
      const texts = url.match(reg).join('');
      const index = url.indexOf(texts);
      return url.substring(index, url.length);
    } else {
      const index = url.lastIndexOf('/');
      return url.substring(index + 1, url.length);
    }
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  return (
    <div className='FormData-Container'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="customerName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="提交人" allowClear />
          </FormItem>
          <FormItem name="option" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="搜索回收文本" allowClear />
          </FormItem>
          <FormItem name="submiTime" label="提交时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => { props.history.go(-1); }}>返回</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
    </div>
  );
};

export default FormData;
