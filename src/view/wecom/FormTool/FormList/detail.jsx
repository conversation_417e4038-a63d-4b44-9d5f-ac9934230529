/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/05/16 10:33
 * @LastEditTime: 2025/07/02 17:34
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/detail.jsx
 * @Description: '问卷详情'
 */

import React, { useEffect, useState } from "react"
import FilterBar from "components/FilterBar/FilterBar"
import {
  Spin,
  Button,
  Card,
  Tooltip,
  Row,
  Col,
  Empty,
  Tabs,
  Image,
  Descriptions,
  Tag,
  Form,
  DatePicker,
  Select,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import SysDictLabel from "components/select/SysDictLabel"
import { QuestionCircleOutlined } from "@ant-design/icons"
import { qs2obj } from "common/object"
import { QrCodeBase } from "common/qrcode"
import { saveAs } from "file-saver"
import { Line } from "@ant-design/plots"
import { getDay } from "common/date"
import AnswerAnalyse from "./comps/DataDetails/answerAnalyse"
import AnswerRecord from "./comps/DataDetails/answerRecord"
import AccessRecord from "./comps/DataDetails/accessRecord"
import MassRecord from "./comps/DataDetails/massRecord"
import DateRecord from "./comps/DataDetails/dateRecord"
import ChannelRecord from "./comps/DataDetails/channelRecord"
import WibotCopyBtn from "components/WibotCopyBtn/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./detail.less"
import { FileHOC } from "components/FileHOC/FileHOC"

const { TabPane } = Tabs
const FormItem = Form.Item
const { RangePicker } = DatePicker

const FormDetail = (props) => {
  const [trendFormRef] = Form.useForm() // 趋势问卷
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [formInfo, setFormInfo] = useState(null)
  const [trendType, setTrendType] = useState("COMMIT_CUSTOMER_COUNT")
  const [trendData, setTrendData] = useState(null)
  const [overViewData, setOverViewData] = useState(null)
  const [detailType, setDetailType] = useState("1")

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
    }
  }, [])

  useEffect(() => {
    id && init()
  }, [id])

  useEffect(() => {
    getTrendData()
  }, [trendType])

  const init = async () => {
    getInfoData()
    getTrendData()
    getOverViewData()
  }

  // 获取问卷详情数据
  const getInfoData = async () => {
    setLoading(true)
    const data = {}
    await apiCall(`/base/form/${id}`, "GET", data)
      .then((res) => {
        setFormInfo(res)
        console.log(`获取问卷详情数据 [res]: `, res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取问卷数据趋势
  const getTrendData = async () => {
    setLoading(true)
    trendFormRef.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      const data = {
        formId: id,
        type: trendType,
        ...formData,
      }
      apiCall("/base/form/feedback/trend", "GET", data)
        .then((res) => {
          setTrendData(res?.list || [])
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  // 获取问卷数据总览
  const getOverViewData = async () => {
    setLoading(true)
    const data = {
      formId: id,
    }
    apiCall(`/base/form/feedback/overview`, "GET", data)
      .then((res) => {
        setOverViewData(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const DemoLine = (data) => {
    const config = {
      data,
      xField: "date",
      yField: "number",
      label: {},
      point: {
        size: 5,
        shape: "diamond",
        style: {
          fill: "white",
          stroke: "#5B8FF9",
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: "#000",
            fill: "red",
          },
        },
      },
      interactions: [
        {
          type: "marker-active",
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
              return `${value.slice(0, 4)}...`
            }
            return value
          },
        },
      },
      meta: {
        number: {
          alias: "总数",
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    }
    return <Line {...config} />
  }

  return (
    <div className="FormDetail-Container">
      <Spin spinning={loading}>
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12} style={{ marginBottom: "20px" }}>
            <Card
              title="问卷详情（基础信息）"
              extra={
                <Button
                  type="primary"
                  size="small"
                  onClick={() => handleGoBack()}
                >
                  返回
                </Button>
              }
            >
              {formInfo ? (
                <Row gutter={16}>
                  <Col xs={24} xl={18}>
                    <Descriptions
                      title={
                        <>
                          {formInfo.title}
                          <SysDictLabel
                            style={{ marginLeft: "6px" }}
                            dataset="FormTool_Form_State"
                            dictkey={formInfo.state}
                            color
                          />
                        </>
                      }
                      column={1}
                    >
                      {formInfo.createTime && (
                        <Descriptions.Item label="创建">
                          {formInfo.createEmployeeName} {formInfo.createTime}
                        </Descriptions.Item>
                      )}
                      {formInfo.modifyStateTime && (
                        <Descriptions.Item label="更新">
                          {formInfo.updateEmployeeName}{" "}
                          {formInfo.modifyStateTime}
                        </Descriptions.Item>
                      )}
                      {formInfo.stopTime && (
                        <Descriptions.Item label="停止">
                          {formInfo.stopEmployeeName} {formInfo.stopTime}
                        </Descriptions.Item>
                      )}
                      {formInfo.collectTime && (
                        <Descriptions.Item label="开始">
                          {formInfo.collectEmployeeName} {formInfo.collectTime}
                        </Descriptions.Item>
                      )}
                      {formInfo.channelNameList.length && (
                        <Descriptions.Item label="推广渠道">
                          {formInfo.channelNameList?.join("、")}
                        </Descriptions.Item>
                      )}
                      <Descriptions.Item label="题目显示方式">
                        {formInfo.showType == "ALL_QUESTION_ONE_PAGE"
                          ? "一页展示所有题目"
                          : "一页展示一题"}
                      </Descriptions.Item>
                    </Descriptions>
                  </Col>
                  <Col flex="auto">
                    <div className="codeBox">
                      <FileHOC src={formInfo.transitUrl}>
                        {(url) => (
                          <>
                            <Image
                              preview={false}
                              width={120}
                              src={QrCodeBase({ url: url })}
                            />
                            <div className="btn">
                              <a
                                onClick={() => {
                                  saveAs(QrCodeBase({ url: url }), "问卷二维码")
                                }}
                              >
                                下载二维码
                              </a>
                              <WibotCopyBtn text={url} title="复制链接" />
                            </div>
                          </>
                        )}
                      </FileHOC>
                    </div>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12} style={{ marginBottom: "20px" }}>
            <Card title="问卷介绍">
              {formInfo ? (
                <Descriptions column={1}>
                  <Descriptions.Item label="问卷介绍">
                    {" "}
                    <WibotEditorView html={formInfo.description} />
                  </Descriptions.Item>
                  <Descriptions.Item label="可见范围">
                    {formInfo.visibleScopeDepartmentNameList?.join("、")}
                  </Descriptions.Item>
                  <Descriptions.Item
                    label="客户标签"
                    contentStyle={{ display: "inline-block" }}
                  >
                    <Space wrap>
                      {formInfo.tagNameList?.map((item, index) => (
                        <Tag style={{ margin: "0" }} key={index}>
                          {item}
                        </Tag>
                      ))}
                    </Space>
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Card title="数据总览" className="data-screening">
          {overViewData ? (
            <Row>
              <Col flex="auto">
                <span className="num">{overViewData.visitorCount}</span>
                <Tooltip title="同一用户访问多次算一次">
                  <p className="tip">
                    累计访问人数
                    <QuestionCircleOutlined />
                  </p>
                </Tooltip>
                <p className="visit">
                  今日访问：{overViewData.visitorCountToday}
                </p>
              </Col>
              <Col flex="auto">
                <span className="num">{overViewData.visitCount}</span>
                <Tooltip title="同一用户访问多次算多次">
                  <p className="tip">
                    累计访问次数
                    <QuestionCircleOutlined />
                  </p>
                </Tooltip>
                <p className="visit">
                  今日访问：{overViewData.visitCountToday}
                </p>
              </Col>
              <Col flex="auto">
                <span className="num">{overViewData.commitCount}</span>
                <Tooltip title="同一用户答题多次算多次">
                  <p className="tip">
                    累计答题次数
                    <QuestionCircleOutlined />
                  </p>
                </Tooltip>
                <p className="visit">
                  今日答题：{overViewData.commitCountToday}
                </p>
              </Col>
              <Col flex="auto">
                <span className="num">{overViewData.commitCustomerCount}</span>
                <Tooltip title="同一用户答题多次算一次">
                  <p className="tip">
                    累计答题人数
                    <QuestionCircleOutlined />
                  </p>
                </Tooltip>
                <p className="visit">
                  今日答题：{overViewData.commitCustomerCountToday}
                </p>
              </Col>
              <Col flex="auto">
                <span className="num">{overViewData.sendEmployeeCount}</span>
                <Tooltip title="后台管理员创建的群发任务涉及的群发员工，实际群发的人数">
                  <p className="tip">
                    群发员工人数
                    <QuestionCircleOutlined />
                  </p>
                </Tooltip>
                <p className="visit">
                  今日群发：{overViewData.sendEmployeeCountToday}
                </p>
              </Col>
              {/* <Col span={4}>
                <span className='num'>{overViewData.sendCustomerCount}</span>
                <Tooltip title="后台管理员创建的群发任务的次数">
                  <p className='tip'>群发客户人数<QuestionCircleOutlined /></p>
                </Tooltip>
                <p className='visit'>今日群发：{overViewData.sendCustomerCountToday}</p>
              </Col> */}
            </Row>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Card>
        <Card title="数据趋势">
          <Tabs
            activeKey={trendType}
            destroyInactiveTabPane
            onChange={(type) => {
              setTrendType(type)
            }}
          >
            <TabPane tab="答题人数" key="COMMIT_CUSTOMER_COUNT" />
            <TabPane tab="答题次数" key="COMMIT_COUNT" />
            <TabPane tab="访问人数" key="VISITOR_COUNT" />
            <TabPane tab="访问次数" key="VISIT_COUNT" />
            <TabPane tab="群发员工人数" key="SEND_EMPLOYEE_COUNT" />
            {/* <TabPane tab="群发客户人数" key="SEND_CUSTOMER_COUNT" /> */}
          </Tabs>
          <FilterBar bodyStyle={{ padding: "unset" }}>
            <Form layout={"inline"} form={trendFormRef}>
              <FormItem
                label="统计时间"
                name="time"
                initialValue={[moment(getDay(-30)), moment(getDay(-1))]}
              >
                <RangePicker
                  allowClear={false}
                  format="YYYY-MM-DD"
                  onChange={(date, dateString) => {
                    trendFormRef.setFieldsValue({ quickTime: null })
                    getTrendData()
                  }}
                />
              </FormItem>
              <FormItem label="快捷时间" name="quickTime" initialValue={"-30"}>
                <Select
                  style={{ width: "200px" }}
                  options={[
                    {
                      label: "今天",
                      value: "0",
                    },
                    {
                      label: "昨天",
                      value: "-1",
                    },
                    {
                      label: "最近7天",
                      value: "-7",
                    },
                    {
                      label: "最近15天",
                      value: "-15",
                    },
                    {
                      label: "最近30天",
                      value: "-30",
                    },
                  ]}
                  onChange={(value) => {
                    let moments = []
                    moments = [
                      moment().add(value, "d"),
                      moment().add(value <= -1 ? -1 : 0, "d"),
                    ]
                    value &&
                      (trendFormRef.setFieldsValue({ time: moments }),
                      getTrendData())
                  }}
                />
              </FormItem>
            </Form>
          </FilterBar>
          {(trendData && DemoLine(trendData)) || (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Card>
        <Card title="数据明细">
          <Tabs
            activeKey={detailType}
            destroyInactiveTabPane
            onChange={(type) => {
              setDetailType(type)
            }}
          >
            <TabPane tab="答题分析" key="1">
              <AnswerAnalyse params={{ formInfo: formInfo }} />
            </TabPane>
            <TabPane tab="答题记录" key="2">
              <AnswerRecord params={{ formInfo: formInfo }} />
            </TabPane>
            <TabPane tab="访问记录" key="3">
              <AccessRecord params={{ formInfo: formInfo }} />
            </TabPane>
            <TabPane tab="群发记录" key="4">
              <MassRecord />
            </TabPane>
            <TabPane tab="日期统计" key="5">
              <DateRecord />
            </TabPane>
            <TabPane tab="推广渠道" key="6">
              <ChannelRecord />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  )
}

export default FormDetail
