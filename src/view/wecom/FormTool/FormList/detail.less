.FormDetail-Container {

  .ant-row {

    .ant-col {
      // margin-bottom: 20px;

      .ant-card {
        height: 100%;
      }

      .ant-descriptions {
        .ant-descriptions-header {
          margin: 0 0 6px;

          .ant-descriptions-title {
            display: flex;
            align-items: center;
          }
        }

        .ant-descriptions-view {
          .ant-descriptions-row {

            .ant-descriptions-item {
              padding: 0 0 6px;
            }
          }
        }
      }

      .codeBox {
        white-space: nowrap;
        text-align: center;

        .btn {
          display: flex;
          justify-content: space-around;
        }
      }
    }
  }

  .data-screening {
    .ant-col {
      text-align: center;

      .num {
        font-size: 40px;
        font-weight: bold;
      }

      .tip {
        font-size: 18px;
        color: #aaa;
        cursor: pointer;

        .anticon {
          margin-left: 6px;
        }
      }

      .visit {
        color: #aaa;
        font-size: 14px;
      }
    }
  }

  .ant-card {
    margin-bottom: 20px;
  }
}