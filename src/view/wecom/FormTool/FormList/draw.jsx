/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/10/24 09:34
 * @LastEditTime: 2023/06/19 09:39
 * @LastEditors: <PERSON>ei<PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/draw.jsx
 * @Description: '问卷绘制'
 * @document: 'https://xrender.fun/generator'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Spin, Card, message } from 'antd';
import Generator, { } from 'fr-generator';
import { defaultSettings, defaultGlobalSettings, defaultCommonSettings, defaultWidgets } from '../x-render/setting';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import './draw.less';

const FormDraw = (props) => {
  const generatorRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      getInfoData(id);
    }
  }, []);

  const getInfoData = (id) => {
    setLoading(true);
    const data = {};
    apiCall(`/base/form/${id}`, 'GET', data).then((res) => {
      const { jsonContent } = res;
      jsonContent && generatorRef?.current?.setValue(JSON.parse(jsonContent));
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className='FormDraw-Container'>
      <Spin spinning={loading}>
        <Card bordered={false}>
          <Generator ref={generatorRef} settings={defaultSettings} globalSettings={defaultGlobalSettings} commonSettings={defaultCommonSettings} widgets={defaultWidgets}
            extraButtons={[
              process.env.NODE_ENV == 'development' || false,
              process.env.NODE_ENV == 'development' || false,
              process.env.NODE_ENV == 'development' || false,
              process.env.NODE_ENV == 'development' || false,
              {
                type: 'primary',
                text: '保存',
                onClick: ((res) => {
                  // 获取配置项校验错误
                  const getErrorFields = generatorRef.current.getErrorFields();
                  if (getErrorFields && getErrorFields.length) {
                    message.warning('配置项校验错误');
                    return false
                  }
                  const jsonContent = generatorRef.current.getValue();
                  if (JSON.stringify(jsonContent.properties) == '{}') {
                    message.error('请先编辑题目');
                    return false;
                  }
                  setLoading(true);
                  const data = {
                    jsonContent: JSON.stringify(jsonContent),
                  };
                  apiCall(`/base/form/${id}`, 'put', data).then((res) => {
                    message.success('保存成功！');
                    props.history.push({
                      pathname: '/wecom/formList',
                    });
                  })
                    .catch((err) => {
                      console.log(err);
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                })
              },
              {
                type: 'default',
                text: '返回',
                onClick: (() => {
                  props.history.push({
                    pathname: '/wecom/formList',
                  });
                })
              }
            ]} />
        </Card>
      </Spin>
    </div>
  );
};

export default FormDraw;
