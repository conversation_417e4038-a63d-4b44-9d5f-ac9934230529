.FormDraw-Container {
  flex: 1;

  .ant-spin-nested-loading,
  .ant-spin-container,
  .ant-card,
  .ant-card-body {
    height: 100%;
  }

  .ant-card-body {
    padding: 0;

    .fr-generator-container {

      // 中间
      .mid-layout {
        .dnd-container {
          .fr-field {

            .fr-label {

              .fr-label-title {
                .fr-desc {
                  margin-top: 0;
                  font-size: 12px;
                  word-break: break-all;
                  color: #888;
                }
              }
            }

            .fr-content {

              .ant-input,
              .ant-input-affix-wrapper,
              .ant-radio-group,
              .ant-checkbox-group,
              .ant-select,
              .ant-picker,
              .fr-upload-mod,
              .ant-upload-picture-card-wrapper {
                pointer-events: none !important;
              }
            }
          }
        }

        // 隐藏id
        .blue {
          display: none;
        }

        // 必填*
        .fr-label-required {
          margin: 1px 4px 0 0;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
        }
      }

      // 右边
      .right-layout {
        .fr-container {
          .w-100 {
            flex-direction: column;
          }
        }
      }

      // 布局铺满
      .fr-content {
        width: 100%;
      }

      // 间距高度
      .field-block {
        min-height: 24px;
      }

      // 错误消息
      .error-message {
        color: #ff4347;
      }
    }
  }
}