/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/20 16:56
 * @LastEditTime: 2025/05/14 14:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/form.jsx
 * @Description: '问卷表单'
 */

import React, { useState, useEffect, useRef } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  message,
  Upload,
  Radio,
  Switch,
  TreeSelect,
  Space,
} from "antd"
import { removeInputEmpty, normFile, editorIsEmpty } from "common/regular"
import { compressImage, base64ToFile, beforeUpload } from "common/image"
import { apiCall } from "common/utils"
import { qs2obj } from "common/object"
import { clearCache } from "react-router-cache-route"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import PreviewForm from "./comps/PreviewForm/home"
import WibotEditor from "components/WibotEditor/home"
import MaterialListForm from "components/MaterialListForm/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import "./form.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const FormForm = (props) => {
  const [formForm] = Form.useForm()
  const DesWibotEditorRef = useRef(null)
  const ActWibotEditorRef = useRef(null)
  const onRefMaterialListForm = useRef(null)
  const [id, setId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [questionData, setQuestionData] = useState({})
  const [imageUrl, setImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [commitActionType, setCommitActionType] = useState(null)
  const [shareImageUrl, setShareImageUrl] = useState("")
  const [uploadShareLoading, setUploadShareLoading] = useState(false)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [jsonContent, setJsonContent] = useState(null)
  const [msgList, setMsgList] = useState([])

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    getCustomerTagTreeData()
    if (id) {
      setId(id)
      getInfoData(id)
    }
  }, [])

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        // setLabelTreeData([...tagTreeData])
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getInfoData = (id) => {
    setLoading(true)
    const data = {}
    apiCall(`/base/form/${id}`, "GET", data)
      .then((res) => {
        const {
          title,
          description,
          pageTitle,
          commitActionContent,
          commitActionType,
          jsonContent,
          image,
          shareImage,
          clauseResourceList,
        } = res
        setQuestionData({
          title: title,
          description: description,
          pageTitle: pageTitle,
          thankWord: commitActionContent,
        })
        setCommitActionType(commitActionType)
        setJsonContent(jsonContent)
        setImageUrl(image)
        setShareImageUrl(shareImage)
        formForm.setFieldsValue({ ...res })
        let timer = setTimeout(() => {
          DesWibotEditorRef.current.setHtml(description)
          ActWibotEditorRef.current.setHtml(commitActionContent)
          onRefMaterialListForm.current.getInitMsgList([...clauseResourceList])
          clearTimeout(timer)
        }, 300)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === "uploading") {
      type == "share" ? setUploadShareLoading(true) : setUploadLoading(true)
      return
    }
  }

  const customRequest = (config, type = null) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        if (type == "share") {
          setShareImageUrl(fileUrl)
          formForm.setFieldsValue({
            shareImage: fileUrl,
          })
        } else {
          setImageUrl(fileUrl)
          formForm.setFieldsValue({
            image: fileUrl,
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
        setUploadShareLoading(false)
      })
  }

  const handleResetUpload = (e, type = null) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    if (type == "share") {
      setShareImageUrl("")
      formForm.setFieldsValue({
        shareImage: "",
      })
    } else {
      setImageUrl("")
      formForm.setFieldsValue({
        image: "",
      })
    }
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      setLoading(true)
      type == "collect" && (formData.state = "COLLECTING")
      const data = {
        ...formData,
        clauseResourceIdList: msgList.map((item) => item.id), // 条款资源id列表
      }
      const apiUrl = id ? `/base/form/${id}` : "/base/form"
      const apiMethod = id ? "put" : "post"
      apiCall(apiUrl, apiMethod, data)
        .then((res) => {
          message.success(id ? "编辑成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          if (type == "draw") {
            props.history.push({
              pathname: "/wecom/formList/draw",
              search: `?id=${res.id}`,
            })
          } else {
            props.history.go(-1)
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="FormForm-Container">
      <Spin spinning={loading}>
        <Card
          bordered={false}
          title={id ? "编辑问卷" : "创建问卷"}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <Form {...layout} form={formForm}>
                {/* <FormItem
                  label="问卷标题"
                  name="title"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: '请输入问卷标题' }]}
                >
                  <Input placeholder="请输入问卷标题(30字内)" maxLength={30} allowClear onChange={(e) => {
                    const data = questionData;
                    data.title = e.target.value;
                    setQuestionData({ ...data });
                  }} />
                </FormItem> */}

                <FormItem
                  label="问卷介绍"
                  name="description"
                  rules={[{ required: false, message: "请输入问卷介绍" }]}
                >
                  <WibotEditor
                    ref={DesWibotEditorRef}
                    toolbarConfig={{
                      excludeKeys: ["group-image", "group-video"],
                    }}
                    editorConfig={{
                      placeholder: "请输入内容(建议100字内)...",
                    }}
                    onChangeHtml={(html) => {
                      formForm.setFieldValue("description", html)
                      const data = questionData
                      data.description = html
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  label="问卷封面"
                  name="image"
                  getValueFromEvent={normFile}
                  rules={[{ required: false, message: "请上传图片" }]}
                  extra="建议宽度750px，大小限制为2M，最多上传1张"
                >
                  <Upload
                    name="file"
                    customRequest={customRequest}
                    listType="picture-card"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={onChangeUpload}
                  >
                    <WibotUploadImage
                      imageUrl={imageUrl}
                      loading={uploadLoading}
                      onClose={handleResetUpload}
                    />
                  </Upload>
                </FormItem>

                <FormItem
                  label="网页标题"
                  name="pageTitle"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入网页标题" }]}
                >
                  <Input
                    placeholder="请输入网页标题(10字内)"
                    maxLength={10}
                    allowClear
                    onChange={(e) => {
                      const data = questionData
                      data.pageTitle = e.target.value
                      setQuestionData({ ...data })
                    }}
                  />
                </FormItem>

                {/* <FormItem label="客户标签" name="customerTagIdList"> */}
                {/*   <TreeSelect */}
                {/*     treeData={labelTreeData} */}
                {/*     treeCheckable */}
                {/*     allowClear */}
                {/*     showArrow */}
                {/*     showSearch */}
                {/*     treeNodeFilterProp="title" */}
                {/*     maxTagCount="responsive" */}
                {/*     showCheckedStrategy={SHOW_PARENT} */}
                {/*     placeholder="客户标签" */}
                {/*   /> */}
                {/* </FormItem> */}
                <CustomTagSelect
                  label="客户标签"
                  name="tagNameList"
                  placeholder="客户标签"
                  useForm={formForm}
                  existTagNameList={formForm.getFieldValue("tagNameList")}
                  labelTreeData={labelTreeData}
                />

                <FormItem label="可见范围" name="visibleScopeDepartmentId">
                  <ETypeTransferModal title="可见范围" onlyDepartment />
                </FormItem>

                <FormItem
                  label="重复提交"
                  name="repeatFlag"
                  valuePropName="checked"
                  extra="开启后，客户可多次提交问卷的内容，填写后内容将默认覆盖，多次提交算1次，数据以最后一次为准。"
                >
                  <Switch />
                </FormItem>

                <FormItem
                  label="题目显示方式"
                  name="showType"
                  initialValue={"ALL_QUESTION_ONE_PAGE"}
                >
                  <Radio.Group>
                    <Radio value={"ALL_QUESTION_ONE_PAGE"}>
                      一页展示所有题目
                    </Radio>
                    <Radio value={"ONE_QUESTION_PER_PAGE"}>一页展示一题</Radio>
                  </Radio.Group>
                </FormItem>

                <FormItem label="分享设置" required>
                  <div className="link-card" style={{ width: "100%" }}>
                    <FormItem
                      name="shareTitle"
                      getValueFromEvent={(e) => removeInputEmpty(e)}
                      rules={[
                        { required: true, message: "请输入分享链接标题" },
                      ]}
                    >
                      <Input
                        placeholder="请输入分享链接标题(30字)"
                        maxLength={30}
                        allowClear
                      />
                    </FormItem>
                    <Row
                      justify="space-between"
                      gutter={16}
                      style={{ marginTop: "10px" }}
                    >
                      <Col span={15}>
                        <FormItem name="shareDescription">
                          <TextArea
                            placeholder="请输入分享链接描述（30字内）"
                            allowClear
                            showCount
                            maxLength={30}
                            autoSize={{ minRows: 2, maxRows: 7 }}
                            className="textArea-mid"
                          />
                        </FormItem>
                      </Col>
                      <Col span={9}>
                        <FormItem
                          name="shareImage"
                          getValueFromEvent={normFile}
                          rules={[{ required: true, message: "请选择图片" }]}
                        >
                          <Upload
                            name="file"
                            customRequest={(file) => {
                              customRequest(file, "share")
                            }}
                            listType="picture-card"
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                            onChange={(info) => {
                              onChangeUpload(info, "share")
                            }}
                          >
                            <WibotUploadImage
                              imageUrl={shareImageUrl}
                              loading={uploadShareLoading}
                              beforeLoadingText="上传1:1图"
                              onClose={(e) => handleResetUpload(e, "share")}
                            />
                          </Upload>
                        </FormItem>
                      </Col>
                    </Row>
                  </div>
                </FormItem>

                <FormItem label="协议与政策" name="msgList">
                  <MaterialListForm
                    params={{
                      formRef: formForm,
                      menuList: ["material"],
                      isNickname: false,
                      needScriptFlag: false,
                      materialAmount: msgList.length,
                      materialTabList: ["pageArticle"],
                      tabType: "pageArticle",
                    }}
                    // 监听回调
                    callback={(params) => {
                      setMsgList(params.data)
                    }}
                    ref={onRefMaterialListForm}
                  />
                </FormItem>

                <FormItem label="问卷结束" required>
                  <FormItem
                    style={{ margin: "unset" }}
                    name="commitActionType"
                    rules={[{ required: true, message: "请选择问卷结束" }]}
                  >
                    <Radio.Group
                      onChange={(e) => {
                        formForm.setFieldValue("commitActionContent", "")
                        setCommitActionType(e.target.value)
                      }}
                    >
                      <Radio value="URL"> 跳转链接 </Radio>
                      <Radio value="TEXT"> 图文内容 </Radio>
                    </Radio.Group>
                  </FormItem>

                  {commitActionType == "URL" && (
                    <FormItem
                      name="commitActionContent"
                      rules={[
                        {
                          required: true,
                          message: "请输入带有【http://或https://】链接",
                          type: "url",
                        },
                      ]}
                    >
                      <Input
                        placeholder="请输入带有【http://或https://】链接"
                        allowClear
                      />
                    </FormItem>
                  )}

                  {commitActionType == "TEXT" && (
                    <FormItem
                      name="commitActionContent"
                      rules={[{ required: true, message: "请输入图文内容" }]}
                    >
                      <WibotEditor
                        ref={ActWibotEditorRef}
                        toolbarConfig={{
                          excludeKeys: ["group-video"],
                        }}
                        onChangeHtml={(html) => {
                          formForm.setFieldValue(
                            "commitActionContent",
                            editorIsEmpty(html) ? "" : html
                          )
                          const data = questionData
                          data.thankWord = html
                          setQuestionData({ ...data })
                        }}
                      />
                    </FormItem>
                  )}
                </FormItem>
              </Form>
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Space size={40}>
                  <Button onClick={() => handleGoBack()}>取消</Button>
                  <Button type="primary" onClick={() => onSubmit("save")}>
                    保存
                  </Button>
                  <Button type="primary" onClick={() => onSubmit("draw")}>
                    保存并绘制问卷
                  </Button>
                  {jsonContent && (
                    <Button type="primary" onClick={() => onSubmit("collect")}>
                      保存并收集
                    </Button>
                  )}
                </Space>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <PreviewForm
                params={{
                  imageUrl,
                  commitActionType,
                  questionData,
                  jsonContent,
                  msgList,
                }}
              />
            </Col>
          </Row>
        </Card>
      </Spin>
    </div>
  )
}

export default FormForm
