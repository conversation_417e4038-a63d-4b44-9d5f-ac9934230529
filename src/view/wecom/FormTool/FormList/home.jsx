/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/20 16:56
 * @LastEditTime: 2025/03/20 11:30
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/home.jsx
 * @Description: '问卷工具-问卷列表'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  message,
  Image,
  Select,
  TreeSelect,
  DatePicker,
  Typography,
} from 'antd';
import SysDictSelect from 'components/select/SysDictSelect';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import SaveTmpModal from './comps/SaveTmpModal/home';
import SysDictLabel from 'components/select/SysDictLabel';
import OperateModal from 'components/Modal/OperateModal/index';
import { QrCodeBase } from 'common/qrcode';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import LinkCard from 'components/LinkCard/home';
import ExtendModal from './comps/ExtendModal/home';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import {usePageCacheLifeCycle} from "common/hooks";
import { FileHOC } from 'components/FileHOC/FileHOC';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;

const FormList = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [saveTmpParams, setSaveTmpParams] = useState({ visible: false });
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '问卷',
      width: '250px',
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      render: (value, record, index) => (
        <div style={{ textAlign: 'left' }}>
          <LinkCard
            isLink={false}
            data={{
              title: record.shareTitle || record.title,
              description: record.shareDescription,
              image: record.shareImage,
            }}
          />
        </div>
      ),
    },
    {
      title: '问卷二维码',
      width: '200px',
      dataIndex: 'transitUrl',
      key: 'transitUrl',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const image = QrCodeBase({ url: value });
        return <FileHOC src={image}>
          {(url) => (
            <Image width={60} src={url} preview={url}></Image>
          )}
        </FileHOC>
      },
    },
    {
      title: '问卷链接',
      width: '200px',
      dataIndex: 'transitUrl',
      key: 'transitUrl',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          <Paragraph ellipsis={{ rows: 1 }} copyable>
            {value}
          </Paragraph>
        </Tooltip>
      ),
    },
    {
      title: '可见范围',
      width: '160px',
      dataIndex: 'visibleScopeDepartmentNameList',
      key: 'visibleScopeDepartmentNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '数据',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
      render: (value, record, index) =>
        record.subType != 'NORMAL' && (
          <div style={{ textAlign: "left" }}>
            群发员工次数：{record.sendCount}
            <br />
            {/* 群发客户人数：{record.sendCustomerCount}
            <br /> */}
            访问次数：{record.visitCount}
            <br />
            访问人数：{record.visitorCount}
            <br />
            答题次数：{record.commitCount}
            <br />
            答题人数：{record.commitCustomerCount}
          </div>
        ),
    },
    {
      title: '问卷状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <SysDictLabel dataset="FormTool_Form_State" dictkey={value} color />
      ),
    },
    {
      title: '问卷状态时间',
      width: '160px',
      dataIndex: 'modifyStateTime',
      key: 'modifyStateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.modifyStateTime) - timeStamp(b.modifyStateTime),
    },
    {
      title: '获客渠道',
      width: '160px',
      dataIndex: 'channelNameList',
      key: 'channelNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleCopy(record), name: "复制" },
          { onClick: () => handleDetail(record), name: "详情" },
          // { onClick: () => handleData(record), name: "统计" },
          { onClick: () => handleSaveTmp(record), name: "保存为模板" },
        ];
        if (record.state == 'STOP_COLLECT') {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" });
          opts.push({ onClick: () => handleDraw(record), name: "题目编辑" });
          opts.push({ onClick: () => handleRules(record), name: "选项高级设置" });
          opts.push({ onClick: () => handleCollect(record, 'COLLECTING'), name: "开始收集" });
        }
        if (record.state == 'COLLECTING') {
          if (record.jsonContent) {
            opts.push({ onClick: () => handleExtend(record), name: "推广" });
          }
          opts.push({ onClick: () => handleCollect(record, 'STOP_COLLECT'), name: "停止收集" });
        }
        if (record.commitCount > 0) {
          opts.push({ onClick: () => handleExport(record), name: "导出数据" });
        }
        if (record.deleteAble) {
          opts.push({ onClick: () => handleDel(record), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const [createMode, setCreateMode] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [extendParams, setExtendParams] = useState({ visible: false });

  useEffect(() => {
    getChannelOptions();
    getCustomerTagTreeData();
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  // 获取获客渠道
  const getChannelOptions = () => {
    setLoading(true);
    apiCall('/activity/channel/option', 'GET')
      .then((res) => {
        setCreateMode([...res]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true);
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([...tagTreeData]);
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.modifyStateTime) {
        formData.startModifyStateTime = moment(
          formData.modifyStateTime[0]._d
        ).format('YYYY-MM-DD 00:00:00');
        formData.endModifyStateTime = moment(
          formData.modifyStateTime[1]._d
        ).format('YYYY-MM-DD 23:59:59');
        delete formData.modifyStateTime;
      }
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.updateTime;
      }
      // formData.customerTagIdList = formData.customerTagIdList?.join(',') || null;
      // formData.visibleScopeDepartmentId = formData.visibleScopeDepartmentId?.join(',') || null;
      // formData.createDeptIdList = formData.createDeptIdList?.join(',') || null;
      // formData.updateDeptIdList = formData.updateDeptIdList?.join(',') || null;
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: 'FORM',
        ...formData,
      };
      apiCall('/base/form/page', 'POST', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    props.history.push({
      pathname: '/wecom/formList/form',
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/formList/form',
      search: `?id=${id}`,
    });
  };

  const handleDraw = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/formList/draw',
      search: `?id=${id}`,
    });
  };

  const handleRules = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/formList/rules',
      search: `?id=${id}`,
    });
  };

  const handleData = (record) => {
    const { id } = record; 6;
    props.history.push({
      pathname: '/wecom/formList/data',
      search: `?id=${id}`,
    });
  };

  const handleDetail = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/formList/detail',
      search: `?id=${id}`,
    });
  };

  const handleCollect = (record, state) => {
    const { shareTitle, id } = record;
    setOperateParams({
      visible: true,
      title: state == 'COLLECTING' ? '开始收集确认' : '停止收集确认',
      content: `您将为【${shareTitle}】进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          state: state,
        };
        apiCall(`/base/form/${id}`, 'PUT', data)
          .then((res) => {
            message.success('操作成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleCopy = (record) => {
    const { shareTitle, id } = record;
    setOperateParams({
      visible: true,
      title: '复制确认',
      content: `您将为【${shareTitle}】进行操作，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/base/form/copy/${id}`, 'post')
          .then((res) => {
            message.success('操作成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleSaveTmp = (record) => {
    const { id } = record;
    setSaveTmpParams({
      visible: true,
      id: id,
      onCancel: () => {
        setSaveTmpParams({ visible: false });
      },
      onSubmit: () => {
        setSaveTmpParams({ visible: false });
      },
    });
  };

  const handleExport = (record) => {
    setLoading(true);
    const { id, shareTitle } = record;
    const data = {
      id: id,
    };
    apiCall('/base/form/feedback/data/export', 'POST', data, null, {
      isExit: true,
      title: `${shareTitle}的统计数据.${moment().format('YYYY-MM-DD')}.xlsx`,
    })
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDel = (record) => {
    const { shareTitle, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除的问卷为【${shareTitle}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/base/form/${id}`, 'DELETE')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const handleExtend = (record) => {
    const { id } = record;
    setExtendParams({
      visible: true,
      formId: id,
      title: '推广渠道',
      onCancel: () => {
        setExtendParams({ visible: false });
        fetchList();
      },
    });
  };

  return (
    <div className="FormList-Container">
      <Spin spinning={loading}>
        <FilterBar>
          <Form layout={'inline'} ref={formRef}>
            <FormItem
              name="keyWord"
              getValueFromEvent={(e) => removeInputEmpty(e)}
            >
              <Input placeholder="问卷标题、描述" allowClear />
            </FormItem>
            <FormItem name="state">
              <SysDictSelect
                placeholder="问卷状态"
                dataset="FormTool_Form_State"
              />
            </FormItem>
            <FormItem name="channelId">
              <Select
                placeholder="获客渠道"
                fieldNames={{ label: 'name', value: 'id' }}
                options={createMode}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.name ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
            {/* <FormItem name="customerTagIdList"> */}
            {/*   <TreeSelect */}
            {/*     treeData={labelTreeData} */}
            {/*     treeCheckable */}
            {/*     allowClear */}
            {/*     showArrow */}
            {/*     showSearch */}
            {/*     treeNodeFilterProp="title" */}
            {/*     maxTagCount="responsive" */}
            {/*     showCheckedStrategy={SHOW_PARENT} */}
            {/*     placeholder="客户标签" */}
            {/*   /> */}
            {/* </FormItem> */}
            <CustomTagSelect
              creatable
              name="tagNameList"
              placeholder="客户标签"
              useForm={formRef?.current}
              labelTreeData={labelTreeData}
            />
            <FormItem
              name="visibleScopeDepartmentId"
              style={{
                minWidth: 'unset',
                maxWidth: '200px',
                marginRight: '0px',
              }}
            >
              <ETypeTransferModal title="可见范围" onlyDepartment />
            </FormItem>
            <FormItem
              name="createDeptIdList"
              style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
            >
              <ETypeTransferModal title="创建人" />
            </FormItem>
            <FormItem
              name="updateDeptIdList"
              style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
            >
              <ETypeTransferModal title="更新人" />
            </FormItem>
            <FormItem name="modifyStateTime" label="问卷状态时间">
              <RangePicker />
            </FormItem>
            <FormItem name="createTime" label="创建时间">
              <RangePicker />
            </FormItem>
            <FormItem name="updateTime" label="更新时间">
              <RangePicker />
            </FormItem>
          </Form>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
            <div>
              <Button
                type="primary"
                onClick={() => {
                  handleAdd();
                }}
              >
                新增
              </Button>
            </div>
          </div>
        </FilterBar>
        <Card bordered={false}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
      <OperateModal params={operateParams} />
      <SaveTmpModal params={saveTmpParams} />
      <ExtendModal params={extendParams} />
    </div>
  );
};

export default FormList;
