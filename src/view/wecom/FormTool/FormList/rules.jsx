/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/06/14 13:55
 * @LastEditTime: 2023/06/20 17:59
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/FormList/rules.jsx
 * @Description: '问卷规则'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Spin, Col, Row, Card, Button, Empty, Tooltip, Form, Checkbox, Space, Select, message } from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import { ObjResetArr } from 'common/array';
import MaterialListForm from "components/MaterialListForm/home";
import './rules.less';

const FormItem = Form.Item;

const FormRules = (props) => {
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [infoData, setInfoData] = useState(null);
  const [selectOption, setSelectOption] = useState(null);
  const [labelTreeData, setLabelTreeData] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    setId(id);
  }, []);

  useEffect(() => {
    if (id) {
      getCustomerTagTreeData();
      getInfoData();
    }
  }, [id]);

  useEffect(() => {
    if (infoData) {
      console.log(infoData, 'infoData-infoData');
      const { optionList } = infoData;
      // 默认选取第一个题目第一个选项
      optionList.length && setSelectOption(`${optionList[0].topicName}_${optionList[0].optionName}`)
    }
  }, [infoData]);

  useEffect(() => {
    if (selectOption) {
      const { optionList } = infoData;
      // 更新当前选项已选择的素材
      for (let index = 0; index < optionList.length; index++) {
        let item = optionList[index];
        if (`${item.topicName}_${item.optionName}` == selectOption) {
          const idList = formForm.getFieldValue(['rules', index, 'resourceIdList']);
          console.log(idList, 'idList-idList');
          if (!idList || !idList.length) {
            onRefMaterialListForm?.current?.getInitMsgList([]);
            return false
          };
          setLoading(true);
          const data = {
            paged: false,
            idList: idList?.join(",")
          }
          apiCall(`/info/infoResource`, 'GET', data).then((res) => {
            const { records } = res;
            onRefMaterialListForm?.current?.getInitMsgList(records);
          })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              setLoading(false);
            });
          return false
        };
      }
    }
  }, [selectOption]);

  // 获取客户标签树
  const getCustomerTagTreeData = async () => {
    setLoading(true);
    const data = {
      type: 'customer',
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data)
      .then((res) => {
        setLabelTreeData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = async () => {
    setLoading(true);
    await apiCall(`/base/form/${id}`, 'GET').then((res) => {
      const { jsonContent, extraSetting } = res;
      const properties = ObjResetArr(JSON.parse(jsonContent).properties, 'name');
      console.log(properties, 'properties-properties');
      const topicList = properties?.map((item, index) => ({ ...item, index: index + 1 }))?.filter(item => item['vc-type'] == 'radio' || item['vc-type'] == 'picker' || item['vc-type'] == 'checkbox');
      console.log(topicList, 'topicList-topicList');
      const optionList = []
      topicList?.forEach((item, index) => {
        item.enumNames.forEach((atem, andex) => {
          optionList.push({
            topicTitle: item.title,
            topicName: item.name,
            optionName: atem,
            optionValue: item.enum[andex],
            topicList: properties.filter((btem, bndex) => item.index < (bndex + 1)).map(btem => ({
              label: btem.title,
              value: btem.name
            })),
            ['vc-type']: item['vc-type'],
            widget: item.widget,
          })
        })
      })
      console.log(optionList, 'optionList-optionList');
      setInfoData({
        ...res,
        properties: properties,
        topicList: topicList,
        optionList: optionList,
      })
      console.log(extraSetting, 'extraSetting-extraSetting');
      // extraSetting.length && formForm.setFieldValue('rules', extraSetting)
      if (extraSetting && extraSetting.length) {
        extraSetting.forEach((item) => {
          // 换了位置也可以查询到当前索引
          let findIndex = optionList?.findIndex((atem) => `${item.topicName}_${item.optionName}` == `${atem.topicName}_${atem.optionName}`)
          console.log(findIndex, 'findIndex-findIndex');
          findIndex >= 0 && (formForm.setFieldValue(['rules', findIndex], item))
        })
      }
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleTopicOption = (params) => {
    const { topicName, optionName } = params;
    setSelectOption(`${topicName}_${optionName}`)
  }

  const onSubmit = () => {
    // console.log(infoData, 'onSubmit-onSubmit');
    formForm.validateFields().then((formData) => {
      setLoading(true);
      console.log(formData, 'onSubmit-onSubmit');
      const { rules } = formData;
      const data = {
        extraSetting: rules,
      };
      apiCall(`/base/form/${id}`, 'put', data).then((res) => {
        message.success('保存成功！');
        props.history.push({
          pathname: '/wecom/formList',
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    })
  }

  return (
    <div className='FormRules-Container'>
      <Spin spinning={loading}>
        <Row>
          <Col span={7}>
            <Card title={infoData?.title} extra={<Button type="primary" onClick={() => props.history.go(-1)}>返回</Button>}>
              {
                infoData?.topicList.length ?
                  <ul className='topic-list'>
                    {
                      infoData?.topicList?.map((item, index) => <li key={index} className='item' >
                        <Tooltip placement="topLeft" title={item.index + '.' + item.title}><p className='item-title'>{item.index}.{item.title}</p></Tooltip>
                        {item?.enumNames?.map((atem, andex) => <Tooltip key={andex} placement="topLeft" title={atem}><p className={selectOption == `${item.name}_${atem}` ? 'item-option selected' : 'item-option'} onClick={() => handleTopicOption({ topicName: item.name, optionName: atem })}>{item.index}.{andex + 1}.{atem}</p></Tooltip>)}
                      </li>)
                    }
                  </ul> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </Card>
          </Col>

          <Col span={17}>
            <Card>
              {
                infoData?.optionList.length ?
                  <>
                    <Form form={formForm} className='option-form'>
                      {infoData?.optionList?.map((item, index) =>
                        <div key={index} style={{ display: selectOption == `${item.topicName}_${item.optionName}` ? 'block' : 'none' }}
                        >
                          <h3>当前题目：{item.topicTitle}</h3>
                          <h4>当前选项：{item.optionName}</h4>
                          <Form.Item style={{ display: 'none' }} name={['rules', index, 'topicName']} initialValue={item.topicName}></Form.Item>

                          <Form.Item style={{ display: 'none' }} name={['rules', index, 'optionName']} initialValue={item.optionName}></Form.Item>

                          <Form.Item style={{ display: 'none' }} name={['rules', index, 'optionValue']} initialValue={item.optionValue}></Form.Item>

                          <Form.Item style={{ display: 'none' }} name={['rules', index, 'topicAllList']} initialValue={item.topicList.map((item) => item.value)}></Form.Item>

                          <Form.Item name={['rules', index, 'topicList']} label="题目显示设置" extra="控制后续题目的显示设置，仅有单选题才可以设置。" initialValue={item.topicList.map((item) => item.value)}>
                            <Checkbox.Group disabled={item['vc-type'] == 'checkbox' ? true : false} options={item.topicList} />
                          </Form.Item>

                          <FormItem
                            label="题目关联标签"
                            name={['rules', index, 'tagIdList']}
                            extra="当客户提交问卷时选择当前选项，则自动打上这些标签。"
                          >
                            <Select
                              mode="multiple"
                              allowClear
                              showArrow
                              maxTagCount="responsive"
                              style={{ width: 200 }}
                              placeholder="请选择关联标签"
                              options={labelTreeData}
                              fieldNames={{ label: 'title', value: 'key', options: 'children' }}
                              showSearch
                              filterOption={(input, option) =>
                                (option?.title ?? '').toLowerCase().includes(input.toLowerCase())
                              }
                            />
                          </FormItem>
                          <FormItem style={{ display: 'none' }} name={['rules', index, 'resourceIdList']}></FormItem>
                        </div>
                      )}
                      <FormItem label="题目关联资源">
                        <MaterialListForm
                          params={{
                            formRef: formForm,
                            isNickname: false,
                            menuList: ['material'],
                            materialTabList: ['Article', 'pageArticle', 'Product'],
                            extra: "当客户提交问卷时选择当前选项，则在提交页的“为您推荐”显示这些资源。",
                          }}
                          // 监听回调
                          callback={(params) => {
                            const { data } = params;
                            console.log(params, 'params-params');
                            // 获取当前选项索引
                            const findIndex = infoData?.optionList?.findIndex(item => selectOption == `${item.topicName}_${item.optionName}`)
                            // 保存当前关联资源id
                            formForm.setFieldValue(['rules', findIndex, 'resourceIdList'], data.map(item => item.id))
                          }}
                          ref={onRefMaterialListForm}
                        />
                      </FormItem>
                    </Form>
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                      <Space size={40}>
                        <Button onClick={() => props.history.go(-1)}>取消</Button>
                        <Button type="primary" onClick={() => onSubmit()}>保存</Button>
                      </Space>
                    </div>
                  </> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </Card>
          </Col>
        </Row>
      </Spin>
    </div >
  );
};

export default FormRules;
