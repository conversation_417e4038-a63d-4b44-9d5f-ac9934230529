.FormRules-Container {
  .ant-row {
    .ant-col {
      .ant-card {
        min-height: 85vh;
        max-height: 85vh;
        overflow-y: scroll;

        &::-webkit-scrollbar {
          width: 8px;
          height: 1px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          box-shadow: inset 0 0 5px rgba(97, 184, 179, 0.1);
          background: #7c7c7c;
        }

        & ::-webkit-scrollbar-track {
          box-shadow: inset 0 0 5px rgba(87, 175, 187, 0.1);
          border-radius: 10px;
          background: #ededed;
        }

        .ant-card-head {
          .ant-page-header {
            padding: 0;
          }
        }

        .ant-card-body {
          .topic-list {
            list-style: none;
            margin: 0;
            padding: 0;

            .item {
              margin-bottom: 10px;

              p {
                margin: 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .item-option {
                cursor: pointer;
                text-indent: 1em;
              }
            }
          }

          .option-form {

            .ant-form-item {
              .ant-checkbox-group {
                display: flex;
                flex-direction: column;
              }

              .MaterialListForm {
                .btn-flex {
                  .ant-btn {
                    margin: 0;
                  }
                }
              }
            }


          }
        }

      }
    }
  }
}