/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/10/26 15:04
 * @LastEditTime: 2022/11/01 16:48
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\FormTool\TemplateList\comps\PreviewModal.jsx
 * @Description: '预览问卷-对话框'
 */

import React, { useEffect, useState, useRef } from 'react';
import { Modal, Spin } from 'antd';
import { apiCall } from 'common/utils';
import Generator, { } from 'fr-generator';
import { defaultSettings, defaultGlobalSettings, defaultCommonSettings, defaultWidgets } from '../../x-render/setting';
import './PreviewModal.less';

const PreviewModal = (props) => {
  const generatorRef = useRef(null);
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const { visible, id } = props.params;
    if (visible) {
      setVisible(true);
      if (id) {
        setId(id);
        getInfoData(id);
      }
    }
  }, [props]);

  const getInfoData = (id) => {
    setLoading(true);
    const data = {};
    apiCall(`/base/form/${id}`, 'GET', data).then((res) => {
      const { jsonContent } = res;
      console.log(JSON.parse(jsonContent));
      generatorRef?.current?.setValue(JSON.parse(jsonContent));
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setId(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      className="Template-PreviewModal-Container"
      title='预览'
      destroyOnClose
      footer={null}
      onCancel={onCancel}
    >
      <Spin spinning={loading}>
        <Generator preview ref={generatorRef} settings={defaultSettings} globalSettings={{}} commonSettings={defaultCommonSettings}
          widgets={defaultWidgets} extraButtons={[false, false, false, false]} />
      </Spin>
    </Modal>
  );
};

export default PreviewModal;
