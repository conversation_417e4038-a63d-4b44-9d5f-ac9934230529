.Template-PreviewModal-Container {
  .ant-modal-body {
    max-height: 600px;
    overflow-y: scroll;
  }

  .fr-generator-container {

    .left-layout,
    .right-layout {
      display: none !important;
    }

    .mid-layout {
      border: unset;
      padding: 0;
      margin-top: -54px;

      .dnd-container {
        .fr-field {

          .fr-label {

            .fr-label-title {
              .fr-desc {
                margin-top: 0;
                font-size: 12px;
                word-break: break-all;
                color: #888;
              }
            }
          }

          .fr-content {

            .ant-input,
            .ant-input-affix-wrapper,
            .ant-radio-group,
            .ant-checkbox-group,
            .ant-select,
            .ant-picker,
            .fr-upload-mod,
            .ant-upload-picture-card-wrapper {
              pointer-events: none !important;
            }
          }
        }
      }

      // 必填*
      .fr-label-required {
        margin: 1px 4px 0 0;
        color: #f5222d;
        font-size: 14px;
        font-family: SimSun, sans-serif;
      }
    }

    // 布局铺满
    .fr-content {
      width: 100%;
    }

    // 间距高度
    .field-block {
      min-height: 24px;
    }
  }
}