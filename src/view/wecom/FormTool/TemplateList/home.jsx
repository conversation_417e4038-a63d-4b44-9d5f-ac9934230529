/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/10/24 14:32
 * @LastEditTime: 2025/07/02 15:00
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/FormTool/TemplateList/home.jsx
 * @Description: '问卷模板'
 */

import { Button, Card, Form, Input, Table, Tooltip, message } from "antd"
import { timeStamp } from "common/date"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import FilterBar from "components/FilterBar/FilterBar"
import ListOperation from "components/ListOperation/home"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"
import SysDictSelect from "components/select/SysDictSelect"
import WibotEditorView from "components/WibotEditorView/home"
import { useEffect, useRef, useState } from "react"
import PreviewModal from "./comps/PreviewModal"

const FormItem = Form.Item

const TemplateList = (props) => {
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "模板标题",
      width: "200px",
      dataIndex: "title",
      key: "title",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "模板介绍",
      width: "200px",
      dataIndex: "description",
      key: "description",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        const content = <WibotEditorView html={value} />
        return (
          <Tooltip placement="topLeft" title={content}>
            {/* 需要一个真实的dom元素承载 */}
            <span style={{ textAlign: "left" }}>{content}</span>
          </Tooltip>
        )
      },
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "templateState",
      key: "templateState",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="FormTool_Template_State" dictkey={value} color />
      ),
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [{ onClick: () => handlePreview(record), name: "预览" }]
        if (record.templateState == "ON") {
          opts.push({ onClick: () => handleAddForm(record), name: "创建问卷" })
          opts.push({
            onClick: () => handleModifyState(record, "OFF"),
            name: "下架",
          })
        } else {
          opts.push({
            onClick: () => handleModifyState(record, "ON"),
            name: "上架",
          })
          opts.push({ onClick: () => handleDel(record), name: "删除" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    fetchList()
  }, [])

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      const { pagination } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: "TEMPLATE",
        ...formData,
      }
      apiCall("/base/form", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  const handlePreview = (record) => {
    const { id } = record
    setPreviewParams({
      visible: true,
      id: id,
      onCancel: () => {
        setPreviewParams({ visible: false })
      },
    })
  }

  const handleAddForm = (record) => {
    const { title, id } = record
    setOperateParams({
      visible: true,
      title: "创建问卷确认",
      content: `您将创建问卷的模板为【${title}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/base/form/by_template/${id}`, "POST")
          .then((res) => {
            message.success("创建成功！")
            props.history.push({
              pathname: "/wecom/formList/draw",
              search: `?id=${res.id}`,
            })
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleModifyState = (record, state) => {
    const { title, id } = record
    setOperateParams({
      visible: true,
      title: state == "ON" ? "上架确认" : "下架确认",
      content: `您将为【${title}】进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          templateState: state,
        }
        apiCall(`/base/form/${id}`, "PUT", data)
          .then((res) => {
            message.success("操作成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleDel = (record) => {
    const { title, id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除的模板为【${title}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/base/form/${id}`, "DELETE")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  return (
    <div className="TemplateList-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="title" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="模板标题/介绍" allowClear />
          </FormItem>
          <FormItem name="templateState">
            <SysDictSelect
              placeholder="状态"
              dataset="FormTool_Template_State"
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <PreviewModal params={previewParams} />
    </div>
  )
}

export default TemplateList
