/**
 * @description: 配置js
 * @return {*}
 * @Author: Janaeiw
 * @Date: 2022/10/24 11:08
 * 官网地址: 'https://1.xrender.fun/generator'
 * @document: 'https://github.com/alibaba/x-render/blob/1.x/tools/schema-generator/src/settings/index.js'
 */

import React from 'react';
import { Upload, Input, message, Radio, Checkbox, InputNumber } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import { removeInputXss } from 'common/regular';

// @document: 'https://1.xrender.fun/form-render/api/schema#propsbuttons'
window.otherOptinoCallback = ({ value, onChange, schema }) => {
  const isOtherOption = value.some((item) => item.label == '其他');
  if (isOtherOption) {
    message.warning('添加失败，已存在“其他”选项');
    return false;
  }
  onChange([...value, { label: '其他', value: 'd' }]);
};

// 自定义组件配置，方便可扩展
export const defaultWidgets = {
  uploadButton: (props) => (
    <Upload name='file' listType='picture-card' showUploadList={false}>
      <div>
        <CameraOutlined style={{ fontSize: '30px', color: '#dcdee0' }} />
      </div>
    </Upload>
  ),
  otherRadio: (props) => {
    const { schema } = props;
    const options = schema.enumNames?.map((item, index) => ({
      label: item,
      value: schema.enum[index],
    }));
    return (
      <Radio.Group>
        {options.map((item, index) => (
          <Radio key={index} value={item.value}>
            {item.label}{' '}
            {item.label == '其他' && <Input style={{ width: '80px' }} placeholder='请输入' />}
          </Radio>
        ))}
      </Radio.Group>
    );
  },
  otherCheckboxes: (props) => {
    const { schema } = props;
    const options = schema.enumNames?.map((item, index) => ({
      label: item,
      value: schema.enum[index],
    }));
    return (
      <Checkbox.Group>
        {options.map((item, index) => (
          <Checkbox key={index} value={item.value}>
            {item.label}{' '}
            {item.label == '其他' && <Input style={{ width: '80px' }} placeholder='请输入' />}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  },
  minInputNumber: (props) => {
    const { addons, value } = props;
    const { dataPath, formData, setValue } = addons;
    return (
      <InputNumber
        style={{ width: '100%' }}
        value={value}
        min={1}
        max={formData.enumList?.length}
        formatter={(value) => {
          if (typeof value === 'string') {
            return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : '';
          } else if (typeof value === 'number') {
            return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : '';
          } else {
            return '';
          }
        }}
        onChange={(value) => {
          setValue(dataPath, value);
          value > formData.max && setValue('max', value);
        }}
      />
    );
  },
  maxInputNumber: (props) => {
    const { addons, value } = props;
    const { dataPath, formData, setValue } = addons;
    return (
      <InputNumber
        style={{ width: '100%' }}
        value={value}
        min={formData.min}
        max={formData.enumList?.length}
        formatter={(value) => {
          if (typeof value === 'string') {
            return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : '';
          } else if (typeof value === 'number') {
            return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : '';
          } else {
            return '';
          }
        }}
        onChange={(value) => {
          setValue(dataPath, value);
        }}
      />
    );
  },
  xssInput: (props) => {
    const { addons, value } = props;
    const { dataPath, setValue } = addons;
    return (
      <Input
        style={{ width: '100%' }}
        value={value}
        disabled={value == '其他'}
        onChange={(e) => {
          const value = e.target.value;
          const filteredValue = removeInputXss(value);
          setValue(dataPath, filteredValue);
        }}
      />
    );
  },
};

// 只需写配置，方便可扩展
export const baseCommonSettings = {
  type: {
    title: '类型',
    type: 'string',
    hidden: '{{true}}',
  },
  widget: {
    title: '组件',
    type: 'string',
    hidden: '{{true}}',
  },
  format: {
    title: '格式',
    type: 'string',
    hidden: '{{true}}',
  },
};

export const defaultCommonSettings = {
  $id: {
    title: 'ID',
    description: '字段名称/英文',
    type: 'string',
    widget: 'idInput',
    require: true,
    hidden: true,
    rules: [
      {
        pattern: '^#/.+$',
        message: 'ID 必填',
      },
    ],
  },
  title: {
    title: '标题',
    type: 'string',
    widget: 'htmlInput',
  },
  description: {
    title: '说明',
    type: 'string',
  },
  // default: {
  //   title: '默认值',
  //   type: 'string',
  // },
  required: {
    title: '必填',
    type: 'boolean',
  },
  placeholder: {
    title: '占位符',
    type: 'string',
  },
};

// widget 用于指定 schema 右侧配置对应的 setting
export const elements = [
  {
    text: '输入框',
    name: 'input',
    schema: {
      title: '输入框',
      type: 'string',
      'vc-type': 'input',
    },
    setting: {
      props: {
        title: '选项',
        type: 'object',
        labelWidth: 80,
        properties: {
          allowClear: {
            title: '是否带清除按钮',
            description: '填写内容后才会出现x哦',
            type: 'boolean',
          },
        },
      },
    },
  },
  {
    text: '文本框',
    name: 'textarea',
    schema: {
      title: '文本框',
      type: 'string',
      widget: 'textarea',
      'vc-type': 'input-textarea',
    },
    setting: {
      props: {
        title: '选项',
        type: 'object',
        labelWidth: 80,
        properties: {
          allowClear: {
            title: '是否带清除按钮',
            description: '填写内容后才会出现x哦',
            type: 'boolean',
          },
        },
      },
    },
  },
  {
    text: '点击单选',
    name: 'radio',
    schema: {
      title: '单选',
      type: 'string',
      enum: ['a', 'b', 'c'],
      enumNames: ['早', '中', '晚'],
      widget: 'radio',
      'vc-type': 'radio',
    },
    setting: {
      enumList: {
        title: '选项',
        type: 'array',
        widget: 'simpleList',
        className: 'frg-options-list flex-column',
        items: {
          type: 'object',
          properties: {
            value: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '编号',
            },
            label: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '内容',
              // @document: 'https://1.xrender.fun/form-render/demos/index1#%E5%88%97%E8%A1%A8%E8%81%94%E5%8A%A8'
              disabled: "{{rootValue.label == '其他'}}",
            },
          },
        },
        props: {
          hideMove: true,
          hideCopy: true,
          buttons: [
            {
              html: '其他',
              callback: 'otherOptinoCallback',
            },
          ],
        },
      },
      placeholder: {},
    },
  },
  {
    text: '点击多选',
    name: 'checkboxes',
    schema: {
      title: '多选',
      type: 'array',
      widget: 'checkboxes',
      'vc-type': 'checkbox',
      items: {
        type: 'string',
      },
      enum: ['A', 'B', 'C', 'D'],
      enumNames: ['杭州', '武汉', '湖州', '贵阳'],
    },
    setting: {
      min: {
        title: '选项最小选择',
        type: 'number',
        widget: 'minInputNumber',
      },
      max: {
        title: '选项最多选择',
        type: 'number',
        widget: 'maxInputNumber',
      },
      enumList: {
        title: '选项',
        type: 'array',
        widget: 'simpleList',
        className: 'frg-options-list flex-column',
        items: {
          type: 'object',
          properties: {
            value: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '编号',
            },
            label: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '内容',
              disabled: "{{rootValue.label == '其他'}}",
            },
          },
        },
        props: {
          hideMove: true,
          hideCopy: true,
          buttons: [
            {
              html: '其他',
              callback: 'otherOptinoCallback',
            },
          ],
        },
      },
      placeholder: {},
    },
  },
  {
    text: '下拉单选',
    name: 'select',
    schema: {
      title: '单选',
      type: 'string',
      enum: ['a', 'b', 'c'],
      enumNames: ['早', '中', '晚'],
      widget: 'select',
      'vc-type': 'picker',
    },
    setting: {
      enumList: {
        title: '选项',
        type: 'array',
        widget: 'simpleList',
        className: 'frg-options-list flex-column',
        items: {
          type: 'object',
          properties: {
            value: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '编号',
            },
            label: {
              title: '',
              type: 'string',
              widget: 'xssInput',
              className: 'frg-options-input',
              props: {},
              placeholder: '内容',
              disabled: "{{rootValue.label == '其他'}}",
            },
          },
        },
        props: {
          hideMove: true,
          hideCopy: true,
          buttons: [
            {
              html: '其他',
              callback: 'otherOptinoCallback',
            },
          ],
        },
      },
    },
  },
  {
    text: '日期选择',
    name: 'date',
    schema: {
      title: '日期选择',
      type: 'string',
      format: 'date',
      'vc-type': 'datetimePicker',
    },
    setting: {
      format: {
        title: '格式',
        type: 'string',
        enum: ['dateTime', 'date', 'time'],
        enumNames: ['日期时间', '日期', '时间'],
      },
    },
  },
  {
    text: '图片/文件',
    name: 'upload',
    schema: {
      title: '图片/文件',
      type: 'string',
      widget: 'uploadButton',
      'vc-type': 'uploader',
    },
    setting: {
      placeholder: {},
    },
  },
  {
    text: '手机号码',
    name: 'tel',
    schema: {
      title: '手机号码',
      type: 'string',
      'vc-type': 'input-tel',
    },
    setting: {},
  },
  {
    text: '姓名',
    name: 'name',
    schema: {
      title: '姓名',
      type: 'string',
      'vc-type': 'input-name',
    },
    setting: {},
  },
];

export const defaultSettings = [
  {
    title: '基础组件',
    widgets: elements,
    show: true,
    useCommon: true, // TODO: 是否将common
  },
];

export const defaultGlobalSettings = {
  type: 'object',
  properties: {
    column: {
      title: '整体布局',
      type: 'number',
      enum: [1, 2, 3],
      enumNames: ['一行一列', '一行二列', '一行三列'],
      props: {
        placeholder: '默认一行一列',
      },
      hidden: true,
    },
    labelWidth: {
      title: '标签宽度',
      type: 'number',
      widget: 'slider',
      max: 300,
      default: 120,
      props: {
        hideNumber: true,
      },
      hidden: true,
    },
    displayType: {
      title: '标签展示模式',
      type: 'string',
      default: 'column',
      enum: [
        // 'row',
        'column',
      ],
      enumNames: [
        // '同行',
        '单独一行',
      ],
      widget: 'radio',
    },
  },
};
