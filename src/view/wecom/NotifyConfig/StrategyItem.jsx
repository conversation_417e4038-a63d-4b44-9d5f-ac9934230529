import { But<PERSON>, Card, Checkbox, DatePicker, Form, InputNumber, message, Switch } from "antd"
import { useForm } from "antd/es/form/Form"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import React, { useEffect } from "react"
import { apiCall } from "common/utils"

const { RangePicker } = DatePicker
const StrategyItem = () => {
  const [strategyForm] = useForm()

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = () => {
    apiCall("globalSetting/notify_strategy_config", "GET").then(res => {
      strategyForm.setFieldsValue({
        itemList: [...res.itemList].map(item => {
          switch (item.type) {
            case "AGENT_HANDLE_LIMIT":
              item.title = "坐席单人处理量抵达上限"
              break
          }
          return item
        })
      })
    })
  }

  const handleSubmit = () => {
    strategyForm.validateFields().then(values => {
      apiCall("globalSetting/notify_strategy_config", "POST", { itemList: values.itemList }).then(res => {
        message.success("保存成功！")
      })
    })
  }
  return <Form form={strategyForm}>
    <Form.List name="itemList">
      {fields => {
        return fields.map(field => {
          return (<React.Fragment key={field.key}>
              <Card key={field.name} style={{ marginBottom: 16, borderRadius: 8 }}
                    title={strategyForm.getFieldValue(["itemList", field.name, "title"])}
                    extra={<Button type="primary" onClick={handleSubmit}>保存</Button>}>
                <Form.Item
                  name={[field.name, "enable"]}
                  label="策略状态"
                  valuePropName="checked"
                  getValueProps={(value) => ({ checked: !!value })}
                >
                  <Switch />
                </Form.Item>
                {/* 根据当前表单项的enable状态来控制显示 */}
                <Form.Item noStyle
                           shouldUpdate={(prevValues, curValues) => prevValues.itemList?.[field.name]?.enable !== curValues.itemList?.[field.name]?.enable}>
                  {({ getFieldValue }) => getFieldValue(["itemList", field.name, "enable"]) ? (<React.Fragment key="enabled-fields">
                      <Form.Item label="刷新频率" name={[field.name, "refreshFrequency"]}
                                 rules={[{ required: true, message: "请输入刷新频率" }]} key="refreshFrequency">
                        <InputNumber min={5} addonAfter={"分钟"} />
                      </Form.Item>
                      <Form.Item label="通知用户" name={[field.name, "employeeIdList"]}
                                 rules={[{ required: true, message: "请选择通知用户" }]} key="employeeIdList">
                        <ETypeTransferModal title="通知用户" onlyEmployee multiple />
                      </Form.Item>
                      <Form.Item label="通知渠道" name={[field.name, "channels"]}
                                 rules={[{ required: true, message: "请选择通知渠道" }]} key="channels">
                        <Checkbox.Group>
                          <Checkbox value="INTERNAL">工作台站内信</Checkbox>
                        </Checkbox.Group>
                      </Form.Item>
                    </React.Fragment>) : null}
                </Form.Item>
              </Card>
            </React.Fragment>)
        })
      }}
    </Form.List>
  </Form>
}

export default StrategyItem
