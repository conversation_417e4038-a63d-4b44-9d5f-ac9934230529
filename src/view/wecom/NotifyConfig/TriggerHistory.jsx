import { <PERSON><PERSON>, Card, DatePicker, Descriptions, Form, Modal, Select, Table, Tag } from "antd"
import { useForm } from "antd/es/form/Form"
import React, { useEffect, useState } from "react"
import FilterBar from "components/FilterBar/FilterBar"
import { apiCall } from "common/utils"
import moment from "moment/moment"
import ListOperation from "components/ListOperation/home"

const { RangePicker } = DatePicker
const TriggerHistory = (item) => {
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      // sorter: (a, b) => a.id - b.id
    },
    {
      title: "触发时间",
      dataIndex: "createTime",
      key: "createTime",
      align: "center"
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      align: "center",
      render(value, record, index) {
        let text = ''
        switch (value) {
          case "AGENT_HANDLE_LIMIT":
            text = "坐席单人处理量抵达上限"
            break;
          default:
            text = "其他"
            break;
        }
        return text
      }

    },
    {
      title: "内容",
      dataIndex: "content",
      key: "content",
      align: "center"
    },
    {
      title: "通知用户",
      dataIndex: "employeeNameList",
      key: "employeeNameList",
      align: "center",
      render(value, record, index) {
        return value.map((item, i) => {
          return <Tag key={i}>{item}</Tag>
        })
      }
    },
    {
      title: "通知渠道",
      dataIndex: "channels",
      key: "channels",
      align: "center",
      render(value, record, index) {
        return value.map((item, i) => {
          switch (item) {
            case "INTERNAL":
              return "工作台站内信"
              break;
            default:
              return "其他"
              break;
          }
        })
      }
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "center",
      render(value, record, index) {
        return <a onClick={() => handleDetail(record)}>详情</a>
      }
    },
  ]
  const [filterForm] = useForm()
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRow, setCurrentRow] = useState({});

  const handleDetail = (record) => {
    setIsModalOpen(true)
    switch (record.type) {
      case "AGENT_HANDLE_LIMIT":
        record.typeStr = "坐席单人处理量抵达上限"
       break
    }
    setCurrentRow(record)
  }
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleQuery = () => {
    fetchList()
  }
  const handleReset = () => {
    filterForm.resetFields()
    fetchList()
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  useEffect(() => {
    fetchList()
  }, [])
  const fetchList = async (params = {}) => {
    const { pagination, query } = params;
    setLoading(true);
    filterForm.validateFields().then(formData => {
      let minCreateTime = null
      let maxCreateTime= null
      if (formData.createTime) {
        minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      apiCall("/notifyMessage", "GET", {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: formData.type,
        minCreateTime,
        maxCreateTime,
      }).then(res => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      }).finally(() => {
        setLoading(false);
      })
    })

  };

  return <>
    <FilterBar>
      <Form layout={"inline"} form={filterForm}>
        <Form.Item name="type">
          <Select
            allowClear
            placeholder="类型"
          >
            <Select.Option value={""}>全部</Select.Option>
            <Select.Option value={"AGENT_HANDLE_LIMIT"}>坐席单人处理量抵达上限</Select.Option>
          </Select>
        </Form.Item>


        <Form.Item name="createTime" label="触发时间">
          <RangePicker
            showTime={{ format: "HH:mm" }}
            format="YYYY-MM-DD HH:mm"
          />
        </Form.Item>


      </Form>
      <div className="flex flex-space-between">
        <div>
          <Button type="primary" onClick={() => handleQuery()}>
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
      </div>
    </FilterBar>
    <Card bordered={false}>
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        scroll={{ x: 1300 }}
        pagination={paginations}
        onChange={onChangeTable}
      />
    </Card>
    <Modal title="详情" open={isModalOpen} onOk={handleOk} onCancel={handleOk} footer={[
      <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
        确定
      </Button>
    ]}>
      <Descriptions title="" column={1} labelStyle={{ width: "80px" }}>
        <Descriptions.Item label="触发时间">{currentRow.createTime}</Descriptions.Item>
        <Descriptions.Item label="类   型">{currentRow.typeStr}</Descriptions.Item>
        <Descriptions.Item label="通知用户">{currentRow.employeeNameList?.join(',')}</Descriptions.Item>
        <Descriptions.Item label="发送内容">{currentRow.content}</Descriptions.Item>
      </Descriptions>
    </Modal>
  </>
}

export default TriggerHistory
