import { Card, Tabs } from "antd"
import React from "react"
import TriggerHistory from "@wecom/NotifyConfig/TriggerHistory"
import StrategyItem from "@wecom/NotifyConfig/StrategyItem"

const Notify = (props) => {
  const items = [
    { label: "通知策略管理", key: "1", children: <StrategyItem /> },
    { label: "通知触发历史", key: "2", children: <TriggerHistory /> },
  ]

  return (<Card bordered={false}>
    <Tabs items={items} />
  </Card>)
}

export default Notify
