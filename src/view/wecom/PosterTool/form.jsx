/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/20 16:26
 * @LastEditTime: 2024/11/14 17:21
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/PosterTool/form.jsx
 * @Description: '海报工具-表单编辑'
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin, Form, Input, Card, Button,
  Select, TreeSelect, message, DatePicker, Space
} from 'antd';
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import { removeInputEmpty } from 'common/regular';
import { disabledDate, disabledTime } from 'common/date';
import { sceneRecursionTreeData } from 'common/tree';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import SysDictSelect from 'components/select/SysDictSelect';
import { clearCache } from 'react-router-cache-route';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import './form.less';

const FormItem = Form.Item;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
const PosterToolForm = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [typeOptions, setTypeOptions] = useState([]);
  const [treeSelectId, setTreeSelectId] = useState(null);
  const [status, setStatus] = useState(null);
  const [productIds, setProductIds] = useState([]);
  const [posterType, setPosterType] = useState('FIXATION');
  const [fastPosterId, setFastPosterId] = useState(null);
  const [fastPosterParams, setFastPosterParams] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    getInfoTypeOptions();
    getProductIds();
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ id });
  };

  // 获取场景类型
  const getInfoTypeOptions = () => {
    apiCall('/info/scene/tree', 'GET').then((res) => {
      const treeData = sceneRecursionTreeData(res);
      setTypeOptions(treeData);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  // 关联产品
  const getProductIds = () => {
    apiCall('/info/infoResource/getProductIds', 'GET').then((res) => {
      setProductIds(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    await apiCall(`/info/posterTemplate/${id}`, 'GET').then((res) => {
      const { type, fastPosterId, sceneParentName, sceneName, startTime, endTime, status, copyWriterList, fastPosterParams } = res;
      setStatus(status);
      setPosterType(type);
      setFastPosterId(fastPosterId);

      fastPosterParams?.forEach((item) => {
        // 去除海报参数的预设值
        item.content = '';
      });
      setFastPosterParams(fastPosterParams);

      // 清除文案中的无效参数
      let cleanCopyWriterList = copyWriterList.map((copyWriter) => {
        let params = [];
        fastPosterParams.forEach((fpp) => {
          let param = copyWriter.params.find((cwp) => cwp.paramsId === fpp.paramsId);
          params.push({ paramsId: fpp.paramsId, content: param?.content || '' });
        });
        return { name: copyWriter.name, params: params };
      });

      formForm.setFieldsValue({
        ...res,
        sceneId: sceneParentName + '-' + sceneName,
        time: startTime && endTime ? [moment(startTime, 'YYYY-MM-DD HH:mm'), moment(endTime, 'YYYY-MM-DD HH:mm')] : '',
        copyWriterList: fastPosterId ? cleanCopyWriterList : null
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    console.log(value, node, extra);
    setTreeSelectId(value);
    formForm.setFieldsValue({
      sceneId: node.parentName + '-' + node.title
    });
  };

  // 海报类别改变
  const onChangeType = (value) => {
    setPosterType(value);
  };

  const handleAddItem = () => {
    let copyWriterField = formForm.getFieldValue('copyWriterList');
    copyWriterField.push({
      name: '',
      params: fastPosterParams.map((item) => ({ paramsId: item.paramsId, content: item.content }))
    });

    formForm.setFieldsValue({
      copyWriterList: copyWriterField
    });
  };

  const handleRemoveItem = (index) => {
    let copyWriterField = formForm.getFieldValue('copyWriterList');
    copyWriterField.splice(index, 1);
    formForm.setFieldsValue({
      copyWriterList: copyWriterField,
    });
  };

  const onSubmit = (params) => {
    const { value, isDraw } = params;
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format('YYYY-MM-DD HH:mm');
        formData.endTime = moment(formData.time[1]._d).format('YYYY-MM-DD HH:mm');
        delete formData.time;
      }
      formData.sceneId = treeSelectId || null;
      formData.copyWriterList = formData.copyWriterList || [];
      setLoading(true);
      const data = {
        id: id,
        status: id ? status : value,
        needFastPosterId: isDraw && true,
        ...formData,
      };
      const apiUrl = id ? `/info/posterTemplate/${id}` : '/info/posterTemplate';
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '修改成功！' : '新增成功！');
        clearCache();// 清空路由缓存
        isDraw ? props.history.push({
          pathname: '/wecom/posterTool/postIframe',
          search: `?id=${res.fastPosterId}&isAdd=true`
        }) : props.history.push('/wecom/posterTool');
      })
        .catch((err) => {
          console.log(err);

        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='PosterToolForm'>
      <Spin spinning={loading}>
        <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} title={id ? '配置海报' : '创建海报'} bordered={false} bodyStyle={{ display: 'none' }}></Card>
        <br />
        <Card>
          <Form {...layout} form={formForm}>
            <FormItem label="海报名称" name="name" getValueFromEvent={(e) => removeInputEmpty(e)} rules={[{ required: true, message: '请输入海报名称' }]}>
              <Input placeholder="请输入海报名称" allowClear />
            </FormItem>
            <FormItem name="introduction" label="海报简介" rules={
              [{ required: true, message: '请输入海报简介（100字）', }]}
            >
              <TextArea placeholder="请输入简介（100字）" allowClear showCount maxLength={100} autoSize={{ minRows: 2, maxRows: 7 }}
                className='textArea-mid' />
            </FormItem>
            <FormItem name="sceneId" label="场景类型" rules={[{ required: true, message: '请选择场景类型' }]}>
              <TreeSelect
                allowClear
                treeData={typeOptions}
                treeDefaultExpandAll
                placeholder="请选择场景类型"
                onSelect={handleTreeSelect}
              />
            </FormItem>
            <FormItem name="type" label="海报类别" rules={[{ required: true, message: '请选择海报类别' }]} extra={
              (!id && posterType == 'TEMPLATE') && '请绘制海报后再返回编辑文案'
            }>
              <SysDictSelect placeholder="海报类别" disabled={id} dataset="POSTER_TEMPLATE_TYPE" onChange={onChangeType} />
            </FormItem>
            <FormItem name="productId" label="关联产品" >
              <Select
                options={productIds}
                allowClear
                placeholder="请选择关联产品"
                fieldNames={{ label: "name", value: "id" }}
                filterOption={(input, option) =>
                  (option?.name ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
            <FormItem name="visibleScopeDepartmentId" label="可见范围" rules={[{ required: true, message: '请选择可见范围' }]}>
              <ETypeTransferModal title="可见范围" onlyDepartment />
            </FormItem>
            <FormItem name="time" label="上下架时间" rules={[{ required: true, message: '请选择上下架时间' }]}>
              <RangePicker
                disabledDate={disabledDate}
                disabledTime={disabledTime}
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                inputReadOnly
              />
            </FormItem>
            {
              (id && posterType == 'TEMPLATE') && fastPosterId && <Form.List name='copyWriterList'>
                {(fields, { add, remove }, { errors }) => (
                  <>
                    <FormItem label="海报文案">
                      <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddItem()}>添加文案</Button>
                    </FormItem>
                    {formForm.getFieldValue('copyWriterList')?.map((item, index) => (
                      <FormItem {...formItemLayout} label=" " colon={false} key={index}>
                        <div className='copyWriter_item'>
                          <Card
                            bordered={false}
                            title={
                              <FormItem name={[index, 'name']} label="文案名称" rules={[{ required: true, message: '请输入文案名称' }]}>
                                <Input placeholder="请输入文案名称" allowClear />
                              </FormItem>
                            }
                            extra={
                              <CloseCircleOutlined className="copyWriter_item_Close" onClick={() => handleRemoveItem(index)} />
                            }
                          >
                            {
                              fastPosterParams && fastPosterParams.length > 0
                                ? fastPosterParams.map((param, pidx) => (
                                  <FormItem name={[index, 'params', pidx, 'content']} label={param.label} key={pidx}>
                                    <TextArea placeholder={'请输入【' + param.label + '】'} autoSize allowClear />
                                  </FormItem>
                                ))
                                : <span>请到海报绘制页中添加参数</span>
                            }
                          </Card>
                        </div>
                      </FormItem>
                    ))}
                  </>
                )}
              </Form.List>
            }

            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Space size={40}>
                {id ? <Button type="primary" onClick={() => onSubmit({})}>保存</Button> : <>
                  <Button type="primary" onClick={() => onSubmit({ value: 'notIssue' })} >仅保存</Button>
                  <Button type="primary" onClick={() => onSubmit({ value: 'notIssue', isDraw: true })}>保存并绘制海报</Button>
                </>}
              </Space>
            </div>
          </Form>
        </Card>
      </Spin>
    </div>
  );
};
export default withRouter(PosterToolForm);
