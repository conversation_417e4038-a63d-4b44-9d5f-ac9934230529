/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/20 15:42
 * @LastEditTime: 2023/11/22 16:05
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/PosterTool/home.jsx
 * @Description: '海报工具'
 */

import React, { useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
  message,
  Image,
  TreeSelect,
  Switch,
} from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { recursionTagKeyTreeData } from 'common/tree';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import './home.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;

const PosterTool = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [typeOptions, setTypeOptions] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '海报名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '海报预览',
      width: '160px',
      dataIndex: 'image',
      key: 'image',
      align: 'center',
      render: (value, record, index) => (
        <FileHOC src={value ? value : '' || 'error'}>
         {(url) => (
            <Image
              width={60}
              height={60}
              src={url}
              fallback="images/fallbackImg.png"
              style={{ objectFit: 'cover' }}
              preview
            />
         )}
       </FileHOC>
      ),
    },
    {
      title: '场景类型',
      width: '160px',
      dataIndex: 'sceneParentName',
      key: 'sceneParentName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value + `-${record.sceneName}`}>
          {value + `-${record.sceneName}`}
        </Tooltip>
      ),
    },
    {
      title: '海报类别',
      width: '160px',
      dataIndex: 'typeName',
      key: 'typeName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '可见范围',
      width: '160px',
      dataIndex: 'visibleScopeDepartmentNameList',
      key: 'visibleScopeDepartmentNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '海报状态',
      width: '160px',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (value, record, index) => (
        <>
          {(value == 'notIssue' || value == 'issued') && (
            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
          )}
          {(value == 'Added' || value == 'soldOut') && (
            <Switch
              className="stateSwitch"
              checkedChildren="已上架"
              unCheckedChildren="已下架"
              checked={value == 'Added'}
              onChange={(checked) => {
                onChangeSwitchStatus(checked, record);
              }}
            />
          )}
        </>
      ),
    },
    {
      title: '上下架时间',
      width: '160px',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    // {
    //   title: '数据',
    //   width: '160px',
    //   dataIndex: 'visitCount',
    //   key: 'visitCount',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => {
    //     const content = (
    //       <div>
    //         转发次数：{record.shareCount}
    //         <br />
    //         下载次数：{record.visitCount}
    //       </div>
    //     );
    //     return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
    //   },
    // },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.status == 'notIssue' && ((record.type == 'FIXATION' && record.fastPosterId) || (record.type == 'TEMPLATE' && record.fastPosterId))) {
          opts.push({ onClick: () => handleShelves(record), name: "上架" });
        }
        if (record.status != 'Added') {
          opts.push({ onClick: () => handleEdit(record), name: "配置参数" });
          opts.push({ onClick: () => handleDraw(record), name: "绘制海报" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getInfoTypeOptions();
    fetchList();
  }, []);

  // 获取场景类型
  const getInfoTypeOptions = () => {
    apiCall('/info/scene/tree', 'GET')
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res);
        setTypeOptions([
          {
            title: '全选',
            value: '',
            key: '',
            children: treeData,
          },
        ]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formForm.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.planTime) {
        formData.startPlanTime = moment(formData.planTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endPlanTime = moment(formData.planTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.planTime;
      }
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      formData.sceneIds = formData.sceneIds?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/info/posterTemplate', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formForm.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    props.history.push('/wecom/posterTool/form');
  };

  // 上架
  const handleShelves = (record) => {
    const { id, status } = record;
    const data = {
      infoResourceId: id,
      resourceStatus: status,
    };
    apiCall('/info/posterTemplate/upStatusAdded', 'PUT', data)
      .then((res) => {
        message.success('修改成功！');
        fetchList();
      })
      .catch((err) => { })
      .finally(() => { });
  };

  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/posterTool/form',
      search: `?id=${id}`,
    });
  };

  const handleDraw = (record) => {
    const { id, fastPosterId } = record;
    if (fastPosterId) {
      apiCall(
        `/info/posterTemplate/update/putTask?fastPosterId=${fastPosterId}`,
        'POST'
      )
        .then((res) => {
          props.history.push({
            pathname: '/wecom/posterTool/postIframe',
            search: `?id=${fastPosterId}`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    } else {
      apiCall(`/info/posterTemplate/beforeDraw?posterTemplateId=${id}`, 'POST')
        .then((res) => {
          props.history.push({
            pathname: '/wecom/posterTool/postIframe',
            search: `?id=${res.fastPosterId}`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    }
  };

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record;
    const data = {
      infoResourceId: id,
      resourceStatus: checked ? 'Added' : 'soldOut',
    };
    apiCall('/info/posterTemplate/upStatus', 'PUT', data)
      .then((res) => {
        message.success('修改成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="PosterTool">
      <FilterBar>
        <Form layout={'inline'} form={formForm}>
          <FormItem name="sceneIds">
            <TreeSelect
              treeData={typeOptions}
              treeCheckable
              treeDefaultExpandedKeys={['']}
              allowClear
              showArrow
              showSearch
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              treeNodeFilterProp="title"
              placeholder="场景类型"
            />
          </FormItem>
          <FormItem name="type">
            <SysDictSelect
              placeholder="海报类别"
              dataset="POSTER_TEMPLATE_TYPE"
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <Button type="primary" onClick={() => handleAdd()}>
            新增
          </Button>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <div>海报必须绘制海报后才可以上架，已上架的必须下架后才可以编辑</div>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(PosterTool);
