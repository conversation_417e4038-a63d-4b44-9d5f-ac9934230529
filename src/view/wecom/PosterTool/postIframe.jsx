/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/20 15:42
 * @LastEditTime: 2022/12/16 11:21
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\PosterTool\postIframe.jsx
 * @Description: '海报工具-绘制'
 */

import React, { useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { Button, Card, Spin } from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import Cookie from 'js-cookie';

const PostIframe = (props) => {
  const [id, setId] = useState(null);
  const [isAdd, setIsAdd] = useState(null);
  const [loading, setLoading] = useState(false);
  const [posterUrl, setPosterUrl] = useState(null);
  useEffect(() => {
    const { id, isAdd } = qs2obj(props.location.search);
    getPosterUrl();
    setId(id);
    setIsAdd(isAdd);
  }, []);

  const getPosterUrl = () => {
    setLoading(true);
    apiCall('/info/posterTemplate/getPosterUrl', 'GET').then((res) => {
      setPosterUrl(process.env.NODE_ENV == 'development' ? 'http://localhost:8080/' : res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div style={{ height: '100%' }}>
      <Spin spinning={loading} style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
      </Spin>
      {posterUrl && <iframe src={`${posterUrl}?id=${id}&isAdd=${isAdd}&token=${Cookie.get('weebot_cloud_token')}`} width="100%" height="100%" frameBorder={0}></iframe>}
    </div>
  );
};

export default withRouter(PostIframe);
