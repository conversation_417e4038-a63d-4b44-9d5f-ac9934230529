/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/15 10:07
 * @LastEditTime: 2025/04/30 10:20
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/Audit/detail.jsx
 * @Description: '审核'
 */

import ConversationModal from "@/view/wecom/Activity/CustomerGroupMsg/conversationModal"
import { LinkOutlined } from "@ant-design/icons"
import {
  Button,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  message,
  Row,
  Space,
  Spin,
  Tag,
} from "antd"
import { qs2obj } from "common/object"
import { apiCall } from "common/utils"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"
import SysDictLabel from "components/select/SysDictLabel"
import moment from "moment"
import React, { useEffect, useState } from "react"
import "./detail.less"

const { TextArea } = Input

const AuditDetail = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [type, setType] = useState(null)
  const [infoData, setInfoData] = useState({})
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })
  const [msgPrevieParams, setMsgPrevieParams] = useState({
    visible: false,
    open: false,
  })
  useEffect(() => {
    const { id, type } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      setType(type)
      getInfoData(id)
    }
  }, [])

  const getInfoData = async (id) => {
    setLoading(true)
    await apiCall(`/info/audit/${id}`, "GET")
      .then((res) => {
        res.typeStr = res.type === "MOMENT" ? "朋友圈" : "群发客户"
        setInfoData(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onSubmit = (state) => {
    formForm.validateFields().then((formData) => {
      if (state == "REJECTED" && !formData.opinion) {
        formForm.setFields([
          { errors: ["请输入审核意见（200字）"], name: ["opinion"] },
        ])
        return false
      } else {
        formForm.setFields([{ errors: [], name: ["opinion"] }])
      }
      setLoading(true)
      const data = {
        ...formData,
        state: state,
      }
      apiCall(`/info/audit/update/${id}`, "POST", data)
        .then((res) => {
          message.success("审核成功！")
          // clearCache(); // 清空路由缓存
          props.history.go(-1)
        })
        .catch((err) => {
          console.log(err)
          if (err.retcode == 23001) {
            // clearCache(); // 清空路由缓存
            props.history.go(-1)
          }
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handlePreview = (record) => {
    const { content } = record
    if (record.scope) {
      const data = [
        {
          type: "copyWriter",
          content: record.copyWriteContent,
        },
        ...content,
      ]
      setMsgPrevieParams({
        visible: true,
        listData: data,
        onCancel: () => {
          setMsgPrevieParams({
            visible: false,
          })
        },
      })
    } else {
      setResourcePreviewParams({
        visible: true,
        listData: content,
        type: "moment",
        onCancel: () => {
          setResourcePreviewParams({
            visible: false,
          })
        },
      })
    }
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }
  const GENDER = {
    "": "全部",
    null: "全部",
    MAN: "男",
    WOMAN: "女",
    UNKNOWN: "未知",
  }
  const fileName = moment().format("YYYY-MM-DD") + ".xlsx"
  const handleDownload = () => {
    handleDownloadCustomerList()
  }
  const handleDownloadCustomerList = () => {
    const data = {
      fileId: infoData?.sourceDetails?.scope?.importCustomerListFileId,
    }
    apiCall("/customerGroupMsg/downloadCustomerList", "GET", data, null, {
      isExit: true,
      title: fileName,
    })
      .then((res) => {
        message.success("下载成功！")
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }
  const standardFilter = (
    <>
      {infoData?.sourceDetails?.scope?.depEmployeeIdList?.length ? (
        <Descriptions.Item label="客户负责人">
          <ETypeTransferModal
            readonly
            btnShowFlag={false}
            value={infoData?.sourceDetails?.scope?.depEmployeeIdList}
          />
        </Descriptions.Item>
      ) : null}

      {infoData?.sourceDetails?.scope?.tagNameList?.length ? (
        <Descriptions.Item label="客户标签">
          <div style={{whiteSpace: "wrap"}}>
          {infoData?.sourceDetails?.scope?.tagNameList?.map((item) => (
            <Tag>{item}</Tag>
          )) ?? "-"}
          </div>
        </Descriptions.Item>
      ) : null}

      {infoData?.sourceDetails?.scope?.authFlag ? (
        <Descriptions.Item label="识别状态">
          {infoData?.sourceDetails?.scope?.authFlag ? "已识别" : "未识别"}
        </Descriptions.Item>
      ) : null}

      {infoData?.sourceDetails?.scope?.minAddTime &&
      infoData?.sourceDetails?.scope?.maxAddTime ? (
        <Descriptions.Item label="添加时间">
          {infoData?.sourceDetails?.scope?.minAddTime}{" "}
          {infoData?.sourceDetails?.scope?.minAddTime ? "~" : ""}{" "}
          {infoData?.sourceDetails?.scope?.maxAddTime}
        </Descriptions.Item>
      ) : null}

      {infoData?.sourceDetails?.scope?.groupNameList?.length ? (
        <Descriptions.Item label="群组">
          {infoData?.sourceDetails?.scope?.groupNameList?.map((item) => (
            <Tag>{item}</Tag>
          ))}
        </Descriptions.Item>
      ) : null}

      <Descriptions.Item label="性别">
        {GENDER[infoData?.sourceDetails?.scope?.gender]}
      </Descriptions.Item>
    </>
  )

  const importTable = (
    <>
      <Descriptions.Item label="客户清单">
        <div style={{ alignItems: "unset", display: "inline-flex" }}>
          <span style={{ marginRight: 12 }}>
            <LinkOutlined />
            {fileName}
          </span>
          <Button type="primary" onClick={handleDownload}>
            下载清单
          </Button>
        </div>
      </Descriptions.Item>
    </>
  )

  return (
    <div className="Audit-Detail-Container">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={
            `审核【${infoData.typeStr}】` + (type == "detail" ? "详情" : "")
          }
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row gutter={32}>
            <Col xs={24} lg={14} style={{ borderRight: "1px solid #f0f0f0" }}>
              <Descriptions
                title="审核内容"
                column={1}
                labelStyle={{ minWidth: "100px", justifyContent: "flex-end" }}
              >
                <Descriptions.Item label={infoData.typeStr + "名称"}>
                  {infoData?.sourceDetails?.name || "-"}
                </Descriptions.Item>
                <Descriptions.Item label={infoData.typeStr + "说明"}>
                  {infoData?.sourceDetails?.description || "-"}
                </Descriptions.Item>
                {infoData.type === "MOMENT" ? (
                  <Descriptions.Item label="发送范围">
                    {infoData?.sourceDetails?.depEmpList?.length ? (
                      <ETypeTransferModal
                        readonly
                        btnShowFlag={false}
                        value={infoData.sourceDetails?.depEmpList}
                      />
                    ) : (
                      "-"
                    )}
                  </Descriptions.Item>
                ) : (
                  <>
                    <Descriptions.Item label="发送范围">
                      <Descriptions
                        column={1}
                        style={{ background: "#eee", padding: 24 }}
                      >
                        {infoData?.sourceDetails?.scope?.type ===
                        "STANDARD_FILTER"
                          ? standardFilter
                          : importTable}
                      </Descriptions>
                    </Descriptions.Item>
                  </>
                )}

                {infoData.type === "MOMENT" ? (
                  <Descriptions.Item label="企业客户标签">
                    {infoData?.sourceDetails?.wxcpTagList?.length ? (
                      <Space wrap>
                        {infoData.sourceDetails?.wxcpTagList.map(
                          (item, index) => (
                            <Tag style={{ margin: "0" }} key={index}>
                              {item.title}
                            </Tag>
                          )
                        )}
                      </Space>
                    ) : (
                      "-"
                    )}
                  </Descriptions.Item>
                ) : null}

                {infoData.type === "MOMENT" ? (
                  <Descriptions.Item label="可发表时间">
                    {infoData ? (
                      <>
                        {infoData.sourceDetails?.startTime &&
                          moment(infoData.sourceDetails?.startTime).format(
                            "YYYY-MM-DD HH:mm"
                          )}{" "}
                        ～{" "}
                        {infoData.sourceDetails?.endTime &&
                          moment(infoData.sourceDetails?.endTime).format(
                            "YYYY-MM-DD HH:mm"
                          )}
                      </>
                    ) : (
                      "-"
                    )}
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item label="可发送时间">
                    {infoData ? (
                      <>
                        {infoData.sourceDetails?.startTime &&
                          moment(infoData.sourceDetails?.startTime).format(
                            "YYYY-MM-DD HH:mm"
                          )}{" "}
                        ～{" "}
                        {infoData.sourceDetails?.endTime &&
                          moment(infoData.sourceDetails?.endTime).format(
                            "YYYY-MM-DD HH:mm"
                          )}
                      </>
                    ) : (
                      "-"
                    )}
                  </Descriptions.Item>
                )}

                <Descriptions.Item
                  label={infoData.typeStr + "内容"}
                  contentStyle={{ display: "flex", flexDirection: "column" }}
                >
                  {infoData?.sourceDetails?.copyWriteContent || ""}
                  {infoData ? (
                    <a onClick={() => handlePreview(infoData.sourceDetails)}>
                      预览
                    </a>
                  ) : (
                    "-"
                  )}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col xs={24} lg={10}>
              <Form form={formForm}>
                <Descriptions
                  title="审核情况"
                  colon={false}
                  column={1}
                  layout="vertical"
                  labelStyle={{ color: "#a5a5a5" }}
                >
                  <Descriptions.Item label="申请人">
                    {infoData?.createEmployeeName || "-"}
                  </Descriptions.Item>
                  <Descriptions.Item label="申请时间">
                    {infoData?.createTime || "-"}
                  </Descriptions.Item>
                  {type && infoData ? (
                    <>
                      {type == "audit" && infoData.state == "AUDITING" ? (
                        <Descriptions.Item label="审核意见">
                          <Form.Item name="opinion">
                            <TextArea
                              placeholder="请输入审核意见（200字）"
                              allowClear
                              showCount
                              maxLength={200}
                              autoSize={{ minRows: 4, maxRows: 7 }}
                            />
                          </Form.Item>
                        </Descriptions.Item>
                      ) : (
                        ""
                      )}

                      {infoData.state != "AUDITING" &&
                      infoData.state != "WITHDRAWN" ? (
                        <>
                          <Descriptions.Item label="审核人">
                            {infoData?.auditEmployeeName || "-"}
                          </Descriptions.Item>
                          <Descriptions.Item label="审核时间">
                            {infoData?.auditTime || "-"}
                          </Descriptions.Item>
                          <Descriptions.Item label="审核状态">
                            {infoData ? (
                              <SysDictLabel
                                dataset="AUDIT_STATE"
                                dictkey={infoData.state}
                              />
                            ) : (
                              "-"
                            )}
                          </Descriptions.Item>
                          <Descriptions.Item label="审核意见">
                            {infoData?.opinion || "-"}
                          </Descriptions.Item>
                        </>
                      ) : (
                        ""
                      )}
                    </>
                  ) : (
                    ""
                  )}
                </Descriptions>
                {type && type == "audit" && infoData?.state == "AUDITING" ? (
                  <Space size={32}>
                    <Button type="default" onClick={() => onSubmit("REJECTED")}>
                      不通过
                    </Button>
                    <Button type="primary" onClick={() => onSubmit("PASSED")}>
                      通过
                    </Button>
                  </Space>
                ) : (
                  ""
                )}
              </Form>
            </Col>
          </Row>
        </Card>
        <WibotMaterialPreviewModal params={resourcePreviewParams} />
        <ConversationModal params={msgPrevieParams} />
      </Spin>
    </div>
  )
}

export default AuditDetail
