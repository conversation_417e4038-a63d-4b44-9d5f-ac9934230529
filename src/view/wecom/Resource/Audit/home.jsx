/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/07/15 09:42
 * @LastEditTime: 2024/07/15 16:13
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/Audit/home.jsx
 * @Description: '内容审核'
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
  DatePicker,
  Select,
} from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import moment from 'moment';
import FilterBar from 'components/FilterBar/FilterBar';
import SysDictSelect from 'components/select/SysDictSelect';
import SysDictLabel from 'components/select/SysDictLabel';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import ListOperation from 'components/ListOperation/home';
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const Audit = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '发起人/发起时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '内容类型',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => ( record.type === 'MOMENT'? '客户朋友圈': '群发客户'),
    },
    {
      title: '审核人/审核时间',
      width: '160px',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'center',
      render: (value, record, index) => (
        value ? <>
          {record.auditEmployeeName}
          <br />
          {value}
        </> : ''
      ),
      sorter: (a, b) => timeStamp(a.auditTime) - timeStamp(b.auditTime),
    },
    {
      title: '审核状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => (<SysDictLabel dataset="AUDIT_STATE" dictkey={value} />),
    },
    {
      title: '审核意见',
      width: '160px',
      dataIndex: 'opinion',
      key: 'opinion',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleDetail(record), name: '详情' },
        ];
        if (record.state == 'AUDITING') {
          opts.push({ onClick: () => handleAudit(record), name: '审核' });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.auditTime) {
        formData.minAuditTime = moment(formData.auditTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxAuditTime = moment(formData.auditTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.auditTime;
      }
      formData.createDepEmpList = formData.createDepEmpList?.join(',') || null;
      formData.auditDepEmpList = formData.auditDepEmpList?.join(',') || null;
      formData.stateList = formData.stateList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/info/audit', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };


  const handleDetail = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/audit/detail',
      search: `?id=${id}&type=detail`,
    });
  };

  const handleAudit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/audit/detail',
      search: `?id=${id}&type=audit`,
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="Audit-Container">
      <FilterBar >
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="createDepEmpList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="发起人" />
          </FormItem>
          <FormItem name="createTime" label="发起时间">
            <RangePicker />
          </FormItem>
          <FormItem name="type">
            <Select
              placeholder="内容类型"
              options={[
                {
                  value: 'MOMENT',
                  label: '客户朋友圈',
                },
                {
                  value: 'CUSTOMER_GROUP_MSG',
                  label: '群发客户',
                },
              ]}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem
            name="auditDepEmpList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="审核人" />
          </FormItem>
          <FormItem name="auditTime" label="审核时间">
            <RangePicker />
          </FormItem>
          <FormItem name="stateList">
            <SysDictSelect placeholder="审核状态" dataset="AUDIT_STATE" mode="multiple" />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default Audit;
