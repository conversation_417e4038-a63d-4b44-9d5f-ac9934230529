
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2022/04/13 16:31
 * @LastEditors: <PERSON><PERSON><PERSON>un<PERSON><PERSON>
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\InformationLibrary\comps\ClassFormModal.jsx
 * @Description: '资讯分类管理对话框'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, Button, Table, Tag } from 'antd';
import { apiCall } from 'common/utils';

const ClassFormModal = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '一级分类',
      width: '100px',
      dataIndex: 'classification',
      key: 'classification',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '二级分类',
      width: '260px',
      dataIndex: 'secondClassification',
      key: 'secondClassification',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const content = value.split(',').map((item, index) => <Tag key={index}>{item}</Tag>);
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
      },
    },
    {
      title: '资讯数量',
      width: '80px',
      dataIndex: 'infoCount',
      key: 'infoCount',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <a onClick={() => handleQuery(record)}>{value}</a>,
    },
  ];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      fetchList();
    }
  }, [props]);

  const fetchList = () => {
    setLoading(true);
    const data = {};
    apiCall('/info/information/classify', 'GET', data).then((res) => {
      setDataSource(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = (record) => {
    setVisible(false);
    setLoading(false);
    props.params?.onQuery?.(record);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title={
        <div style={{ display: 'flex', alignItem: 'center', justifyContent: 'space-between' }}>
          资讯分类管理
          {/* <Button style={{ marginRight: "20px" }} type="primary" onClick={() => handleClassManage()}>新增资讯分类</Button> */}
        </div>
      }
      maskClosable={false}
      afterClose={null}
      onCancel={onCancel}
      destroyOnClose
      footer={null}
      width={800}
    >
      <Table rowKey="classification" loading={loading} dataSource={dataSource} columns={columns} pagination={false} />
    </Modal>
  );
};

export default ClassFormModal;
