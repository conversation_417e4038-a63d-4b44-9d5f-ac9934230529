/*
 * @Author: <PERSON>eiw
 * @Date: 2022/11/08 09:37
 * @LastEditTime: 2023/06/05 16:29
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\InformationLibrary\comps\MaintainFormModal.jsx
 * @Description: '维护咨询表单对话框'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Spin, TreeSelect, Select, message } from 'antd';
import { apiCall } from 'common/utils';
import {
  recursionTagKeyTreeData,
  recursionTagKeyTreeDataDisabled,
  recursionTagKeyTreeDataTag,
  sceneRecursionTreeData
} from "common/tree"
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;

const MaintainFormModal = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [resourceTreeData, setResourceTreeData] = useState([]);
  const [productOption, setProductOption] = useState([]);

  useEffect(async () => {
    const { visible, formData } = props.params;
    if (visible) {
      setVisible(true);
      await getInfoSourceTypeTree();
      await getResourceCategoryTreeTwo();
      await getProductIds();
      await formRef?.current?.setFieldsValue({ ...formData });
    }
  }, [props]);

  const getInfoSourceTypeTree = async () => {
    setLoading(true);
    const data = {};
    await apiCall('/info/sourceType/tree', 'GET', data).then((res) => {
      const treeData = sceneRecursionTreeData(res);
      setClassifyMenu(treeData);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true);
    const data = {
      type: 'resource'
    };
    await apiCall('/info/tag/tagGroup/tree', 'GET', data).then((res) => {
      // setResourceTreeData([...recursionTagKeyTreeDataTag(res)]);
      const tagTreeData = recursionTagKeyTreeData(res)
      // setResourceTreeData(tagTreeData)
      setResourceTreeData([
        {
          title: "全选",
          value: "resource",
          key: "resource",
          children: tagTreeData,
        },
      ])
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取关联产品
  const getProductIds = async () => {
    setLoading(true);
    await apiCall('/info/infoResource/getProductIds', 'GET').then((res) => {
      setProductOption(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      formData.infoClassify = formData.infoClassify?.join('-');
      const data = {
        id: props?.params?.id || null,
        ...formData
      };
      apiCall('/info/information/saveInformation', 'POST', data).then((res) => {
        message.success('编辑成功！');
        setVisible(false);
        setConfirmLoading(false);
        setLoading(false);
        props.params?.onSubmit?.();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title="维护资讯"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>

          <FormItem name="sourceTypeId" label="资讯分类" rules={[{ required: true, message: '请选择资讯分类' }]}>
            <TreeSelect
              allowClear
              showArrow
              showSearch
              treeData={classifyMenu}
              treeDefaultExpandAll
              placeholder="请选择资讯分类"
              treeNodeFilterProp='title'
            />
          </FormItem>

          <FormItem name="infoTagIds" label="资源标签" rules={[{ required: true, message: '请选择资讯标签' }]}>
            <TreeSelect
              placeholder="请选择资源标签"
              treeData={resourceTreeData}
              treeCheckable
              treeDefaultExpandedKeys={['resource']}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp='title'
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          {/*<CustomTagSelect*/}
          {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
          {/*  label="资源标签"*/}
          {/*  name="resourceTagNameList"*/}
          {/*  placeholder="资源标签"*/}
          {/*  useRefForm={formRef}*/}
          {/*  existTagNameList={formRef.current?.getFieldValue("resourceTagNameList")}*/}
          {/*  labelTreeData={resourceTreeData}*/}
          {/*/>*/}

          <FormItem name="productId" label="关联产品" rules={[{ required: true, message: '请选择关联产品' }]}>
            <Select
              placeholder="请选择关联产品"
              options={productOption}
              fieldNames={{ label: "name", value: "id" }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>

          {/* <FormItem name="visibleScopeDepartmentId" label="可见范围" rules={[{ required: true, message: '请选择可见范围' }]}>
            <ETypeTransferModal title="可见范围" onlyDepartment={true}/>
          </FormItem> */}

        </Form>
      </Spin>
    </Modal>
  );
};

export default MaintainFormModal;
