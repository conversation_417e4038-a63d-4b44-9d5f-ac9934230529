
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2023/11/17 11:25
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/InformationLibrary/comps/SourceFormModal.jsx
 * @Description: '资讯来源管理对话框'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, Button, Table, Tag } from 'antd';
import { apiCall } from 'common/utils';

const SourceFormModal = (props) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '资讯源名称',
      width: '140px',
      dataIndex: 'sourceName',
      key: 'sourceName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '资讯源平台',
      width: '140px',
      dataIndex: 'sourcePlatform',
      key: 'sourcePlatform',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '资讯分类',
      width: '140px',
      dataIndex: 'infoClassification',
      key: 'infoClassification',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const content = value.split(',').map((item, index) => <Tag key={index}>{item}</Tag>);
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
      },
    },
    {
      title: '资讯数量',
      width: '80px',
      dataIndex: 'infoCount',
      key: 'infoCount',
      align: 'center',
      render: (value, record, index) => <a onClick={() => handleQuery(record)}>{value}</a>,
    },
  ];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      fetchList();
    }
  }, [props]);

  const fetchList = () => {
    setLoading(true);
    const data = {};
    apiCall('/info/information/source', 'GET', data).then((res) => {
      setDataSource(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = (record) => {
    setVisible(false);
    setLoading(false);
    props.params?.onQuery?.(record);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      title={
        <div style={{ display: 'flex', alignItem: 'center', justifyContent: 'space-between' }}>
          资讯来源管理
          {/* <Button style={{ marginRight: "20px" }} type="primary" onClick={() => handleClassManage()}>新增资讯分类</Button> */}
        </div>
      }
      maskClosable={false}
      afterClose={null}
      onCancel={onCancel}
      destroyOnClose
      footer={null}
      width={800}
    >
      <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} pagination={false} />
    </Modal>
  );
};

export default SourceFormModal;
