/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/18 18:07
 * @LastEditTime: 2025/05/15 11:10
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/InformationLibrary/form.jsx
 * @Description: '资讯库-表单'
 */

import React, { useState, useEffect, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Radio,
  Select,
  TreeSelect,
  message,
  Upload,
  Space,
} from "antd"
import { PlusOutlined, UserOutlined } from "@ant-design/icons"
import { removeInputEmpty, normFile, editorIsEmpty } from "common/regular"
import { compressImage, base64ToFile, beforeUpload } from "common/image"
import {
  recursionTagKeyTreeData,
  recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag,
  sceneRecursionTreeData
} from "common/tree"
import { apiCall } from "common/utils"
import { qs2obj } from "common/object"
import MaterialModal from "components/Modal/MaterialModal/home"
import { clearCache } from "react-router-cache-route"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEditor from "components/WibotEditor/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import "./form.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const InformationLibraryForm = (props) => {
  const [formForm] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [imageUrl, setImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [linkCardData, setLinkCardData] = useState({})
  const [status, setStatus] = useState(null)
  const [productIds, setProductIds] = useState([])
  const [classifyMenu, setClassifyMenu] = useState([])
  const [productValue, setProductValue] = useState(null)
  const [materialParams, setMaterialParams] = useState({ visible: false })

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    getInfoSourceTypeTree()
    getResourceCategoryTreeTwo()
    getProductIds()
    if (id) {
      setId(id)
      init(id)
    }
  }, [])

  const init = async (id) => {
    await fetchList({ id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          }
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取资讯分类
  const getInfoSourceTypeTree = () => {
    const data = {}
    apiCall("/info/sourceType/tree", "GET", data)
      .then((res) => {
        const treeData = sceneRecursionTreeData(res)
        setClassifyMenu(treeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 关联产品
  const getProductIds = () => {
    setLoading(true)
    apiCall("/info/infoResource/getProductIds", "GET")
      .then((res) => {
        setProductIds(
          res.map((item) => ({
            ...item,
            disabled: !item.enable,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    await apiCall(`/info/information/detail/${id}`, "POST")
      .then((res) => {
        const {
          coverFileId,
          title,
          description,
          shelfState,
          productId,
          bodyText,
        } = res
        formForm.setFieldsValue({
          ...res,
          fileId: coverFileId,
        })
        setImageUrl(coverFileId)
        setStatus(shelfState)
        setProductValue(productId)
        setLinkCardData({
          title,
          description,
          image: coverFileId,
        })
        let timer = setTimeout(() => {
          WibotEditorRef.current.setHtml(bodyText)
          clearTimeout(timer)
        }, 300)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 推文封面
  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        setImageUrl(fileUrl)
        const data = linkCardData
        data.image = fileUrl
        setLinkCardData({ ...data })
        formForm.setFieldsValue({
          fileId,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  // 从素材库选择海报
  const handleSelectMaterial = () => {
    setMaterialParams({
      visible: true,
      tabType: "Poster",
      tabList: ["Poster"],
      multiple: false,
      placeholder: "请输入海报名称",
      onSubmit: (data) => {
        setMaterialParams({ visible: false })
        const { fileId } = data
        setImageUrl(fileId && fileId[0])
        formForm.setFieldsValue({
          fileId: fileId && fileId[0],
        })
      },
      onCancel: () => {
        setMaterialParams({ visible: false })
      },
    })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: "",
    })
    setLinkCardData({ ...linkCardData, image: "" })
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      formData.coverFileId = formData.fileId
      setLoading(true)
      const data = {
        id,
        shelfState: type,
        ...formData,
      }
      const apiMode = "POST"
      const apiUrl = id
        ? "/info/information/saveInformation"
        : "/info/information/addManual"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/informationLibrary")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="InformationLibraryForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑资讯" : "新增资讯"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card bordered={false}>
          <Row>
            <Col xs={24} lg={16}>
              <Form {...layout} form={formForm}>
                <h2>资讯内容</h2>
                <FormItem
                  label="资讯名称"
                  name="title"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入资讯名称" }]}
                >
                  <Input
                    placeholder="请输入资讯名称(30字)"
                    maxLength={30}
                    allowClear
                    onChange={(e) => {
                      const data = linkCardData
                      data.title = e.target.value
                      setLinkCardData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  label="资讯描述"
                  name="description"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入文资讯描述" }]}
                >
                  <TextArea
                    autoSize={{ minRows: 4, maxRows: 8 }}
                    maxLength={30}
                    placeholder="请输入资讯描述(30字)"
                    allowClear
                    onChange={(e) => {
                      const data = linkCardData
                      data.description = e.target.value
                      setLinkCardData({ ...data })
                    }}
                  />
                </FormItem>

                <FormItem
                  name="fileId"
                  valuePropName="fileList"
                  getValueFromEvent={normFile}
                  label="资讯封面"
                  rules={[{ required: true, message: "请上传图片" }]}
                  extra="建议尺寸500*500px，大小限制为2M，最多上传1张"
                >
                  <Upload
                    name="fileId"
                    customRequest={customRequest}
                    listType="picture-card"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={onChangeUpload}
                  >
                    <WibotUploadImage
                      imageUrl={imageUrl}
                      loading={uploadLoading}
                      onClose={handleResetUpload}
                    />
                  </Upload>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleSelectMaterial}
                  >
                    从海报选择
                  </Button>
                </FormItem>

                <FormItem
                  name="sourceTypeId"
                  label="资讯分类"
                  rules={[{ required: true, message: "请选择资讯分类" }]}
                >
                  <TreeSelect
                    allowClear
                    showArrow
                    showSearch
                    treeData={classifyMenu}
                    treeDefaultExpandAll
                    placeholder="请选择资讯分类"
                    treeNodeFilterProp="title"
                  />
                </FormItem>

                <FormItem
                  name="visibleScopeDepartmentId"
                  label="可见范围"
                  rules={[{ required: true, message: "请选择可见范围" }]}
                >
                  <ETypeTransferModal title="可见范围" onlyDepartment />
                </FormItem>

                <FormItem
                  name="infoTagIds"
                  label="资源标签"
                  rules={[{ required: false, message: "请选择资源标签" }]}
                >
                  <TreeSelect
                    treeData={resourceTreeData}
                    treeCheckable
                    treeDefaultExpandedKeys={["resource"]}
                    allowClear
                    showArrow
                    showSearch
                    treeNodeFilterProp="title"
                    maxTagCount="responsive"
                    showCheckedStrategy={SHOW_PARENT}
                    placeholder="资源标签"
                  />
                </FormItem>
                {/*<CustomTagSelect*/}
                {/*  rules={[{ required: false, message: "请选择资源标签" }]}*/}
                {/*  label="资源标签"*/}
                {/*  name="resourceTagNameList"*/}
                {/*  placeholder="资源标签"*/}
                {/*  useForm={formForm}*/}
                {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
                {/*  labelTreeData={resourceTreeData}*/}
                {/*/>*/}
                <FormItem label="关联产品" name="productId">
                  <Select
                    options={productIds}
                    allowClear
                    showSearch
                    placeholder="请选择关联产品"
                    fieldNames={{ label: "name", value: "id" }}
                    filterOption={(input, option) =>
                      (option?.name ?? "")
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    onChange={(value) => {
                      setProductValue(value)
                    }}
                  />
                </FormItem>

                {productValue && (
                  <FormItem
                    label="底部链接样式"
                    name="btnLinkType"
                    initialValue={0}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value={0}>文本+二维码</Radio>
                      <Radio value={1}>文本+按钮</Radio>
                    </Radio.Group>
                  </FormItem>
                )}

                <FormItem
                  label="正文"
                  name="bodyText"
                  rules={[{ required: true, message: "请输入正文" }]}
                >
                  <WibotEditor
                    ref={WibotEditorRef}
                    toolbarConfig={{
                      excludeKeys: ["group-video"],
                    }}
                    onChangeHtml={(html) => {
                      formForm.setFieldValue(
                        "bodyText",
                        editorIsEmpty(html) ? "" : html
                      )
                    }}
                  />
                </FormItem>
              </Form>
              <div style={{ display: "flex", justifyContent: "center" }}>
                <Space size={40}>
                  <Button type="primary" onClick={() => onSubmit("soldOut")}>
                    仅保存
                  </Button>
                  <Button type="primary" onClick={() => onSubmit("Added")}>
                    保存并上架
                  </Button>
                </Space>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <WibotMaterialPreview
                type="session"
                listData={
                  linkCardData.title ||
                  linkCardData.description ||
                  linkCardData.image
                    ? [
                        {
                          type: "Article",
                          title: linkCardData.title,
                          description: linkCardData.description,
                          image: linkCardData.image,
                        },
                      ]
                    : []
                }
              />
            </Col>
          </Row>
        </Card>
      </Spin>
      <MaterialModal params={materialParams} />
    </div>
  )
}

export default withRouter(InformationLibraryForm)
