.InformationLibraryForm{
  .pre-wrap {
    text-align: center;

    .phone-box {
      position: relative;
      width: 306px;
      height: 600px;
      margin: auto;

      .phone_box_img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .title {
        position: absolute;
        top: 55px;
        left: 125px;
      }

      .text {
        position: absolute;
        top: 100px;
        left: 40px;
        width: 220px;
        text-align: left;
        color: #8b8b8b;
        font-size: 12px;
      }

      .content {
        position: absolute;
        top: 105px;
        left: 22px;

        .list-item {
          // display: flex;
          // flex-wrap: wrap;
          padding: 0;
          margin: 0;
          height: 450px;
          overflow-y: scroll;
          text-align: left;

          .item {
            // display: flex;
            // flex-direction: row;
            // width: 205px;
            // height: 300px;
            // overflow-y: scroll;
            display: block;
            // padding: 10px;
            // border-radius: 6px;
            // box-shadow: 1px 2px 3px 0px #ccc;
            // border: 1px solid #ccc;
            margin: 0 10px 20px;
            position: relative;
            // cursor: pointer;

            .title {
              margin: 0;
              font-size: 16px;
            }

            .describe {
              max-width: 250px;
              display: inline-block;
              padding: 10px;
              border-radius: 6px;
              box-shadow: 1px 2px 3px 0px #ccc;
              border: 1px solid #ccc;
              margin: 0;
              white-space: pre-wrap;
            }

            .content {
              margin: 10px 0;
            }

            .review-wrap {
              display: inline-block;
              vertical-align: top;
              width: 194px;

              .ant-typography {
                width: 100%;

                p {
                  display: inline-block;
                  word-break: break-all;
                }
              }
            }
          }

          // 滚动条整体部分
          &::-webkit-scrollbar {
            width: 6px; //对垂直方向滚动条
            height: 6px; //对水平方向滚动条
          }

          //滚动的滑块
          &::-webkit-scrollbar-thumb {
            border-radius: 6px;
            background-color: #f2f2f2 //滚动条的颜色
          }

          //内层滚动槽
          &::-webkit-scrollbar-track-piece {
            background-color: #f2f2f2;
          }
        }
      }
    }
  }
}