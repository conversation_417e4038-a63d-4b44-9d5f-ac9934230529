/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/28 11:37
 * @LastEditTime: 2024/06/04 11:00
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/InformationLibrary/home.jsx
 * @Description: '资讯库'
 */

import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  Row,
  Col,
  Typography,
  Switch,
  Image,
  TreeSelect,
  Select,
  Popover,
  message,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import { removeInputEmpty } from "common/regular";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import ClassFormModal from "./comps/ClassFormModal";
import SourceFormModal from "./comps/SourceFormModal";
import MaintainFormModal from "./comps/MaintainFormModal";
import TypeFormModal from "./comps/TypeFormModal";
import OperateModal from "components/Modal/OperateModal/index";
import OfficialModal from "./comps/OfficialModal";
import SysDictLabel from "components/select/SysDictLabel";
import WibotTableTag from 'components/WibotTableTag/home';
import "./home.less"; import LinkCard from "components/LinkCard/home";
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;
const { Paragraph } = Typography;
const { Option } = Select;

const InformationLibrary = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [classifyMenu, setClassifyMenu] = useState([]);
  const [sourceMenu, setSourceMenu] = useState([]);
  const [productOption, setProductOption] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [classFormParams, setClassFormParams] = useState({ visible: false });
  const [sourceFormParams, setSourceFormParams] = useState({ visible: false });
  const [maintainFormParams, setMaintainFormParams] = useState({ visible: false });
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [resourceTreeData, setResourceTreeData] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [open, setOpen] = useState(false);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [officialParams, setOfficialParams] = useState({ visible: false });
  const [operateRef, setOperateRef] = useState({});
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "资讯",
      width: "250px",
      dataIndex: "title",
      key: "title",
      align: "center",
      render: (value, record, index) => (
        <LinkCard
          data={{
            ...record,
            url: record.mipLink,
            image: record.coverFileId
          }}
        />
      ),
    },
    {
      title: "资讯分类",
      width: "160px",
      dataIndex: "sourceTypeId",
      key: "sourceTypeId",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        const content =
          (record.sourceTypeId && (
            <>
              {record.parentSourceTypeName}-{record.sourceTypeName}
            </>
          )) ||
          null;
        return <Tooltip placement="topLeft" title={content}>{content}</Tooltip>;
      },
    },
    // {
    //   title: '资讯来源',
    //   width: '160px',
    //   dataIndex: 'infoSource',
    //   key: 'infoSource',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    {
      title: "资源标签",
      width: "160px",
      dataIndex: "infoTagNames",
      key: "infoTagNames",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "新增方式",
      width: "160px",
      dataIndex: "inputType",
      key: "inputType",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value == "AUTO" ? "自动导入" : "手动新增"}>
          {value == "AUTO" ? "自动导入" : "手动新增"}
        </Tooltip>
      ),
    },
    {
      title: "可见范围",
      width: "160px",
      dataIndex: "visibleScopeDepartmentNameList",
      key: "visibleScopeDepartmentNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "关联产品",
      width: "160px",
      dataIndex: "productName",
      key: "productName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "访问数据",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: 'left' }}>
          访问次数：{record.visitCount}
          <br />
          访问人数：{record.visitorCount}
        </div>
      ),
    },
    {
      title: "资源状态",
      width: "160px",
      dataIndex: "shelfState",
      key: "shelfState",
      align: "center",
      render: (value, record, index) => (
        <>
          {(value == "notIssue" || value == "issued") && (
            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
          )}
          {(value == "Added" || value == "soldOut") && (
            <Switch
              checkedChildren="已上架"
              unCheckedChildren="已下架"
              checked={value == "Added"}
              onChange={(checked) => {
                onChangeSwitchStatus(checked, record);
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "资讯状态时间",
      width: "160px",
      dataIndex: "stateUpdateTime",
      key: "stateUpdateTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.stateUpdateEmployeeName}
          <br />
          {value}
        </>
      ),
    },
    {
      title: "创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    // {
    //   title: '发布状态',
    //   width: '160px',
    //   dataIndex: 'state',
    //   key: 'state',
    //   align: 'center',
    //   fixed: 'right',
    //   render: (value, record, index) => <Switch checkedChildren="已发布" unCheckedChildren="未发布" checked={value} onChange={(checked) => onChangeState(checked, record, index)} />,
    // },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleEdit(record)}>编辑</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    getInfoSourceTypeTree();
    getResourceCategoryTreeTwo();
    getProductIds();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      formData.sourceTypeIdList =
        formData.sourceTypeIdList?.join(",") == ""
          ? null
          : formData.sourceTypeIdList;
      // formData.infoSourceLists = formData.infoSourceLists?.map((item) => item.join('-'));
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/info/information/page", "POST", data)
        .then((res) => {
          if (res) {
            const { records, current, size, total, pages } = res;
            setDataSource(records);
            setPaginations({
              current: current,
              pageSize: size,
              total: total,
              showQuickJumper: true,
              showSizeChanger: true,
              showTotal: (total, range) =>
                `共 ${total} 条记录 第${current}/${pages}页`,
            });
          } else {
            setDataSource([]);
            setPaginations({ current: 1, pageSize: 10 });
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    const data = {
      type: "resource",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res);
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          },
        ]);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getInfoSourceTypeTree = () => {
    const data = {};
    apiCall("/info/sourceType/tree", "GET", data)
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res);
        setClassifyMenu([
          {
            title: "全选",
            value: "",
            key: "",
            children: treeData,
          },
        ]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取关联产品
  const getProductIds = async () => {
    await apiCall("/info/infoResource/getProductIds", "GET")
      .then((res) => {
        setProductOption(
          res
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增资讯
  const handleAdd = () => {
    props.history.push("/wecom/informationLibrary/form");
  };

  // 公众号管理
  const handleOfficialManage = () => {
    setOfficialParams({
      visible: true,
      onCancel: () => {
        setOfficialParams({ visible: false });
      },
    });
  };

  const handleClassManage = () => {
    setTypeFormParams({
      visible: true,
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getInfoSourceTypeTree();
      },
    });
  };

  // 批量上下架
  const handleshelf = (shelfState) => {
    setOpen(false);
    setOperateParams({
      visible: true,
      title: shelfState == "Added" ? "批量上架确认" : "批量下架确认",
      content: `您将批量${shelfState == "Added" ? "上架" : "下架"} ${selectedRowKeys.length
        } 个资讯，确认继续吗？`,
      onSubmit: () => {
        const data = {
          shelfState,
          informationIdList: selectedRowKeys,
        };
        apiCall("/info/information/batchUpdateShelfState", "POST", data)
          .then((res) => {
            message.success(
              shelfState == "Added" ? "批量上架成功！" : "批量下架成功！"
            );
            setSelectedRowKeys([]);
            setSelectedRows([]);
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record;
    const data = {
      informationIdList: [id],
      shelfState: checked ? "Added" : "soldOut",
    };
    apiCall("/info/information/batchUpdateShelfState", "POST", data)
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);

      })
      .finally(() => { });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleEdit = (record) => {
    const { id, sourceTypeId, infoTagIds, resourceTagNameList, productId, inputType } = record;
    if (inputType == "AUTO") {
      setMaintainFormParams({
        visible: true,
        id: id,
        formData: {
          sourceTypeId: sourceTypeId,
          resourceTagNameList: resourceTagNameList || [],
          infoTagIds,
          productId,
        },
        onCancel: () => {
          setMaintainFormParams({ visible: false });
        },
        onSubmit: () => {
          setMaintainFormParams({ visible: false });
          fetchList();
        },
      });
    } else {
      props.history.push({
        pathname: "/wecom/informationLibrary/form",
        search: `?id=${id}`,
      });
    }
  };

  const handleOpenChange = (newOpen) => {
    setOpen(newOpen);
  };

  const onChangeRowSelect = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onChangeRowSelect,
  };

  const hasSelected = selectedRowKeys.length > 0;

  return (
    <div className="informationLibrary">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="keyWord"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="资讯名称、资讯描述" allowClear />
          </FormItem>
          <FormItem name="sourceTypeIdList">
            <TreeSelect
              placeholder="资讯分类"
              treeData={classifyMenu}
              treeCheckable
              treeDefaultExpandedKeys={[""]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          {/* <FormItem name="infoSourceLists">
            <Cascader
              placeholder="资讯来源"
              options={sourceMenu}
              multiple
              allowClear
              showArrow
              maxTagCount="responsive"
            />
          </FormItem> */}
          {/* <FormItem name="createTime" >
            <RangePicker />
          </FormItem> */}
          <FormItem name="productIds">
            <Select
              placeholder="关联产品"
              fieldNames={{ label: "name", value: "id" }}
              options={productOption}
              allowClear
              showSearch
              showArrow
              mode="multiple"
              filterOption={(input, option) =>
                (option?.name ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="infoTagIds">
            <TreeSelect
              treeData={resourceTreeData}
              treeCheckable
              treeDefaultExpandedKeys={["resource"]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder="资源标签"
            />
          </FormItem>
          {/*<CustomTagSelect*/}
          {/*  creatable*/}
          {/*  name="resourceTagNameList"*/}
          {/*  placeholder="资源标签"*/}
          {/*  useRefForm={formRef}*/}
          {/*  existTagNameList={formRef.current?.getFieldValue("resourceTagNameList")}*/}
          {/*  labelTreeData={resourceTreeData}*/}
          {/*/>*/}
          <FormItem name="shelfState">
            <Select placeholder="资讯状态" allowClear>
              <Option value="Added">已上架</Option>
              <Option value="soldOut">已下架</Option>
            </Select>
          </FormItem>
          <FormItem name="time" label="资讯状态时间">
            <RangePicker />
          </FormItem>
          {/* <FormItem name="employeeName" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="创建人" allowClear />
          </FormItem> */}
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增资讯
            </Button>
            {hasSelected ? (
              <Popover
                placement="bottom"
                overlayClassName="PopoverInformation"
                overlayInnerStyle={{ width: "102px", textAlign: "center" }}
                open={open}
                onOpenChange={handleOpenChange}
                content={
                  <div className="information_content">
                    <div onClick={() => handleshelf("Added")}>批量上架</div>
                    <div onClick={() => handleshelf("soldOut")}>批量下架</div>
                  </div>
                }
                trigger="hover"
              >
                <Button type="primary">批量上下架</Button>
              </Popover>
            ) : (
              <Button type="primary" disabled>
                批量上下架
              </Button>
            )}
            <Button type="primary" onClick={() => handleOfficialManage()}>
              公众号管理
            </Button>
            <Button type="primary" onClick={() => handleClassManage()}>
              资讯分类管理
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
          rowSelection={rowSelection}
        />
      </Card>
      <ClassFormModal params={classFormParams} />
      <SourceFormModal params={sourceFormParams} />
      <MaintainFormModal params={maintainFormParams} />
      <TypeFormModal params={typeFormParams} />
      <OperateModal params={operateParams} />
      <OfficialModal params={officialParams} />
    </div>
  );
};

export default InformationLibrary;
