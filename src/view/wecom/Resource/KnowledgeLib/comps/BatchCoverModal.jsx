/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/12/27 10:28
 * @LastEditTime: 2023/12/27 14:43
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/KnowledgeLib/comps/BatchCoverModal.jsx
 * @Description: '批量覆盖问答库'
 */

import React, { useRef, useState } from 'react';
import { Form, message, Modal, Spin, Button, Upload } from 'antd';
import { apiCall } from 'common/utils';
import moment from 'moment';
import { UploadOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';

const FormItem = Form.Item;

const BatchCoverModal = (props) => {
  const { visible, onSubmit, onCancel } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  const onChangeUpload = (info) => {
    if (!info.file.name.endsWith('xls') && !info.file.name.endsWith('xlsx')) {
      return false;
    }
    if (!info.fileList.length) {
      formRef.current.setFieldsValue({ file: '' });
    }
    let newFileList = [...info.fileList];
    setFileList(newFileList);
  };

  const customFileRequest = (config) => {
    setConfirmLoading(true);
    const File = config.file;
    const newFileLIst = fileList;
    let timer = setTimeout(() => {
      newFileLIst[0].status = 'done';
      newFileLIst[0].percent = 100;
      newFileLIst[0].file = File;
      setFileList([...newFileLIst]);
      setConfirmLoading(false);
      clearTimeout(timer);
    }, 2000);
  };

  // 下载模板
  const handleDownload = () => {
    setLoading(true);
    const title = '批量覆盖问答库模版';
    const data = {
      fileName: `${title}.xlsx`,
    };
    apiCall('/file/template', 'GET', data, null, {
      isExit: true,
      title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
    })
      .then((res) => {
        message.success('下载成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      // 通过FormData构造函数创建一个空对象
      const formObj = new FormData();
      // 通过append方法来追加数据
      formObj.append('file', fileList[0].file); // 返回压缩后的图片
      const data = formObj;
      setConfirmLoading(true);
      apiCall('/knowledge/question/import', 'POST', data)
        .then((res) => {
          if (res) {
            message.error('部分数据上传失败，失败数据已下载！');
            saveAs(res, `批量覆盖问答库失败列表.${moment().format('YYYY-MM-DD')}.xlsx`);
          } else {
            message.success('上传成功！');
          }
          setLoading(false);
          setFileList([]);
          onCancel?.();
          onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  return (
    <Modal
      className="KnowledgeLib-BatchCoverModal"
      visible={visible}
      title="批量覆盖问答库"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={() => {
        setLoading(false);
        setFileList([]);
        onCancel?.();
      }}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <p className="tips">通过excel批量覆盖问答库，问题标题存在时则覆盖，不存在时则创建。</p>
          <FormItem
            label="选择文件"
            name="file"
            rules={[{ required: true, message: '请上传xls、xlsx格式的文件' }]}
            extra={
              <div>
                请选择批量覆盖的文件，格式xls、xlsx
                <a onClick={handleDownload} style={{ marginLeft: '5px' }}>
                  下载模板
                </a>
              </div>
            }
          >
            <Upload
              name="file"
              accept=".xls,.xlsx"
              fileList={fileList}
              onChange={onChangeUpload}
              beforeUpload={(file) => {
                if (
                  !file.name.endsWith('xls') &&
                  !file.name.endsWith('xlsx')
                ) {
                  message.error('请上传xls、xlsx格式的文件!');
                  return false || Upload.LIST_IGNORE;
                }
              }}
              customRequest={customFileRequest}
            >
              {fileList.length <= 0 ? (
                <Button icon={<UploadOutlined />}>选择文件</Button>
              ) : null}
            </Upload>
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default BatchCoverModal;
