/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/15 16:25
 * @LastEditTime: 2024/11/14 17:32
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/KnowledgeLib/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Spin, Card, Button, Space, message, Divider } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import './form.less';

const { TextArea } = Input;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

const KnowledgeLibForm = (props) => {
  const [formForm] = Form.useForm();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [knowledgeOption, setKnowledgeOption] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    getKnowledgeOption();
    if (id) {
      setId(id)
      getInfoData(id);
    } else {
      formForm.setFieldsValue({
        answerList: [{}]
      })
    }
  }, []);

  const getKnowledgeOption = () => {
    setLoading(true);
    apiCall("/info/tag/knowledge/tag", "GET", { paged: false }).then((res) => {
      const { records } = res;
      setKnowledgeOption(records);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/knowledge/question/${id}`, 'GET').then((res) => {
      const { title, content, tagList, answerList } = res;
      formForm.setFieldsValue({
        title,
        content,
        tagList: tagList?.map(item => ({
          label: item.name,
          value: item.id,
        })),
        answerList: answerList?.map(item => ({
          content: item.content,
          tagList: item.tagList?.map(atem => ({
            label: atem.name,
            value: atem.id,
          })),
        })),
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      formData.tagList = formData.tagList?.map((item) => ({
        name: item.label || item.value,
        id: item.label ? item.value : null,
      })) || [];
      formData.answerList = formData.answerList?.map((item) => ({
        ...item,
        tagList: item.tagList?.map((atem) => ({
          name: atem.label || atem.value,
          id: atem.label ? atem.value : null,
        })) || []
      }))
      const data = {
        ...formData,
        enable: id ? null : true
      }
      const apiUrl = id ? `/knowledge/question/update/${id}` : `/knowledge/question`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        // clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='KnowledgeLib-Form-Container'>
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + '问答'}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Form
            {...layout}
            form={formForm}
          >
            <Form.Item
              name="title"
              label="问题标题"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入问题标题' }]}
            >
              <Input placeholder="请输入问题标题" allowClear maxLength={50} />
            </Form.Item>

            <Form.Item
              name="tagList"
              label="问题标签"
              rules={[{ required: false, message: '请选择问题标签', }]}
            >
              <Select
                placeholder="请选择问题标签"
                fieldNames={{ label: 'name', value: 'id' }}
                options={knowledgeOption}
                allowClear
                showSearch
                showArrow
                mode="tags"
                labelInValue
                maxTagCount="responsive"
                optionFilterProp="name"
              />
            </Form.Item>

            <Form.Item
              name="content"
              label="问题内容"
              rules={[{ required: true, message: '请输入问题内容' }]}
            >
              <TextArea
                showCount
                maxLength={1000}
                allowClear
                autoSize={{ minRows: 4, maxRows: 6 }}
                placeholder="请输入问题内容"
              />
            </Form.Item>

            <Form.List name="answerList">
              {(fields, { add, remove }) => (
                <>
                  <Form.Item label="答案列表">
                    <Button type="primary" onClick={() => add()} icon={<PlusOutlined />}>
                      新增答案
                    </Button>
                  </Form.Item>

                  {fields.map((field, index) => (
                    <div key={field.key}>
                      <Form.Item
                        label={'答案' + (index + 1)}
                        required
                      >
                        <Space align="flexStart">
                          <Form.Item
                            noStyle
                            name={[field.name, 'content']}
                            rules={[{ required: true, message: '请输入答案' + (index + 1) }]}
                          >
                            <TextArea
                              showCount
                              maxLength={1000}
                              allowClear
                              autoSize={{ minRows: 4, maxRows: 6 }}
                              placeholder={'请输入答案' + (index + 1)}
                            />
                          </Form.Item>

                          {fields.length > 1 ? (
                            <MinusCircleOutlined
                              className="dynamic-delete-button"
                              onClick={() => remove(field.name)}
                            />
                          ) : null}
                        </Space>
                      </Form.Item>

                      <Form.Item
                        name={[field.name, 'tagList']}
                        label={"答案" + (index + 1) + '标签'}
                      >
                        <Select
                          placeholder={"请选择答案" + (index + 1) + '标签'}
                          fieldNames={{ label: 'name', value: 'id' }}
                          options={knowledgeOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="tags"
                          labelInValue
                          maxTagCount="responsive"
                          optionFilterProp="name"
                        />
                      </Form.Item>

                      {fields.length > 1 ? (<Divider />) : null}
                    </div>
                  ))}
                </>
              )}
            </Form.List>

          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button onClick={() => handleGoBack()}>取消</Button>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Card>
      </Spin>
    </div>
  );
};

export default KnowledgeLibForm;
