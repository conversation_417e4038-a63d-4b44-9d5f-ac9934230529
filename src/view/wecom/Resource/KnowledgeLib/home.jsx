/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/12 09:32
 * @LastEditTime: 2023/12/27 10:29
 * @LastEditors: Jana<PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/KnowledgeLib/home.jsx
 * @Description: '问答库'
 */

import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  message,
  Select,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { removeInputEmpty } from "common/regular";
import OperateModal from "components/Modal/OperateModal/index";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import BatchCoverModal from './comps/BatchCoverModal';
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;

const KnowledgeLib = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [knowledgeOption, setKnowledgeOption] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [batchCoverParams, setBatchCoverParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "问题标题",
      width: "160px",
      dataIndex: "title",
      key: "title",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "问题内容",
      width: "160px",
      dataIndex: "content",
      key: "content",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "相关标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "enable",
      key: "enable",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        switch (value) {
          case true:
            value = "启用";
            break;
          case false:
            value = "停用";
            break;
        }
        return <Tooltip placement="topLeft" title={value}>{value}</Tooltip>;
      },
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        if (record.enable) {
          opts.push({ onClick: () => handleChangeEnable(record), name: "停用" });
        } else {
          opts.push({ onClick: () => handleChangeEnable(record), name: "启用" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getKnowledgeOption();
    fetchList();
  }, []);

  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  const getKnowledgeOption = () => {
    setLoading(true);
    apiCall("/info/tag/knowledge/tag", "GET", { paged: false }).then((res) => {
      const { records } = res;
      setKnowledgeOption(records);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      formData.tagIdList = formData.tagIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/knowledge/question", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/knowledgeLib/form");
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/knowledgeLib/form",
      search: `?id=${id}`,
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { title, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: title ? `确定要删除问答【${title}】吗？` : '确定要删除该问答吗？',
      onSubmit: () => {
        apiCall(`/knowledge/question/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  // 启用/停用
  const handleChangeEnable = (record) => {
    const { id, enable, title } = record;
    setOperateParams({
      visible: true,
      title: enable ? "停用确认" : "启用确认",
      content: title ? `您将为【${title}】进行操作，确认继续吗？` : '确定要操作该问答吗？',
      onSubmit: () => {
        const data = {
          enable: !enable,
        };
        apiCall(`/knowledge/question/update/${id}`, "POST", data)
          .then((res) => {
            message.success("修改成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  const handleBatchCover = () => {
    setBatchCoverParams({
      visible: true,
      onSubmit: () => {
        fetchList();
      },
      onCancel: () => {
        setBatchCoverParams({ visible: false });
      },
    });
  };

  return (
    <div className="KnowledgeLib-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="title" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="问题标题" allowClear />
          </FormItem>

          <FormItem name="content" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="问题内容" allowClear />
          </FormItem>

          <FormItem name="tagIdList">
            <Select
              placeholder="相关标签"
              fieldNames={{ label: "name", value: "id" }}
              options={knowledgeOption}
              allowClear
              showSearch
              showArrow
              mode="multiple"
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={handleBatchCover}>
              批量覆盖
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <BatchCoverModal params={batchCoverParams} />
    </div>
  );
};

export default KnowledgeLib;
