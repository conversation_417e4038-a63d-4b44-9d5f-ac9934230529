/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/29 10:34
 * @LastEditTime: 2024/10/22 09:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/comps/ExportModal.jsx
 * @Description: '导出对话框'
 */

import React, { useEffect, useRef, useState } from "react";
import {
  Tabs,
  Form,
  message,
  Modal,
  Spin,
  DatePicker,
  Button,
  Image,
  Select,
} from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import "./ExportModal.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ExportModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("1");
  const [imageVisible, setImageVisible] = useState(false);
  const [scriptOption, setScriptOption] = useState([]);
  const ImagePreviewList = [
    require("images/微话本资源情况汇总.png"),
    require("images/微话本反馈明细.png"),
    require("images/微话本转发明细.png"),
  ];
  const layout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 14 },
    },
  };

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getScriptOption();
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const getScriptOption = () => {
    setLoading(true);
    apiCall("/info/microScript/option", "GET")
      .then((res) => {
        setScriptOption(
          res
        );
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      if (formData.date) {
        formData.startTime = moment(formData.date[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endTime = moment(formData.date[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.date;
      }
      const data = {
        ...formData,
      };

      let apiUrl = "/info/microScript/export/summary";
      let title = "微话本资源情况汇总";
      if (TabsActiveKey == "2") {
        apiUrl = "/info/microScript/export/feedbackDetail";
        title = "微话本反馈明细";
      } else if (TabsActiveKey == "3") {
        apiUrl = "/info/microScript/export/shareDetail";
        title = "微话本转发明细";
      }
      apiCall(apiUrl, "POST", data, null, {
        isExit: true,
        title: `${title}.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => {
          message.success("导出成功！");
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
    setConfirmLoading(false);
  };

  return (
    <Modal
      className="CustomerStatistics-ExportModal"
      visible={visible}
      title="导出微话本数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
                style={{ display: "none" }}
                preview={{
                  visible: imageVisible,
                  src: url,
                  onVisibleChange: (value) => {
                    setImageVisible(value);
                  },
                }}
              />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={onOk}
          >
            导出
          </Button>
        </div>,
      ]}
    >
      <Spin spinning={loading}>
        <Form ref={formRef} {...layout}>
          <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
            <Tabs.TabPane tab="微话本资源情况汇总" key="1">
              <p className="tips">
                根据部门名下客户经理对话本的转发时间来汇总计算话本使用量、素材使用量、话本反馈量
              </p>
              <FormItem name="idList" label="话本名称">
                <Select
                  placeholder="选择话本"
                  fieldNames={{ label: "name", value: "id" }}
                  options={scriptOption}
                  allowClear
                  showSearch
                  mode="multiple"
                  maxTagCount="responsive"
                  filterOption={(input, option) =>
                    (option?.name ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </FormItem>
              <FormItem
                label="使用时间"
                name="date"
                rules={[{ required: true, message: "请选择统计时间" }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择部门"
                rules={[{ required: true, message: "请选择统计对象" }]}
              >
                <ETypeTransferModal title="选择对象" onlyDepartment />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="微话本反馈明细" key="2">
              <p className="tips">
                根据部门名下客户经理对话本的反馈时间，来统计话本的反馈情况
              </p>
              <FormItem
                label="反馈时间"
                name="date"
                rules={[{ required: true, message: "请选择反馈时间" }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择员工、部门"
                rules={[{ required: true, message: "请选择统计对象" }]}
              >
                <ETypeTransferModal title="选择对象" mode={["dep", "emp"]} />
              </FormItem>
            </Tabs.TabPane>

            <Tabs.TabPane tab="微话本转发明细" key="3">
              <p className="tips">
                根据部门名下客户经理对话本的转发时间，来统计话本的转发情况
              </p>
              <FormItem
                label="转发时间"
                name="date"
                rules={[{ required: true, message: "请选择转发时间" }]}
              >
                <RangePicker />
              </FormItem>
              <FormItem
                label="统计对象"
                name="depEmployeeIdList"
                extra="支持选择员工、部门"
                rules={[{ required: true, message: "请选择统计对象" }]}
              >
                <ETypeTransferModal title="选择对象" mode={["dep", "emp"]} />
              </FormItem>
            </Tabs.TabPane>
          </Tabs>
        </Form>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
