/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/08 15:29
 * @LastEditTime: 2025/07/02 14:56
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/comps/ScriptContent.jsx
 * @Description: '话术组'
 */

import React, {
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
  memo,
} from "react";
import { Form, Tabs, Input, Button, Select, message } from "antd";
import {
  LeftOutlined,
  RightOutlined,
  PlusOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  FormOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { apiCall } from "common/utils";
import MaterialListForm from "components/MaterialListForm/home";
import { qs2obj } from "common/object";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import DragSort from "components/DragSort/home";
import { removeInputEmpty, editorIsEmpty } from "common/regular";
import WibotEditor from "components/WibotEditor/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./ScriptContent.less";

const FormItem = Form.Item;
const { TabPane } = Tabs;

const ScriptTabs = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    getInitObjData,
    getModifyObjList,
    setRetrieveFun,
  }));

  //默认显示一条时段
  const defaultPanes = Array.from({ length: 1 }).map((_, index) => {
    const id = String(index + 1);
    return { title: `话术组${id}`, key: String(index) };
  });

  const onRefMaterialListForm = useRef();
  const WibotEditorRef = useRef(null);
  const [activeKey, setActiveKey] = useState(defaultPanes[0].key);
  const [objList, setObjList] = useState([
    {
      microScriptSceneId: "",
      isEdit: true,
      panes: defaultPanes,
      list: [
        {
          tipsList: [],
          contentList: [],
        },
      ],
    },
  ]);
  const [formRef, setFormRef] = useState(null);
  const [occasionOption, setOccasionOption] = useState([]);
  const [sceneActiveIdx, setSceneActiveIdx] = useState(0);

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    getOccasionOption(id);
    if (props.formRef) {
      setFormRef(props.formRef);
      // //获取LocalStorage存储的数据
      // getLocalStorageObjList(props.formRef);
    }
  }, []);

  //获取场合
  const getOccasionOption = (id) => {
    apiCall("/info/microScriptScene", "GET")
      .then((res) => {
        const options = res.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        setOccasionOption(options);
        if (!id) {
          //新增情况下获取LocalStorage存储的数据
          getLocalStorageObjList(props.formRef, options);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  //时段Tab切换改变相应的数据
  useEffect(() => {
    if (formRef) {
      let fieldData = objList[Number(sceneActiveIdx)].list[Number(activeKey)];
      onRefMaterialListForm.current.getInitMsgList(fieldData.contentList);

      formRef.setFieldsValue({
        scriptName: fieldData.name, //初始化话术组名称
        tipsTitle: fieldData.tipsTitle, //初始化TIPS标题
        title: fieldData.title, //初始化话术标题
      });
    }
  }, [activeKey]);

  //获取LocalStorage存储的数据
  const getLocalStorageObjList = (formRef, options) => {
    const localStorageData = JSON.parse(localStorage.getItem("scriptObjList"));
    const newObjList =
      localStorageData?.length > 0
        ? localStorageData
        : [
          {
            microScriptSceneId: "",
            isEdit: true,
            panes: defaultPanes,
            list: [
              {
                tipsList: [],
                contentList: [],
              },
            ],
          },
        ];
    //设置拿到的存储数据
    setObjList([...newObjList]);
    //禁选已经被选中的场合
    const data = newObjList.map((item, idx) => item.microScriptSceneId);
    const newOptions = JSON.parse(JSON.stringify(options));
    newOptions.forEach((item, index) => {
      if (data.includes(item.value)) {
        item.disabled = true;
      }
    });
    setOccasionOption(newOptions);

    let fieldData = newObjList[sceneActiveIdx].list[Number(activeKey)];

    formRef.setFieldsValue({
      scriptName: fieldData.name, //初始化话术组名称
      tipsTitle: fieldData.tipsTitle, //初始化TIPS标题
      title: fieldData.title, //初始化话术标题
    });
    //初始化话术内容
    let timer = setTimeout(() => {
      onRefMaterialListForm.current.getInitMsgList(
        JSON.parse(JSON.stringify(fieldData.contentList))
      );
      clearTimeout(timer);
    }, 100);
  };

  //获取查看详情初始化的数据
  const getInitObjData = (data) => {
    data?.forEach((item, index) => {
      item.isEdit = false;
      item.panes = item.list.map((atem, andex) => ({
        title: `话术组${andex + 1}`,
        key: String(andex),
      }));
    });
    setObjList(data);
    //禁选已经被选中的场合
    const sceneData = data.map((item, idx) => item.microScriptSceneId);
    const newOptions = JSON.parse(JSON.stringify(occasionOption));
    newOptions.forEach((item, index) => {
      if (sceneData.includes(item.value)) {
        item.disabled = true;
      }
    });
    setOccasionOption(newOptions);
    //初始化话术内容
    let fieldData = data[sceneActiveIdx].list[Number(activeKey)];

    formRef.setFieldsValue({
      scriptName: fieldData.name, //初始化话术组名称
      tipsTitle: fieldData.tipsTitle, //初始化TIPS标题
      title: fieldData.title, //初始化话术标题
    });

    onRefMaterialListForm.current.getInitMsgList(
      JSON.parse(JSON.stringify(fieldData.contentList))
    );
  };

  const onChange = (key) => {
    setActiveKey(key);
    WibotEditorRef.current.clear()
  };

  const onEdit = (targetKey, action) => {
    if (action === "add") {
      handleAdd();
    } else {
      remove(targetKey);
    }
  };

  //添加场合
  const handleAddOccasion = () => {
    const localStorageData = JSON.parse(localStorage.getItem("scriptObjList"));
    const newObjList =
      localStorageData?.length > 0
        ? localStorageData
        : [
          {
            microScriptSceneId: "",
            isEdit: true,
            panes: defaultPanes,
            list: [
              {
                tipsList: [],
                contentList: [],
              },
            ],
          },
        ];
    if (localStorageData?.length > 0) {
      newObjList.push({
        microScriptSceneId: "",
        isEdit: true,
        panes: defaultPanes,
        list: [
          {
            tipsList: [],
            contentList: [],
          },
        ],
      });
    }
    setObjList(newObjList);
  };

  //选择场合
  const onChangeSelect = (val, option, index) => {
    const newObjList = JSON.parse(localStorage.getItem("scriptObjList"));
    const data = newObjList.map((item, idx) => item.microScriptSceneId);
    const newOption = JSON.parse(JSON.stringify(occasionOption));
    if (val) {
      //已被选择的场合不能再选
      newObjList[index].microScriptSceneId = val;
      newObjList[index].microScriptSceneName = option.label;
      newOption.forEach((item, index) => {
        if (item.value == val) {
          item.disabled = true;
        }
      });
      setOccasionOption(newOption);
    } else if (data.length > 0) {
      //放出清空的场合
      newOption.forEach((item, idx) => {
        if (item.value == data[index]) {
          item.disabled = false;
        }
      });
      setOccasionOption(newOption);
      newObjList[index].microScriptSceneId = "";
      newObjList[index].microScriptSceneName = "";
    }
    setObjList(newObjList);
  };

  //点击保存后的场合切换到对应的话术组
  const onFocusSelect = (index, obj = null) => {
    setSceneActiveIdx(index);
    setActiveKey("0");
    const objList = obj
      ? obj
      : JSON.parse(localStorage.getItem("scriptObjList"));
    let fieldData = objList[Number(index)].list[Number("0")];
    formRef.setFieldsValue({
      scriptName: fieldData.name, //初始化话术组名称
      tipsTitle: fieldData.tipsTitle, //初始化TIPS标题
      title: fieldData.title, //初始化话术标题
    });
    onRefMaterialListForm.current.getInitMsgList(fieldData.contentList);
  };

  //场合删除
  const handleDelete = (value, index) => {
    const localStorageData = JSON.parse(localStorage.getItem("scriptObjList"));
    const data = localStorageData.map((item, idx) => item.microScriptSceneId);
    const newOption = JSON.parse(JSON.stringify(occasionOption));
    newOption.forEach((item, idx) => {
      if (item.value == data[index]) {
        item.disabled = false; //删除的场合可以被选
      }
    });
    setOccasionOption(newOption);

    localStorageData.splice(index, 1);
    let idx = 0;
    if (index > sceneActiveIdx) {
      idx = sceneActiveIdx;
    } else {
      idx = index == 0 || sceneActiveIdx == 0 ? 0 : sceneActiveIdx - 1;
    }
    setSceneActiveIdx(idx);
    onFocusSelect(idx, localStorageData); //切换到对应的话术组数据
    setObjList(localStorageData);
  };

  //场合保存或编辑
  const handleSaveOrEdit = (value, index, type = null) => {
    const newObjList = JSON.parse(localStorage.getItem("scriptObjList"));
    if (type) {
      //场合编辑
      newObjList[index].isEdit = true;
    } else {
      //场合保存
      if (newObjList[index].microScriptSceneId) {
        newObjList[index].isEdit = false;
        onFocusSelect(index);
      } else {
        message.error("请选择场合！");
        return;
      }
    }
    setObjList(newObjList);
  };

  // 拖拽排序
  const changePosition = (dragIndex, hoverIndex) => {
    console.log(dragIndex, hoverIndex);
    const newObjList = JSON.parse(localStorage.getItem("scriptObjList"));
    const temp = newObjList[dragIndex];
    // 交换位置
    newObjList[dragIndex] = newObjList[hoverIndex];
    newObjList[hoverIndex] = temp;
    setObjList(newObjList);
  };

  //新增话术组
  const handleAdd = () => {
    let newObjList = JSON.parse(JSON.stringify(objList));
    const num = newObjList[sceneActiveIdx].list.length + 1;
    const newPanes = Array.from({ length: num }).map((_, index) => {
      const id = String(index + 1);
      return { title: `话术组${id}`, key: String(index) };
    });
    // setPanes([...newPanes]);
    newObjList[sceneActiveIdx].panes = newPanes;
    newObjList[sceneActiveIdx].list.push({
      tipsList: [],
      contentList: [],
    });
    setObjList(newObjList);
    setActiveKey(String(newObjList[sceneActiveIdx].list.length - 1));
  };

  //删除话术组
  const remove = (targetKey) => {
    const newObjList = JSON.parse(JSON.stringify(objList));
    const panes = newObjList[sceneActiveIdx].panes;
    const targetIndex = panes.findIndex((pane) => pane.key === targetKey);
    const newPanes = panes.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeKey) {
      setActiveKey(targetKey == 0 ? "0" : String(targetIndex - 1));
    } else {
      if (activeKey == "0") {
        setActiveKey("0");
      } else {
        setActiveKey(String(Number(activeKey) - 1));
      }
    }
    newObjList[sceneActiveIdx].list.splice(targetIndex, 1);
    newObjList[sceneActiveIdx].panes = newPanes.map((item, index) => ({
      title: `话术组${index + 1}`,
      key: String(index),
    }));
    setObjList(newObjList);
  };

  //改变话术相关名称标题
  const onChangeTitle = (e, type = null) => {
    const value = e.target.value;
    let newObjList = JSON.parse(JSON.stringify(objList));
    const tabIdx = Number(activeKey);
    if (type == "tips") {
      newObjList[sceneActiveIdx].list[tabIdx].tipsTitle = value;
    } else if (type == "script") {
      newObjList[sceneActiveIdx].list[tabIdx].title = value;
    } else {
      newObjList[sceneActiveIdx].list[tabIdx].name = value;
    }
    setObjList([...newObjList]);
  };

  //添加TIPS
  const onAdd = () => {
    const copyWriterText = WibotEditorRef.current.getHtml();
    if (!editorIsEmpty(copyWriterText)) {
      let newObjList = JSON.parse(JSON.stringify(objList));
      const tabIdx = Number(activeKey);
      if (newObjList[sceneActiveIdx].list[tabIdx].tipsList.length < 5) {
        newObjList[sceneActiveIdx].list[tabIdx].tipsList.push(copyWriterText);
        setObjList([...newObjList]);
        WibotEditorRef.current.clear();
      } else {
        message.error("TIPS最多添加5条！");
      }
    } else {
      message.error("TIPS不能为空！");
    }
  };

  //删除TIPS
  const onDel = (index) => {
    let newObjList = JSON.parse(JSON.stringify(objList));
    const tabIdx = Number(activeKey);
    newObjList[sceneActiveIdx].list[tabIdx].tipsList.splice(index, 1);
    setObjList([...newObjList]);
  };

  //资源库选择资源回调
  const materialCallback = (params) => {
    params.data.forEach((item, index) => {
      if (item.type == "Picture" && item.image) {
        item.fileId = [item.image];
      }
    });
    let newObjList = JSON.parse(JSON.stringify(objList));
    const tabIdx = Number(activeKey);
    newObjList[sceneActiveIdx].list[tabIdx].contentList = params.data;
    setObjList([...newObjList]);
    props.getMaterialList(params.data);
  };

  //监听活码对象数据变化改变对应的本地存储数据
  useEffect(() => {
    setTimeout(() => {
      localStorage.setItem("scriptObjList", JSON.stringify(objList));
    }, 500);
  }, [objList]);

  //选择时段-左
  const handleLeft = () => {
    let newObjList = JSON.parse(JSON.stringify(objList));
    const panes = newObjList[sceneActiveIdx].panes;
    if (activeKey == "0") {
      setActiveKey(String(panes.length - 1));
    } else {
      const newActiveKey = Number(activeKey) - 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //选择时段-右
  const handleRight = () => {
    let newObjList = JSON.parse(JSON.stringify(objList));
    const panes = newObjList[sceneActiveIdx].panes;
    if (activeKey == panes.length - 1) {
      setActiveKey("0");
    } else {
      const newActiveKey = Number(activeKey) + 1;
      setActiveKey(String(newActiveKey));
    }
  };

  //提交时获取修改后的所有对象数据
  const getModifyObjList = () => {
    return objList;
  };

  // 关于设置挽回语的操作
  const setRetrieveFun = (params = {}) => {
    const { specialFlag } = params;
    onRefMaterialListForm.current.setRetrieveFun({
      specialFlag: specialFlag,
      menuList: (specialFlag && ["copyWriter", "material"]) || null,
    });
  };

  return (
    <div className="ScriptContent">
      <div className="Row-flex">
        <div className="col-left">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAddOccasion()}
            style={{ marginBottom: "20px" }}
          >
            添加场合
          </Button>
          <DndProvider backend={HTML5Backend}>
            {objList.length > 0 &&
              objList.map((item, index) => (
                <DragSort
                  key={index}
                  id={index}
                  index={index}
                  changePosition={changePosition}
                >
                  <div
                    className={`occasion-item ${
                      index == sceneActiveIdx && !item.isEdit && "noEdit"
                    }`}
                  >
                    {
                      item.isEdit ? (
                        <Select
                          style={{ width: "120px" }}
                          placeholder="选择场合"
                          value={item.microScriptSceneId}
                          allowClear
                          options={occasionOption}
                          showSearch
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          onChange={(val, option) => {
                            onChangeSelect(val, option, index)
                          }}
                        />
                      ) : (
                        <span
                          className="item-text"
                          onClick={() => {
                            onFocusSelect(index)
                          }}
                        >
                          {item.microScriptSceneName}
                        </span>
                      )
                    }

                    {
                      item.isEdit ? (
                        <SaveOutlined
                          className="delete-icon"
                          onClick={() => {
                            handleSaveOrEdit(item, index)
                          }}
                        />
                      ) : (
                        <FormOutlined
                          className="delete-icon"
                          onClick={() => {
                            handleSaveOrEdit(item, index, "edit")
                          }}
                        />
                      )
                    }
                    {
                      objList.length > 1 && (
                        <DeleteOutlined
                          className="delete-icon"
                          onClick={() => {
                            handleDelete(item, index)
                          }}
                        />
                      )
                    }
                  </div>
                </DragSort>
              ))}
          </DndProvider>
        </div>
        <div className="col-right">
          <div className="ScriptTabs">
            <Tabs
              hideAdd
              onChange={onChange}
              activeKey={activeKey}
              type="editable-card"
              onEdit={onEdit}
              tabBarExtraContent={
                objList.length > 0 &&
                objList[sceneActiveIdx].panes.length > 3 ? (
                  {
                    left: (
                      <LeftOutlined
                        className="left-icon"
                        onClick={handleLeft}
                      />
                    ),
                    right: (
                      <div>
                        <RightOutlined
                          className="right-icon"
                          onClick={handleRight}
                        />
                        <div className="add-btn" onClick={handleAdd}>
                          添加
                        </div>
                      </div>
                    ),
                  }
                ) : (
                  <div className="add-btn" onClick={handleAdd}>
                    添加
                  </div>
                )
              }
            >
              {objList.length > 0 &&
                objList[sceneActiveIdx].panes.map((pane) => (
                  <TabPane
                    tab={pane.title}
                    key={pane.key}
                    closable={objList[sceneActiveIdx].panes.length != 1}
                  ></TabPane>
                ))}
            </Tabs>
            <div className="content-wrap">
              <FormItem
                label="话术组名称"
                name="scriptName"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[
                  { required: true, message: "请输入话术组名称（10字内）" },
                ]}
                labelCol={{ span: 5 }}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 12 } }}
              >
                <Input
                  placeholder="请输入话术组名称（10字内）"
                  maxLength={10}
                  allowClear
                  onChange={(e) => {
                    onChangeTitle(e)
                  }}
                />
              </FormItem>
              <h2>TIPS</h2>
              <FormItem
                label="TIPS标题"
                name="tipsTitle"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入TIPS标题（6字内）" }]}
                labelCol={{ span: 5 }}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 12 } }}
              >
                <Input
                  placeholder="请输入TIPS标题（6字内）"
                  maxLength={6}
                  allowClear
                  onChange={(e) => {
                    onChangeTitle(e, "tips")
                  }}
                />
              </FormItem>
              <FormItem
                label="TIPS内容"
                labelCol={{ span: 5 }}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}
                extra={
                  objList.length > 0 &&
                  objList[sceneActiveIdx].list[Number(activeKey)]?.tipsList
                    ?.length > 0 && (
                    <div className="tip-wrap">
                      {objList[sceneActiveIdx].list[
                        Number(activeKey)
                      ]?.tipsList?.map((item, index) => (
                        <div key={index} className="tip-item">
                          <WibotEditorView className="tipText" html={item} />
                          <CloseCircleOutlined
                            className="tagClose"
                            onClick={() => onDel(index)}
                          />
                        </div>
                      ))}
                    </div>
                  )
                }
              >
                <div className="flex-row">
                  <WibotEditor
                    ref={WibotEditorRef}
                    toolbarConfig={{
                      excludeKeys: [
                        "group-image",
                        "group-video",
                        "insertTable",
                      ],
                    }}
                    onChangeHtml={(html) => {}}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    style={{ marginLeft: "10px" }}
                    onClick={() => onAdd()}
                  >
                    添加
                  </Button>
                </div>
              </FormItem>
              <h2>话术</h2>
              <FormItem
                label="话术标题"
                name="title"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入话术标题（6字内）" }]}
                labelCol={{ span: 5 }}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 12 } }}
              >
                <Input
                  placeholder="请输入话术标题（6字内）"
                  maxLength={6}
                  allowClear
                  onChange={(e) => {
                    onChangeTitle(e, "script")
                  }}
                />
              </FormItem>
              <FormItem
                label="话术"
                className="formBtn"
                labelCol={{ span: 5 }}
                wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}
              >
                <MaterialListForm
                  params={{
                    formRef: props.formRef,
                    isNickname: false,
                    labelCol: 4,
                    materialAmount:
                      objList[sceneActiveIdx].list[Number(activeKey)]
                        .contentList.length,
                    extra: "请按发送顺序添加话术",
                  }}
                  // 监听回调
                  callback={(params) => {
                    materialCallback(params)
                  }}
                  ref={onRefMaterialListForm}
                />
              </FormItem>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
});

export default memo(ScriptTabs);
