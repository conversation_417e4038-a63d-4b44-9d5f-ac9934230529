.ScriptContent {
  .ScriptTabs {
    max-width: 500px;

    // .tabBar-box {
    //   .tab-bar {
    //     display: inline-block;
    //     // width: 65px;
    //     // height: 40px;
    //     text-align: center;
    //     // line-height: 40px;

    //     &.tab-bar_active {
    //       background: #f2f2f2;
    //     }
    //   }
    // }

    .content-wrap {
      // background: #f2f2f2;
      padding: 10px;

      .flex-row {
        display: flex;
      }

      .tip-wrap {
        // position: relative;
        // .tagClose {
        //   cursor: pointer;
        //   font-size: 20px;
        //   color: #d9d9d9;
        //   position: absolute;
        //   right: 0;

        //   &:hover {
        //     color: #1989fa;
        //   }
        // }
        .tip-item {
          display: flex;
          align-items: center;
          margin: 10px 0 0 0;

          p {
            margin-bottom: 0px;
          }
        }
      }

      .tipText {
        color: #000;
        vertical-align: middle;
      }

      .tagClose {
        cursor: pointer;
        font-size: 16px;
        color: #d9d9d9;
        vertical-align: middle;
        margin-left: 10px;
      }

    }

    .ant-tabs>.ant-tabs-nav {
      margin-bottom: 0px;
    }

    .left-icon {
      position: absolute;
      left: -20px;
      z-index: 99;
      cursor: pointer;
      top: 14px;
    }

    .right-icon {
      cursor: pointer;
    }

    .add-btn {
      display: inline-block;
      color: #5d8dd4;
      margin-left: 20px;
      cursor: pointer;
    }

    .msgList .template .ant-input-textarea {
      width: 240px;
    }
  }
}