/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/08 11:20
 * @LastEditTime: 2024/11/14 17:25
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/comps/TypeFormModal.jsx
 * @Description: '话本类型'
 */

import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Modal,
  Spin,
  Tooltip,
  Button,
  Table,
  Input,
  message,
  Row,
  Col,
} from "antd";
import { PlusOutlined, MenuOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import update from "immutability-helper";
import ListOperation from 'components/ListOperation/home';
import "./TypeFormModal.less";

const MaxDraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: 'MaxDraggableBodyRow',
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? " drop-over-downward" : " drop-over-upward",
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type: 'MaxDraggableBodyRow',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ""}`}
      style={{ cursor: "move", ...style }}
      {...restProps}
    />
  );
};
const MinDraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: 'MinDraggableBodyRow',
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? " drop-over-downward" : " drop-over-upward",
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type: 'MinDraggableBodyRow',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ""}`}
      style={{ cursor: "move", ...style }}
      {...restProps}
    />
  );
};

const TypeFormModal = (props) => {
  const { visible, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [maxDataSource, setMaxDataSource] = useState([]);
  const [minDataSource, setMinDataSource] = useState([]);
  const [initMaxDataSource, setInitMaxDataSource] = useState([]);
  const [initMinDataSource, setInitMinDataSource] = useState([]);
  const [activeParentId, setActiveParentId] = useState(null);
  const [isdrag, setIsdrag] = useState(true);
  const [specialFlag, setSpecialFlag] = useState(false);
  const maxColumns = [
    {
      title: "序号",
      width: "80px",
      dataIndex: "sort",
      align: "center",
      render: (value, record, index) => (
        <>
          <MenuOutlined style={{ marginRight: "10px" }} />
          {index + 1}
        </>
      ),
    },
    {
      title: "类型名称",
      width: "170px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Input
              value={value}
              maxLength={20}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(maxDataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, "");
                setMaxDataSource([...newDataSource]);
              }}
              placeholder="请输入20字内"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
        </>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const minColumns = [
    {
      title: "序号",
      width: "80px",
      dataIndex: "sort",
      align: "center",
      render: (value, record, index) => (
        <>
          <MenuOutlined style={{ marginRight: "10px" }} />
          {index + 1}
        </>
      ),
    },
    {
      title: "类型名称",
      width: "170px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Input
              value={value}
              maxLength={20}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(minDataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, "");
                setMinDataSource([...newDataSource]);
              }}
              placeholder="请输入20字内"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
        </>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index, "min"), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index, "min"), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index, "min"), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index, "min"), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const MaxDndComponents = {
    body: {
      row: MaxDraggableBodyRow,
    },
  };
  const MinDndComponents = {
    body: {
      row: MinDraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex, type = null) => {
      setLoading(true);
      const listData = type ? minDataSource : maxDataSource;
      const dragRow = listData[dragIndex];
      const data = update(listData, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      const params = {
        list: data.map((item) => item.id),
      };
      apiCall("/info/microScriptType/sort", "POST", params)
        .then((res) => {
          message.success("修改成功！");
          type
            ? setMinDataSource(
              update(listData, {
                $splice: [
                  [dragIndex, 1],
                  [hoverIndex, 0, dragRow],
                ],
              })
            )
            : setMaxDataSource(
              update(listData, {
                $splice: [
                  [dragIndex, 1],
                  [hoverIndex, 0, dragRow],
                ],
              })
            );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [maxDataSource, minDataSource]
  );

  useEffect(() => {
    if (visible) {
      getMaxGroupOptions();
    }
  }, [visible]);

  useEffect(() => {
    const isEdit = maxDataSource.some(item => item.isEdit) || minDataSource.some(item => item.isEdit)
    setIsdrag(!isEdit)
  }, [maxDataSource, minDataSource]);

  const getMaxGroupOptions = (type = null) => {
    setLoading(true);
    apiCall("/info/microScriptType", "GET")
      .then((res) => {
        setMaxDataSource(res);
        setInitMaxDataSource(res);
        const id = type ? activeParentId : (res?.length ? res[0].id : "");
        setActiveParentId(id);
        getMinGroupOptions(id);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getMinGroupOptions = (id) => {
    apiCall("/info/microScriptType", "GET", { parentId: id })
      .then((res) => {
        setMinDataSource([...res]);
        setInitMinDataSource([...res]);
      })
      .catch((err) => {
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = (type = null) => {
    let newDataSource = type ? minDataSource : maxDataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning("已存在输入项，请保存后再操作！");
      return false;
    }
    newDataSource.unshift({
      name: "",
      isEdit: true,
    });
    type ? setMinDataSource([...newDataSource]) : setMaxDataSource([...newDataSource]);
  };

  const handleEdit = (record, index, type = null) => {
    let newDataSource = type ? minDataSource : maxDataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource[index].isEdit = true;
    type ? setMinDataSource([...newDataSource]) : setMaxDataSource([...newDataSource]);
  };

  const handleKeep = (record, index, type = null) => {
    const { id, name } = record;
    if (name == "") {
      message.warning("名称不能为空！");
      return false;
    }
    setLoading(true);
    const data = {
      id: id ?? null,
      name: name,
      parentId: type ? activeParentId : null,
    };
    const apiUrl = id ? `/info/microScriptType/update/${id}` : "/info/microScriptType";
    apiCall(apiUrl, "POST", data)
      .then((res) => {
        message.success("保存成功！");
        if (type) {
          getMinGroupOptions(activeParentId);
          return;
        }
        getMaxGroupOptions("save");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index, type = null) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/info/microScriptType/delete/${id}`, "POST")
      .then((res) => {
        message.success("删除成功！");
        if (type) {
          getMinGroupOptions(activeParentId);
          return;
        }
        getMaxGroupOptions();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index, type = null) => {
    const { id } = record;
    let newDataSource = type ? minDataSource : maxDataSource;
    let newInitMinDataSource = type ? initMinDataSource : initMaxDataSource;
    if (id) {
      newDataSource[index].isEdit = false;
      const findData = newInitMinDataSource.find(item => item.id == id);
      findData && (newDataSource[index].name = findData.name);
    } else {
      newDataSource.splice(index, 1);
    }
    type ? setMinDataSource([...newDataSource]) : setMaxDataSource([...newDataSource]);
  };

  // 点击整行选择
  const onSelectRow = (record) => {
    if (record.id) {
      setActiveParentId(record.id);
      getMinGroupOptions(record.id);
      setSpecialFlag(record.specialFlag);
    }
  };

  const setClassName = (
    record,
    index // record代表表格行的内容，index代表行索引
  ) =>
    // 判断索引相等时添加行的高亮样式
    // activeParentId
    record.id ===
      (activeParentId || (maxDataSource.length > 0 && maxDataSource[0].id)) &&
      record.id != undefined
      ? "l-table-row-active"
      : "";

  return (
    <Modal
      className="TypeFormModal"
      visible={visible}
      width={850}
      title="话本类型"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      onCancel={() => {
        setLoading(false);
        setMaxDataSource([]);
        setMinDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <Spin spinning={loading}>
        <Row gutter={16}>
          <Col span={12} className="left-col">
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                height: "32px",
                margin: "10px 0",
              }}
            >
              大类
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleAdd()}
              >
                新增
              </Button>
            </div>
            <DndProvider backend={HTML5Backend}>
              <Table
                rowKey="id"
                pagination={false}
                dataSource={maxDataSource}
                columns={maxColumns}
                scroll={{ y: 500 }}
                rowClassName={setClassName} // 表格行点击高亮
                components={isdrag && MaxDndComponents}
                onRow={isdrag ? (record, index) => ({ index, moveRow: (dragIndex, hoverIndex) => moveRow(dragIndex, hoverIndex), onClick: () => onSelectRow(record) }) : null
                }
              />
            </DndProvider>
          </Col>
          <Col span={12}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                height: "32px",
                margin: "10px 0",
              }}
            >
              小类
              {!specialFlag && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAdd("min")}
                >
                  新增
                </Button>
              )}
            </div>
            <DndProvider backend={HTML5Backend}>
              <Table
                rowKey="id"
                dataSource={minDataSource}
                columns={minColumns}
                pagination={false}
                scroll={{ y: 500 }}
                components={isdrag && MinDndComponents}
                onRow={isdrag ? (record, index) => ({ index, moveRow: (dragIndex, hoverIndex) => moveRow(dragIndex, hoverIndex, "min") }) : null}
              />
            </DndProvider>
          </Col>
        </Row>
      </Spin>
    </Modal>
  );
};

export default TypeFormModal;
