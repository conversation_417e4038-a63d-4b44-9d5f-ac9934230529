/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/09 11:34
 * @LastEditTime: 2025/07/03 10:50
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/detail.jsx
 * @Description: '微话本-详情'
 */

import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Tag,
  Space,
  Image,
  Tabs,
  Form,
  DatePicker,
  Select,
} from "antd"
import { QuestionCircleOutlined } from "@ant-design/icons"
import { apiCall } from "common/utils"
import { timeStamp, getDay } from "common/date"
import moment from "moment"
import { qs2obj } from "common/object"
import AppletCard from "components/AppletCard/home"
import LinkCard from "components/LinkCard/home"
import SysDictLabel from "components/select/SysDictLabel"
import { Line } from "@ant-design/plots"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./details.less"

const { TabPane } = Tabs
const FormItem = Form.Item
const { RangePicker } = DatePicker

const MicroScriptDetails = (props) => {
  const [id, setId] = useState(null)
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [userInfo, setUserInfo] = useState({})
  const [tabsTrendType, setTabsTrendType] = useState("SHARER_COUNT")
  const [chartsData, setChartsData] = useState(null)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([])
  const [msgList, setMsgList] = useState([])
  const [sceneOptions, setSceneOptions] = useState([])
  const [scriptOptions, setScriptOptions] = useState([])
  const [sceneId, setSceneId] = useState("")
  const [scriptId, setScriptId] = useState("")
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        // const companyName = <span style={record.type == '微信用户' ? { color: '#07c160' } : { color: '#f59a23' }}>{record.departmentName}</span>;
        const title = <div>{value}</div>
        const content = (
          <div style={{ display: "flex" }}>
            {record.avatar && <Avatar size={40} src={record.avatar} />}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        )
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        )
      },
    },
    {
      title: "转发时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "转发内容",
      width: "250px",
      dataIndex: "content",
      key: "content",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          {value.type == "Article" && <LinkCard data={value} />}
          {(value.type == "pageArticle" ||
            value.type == "FORM" ||
            value.type == "Product") && <LinkCard data={value} />}
          {value.type == "copyWriter" && (
            <Tooltip placement="topLeft" title={value.content}>
              {value.content}
            </Tooltip>
          )}
          {value.type == "Video" && (
            <FileHOC
              src={
                value.fileId && value.fileId.length > 0 ? value.fileId[0] : ""
              }
            >
              {(url) => (
                <video
                  style={{ maxWidth: "200px", maxHeight: "100px" }}
                  controls
                  src={url}
                />
              )}
            </FileHOC>
          )}
          {(value.type == "Poster" ||
            value.type == "Picture" ||
            value.type == "QrCode") && (
            <>
              {value.fileId?.map((item, index) => (
                <FileHOC src={item || "error"}>
                  {(url) => (
                    <Image
                      key={index}
                      width={60}
                      height={60}
                      src={url}
                      fallback="images/fallbackImg.png"
                      style={{ objectFit: "cover" }}
                      preview
                    />
                  )}
                </FileHOC>
              ))}
            </>
          )}
          {value.type == "MINI_PROGRAM" && <AppletCard data={value || {}} />}
        </div>
      ),
    },
    {
      title: "内容类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel
          dataset="Resource_Type_Name"
          dictkey={record.content?.type}
        />
      ),
    },
    {
      title: "场合及话术组",
      width: "160px",
      dataIndex: "microSceneName",
      key: "microSceneName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip
          placement="topLeft"
          title={value + `-话术组${record.microScriptItemIndex}`}
        >
          {value + `-话术组${record.microScriptItemIndex}`}
        </Tooltip>
      ),
    },
  ]

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      init(id)
    }
  }, [])

  const init = async (id) => {
    await fetchList({ id })
    await getTabEcharts({ detailId: id })
    await getDataDetails({ detailId: id })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    await apiCall(`/info/microScript/detail/${id}`, "GET")
      .then((res) => {
        const data = { ...res }
        data.startTime =
          data.startTime && moment(data.startTime).format("YYYY/MM/DD HH:mm")
        data.endTime =
          data.endTime && moment(data.endTime).format("YYYY/MM/DD HH:mm")
        setUserInfo(data)
        const sceneData = data.itemVOList.map((item) => ({
          label: item.microScriptSceneName,
          value: item.microScriptSceneId,
        }))
        const scriptData =
          data.itemVOList && data.itemVOList.length > 0
            ? data.itemVOList[0].list.map((item, index) => ({
                label: `话术组${index + 1}`,
                value: item.id,
              }))
            : []
        setSceneOptions(sceneData)
        setScriptOptions(scriptData)
        setSceneId(sceneData.length > 0 ? sceneData[0].value : [])
        setScriptId(scriptData.length > 0 ? scriptData[0].value : [])
        setMsgList(
          data.itemVOList && data.itemVOList.length > 0
            ? data.itemVOList[0].list[0]?.contentList || []
            : []
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getDataDetails = async (params = {}) => {
    setLoading(true)
    const { pagination, query, detailId, detailType } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const data = {
      microScriptId: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    }
    let apiUrl = "/info/microScript/detail/page"
    apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, pages, size, total } = res
        setDataSource(records)
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getTabEcharts = async (params = {}) => {
    setLoading(true)
    const { tabType, detailId, time } = params
    const data = {
      id: detailId || id,
      type: tabType || tabsTrendType,
      startDate:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format("YYYY-MM-DD"),
      endDate:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format("YYYY-MM-DD"),
    }
    apiCall("/info/microScript/detail/chart", "GET", data)
      .then((res) => {
        const { list } = res
        setChartsData(list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    getDataDetails({ pagination })
  }

  const onChangeTabsDataTrend = (type) => {
    const time = formForm.getFieldValue("time")
    setTabsTrendType(type)
    getTabEcharts({
      time: time && [
        time[0].format("YYYY-MM-DD"),
        time[1].format("YYYY-MM-DD"),
      ],
      tabType: type,
    })
  }

  const onChangeQuickTime = async (value) => {
    let time = []
    switch (value) {
      case "0":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ]
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-1":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ]
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-7":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-15":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-30":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getTabEcharts({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
    }
    if (value) {
      await formForm.setFieldsValue({
        time,
      })
    }
  }

  const onChangeTime = async (date, dateString) => {
    getTabEcharts({ time: date ? dateString : null })
    formForm.setFieldsValue({
      quickTime: null,
    })
  }

  const onChangeScene = (val) => {
    setSceneId(val)
    const data = userInfo.itemVOList.find(
      (item) => item.microScriptSceneId == val
    )
    const list =
      data?.list?.map((item, index) => ({
        label: `话术组${index + 1}`,
        value: item.id,
      })) || []
    setScriptOptions(list)
    setScriptId(list[0]?.value)
    setMsgList(data?.list[0]?.contentList || [])
  }

  const onChangeScript = (val) => {
    setScriptId(val)
    const data = userInfo.itemVOList.find(
      (item) => item.microScriptSceneId == sceneId
    )
    setMsgList(data.list.find((item) => item.id == val).contentList || [])
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const DemoLine = (data) => {
    const config = {
      data,
      xField: "date",
      yField: "number",
      label: {},
      point: {
        size: 5,
        shape: "diamond",
        style: {
          fill: "white",
          stroke: "#5B8FF9",
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: "#000",
            fill: "red",
          },
        },
      },
      interactions: [
        {
          type: "marker-active",
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value
          },
        },
      },
      meta: {
        number: {
          alias: "总数",
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    }
    return <Line {...config} />
  }

  return (
    <div className="MicroScriptDetails">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title="话本详情"
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={16}>
            <div>
              <Card title={<>基础信息</>}>
                <Row>
                  <Col span={13}>
                    {
                      // JSON.stringify(userInfo) != '{}' ?
                      <div className="header-box">
                        <div className="info">
                          <div className="taskName">
                            <span style={{ marginRight: "10px" }}>
                              {userInfo.name}
                            </span>
                            <Tag
                              color={
                                userInfo.shelfState == "Added" && "#70b603"
                              }
                            >
                              <SysDictLabel
                                dataset="RESOURCE_STATUS"
                                dictkey={userInfo.shelfState}
                              />
                            </Tag>
                          </div>
                          <Space wrap>
                            <span>
                              {userInfo.typeName}&nbsp;|&nbsp;
                              {userInfo.sceneCount}个场合&nbsp;|&nbsp;
                              {userInfo.itemCount}个话术组&nbsp;|&nbsp;
                              {userInfo.itemContentCount}条话术
                            </span>
                          </Space>
                          <div>
                            <span>话本简介：</span>
                            <span className="taskContent">
                              {userInfo.description}
                            </span>
                          </div>
                          <div style={{ display: "flex" }}>
                            <span>应用指南：</span>
                            <WibotEditorView html={userInfo.guide} />
                          </div>
                          <div>
                            <span>上下架时间：</span>
                            <span>
                              {userInfo.startTime}~{userInfo.endTime}
                            </span>
                          </div>
                          {userInfo.updateEmployeeName &&
                            userInfo.updateTime && (
                              <div>
                                <span>更新：</span>
                                <span>
                                  {userInfo.updateEmployeeName}&nbsp;
                                  {userInfo.updateTime}
                                </span>
                              </div>
                            )}
                          <div>
                            <span>创建：</span>
                            <span>
                              {userInfo.createEmployeeName}&nbsp;
                              {userInfo.createTime}
                            </span>
                          </div>
                        </div>
                      </div>
                      //  : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    }
                  </Col>
                  <Col span={11}>
                    {
                      // JSON.stringify(statisticalInfo) != '{}' ?
                      <Row className="data-screening">
                        <Col span={12}>
                          <span className="num">
                            {userInfo.sharerCount || 0}
                          </span>
                          <Tooltip title="同一客户经理转发话术多次算一次，转发整个话术组时以实际转发数量算人数">
                            <p className="tip">
                              累计转发人数
                              <QuestionCircleOutlined />
                            </p>
                          </Tooltip>
                          <p className="visit">
                            今日转发人数：{userInfo.sharerTodayCount}
                          </p>
                        </Col>
                        <Col span={12}>
                          <span className="num">
                            {userInfo.shareCount || 0}
                          </span>
                          <Tooltip title="同一客户经理转发话术多次算多次，转发整个话术组时以实际转发数量算次数">
                            <p className="tip">
                              累计转发次数
                              <QuestionCircleOutlined />
                            </p>
                          </Tooltip>
                          <p className="visit">
                            今日转发次数：{userInfo.shareTodayCount}
                          </p>
                        </Col>
                      </Row>
                      // : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    }
                  </Col>
                </Row>
              </Card>
              <br />
              <Card bordered={false} title="数据趋势">
                <Tabs
                  activeKey={tabsTrendType}
                  destroyInactiveTabPane
                  onChange={onChangeTabsDataTrend}
                >
                  <TabPane tab="转发人数" key="SHARER_COUNT"></TabPane>
                  <TabPane tab="转发次数" key="SHARE_COUNT"></TabPane>
                </Tabs>
                <Form layout={"inline"} form={formForm}>
                  <FormItem
                    label="统计时间"
                    name="time"
                    initialValue={[moment(getDay(-30)), moment(getDay(-1))]}
                  >
                    <RangePicker
                      allowClear={false}
                      format="YYYY-MM-DD"
                      onChange={onChangeTime}
                    />
                  </FormItem>
                  <FormItem
                    label="快捷时间"
                    name="quickTime"
                    initialValue={"-30"}
                  >
                    <Select
                      style={{ width: "200px" }}
                      options={[
                        {
                          label: "今天",
                          value: "0",
                        },
                        {
                          label: "昨天",
                          value: "-1",
                        },
                        {
                          label: "最近7天",
                          value: "-7",
                        },
                        {
                          label: "最近15天",
                          value: "-15",
                        },
                        {
                          label: "最近30天",
                          value: "-30",
                        },
                      ]}
                      onChange={onChangeQuickTime}
                    />
                  </FormItem>
                </Form>
                {(chartsData && DemoLine(chartsData)) || (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <Card style={{ height: " 100%" }}>
              <h2 className="card-title">话术预览</h2>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <Select
                  placeholder="场合名称"
                  value={sceneId}
                  options={sceneOptions}
                  allowClear
                  showSearch
                  style={{ width: "140px" }}
                  onChange={onChangeScene}
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
                <Select
                  placeholder="话术组"
                  value={scriptId}
                  options={scriptOptions}
                  allowClear
                  showSearch
                  style={{ width: "100px" }}
                  onChange={onChangeScript}
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </div>
              <br />
              <WibotMaterialPreview type="session" listData={msgList} />
            </Card>
          </Col>
        </Row>
        <br />
        <Card bordered={false} title="数据明细(转发记录)">
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
    </div>
  )
}

export default withRouter(MicroScriptDetails)
