/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/08 14:04
 * @LastEditTime: 2025/05/13 11:40
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/form.jsx
 * @Description: '微话本-创建编辑'
 */

import React, { useEffect, useRef, useState } from "react"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Image,
  TreeSelect,
  message,
  DatePicker,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import { removeInputEmpty, editorIsEmpty } from "common/regular"
import { sceneRecursionTreeData } from "common/tree"
import { qs2obj } from "common/object"
import ScriptContent from "./comps/ScriptContent"
import { clearCache } from "react-router-cache-route"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEditor from "components/WibotEditor/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import "./form.less"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { TextArea } = Input

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
}
const MicroScriptForm = (props) => {
  const [formForm] = Form.useForm()
  const onRefScriptFrame = useRef()
  const GuideWibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [status, setStatus] = useState(null)
  const [msgList, setMsgList] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [typeOptions, setTypeOptions] = useState([])
  const [scriptName, setScriptName] = useState("")
  const [isRetrieve, setIsRetrieve] = useState(false)

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    getInfoTypeOptions(id)
    if (id) {
      setId(id)
    }
    init(id)
  }, [])

  const init = (id = null) => {
    if (id) {
      const params = {
        id,
      }
      fetchList(params)
    }
  }

  // 获取话本类型
  const getInfoTypeOptions = (id) => {
    setLoading(true)
    apiCall("/info/microScriptType/tree", "GET")
      .then((res) => {
        const treeData = sceneRecursionTreeData(res)
        setTypeOptions(treeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        !id && setLoading(false)
      })
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    const { id } = params
    apiCall(`/info/microScript/${id}`, "GET")
      .then((res) => {
        const {
          name,
          description,
          microScriptTypeId,
          startTime,
          endTime,
          shelfState,
          itemVOList,
          specialFlag,
          visibleScopeDepartmentId,
          guide,
        } = res
        formForm.setFieldsValue({
          name,
          guide,
          description,
          microScriptTypeId,
          visibleScopeDepartmentId,
          time: [
            startTime ? moment(startTime) : null,
            endTime ? moment(endTime) : null,
          ],
        })
        setStatus(shelfState)
        setScriptName(name)
        setIsRetrieve(specialFlag)
        specialFlag &&
          message.warning("话术类型【流失客户挽回词】不可添加非文本话术")
        onRefScriptFrame.current.setRetrieveFun({
          specialFlag: specialFlag,
        })
        let timer = setTimeout(() => {
          GuideWibotEditorRef.current.setHtml(guide)
          clearTimeout(timer)
        }, 300)
        onRefScriptFrame.current.getInitObjData(itemVOList)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getMaterialList = (data) => {
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      const objList = onRefScriptFrame.current.getModifyObjList()
      if (!(objList.length > 0)) {
        message.error("请至少添加一个场合！")
        return
      } else {
        for (let i = 0; i < objList.length; i++) {
          let list = objList[i].list
          if (list.some((atem) => atem.contentList.length == 0)) {
            message.error("话术内容不能为空！")
            return
          } else if (
            list.some((atem) => !atem.name) ||
            list.some((atem) => !atem.tipsTitle) ||
            list.some((atem) => !atem.title)
          ) {
            message.error("请补充完整话术组必填内容！")
            return
          } else if (
            isRetrieve &&
            !list.some(
              (atem) =>
                atem.contentList.length > 0 &&
                atem.contentList.every((btem) => btem.type == "copyWriter")
            )
          ) {
            // 话术类型【流失客户挽回词】不可添加非文本话术
            message.warning("话术类型【流失客户挽回词】不可添加非文本话术")
            return
          }
        }
      }
      setLoading(true)
      const {
        name,
        microScriptTypeId,
        description,
        time,
        guide,
        visibleScopeDepartmentId,
      } = formData
      const startTime = moment(time[0]._d).format("YYYY-MM-DD HH:mm")
      const endTime = moment(time[1]._d).format("YYYY-MM-DD HH:mm")
      let sceneSort = []
      objList.forEach((item, index) => {
        sceneSort.push(item.microScriptSceneId)
        item.list.forEach((atem, andex) => {
          atem.microScriptSceneId = item.microScriptSceneId
        })
      })
      const data = {
        id: id ?? null,
        shelfState: id ? status : type,
        name,
        guide,
        microScriptTypeId,
        description,
        startTime,
        endTime,
        sceneSort,
        visibleScopeDepartmentId,
        itemDTOList: objList,
      }
      const apiUrl = id ? `/info/microScript/update/${id}` : "/info/microScript"
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          localStorage.removeItem("scriptObjList")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/microScript")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleGoBack = () => {
    props.history.go(-1)
    localStorage.removeItem("scriptObjList")
  }

  return (
    <div className="MicroScriptForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? `编辑话本（${scriptName}）` : "创建话本"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Form {...layout} form={formForm}>
          <Card>
            <h2 className="card-title">话本信息</h2>

            <FormItem
              label="话本名称"
              name="name"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: "请输入话本名称" }]}
            >
              <Input
                placeholder="请输入话本名称（20字内）"
                maxLength={20}
                allowClear
              />
            </FormItem>

            <FormItem
              name="time"
              label="上下架时间"
              rules={[{ required: true, message: "请选择上下架时间" }]}
            >
              <RangePicker
                showTime={{ format: "HH:mm" }}
                format="YYYY-MM-DD HH:mm"
                inputReadOnly
                style={{ width: "100%" }}
              />
            </FormItem>

            <FormItem
              name="microScriptTypeId"
              label="话本类型"
              rules={[{ required: true, message: "请选择话本类型" }]}
            >
              <TreeSelect
                allowClear
                showArrow
                showSearch
                treeData={typeOptions}
                treeDefaultExpandAll
                placeholder="请选择话本类型"
                treeNodeFilterProp="title"
                onChange={(value, label, extra) => {
                  const { specialFlag = false } =
                    extra?.triggerNode?.props || {}
                  specialFlag &&
                    message.warning(
                      "话术类型【流失客户挽回词】不可添加非文本话术"
                    )
                  onRefScriptFrame.current.setRetrieveFun({
                    specialFlag: specialFlag,
                  })
                  setIsRetrieve(specialFlag)
                }}
              />
            </FormItem>

            <FormItem
              name="visibleScopeDepartmentId"
              label="可见范围"
              rules={[{ required: true, message: "请选择可见范围" }]}
            >
              <ETypeTransferModal title="可见范围" onlyDepartment />
            </FormItem>

            <FormItem
              name="description"
              label="话本介绍"
              rules={[{ required: true, message: "请输入话本介绍" }]}
            >
              <TextArea
                placeholder="请输入话本介绍（100字）"
                allowClear
                showCount
                maxLength={100}
                autoSize={{ minRows: 2, maxRows: 7 }}
                className="textArea-mid"
              />
            </FormItem>

            <FormItem
              name="guide"
              label="应用指南"
              rules={[{ required: true, message: "请输入应用指南" }]}
            >
              <WibotEditor
                ref={GuideWibotEditorRef}
                toolbarConfig={{
                  excludeKeys: ["group-image", "group-video"],
                }}
                editorConfig={{
                  placeholder: "请输入内容(建议100字内)...",
                }}
                onChangeHtml={(html) => {
                  formForm.setFieldValue(
                    "guide",
                    editorIsEmpty(html) ? "" : html
                  )
                }}
              />
            </FormItem>
          </Card>
          <br />
          <Card>
            <Row gutter={10}>
              <Col xs={24} lg={16}>
                <h2 className="card-title">话本内容</h2>
                <ScriptContent
                  formRef={formForm}
                  ref={onRefScriptFrame}
                  getMaterialList={getMaterialList}
                  detailId={id}
                />
                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    {id ? (
                      <Button type="primary" onClick={() => onSubmit()}>
                        保存
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="primary"
                          onClick={() => onSubmit("notIssue")}
                        >
                          仅保存
                        </Button>
                        <Button
                          type="primary"
                          onClick={() => onSubmit("issued")}
                        >
                          保存并发布
                        </Button>
                      </>
                    )}
                  </Space>
                </div>
              </Col>
              <Col xs={24} lg={8}>
                <WibotMaterialPreview type="session" listData={msgList} />
              </Col>
            </Row>
          </Card>
        </Form>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
    </div>
  )
}

export default MicroScriptForm
