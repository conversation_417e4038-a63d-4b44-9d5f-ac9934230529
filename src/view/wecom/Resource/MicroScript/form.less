.MicroScriptForm {
  .ant-card {
    .card-title {
      font-size: 18px;
      font-weight: bold;
    }

    .Row-flex {
      display: flex;
      flex-direction: row;

      .col-left {
        width: 190px;
        border-right: 1px solid #f0f2f5;
      }

      .col-right {
        flex: 1;
      }
    }

    .occasion-item {
      margin-bottom: 20px;
      padding: 5px 0;
      padding-left: 10px;
      box-sizing: border-box;

      &.noEdit {
        background: #dcf4ff;
        // cursor: pointer;
      }

      .item-text {
        display: inline-block;
        width: 110px;
      }

      .delete-icon {
        margin-left: 10px;
        cursor: pointer;

        &:hover {
          color: #5d8dd4;
        }
      }
    }

    .textArea-mid {
      textarea {
        height: 100px !important;
      }
    }
  }
}
