/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/08 10:45
 * @LastEditTime: 2025/07/03 10:57
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/MicroScript/home.jsx
 * @Description: '微话本'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import {
  Form,
  Input,
  Button,
  Table,
  Card,
  Tooltip,
  DatePicker,
  Select,
  TreeSelect,
  message,
  Switch,
  Typography,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { removeInputEmpty } from "common/regular";
import { recursionTagKeyTreeData } from "common/tree";
import moment from "moment";
import OccasionFormModal from "./comps/OccasionFormModal";
import TypeFormModal from "./comps/TypeFormModal";
import OperateModal from "components/Modal/OperateModal/index";
import SysDictLabel from "components/select/SysDictLabel";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import ExportModal from "./comps/ExportModal";
import ListOperation from 'components/ListOperation/home';
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;
const { Paragraph } = Typography;

const MicroScript = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [typeOptions, setTypeOptions] = useState([]);
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [occasionFormParams, setOccasionFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [occasionOption, setOccasionOption] = useState([]);
  const [exportParams, setExportParams] = useState({ visible: false });
  // const [disableDepartmentIds, setDisableDepartmentIds] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "话本名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: "话本简介",
      width: "160px",
      dataIndex: "description",
      key: "description",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: "应用指南",
      width: "160px",
      dataIndex: "guide",
      key: "guide",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        let content = <WibotEditorView html={value} />
        return (
          <Tooltip placement="topLeft" title={content}>
            <span style={{ textAlign: "left" }}>{content}</span>
          </Tooltip>
        )
      },
    },
    {
      title: "话本类型",
      width: "160px",
      dataIndex: "typeName",
      key: "typeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: "适用场合",
      width: "160px",
      dataIndex: "sceneList",
      key: "sceneList",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        const content = value.map((item, idx) => (
          <span key={idx}>
            {item}
            {idx != value.length - 1 && "、"}
          </span>
        ))
        return (
          <Tooltip title={content} placement="topLeft">
            {content}
          </Tooltip>
        )
      },
    },
    {
      title: "统计数据",
      width: "160px",
      dataIndex: "shareCount",
      key: "shareCount",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          转发次数：{value ?? 0}
          <br />
          转发人数：{record?.sharerCount ?? 0}
        </div>
      ),
    },
    {
      title: "上下架状态",
      width: "160px",
      dataIndex: "shelfState",
      key: "shelfState",
      align: "center",
      render: (value, record, index) => (
        <>
          {(value == "notIssue" || value == "issued") && (
            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
          )}
          {(value == "Added" || value == "soldOut") && (
            <Switch
              className="stateSwitch"
              checkedChildren="已上架"
              unCheckedChildren="已下架"
              checked={value == "Added"}
              onChange={(checked) => {
                onChangeSwitchStatus(checked, record)
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "上下架时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: "更新人/更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDetail(record), name: "详情" },
        ]
        if (record.shelfState == "notIssue") {
          opts.push({
            onClick: () => handleShelves(record, "issued"),
            name: "发布",
          })
        }
        if (record.shelfState == "issued") {
          opts.push({
            onClick: () => handleShelves(record, "notIssue"),
            name: "取消发布",
          })
        }
        if (record.sharerCount == 0 && record.shareCount == 0) {
          opts.push({ onClick: () => handleDelete(record), name: "删除" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    getInfoTypeOptions();
    getOccasionOption();
    fetchList();
  }, []);

  // 话本场合
  const getOccasionOption = () => {
    apiCall("/info/microScriptScene", "GET")
      .then((res) => {
        setOccasionOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取话本类型
  const getInfoTypeOptions = () => {
    apiCall("/info/microScriptType/tree", "GET")
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res);
        setTypeOptions([
          {
            title: "全选",
            value: "",
            key: "",
            children: treeData,
          },
        ]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.updateTime;
      }
      if (formData.onShelfTime) {
        formData.minOnShelfTime = moment(formData.onShelfTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxOnShelfTime = moment(formData.onShelfTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.onShelfTime;
      }
      formData.microScriptTypeIdList =
        formData.microScriptTypeIdList?.join(",") || null;
      formData.createDeptIdList = formData.createDeptIdList?.join(",") || null;
      formData.updateDeptIdList = formData.updateDeptIdList?.join(",") || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall("/info/microScript", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record;
    apiCall(
      `/info/microScript/update/shelfState/${id}?shelfState=${checked ? "Added" : "soldOut"
      }`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);

      })
      .finally(() => { });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 导出
  const handleExport = () => {
    setExportParams({
      visible: true,
      // disableDepartmentIds,
      onCancel: () => {
        setExportParams({ visible: false });
      },
    });
  };

  const handleOccasion = () => {
    setOccasionFormParams({
      visible: true,
      title: "话本场合",
      onCancel: () => {
        setOccasionFormParams({ visible: false });
        getOccasionOption();
      },
    });
  };

  const handleType = () => {
    setTypeFormParams({
      visible: true,
      title: "话本类型",
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getInfoTypeOptions();
      },
    });
  };

  const handleAdd = () => {
    props.history.push("/wecom/microScript/form");
  };

  // 发布
  const handleShelves = (record, state) => {
    const { id } = record;
    apiCall(
      `/info/microScript/update/shelfState/${id}?shelfState=${state}`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);

      })
      .finally(() => { });
  };

  const handleDetail = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/microScript/detail",
      search: `?id=${id}`,
    });
  };

  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/microScript/form",
      search: `?id=${id}`,
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除话本【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/info/microScript/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {

            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onTableChange = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="MicroScript">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="nameOrdesc"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input
              placeholder="话本名称、话本简介、应用指南"
              allowClear
              style={{ width: "240px" }}
            />
          </FormItem>
          <FormItem name="microScriptSceneId">
            <Select
              placeholder="话本场合"
              fieldNames={{ label: "name", value: "id" }}
              options={occasionOption}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="microScriptTypeIdList">
            <TreeSelect
              placeholder="话本类型"
              treeData={typeOptions}
              treeCheckable
              treeDefaultExpandedKeys={[""]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem
            name="updateDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="更新人" />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="onShelfTime" label="上下架时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
            <Button type="primary" onClick={() => handleOccasion()}>
              话本场合
            </Button>
            <Button type="primary" onClick={() => handleType()}>
              话本类型
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              创建话本
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onTableChange}
        />
      </Card>
      <OccasionFormModal params={occasionFormParams} />
      <TypeFormModal params={typeFormParams} />
      <OperateModal params={operateParams} />
      {/* 导出对话框 */}
      <ExportModal params={exportParams} />
    </div>
  );
};

export default withRouter(MicroScript);
