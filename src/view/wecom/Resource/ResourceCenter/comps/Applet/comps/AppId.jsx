/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/08 09:33
 * @LastEditTime: 2023/02/14 14:59
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ResourceCenter\comps\Applet\comps\AppId.jsx
 * @Description: '小程序appId'
 */
import React from 'react';
import { Card, Button } from 'antd';

const AppletAppId = (props) => {

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='AppletAppId'>
      <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} bordered={false} bodyStyle={{ display: 'none' }}></Card>
      <br />
      <Card bordered={false} style={{ textAlign: 'center' }}>
        <h2>如何查看小程序AppID</h2>
        <div style={{ width: '600px', margin: '0 auto', textAlign: 'left' }}>
          <h2>手机端</h2>
          <p style={{ marginTop: '10px' }}>在微信中找到并点击查看需要添加的小程序，根据以下步骤即可获取小程序AppID</p>
          <img src='images/appId_1.png'></img>
          <h2 style={{ marginTop: '20px' }}>电脑端</h2>
          <p style={{ marginTop: '10px' }}>1.访问并登录<a href='https://mp.weixin.qq.com/'>微信公众平台</a>，滑动至页面底部，点击左下角【设置】</p>
          <img src='images/appId_2.png'></img>
          <p style={{ marginTop: '10px' }}>二、进入【设置】页面，滑动至页面底部，在账号信息中找到并复制AppID</p>
          <img src='images/appId_3.png'></img>
        </div>
      </Card>
    </div>
  );
}
export default AppletAppId;
