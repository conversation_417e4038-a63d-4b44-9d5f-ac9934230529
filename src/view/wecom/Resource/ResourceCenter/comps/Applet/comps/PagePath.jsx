/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/30 16:08
 * @LastEditTime: 2023/02/14 15:00
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ResourceCenter\comps\Applet\comps\PagePath.jsx
 * @Description: '小程序页面路径'
 */
import React from 'react';
import { Card, Button } from 'antd';

const AppletPagePath = (props) => {

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='AppletPagePath'>
      <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} bordered={false} bodyStyle={{ display: 'none' }}></Card>
      <br />
      <Card bordered={false} style={{ textAlign: 'center' }}>
        <h2>如何查看小程序页面路径</h2>
        <div style={{ width: '600px', margin: '0 auto', textAlign: 'left' }}>
          <p style={{ marginTop: '10px' }}>1.访问并登录<a href='https://mp.weixin.qq.com/'>微信公众平台</a>，鼠标移动到【工具】，点击选项【生成小程序码】</p>
          <img src='images/lj_1.png'></img>
          <p style={{ marginTop: '10px' }}>2.鼠标移动到【获取更多页面路径】，填入项目成员的微信号，点击【开启】即可</p>
          <img src='images/lj_2.png'></img>
          <p style={{ marginTop: '10px' }}>3.使用已开启权限的项目成员微信，打开要获取页面的小程序，按以下顺序操作</p>
          <p style={{ textAlign: 'center' }}><img src='images/lj_3.png'></img></p>
          <p style={{ marginTop: '10px' }}>注：如果添加的小程序路径有误，客户在微信端访问小程序会提示页面链接无效</p>
          <p style={{ textAlign: 'center' }}><img src='images/lj_4.png'></img></p>
        </div>
      </Card>
    </div>
  );
}
export default AppletPagePath;
