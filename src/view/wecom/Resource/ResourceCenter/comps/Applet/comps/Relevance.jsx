/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/30 11:34
 * @LastEditTime: 2023/02/14 16:31
 * @LastEditors: Janaeiw
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ResourceCenter\comps\Applet\comps\Relevance.jsx
 * @Description: '小程序-关联企业'
 */

import React from 'react';
import { Card, Button } from 'antd';

const AppletRelevance = (props) => {

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='AppletRelevance'>
      <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} bordered={false} bodyStyle={{ display: 'none' }}></Card>
      <br />
      <Card bordered={false} style={{ textAlign: 'center' }}>
        <h2>微信小程序关联企业微信</h2>
        <div style={{ width: '600px', margin: '0 auto', textAlign: 'left' }}>
          <p style={{ marginTop: '10px' }}>1.访问并登录<a href='https://work.weixin.qq.com/wework_admin/frame#apps/minAppIndex'>企业微信管理后台</a>，在页面底部找到【自建】，点击【创建应用】</p>
          <img src='images/gl_1.png'></img>
          <p style={{ marginTop: '10px' }}>2.点击页面底部【已有小程序快速创建】</p>
          <img src='images/gl_2.png'></img>
          <p style={{ marginTop: '10px' }}>3.点击弹窗右下角【开始关联】</p>
          <img src='images/gl_3.png'></img>
          <p style={{ marginTop: '10px' }}>4.使用管理员账号扫码登录授权关联</p>
          <img src='images/gl_4.png'></img>
          <p style={{ marginTop: '10px' }}>5.最后在手机微信上选择要关联的小程序，勾选【我已阅读并同意】，点击【确认授权】后完成操作。</p>
          <img src='images/gl_5.png'></img>
          <img src='images/gl_6.png'></img>
          <img src='images/gl_7.png'></img>
        </div>
      </Card>
    </div>
  );
}
export default AppletRelevance;
