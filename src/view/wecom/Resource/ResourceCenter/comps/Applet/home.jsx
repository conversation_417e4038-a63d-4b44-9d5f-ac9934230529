/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/28 11:45
 * @LastEditTime: 2025/05/16 14:36
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Applet/home.jsx
 * @Description: '小程序'
 */

import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  AutoComplete,
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Upload,
} from "antd"
import { beforeUpload } from "common/image"
import { qs2obj } from "common/object"
import { normFile, removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  disabledDate,
  disabledTime,
  formatResourcePreviewList,
} from "../common"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const Applet = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [imageUrl, setImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [appletCardData, setAppletCardData] = useState({})
  const [treeSelectId, setTreeSelectId] = useState(null)
  const [status, setStatus] = useState(null)
  const [appIdList, setAppIdList] = useState([])
  const [switchResourcesFlag, setSwitchResourcesFlag] = useState(false)

  useEffect(() => {
    const { id, copyId } = qs2obj(props.location.search)
    getInfoTypeOptions()
    getResourceCategoryTreeTwo()
    getAppIdList()
    if (id) {
      setId(id)
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          }
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    setLoading(true)
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取appId列表
  const getAppIdList = async () => {
    setLoading(true)
    await apiCall("/info/infoResource/appIdList", "GET")
      .then((res) => {
        const list = res.list?.map((item) => ({
          value: item,
        }))
        setAppIdList(list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          sceneParentName,
          sceneName,
          startTime,
          endTime,
          resourceStatus,
          miniProgram,
          sceneId,
          internalResourcesFlag,
        } = res
        const image = miniProgram ? miniProgram.fileId : ""
        formForm.setFieldsValue({
          ...res,
          ...miniProgram,
          sceneId: sceneParentName + "-" + sceneName,
          fileId: image ? [image] : [],
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
        })
        setScriptText(script)
        setImageUrl(image)
        setStatus(resourceStatus)
        setTreeSelectId(sceneId)
        setSwitchResourcesFlag(internalResourcesFlag)
        setAppletCardData({
          ...miniProgram,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // 内部资源
  const onChangeSwitchResourcesFlag = (checked) => {
    setSwitchResourcesFlag(checked)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  // 推文封面
  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        setImageUrl(fileUrl)
        const data = appletCardData
        data.fileId = fileUrl
        setAppletCardData({ ...data })
        formForm.setFieldsValue({
          fileId: [fileId],
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: "",
    })
  }

  const handleRelevance = () => {
    props.history.push("/wecom/applet/relevance")
  }
  const handlePagePath = () => {
    props.history.push("/wecom/applet/pagePath")
  }
  const handleAppId = () => {
    props.history.push("/wecom/applet/appId")
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      formData.sceneId = treeSelectId || null
      const { title, appId, page, fileId } = formData
      const miniProgram = {
        title,
        appId,
        fileId: fileId && fileId[0],
        page,
      }
      setLoading(true)
      const data = {
        id: id,
        type: "MINI_PROGRAM",
        resourceStatus: id
          ? status == "soldOut" || status == "notIssue"
            ? type
            : status
          : type,
        ...formData,
        miniProgram,
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  return (
    <div className="ResourceApplet">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>推荐信息</h2>
              <FormItem
                name="reason"
                label="推荐理由"
                rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
              >
                <TextArea
                  placeholder="请输入推荐理由（100字）"
                  allowClear
                  showCount
                  maxLength={100}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                  className="textArea-mid"
                />
              </FormItem>
              <FormItem
                name="script"
                label="参考话术"
                rules={[{ required: true, message: "请输入参考话术（300字）" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入参考话术（300字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={300}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={handleScriptChange}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </FormItem>
              <h2>资源内容</h2>
              <FormItem
                name="internalResourcesFlag"
                label="内部资源"
                valuePropName="checked"
                // rules={[{ required: true }]}
              >
                <Switch onChange={onChangeSwitchResourcesFlag} />
              </FormItem>
              <FormItem
                name="sceneId"
                label="场景类型"
                rules={[{ required: true, message: "请选择场景类型" }]}
              >
                <TreeSelect
                  allowClear
                  showSearch
                  treeData={typeOptions}
                  treeDefaultExpandAll
                  placeholder="请选择场景类型"
                  fieldNames={{
                    label: "title",
                    value: "key",
                    children: "children",
                  }}
                  treeNodeFilterProp="title"
                  onSelect={handleTreeSelect}
                />
              </FormItem>
              <FormItem
                name="infoTagIds"
                label="资源标签"
                rules={[{ required: true, message: "请选择资源标签" }]}
              >
                <TreeSelect
                  treeData={resourceTreeData}
                  treeCheckable
                  treeDefaultExpandedKeys={["resource"]}
                  allowClear
                  showArrow
                  showSearch
                  treeNodeFilterProp="title"
                  maxTagCount="responsive"
                  showCheckedStrategy={SHOW_PARENT}
                  placeholder="资源标签"
                />
              </FormItem>
              {/*<CustomTagSelect*/}
              {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
              {/*  label="资源标签"*/}
              {/*  name="resourceTagNameList"*/}
              {/*  placeholder="资源标签"*/}
              {/*  useForm={formForm}*/}
              {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
              {/*  labelTreeData={resourceTreeData}*/}
              {/*/>*/}
              {!switchResourcesFlag && (
                <>
                  <FormItem
                    label="置顶展示"
                    name="topFlag"
                    initialValue={1}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value={1}>是</Radio>
                      <Radio value={0}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="本周推荐"
                    name="recommendFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                </>
              )}
              <LongTime
                form={formForm}
                disabledTime={disabledTime}
                disabledDate={disabledDate}
              />
              {/*<FormItem*/}
              {/*  name="time"*/}
              {/*  label="上下架时间"*/}
              {/*  rules={[{ required: true, message: '请选择上下架时间' }]}*/}
              {/*>*/}
              {/*  <RangePicker*/}
              {/*    disabledDate={disabledDate}*/}
              {/*    disabledTime={disabledTime}*/}
              {/*    showTime={{ format: 'HH:mm' }}*/}
              {/*    format="YYYY-MM-DD HH:mm"*/}
              {/*    inputReadOnly*/}
              {/*  />*/}
              {/*</FormItem>*/}
              <FormItem
                name="visibleScopeDepartmentId"
                label="可见范围"
                rules={[{ required: true, message: "请选择可见范围" }]}
              >
                <ETypeTransferModal title="可见范围" onlyDepartment />
              </FormItem>
              <div className="applet-tips">
                <span>
                  创建前，请确保该小程序已关联到企业微信工作台，否则将无法正常使用。
                </span>
                <span className="applet-tips_get" onClick={handleRelevance}>
                  如何关联？
                </span>
              </div>
              <FormItem label="小程序ID" required>
                <div>
                  <FormItem
                    name="appId"
                    rules={[{ required: true, message: "请输入小程序ID" }]}
                    className="formNoMargin"
                  >
                    {/* <Input placeholder="请输入小程序ID" allowClear /> */}
                    <AutoComplete
                      options={appIdList}
                      placeholder="请输入小程序ID"
                      filterOption={(input, option) =>
                        option.value
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    />
                  </FormItem>
                  <a onClick={handleAppId}>如何获取小程序ID（AppID）？</a>
                </div>
              </FormItem>
              <FormItem label="页面路径" required>
                <div>
                  <FormItem
                    name="page"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[{ required: true, message: "请输入页面路径" }]}
                    className="formNoMargin"
                  >
                    <Input placeholder="请输入页面路径" allowClear />
                  </FormItem>
                  <a onClick={handlePagePath}>如何获取小程序页面路径？</a>
                </div>
              </FormItem>
              <FormItem
                label="小程序标题"
                name="title"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入小程序标题" }]}
              >
                <Input
                  placeholder="请输入小程序标题(15字内)"
                  maxLength={15}
                  allowClear
                  onChange={(e) => {
                    const data = appletCardData
                    data.title = e.target.value
                    setAppletCardData({ ...data })
                  }}
                />
              </FormItem>

              <FormItem
                name="fileId"
                valuePropName="fileList"
                getValueFromEvent={normFile}
                label="小程序封面"
                rules={[{ required: true, message: "请上传图片" }]}
                extra="建议尺寸520*416px，大小限制为2M，最多上传1张"
              >
                <Upload
                  name="file"
                  customRequest={customRequest}
                  listType="picture-card"
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                  onChange={onChangeUpload}
                >
                  <WibotUploadImage
                    imageUrl={imageUrl}
                    loading={uploadLoading}
                    onClose={handleResetUpload}
                  />
                </Upload>
              </FormItem>
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                {id ? (
                  status == "soldOut" || status == "notIssue" ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    </>
                  ) : (
                    <Button type="primary" onClick={() => onSubmit()}>
                      保存
                    </Button>
                  )
                ) : (
                  <>
                    <Button type="primary" onClick={() => onSubmit("notIssue")}>
                      仅保存
                    </Button>
                    {props.reviewSetting ? (
                      <Button
                        type="primary"
                        onClick={() =>
                          Modal.confirm({
                            title: "提交审核确认",
                            content:
                              "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                            okText: "确认",
                            cancelText: "取消",
                            onCancel() {},
                            onOk() {
                              onSubmit("issued")
                            },
                          })
                        }
                      >
                        发布并提交审核
                      </Button>
                    ) : (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </>
                )}
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview
              type="session"
              listData={formatResourcePreviewList([
                {
                  type: "copyWriter",
                  content: scriptText,
                },
                {
                  type: "MINI_PROGRAM",
                  title: appletCardData.title,
                  image: imageUrl,
                },
              ])}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  )
}
export default withRouter(Applet)
