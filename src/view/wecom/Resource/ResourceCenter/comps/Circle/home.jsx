/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/31 11:26
 * @LastEditTime: 2025/07/04 11:14
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Circle/home.jsx
 * @Description: '朋友圈素材'
 */

import React, { useState, useEffect, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Radio,
  Typography,
  TreeSelect,
  message,
  Image,
  DatePicker,
  Upload,
  Spin,
  Space,
} from "antd"
import {
  PlusOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
} from "@ant-design/icons"
import { normFile, editorIsEmpty } from "common/regular"
import {
  base64ToFile,
  beforeUpload,
  videoBeforeUpload,
  getVideoBase64,
} from "common/image"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import moment from "moment"
import MaterialModal from "components/Modal/MaterialModal/home"
import WibotEmoji from "components/WibotEmoji/home"
import CircleMaterialModal from "components/Modal/CircleMaterialModal/home"
import EditFormModal from "components/Modal/EditFormModal/home"
import LinkCard from "components/LinkCard/home"
import WibotEditor from "components/WibotEditor/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"
import { FileHOC } from "components/FileHOC/FileHOC"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const { RangePicker } = DatePicker
const { Paragraph } = Typography

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const Circle = (props) => {
  const { onChangeTabs } = props
  const [formForm] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [materialParams, setMaterialParams] = useState({ visible: false })
  const [circleFormParams, setCircleFormParams] = useState({ visible: false })
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [editFormParams, setEditFormParams] = useState({ visible: false })
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [fileList, setFileList] = useState([])
  const [circleType, setCircleType] = useState("TEXT_IMAGE")
  const [linkCard, setLinkCard] = useState({})
  const [textContent, setTextContent] = useState("")
  const [videoUrl, setVideoUrl] = useState("")
  const [treeSelectId, setTreeSelectId] = useState(null)

  useEffect(() => {
    const { id } = props.match.params
    getInfoTypeOptions()
    getResourceCategoryTreeTwo()
    if (id) {
      setId(id)
      init(id)
    }
  }, [])

  const init = async (id) => {
    await fetchList({ id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setResourceTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    await apiCall(`/info/infoResource/${id}`, "GET")
      .then((res) => {
        const {
          type,
          script,
          sceneParentName,
          sceneName,
          startTime,
          endTime,
          friendContent,
        } = res
        let circleFormType = {}
        let circleType = "TEXT_IMAGE"
        if (friendContent.friendCircleType == "TEXT_IMAGE") {
          circleFormType = { resourceId: friendContent.images }
          const newFileList = friendContent.images?.map((item) => ({
            uid: item,
            fileId: item,
            status: "done",
            url: item,
          }))
          circleType = "TEXT_IMAGE"
          setCircleType("TEXT_IMAGE")
          setFileList(newFileList ?? [])
        } else if (friendContent.friendCircleType == "TEXT_VIDEO") {
          circleType = "TEXT_VIDEO"
          setCircleType("TEXT_VIDEO")
          circleFormType = { videoResourceId: friendContent.videos }
          setVideoUrl(friendContent.videos ? friendContent.videos[0] : null)
        } else {
          circleType = "TEXT_LINK"
          setCircleType("TEXT_LINK")
          circleFormType = { circleUrl: friendContent.link }
          setLinkCard({
            title: friendContent.linkTitle,
            description: friendContent.linkDescription,
            image: friendContent.linkCoverUrl,
            url: friendContent.link,
          })
        }
        formForm.setFieldsValue({
          ...res,
          circleType,
          sceneId: sceneParentName + "-" + sceneName,
          time:
            startTime && endTime
              ? [moment(startTime, "YYYY-MM-DD"), moment(endTime, "YYYY-MM-DD")]
              : "",
          circleContent: friendContent.text,
          ...circleFormType,
        })
        onChangeTabs(type)
        setScriptText(script)
        // setImageUrl(image);
        // setLinkCardData({
        //   title,
        //   description,
        //   image,
        //   url
        // });
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 朋友圈图片视频
  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    if (File.type.includes("image/")) {
      // 通过append方法来追加数据
      formData.append("file", File) // 返回压缩后的图片
      const data = formData
      apiCall("/file/image", "POST", data)
        .then((res) => {
          const { fileId, fileUrl } = res
          let newFileList = fileList
          newFileList.push({
            uid: fileId,
            name: File.name,
            fileId: fileId,
            status: "done",
            url: fileUrl,
          })
          setFileList([...newFileList])
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setUploadLoading(false)
        })
    } else if (File.type.includes("video/")) {
      // 通过append方法来追加数据
      formData.append("file", File) // 返回压缩后的图片
      const data = formData
      let fileIds = []
      apiCall("/file/video", "POST", data)
        .then((res) => {
          const { fileId, fileUrl } = res
          // setVideoUrl(fileUrl);
          fileIds.push(fileId)
          getVideoBase64({ url: fileUrl }).then((res) => {
            // 获取视频第一帧
            formData.set("file", base64ToFile(res, "first_frame.jpg")) // 返回压缩后的图片
            const data = formData
            apiCall("/file/image", "POST", data)
              .then((res) => {
                const { fileId } = res
                fileIds.push(fileId)
                // setVideoFileId([...fileIds]);
                formForm.setFieldsValue({
                  videoResourceId: [...fileIds],
                })
                setVideoUrl(fileUrl)
              })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
                setUploadLoading(false)
              })
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setUploadLoading(false)
        })
    }
  }

  const onChangeUpload = (info, type = null) => {
    if (type == "creat") {
      return
    }
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
    if (info.file.status === "removed") {
      setFileList(info.fileList)
      return
    }
  }

  const handleResetUpload = (e, index) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    let newFileList = fileList
    newFileList.splice(index, 1)
    setFileList([...newFileList])
    // setImageUrl('');
    formForm.setFieldsValue({
      fileId: newFileList.length > 0 ? newFileList : "",
    })
  }

  const onPreviewImage = (file) => {
    setPreviewParams({
      ...file,
      visible: true,
    })
  }

  // 链接编辑
  const onCircleLinkSearch = (event) => {
    const value = event.target.value
    if (!value) {
      message.error("链接不能为空！")
      return
    }
    if (/http(s)?:\/\/([\w-]+\.)+[\w-]+([\w- .?%&=]*)?/.test(value)) {
      setLoading(true)
      // setLoading(true);
      const data = {
        url: value,
      }
      apiCall("/materiallibrary/getOneByUrl", "GET", data)
        .then((res) => {
          const { title, url } = res
          if (!title) {
            message.info("暂无数据！")
            setLinkCard({ url })
            return
          }
          setLinkCard(res)
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  const handleEditLinkCard = () => {
    setEditFormParams({
      visible: true,
      data: linkCard,
      onOk: (data) => {
        // data.image = data.imageUrl;
        setLinkCard(data)
        setEditFormParams({ visible: false })
      },
      onCancel: () => {
        setEditFormParams({ visible: false })
      },
    })
  }

  // 朋友圈内容选择
  const onChangeRadioCircleType = (e) => {
    const { value } = e.target
    setCircleType(value)
    setTextContent("")
    setFileList([])
    setLinkCard({})
    setVideoUrl("")
    formForm.setFieldsValue({
      circleContent: "",
      resourceId: "",
      videoResourceId: "",
      circleUrl: "",
    })
  }

  // 从资源中心选择
  const handleAddCircle = () => {
    setCircleFormParams({
      visible: true,
      onSubmit: (data) => {
        let circleType = "TEXT_IMAGE"
        let circleFormType = {}
        if (data.resourceType == "Picture") {
          circleType = "TEXT_IMAGE"
          const newFileList = data.fileId?.map((item) => ({
            fileId: item,
            status: "done",
            url: item,
          }))
          circleFormType = { resourceId: data.fileId[0] || data.resourceType }
          setCircleType("TEXT_IMAGE")
          setFileList(newFileList ?? [])
        } else if (data.resourceType == "Video") {
          circleType = "TEXT_VIDEO"
          setCircleType("TEXT_VIDEO")
          circleFormType = {
            videoResourceId: data.fileId || data.resourceType,
          }
          setVideoUrl(data.fileId ? data.fileId[0] : null)
        } else {
          circleType = "TEXT_LINK"
          setCircleType("TEXT_LINK")
          circleFormType = { circleUrl: data.url || data.resourceType }
          setLinkCard({ ...data, image: data.fileId ? data.fileId[0] : null })
        }
        formForm.setFieldsValue({
          circleType,
          ...circleFormType,
        })
        let timer = setTimeout(() => {
          WibotEditorRef.current.setHtml(data.content)
          clearTimeout(timer)
        }, 300)
        setCircleFormParams({ visible: false })
      },
      onCancel: () => {
        setCircleFormParams({ visible: false })
      },
    })
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format("YYYY-MM-DD")
        formData.endTime = moment(formData.time[1]._d).format("YYYY-MM-DD")
        delete formData.time
      }
      formData.images = formData.fileId ? [formData.fileId] : []
      formData.sceneId = treeSelectId || null
      let friendContent = {}
      if (circleType == "TEXT_IMAGE") {
        friendContent = {
          friendCircleType: "TEXT_IMAGE",
          text: formData.circleContent,
          images: fileList.map((item) => item.uid || item.url),
        }
      } else if (circleType == "TEXT_VIDEO") {
        friendContent = {
          friendCircleType: "TEXT_VIDEO",
          text: formData.circleContent,
          videos: formData.videoResourceId,
        }
      } else {
        friendContent = {
          friendCircleType: "TEXT_LINK",
          text: formData.circleContent,
          linkTitle: linkCard.title,
          linkDescription: linkCard.description,
          linkCoverUrl: linkCard.image,
          link: linkCard.url,
        }
      }
      setLoading(true)
      const data = {
        id: id,
        type: "Moment_Material",
        resourceStatus: type,
        ...formData,
        friendContent,
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const uploadButton = (
    <div>
      {uploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{uploadLoading ? "上传中" : "上传"}</div>
    </div>
  )

  return (
    <div className="ResourceCircle">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>推荐信息</h2>

              <FormItem
                name="reason"
                label="推荐理由"
                rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
              >
                <TextArea
                  placeholder="请输入推荐理由（100字）"
                  allowClear
                  showCount
                  maxLength={100}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                  className="textArea-mid"
                />
              </FormItem>

              <FormItem
                name="script"
                label="参考话术"
                rules={[{ required: true, message: "请输入参考话术（300字）" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入参考话术（300字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={300}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={handleScriptChange}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </FormItem>

              <h2>资源内容</h2>

              <FormItem
                name="sceneId"
                label="场景类型"
                rules={[{ required: true, message: "请选择场景类型" }]}
              >
                <TreeSelect
                  allowClear
                  showSearch
                  treeData={typeOptions}
                  treeDefaultExpandAll
                  placeholder="请选择场景类型"
                  fieldNames={{
                    label: "title",
                    value: "key",
                    children: "children",
                  }}
                  treeNodeFilterProp="title"
                  onSelect={handleTreeSelect}
                />
              </FormItem>

              {/*<FormItem*/}
              {/*  name="infoTagIds"*/}
              {/*  label="资源标签"*/}
              {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
              {/*>*/}
              {/*  <TreeSelect*/}
              {/*    treeData={resourceTreeData}*/}
              {/*    treeCheckable*/}
              {/*    treeDefaultExpandedKeys={[""]}*/}
              {/*    allowClear*/}
              {/*    showArrow*/}
              {/*    showSearch*/}
              {/*    treeNodeFilterProp="title"*/}
              {/*    maxTagCount="responsive"*/}
              {/*    showCheckedStrategy={SHOW_PARENT}*/}
              {/*    placeholder="资源标签"*/}
              {/*  />*/}
              {/*</FormItem>*/}
              <CustomTagSelect
                rules={[{ required: true, message: "请选择资源标签" }]}
                label="资源标签"
                name="resourceTagNameList"
                placeholder="资源标签"
                useForm={formForm}
                existTagNameList={formForm.getFieldValue("resourceTagNameList")}
                labelTreeData={resourceTreeData}
              />
              <FormItem
                label="置顶展示"
                name="topFlag"
                initialValue={1}
                rules={[{ required: true }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </FormItem>

              <FormItem
                name="time"
                label="上下架时间"
                rules={[{ required: true, message: "请选择上下架时间" }]}
              >
                <RangePicker />
              </FormItem>

              <FormItem
                name="circleType"
                label="朋友圈内容"
                initialValue={"TEXT_IMAGE"}
                rules={[{ required: true, message: "请选择朋友圈类型" }]}
                extra={
                  <div>
                    <span>请根据需要选择朋友圈类型及添加朋友圈内容</span>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddCircle()}
                    >
                      从资源中心选择
                    </Button>
                  </div>
                }
              >
                <Radio.Group onChange={onChangeRadioCircleType}>
                  <Radio value={"TEXT_IMAGE"}>文本/图片</Radio>
                  <Radio value={"TEXT_VIDEO"}>文本/视频</Radio>
                  <Radio value={"TEXT_LINK"}>文本/链接</Radio>
                </Radio.Group>
              </FormItem>

              <FormItem
                label="文本"
                name="circleContent"
                rules={[{ required: true, message: "请输入文本" }]}
              >
                <WibotEditor
                  ref={WibotEditorRef}
                  toolbarConfig={{
                    toolbarKeys: ["emotion"],
                  }}
                  editorConfig={{
                    placeholder: "请输入内容(建议100字内)...",
                  }}
                  onChangeHtml={(html) => {
                    setTextContent(html)
                    formForm.setFieldValue(
                      "circleContent",
                      editorIsEmpty(html) ? "" : html
                    )
                  }}
                />
              </FormItem>

              {circleType == "TEXT_IMAGE" ? (
                <FormItem
                  name="resourceId"
                  getValueFromEvent={normFile}
                  label="图片"
                  rules={[{ required: true, message: "请上传图片" }]}
                  extra={"图片大小限制为2M，最多上传9张"}
                >
                  <Upload
                    name="file"
                    customRequest={customRequest}
                    listType="picture-card"
                    fileList={fileList}
                    beforeUpload={beforeUpload}
                    onChange={onChangeUpload}
                    onPreview={onPreviewImage}
                  >
                    {fileList.length >= 9 ? null : uploadButton}
                  </Upload>
                </FormItem>
              ) : null}

              {circleType == "TEXT_VIDEO" ? (
                <FormItem
                  name="videoResourceId"
                  getValueFromEvent={normFile}
                  label="视频"
                  rules={[{ required: true, message: "请上传视频" }]}
                  extra={"视频大小限制为10M，最多上传1个"}
                >
                  <Upload
                    name="file"
                    customRequest={customRequest}
                    listType="picture-card"
                    showUploadList={false}
                    beforeUpload={(file) => videoBeforeUpload(file)}
                    onChange={onChangeUpload}
                  >
                    {videoUrl ? (
                      <div style={{ position: "relative" }}>
                        <FileHOC src={videoUrl}>
                          {(url) => (
                            <video
                              src={url}
                              style={{ width: "100%", height: "100%" }}
                            />
                          )}
                        </FileHOC>{" "}
                        <CloseCircleOutlined
                          onClick={handleResetUpload}
                          className="cancel-upload-icon"
                        />
                      </div>
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </FormItem>
              ) : null}

              {circleType == "TEXT_LINK" ? (
                !linkCard.title ? (
                  <FormItem
                    name="circleUrl"
                    label="链接"
                    rules={[
                      {
                        required: true,
                        message: "请输入带有【http://或https://】链接",
                        type: "url",
                      },
                    ]}
                    extra={
                      linkCard.url ? (
                        <a
                          onClick={() => {
                            handleEditLinkCard()
                          }}
                        >
                          编辑标题、描述、封面
                        </a>
                      ) : (
                        ""
                      )
                    }
                  >
                    <Input
                      placeholder="请输入带有【http://或https://】链接"
                      onBlur={(e) => {
                        onCircleLinkSearch(e)
                      }}
                      onPressEnter={(e) => {
                        onCircleLinkSearch(e)
                      }}
                      allowClear
                    />
                  </FormItem>
                ) : (
                  <FormItem
                    label="链接"
                    extra={
                      <a
                        style={{ color: "#5d8dd4" }}
                        onClick={() => {
                          handleEditLinkCard()
                        }}
                      >
                        编辑标题、描述、封面
                      </a>
                    }
                  >
                    <LinkCard data={linkCard} isLink={false} />
                  </FormItem>
                )
              ) : null}
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                <Button type="primary" onClick={() => onSubmit("notIssue")}>
                  仅保存
                </Button>
                <Button type="primary" onClick={() => onSubmit("issued")}>
                  保存并发布
                </Button>
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <Card style={{ height: " 100%" }} bordered={false}>
              <h2>界面预览</h2>
              <div className="pre-wrap">
                <div className="phone-box">
                  <div className="circle-box">
                    {textContent ? <WibotEditorView html={textContent} /> : ""}
                    {fileList.length > 0
                      ? fileList.map((item, index) => (
                          <FileHOC src={item.url}>
                            {(url) => (
                              <Image
                                key={index}
                                className="fileImg"
                                src={url}
                                width={60}
                                height={60}
                                preview={false}
                              />
                            )}
                          </FileHOC>
                        ))
                      : ""}
                    {videoUrl ? (
                      <FileHOC src={videoUrl}>
                        {(url) => (
                          <video
                            controls
                            src={videourl}
                            style={{ width: "100px", height: "100px" }}
                          />
                        )}
                      </FileHOC>
                    ) : (
                      ""
                    )}
                    {!linkCard.title ? (
                      <Paragraph
                        className="describe"
                        ellipsis={{ rows: 3, tooltip: true }}
                      >
                        {linkCard.url}
                      </Paragraph>
                    ) : (
                      <LinkCard data={linkCard} isLink={false} />
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
      <MaterialModal params={materialParams} />
      <EditFormModal params={editFormParams} />
      <CircleMaterialModal {...circleFormParams} />
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
    </div>
  )
}

export default withRouter(Circle)
