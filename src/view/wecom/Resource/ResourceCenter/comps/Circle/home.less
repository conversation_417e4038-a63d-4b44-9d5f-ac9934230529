.ResourceCircle {
  .circle-box {
    position: absolute;
    top: 266px;
    left: 79px;
    text-align: left;

    p {
      width: 180px;
      margin: 0px;
      overflow: hidden;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      /*! autoprefixer: ignore next */
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
    }

    // .ant-image {
    //   margin-right: 5px;
    //   margin-top: 5px;
    // }

    .fileImg {
      object-fit: cover;
      height: 100%;
      // img{
      //   object-fit: cover;
      // }
    }
  }

  .link-card {
    // max-width: 190px;
    width: 190px;
    background: #fff;
  }
}