/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/05/09 13:51
 * @LastEditTime: 2023/11/15 14:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/CollectionModal/home.jsx
 * @Description: '强制推送集锦记录'
 */

import React, { memo, useEffect, useState } from 'react';
import { Modal, Table, Tooltip } from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import './home.less';

const CollectionModal = (props) => {
  const { visible, resourceId, onCancel } = props;
  const [loading, setLoading] = useState(true);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [dataSource, setDataSource] = useState([]);
  const [dataSource1, setDataSource1] = useState([]);
  const [logTotal, setLogTotal] = useState(0);

  const columns = [
    {
      title: '次数',
      width: '60px',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      render: (value, record, index) => {
        const obj = {
          children: `第${value + 1}次`,
          props: {},
        };
        obj.props.rowSpan = record.num;
        return obj;
      },
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '创建人',
      width: '100px',
      dataIndex: 'createEmployeeName',
      key: 'createEmployeeName',
      align: 'center',
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: {},
        };
        obj.props.rowSpan = record.num;
        return obj;
      },
    },
    {
      title: '创建时间',
      width: '100px',
      dataIndex: 'enableTime',
      key: 'enableTime',
      align: 'center',
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: {},
        };
        obj.props.rowSpan = record.num;
        return obj;
      },
      sorter: (a, b) => timeStamp(a.enableTime) - timeStamp(b.enableTime),
    },
    {
      title: '取消人',
      width: '100px',
      dataIndex: 'disableEmployeeName',
      key: 'disableEmployeeName',
      align: 'center',
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: {},
        };
        obj.props.rowSpan = record.num;
        return obj;
      },
    },
    {
      title: '取消时间',
      width: '100px',
      dataIndex: 'disableTime',
      key: 'disableTime',
      align: 'center',
      render: (value, record, index) => {
        const obj = {
          children: value,
          props: {},
        };
        obj.props.rowSpan = record.num;
        return obj;
      },
      sorter: (a, b) => timeStamp(a.disableTime) - timeStamp(b.disableTime),
    },
    {
      title: '集锦名称',
      width: '100px',
      dataIndex: 'collectName',
      key: 'collectName',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '推送时间',
      width: '100px',
      dataIndex: 'pushTime',
      key: 'pushTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.pushTime) - timeStamp(b.pushTime),
    },
  ];

  useEffect(() => {
    if (visible) {
      getPushLogPage();
    }
  }, [visible]);

  const getPushLogPage = (params = {}) => {
    setLoading(true);
    const { pagination } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const data = {
      paged: false,
      resourceId,
    };
    apiCall('/info/infoResource/pushLog/page', 'GET', data)
      .then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(datas([...records]));
        setDataSource1(datas([...records]));
        setPaginations({
          current: current,
          pageSize: size,
          total: records.length,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录`,
        })
        setLogTotal(total);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const datas = (data) => {
    let objs = {},
      k,
      arr1 = [];
    for (let i = 0, len = data.length; i < len; i++) {
      k = data[i].id;
      if (objs[k]) {
        objs[k]++;
      } else {
        objs[k] = 1;
      }
    }
    // 保存结果{el-'元素'，count-出现次数}
    for (let o in objs) {
      for (let i = 0; i < objs[o]; i++) {
        if (i === 0) {
          arr1.push(objs[o]);
        } else {
          arr1.push(0);
        }
      }
    }
    arr1.forEach((r, index) => {
      data[index]['num'] = r;
      data[index]['key'] = index;
    });
    return data;
  };

  const onChangeTable = (pagination, filters, sorter) => {
    let { current, pageSize } = pagination;
    if (current >= 2) {
      current = current - 1
      pageSize = pageSize * current;
      setDataSource(datas([...dataSource1.filter((item, index) => index >= pageSize)]));
    } else if (current <= 1) {
      setDataSource(datas([...dataSource1]));
    }
    setPaginations({
      ...pagination,
      showTotal: (total, range) => `共 ${total} 条记录`,
    })
  }

  const onModalCancel = () => {
    onCancel();
    setDataSource([])
    setDataSource1([])
  };

  return (
    <Modal
      visible={visible}
      title={`强制推送集锦记录(${logTotal}次)`}
      centered
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onModalCancel}
      className="CollectionModal"
      footer={false}
      width={1200}
      height={650}
    >
      <Table
        rowKey="uuid"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        bordered
        // onChange={onChangeTable}
        // scroll={{ x: 1300 }}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};
export default memo(CollectionModal);
