/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/30 10:36
 * @LastEditTime: 2025/05/06 15:31
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/FaceToFace/home.jsx
 * @Description: '面对面素材'
 */

import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Space,
  Spin,
  Upload,
} from "antd"
import { beforeUpload } from "common/image"
import { qs2obj } from "common/object"
import { normFile, removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import SysDictRadio from "components/select/SysDictRadio"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  disabledDate,
  disabledTime,
  formatResourcePreviewList,
} from "../common"

const { TextArea } = Input
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const FaceToFace = (props) => {
  const { id = null, copyId = null } = qs2obj(props.location.search)
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [imageUrl, setImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [scriptText, setScriptText] = useState("")
  const [linkCardData, setLinkCardData] = useState({})
  const [resourceStatus, setResourceStatus] = useState("")
  const [imageType, setImageType] = useState("LINK")
  const [contentType, setContentType] = useState("BUSINESS")
  const [productType, setProductType] = useState("")

  useEffect(() => {
    if (id || copyId) {
      getInfo()
    }
  }, [])

  const getInfo = async (params = {}) => {
    setLoading(true)
    const {} = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          startTime,
          endTime,
          images,
          productContent,
          resourceStatus,
          imageType,
          title,
          description,
          url,
        } = res
        formForm.setFieldsValue({
          ...res,
          ...productContent,
          fileId: images?.length ? images : [],
          imageUrl: images?.[0] || "",
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
        })
        setImageType(imageType)
        setContentType(productContent.contentType)
        setProductType(productContent.productType)
        setScriptText(script)
        setImageUrl(images?.[0] || "")
        setResourceStatus(resourceStatus)
        setLinkCardData({
          title,
          description,
          url,
          image: images?.[0],
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图标
    const data = formData
    apiCall("/file/image?uploadWx=1", "POST", data)
      .then((res) => {
        const { fileId, fileUrl, mediaImageId } = res
        setImageUrl(fileUrl)
        setLinkCardData({
          ...linkCardData,
          image: fileUrl,
        })
        formForm.setFieldsValue({
          fileId: [fileId],
          mediaImageId,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: [],
      mediaImageId: "",
    })
    setLinkCardData({
      ...linkCardData,
      image: "",
    })
  }

  const onSubmit = (status = resourceStatus) => {
    formForm.validateFields().then((formData) => {
      const {
        contentType,
        script,
        url,
        title,
        time,
        imageType,
        fileId,
        imageUrl,
      } = formData
      const isBusinessOrActivity =
        contentType === "BUSINESS" || contentType === "ACTIVITY"
      const isProduct = contentType === "PRODUCT"
      const hasScript = !!script?.trim()
      const hasUrl = !!url?.trim()
      const hasTitle = !!title?.trim()
      const hasUrlAndTitle = hasUrl && hasTitle
      if (isBusinessOrActivity && !hasScript && !hasUrlAndTitle) {
        message.warning(
          "当内容类型为 “业务 & 活动” 时， “文案” 与 “链接信息” 不能同时为空！"
        )
        return
      }
      // if (isBusinessOrActivity && (hasUrl || hasTitle) && !hasUrlAndTitle) {
      //   message.warning("请同时填写链接地址和链接标题，不能只填写其中一项！")
      //   return
      // }
      // if (isProduct && !hasTitle) {
      //   message.warning("当内容类型为 “产品” 时，链接标题不能为空！")
      //   return
      // }
      setLoading(true)
      if (time) {
        formData.startTime = moment(time[0]._d).format("YYYY-MM-DD HH:mm")
        formData.endTime = moment(time[1]._d).format("YYYY-MM-DD HH:mm")
        delete formData.time
      }
      formData.images =
        imageType == "UPLOAD" ? fileId : imageUrl ? [imageUrl] : []
      const data = {
        id: id,
        type: "FACE_PRODUCT",
        resourceStatus: status,
        ...formData,
        productContent: {
          ...formData,
        },
        internalResourcesFlag: false,
        recommend_flag: 0,
        topFlag: 0,
      }
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      const apiMode = id ? "PUT" : "POST"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const isValidLinkCard = () => {
    if (contentType === "PRODUCT") {
      return true
    }
    const { title = "", description = "", image = "", url = "" } = linkCardData
    return [title, description, image, url].some(
      (val) => typeof val === "string" && val?.trim() !== ""
    )
  }

  return (
    <div className="Resource-FaceToFace-Container">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>资源内容</h2>
              <Form.Item
                label="素材名称"
                name="productName"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入素材名称" }]}
              >
                <Input
                  placeholder="请输入素材名称(30字)"
                  maxLength={30}
                  allowClear
                />
              </Form.Item>

              <LongTime
                form={formForm}
                disabledTime={disabledTime}
                disabledDate={disabledDate}
              />

              <Form.Item
                name="visibleScopeDepartmentId"
                label="可见范围"
                rules={[{ required: true, message: "请选择可见范围" }]}
              >
                <ETypeTransferModal title="可见范围" onlyDepartment />
              </Form.Item>

              <Form.Item
                label="内容类型"
                name="contentType"
                rules={[{ required: true, message: "请选择内容类型" }]}
                initialValue={"BUSINESS"}
              >
                <SysDictRadio
                  dataset="FaceProductContentType"
                  onChange={(e) => {
                    setContentType(e.target.value)
                  }}
                />
              </Form.Item>

              <Form.Item
                name="script"
                label="文案"
                rules={[{ required: false, message: "请输入文案" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入文案（500字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={500}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={(e) => {
                      setScriptText(e.target.value)
                      formForm.setFieldValue("script", e.target.value)
                    }}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </Form.Item>

              {contentType == "BUSINESS" || contentType == "ACTIVITY" ? (
                <>
                  <Form.Item
                    label="链接地址"
                    name="url"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[
                      {
                        required: isValidLinkCard(),
                        message: "请输入链接地址",
                      },
                      {
                        required: false,
                        type: "url",
                        message: "请输入有效的链接地址",
                      },
                    ]}
                  >
                    <Input
                      placeholder="请输入链接地址"
                      allowClear
                      onChange={(e) => {
                        setLinkCardData({
                          ...linkCardData,
                          url: e.target.value,
                        })
                      }}
                    />
                  </Form.Item>
                </>
              ) : (
                ""
              )}

              <Form.Item
                label="链接标题"
                name="title"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[
                  { required: isValidLinkCard(), message: "请输入链接标题" },
                ]}
              >
                <Input
                  placeholder="请输入链接标题(30字)"
                  maxLength={30}
                  allowClear
                  onChange={(e) => {
                    setLinkCardData({
                      ...linkCardData,
                      title: e.target.value,
                    })
                  }}
                />
              </Form.Item>

              <Form.Item
                label="链接描述"
                name="description"
                rules={[
                  { required: isValidLinkCard(), message: "请输入链接描述" },
                ]}
              >
                <TextArea
                  autoSize={{ minRows: 4, maxRows: 8 }}
                  maxLength={60}
                  placeholder="请输入链接描述(60字)"
                  allowClear
                  onChange={(e) => {
                    setLinkCardData({
                      ...linkCardData,
                      description: e.target.value,
                    })
                  }}
                />
              </Form.Item>

              <Form.Item
                required={isValidLinkCard()}
                label="链接图标"
                style={{ margin: 0 }}
              >
                <Form.Item name="imageType" initialValue={"LINK"}>
                  <Radio.Group
                    onChange={(e) => {
                      setImageType(e.target.value)
                      setLinkCardData({
                        ...linkCardData,
                        image: "",
                      })
                      formForm.setFieldsValue({
                        fileId: [],
                        imageUrl: "",
                        mediaImageId: "",
                      })
                      setImageUrl("")
                    }}
                  >
                    <Radio value={"LINK"}>图标链接</Radio>
                    <Radio value={"UPLOAD"}>上传图标</Radio>
                  </Radio.Group>
                </Form.Item>
                {imageType == "UPLOAD" ? (
                  <>
                    <Form.Item
                      name="fileId"
                      initialValue={[]}
                      valuePropName="fileList"
                      getValueFromEvent={normFile}
                      rules={[
                        { required: isValidLinkCard(), message: "请上传图标" },
                      ]}
                      extra="建议图标尺寸比例1:1，图标大小最大为2M，最多上传1张"
                    >
                      <Upload
                        name="file"
                        customRequest={customRequest}
                        listType="picture-card"
                        showUploadList={false}
                        beforeUpload={beforeUpload}
                        onChange={onChangeUpload}
                      >
                        <WibotUploadImage
                          imageUrl={imageUrl}
                          loading={uploadLoading}
                          onClose={handleResetUpload}
                        />
                      </Upload>
                    </Form.Item>

                    <Form.Item name="mediaImageId" noStyle></Form.Item>
                  </>
                ) : (
                  <Form.Item
                    name="imageUrl"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[
                      {
                        required: isValidLinkCard(),
                        message: "请输入图标链接",
                      },
                      // {
                      //   required: false,
                      //   type: "url",
                      //   message: "请输入有效的链接地址",
                      // },
                    ]}
                    extra="建议图标尺寸比例1:1，图标大小最大为2M"
                  >
                    <Input
                      placeholder="请输入图标链接"
                      allowClear
                      onChange={(e) => {
                        setLinkCardData({
                          ...linkCardData,
                          image: e.target.value,
                        })
                      }}
                    />
                  </Form.Item>
                )}
              </Form.Item>

              {contentType == "PRODUCT" ? (
                <Form.Item
                  label="产品类型"
                  name="productType"
                  rules={[{ required: true, message: "请选择产品类型" }]}
                >
                  <SysDictSelect
                    dataset="FaceProductType"
                    placeholder="请选择产品类型"
                    onChange={(e) => {
                      setProductType(e)
                    }}
                  />
                </Form.Item>
              ) : (
                ""
              )}

              {contentType == "PRODUCT" && productType == "12" ? (
                <Form.Item
                  name="cardType"
                  label="二维码类型"
                  initialValue={"topic"}
                  rules={[{ required: true, message: "请选择二维码类型" }]}
                >
                  <Radio.Group>
                    <Radio value={"topic"}>信用卡主体二维码</Radio>
                    <Radio value={"product"}>信用卡产品二维码</Radio>
                  </Radio.Group>
                </Form.Item>
              ) : (
                ""
              )}

              {contentType == "PRODUCT" ? (
                <Form.Item
                  label="产品代码"
                  name="productCode"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: "请输入行内产品代码" }]}
                >
                  <Input placeholder="请输入行内产品代码" allowClear />
                </Form.Item>
              ) : (
                ""
              )}
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                {id ? (
                  resourceStatus == "soldOut" ||
                  resourceStatus == "notIssue" ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    </>
                  ) : (
                    <Button type="primary" onClick={() => onSubmit()}>
                      保存
                    </Button>
                  )
                ) : (
                  <>
                    <Button type="primary" onClick={() => onSubmit("notIssue")}>
                      仅保存
                    </Button>
                    {props.reviewSetting ? (
                      <Button
                        type="primary"
                        onClick={() =>
                          Modal.confirm({
                            title: "提交审核确认",
                            content:
                              "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                            okText: "确认",
                            cancelText: "取消",
                            onCancel() {},
                            onOk() {
                              onSubmit("issued")
                            },
                          })
                        }
                      >
                        发布并提交审核
                      </Button>
                    ) : (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </>
                )}
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview
              type="session"
              listData={formatResourcePreviewList([
                {
                  type: "copyWriter",
                  content: scriptText,
                },
                {
                  type: "Article",
                  title: linkCardData.title,
                  description: linkCardData.description,
                  image: linkCardData.image,
                },
              ])}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  )
}

export default withRouter(FaceToFace)
