import { DatePicker, Form } from "antd"
import moment from "moment"
import "./longtime.less"
const { RangePicker } = DatePicker
const FormItem = Form.Item

const LongTime = ({ form, disabledDate, disabledTime }) => {
  return (
    <FormItem required label="上下架时间" className="long-time">
      <FormItem
        noStyle
        name="time"
        rules={[{ required: true, message: "请选择上下架时间" }]}
      >
        <RangePicker
          style={{ width: "100%" }}
          disabledDate={disabledDate}
          disabledTime={disabledTime}
          showTime={{ format: "HH:mm" }}
          format="YYYY-MM-DD HH:mm"
          inputReadOnly
        />
      </FormItem>
      <FormItem noStyle>
        <div
          className="box"
          style={{ marginLeft: 10, color: "#4a6aed", cursor: "pointer" }}
        >
          <span
            onClick={(e) => {
              const longTermEndDate = moment("2099-12-31", "YYYY-MM-DD")
              form.setFieldsValue({
                time: [
                  form.getFieldValue("time")?.length
                    ? form.getFieldValue("time")[0]
                    : moment(new Date(), "YYYY-MM-DD HH:mm"),
                  longTermEndDate.clone().hour(23).minute(59),
                ],
              })
            }}
          >
            设为长期有效(至2099年12月31日)
          </span>
        </div>
      </FormItem>
    </FormItem>
  )
}

export default LongTime
