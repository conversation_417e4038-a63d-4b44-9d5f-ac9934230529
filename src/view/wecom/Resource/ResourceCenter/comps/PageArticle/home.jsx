/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/30 10:08
 * @LastEditTime: 2025/05/06 15:30
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/PageArticle/home.jsx
 * @Description: '网页文章'
 */

import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Upload,
} from "antd"
import { beforeUpload } from "common/image"
import { qs2obj } from "common/object"
import { editorIsEmpty, normFile, removeInputEmpty } from "common/regular"
import {
  findTreeItemByName,
  recursionTagKeyTreeData,
  recursionTagKeyTreeDataDisabled,
  recursionTagKeyTreeDataTag
} from "common/tree"
import { apiCall } from "common/utils"
import MaterialModal from "components/Modal/MaterialModal/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEditor from "components/WibotEditor/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import { removeAbsoluteURL, replaceOfEditorMediaURL } from "config"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  disabledDate,
  disabledTime,
  formatResourcePreviewList,
} from "../common"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const PageArticle = (props) => {
  const [formForm] = Form.useForm()
  const WibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [imageUrl, setImageUrl] = useState("")
  const [materialParams, setMaterialParams] = useState({ visible: false })
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [createMode, setCreateMode] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [linkCardData, setLinkCardData] = useState({})
  const [treeSelectId, setTreeSelectId] = useState(null)
  const [status, setStatus] = useState(null)
  const [productIds, setProductIds] = useState([])
  const [productValue, setProductValue] = useState(null)
  const [correlationOption, setCorrelationOption] = useState([])
  const [collectTagOption, setCollectTagOption] = useState([])
  const [collectionFlag, setCollectionFlag] = useState(false)
  const [switchResourcesFlag, setSwitchResourcesFlag] = useState(false)

  useEffect(() => {
    const { id, copyId } = qs2obj(props.location.search)
    getInfoTypeOptions()
    getTagCategoryTreeTwo()
    getResourceCategoryTreeTwo()
    getcreateModeType()
    getProductIds()
    getCorrelationOption()
    getCollectTagOption()
    if (id) {
      setId(id)
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          }
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    setLoading(true)
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获客渠道
  const getcreateModeType = () => {
    setLoading(true)
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setCreateMode(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 关联产品
  const getProductIds = () => {
    setLoading(true)
    apiCall("/info/infoResource/getProductIds", "GET")
      .then((res) => {
        setProductIds(
          res.map((item) => ({
            ...item,
            disabled: !item.enable,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取关联集锦
  const getCorrelationOption = async () => {
    await apiCall("/activity/collection", "GET")
      .then((res) => {
        let list = res.map((item) => ({
          label: item.name,
          value: item.id + "",
        }))
        list.unshift({
          label: "全部集锦",
          value: "ALL",
        })
        setCorrelationOption(list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取集锦标签
  const getCollectTagOption = async () => {
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCollectTagOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          sceneParentName,
          sceneName,
          startTime,
          endTime,
          images,
          title,
          description,
          url,
          resourceStatus,
          sceneId,
          productId,
          internalResourcesFlag,
          collectionFlag,
          body,
        } = res
        const image = images && images.length > 0 ? images[0] : ""
        formForm.setFieldsValue({
          ...res,
          sceneId: sceneParentName + "-" + sceneName,
          fileId: image ? images : [],
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
        })
        setScriptText(script)
        setImageUrl(image)
        setStatus(resourceStatus)
        setTreeSelectId(sceneId)
        setProductValue(productId)
        setLinkCardData({
          title,
          description,
          image,
          url,
        })
        setSwitchResourcesFlag(internalResourcesFlag)
        setCollectionFlag(collectionFlag)
        let timer = setTimeout(() => {
          WibotEditorRef.current.setHtml(replaceOfEditorMediaURL(body, true))
          clearTimeout(timer)
        }, 300)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  // 内部资源
  const onChangeSwitchResourcesFlag = (checked) => {
    setSwitchResourcesFlag(checked)
  }

  // 推文封面
  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image?uploadWx=1", "POST", data)
      .then((res) => {
        const { fileId, fileUrl, mediaImageId } = res
        setImageUrl(fileUrl)
        const data = linkCardData
        data.image = fileUrl
        setLinkCardData({ ...data })
        formForm.setFieldsValue({
          mediaImageId,
          fileId: [fileId],
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: "",
    })
    setLinkCardData({
      ...linkCardData,
      image: "",
    })
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      formData.images = formData.fileId ? formData.fileId : []
      formData.sceneId = treeSelectId || null
      setLoading(true)
      console.log(`[formData]: `, formData)
      const data = {
        id: id,
        mediaImageId: formForm.getFieldValue("mediaImageId"),
        type: "pageArticle",
        resourceStatus: id
          ? status == "soldOut" || status == "notIssue"
            ? type
            : status
          : type,
        ...formData,
        body: removeAbsoluteURL(formData.body),
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  return (
    <div className="ResourceArticle">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>推荐信息</h2>

              <FormItem
                name="reason"
                label="推荐理由"
                rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
              >
                <TextArea
                  placeholder="请输入推荐理由（100字）"
                  allowClear
                  showCount
                  maxLength={100}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                  className="textArea-mid"
                />
              </FormItem>

              <FormItem
                name="script"
                label="参考话术"
                rules={[{ required: true, message: "请输入参考话术（300字）" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入参考话术（300字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={300}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={handleScriptChange}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </FormItem>

              <h2>资源内容</h2>

              <FormItem
                name="internalResourcesFlag"
                label="内部资源"
                valuePropName="checked"
                // rules={[{ required: true }]}
              >
                <Switch onChange={onChangeSwitchResourcesFlag} />
              </FormItem>

              <FormItem
                name="sceneId"
                label="场景类型"
                rules={[{ required: true, message: "请选择场景类型" }]}
              >
                <TreeSelect
                  allowClear
                  showSearch
                  treeData={typeOptions}
                  treeDefaultExpandAll
                  placeholder="请选择场景类型"
                  fieldNames={{
                    label: "title",
                    value: "key",
                    children: "children",
                  }}
                  treeNodeFilterProp="title"
                  onSelect={handleTreeSelect}
                />
              </FormItem>

              <FormItem
                name="infoTagIds"
                label="资源标签"
                rules={[{ required: true, message: "请选择资源标签" }]}
              >
                <TreeSelect
                  treeData={resourceTreeData}
                  treeCheckable
                  treeDefaultExpandedKeys={["resource"]}
                  allowClear
                  showArrow
                  showSearch
                  treeNodeFilterProp="title"
                  maxTagCount="responsive"
                  showCheckedStrategy={SHOW_PARENT}
                  placeholder="资源标签"
                />
              </FormItem>
              {/*<CustomTagSelect*/}
              {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
              {/*  label="资源标签"*/}
              {/*  name="resourceTagNameList"*/}
              {/*  placeholder="资源标签"*/}
              {/*  useForm={formForm}*/}
              {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
              {/*  labelTreeData={resourceTreeData}*/}
              {/*/>*/}

              {/* <FormItem */}
              {/*   name="customerTagIds" */}
              {/*   label="客户标签" */}
              {/*   // rules={[{ required: true, message: '请选择客户标签' }]} */}
              {/* > */}
              {/*   <TreeSelect */}
              {/*     treeData={labelTreeData} */}
              {/*     treeCheckable */}
              {/*     treeDefaultExpandedKeys={[""]} */}
              {/*     allowClear */}
              {/*     showArrow */}
              {/*     showSearch */}
              {/*     treeNodeFilterProp="title" */}
              {/*     maxTagCount="responsive" */}
              {/*     showCheckedStrategy={SHOW_PARENT} */}
              {/*     placeholder="客户标签" */}
              {/*   /> */}
              {/* </FormItem> */}
              <CustomTagSelect
                label="客户标签"
                name="tagNameList"
                placeholder="客户标签"
                useForm={formForm}
                existTagNameList={formForm.getFieldValue("tagNameList")}
                labelTreeData={labelTreeData}
              />

              {!switchResourcesFlag && (
                <>
                  <FormItem
                    label="置顶展示"
                    name="topFlag"
                    initialValue={1}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value={1}>是</Radio>
                      <Radio value={0}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="本周推荐"
                    name="recommendFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="加入集锦"
                    name="collectionFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group
                      onChange={(e) => {
                        setCollectionFlag(e.target.value)
                      }}
                    >
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  {collectionFlag && (
                    <div
                      className="input-content-mask"
                      style={{ marginBottom: "20px" }}
                    >
                      <FormItem
                        name="collectIdList"
                        label="关联集锦"
                        rules={[{ required: true, message: "请选择关联集锦" }]}
                        extra="支持多选本部门在用的集锦，选择全部会自动添加后续的集锦"
                      >
                        <Select
                          placeholder="关联集锦"
                          options={correlationOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="collectTagIdList"
                        label="集锦标签"
                        rules={[{ required: true, message: "请选择集锦标签" }]}
                      >
                        <Select
                          placeholder="集锦标签"
                          fieldNames={{ label: "name", value: "id" }}
                          options={collectTagOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.name ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="pushFlag"
                        label="营销推送"
                        valuePropName="checked"
                        extra="资源上架后次日，系统自动为集锦问卷题目三选择“是”的客户推送，每次开启仅生效一次。如需停止可在次日集锦发送前下架资源或关闭推送"
                      >
                        <Switch />
                      </FormItem>
                    </div>
                  )}
                </>
              )}

              <FormItem label="关联产品" name="productId">
                <Select
                  options={productIds}
                  fieldNames={{ label: "name", value: "id" }}
                  allowClear
                  showSearch
                  placeholder="请选择关联产品"
                  filterOption={(input, option) =>
                    (option?.name ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // onChange={(value) => {
                  //   setProductValue(value);
                  // }}
                />
              </FormItem>

              {/* {productValue && (
                <FormItem
                  label="底部链接样式"
                  name="urlStyle"
                  initialValue="TEXT_CODE"
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    <Radio value="TEXT_CODE">文本+二维码</Radio>
                    <Radio value="TEXT_BUTTON">文本+按钮</Radio>
                  </Radio.Group>
                </FormItem>
              )} */}

              <LongTime
                form={formForm}
                disabledTime={disabledTime}
                disabledDate={disabledDate}
              />
              {/*<FormItem*/}
              {/*  name="time"*/}
              {/*  label="上下架时间"*/}
              {/*  rules={[{ required: true, message: "请选择上下架时间" }]}*/}
              {/*>*/}
              {/*  <RangePicker*/}
              {/*    disabledDate={disabledDate}*/}
              {/*    disabledTime={disabledTime}*/}
              {/*    showTime={{ format: "HH:mm" }}*/}
              {/*    format="YYYY-MM-DD HH:mm"*/}
              {/*    inputReadOnly*/}
              {/*  />*/}
              {/*</FormItem>*/}

              <FormItem
                name="channelId"
                label="获客渠道"
                rules={[{ required: true, message: "请选择获客渠道" }]}
              >
                <Select
                  options={createMode}
                  fieldNames={{ label: "name", value: "id" }}
                  allowClear
                  showSearch
                  placeholder="请选择获客渠道"
                  filterOption={(input, option) =>
                    (option?.name ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </FormItem>

              <FormItem
                name="visibleScopeDepartmentId"
                label="可见范围"
                rules={[{ required: true, message: "请选择可见范围" }]}
              >
                <ETypeTransferModal title="可见范围" onlyDepartment />
              </FormItem>

              <FormItem
                label="文章标题"
                name="title"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入文章标题" }]}
              >
                <Input
                  placeholder="请输入文章标题(30字)"
                  maxLength={30}
                  allowClear
                  onChange={(e) => {
                    const data = linkCardData
                    data.title = e.target.value
                    setLinkCardData({ ...data })
                  }}
                />
              </FormItem>

              <FormItem
                label="文章描述"
                name="description"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入文章描述" }]}
              >
                <TextArea
                  autoSize={{ minRows: 4, maxRows: 8 }}
                  maxLength={60}
                  placeholder="请输入文章描述(60字)"
                  allowClear
                  onChange={(e) => {
                    const data = linkCardData
                    data.description = e.target.value
                    setLinkCardData({ ...data })
                  }}
                />
              </FormItem>

              <FormItem
                name="fileId"
                valuePropName="fileList"
                getValueFromEvent={normFile}
                label="文章封面"
                rules={[{ required: true, message: "请上传图片" }]}
                extra="建议图片尺寸比例1:1，图片大小最大为2M，最多上传1张"
              >
                <Upload
                  name="file"
                  customRequest={customRequest}
                  listType="picture-card"
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                  onChange={onChangeUpload}
                >
                  <WibotUploadImage
                    imageUrl={imageUrl}
                    loading={uploadLoading}
                    onClose={handleResetUpload}
                  />
                </Upload>
              </FormItem>

              <FormItem
                label="正文"
                name="body"
                rules={[{ required: true, message: "请输入正文" }]}
              >
                <WibotEditor
                  ref={WibotEditorRef}
                  // toolbarConfig={{
                  //   // excludeKeys: [
                  //   //   "insertVideo",
                  //   // ],
                  //   // excludeKeys: [
                  //   //   'group-video',
                  //   // ]
                  // }}
                  onChangeHtml={(html) => {
                    console.log(`[html]: `, html)
                    formForm.setFieldValue(
                      "body",
                      editorIsEmpty(html)
                        ? ""
                        : replaceOfEditorMediaURL(html, true)
                    )
                  }}
                />
              </FormItem>
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                {id ? (
                  status == "soldOut" || status == "notIssue" ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    </>
                  ) : (
                    <Button type="primary" onClick={() => onSubmit()}>
                      保存
                    </Button>
                  )
                ) : (
                  <>
                    <Button type="primary" onClick={() => onSubmit("notIssue")}>
                      仅保存
                    </Button>
                    {props.reviewSetting ? (
                      <Button
                        type="primary"
                        onClick={() =>
                          Modal.confirm({
                            title: "提交审核确认",
                            content:
                              "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                            okText: "确认",
                            cancelText: "取消",
                            onCancel() {},
                            onOk() {
                              onSubmit("issued")
                            },
                          })
                        }
                      >
                        发布并提交审核
                      </Button>
                    ) : (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </>
                )}
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview
              type="session"
              listData={formatResourcePreviewList([
                {
                  type: "copyWriter",
                  content: scriptText,
                },
                {
                  type: "Article",
                  title: linkCardData.title,
                  description: linkCardData.description,
                  image: linkCardData.image,
                },
              ])}
            />
          </Col>
        </Row>
      </Spin>
      <MaterialModal params={materialParams} />
    </div>
  )
}

export default withRouter(PageArticle)
