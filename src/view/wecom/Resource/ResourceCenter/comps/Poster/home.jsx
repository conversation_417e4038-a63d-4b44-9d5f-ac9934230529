/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/31 10:08
 * @LastEditTime: 2025/05/16 14:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Poster/home.jsx
 * @Description: '海报'
 */

import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  CloseCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
} from "@ant-design/icons"
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Image,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Typography,
} from "antd"
import { base64ToFile, compressImage } from "common/image"
import { qs2obj } from "common/object"
import { removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import { api<PERSON>all, genUUID } from "common/utils"
import { FileHOC } from "components/FileHOC/FileHOC"
import MaterialModal from "components/Modal/MaterialModal/home"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  disabledDate,
  disabledTime,
  formatResourcePreviewList,
} from "../common"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const { RangePicker } = DatePicker
const { Paragraph } = Typography

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
}

const Poster = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [productIds, setProductIds] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [treeSelectId, setTreeSelectId] = useState(null)
  const [status, setStatus] = useState(null)
  const [productValue, setProductValue] = useState(null)
  const [fileList, setFileList] = useState([])
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  })
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [correlationOption, setCorrelationOption] = useState([])
  const [collectTagOption, setCollectTagOption] = useState([])
  const [collectionFlag, setCollectionFlag] = useState(false)
  const [switchResourcesFlag, setSwitchResourcesFlag] = useState(false)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [fastPosterId, setFastPosterId] = useState(null)
  const [fastPosterParams, setFastPosterParams] = useState([])

  useEffect(() => {
    const { id, copyId } = qs2obj(props.location.search)
    getInfoTypeOptions()
    getResourceCategoryTreeTwo()
    getProductIds()
    getCorrelationOption()
    getCollectTagOption()
    getTagCategoryTreeTwo()
    if (id) {
      setId(id)
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          }
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    setLoading(true)
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 关联产品
  const getProductIds = () => {
    setLoading(true)
    apiCall("/info/infoResource/getProductIds", "GET")
      .then((res) => {
        setProductIds(
          res.map((item) => ({
            ...item,
            disabled: !item.enable,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取关联集锦
  const getCorrelationOption = async () => {
    await apiCall("/activity/collection", "GET")
      .then((res) => {
        let list = res.map((item) => ({
          label: item.name,
          value: item.id + "",
        }))
        list.unshift({
          label: "全部集锦",
          value: "ALL",
        })
        setCorrelationOption(list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取集锦标签
  const getCollectTagOption = async () => {
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCollectTagOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          sceneParentName,
          sceneName,
          // images,
          startTime,
          endTime,
          resourceStatus,
          productId,
          sceneId,
          collectionFlag,
          internalResourcesFlag,
          fastPosterId,
          fastPosterParams,
          copyWriterList,
        } = res

        fastPosterParams?.forEach((item) => {
          // 去除海报参数的预设值
          item.content = ""
        })
        setFastPosterParams(fastPosterParams)

        // 清除文案中的无效参数
        let cleanCopyWriterList = copyWriterList.map((copyWriter) => {
          let params = []
          fastPosterParams.forEach((fpp) => {
            let param = copyWriter.params.find(
              (cwp) => cwp.paramsId === fpp.paramsId
            )
            params.push({
              paramsId: fpp.paramsId,
              content: param?.content || "",
            })
          })
          return { name: copyWriter.name, params: params }
        })

        formForm.setFieldsValue({
          ...res,
          sceneId: sceneParentName + "-" + sceneName,
          // fileId: images,
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
          copyWriterList: fastPosterId ? cleanCopyWriterList : null,
        })
        setScriptText(script)
        // const newFileList =
        //   images && images.length > 0
        //     ? images.map((item) => ({
        //         uid: genUUID(8),
        //         name: "海报" + genUUID(8),
        //         status: "done",
        //         url: item,
        //       }))
        //     : [];
        // setFileList(newFileList);
        // formForm.setFieldsValue({
        //   fileId: [...newFileList],
        // });
        setFastPosterId(fastPosterId)
        setStatus(resourceStatus)
        setProductValue(productId)
        setTreeSelectId(sceneId)
        setSwitchResourcesFlag(internalResourcesFlag)
        setCollectionFlag(collectionFlag)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // 内部资源
  const onChangeSwitchResourcesFlag = (checked) => {
    setSwitchResourcesFlag(checked)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
    if (info.file.status === "removed") {
      setFileList(info.fileList)
      formForm.setFieldsValue({
        fileId: [...info.fileList],
      })
      return
    }
  }

  // 上传图片
  const customRequest = (config) => {
    const File = config.file
    const options = {
      quality: 0.9,
    }
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    compressImage(File, options).then((result) => {
      // 通过append方法来追加数据
      formData.append("file", base64ToFile(result, File.name)) // 返回压缩后的图片
      const data = formData
      apiCall("/file/image", "POST", data)
        .then((res) => {
          const { fileId, fileUrl } = res
          let newFileList = fileList
          newFileList.push({
            uid: fileId,
            name: File.name,
            status: "done",
            url: fileUrl,
          })
          setFileList([...newFileList])
          formForm.setFieldsValue({
            fileId: [...newFileList],
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setUploadLoading(false)
        })
    })
  }

  const onPreviewImage = (file) => {
    setPreviewParams({
      ...file,
      visible: true,
    })
  }

  // 从素材库选择海报
  const handleSelectMaterial = () => {
    setMaterialModalParams({
      visible: true,
      materialAmount: fileList.length,
      tabType: "Poster",
      tabList: ["Poster"],
      placeholder: "请输入海报名称",
      onSubmit: (data) => {
        setMaterialModalParams({ visible: false })
        // const { fileId, name } = data;
        let newFileList = fileList
        data.forEach((item) => {
          newFileList.push({
            uid: genUUID(8),
            name: item.name || "海报" + genUUID(8),
            status: "done",
            url: item.fileId[0],
          })
        })
        setFileList([...newFileList])
        formForm.setFieldsValue({
          fileId: [...newFileList],
        })
      },
      onCancel: () => {
        setMaterialModalParams({ visible: false })
      },
    })
  }

  // 关联产品值变化
  const handleProductChange = (value) => {
    setProductValue(value)
  }

  // 添加海报文案
  const handleAddItem = () => {
    let copyWriterField = formForm.getFieldValue("copyWriterList")
    copyWriterField.push({
      name: "",
      params: fastPosterParams.map((item) => ({
        paramsId: item.paramsId,
        content: item.content,
      })),
    })

    formForm.setFieldsValue({
      copyWriterList: copyWriterField,
    })
  }

  // 移除海报文案
  const handleRemoveItem = (index) => {
    let copyWriterField = formForm.getFieldValue("copyWriterList")
    copyWriterField.splice(index, 1)
    formForm.setFieldsValue({
      copyWriterList: copyWriterField,
    })
  }

  const onSubmit = (params) => {
    const { type, isDraw } = params
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      // formData.images = fileList.map((item) => item.url);
      formData.sceneId = treeSelectId || null
      console.log(formData, "formDataformData")
      setLoading(true)
      const data = {
        id: id,
        type: "POSTER_TOOL",
        resourceStatus: id
          ? status == "soldOut" || status == "notIssue"
            ? type
            : status
          : type,
        ...formData,
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          isDraw
            ? props.history.push({
                pathname: "/wecom/posterTool/postIframe",
                search: `?id=${res.fastPosterId}&isAdd=true`,
              })
            : props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const uploadButton = (
    <div>
      {uploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{uploadLoading ? "上传中" : "上传"}</div>
    </div>
  )

  return (
    <div className="ResourcePoster">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>推荐信息</h2>
              <FormItem
                name="reason"
                label="推荐理由"
                rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
              >
                <TextArea
                  placeholder="请输入推荐理由（100字）"
                  allowClear
                  showCount
                  maxLength={100}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                  className="textArea-mid"
                />
              </FormItem>
              <FormItem
                name="script"
                label="参考话术"
                rules={[{ required: true, message: "请输入参考话术（300字）" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入参考话术（300字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={300}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={handleScriptChange}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </FormItem>
              <h2>资源内容</h2>
              <FormItem
                name="internalResourcesFlag"
                label="内部资源"
                valuePropName="checked"
                // rules={[{ required: true }]}
              >
                <Switch onChange={onChangeSwitchResourcesFlag} />
              </FormItem>
              <FormItem
                name="sceneId"
                label="场景类型"
                rules={[{ required: true, message: "请选择场景类型" }]}
              >
                <TreeSelect
                  allowClear
                  showSearch
                  treeData={typeOptions}
                  treeDefaultExpandAll
                  placeholder="请选择场景类型"
                  fieldNames={{
                    label: "title",
                    value: "key",
                    children: "children",
                  }}
                  treeNodeFilterProp="title"
                  onSelect={handleTreeSelect}
                />
              </FormItem>
              <FormItem
                name="infoTagIds"
                label="资源标签"
                rules={[{ required: true, message: "请选择资源标签" }]}
              >
                <TreeSelect
                  treeData={resourceTreeData}
                  treeCheckable
                  treeDefaultExpandedKeys={["resource"]}
                  allowClear
                  showArrow
                  showSearch
                  treeNodeFilterProp="title"
                  maxTagCount="responsive"
                  showCheckedStrategy={SHOW_PARENT}
                  placeholder="资源标签"
                />
              </FormItem>
              {/*<CustomTagSelect*/}
              {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
              {/*  label="资源标签"*/}
              {/*  name="resourceTagNameList"*/}
              {/*  placeholder="资源标签"*/}
              {/*  useForm={formForm}*/}
              {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
              {/*  labelTreeData={resourceTreeData}*/}
              {/*/>*/}
              {/* <FormItem */}
              {/*   name="customerTagIds" */}
              {/*   label="客户标签" */}
              {/*   // rules={[{ required: true, message: '请选择客户标签' }]} */}
              {/* > */}
              {/*   <TreeSelect */}
              {/*     treeData={labelTreeData} */}
              {/*     treeCheckable */}
              {/*     treeDefaultExpandedKeys={[""]} */}
              {/*     allowClear */}
              {/*     showArrow */}
              {/*     showSearch */}
              {/*     treeNodeFilterProp="title" */}
              {/*     maxTagCount="responsive" */}
              {/*     showCheckedStrategy={SHOW_PARENT} */}
              {/*     placeholder="客户标签" */}
              {/*   /> */}
              {/* </FormItem> */}
              <CustomTagSelect
                label="客户标签"
                name="tagNameList"
                placeholder="客户标签"
                useForm={formForm}
                labelTreeData={labelTreeData}
              />
              {!switchResourcesFlag && (
                <>
                  <FormItem
                    label="置顶展示"
                    name="topFlag"
                    initialValue={1}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value={1}>是</Radio>
                      <Radio value={0}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="本周推荐"
                    name="recommendFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="加入集锦"
                    name="collectionFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group
                      onChange={(e) => {
                        setCollectionFlag(e.target.value)
                      }}
                    >
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  {collectionFlag && (
                    <div
                      className="input-content-mask"
                      style={{ marginBottom: "20px" }}
                    >
                      <FormItem
                        name="collectIdList"
                        label="关联集锦"
                        rules={[{ required: true, message: "请选择关联集锦" }]}
                        extra="支持多选本部门在用的集锦，选择全部会自动添加后续的集锦"
                      >
                        <Select
                          placeholder="关联集锦"
                          options={correlationOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="collectTagIdList"
                        label="集锦标签"
                        rules={[{ required: true, message: "请选择集锦标签" }]}
                      >
                        <Select
                          placeholder="集锦标签"
                          fieldNames={{ label: "name", value: "id" }}
                          options={collectTagOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.name ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="pushFlag"
                        label="营销推送"
                        valuePropName="checked"
                        extra="资源上架后次日，系统自动为集锦问卷题目三选择“是”的客户推送，每次开启仅生效一次。如需停止可在次日集锦发送前下架资源或关闭推送"
                      >
                        <Switch />
                      </FormItem>
                    </div>
                  )}
                </>
              )}
              <LongTime
                form={formForm}
                disabledTime={disabledTime}
                disabledDate={disabledDate}
              />
              {/*<FormItem*/}
              {/*  name="time"*/}
              {/*  label="上下架时间"*/}
              {/*  rules={[{ required: true, message: "请选择上下架时间" }]}*/}
              {/*>*/}
              {/*  <RangePicker*/}
              {/*    disabledDate={disabledDate}*/}
              {/*    disabledTime={disabledTime}*/}
              {/*    showTime={{ format: "HH:mm" }}*/}
              {/*    format="YYYY-MM-DD HH:mm"*/}
              {/*    inputReadOnly*/}
              {/*  />*/}
              {/*</FormItem>*/}
              {/* {
                id && <FormItem name="resourceStatus" label="资源状态" rules={[{ required: true, message: '请选择资源状态' }]}>
                  <Select placeholder="资源状态" allowClear>
                    <Option value={'notIssue'}>未发布</Option>
                    <Option value={'Added'}>上架</Option>
                    <Option value={'soldOut'}>下架</Option>
                  </Select>
                </FormItem>
              } */}
              <FormItem name="productId" label="关联产品">
                <Select
                  options={productIds}
                  allowClear
                  showSearch
                  placeholder="请选择关联产品"
                  fieldNames={{ label: "name", value: "id" }}
                  // onChange={handleProductChange}
                  filterOption={(input, option) =>
                    (option?.name ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </FormItem>
              {/* {productValue && (
                <FormItem
                  label="产品二维码"
                  name="showFlag"
                  initialValue={1}
                  rules={[{ required: true, message: "请选择产品二维码" }]}
                >
                  <Radio.Group>
                    <Radio value={1}>显示</Radio>
                    <Radio value={0}>隐藏</Radio>
                  </Radio.Group>
                </FormItem>
              )} */}
              <FormItem
                name="visibleScopeDepartmentId"
                label="可见范围"
                rules={[{ required: true, message: "请选择可见范围" }]}
              >
                <ETypeTransferModal title="可见范围" onlyDepartment />
              </FormItem>
              <FormItem
                name="posterName"
                label="海报名称"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入海报名称" }]}
              >
                <Input
                  placeholder="请输入海报名称(30字)"
                  maxLength={30}
                  allowClear
                />
              </FormItem>

              {id && fastPosterId && (
                <Form.List name="copyWriterList">
                  {(fields, { add, remove }, { errors }) => (
                    <>
                      <h3>模板海报文案管理</h3>
                      <FormItem label="海报文案">
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddItem()}
                        >
                          添加文案
                        </Button>
                      </FormItem>
                      {formForm
                        .getFieldValue("copyWriterList")
                        ?.map((item, index) => (
                          <FormItem
                            {...formItemLayout}
                            label=" "
                            colon={false}
                            key={index}
                          >
                            <div className="copyWriter_item">
                              <Card
                                bordered={false}
                                title={
                                  <FormItem
                                    name={[index, "name"]}
                                    label="文案名称"
                                    rules={[
                                      {
                                        required: true,
                                        message: "请输入文案名称",
                                      },
                                    ]}
                                  >
                                    <Input
                                      placeholder="请输入文案名称"
                                      allowClear
                                    />
                                  </FormItem>
                                }
                                extra={
                                  <CloseCircleOutlined
                                    className="copyWriter_item_Close"
                                    onClick={() => handleRemoveItem(index)}
                                  />
                                }
                              >
                                {fastPosterParams &&
                                fastPosterParams.length > 0 ? (
                                  fastPosterParams.map((param, pidx) => (
                                    <FormItem
                                      name={[index, "params", pidx, "content"]}
                                      label={param.label}
                                      key={pidx}
                                    >
                                      <TextArea
                                        placeholder={
                                          "请输入【" + param.label + "】"
                                        }
                                        autoSize
                                        allowClear
                                      />
                                    </FormItem>
                                  ))
                                ) : (
                                  <span>请到海报绘制页中添加参数</span>
                                )}
                              </Card>
                            </div>
                          </FormItem>
                        ))}
                    </>
                  )}
                </Form.List>
              )}
              {/* <FormItem
                name="fileId"
                getValueFromEvent={normFile}
                label="上传图片"
                rules={[{ required: true, message: '请上传图片' }]}
                extra="大小限制为2M，最多上传9张"
              >
                <div>
                  <Upload
                    name="file"
                    customRequest={customRequest}
                    listType="picture-card"

                    fileList={fileList}
                    beforeUpload={beforeUpload}
                    onChange={onChangeUpload}
                    onPreview={onPreviewImage}
                  >
                    {fileList.length >= 9 ? null : uploadButton}
                  </Upload>
                  {fileList.length < 9 && (
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        handleSelectMaterial();
                      }}
                    >
                      从海报选择
                    </Button>
                  )}
                </div>
              </FormItem> */}
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                {id ? (
                  status == "soldOut" || status == "notIssue" ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() =>
                          onSubmit({ type: "notIssue", isDraw: true })
                        }
                      >
                        保存并绘制海报
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => onSubmit({ type: "notIssue" })}
                      >
                        仅保存
                      </Button>
                      {fastPosterId && (
                        <Button
                          type="primary"
                          onClick={() => onSubmit({ type: "issued" })}
                        >
                          保存并发布
                        </Button>
                      )}
                    </>
                  ) : (
                    <Button type="primary" onClick={() => onSubmit({})}>
                      保存
                    </Button>
                  )
                ) : (
                  <>
                    <Button
                      type="primary"
                      onClick={() => onSubmit({ type: "notIssue" })}
                    >
                      仅保存
                    </Button>
                    <Button
                      type="primary"
                      onClick={() =>
                        onSubmit({ type: "notIssue", isDraw: true })
                      }
                    >
                      保存并绘制海报
                    </Button>
                    {props.reviewSetting && (
                      <Button
                        type="primary"
                        onClick={() =>
                          Modal.confirm({
                            title: "提交审核确认",
                            content:
                              "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                            okText: "确认",
                            cancelText: "取消",
                            onCancel() {},
                            onOk() {
                              onSubmit({ type: "issued" })
                            },
                          })
                        }
                      >
                        发布并提交审核
                      </Button>
                    )}
                  </>
                )}
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview
              type="session"
              listData={formatResourcePreviewList([
                {
                  type: "copyWriter",
                  content: scriptText,
                },
                // {
                //   type: "Picture",
                //   imageUrl: imageUrl,
                // },
              ])}
            />
          </Col>
        </Row>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
      <MaterialModal params={materialModalParams} />
    </div>
  )
}
export default withRouter(Poster)
