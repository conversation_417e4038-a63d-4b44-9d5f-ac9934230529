/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/02 10:57
 * @LastEditTime: 2025/04/23 16:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Product/FaceProduct.jsx
 * @Description: '面客产品'
 */

import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  Button,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Upload,
} from "antd"
import { beforeUpload } from "common/image"
import { qs2obj } from "common/object"
import { normFile, removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import MaterialModal from "components/Modal/MaterialModal/home"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import { versionFnMap } from "config"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { clearCache } from "react-router-cache-route"
import { withRouter } from "react-router-dom"
import { disabledDate, disabledTime } from "../common"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect
const { RangePicker } = DatePicker
const { Option } = Select

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const FaceProduct = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [imageUrl, setImageUrl] = useState("")
  const [materialParams, setMaterialParams] = useState({ visible: false })
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [linkCardData, setLinkCardData] = useState({})
  const [treeSelectId, setTreeSelectId] = useState(null)
  const [status, setStatus] = useState(null)
  const [productLink, setProductLink] = useState(
    versionFnMap.system_ui().resourceCenter_productFlag
      ? "CODE_URL"
      : "FIXED_URL"
  )
  const [showCodeType, setShowCodeType] = useState(false)
  const [switchResourcesFlag, setSwitchResourcesFlag] = useState(false)
  // const [correlationOption, setCorrelationOption] = useState([]);
  // const [collectionFlag, setCollectionFlag] = useState(false);
  const [imageType, setImageType] = useState("UPLOAD")

  useEffect(() => {
    const { id, copyId } = qs2obj(props.location.search)
    getInfoTypeOptions()
    getResourceCategoryTreeTwo()
    if (id) {
      setId(id)
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        setResourceTreeData([{
          title: "全选",
          value: "resource",
          key: "resource",
          children: tagTreeData,
        }])
        // setResourceTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    setLoading(true)
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          sceneParentName,
          sceneName,
          startTime,
          endTime,
          images,
          productContent,
          resourceStatus,
          sceneId,
          internalResourcesFlag,
          imageType,
        } = res
        const image = images && images.length > 0 ? images[0] : ""
        productContent.areaCode = productContent.areaCodeVO
        productContent.productType = productContent.productTypeVO
        formForm.setFieldsValue({
          ...res,
          ...productContent,
          sceneId: sceneParentName + "-" + sceneName,
          fileId: image ? images : [],
          imageUrl: images[0],
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
        })
        if (productContent?.productTypeVO == "12") {
          setShowCodeType(true)
        }
        setScriptText(script)
        setImageType(imageType)
        setImageUrl(image)
        setStatus(resourceStatus)
        setTreeSelectId(sceneId)
        setProductLink(productContent?.productUrlTypeEnum)
        setSwitchResourcesFlag(internalResourcesFlag)
        // setCollectionFlag(collectionFlag);
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  // 内部资源
  const onChangeSwitchResourcesFlag = (checked) => {
    setSwitchResourcesFlag(checked)
  }

  // 推文封面
  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image?uploadWx=1", "POST", data)
      .then((res) => {
        const { fileId, fileUrl, mediaImageId } = res
        setImageUrl(fileUrl)
        const data = linkCardData
        data.image = fileUrl
        setLinkCardData({ ...data })
        formForm.setFieldsValue({
          fileId: [fileId],
          mediaImageId,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  // 从资源中心选择
  const handleSelectMaterial = () => {
    setMaterialParams({
      visible: true,
      onCancel: () => {
        setMaterialParams({ visible: false })
      },
      onSubmit: (data) => {
        const { type, images, videos } = data
        setMaterialParams({ visible: false })
        let material = ""
        if (type == "Video") {
          material = (videos && videos[1]) || ""
        } else {
          material = (images && images[0]) || ""
        }
        setImageUrl(material)
        const linkData = linkCardData
        linkData.image = material
        setLinkCardData({ ...linkData })
        formForm.setFieldsValue({
          fileId: material,
        })
      },
    })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldsValue({
      fileId: "",
    })
  }

  const handleProductType = (e) => {
    if (e == "12") {
      setShowCodeType(true)
    } else {
      setShowCodeType(false)
    }
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      setLoading(true)
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      formData.images =
        formData.imageType == "UPLOAD" ? formData.fileId : [formData.imageUrl]
      formData.sceneId = treeSelectId || null
      const {
        productUrlTypeEnum,
        productName,
        areaCode,
        cardType,
        productCode,
        productType,
        remark,
        url,
      } = formData
      const productContent = {
        productUrlTypeEnum,
        productName,
        areaCode,
        cardType,
        productCode,
        productType,
        remark,
        url,
      }
      const data = {
        id: id,
        mediaImageId: formForm.getFieldValue("mediaImageId"),
        type: "Product",
        subType: "FACE",
        resourceStatus: id
          ? status == "soldOut" || status == "notIssue"
            ? type
            : status
          : type,
        productContent,
        title: productName,
        method: "FACE",
        ...formData,
      }

      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          clearCache() // 清空路由缓存
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  return (
    <div className="FaceProduct">
      <Spin spinning={loading}>
        <Form {...layout} form={formForm}>
          <h2>推荐信息</h2>
          <FormItem
            name="reason"
            label="推荐理由"
            rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
          >
            <TextArea
              placeholder="请输入推荐理由（100字）"
              allowClear
              showCount
              maxLength={100}
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
          </FormItem>
          <FormItem
            name="script"
            label="参考话术"
            rules={[{ required: true, message: "请输入参考话术（300字）" }]}
          >
            <div className="textarea-emoji">
              <TextArea
                id="msgTextInput"
                placeholder="请输入参考话术（300字）"
                value={scriptText}
                allowClear
                showCount
                maxLength={300}
                autoSize={{ minRows: 2, maxRows: 7 }}
                onChange={handleScriptChange}
                className="textArea-big"
              />

              <WibotEmoji
                iconClassName="textarea-emoji_icon"
                onEmojiSelect={handleEmojiSelect}
              />
            </div>
          </FormItem>
          <h2>资源内容</h2>
          <FormItem
            name="internalResourcesFlag"
            label="内部资源"
            valuePropName="checked"
            // rules={[{ required: true }]}
          >
            <Switch onChange={onChangeSwitchResourcesFlag} />
          </FormItem>
          <FormItem
            label="产品名称"
            name="productName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: "请输入产品名称" }]}
          >
            <Input
              placeholder="请输入产品名称(30字)"
              maxLength={30}
              allowClear
            />
          </FormItem>
          <FormItem
            name="description"
            label="产品描述"
            rules={[{ required: true, message: "请输入产品描述（60字）" }]}
          >
            <TextArea
              placeholder="请输入产品描述（60字）"
              allowClear
              showCount
              maxLength={60}
              autoSize={{ minRows: 2, maxRows: 7 }}
            />
          </FormItem>
          <FormItem label="产品封面" required>
            <FormItem name="imageType" initialValue={"UPLOAD"}>
              <Radio.Group
                onChange={(e) => {
                  setImageType(e.target.value)
                }}
              >
                <Radio value={"UPLOAD"}>图片文件</Radio>
                <Radio value={"LINK"}>图片链接</Radio>
              </Radio.Group>
            </FormItem>
            {imageType == "UPLOAD" ? (
              <FormItem
                name="fileId"
                valuePropName="fileList"
                getValueFromEvent={normFile}
                rules={[{ required: true, message: "请上传图片" }]}
                extra="建议图片尺寸比例1:1，图片大小最大为2M，最多上传1张"
              >
                <Upload
                  name="file"
                  customRequest={customRequest}
                  listType="picture-card"
                  showUploadList={false}
                  beforeUpload={beforeUpload}
                  onChange={onChangeUpload}
                >
                  <WibotUploadImage
                    imageUrl={imageUrl}
                    loading={uploadLoading}
                    onClose={handleResetUpload}
                  />
                </Upload>
              </FormItem>
            ) : (
              <FormItem
                name="imageUrl"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入图片链接" }]}
                extra="建议图片尺寸比例1:1，图片大小最大为2M"
              >
                <Input placeholder="请输入图片链接" allowClear />
              </FormItem>
            )}
            {/* <Button type="primary" icon={<PlusOutlined />} onClick={handleSelectMaterial}>从资源中心选择</Button> */}
          </FormItem>

          {versionFnMap.system_ui().resourceCenter_productFlag ? (
            <FormItem
              label="产品链接"
              name="productUrlTypeEnum"
              initialValue="CODE_URL"
              rules={[{ required: true, message: "请选择产品链接" }]}
            >
              <Radio.Group
                onChange={(e) => {
                  setProductLink(e.target.value)
                }}
              >
                <Radio value={"CODE_URL"}>码上赢链接</Radio>
                <Radio value={"FIXED_URL"}>固定链接</Radio>
              </Radio.Group>
            </FormItem>
          ) : (
            <FormItem
              name="productUrlTypeEnum"
              initialValue="FIXED_URL"
              noStyle
            ></FormItem>
          )}

          {productLink == "FIXED_URL" && (
            <FormItem
              label="产品固定链接"
              name="url"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[
                { required: true, type: "url", message: "请输入产品固定链接" },
              ]}
            >
              <Input placeholder="请输入产品固定链接" allowClear />
            </FormItem>
          )}
          {!switchResourcesFlag && (
            <>
              <FormItem
                label="本周推荐"
                name="recommendFlag"
                initialValue={false}
                rules={[{ required: true }]}
              >
                <Radio.Group>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </FormItem>
              <FormItem
                label="置顶展示"
                name="topFlag"
                initialValue={1}
                rules={[{ required: true }]}
              >
                <Radio.Group>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </FormItem>
            </>
          )}
          {/* <FormItem label="加入集锦" name='collectionFlag'  initialValue={false} rules={[{ required: true }]}>
            <Radio.Group onChange={(e) => {
              setCollectionFlag(e.target.value);
            }}>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </FormItem>
          {
            collectionFlag && <div className='input-content-mask' style={{ marginBottom: '20px' }}>
              <FormItem name="productIds" label="关联集锦" extra="支持多选本部门在用的集锦，选择全部会自动添加后续的集锦">
                <Select
                  placeholder="关联集锦"
                  options={correlationOption}
                  allowClear
                  showSearch
                  showArrow
                  mode="multiple"
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </FormItem>
              <FormItem name="infoTagIds" label="集锦标签" rules={[{ required: true, message: '请选择集锦标签' }]}>
                <TreeSelect
                  treeData={resourceTreeData}
                  treeCheckable
                  treeDefaultExpandedKeys={['']}
                  allowClear
                  showArrow
                  showSearch
                  treeNodeFilterProp='title'
                 maxTagCount="responsive"
                  showCheckedStrategy={SHOW_PARENT}
                  placeholder='集锦标签'
                />
              </FormItem>
              <FormItem name='autoCreateGroup' label="营销推送" valuePropName="checked" extra="资源上架后次日，系统自动为集锦问卷题目三选择“是”的客户推送，每次开启仅生效一次。如需停止可在次日集锦发送前下架资源或关闭推送">
                <Switch />
              </FormItem>
            </div>
          } */}
          <LongTime
            form={formForm}
            disabledTime={disabledTime}
            disabledDate={disabledDate}
          />
          {/*<FormItem*/}
          {/*  name="time"*/}
          {/*  label="上下架时间"*/}
          {/*  rules={[{ required: true, message: "请选择上下架时间" }]}*/}
          {/*>*/}
          {/*  <RangePicker*/}
          {/*    disabledDate={disabledDate}*/}
          {/*    disabledTime={disabledTime}*/}
          {/*    showTime={{ format: "HH:mm" }}*/}
          {/*    format="YYYY-MM-DD HH:mm"*/}
          {/*    inputReadOnly*/}
          {/*  />*/}
          {/*</FormItem>*/}
          <FormItem
            name="sceneId"
            label="场景类型"
            rules={[{ required: true, message: "请选择场景类型" }]}
          >
            <TreeSelect
              allowClear
              showSearch
              treeData={typeOptions}
              treeDefaultExpandAll
              placeholder="请选择场景类型"
              fieldNames={{
                label: "title",
                value: "key",
                children: "children",
              }}
              treeNodeFilterProp="title"
              onSelect={handleTreeSelect}
            />
          </FormItem>
          <FormItem
            name="visibleScopeDepartmentId"
            label="可见范围"
            rules={[{ required: true, message: "请选择可见范围" }]}
          >
            <ETypeTransferModal title="可见范围" onlyDepartment />
          </FormItem>
          <FormItem
            name="infoTagIds"
            label="资源标签"
            rules={[{ required: true, message: "请选择资源标签" }]}
          >
            <TreeSelect
              treeData={resourceTreeData}
              treeCheckable
              treeDefaultExpandedKeys={["resource"]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder="资源标签"
            />
          </FormItem>

          {/*<CustomTagSelect*/}
          {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
          {/*  label="资源标签"*/}
          {/*  name="resourceTagNameList"*/}
          {/*  placeholder="资源标签"*/}
          {/*  useForm={formForm}*/}
          {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
          {/*  labelTreeData={resourceTreeData}*/}
          {/*/>*/}

          {versionFnMap.system_ui().resourceCenter_productFlag ? (
            <FormItem
              label="地区"
              name="areaCode"
              rules={[{ required: true, message: "请选择地区" }]}
            >
              <SysDictSelect dataset="AREA_CODE" placeholder="请选择地区" />
            </FormItem>
          ) : (
            ""
          )}

          <FormItem
            label="产品代码"
            name="productCode"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: "请输入行内产品代码" }]}
          >
            <Input placeholder="请输入行内产品代码" allowClear />
          </FormItem>

          {versionFnMap.system_ui().resourceCenter_productFlag ? (
            <FormItem
              label="产品类型"
              name="productType"
              rules={[{ required: true, message: "请选择产品类型" }]}
            >
              <SysDictSelect
                dataset="PRODUCT_TYPE"
                placeholder="产品类型"
                onChange={handleProductType}
              />
            </FormItem>
          ) : (
            ""
          )}

          {showCodeType && (
            <FormItem name="cardType" label="二维码类型">
              <Select placeholder="二维码类型" allowClear>
                <Option value="topic">信用卡主题二维码</Option>
                <Option value="product">信用卡产品二维码</Option>
              </Select>
            </FormItem>
          )}
          <FormItem name="remark" label="备注">
            <TextArea
              placeholder="请输入产品备注"
              allowClear
              showCount
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
          </FormItem>
        </Form>
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Space size={40}>
            {id ? (
              status == "soldOut" || status == "notIssue" ? (
                <>
                  <Button type="primary" onClick={() => onSubmit("notIssue")}>
                    仅保存
                  </Button>
                  <Button type="primary" onClick={() => onSubmit("issued")}>
                    保存并发布
                  </Button>
                </>
              ) : (
                <Button type="primary" onClick={() => onSubmit()}>
                  保存
                </Button>
              )
            ) : (
              <>
                <Button type="primary" onClick={() => onSubmit("notIssue")}>
                  仅保存
                </Button>
                {props.reviewSetting ? (
                  <Button
                    type="primary"
                    onClick={() =>
                      Modal.confirm({
                        title: "提交审核确认",
                        content:
                          "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                        okText: "确认",
                        cancelText: "取消",
                        onCancel() {},
                        onOk() {
                          onSubmit("issued")
                        },
                      })
                    }
                  >
                    发布并提交审核
                  </Button>
                ) : (
                  <Button type="primary" onClick={() => onSubmit("issued")}>
                    保存并发布
                  </Button>
                )}
              </>
            )}
          </Space>
        </div>
      </Spin>
      <MaterialModal params={materialParams} />
    </div>
  )
}
export default withRouter(FaceProduct)
