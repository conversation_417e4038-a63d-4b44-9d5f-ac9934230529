/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/30 10:36
 * @LastEditTime: 2025/04/23 13:43
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Product/home.jsx
 * @Description: '产品'
 */

import { CheckOutlined } from "@ant-design/icons"
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Spin,
} from "antd"
import { qs2obj } from "common/object"
import { removeInputEmpty } from "common/regular"
import { apiCall } from "common/utils"
import SysDictSelect from "components/select/SysDictSelect"
import { versionFnMap } from "config"
import { saveAs } from "file-saver"
import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import FaceProduct from "./FaceProduct"
import "./home.less"

const FormItem = Form.Item
const { TextArea } = Input
const { Option } = Select

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}
const Product = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [copyId, setCopyId] = useState(null)
  const [selectType, setSelectType] = useState(
    versionFnMap.system_ui().resourceCenter_productFlag ? "NORMAL" : "FACE"
  )
  const [fileDataSource, setFileDataSource] = useState([])
  const [fileData, setFileData] = useState([])
  const [isUpload, setIsUpload] = useState(true)
  const [showCodeType, setShowCodeType] = useState(false)
  const [haveFail, setHaveFail] = useState(false)
  const [uuid, setUuid] = useState("")
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [productFormData, setProductFormData] = useState({})
  const [status, setStatus] = useState(null)
  const [productLink, setProductLink] = useState("CODE_URL")
  const scrollListData = [
    ...(versionFnMap.system_ui().resourceCenter_productFlag
      ? [
          {
            title: "普通产品",
            describe: "只储存产品信息，无需对外展示",
            type: "NORMAL",
          },
        ]
      : []),
    {
      title: "面客产品",
      describe: "用于客户经理对外发送产品链接给到客户",
      type: "FACE",
    },
  ]

  useEffect(() => {
    const { id, copyId, subType } = qs2obj(props.location.search)
    subType && setSelectType(subType)
    setId(id)
    setCopyId(copyId)
    if ((id && subType == "FACE") || (copyId && subType == "FACE")) {
      return false
    }
    if (id) {
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const { productContent, resourceStatus } = res
        setProductFormData(productContent)
        setStatus(resourceStatus)
        if (productContent?.productTypeVO == "12") {
          setShowCodeType(true)
        }
        if (productContent) {
          productContent.areaCode = productContent.areaCodeVO
          productContent.productType = productContent.productTypeVO
          setProductLink(productContent.productUrlTypeEnum)
          formForm.setFieldsValue({
            ...productContent,
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 切换群发类型
  const handleScrollItem = (item) => {
    if (id || copyId) {
      return
    }
    setSelectType(item.type)
    formForm.setFieldsValue({
      ...productFormData,
    })
    // formForm.resetFields();
  }

  const handleProductType = (e) => {
    console.log(e)
    if (e == "12") {
      setShowCodeType(true)
    } else {
      setShowCodeType(false)
    }
  }

  // 下载文件模板
  const handleDownload = () => {
    setLoading(true)
    apiCall("/info/infoResource/downloadTemplate", "GET")
      .then((res) => {
        // location.href = res;
        saveAs(res, "产品导入模板.xlsx")
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }
  // 选择文件
  const customFileRequest = (config) => {
    const File = config.file
    setFileData([
      {
        name: File.name,
        status: "uploading",
      },
    ])
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/add", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        setFileData([
          {
            name: File.name,
            fileId,
            fileUrl,
            status: "done",
          },
        ])
        formForm.setFieldsValue({
          file: fileId,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }
  const onRemoveUpload = (file) => {
    setFileData([])
  }

  const handleFile = (params = {}) => {
    if (!fileData.length > 0) {
      message.error("请上传文件！")
      return
    }
    setLoading(true)
    const { pagination } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const { fileId, fileUrl } = fileData[0]
    const data = {
      current: pageInfo.current,
      size: pageInfo.pageSize,
      fileId,
      fileUrl,
    }
    apiCall("/info/infoResource/fileParse", "GET", data)
      .then((res) => {
        const { page, uuid, current, size, total, pages } = res
        setFileDataSource(page)
        setUuid(uuid)
        setHaveFail(
          page && page.length > 0
            ? page.some((item) => item.status == "失败")
            : false
        )
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        })
        setIsUpload(false)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    handleFile({ pagination })
  }

  // 忽略失败继续导入
  const handleImport = () => {
    setLoading(true)
    const { fileId, fileUrl } = fileData[0]
    const data = {
      uuid,
      fileId,
      fileUrl,
    }
    apiCall("/info/infoResource/productInto", "GET", data)
      .then((res) => {
        message.success("导入成功！")
        setIsUpload(true)
        onRemoveUpload()
      })
      .catch((err) => {})
      .finally(() => {
        setLoading(false)
      })
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      setLoading(true)
      const productContent = {
        ...formData,
      }
      const data = {
        id: id,
        type: "Product",
        subType: "NORMAL",
        resourceStatus: id ? status : type,
        productContent,
        method: selectType,
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache();// 清空路由缓存
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  return (
    <div className="resourceProduct">
      <Spin spinning={loading}>
        <div style={{ marginBottom: "10px" }}>
          <h2>产品用途</h2>
          <ul className="scrollList">
            {scrollListData.map((item, index) => (
              <li
                key={index}
                className={
                  selectType == item.type ? "listItem activeItem" : "listItem"
                }
                style={{
                  background: `${
                    selectType != item.type && (id || copyId)
                      ? "#c1c1c1"
                      : "unset"
                  }`,
                  cursor: `${
                    selectType != item.type && (id || copyId)
                      ? "unset"
                      : "pointer"
                  }`,
                }}
                onClick={() => handleScrollItem(item)}
              >
                <h2>{item.title}</h2>
                <p>{item.describe}</p>
                <div className="active">
                  <CheckOutlined />
                </div>
              </li>
            ))}
          </ul>
        </div>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            {selectType == "NORMAL" ? (
              <div>
                <Form {...layout} form={formForm}>
                  <h2>基础信息</h2>
                  <FormItem
                    label="产品名称"
                    name="productName"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[{ required: true, message: "请输入产品名称" }]}
                  >
                    <Input
                      placeholder="请输入产品名称(30字)"
                      maxLength={30}
                      allowClear
                    />
                  </FormItem>
                  <FormItem
                    label="地区"
                    name="areaCode"
                    rules={[{ required: true, message: "请选择地区" }]}
                  >
                    <SysDictSelect
                      dataset="AREA_CODE"
                      placeholder="请选择地区"
                    />
                  </FormItem>
                  <FormItem
                    label="产品代码"
                    name="productCode"
                    getValueFromEvent={(e) => removeInputEmpty(e)}
                    rules={[{ required: true, message: "请输入行内产品代码" }]}
                  >
                    <Input placeholder="请输入行内产品代码" allowClear />
                  </FormItem>
                  <FormItem
                    label="产品类型"
                    name="productType"
                    rules={[{ required: true, message: "请选择产品类型" }]}
                  >
                    <SysDictSelect
                      dataset="PRODUCT_TYPE"
                      placeholder="产品类型"
                      onChange={handleProductType}
                    />
                  </FormItem>
                  {showCodeType && (
                    <FormItem name="cardType" label="二维码类型">
                      <Select placeholder="二维码类型" allowClear>
                        <Option value="topic">信用卡主题二维码</Option>
                        <Option value="product">信用卡产品二维码</Option>
                      </Select>
                    </FormItem>
                  )}

                  <FormItem
                    label="产品链接"
                    name="productUrlTypeEnum"
                    initialValue="CODE_URL"
                    rules={[{ required: true, message: "请选择产品链接" }]}
                  >
                    <Radio.Group
                      onChange={(e) => {
                        setProductLink(e.target.value)
                      }}
                    >
                      <Radio value={"CODE_URL"}>码上赢链接</Radio>
                      <Radio value={"FIXED_URL"}>固定链接</Radio>
                    </Radio.Group>
                  </FormItem>

                  {productLink == "FIXED_URL" && (
                    <FormItem
                      label="产品固定链接"
                      name="url"
                      getValueFromEvent={(e) => removeInputEmpty(e)}
                      rules={[
                        {
                          required: true,
                          type: "url",
                          message: "请输入产品固定链接",
                        },
                      ]}
                    >
                      <Input placeholder="请输入产品固定链接" allowClear />
                    </FormItem>
                  )}

                  <FormItem name="remark" label="备注">
                    <TextArea
                      placeholder="请输入产品备注"
                      allowClear
                      showCount
                      autoSize={{ minRows: 2, maxRows: 7 }}
                      className="textArea-mid"
                    />
                  </FormItem>
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <Space size={40}>
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                      {/* <Button type="primary" onClick={() => onSubmit('issued')}>保存并发布</Button> */}
                    </Space>
                  </div>
                </Form>
              </div>
            ) : (
              <FaceProduct />
            )}
          </Col>
        </Row>
      </Spin>
    </div>
  )
}

export default withRouter(Product)
