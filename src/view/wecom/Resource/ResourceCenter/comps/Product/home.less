.resourceProduct {
  .scrollList {
    max-width: 1000px;
    height: 150px;
    // margin: 0 auto 20px;
    padding: 0;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;

    .listItem {
      position: relative;
      width: 250px;
      display: inline-block;
      height: 124px;
      border: 1px solid #d7d7d7;
      border-radius: 6px;
      padding: 10px;
      overflow: hidden;
      cursor: pointer;
      margin-right: 30px;
      white-space: break-spaces;

      h2 {
        font-size: 18px;
        font-weight: bold;
        margin: 0;
      }

      p {
        font-size: 14px;
        margin: 0;
      }

      .active {
        border: 20px solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
        border-right-color: #1890ff;
        position: absolute;
        transform: rotate(135deg);
        right: -20px;
        top: -20px;
        display: none;

        span {
          position: absolute;
          color: #fff;
          left: 6px;
          bottom: -8px;
          transform: rotate(-120deg);
          font-size: 13px;
        }
      }

      &.activeItem {
        border: 1px solid #1890ff;

        .active {
          display: block;
        }
      }
    }

    // 滚动条整体部分
    &::-webkit-scrollbar {
      width: 6px; //对垂直方向滚动条
      height: 6px; //对水平方向滚动条
    }

    //滚动的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: rgba(0, 0, 0, .5) //滚动条的颜色
    }

    //内层滚动槽
    &::-webkit-scrollbar-track-piece {
      background-color: rgba(0, 0, 0, .1);
    }
  }

  .ant-timeline {
    .ant-timeline-item-content {
      display: flex;

      span {
        margin-right: 10px;
        white-space: nowrap;
      }
    }
  }

  .carousel-box {
    width: 240px;
    margin-bottom: 10px;

    .carousel-item {
      width: 240px !important;
      height: 120px;
    }
  }

  .pre-item {
    width: 100%;
    max-width: 230px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 6px;
    text-align: left;
    white-space: break-spaces;
    overflow: hidden;
    margin: auto;
    margin-bottom: 10px;

    .questions {
      margin-bottom: 10px;

      .questions-flex {
        display: flex;
        margin-bottom: 5px;

        .questions-flex_tip {
          color: #fff;
          background: #3c7af7;
          width: 20px;
          text-align: center;
          font-size: 12px;
          height: 20px;
          line-height: 20px;
          margin-right: 10px;

          &.bot-tip {
            background: #ee7b46;
          }
        }
      }
    }
  }

  .pre-buy {
    position: absolute;
    bottom: 40px;
    left: 60px;
    width: 120px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 168px;
    color: #fff;
    background: #f99700;
    cursor: pointer;
  }

  .pre-wrap {
    .content {
      height: 450px;
      overflow-y: scroll;

      // 滚动条整体部分
      &::-webkit-scrollbar {
        width: 6px; //对垂直方向滚动条
        height: 6px; //对水平方向滚动条
      }

      //滚动的滑块
      &::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: #f2f2f2 //滚动条的颜色
      }

      //内层滚动槽
      &::-webkit-scrollbar-track-piece {
        background-color: #f2f2f2;
      }
    }
  }

  .fileParsing {
    .file-flex {
      display: flex;

      .file-text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 5px;
        margin-left: 10px;
      }
    }

  }
}