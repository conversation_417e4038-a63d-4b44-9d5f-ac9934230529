/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/31 10:49
 * @LastEditTime: 2025/05/06 15:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/Video/home.jsx
 * @Description: '视频'
 */

import { formatURL } from "@/config"
import LongTime from "@/view/wecom/Resource/ResourceCenter/comps/LongTime/LongTime"
import {
  CloseCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
} from "@ant-design/icons"
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Spin,
  Switch,
  TreeSelect,
  Upload,
} from "antd"
import {
  base64ToFile,
  getVideoBase64,
  getVideoDuration,
  videoBeforeUpload,
} from "common/image"
import { qs2obj } from "common/object"
import { normFile, removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataDisabled, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import { FileHOC } from "components/FileHOC/FileHOC"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotEmoji from "components/WibotEmoji/home"
import WibotMaterialPreview from "components/WibotMaterialPreview/home"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { withRouter } from "react-router-dom"
import {
  disabledDate,
  disabledTime,
  formatResourcePreviewList,
} from "../common"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { TextArea } = Input
const { SHOW_PARENT } = TreeSelect

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const Video = (props) => {
  const [formForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [typeOptions, setTypeOptions] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [scriptText, setScriptText] = useState("")
  const [videoUrl, setVideoUrl] = useState("")
  const [videoFileId, setVideoFileId] = useState([])
  const [treeSelectId, setTreeSelectId] = useState(null)
  const [status, setStatus] = useState(null)
  const [correlationOption, setCorrelationOption] = useState([])
  const [collectTagOption, setCollectTagOption] = useState([])
  const [collectionFlag, setCollectionFlag] = useState(false)
  const [switchResourcesFlag, setSwitchResourcesFlag] = useState(false)

  useEffect(() => {
    const { id, copyId } = qs2obj(props.location.search)
    getInfoTypeOptions()
    getResourceCategoryTreeTwo()
    getCorrelationOption()
    getCollectTagOption()
    if (id) {
      setId(id)
      init(id)
    } else if (copyId) {
      init(copyId, true)
    }
  }, [])

  const init = async (id, isCopy = false) => {
    await fetchList(isCopy ? { copyId: id } : { id })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          }
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    setLoading(true)
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        setTypeOptions(
          res.map((item) => ({
            ...item,
            disabled: true,
          }))
        )
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取关联集锦
  const getCorrelationOption = async () => {
    await apiCall("/activity/collection", "GET")
      .then((res) => {
        let list = res.map((item) => ({
          label: item.name,
          value: item.id + "",
        }))
        list.unshift({
          label: "全部集锦",
          value: "ALL",
        })
        setCorrelationOption(list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取集锦标签
  const getCollectTagOption = async () => {
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCollectTagOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id, copyId } = params
    await apiCall(`/info/infoResource/${id || copyId}`, "GET")
      .then((res) => {
        const {
          script,
          sceneParentName,
          sceneName,
          startTime,
          endTime,
          videos,
          resourceStatus,
          sceneId,
          collectionFlag,
          internalResourcesFlag,
        } = res
        formForm.setFieldsValue({
          ...res,
          sceneId: sceneParentName + "-" + sceneName,
          videoFileId: videos,
          time:
            id &&
            (startTime && endTime
              ? [
                  moment(startTime, "YYYY-MM-DD HH:mm"),
                  moment(endTime, "YYYY-MM-DD HH:mm"),
                ]
              : ""),
        })
        setScriptText(script)
        setVideoFileId(videos)
        setVideoUrl(videos && videos.length > 0 ? videos[0] : "")
        setStatus(resourceStatus)
        setTreeSelectId(sceneId)
        setSwitchResourcesFlag(internalResourcesFlag)
        setCollectionFlag(collectionFlag)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleScriptChange = (e) => {
    setScriptText(e.target.value)
  }

  // emoji组件
  const handleEmojiSelect = async (e) => {
    const insertEmoji = e.native
    const inputIndex = document.getElementById("msgTextInput") // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      scriptText.substring(0, startPos) +
      insertEmoji +
      scriptText.substring(endPos)
    setScriptText(text)
    formForm.setFieldsValue({
      script: text,
    })
    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertEmoji.length,
      endPos + insertEmoji.length
    )
  }

  // 选择视频
  const customRequest = (config) => {
    console.log(config, "config-config")
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    const fileIds = []
    // 获取视频总时长，再上传图片
    getVideoDuration(File).then((duration) => {
      apiCall(`/file/video?duration=${duration}`, "POST", data)
        .then((res) => {
          const { fileId, fileUrl } = res
          fileIds.push(fileId)
          formForm.setFieldsValue({
            videoFileId: fileId,
          })
          getVideoBase64({ url: formatURL(fileUrl) }).then((res) => {
            // 获取视频第一帧
            formData.set("file", base64ToFile(res, "first_frame.jpg")) // 返回压缩后的图片
            const data = formData
            apiCall("/file/image", "POST", data)
              .then((res) => {
                const { fileId } = res
                fileIds.push(fileId)
                setVideoFileId([...fileIds])
                console.log([...fileIds])
                setVideoUrl(fileUrl)
              })
              .catch((err) => {
                console.log(err)
              })
              .finally(() => {
                setUploadLoading(false)
              })
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {})
    })
  }

  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setVideoUrl("")
    formForm.setFieldsValue({
      videoFileId: "",
    })
  }

  // 场景类型改变
  const handleTreeSelect = (value, node, extra) => {
    setTreeSelectId(value)
    formForm.setFieldsValue({
      sceneId: node.parentName + "-" + node.title,
    })
  }

  // 内部资源
  const onChangeSwitchResourcesFlag = (checked) => {
    setSwitchResourcesFlag(checked)
  }

  const onSubmit = (type) => {
    formForm.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      formData.videos = videoFileId
      formData.sceneId = treeSelectId || null
      setLoading(true)
      const data = {
        id: id,
        type: "Video",
        resourceStatus: id
          ? status == "soldOut" || status == "notIssue"
            ? type
            : status
          : type,
        ...formData,
      }
      const apiMode = id ? "PUT" : "POST"
      const apiUrl = id ? `/info/infoResource/${id}` : "/info/infoResource"
      apiCall(apiUrl, apiMode, data)
        .then((res) => {
          message.success(id ? "修改成功！" : "新增成功！")
          // clearCache(); // 清空路由缓存
          props.history.push("/wecom/resourceCenter")
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const uploadButton = (
    <div>
      {uploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{uploadLoading ? "上传中" : "上传"}</div>
    </div>
  )

  return (
    <div className="ResourceVideo">
      <Spin spinning={loading}>
        <Row gutter={10}>
          <Col xs={24} lg={16}>
            <Form {...layout} form={formForm}>
              <h2>推荐信息</h2>
              <FormItem
                name="reason"
                label="推荐理由"
                rules={[{ required: true, message: "请输入推荐理由（100字）" }]}
              >
                <TextArea
                  placeholder="请输入推荐理由（100字）"
                  allowClear
                  showCount
                  maxLength={100}
                  autoSize={{ minRows: 2, maxRows: 7 }}
                  className="textArea-mid"
                />
              </FormItem>
              <FormItem
                name="script"
                label="参考话术"
                rules={[{ required: true, message: "请输入参考话术（300字）" }]}
              >
                <div className="textarea-emoji">
                  <TextArea
                    id="msgTextInput"
                    placeholder="请输入参考话术（300字）"
                    value={scriptText}
                    allowClear
                    showCount
                    maxLength={300}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    onChange={handleScriptChange}
                    className="textArea-big"
                  />

                  <WibotEmoji
                    iconClassName="textarea-emoji_icon"
                    onEmojiSelect={handleEmojiSelect}
                  />
                </div>
              </FormItem>
              <h2>资源内容</h2>
              <FormItem
                name="internalResourcesFlag"
                label="内部资源"
                valuePropName="checked"
                // rules={[{ required: true }]}
              >
                <Switch onChange={onChangeSwitchResourcesFlag} />
              </FormItem>
              <FormItem
                name="sceneId"
                label="场景类型"
                rules={[{ required: true, message: "请选择场景类型" }]}
              >
                <TreeSelect
                  allowClear
                  showSearch
                  treeData={typeOptions}
                  treeDefaultExpandAll
                  placeholder="请选择场景类型"
                  fieldNames={{
                    label: "title",
                    value: "key",
                    children: "children",
                  }}
                  treeNodeFilterProp="title"
                  onSelect={handleTreeSelect}
                />
              </FormItem>
              <FormItem
                name="infoTagIds"
                label="资源标签"
                rules={[{ required: true, message: "请选择资源标签" }]}
              >
                <TreeSelect
                  treeData={resourceTreeData}
                  treeCheckable
                  treeDefaultExpandedKeys={["resource"]}
                  allowClear
                  showArrow
                  showSearch
                  treeNodeFilterProp="title"
                  maxTagCount="responsive"
                  showCheckedStrategy={SHOW_PARENT}
                  placeholder="资源标签"
                />
              </FormItem>
              {/*<CustomTagSelect*/}
              {/*  rules={[{ required: true, message: "请选择资源标签" }]}*/}
              {/*  label="资源标签"*/}
              {/*  name="resourceTagNameList"*/}
              {/*  placeholder="资源标签"*/}
              {/*  useForm={formForm}*/}
              {/*  existTagNameList={formForm.getFieldValue("resourceTagNameList")}*/}
              {/*  labelTreeData={resourceTreeData}*/}
              {/*/>*/}
              {!switchResourcesFlag && (
                <>
                  <FormItem
                    label="置顶展示"
                    name="topFlag"
                    initialValue={1}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value={1}>是</Radio>
                      <Radio value={0}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="本周推荐"
                    name="recommendFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem
                    label="加入集锦"
                    name="collectionFlag"
                    initialValue={false}
                    rules={[{ required: true }]}
                  >
                    <Radio.Group
                      onChange={(e) => {
                        setCollectionFlag(e.target.value)
                      }}
                    >
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>
                  </FormItem>
                  {collectionFlag && (
                    <div
                      className="input-content-mask"
                      style={{ marginBottom: "20px" }}
                    >
                      <FormItem
                        name="collectIdList"
                        label="关联集锦"
                        rules={[{ required: true, message: "请选择关联集锦" }]}
                        extra="支持多选本部门在用的集锦，选择全部会自动添加后续的集锦"
                      >
                        <Select
                          placeholder="关联集锦"
                          options={correlationOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="collectTagIdList"
                        label="集锦标签"
                        rules={[{ required: true, message: "请选择集锦标签" }]}
                      >
                        <Select
                          placeholder="集锦标签"
                          fieldNames={{ label: "name", value: "id" }}
                          options={collectTagOption}
                          allowClear
                          showSearch
                          showArrow
                          mode="multiple"
                          filterOption={(input, option) =>
                            (option?.name ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                        />
                      </FormItem>
                      <FormItem
                        name="pushFlag"
                        label="营销推送"
                        valuePropName="checked"
                        extra="资源上架后次日，系统自动为集锦问卷题目三选择“是”的客户推送，每次开启仅生效一次。如需停止可在次日集锦发送前下架资源或关闭推送"
                      >
                        <Switch />
                      </FormItem>
                    </div>
                  )}
                </>
              )}
              <LongTime
                form={formForm}
                disabledTime={disabledTime}
                disabledDate={disabledDate}
              />
              {/*<FormItem*/}
              {/*  name="time"*/}
              {/*  label="上下架时间"*/}
              {/*  rules={[{ required: true, message: '请选择上下架时间' }]}*/}
              {/*>*/}
              {/*  <RangePicker*/}
              {/*    disabledDate={disabledDate}*/}
              {/*    disabledTime={disabledTime}*/}
              {/*    showTime={{ format: 'HH:mm' }}*/}
              {/*    format="YYYY-MM-DD HH:mm"*/}
              {/*    inputReadOnly*/}
              {/*  />*/}
              {/*</FormItem>*/}
              <FormItem
                name="visibleScopeDepartmentId"
                label="可见范围"
                rules={[{ required: true, message: "请选择可见范围" }]}
              >
                <ETypeTransferModal title="可见范围" onlyDepartment />
              </FormItem>
              <FormItem
                label="视频名称"
                name="title"
                getValueFromEvent={(e) => removeInputEmpty(e)}
                rules={[{ required: true, message: "请输入视频名称" }]}
              >
                <Input
                  placeholder="请输入视频名称(30字)"
                  maxLength={30}
                  allowClear
                />
              </FormItem>
              <FormItem
                name="videoFileId"
                getValueFromEvent={normFile}
                label="视频"
                rules={[{ required: true, message: "请上传视频" }]}
                extra={
                  <div>
                    {/* <Button type="primary" icon={<PlusOutlined />} onClick={handleSelectMaterial}>从资源中心选择</Button> */}
                    <span>支持mp4格式，视频大小不超过10M</span>
                  </div>
                }
              >
                <Upload
                  name="file"
                  customRequest={customRequest}
                  listType="picture-card"
                  showUploadList={false}
                  beforeUpload={(file) => videoBeforeUpload(file)}
                  onChange={onChangeUpload}
                >
                  {videoUrl ? (
                    <div
                      style={{
                        position: "relative",
                        width: "100%",
                        height: "100%",
                      }}
                    >
                      <FileHOC src={videoUrl} v-slot="{ url }">
                        {(url) => (
                          <video
                            src={url}
                            style={{ width: "100%", height: "100%" }}
                          />
                        )}
                      </FileHOC>{" "}
                      <CloseCircleOutlined
                        onClick={handleResetUpload}
                        className="cancel-upload-icon"
                      />
                    </div>
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </FormItem>
            </Form>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Space size={40}>
                {id ? (
                  status == "soldOut" || status == "notIssue" ? (
                    <>
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    </>
                  ) : (
                    <Button type="primary" onClick={() => onSubmit()}>
                      保存
                    </Button>
                  )
                ) : (
                  <>
                    <Button type="primary" onClick={() => onSubmit("notIssue")}>
                      仅保存
                    </Button>
                    {props.reviewSetting ? (
                      <Button
                        type="primary"
                        onClick={() =>
                          Modal.confirm({
                            title: "提交审核确认",
                            content:
                              "您将对本资源提交审核，审核结果以行内审批系统为准，确认提交审核吗？",
                            okText: "确认",
                            cancelText: "取消",
                            onCancel() {},
                            onOk() {
                              onSubmit("issued")
                            },
                          })
                        }
                      >
                        发布并提交审核
                      </Button>
                    ) : (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </>
                )}
              </Space>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <WibotMaterialPreview
              type="session"
              listData={formatResourcePreviewList([
                {
                  type: "copyWriter",
                  content: scriptText,
                },
                {
                  type: "Video",
                  videoUrl: videoUrl,
                },
              ])}
            />
          </Col>
        </Row>
      </Spin>
    </div>
  )
}
export default withRouter(Video)
