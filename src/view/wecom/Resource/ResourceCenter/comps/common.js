/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/09/14 10:01
 * @LastEditTime: 2025/05/06 15:45
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/comps/common.js
 * @Description: '资源中心相关公用方法'
 */
import moment from "moment"
// 只能选择当前日期之后的日期
export const disabledDate = (current) => current < moment().subtract(1, "day")

// 只能选择当前时间之后的时间点
export const disabledTime = (date) => {
  let currentDay = moment().date() // 当下的时间
  let currentHours = moment().hours()
  let currentMinutes = moment().minutes() // 设置的时间
  let settingHours = moment(date).hours()
  let settingDay = moment(date).date()
  if (date && settingDay === currentDay && settingHours === currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 设置为当天现在这小时，禁用该小时，该分钟之前的时间
      disabledMinutes: () => range(0, currentMinutes - 1),
    }
  } else if (date && settingDay === currentDay && settingHours > currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 设置为当天现在这小时之后，只禁用当天该小时之前的时间
    }
  } else if (date && settingDay === currentDay && settingHours < currentHours) {
    return {
      disabledHours: () => range(0, currentHours - 1), // 若先设置了的小时小于当前的，再设置日期为当天，需要禁用当天现在这小时之前的时间和所有的分
      disabledMinutes: () => range(0, 59),
    }
  } else if (date && settingDay > currentDay) {
    return {
      disabledHours: () => [], // 设置为当天之后的日期，则不应有任何时间分钟的限制
      disabledMinutes: () => [],
    }
  }
}

const range = (start, end) => {
  const result = []
  for (let i = start; i <= end; i++) {
    result.push(i)
  }
  return result
}

/**
 * @description 忽略无效资源（即除了 type 外，所有字段都为空、空字符串、空数组或 null/undefined）
 *              有效字段包括字符串、非空数组、非 null/undefined 的值
 * @param {Array<Object>} resources - 素材数组，每项应包含 type 字段及其对应内容字段（如 imageUrl、title、content 等）
 * @returns {Array<Object>} 过滤掉无有效内容的素材项，保留有值的资源列表
 * <AUTHOR>
 * @date 2025/04/30 11:40
 */
export const formatResourcePreviewList = (resources = []) => {
  const hasValidValue = (res) => {
    if (!res || typeof res !== "object") return false

    return Object.entries(res).some(([key, val]) => {
      if (key === "type") return false // 忽略 type 字段本身
      if (Array.isArray(val)) return val.length > 0
      if (typeof val === "string") return val?.trim() !== ""
      return val !== null && val !== undefined
    })
  }

  return resources.filter(hasValidValue)
}
