/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/29 15:56
 * @LastEditTime: 2025/05/07 09:50
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/form.jsx
 * @Description: '新增（编辑）资源'
 */
import { <PERSON><PERSON>, Card, Tabs } from "antd"
import { qs2obj } from "common/object"
import { apiCall } from "common/utils"
import { isV1, isV2 } from "config"
import React, { useEffect, useState } from "react"
import Applet from "./comps/Applet/home"
import FaceToFace from "./comps/FaceToFace/home"
import PageArticle from "./comps/PageArticle/home"
import Picture from "./comps/Picture/home"
import Poster from "./comps/Poster/home"
import Product from "./comps/Product/home"
import Text from "./comps/Text/home"
import Tweet from "./comps/Tweet/home"
import Video from "./comps/Video/home"
import "./form.less"

const { TabPane } = Tabs

const ResourceCenterForm = (props) => {
  const [id, setId] = useState(null)
  const [copyId, setCopyId] = useState(null)
  const [tabsIndex, setTabsIndex] = useState("")
  const [reviewSetting, setReviewSetting] = useState(null)

  useEffect(() => {
    const { id, copyId, type } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      onChangeTabs(type)
      return
    } else if (copyId) {
      setCopyId(copyId)
      onChangeTabs(type)
      return
    }
    setTabsIndex(localStorage.getItem("TabsResourceActiveKey") || "Article")
    getGlobalSetting()
  }, [])

  const getGlobalSetting = () => {
    apiCall("/globalSetting/getOneByValid", "GET")
      .then((res) => {
        const {
          Article = false,
          MINI_PROGRAM = false,
          Picture = false,
          copyWriter = false,
          Video = false,
          pageArticle = false,
        } = res.reviewSetting
        setReviewSetting({
          Article,
          MINI_PROGRAM,
          Picture,
          copyWriter,
          Video,
          pageArticle,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const onChangeTabs = (type) => {
    getGlobalSetting()
    localStorage.setItem("TabsResourceActiveKey", type)
    setTabsIndex(type)
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  return (
    <div className="ResourceCenterForm">
      <Card
        extra={
          <Button type="primary" onClick={() => handleGoBack()}>
            返回
          </Button>
        }
        title={id ? "编辑资源" : "新增资源"}
        bordered={false}
        bodyStyle={{ display: "none" }}
      ></Card>
      <br />
      <Card bordered={false}>
        <Tabs
          destroyInactiveTabPane
          activeKey={tabsIndex}
          onChange={onChangeTabs}
        >
          <TabPane
            tab={isV1() ? "推文" : "链接"}
            key="Article"
            disabled={!!id || !!copyId}
          >
            <Tweet reviewSetting={reviewSetting?.Article} />
          </TabPane>
          <TabPane
            tab={isV1() ? "网页文章" : "雷达海报"}
            key="pageArticle"
            disabled={!!id || !!copyId}
          >
            <PageArticle reviewSetting={reviewSetting?.pageArticle} />
          </TabPane>
          {isV1() ? (
            <TabPane tab="产品" key="Product" disabled={!!id || !!copyId}>
              <Product />
            </TabPane>
          ) : (
            ""
          )}
          {isV2() ? (
            <TabPane
              tab="面对面素材"
              key="FACE_PRODUCT"
              disabled={!!id || !!copyId}
            >
              <FaceToFace></FaceToFace>
            </TabPane>
          ) : (
            ""
          )}
          <TabPane tab="图片" key="Picture" disabled={!!id || !!copyId}>
            <Picture reviewSetting={reviewSetting?.Picture} />
          </TabPane>
          <TabPane tab="文案" key="copyWriter" disabled={!!id || !!copyId}>
            <Text reviewSetting={reviewSetting?.copyWriter} />
          </TabPane>
          <TabPane tab="视频" key="Video" disabled={!!id || !!copyId}>
            <Video reviewSetting={reviewSetting?.Video} />
          </TabPane>
          <TabPane tab="小程序" key="MINI_PROGRAM" disabled={!!id || !!copyId}>
            <Applet reviewSetting={reviewSetting?.MINI_PROGRAM} />
          </TabPane>
          <TabPane tab="海报" key="POSTER_TOOL" disabled={!!id || !!copyId}>
            <Poster />
          </TabPane>
          {/* <TabPane tab="朋友圈素材" key="Moment_Material" disabled={!!id}>
            <Circle onChangeTabs={onChangeTabs} />
          </TabPane> */}
        </Tabs>
      </Card>
    </div>
  )
}
export default ResourceCenterForm
