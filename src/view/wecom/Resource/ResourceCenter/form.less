.ResourceCenterForm {
  // .pre-wrap {
  //   text-align: center;

  //   .phone-box {
  //     position: relative;
  //     width: 306px;
  //     height: 600px;
  //     margin: auto;

  //     .phone_box_img {
  //       width: 100%;
  //       height: 100%;
  //       object-fit: contain;
  //     }

  //     .title {
  //       position: absolute;
  //       top: 55px;
  //       left: 125px;
  //     }

  //     .text {
  //       position: absolute;
  //       top: 100px;
  //       left: 40px;
  //       width: 220px;
  //       text-align: left;
  //       color: #8b8b8b;
  //       font-size: 12px;
  //     }

  //     .content {
  //       position: absolute;
  //       top: 105px;
  //       left: 22px;

  //       .list-item {
  //         // display: flex;
  //         // flex-wrap: wrap;
  //         padding: 0;
  //         margin: 0;
  //         height: 450px;
  //         overflow-y: scroll;
  //         text-align: left;

  //         .item {
  //           // display: flex;
  //           // flex-direction: row;
  //           // width: 205px;
  //           // height: 300px;
  //           // overflow-y: scroll;
  //           display: block;
  //           // padding: 10px;
  //           // border-radius: 6px;
  //           // box-shadow: 1px 2px 3px 0px #ccc;
  //           // border: 1px solid #ccc;
  //           margin: 0 10px 20px;
  //           position: relative;
  //           // cursor: pointer;

  //           .title {
  //             margin: 0;
  //             font-size: 16px;
  //           }

  //           .describe {
  //             max-width: 250px;
  //             display: inline-block;
  //             padding: 10px;
  //             border-radius: 6px;
  //             box-shadow: 1px 2px 3px 0px #ccc;
  //             border: 1px solid #ccc;
  //             margin: 0;
  //             white-space: pre-wrap;
  //           }

  //           .content {
  //             margin: 10px 0;
  //           }

  //           .review-wrap {
  //             display: inline-block;
  //             vertical-align: top;
  //             width: 194px;

  //             .ant-typography {
  //               width: 100%;

  //               p {
  //                 display: inline-block;
  //                 word-break: break-all;
  //               }
  //             }
  //           }
  //         }

  //         // 滚动条整体部分
  //         &::-webkit-scrollbar {
  //           width: 6px; //对垂直方向滚动条
  //           height: 6px; //对水平方向滚动条
  //         }

  //         //滚动的滑块
  //         &::-webkit-scrollbar-thumb {
  //           border-radius: 6px;
  //           background-color: #f2f2f2 //滚动条的颜色
  //         }

  //         //内层滚动槽
  //         &::-webkit-scrollbar-track-piece {
  //           background-color: #f2f2f2;
  //         }
  //       }
  //     }
  //   }
  // }

  .textarea-emoji {
    position: relative;

    .textarea-emoji_icon {
      position: absolute;
      bottom: -20px;
      right: 62px;
      font-size: 16px;
      color: #6f7072;
      cursor: pointer;
      -webkit-font-smoothing: antialiased;

      &:hover {
        color: #333;
      }
    }
  }

  .textArea-big {
    textarea {
      height: 150px !important;
    }
  }

  .textArea-mid {
    textarea {
      height: 100px !important;
    }
  }

  .formNoMargin {
    margin-bottom: 0px;
  }

  .ResourceApplet {
    .applet-tips {
      background: #fffae2;
      padding: 5px 10px;
      margin-bottom: 24px;

      .applet-tips_get {
        color: #0000ff;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .ResourcePoster {
    .copyWriter_item {
      position: relative;
      width: 500px;
      box-shadow: 1px 2px 3px 0px #ccc;
      padding: 10px;

      .copyWriter_item_title {
        font-weight: bold;
      }

      .copyWriter_item_Close {
        position: absolute;
        top: 0px;
        right: -40px;
        font-size: 20px;
        color: #d9d9d9;
        cursor: pointer;

        &:hover {
          color: #1989fa;
        }
      }
    }
  }
  
  .input-content-mask {
    padding: 10px;
    background: #f2f2f2;
    border-radius: 6px;
  }
}
