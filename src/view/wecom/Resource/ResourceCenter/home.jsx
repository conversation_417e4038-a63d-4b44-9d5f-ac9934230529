/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/04 09:37
 * @LastEditTime: 2025/07/04 11:23
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCenter/home.jsx
 * @Description: '资源中心'
 */

import { QuestionCircleOutlined } from "@ant-design/icons"
import {
  Button,
  Card,
  DatePicker,
  Form,
  Image,
  Input,
  message,
  Select,
  Switch,
  Table,
  Tooltip,
  TreeSelect,
  Typography,
} from "antd"
import { timeStamp } from "common/date"
import { usePageCacheLifeCycle } from "common/hooks"
import { removeInputEmpty } from "common/regular"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { apiCall } from "common/utils"
import AppletCard from "components/AppletCard/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import FilterBar from "components/FilterBar/FilterBar"
import LinkCard from "components/LinkCard/home"
import ListOperation from "components/ListOperation/home"
import OperateModal from "components/Modal/OperateModal/index"
import SysDictLabel from "components/select/SysDictLabel"
import SysDictSelect from "components/select/SysDictSelect"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import WibotTableTag from "components/WibotTableTag/home"
import { isV2 } from "config"
import moment from "moment"
import React, { useEffect, useRef, useState } from "react"
import { withRouter } from "react-router-dom"
import CollectionModal from "./comps/CollectionModal/home"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Paragraph } = Typography
const { SHOW_PARENT } = TreeSelect
const { Option } = Select

const ResourceCenter = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [typeOptions, setTypeOptions] = useState([])
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [collectionParams, setCollectionParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [resourceTreeData, setResourceTreeData] = useState([])
  const [collectTagOption, setCollectTagOption] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "编码",
      width: "160px",
      dataIndex: "id",
      key: "id",
      align: "center",
    },
    {
      title: "资源",
      width: "250px",
      dataIndex: "title",
      key: "title",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "center" }}>
          {record.type == "Article" && <LinkCard data={record} />}
          {record.type == "pageArticle" && <LinkCard data={record} />}
          {record.type == "copyWriter" && (
            <Tooltip placement="topLeft" title={record.copyWriter}>
              <Paragraph ellipsis={{ rows: 1 }}>{record.copyWriter}</Paragraph>
            </Tooltip>
          )}
          {record.type == "Video" && (
            <FileHOC src={record.videos}>
              {(url) => (
                <video
                  style={{ maxWidth: "200px", maxHeight: "100px" }}
                  controls
                  src={url && url.length > 0 ? url[0] : ""}
                />
              )}
            </FileHOC>
          )}
          {record.type == "Product" && record.subType == "FACE" && (
            <div style={{ textAlign: "left" }}>
              <LinkCard data={record} />
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品代码：{record.productContent?.productCode || "-"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品类型：{record.productContent?.productTypeVO || "-"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品链接：
                {record.productContent?.productUrlTypeEnum == "FIXED_URL"
                  ? "固定链接"
                  : "码上赢链接"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品用途：{record.subType == "FACE" ? "面客产品" : "普通产品"}
              </div>
            </div>
          )}
          {record.type == "Product" && record.subType == "NORMAL" && (
            <div style={{ textAlign: "left" }}>
              <div>{record.productContent?.productName}</div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品代码：{record.productContent?.productCode || "-"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品类型：{record.productContent?.productTypeVO || "-"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品链接：
                {record.productContent?.productUrlTypeEnum == "FIXED_URL"
                  ? "固定链接"
                  : "码上赢链接"}
              </div>
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                产品用途：{record.subType == "FACE" ? "面客产品" : "普通产品"}
              </div>
            </div>
          )}
          {record.type == "FACE_PRODUCT" && (
            <div style={{ textAlign: "left" }}>
              {isValidLinkCard({
                title: record.title,
                description: record.description,
                url: record.url,
                image: record.images?.[0],
              }) && <LinkCard data={record} />}
              <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                素材名称：{record.productContent?.productName || "-"}
              </div>
              <div
                style={{ color: "#7F7F7F", fontSize: "12px", display: "flex" }}
              >
                内容类型：
                <SysDictLabel
                  dataset="FaceProductContentType"
                  dictkey={record.productContent?.contentType}
                />
              </div>
              {record.productContent?.contentType == "PRODUCT" && (
                <>
                  <div
                    style={{
                      color: "#7F7F7F",
                      fontSize: "12px",
                      display: "flex",
                    }}
                  >
                    产品类型：
                    <SysDictLabel
                      dataset="FaceProductType"
                      dictkey={record.productContent?.productType}
                    />
                  </div>
                  <div style={{ color: "#7F7F7F", fontSize: "12px" }}>
                    产品代码：{record.productContent?.productCode || "-"}
                  </div>
                </>
              )}
            </div>
          )}
          {record.type == "Picture" && (
            <>
              {record.images?.map((item, index) => (
                <FileHOC src={item}>
                  {(url) => (
                    <Image
                      key={index}
                      width={60}
                      height={60}
                      src={url || "error"}
                      fallback="images/fallbackImg.png"
                      style={{ objectFit: "cover" }}
                      preview
                    />
                  )}
                </FileHOC>
              ))}
            </>
          )}
          {record.type == "POSTER_TOOL" && (
            <>
              {record.images && record.images.length > 0 ? (
                record.images?.map((item, index) => (
                  <FileHOC src={item}>
                    {(url) => (
                      <Image
                        key={index}
                        width={60}
                        height={60}
                        src={url || "error"}
                        fallback="images/fallbackImg.png"
                        style={{ objectFit: "cover" }}
                        preview
                      />
                    )}
                  </FileHOC>
                ))
              ) : (
                <span>{record.posterName}</span>
              )}
            </>
          )}
          {record.type == "MINI_PROGRAM" && (
            <AppletCard data={record.miniProgram || {}} />
          )}
          {record.type == "Moment_Material" && (
            <div>
              <WibotEditorView html={record.friendContent?.text} />
              {getMomentMaterialDom(record.friendContent)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "推荐理由",
      width: "160px",
      dataIndex: "reason",
      key: "reason",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) =>
        value ? (
          <Tooltip placement="topLeft" title={value}>
            <Paragraph ellipsis={{ rows: 1 }} style={{ whiteSpace: "pre" }}>
              {value}
            </Paragraph>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      title: () =>
        isV2() ? (
          <Tooltip
            placement="top"
            title="若素材类型为面对面素材，此处展示为“文案”内容"
          >
            参考话术 <QuestionCircleOutlined />
          </Tooltip>
        ) : (
          "参考话术"
        ),
      width: "160px",
      dataIndex: "script",
      key: "script",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) =>
        value ? (
          <Tooltip placement="topLeft" title={value}>
            <Paragraph ellipsis={{ rows: 1 }} style={{ whiteSpace: "pre" }}>
              {value}
            </Paragraph>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      title: "资源状态",
      width: "160px",
      dataIndex: "resourceStatus",
      key: "resourceStatus",
      align: "center",
      render: (value, record, index) =>
        record.subType != "NORMAL" && (
          <>
            {(value == "notIssue" || value == "issued") && (
              <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
            )}
            {(value == "Added" || value == "soldOut") && (
              <Switch
                checkedChildren="已上架"
                unCheckedChildren="已下架"
                checked={value == "Added"}
                onChange={(checked) => {
                  onChangeSwitchStatus(checked, record)
                }}
              />
            )}
          </>
        ),
    },
    {
      title: "上下架时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) =>
        record.subType != "NORMAL" && (
          <>
            {record.startTime}
            <br />至<br />
            {record.endTime}
          </>
        ),
    },
    {
      title: "数据",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
      render: (value, record, index) =>
        record.subType != "NORMAL" && (
          <div style={{ textAlign: "left" }}>
            转发次数：{record.shareCount}
            <br />
            访问次数：{record.visitCount}
            <br />
            访问人数：{record.visitorCount}
          </div>
        ),
    },
    {
      title: "资源类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <SysDictLabel dataset="Resource_Type" dictkey={value} />
      ),
    },
    {
      title: "资源参数",
      width: "160px",
      dataIndex: "internalResourcesFlag",
      key: "internalResourcesFlag",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) =>
        record.subType != "NORMAL" && (
          <div>
            内部资源：{value ? "是" : "否"}
            {!value && (
              <>
                <br />
                置顶展示：{record.topFlag ? "是" : "否"}
                <br />
                本周推荐：{record.recommendFlag ? "是" : "否"}
                {(record.type == "Article" ||
                  record.type == "pageArticle" ||
                  record.type == "copyWriter" ||
                  record.type == "Video" ||
                  record.type == "Picture") && (
                  <>
                    <br />
                    加入集锦：{record.collectionFlag ? "是" : "否"}
                  </>
                )}
              </>
            )}
          </div>
        ),
    },
    {
      title: "场景类型",
      width: "160px",
      dataIndex: "sceneParentName",
      key: "sceneParentName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        if (record.subType == "NORMAL") {
          return "-"
        } else if (!value) {
          return "-"
        } else {
          return (
            <Tooltip placement="topLeft" title={value + `-${record.sceneName}`}>
              {value + `-${record.sceneName}`}
            </Tooltip>
          )
        }
      },
    },
    {
      title: "关联产品",
      width: "160px",
      dataIndex: "posterProductName",
      key: "posterProductName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip title={value || record.productContent?.productName}>
          {value || record.productContent?.productName}
        </Tooltip>
      ),
    },
    {
      title: "获客渠道",
      width: "160px",
      dataIndex: "channelName",
      key: "channelName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "可见范围",
      width: "160px",
      dataIndex: "visibleScopeDepartmentNameList",
      key: "visibleScopeDepartmentNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "资源标签",
      width: "160px",
      dataIndex: "infoTagNames",
      key: "infoTagNames",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "集锦标签",
      width: "160px",
      dataIndex: "collectTagList",
      key: "collectTagList",
      align: "center",
      render: (value, record, index) => (
        <WibotTableTag tagList={value.map((item) => item.name)} />
      ),
    },
    {
      title: "集锦营销推送次数",
      width: "160px",
      dataIndex: "pushCount",
      key: "pushCount",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handleSendStatus(record)}>{value}次</a>
      ),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [{ onClick: () => handleCopy(record), name: "复制" }]
        if (record.subType != "NORMAL") {
          if (record.resourceStatus == "notIssue") {
            opts.push({ onClick: () => handleShelves(record), name: "发布" })
          }
          if (record.resourceStatus == "issued") {
            opts.push({
              onClick: () => handleUnShelves(record),
              name: "取消发布",
            })
          }
        }
        if (
          record.resourceStatus != "issued" &&
          record.resourceStatus != "Added"
        ) {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" })
        }
        if (record.deleteFlag) {
          opts.push({ onClick: () => handleDelete(record), name: "删除" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    getInfoTypeOptions()
    getTagCategoryTreeTwo()
    getResourceCategoryTreeTwo()
    getCollectTagOption()
    fetchList()
  }, [])
  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    },
  })

  const isValidLinkCard = (linkCardData = {}) => {
    const { title = "", description = "", image = "", url = "" } = linkCardData
    return [title, description, image, url].some(
      (val) => typeof val === "string" && val?.trim() !== ""
    )
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.createTimeMin = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD"
        )
        formData.createTimeMax = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD"
        )
        delete formData.createTime
      }
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format("YYYY-MM-DD")
        formData.endTime = moment(formData.time[1]._d).format("YYYY-MM-DD")
        delete formData.time
      }
      if (formData.auditTime) {
        formData.auditTimeMin = moment(formData.auditTime[0]._d).format(
          "YYYY-MM-DD"
        )
        formData.auditTimeMax = moment(formData.auditTime[1]._d).format(
          "YYYY-MM-DD"
        )
        delete formData.auditTime
      }
      if (formData.pushTime) {
        formData.minPushTime = moment(formData.pushTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxPushTime = moment(formData.pushTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.pushTime
      }
      if (formData.resourceStatus) {
        formData.resourceStatus = [formData.resourceStatus]
      }
      console.log(`[formData]: `, formData)
      // formData.sceneIds = formData.sceneIds?.join(",") || null
      // formData.infoTagIds = formData.infoTagIds?.join(",") || null
      // formData.customerTagIds = formData.customerTagIds?.join(",") || null
      // formData.createDeptIdList = formData.createDeptIdList?.join(",") || null
      // formData.collectTagIdList = formData.collectTagIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      // formData.resourceTypes =
      //   formData.resourceTypes && formData.resourceTypes.length > 0
      //     ? formData.resourceTypes.join(",")
      //     : null
      if (formData.sceneIds) {
        formData.sceneIds = formData.sceneIds.filter(Boolean)
      }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
        needTransit: false,
      }
      apiCall("/info/infoResource/page", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  // 获取场景类型
  const getInfoTypeOptions = () => {
    apiCall("/info/scene/tree", "GET")
      .then((res) => {
        const treeData = recursionTagKeyTreeData(res)
        console.log(`[treeData]: `, treeData)
        setTypeOptions([
          {
            title: "全选",
            value: "",
            key: "",
            children: treeData,
          },
        ])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ])
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 获取资源标签
  const getResourceCategoryTreeTwo = async () => {
    const data = {
      type: "resource",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeData(res)
        console.log(`[tagTreeData]: `, tagTreeData)
        // setResourceTreeData(tagTreeData)
        setResourceTreeData([
          {
            title: "全选",
            value: "resource",
            key: "resource",
            children: tagTreeData,
          },
        ])
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 获取集锦标签
  const getCollectTagOption = async () => {
    await apiCall("/info/collectTag/select", "GET")
      .then((res) => {
        setCollectTagOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const getMomentMaterialDom = (data) => {
    if (data) {
      if (data.friendCircleType == "TEXT_LINK") {
        const newData = {
          title: data.linkTitle,
          description: data.linkDescription,
          image: data.linkCoverUrl,
          url: data.link,
        }
        return data.linkTitle ? (
          <LinkCard data={newData} />
        ) : (
          <div>{data.link}</div>
        )
      } else if (data.friendCircleType == "TEXT_IMAGE") {
        return (
          <Image.PreviewGroup>
            {data.images?.map((item, index) => (
              <div
                key={index}
                style={{
                  display: "inline-block",
                  width: "60px",
                  margin: "10px 10px 0 0",
                }}
              >
                <FileHOC src={item}>{(url) => <Image src={url} />}</FileHOC>
              </div>
            ))}{" "}
          </Image.PreviewGroup>
        )
      } else {
        return (
          <FileHOC src={data.videos && data.videos[0]}>
            {(url) => <video style={{ width: "100%" }} controls src={url[0]} />}
          </FileHOC>
        )
      }
    } else {
      return "-"
    }
  }

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/resourceCenter/form")
  }

  // 集锦营销推送次数
  const handleSendStatus = (record) => {
    setCollectionParams({
      visible: true,
      resourceId: record.id,
      onCancel: () => {
        setCollectionParams({ visible: false })
      },
    })
  }

  // 发布
  const handleShelves = (record) => {
    const { id, resourceStatus } = record
    const data = {
      infoResourceId: id,
      resourceStatus,
    }
    apiCall("/info/infoResource/upResourceStatusAdded", "PUT", data)
      .then((res) => {
        message.success("发布成功！")
        fetchList()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 取消发布
  const handleUnShelves = (record) => {
    const { id, resourceStatus } = record
    const data = {
      infoResourceId: id,
      resourceStatus,
    }
    apiCall("/info/infoResource/upResourceStatusUnPublish", "PUT", data)
      .then((res) => {
        message.success("取消发布成功！")
        fetchList()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 编辑
  const handleEdit = (record) => {
    const { id, type, subType } = record
    props.history.push({
      pathname: "/wecom/resourceCenter/form",
      search:
        type == "Product"
          ? `?id=${id}&type=${type}&subType=${subType}`
          : `?id=${id}&type=${type}`,
    })
  }

  // 复制
  const handleCopy = (record) => {
    const { id, type, subType } = record
    props.history.push({
      pathname: "/wecom/resourceCenter/form",
      search:
        type == "Product"
          ? `?copyId=${id}&type=${type}&subType=${subType}`
          : `?copyId=${id}&type=${type}`,
    })
  }

  // 删除
  const handleDelete = (record) => {
    const { title, id, type, typeName } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content:
        type == "Article"
          ? `您将删除的资源为推文【${title}】，确认继续？`
          : `您将删除的资源为${typeName}，确认继续？`,
      onSubmit: () => {
        apiCall(`/info/infoResource/${id}`, "DELETE")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  // 判断当前时间是否在上下架时间内，不在则禁选
  const resourceStatusDisabled = (record) => {
    const { startTime, endTime } = record
    const curDate = new Date()
    const beginDate = new Date(startTime)
    const endDate = new Date(endTime)
    if (curDate >= beginDate && curDate <= endDate) {
      return false
    }
    return true
  }

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record
    const data = {
      infoResourceId: id,
      resourceStatus: checked ? "Added" : "soldOut",
    }
    apiCall("/info/infoResource/upResourceStatus", "PUT", data)
      .then((res) => {
        message.success("修改成功！")
        fetchList()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="resourceCenter">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="searchStr"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input
              placeholder="资源名称、资源内容、推荐理由、参考话术"
              allowClear
              style={{ width: "310px" }}
            />
          </FormItem>
          <FormItem name="resourceTypes">
            <SysDictSelect
              dataset="Resource_Type"
              placeholder="资源类型"
              mode="multiple"
            />
          </FormItem>
          <FormItem name="sceneIds">
            <TreeSelect
              treeData={typeOptions}
              treeCheckable
              treeDefaultExpandedKeys={null}
              allowClear
              showArrow
              showSearch
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              treeNodeFilterProp="title"
              placeholder="场景类型"
            />
          </FormItem>
          <FormItem name="resourceStatus">
            <SysDictSelect dataset="RESOURCE_STATUS" placeholder="资源状态" />
          </FormItem>
          {/* <FormItem name="customerTagIds"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={["customer"]} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            labelTreeData={labelTreeData}
          />
          <FormItem name="infoTagIds">
            <TreeSelect
              treeData={resourceTreeData}
              treeCheckable
              treeDefaultExpandedKeys={["resource"]}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp="title"
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder="资源标签"
            />
          </FormItem>
          {/*<CustomTagSelect*/}
          {/*  creatable*/}
          {/*  name="resourceTagNameList"*/}
          {/*  placeholder="资源标签"*/}
          {/*  useRefForm={formRef}*/}
          {/*  labelTreeData={resourceTreeData}*/}
          {/*/>*/}
          <FormItem name="collectTagIdList">
            <Select
              placeholder="集锦标签"
              fieldNames={{ label: "name", value: "id" }}
              options={collectTagOption}
              allowClear
              showSearch
              showArrow
              mode="multiple"
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem name="topFlag">
            <Select placeholder="置顶展示" allowClear>
              <Option value={1}>是</Option>
              <Option value={0}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="collectionFlag">
            <Select placeholder="加入集锦" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="recommendFlag">
            <Select placeholder="本周推荐" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="internalResourcesFlag">
            <Select placeholder="内部资源" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="time" label="上下架时间">
            <RangePicker />
          </FormItem>
          <FormItem name="pushTime" label="集锦营销推送时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新建资源
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <CollectionModal {...collectionParams} />
    </div>
  )
}

export default withRouter(ResourceCenter)
