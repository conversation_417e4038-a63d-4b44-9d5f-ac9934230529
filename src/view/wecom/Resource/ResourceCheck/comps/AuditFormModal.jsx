/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/11 14:02
 * @LastEditTime: 2022/11/30 10:15
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ResourceCheck\comps\AuditFormModal.jsx
 * @Description: '资源审核对话框'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Spin, Switch } from 'antd';
import { apiCall } from 'common/utils';

const FormItem = Form.Item;

const AuditFormModal = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [configInfo, setConfigInfo] = useState(null);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getInfoData();
    }
  }, [props]);

  const getInfoData = () => {
    setLoading(true);
    apiCall('/globalSetting/getOneByValid', 'GET').then((res) => {
      setConfigInfo(res);
      const { Article = false, MINI_PROGRAM = false, Poster = false, copyWriter = false, Video = false, pageArticle = false } = res.reviewSetting;
      formRef.current.setFieldsValue({
        Article,
        MINI_PROGRAM,
        Poster,
        copyWriter,
        Video,
        pageArticle
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        id: configInfo?.id || null,
        reviewSetting: { ...formData }
      };
      apiCall('/globalSetting/addOrModify', 'POST', data).then((res) => {
        message.success('配置成功！');
        props.params?.onSubmit?.();
        onCancel();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setConfigInfo(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={456}
      title="审核配置"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <p>请根据需要开启需要审核的资源类型，开启后创建时需审核通过后才可以发布及使用，关闭则按不审核处理。</p>

        <Form layout={'inline'} ref={formRef}>
          <FormItem name="Poster" label="图片" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem name="Article" label="推文" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem name="pageArticle" label="文章" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem name="Video" label="视频" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem name="copyWriter" label="文案" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem name="MINI_PROGRAM" label="小程序" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>
        </Form>

      </Spin>
    </Modal>
  );
};

export default AuditFormModal;
