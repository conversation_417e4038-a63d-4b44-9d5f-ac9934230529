/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/31 11:01
 * @LastEditTime: 2023/11/15 11:23
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceCheck/home.jsx
 * @Description: '资源审核记录'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import { Form, Input, Button, Table, Card, Tooltip, Image, DatePicker } from 'antd';
import SysDictLabel from 'components/select/SysDictLabel';
import SysDictSelect from 'components/select/SysDictSelect';
import AuditFormModal from './comps/AuditFormModal';
import LinkCard from 'components/LinkCard/home';
import AppletCard from 'components/AppletCard/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ResourceCheck = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [auditFormParams, setAuditFormParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '审核流水号',
      width: '160px',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '资源',
      width: '250px',
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      render: (value, record, index) =>
      (
        <div style={{ textAlign: 'left' }}>
          {
            record.type == 'Article' && <LinkCard data={record} />
          }
          {
            record.type == 'pageArticle' && <LinkCard data={record} />
          }
          {
            record.type == 'copyWriter' && <Tooltip placement="topLeft" title={record.copyWriter}>{record.copyWriter}</Tooltip>
          }
          {
            record.type == 'Video' && <FileHOC src={record.videos && record.videos.length > 0 ? record.videos[0] : ''}>
              {(url) => (
                <video style={{ maxWidth: '200px', maxHeight: '100px' }} 
                controls 
                src={url} />
              )}
            </FileHOC>
          }
          {
            record.type == 'MINI_PROGRAM' && <AppletCard data={record.miniProgram || {}} />
          }
          {
            record.type == 'Poster' && <>
              {
                record.images?.map((item, index) => (
                  <FileHOC src={item || 'error'}>
                    {(url) => (
                      <Image
                        key={index}
                        width={60}
                        height={60}
                        src={url}
                        fallback="images/fallbackImg.png"
                        style={{ objectFit: 'cover' }}
                        preview
                      />
                    )}
                  </FileHOC>
                ))
              }
            </>
          }
        </div>
      )

    },
    {
      title: '资源类型',
      width: '160px',
      dataIndex: 'type',
      key: 'type',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <SysDictLabel dataset="Audit_Resource_Type" dictkey={value} />,
    },
    // {
    //   title: '资源可见范围',
    //   width: '160px',
    //   dataIndex: 'departmentName',
    //   key: 'departmentName',
    //   ellipsis: 'true',
    //   align: 'center',
    //   render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    // },
    {
      title: '申请人/申请时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.applicantName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime)
    },
    {
      title: '审核人/审核时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.reviewerName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime)
    },
    {
      title: '审核状态',
      width: '160px',
      dataIndex: 'reviewState',
      key: 'reviewState',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <SysDictLabel dataset="RESOURCECHECKSTATE" dictkey={value} color />
    },
    {
      title: '审核备注',
      width: '160px',
      dataIndex: 'reviewRemarks',
      key: 'reviewRemarks',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    // {
    //   title: '操作',
    //   width: '120px',
    //   align: 'center',
    //   fixed: 'right',
    //   render: (value, record, index) => (
    //     <>
    //       <a onClick={() => handleDetails(record, index)}>详情</a>
    //     </>
    //   )
    // },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.endTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.startUpdateTime = moment(formData.updateTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.endUpdateTim = moment(formData.updateTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.updateTime;
      }
      formData.applicantDeptIdList = formData.applicantDeptIdList?.join(',') || null;
      formData.reviewerDeptIdList = formData.reviewerDeptIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/info/informationReviewRecord/page', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleConfig = () => {
    setAuditFormParams({
      visible: true,
      onCancel: () => {
        setAuditFormParams({ visible: false });
      },
      onSubmit: () => {
        // setAuditFormParams({ visible: false });
      },
    });
  };

  const handleDetails = (record, index) => {
    console.log('详情');
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='resourceCheck'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="serialNumber" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="审核流水号" allowClear />
          </FormItem>
          <FormItem name="resourceTitle" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="资源名称" allowClear />
          </FormItem>
          <FormItem name="type">
            <SysDictSelect placeholder="资源类型" dataset="Audit_Resource_Type" />
          </FormItem>
          {/* <FormItem name="departmentId" >
            <Cascader
              placeholder="可见范围"
              options={deptTreeOptions}
              multiple
              allowClear
              maxTagCount="responsive"
            />
          </FormItem> */}
          <FormItem name="applicantDeptIdList" style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}>
            <ETypeTransferModal title="申请人" />
          </FormItem>
          <FormItem name="createTime" label="申请时间">
            <RangePicker />
          </FormItem>
          <FormItem name="reviewerDeptIdList" style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}>
            <ETypeTransferModal title="审核人" />
          </FormItem>
          <FormItem name="updateTime" label="审核时间">
            <RangePicker />
          </FormItem>
          <FormItem name="reviewState">
            <SysDictSelect placeholder="审核状态" dataset="RESOURCECHECKSTATE" />
          </FormItem>
          <FormItem name="reviewRemarks" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="审核备注" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleConfig()}>审核配置</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
      <AuditFormModal params={auditFormParams} />
    </div>
  );
};
export default withRouter(ResourceCheck);
