/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/15 16:25
 * @LastEditTime: 2024/11/14 17:32
 * @LastEditors: Jana<PERSON>w
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceSet/form.jsx
 * @Description: ''
 */

import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select, Spin, Card, Button, Space, Row, Col, Typography, message } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import MaterialListForm from "components/MaterialListForm/home";
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import './form.less';

const FormItem = Form.Item;
const { TextArea } = Input;
const { Title, Paragraph, } = Typography;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

const ResourceSetForm = (props) => {
  const [id, setId] = useState(null);
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [loading, setLoading] = useState(false);
  const [setGroupOption, setSetGroupOption] = useState([]);
  const [internalResourcesFlag, setInternalResourcesFlag] = useState(false);
  const [resourceStatus, setResourceStatus] = useState(false);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    getSetGroupOption();
    if (id) {
      setId(id)
      getInfoData(id);
    }
  }, []);

  const getSetGroupOption = () => {
    setLoading(true);
    apiCall("/info/resourceSetGroup", "GET", {
      paged: false
    })
      .then((res) => {
        const { records } = res;
        setSetGroupOption(records);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoData = (id) => {
    setLoading(true);
    apiCall(`/info/resourceSet/${id}`, 'GET').then((res) => {
      const { name, groupId, itemList, resourceList, useDesc } = res;
      formForm.setFieldsValue({
        name,
        groupId,
        itemList,
        useDesc,
      });
      onRefMaterialListForm.current.getInitMsgList([...resourceList.map((item) => {
        if (item.type == 'copyWriter') {
          return {
            ...item,
            content: item.copyWriter
          }
        } else if (item.type == 'MINI_PROGRAM') {
          return {
            ...item,
            fileId: [item.miniProgram.fileId],
            appId: item.miniProgram.appId,
            url: item.miniProgram.page,
            title: item.miniProgram.title,
          }
        } else if (item.type == 'Video') {
          return {
            ...item,
            fileId: item.videos,
          }
        } else if (item.type == 'Picture') {
          return {
            ...item,
            fileId: item.images,
          }
        } else if (item.type == 'POSTER_TOOL') {
          return {
            ...item,
            type: 'Poster'
          }
        } else {
          return {
            ...item
          }
        }
      })]);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const data = {
        id: id || null,
        ...formData,
      }
      const apiUrl = id ? `/info/resourceSet/update/${id}` : `/info/resourceSet`
      apiCall(apiUrl, 'POST', data).then((res) => {
        message.success(id ? '编辑成功！' : '新增成功！');
        clearCache()
        props.history.go(-1);
      }).catch((err) => {
        console.log(err);
      }).finally(() => {
        setLoading(false);
      })
    });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div
      className='ResourceSet-Form-Container'
    >
      <Spin spinning={loading}>
        <Card
          title={(id ? '编辑' : '新增') + '资源集'}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
        >
          <Row gutter={10}>
            <Col xs={24} lg={16}>
              <Form
                {...layout}
                form={formForm}
              >
                <FormItem
                  name="name"
                  label="资源集名称"
                  getValueFromEvent={(e) => removeInputEmpty(e)}
                  rules={[{ required: true, message: '请输入资源集名称', }]}
                >
                  <Input placeholder="请输入资源集名称" allowClear />
                </FormItem>

                <FormItem
                  name="groupId"
                  label="资源集分组"
                  rules={[{ required: true, message: '请选择资源集分组', }]}
                >
                  <Select
                    placeholder="请选择资源集分组"
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={setGroupOption}
                    allowClear
                    showSearch
                    showArrow
                    maxTagCount="responsive"
                    filterOption={(input, option) =>
                      (option?.name ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  />
                </FormItem>

                <FormItem
                  name="useDesc"
                  label="使用说明"
                >
                  <TextArea
                    showCount
                    maxLength={100}
                    allowClear
                    autoSize={{ minRows: 4, maxRows: 6 }}
                    placeholder="请输入使用说明"
                  />
                </FormItem>

                <FormItem
                  name="itemList"
                  label="资源列表"
                  rules={[{ required: true, message: '请选择资源列表', }]}
                  extra={<>
                    {internalResourcesFlag && <p style={{ margin: "unset" }}>当前资源集包含内部资源，资源集只能在后台使用。</p>}
                    {resourceStatus && <p style={{ margin: "unset" }}>当前资源集包含已下架资源，使用时将自动忽略这些资源。</p>}
                  </>}
                >
                  <MaterialListForm
                    ref={onRefMaterialListForm}
                    params={{
                      formRef: formForm,
                      isNickname: false,
                      limitNineFlag: false,
                      isCopyWriterDisabled: true,
                      isOpenTag: true,
                      isSort: true,
                      compress: false,
                      labelCol: 4,
                      menuList: ['copyWriter', 'image', 'material'],
                      materialTabList: [
                        "Article",
                        "pageArticle",
                        "Video",
                        "copyWriter",
                        "MINI_PROGRAM",
                        "Picture",
                        "Poster",
                        "Product",
                      ],
                    }}
                    // 监听回调
                    callback={(params) => {
                      console.log(params, 'params');
                      setInternalResourcesFlag(params.data?.some(item => item.internalResourcesFlag))
                      setResourceStatus(params.data?.some(item => item.resourceStatus && item.resourceStatus != 'Added'))

                      formForm.setFieldsValue({
                        itemList: params.data?.map((item) => {
                          if (item.id) {
                            return {
                              id: item.id,
                              type: 'RESOURCE',
                            }
                          } else {
                            return {
                              type: item.type,
                              copyWriter: item.content,
                              images: item.image && [item.image],
                            }
                          }
                        }),
                      });
                    }}
                  />
                </FormItem>
              </Form>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Space size={40}>
                  <Button onClick={() => handleGoBack()}>取消</Button>
                  <Button type="primary" onClick={() => onSubmit()}>保存</Button>
                </Space>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <Typography>
                <Title level={3}>资源搭配说明</Title>
                <Paragraph>资源集的资源类型与数量在创建、编辑时不受限制，但使用时可能受限于具体的功能，详情说明如下：</Paragraph>
                <Title level={3}>群发任务的群发客户、群发客户群</Title>
                <Paragraph>一个文案（可选）和最多九个其他类型资源</Paragraph>
                <Title level={3}>群发任务的群发朋友圈</Title>
                <Paragraph>一个文案（可选）和最多九张图片，或者一个视频，或者一个链接（推文、网页文章、产品）</Paragraph>
              </Typography>
            </Col>
          </Row>
        </Card>
      </Spin>
    </div>
  );
};

export default ResourceSetForm;
