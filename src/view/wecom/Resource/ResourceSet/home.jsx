/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/12 09:32
 * @LastEditTime: 2025/05/16 14:37
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ResourceSet/home.jsx
 * @Description: '资源集'
 */

import React, { useState, useEffect, useRef } from "react"
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  message,
  Select,
} from "antd"
import FilterBar from "components/FilterBar/FilterBar"
import { apiCall } from "common/utils"
import { timeStamp } from "common/date"
import { removeInputEmpty } from "common/regular"
import OperateModal from "components/Modal/OperateModal/index"
import SetGroupModal from "./comps/SetGroupModal"
import ListOperation from "components/ListOperation/home"
import WibotMaterialPreviewModal from "components/WibotMaterialPreview/modal"

const FormItem = Form.Item

const ResourceSet = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [setGroupOption, setSetGroupOption] = useState([])
  const [SetGroupParams, setSetGroupParams] = useState({ visible: false })
  const [formParams, setFormParams] = useState({ visible: false })
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "编码",
      width: "160px",
      dataIndex: "id",
      key: "id",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "分组",
      width: "160px",
      dataIndex: "groupName",
      key: "groupName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "资源数",
      width: "160px",
      dataIndex: "resourceCount",
      key: "resourceCount",
      align: "center",
    },
    {
      title: "上架中的资源数",
      width: "160px",
      dataIndex: "onShelfResourceCount",
      key: "onShelfResourceCount",
      align: "center",
    },
    {
      title: "预览",
      width: "160px",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "enable",
      key: "enable",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        switch (value) {
          case true:
            value = "启用"
            break
          case false:
            value = "停用"
            break
        }
        return (
          <Tooltip placement="topLeft" title={value}>
            {value}
          </Tooltip>
        )
      },
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      fixed: "right",
      align: "center",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" },
        ]
        if (record.enable) {
          opts.push({ onClick: () => handleChangeEnable(record), name: "停用" })
        } else {
          opts.push({ onClick: () => handleChangeEnable(record), name: "启用" })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]
  const [resourcePreviewParams, setResourcePreviewParams] = useState({
    visible: false,
  })

  useEffect(() => {
    getSetGroupOption()
    fetchList()
  }, [])

  const getSetGroupOption = () => {
    setLoading(true)
    apiCall("/info/resourceSetGroup", "GET", {
      paged: false,
    })
      .then((res) => {
        const { records } = res
        setSetGroupOption(records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      formData.groupIdList = formData.groupIdList?.join(",") || null
      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      }
      apiCall("/info/resourceSet", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 分组管理
  const handleSetGroup = () => {
    setSetGroupParams({
      visible: true,
      onCancel: () => {
        setSetGroupParams({ visible: false })
        getSetGroupOption()
      },
    })
  }

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/resourceSet/form")
    return false
    setFormParams({
      visible: true,
      onOk: (params) => {
        setFormParams({ visible: false })
        message.success("新增成功！")
        fetchList()
      },
      onCancel: () => {
        setFormParams({ visible: false })
      },
    })
  }

  // 编辑
  const handleEdit = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/resourceSet/form",
      search: `?id=${id}`,
    })
    return false
    setFormParams({
      visible: true,
      id: id,
      onOk: (params) => {
        setFormParams({ visible: false })
        message.success("编辑成功！")
        fetchList()
      },
      onCancel: () => {
        setFormParams({ visible: false })
      },
    })
  }

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/info/resourceSet/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  // 启用/停用
  const handleChangeEnable = (record) => {
    const { id, enable, name } = record
    setOperateParams({
      visible: true,
      title: enable ? "停用确认" : "启用确认",
      content: `您将为【${name}】进行操作，确认继续吗？`,
      onSubmit: () => {
        const data = {
          enable: !enable,
        }
        apiCall(`/info/resourceSet/update/${id}`, "POST", data)
          .then((res) => {
            message.success("修改成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handlePreview = (record) => {
    let { resourceList } = record
    let newDataSource = []
    resourceList?.forEach((item) => {
      if (item.type == "Video") {
        newDataSource.push({
          ...item,
          videoUrl: item.videos ? item.videos[0] : "",
        })
      } else if (item.type == "MINI_PROGRAM") {
        newDataSource.push({
          ...item,
          fileId: [item.miniProgram.fileId],
          appId: item.miniProgram.appId,
          url: item.miniProgram.page,
          title: item.miniProgram.title,
        })
      } else if (item.type == "POSTER_TOOL") {
        newDataSource.push({
          ...item,
          type: "Poster",
          image: item.images[0],
        })
      } else if (item.type == "Picture") {
        newDataSource.push({
          ...item,
          image: item.images[0],
        })
      } else {
        newDataSource.push({
          ...item,
          content: item.copyWriter,
          image: item.fileId ? item.fileId[0] : "",
        })
      }
    })
    setResourcePreviewParams({
      visible: true,
      listData: newDataSource,
      type: "session",
      onCancel: () => {
        setResourcePreviewParams({
          visible: false,
        })
      },
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="ResourceSet-Container">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="id" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="编码" allowClear />
          </FormItem>

          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="名称" allowClear />
          </FormItem>

          <FormItem name="groupIdList">
            <Select
              placeholder="分组"
              fieldNames={{ label: "name", value: "id" }}
              options={setGroupOption}
              allowClear
              showSearch
              showArrow
              mode="multiple"
              maxTagCount="responsive"
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleSetGroup()}>
              分组管理
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <SetGroupModal params={SetGroupParams} />
      <WibotMaterialPreviewModal params={resourcePreviewParams} />
    </div>
  )
}

export default ResourceSet
