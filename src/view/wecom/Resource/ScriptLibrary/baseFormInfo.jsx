/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/30 15:37
 * @LastEditTime: 2023/06/05 17:19
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ScriptLibrary\baseFormInfo.jsx
 * @Description: ''
 */

import React, { useState, useEffect, useRef } from 'react';
import { apiCall } from 'common/utils';
import { Button, Card, Form, Input, Select, Upload, Spin, Row, Col, Tag, Switch, message, Typography, Image } from 'antd';
import { LoadingOutlined, PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { removeInputEmpty, normFile } from 'common/regular';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import { qs2obj } from 'common/object';
import MaterialFormModal from 'components/Modal/MaterialModal/home';
import EditFormModal from 'components/Modal/EditFormModal/home';
import MaterialListForm from 'components/MaterialListForm/home';

import './baseFormInfo.less';

const FormItem = Form.Item;
const { TextArea } = Input;
const { Paragraph } = Typography;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const BaseFormInfo = (props) => {
  const [formForm] = Form.useForm();
  const onRefMaterialListForm = useRef();
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [keyWordsTag, setKeyWordsTag] = useState([]);
  const [msgList, setMsgList] = useState([]);
  const [typeOptions, setTypeOptions] = useState([]);
  const [formModalParams, setFormModalParams] = useState({ visible: false });
  const [editFormParams, setEditFormParams] = useState({ visible: false });

  useEffect(() => {
    setLoading(true);
    getInfoTypeOptions();
    const { id } = qs2obj(props.location.search);
    if (id) {
      getInfoScript(id);
      setId(id);
    }
  }, []);

  const getInfoScript = (id) => {
    setLoading(true);
    const data = {
      id,
    };
    apiCall('/info/script/get', 'GET', data).then((res) => {
      const { title, notice, typeId, keyWords, materialLibraryList } = res;
      formForm.setFieldsValue({
        title,
        notice: notice ?? 0,
        typeId: typeId
      });
      setKeyWordsTag(keyWords ? keyWords.split(',') : []);
      onRefMaterialListForm?.current?.getInitMsgList(materialLibraryList);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoTypeOptions = () => {
    const data = {
      type: 'Script'
    };
    apiCall('/info/informationType/option', 'GET', data).then((res) => {
      setTypeOptions(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSureTag = () => {
    const value = formForm.getFieldValue('keyWords');
    const newKeyWordsTag = keyWordsTag;
    if (!!value && !new RegExp(/^[ ]+$/).test(value) && keyWordsTag.every((item) => item != value)) {
      newKeyWordsTag.push(value);
    }
    setKeyWordsTag([...newKeyWordsTag]);
    formForm.setFieldsValue({
      keyWords: '',
    });
  };
  const handleTagClose = (e, value) => {
    e.preventDefault();
    setKeyWordsTag(keyWordsTag.filter((item) => item !== value));
  };

  const onChangeSwitchUpdate = (checked) => {
    formForm.setFieldsValue({
      notice: checked ? 1 : 0
    });
  };

  /**
* @description: 自定义上传图片
* @param {*}
* @return {*}
* @Author: ZhongJunWei
* @Date: 2021/08/05 14:18
*/
  const customRequest = (config, index) => {
    const File = config.file;
    const options = {
      quality: 0.9,
    };
    let newMsgList = [...msgList];
    // 用于图片上传中限制
    newMsgList[index].imageUrl = 'loading';
    setMsgList(newMsgList);
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append('file', base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data).then((res) => {
        const { fileId, fileUrl } = res;
        newMsgList[index].imageUrl = fileUrl;
        newMsgList[index].fileId = [fileId];
        setMsgList(newMsgList);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setUploadLoading(false);
        });
    });
  };

  const onChangeUpload = (info) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
  };

  const handleDelMsg = (order) => {
    const formMsgList = [...msgList];
    const fltMsgList = formMsgList.filter((el, idx) => idx !== order);
    const fieldMsgList = formForm.getFieldValue('msgList');
    const newFieldMsgList = fieldMsgList.filter((el, idx) => idx !== order);
    formForm.setFieldsValue({
      msgList: newFieldMsgList
    });
    setMsgList(fltMsgList);
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      if (!msgList.length > 0) {
        message.error('请至少添加一条话术内容！');
        return;
      } else if (msgList.length > 9) {
        message.error('最多选择9条话术内容！');
        return;
      }
      setLoading(true);
      const { title, typeId, notice } = formData;

      const data = {
        id: id ?? null,
        title,
        notice: id ? notice : 1,
        typeId: typeId ?? null,
        keyWords: keyWordsTag.join(','),
        materialLibraryList: onRefMaterialListForm.current.getModifyMsgList()
      };
      apiCall('/info/script/addOrmodify', 'PUT', data).then((res) => {
        message.success('保存成功！');
        props.history.push('/wecom/ScriptLibrary');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });

  };

  const handleEdit = (index) => {
    const newMsgList = msgList;
    newMsgList[index].image = newMsgList[index].fileId && newMsgList[index].fileId[0] || '';
    setEditFormParams({
      visible: true,
      data: newMsgList[index],
      onOk: (data) => {
        const { title, description, image, url } = data;
        newMsgList[index].title = title;
        newMsgList[index].description = description;
        newMsgList[index].fileId = [image];
        newMsgList[index].imageUrl = image;
        newMsgList[index].url = url;
        setMsgList(newMsgList);
        setEditFormParams({ visible: false });
      },
      onCancel: () => {
        setEditFormParams({ visible: false });
      }
    });
  };

  const onLinkSearch = (event, index) => {
    const value = event.target.value;
    if (!value) {
      message.error('链接不能为空！');
      return;
    }
    if (/http(s)?:\/\/([\w-]+\.)+[\w-]+([\w- .?%&=]*)?/.test(value)) {
      setLoading(true);
      const data = {
        url: value
      };
      apiCall('/materiallibrary/getOneByUrl', 'GET', data).then((res) => {
        if (res) {
          const { title, imageUrl, description, fileId, url } = res;

          const newMsgList = msgList;
          newMsgList[index].url = url;
          if (!title) {
            message.error('暂无数据！');
            setMsgList(newMsgList);
            return;
          }
          newMsgList[index].title = title;
          newMsgList[index].imageUrl = imageUrl;
          newMsgList[index].description = description;
          newMsgList[index].fileId = fileId;
          setMsgList(newMsgList);
        } else {
          message.error('暂无数据！');
        }
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <>
      <Spin spinning={loading}>
        <Card bordered={false} title="基础信息" extra={<Button type="primary" onClick={() => handleGoBack()}>返 回</Button>} className="baseFormInfo">
          <Form {...layout} form={formForm}>

            <FormItem label="话术标题" name="title" getValueFromEvent={(e) => removeInputEmpty(e)} rules={[{ required: true, message: '请输入推文标题' }]}>
              <Input placeholder="请输入话术标题，近用于内部查看（30字以内）" allowClear />
            </FormItem>
            <FormItem label="话术类型" name="typeId" rules={[{ required: true, message: '请选择话术类型' }]}>
              {/* <TreeSelect
                placeholder='请选择话术类型'
                treeData={classDataOptions}
                treeCheckable
                treeDefaultExpandedKeys={['']}
                allowClear
                showArrow
                showSearch
                treeNodeFilterProp='title'
               maxTagCount="responsive"
                showCheckedStrategy={SHOW_PARENT}
              /> */}
              <Select
                allowClear
                options={typeOptions}
                showSearch
                placeholder="请选择"
                fieldNames={{ label: "name", value: "id" }}
                filterOption={(input, option) =>
                  (option?.name ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
            <FormItem label="关键词" extra="注：关键词用于与客户沟通时的话术匹配，系统基于【会话存档】进行会话分析时，识别到客户消息中出现了关键词，便会将匹配到的话术优先显示在企微侧边栏[会话工具]中。">
              <Row justify='space-between'>
                <Col style={{ width: '82%' }}>
                  <FormItem name="keyWords" style={{ margin: 'unset' }} extra={
                    keyWordsTag.map((item, index) => <Tag
                      closable
                      style={{ marginTop: '5px' }}
                      key={index}
                      onClose={(e) => handleTagClose(e, item)}
                    >
                      {item}
                    </Tag>)
                  }>
                    <Input placeholder="请添加关键词" onPressEnter={onSureTag} allowClear />
                  </FormItem>
                </Col>
                <Col>
                  <Button type="primary" onClick={onSureTag}>确定</Button>
                </Col>
              </Row>
            </FormItem>
            {
              id ? <FormItem name='notice' label="更新通知" extra="开启后，将会把本次更新的动态通过企微应用消息通知给可见范围内的员工" valuePropName="checked">
                <Switch onChange={onChangeSwitchUpdate} />
              </FormItem> : ''
            }
            <div style={{ fontSize: '16px' }}>话术内容</div>
            <FormItem label=" " className="formBtn" wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }}>
              <MaterialListForm
                params={{
                  formRef: formForm,
                  menuList: ['copyWriter', 'image', 'material'],
                  isNickname: false,
                  materialAmount: msgList.length,
                  extra: `请按发送顺序添加话术内容`
                }}
                // 监听回调
                callback={(params) => { setMsgList(params.data); }}
                ref={onRefMaterialListForm}
              />
            </FormItem>
            <FormItem label=" ">
              <Button type="primary" onClick={() => onSubmit()}>确 认</Button>
            </FormItem>
          </Form>
        </Card>
        <MaterialFormModal params={formModalParams} />
        <EditFormModal params={editFormParams} />
      </Spin>
    </>
  );
};
export default BaseFormInfo;
