/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/01 16:47
 * @LastEditTime: 2022/06/17 09:46
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\ScriptLibrary\comps\PreviewModal.jsx
 * @Description: ''
 */
import React, { useState, useEffect, memo, } from 'react';
import { Modal, Spin, Card, Typography, Image, Row, Col, Empty, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';

import './PreviewModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { Paragraph } = Typography;

const PreviewModal = (props) => {
  const { visible, id, onCancel } = props;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (visible) {
      getScriptData();
    }
  }, [visible]);

  const getScriptData = () => {
    setLoading(true);
    apiCall('/info/script/get', 'GET', { id }).then((res) => {
      const { materialLibraryList } = res;
      setDataSource(materialLibraryList);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <Modal
      title="话术内容"
      width={400}
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      centered
      className="PreviewModal"
    >
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          {
            dataSource && dataSource?.length > 0
              ? <>
                <ul className='list-item'>
                  {
                    dataSource?.map((item, index) =>
                      <li key={index} className={'item'}>
                        <Avatar shape="square" size="large" style={{ marginRight: '10px' }} icon={<UserOutlined />} />
                        <div key={index} style={{ display: 'inline-block', verticalAlign: 'middle' }}>
                          {
                            item.type == 'Text' ? <Paragraph key={index} className='describe' ellipsis={{ rows: 3, tooltip: true }}>{item.content}</Paragraph> : ''
                          }
                          {
                            item.type == 'Picture' || item.type == 'Product' || item.type == 'Poster' ? item.fileId?.map((atem, andex) => (
                              <FileHOC src={atem}>
                                {(url) => (
                                  <Image key={andex} style={{ margin: '10px 0' }} src={url} width={150} preview={false}/>
                                )}
                              </FileHOC>
                            )) : ''
                          }
                          {
                            item.type == 'Video' ? item.fileId && (
                              <FileHOC src={item.fileId[0]}>
                                {(url) => (
                                  <video width="150px" style={{ margin: '10px 0' }} src={url} controls />
                                )}
                              </FileHOC>
                            ) : ''
                          }
                          {
                            item.type == 'Article' || item.type == 'LINK_CARD'
                              ? item.title ? <div className="link-card" key={index}>
                                <Paragraph strong ellipsis={{ rows: 2, tooltip: true }}>{item.title}</Paragraph>
                                <Row justify="space-between">
                                  <Col span={17}>
                                    <Paragraph style={{ fontSize: '12px', lineHeight: '18px' }} ellipsis={{ rows: 3, tooltip: true }}>
                                      {item.description}
                                    </Paragraph>
                                  </Col>
                                  <Col>
                                    <FileHOC src={item.fileId && item.fileId[0] || 'error'}>
                                      {(url) => (
                                        <Image
                                          width={54}
                                          src={url}
                                          fallback="images/fallbackImg.png"
                                          preview={false}
                                        />
                                      )}
                                    </FileHOC>
                                  </Col>
                                </Row>
                              </ div> : <Paragraph className='describe' ellipsis={{ tooltip: true }}>{item.url}</Paragraph>
                              : ''
                          }
                        </div>
                      </li>
                    )
                  }
                </ul>
              </> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </Card>
      </Spin>
    </Modal>
  );
};

export default memo(PreviewModal);
