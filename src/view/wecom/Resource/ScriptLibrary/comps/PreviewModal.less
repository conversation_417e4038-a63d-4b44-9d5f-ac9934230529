.PreviewModal {
  .ant-modal-body {
    max-height: 400px;
    overflow-y: scroll;
  }

  .ant-card {
    .list-item {
      // display: flex;
      // flex-wrap: wrap;
      padding: 0;
      margin: 0;

      .item {
        // display: flex;
        // flex-direction: row;
        // width: 205px;
        // height: 300px;
        // overflow-y: scroll;
        display: block;
        // padding: 10px;
        // border-radius: 6px;
        // box-shadow: 1px 2px 3px 0px #ccc;
        // border: 1px solid #ccc;
        margin: 0 10px 20px;
        position: relative;

        // cursor: pointer;
        /* 设置滚动条的样式 */
        &::-webkit-scrollbar {
          width: 0;
        }

        /* 滚动槽 */
        &::-webkit-scrollbar-track {
          -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);
          border-radius: 10px;
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          background: rgba(0, 0, 0, 0.1);
          -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
        }

        &::-webkit-scrollbar-thumb:window-inactive {
          background: rgba(0, 0, 0, 0);
        }

        .title {
          margin: 0;
          font-size: 16px;
        }

        .describe {
          max-width: 250px;
          display: inline-block;
          padding: 10px;
          border-radius: 6px;
          box-shadow: 1px 2px 3px 0px #ccc;
          border: 1px solid #ccc;
          margin: 0;
          white-space: normal;
        }

        .content {
          margin: 10px 0;
        }

        // &.select-item {
        //   overflow: hidden;
        // }

        // .mask {
        //   position: absolute;
        //   top: 0;
        //   right: 0;
        //   bottom: 0;
        //   left: 0;
        //   z-index: 1000;
        //   height: 100%;
        //   background-color: rgba(0, 0, 0, 0.45);
        //   -webkit-overflow-scrolling: touch;

        //   .anticon {
        //     font-size: 40px;
        //     position: absolute;
        //     left: 50%;
        //     top: 50%;
        //     transform: translate(-50%, -50%);
        //   }
        // }
      }
    }

    .link-card {
      width: 230px;
    }

    .ant-pagination {
      text-align: right;
    }
  }
}