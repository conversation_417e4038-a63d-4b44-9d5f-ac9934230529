
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2023/11/21 16:25
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/comps/TypeFormModal.jsx
 * @Description: '信息资源类型管理对话框'
 */

import React, { useState, useEffect } from 'react';
import { Modal, Tooltip, Button, Table, Input, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import ListOperation from 'components/ListOperation/home';

const TypeFormModal = (props) => {
  const { visible, title, type, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [initDataSource, setInitDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '类型名称',
      width: '170px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <>
          {
            record.isEdit ? <Input
              value={value}
              onChange={(e) => {
                let newDataSource = JSON.parse(JSON.stringify(dataSource));
                newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, '');
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入"
              allowClear />
              :
              <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          }
        </>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    if (visible) {
      getGroupOptions();
    }
  }, [visible]);

  const getGroupOptions = () => {
    setLoading(true);
    const data = {
      type: type
    };
    apiCall('/info/informationType/option', 'GET', data).then((res) => {
      setDataSource(res);
      setInitDataSource(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = () => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource.unshift({
      name: '',
      isEdit: true,
    });
    setDataSource([...newDataSource]);
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    if (newDataSource.some((item) => item.isEdit)) {
      message.warning('已存在输入项，请保存后再操作！');
      return false;
    }
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { id, name } = record;
    if (name == '') {
      message.warning('名称不能为空！');
      return false;
    }
    setLoading(true);
    const data = {
      type: type,
      id: id ?? null,
      name: name
    };
    apiCall('/info/informationType/addOrmodify', 'PUT', data).then((res) => {
      message.success('保存成功！');
      getGroupOptions();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    const data = {
      id: id,
    };
    apiCall('/info/informationType/del', 'DELETE', data).then((res) => {
      message.success('删除成功！');
      let newDataSource = dataSource;
      newDataSource.splice(index, 1);
      setDataSource([...newDataSource]);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index) => {
    const { id } = record;
    let newDataSource = dataSource;
    if (id) {
      newDataSource[index].isEdit = false;
      const findData = initDataSource.find(item => item.id == id);
      findData && (newDataSource[index].name = findData.name);
    } else {
      newDataSource.splice(index, 1);
    }
    setDataSource([...newDataSource]);
  };

  return (
    <Modal
      visible={visible}
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {title}
          <Button style={{ marginRight: '20px' }} type="primary" icon={<PlusOutlined />} onClick={() => handleAdd()}>新增</Button>
        </div>
      }
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      centered
      onCancel={() => {
        setLoading(false);
        setDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default TypeFormModal;
