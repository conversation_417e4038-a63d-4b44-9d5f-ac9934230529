/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/30 09:24
 * @LastEditTime: 2023/11/22 10:07
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/ScriptLibrary/home.jsx
 * @Description: '话术库'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import { PlusOutlined } from '@ant-design/icons';
import TypeFormModal from './comps/TypeFormModal';
import OperateModal from 'components/Modal/OperateModal/index';
import PreviewModal from './comps/PreviewModal';
import { Form, Input, Button, Table, Card, Tooltip, DatePicker, Tag, Popover, TreeSelect, message } from 'antd';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { SHOW_PARENT } = TreeSelect;

const ScriptLibrary = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [typeOptions, setTypeOptions] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [previewParams, setPreviewParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '话术标题',
      width: '160px',
      dataIndex: 'title',
      key: 'title',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '话术内容',
      width: '120px',
      align: 'center',
      render: (value, record, index) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: '话术关键词',
      width: '220px',
      dataIndex: 'keyWords',
      key: 'keyWords',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        const newKeyWords = value ? value.split(',') : '';
        const content = newKeyWords ? <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {newKeyWords.map((atem, andex) => <Tag key={andex}>{atem}</Tag>)}
        </div> : null;
        return <div>
          {
            newKeyWords ? <Popover placement="topLeft" content={content}>{content}</Popover> : null
          }
          {newKeyWords && <br />}
          {record.isAddWords ? <>
            <Input style={{ width: '160px' }} placeholder="请输入话术关键词" onBlur={(e) => { onInputBlur(e, index); }} onChange={(e) => { handleInputValue(e); }} allowClear />
            <Button
              type="primary"
              onClick={() => { handleQueryFilter(value, record, index); }}
              size="small"
              style={{ width: 50, marginLeft: '5px' }}
            >
              确定
            </Button>
          </> : <Button type="primary" icon={<PlusOutlined />} size="small" onClick={() => { handleAddTag(index); }}>新增</Button>}
        </div>;
      },
    },
    {
      title: '话术类型',
      width: '160px',
      dataIndex: 'scriptTypeName',
      key: 'scriptTypeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '发送次数',
      width: '160px',
      dataIndex: 'sendNumber',
      key: 'sendNumber',
      align: 'center',
      sorter: (a, b) => a.sendNumber - b.sendNumber
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime)
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record, index), name: "编辑" },
          { onClick: () => handleDelete(record, index), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];


  useEffect(() => {
    getInfoTypeOptions();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startTime = moment(formData.createTime[0]._d).format('YYYY-MM-DD 00:00:00');
        formData.endTime = moment(formData.createTime[1]._d).format('YYYY-MM-DD 23:59:59');
        delete formData.createTime;
      }
      formData.typeId = formData.typeId?.join(',') || null;
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData
      };
      apiCall('/info/script/page', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getInfoTypeOptions = () => {
    const data = {
      type: 'Script'
    };
    apiCall('/info/informationType/option', 'GET', data).then((res) => {
      setTypeOptions([
        {
          title: '全选',
          value: '',
          key: '',
          children: res.map((item) => ({
            title: item.name,
            value: item.id,
            key: item.id,
          })
          )
        }
      ]);
    })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleScriptAddOrmodify = (params) => {
    setLoading(true);
    apiCall('/info/script/addOrmodify', 'PUT', params).then((res) => {
      fetchList();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handlePreview = (record) => {
    setPreviewParams({
      visible: true,
      id: record.id,
      onCancel: () => {
        setPreviewParams({ visible: false });
      }
    });
  };

  const handleInputValue = (e) => {
    setInputValue(e.target.value);
  };

  const handleQueryFilter = (value, record, index) => {
    if (inputValue) {
      const newValue = value ? value.split(',') : [];
      newValue.push(inputValue);
      handleScriptAddOrmodify({ keyWords: newValue.join(','), id: record.id });
      setInputValue('');
    } else {
      message.error('请输入话术关键词！');
    }
  };

  const onInputBlur = (e, index) => {
    if (!inputValue) {
      const newDataSource = dataSource;
      newDataSource[index].isAddWords = false;
      setDataSource([...newDataSource]);
    }
  };

  const handleAddTag = (index) => {
    const newDataSource = dataSource;
    newDataSource[index].isAddWords = true;
    setDataSource([...newDataSource]);
  };

  const handleType = () => {
    setTypeFormParams({
      visible: true,
      type: 'Script',
      title: '话术类型',
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getInfoTypeOptions();
      },
    });
  };

  const handleAdd = () => {
    props.history.push('/wecom/scriptLibrary/baseFormInfo');
  };

  const handleEdit = (record, index) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/scriptLibrary/baseFormInfo',
      search: `?id=${id}`
    });
  };

  const handleDelete = (record, index) => {
    const { title, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除话术【${title}】，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id
        };
        apiCall('/info/script/del', 'DELETE', data).then((res) => {
          message.success('删除成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onTableChange = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='scriptLibrary'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="title" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="话术标题" allowClear />
          </FormItem>
          <FormItem name="keyWords" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="话术关键词" allowClear />
          </FormItem>
          <FormItem name="typeId">
            <TreeSelect
              placeholder='话术类型'
              treeData={typeOptions}
              treeCheckable
              treeDefaultExpandedKeys={['']}
              allowClear
              showArrow
              showSearch
              treeNodeFilterProp='title'
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
            />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleType()}>话术类型</Button>
            <Button type="primary" onClick={() => handleAdd()}>创建话术</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onTableChange} />
      </Card>
      <TypeFormModal params={typeFormParams} />
      <OperateModal params={operateParams} />
      <PreviewModal {...previewParams} />
    </div>
  );
};

export default withRouter(ScriptLibrary);
