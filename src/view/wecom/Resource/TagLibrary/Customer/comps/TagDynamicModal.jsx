/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/03 16:42
 * @LastEditTime: 2024/01/10 10:33
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\TagLibrary\Customer\comps\TagDynamicModal.jsx
 * @Description: '客户标签画像动态策略'
 */

import React, { useEffect, useRef, useState } from 'react';
import { Form, message, Modal, Spin, Switch, InputNumber } from 'antd';
import { apiCall } from 'common/utils';

const FormItem = Form.Item;

const TagDynamicModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isRemoveChecked, setIsRemoveChecked] = useState(false);
  const [resultData, setResultData] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getGlobalSetting();
    }
  }, [props]);

  const getGlobalSetting = () => {
    setLoading(true);
    apiCall('/globalSetting/getOneByValid', 'GET')
      .then((res) => {
        const { customerTagSetting } = res;
        if (customerTagSetting) {
          formRef.current.setFieldsValue({
            ...customerTagSetting,
          });
          setIsChecked(customerTagSetting?.addPreference?.enable);
          setIsRemoveChecked(customerTagSetting?.removePreference?.enable);
        }
        setResultData(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      const { addPreference, removePreference } = formData;
      if (addPreference.enable) {
        if (addPreference.day && !addPreference.count) {
          message.error('请输入添加画像标记数！');
          return false;
        } else if (addPreference.count && !addPreference.day) {
          message.error('请输入添加画像标记天数！');
          return false;
        } else if (
          !addPreference.count &&
          !addPreference.day &&
          !addPreference.totalCount
        ) {
          message.error('请输入添加画像累计标记次数！');
          return false;
        }
      }

      if (removePreference.enable) {
        if (removePreference.day && !removePreference.count) {
          message.error('请输入移除画像天数！');
          return false;
        } else if (removePreference.count && !removePreference.day) {
          message.error('请输入移除画像标记次数！');
          return false;
        } else if (
          !removePreference.count &&
          !removePreference.day &&
          !removePreference.totalCount
        ) {
          message.error('请输入移除画像累计标记次数！');
          return false;
        }
      }

      setConfirmLoading(true);
      const data = {
        ...resultData,
        customerTagSetting: {
          ...formData,
        },
      };
      apiCall('/globalSetting/addOrModify', 'POST', data)
        .then((res) => {
          message.success('修改成功！');
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setIsChecked(false);
    setIsRemoveChecked(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="TagDynamicModal"
      visible={visible}
      title="客户标签画像"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      width={600}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            name={['addPreference', 'enable']}
            valuePropName="checked"
            label="添加画像"
            extra="设置客户标签画像添加规则，以下规则满足其中一个即可"
            initialValue={false}
          >
            <Switch
              onChange={(checked) => {
                setIsChecked(checked);
              }}
            />
          </FormItem>
          {isChecked && (
            <>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '24px',
                }}
              >
                <span>同个客户标签</span>
                <FormItem
                  // rules={[{ required: addDayFlag, message: "请输入天数" }]}
                  name={['addPreference', 'day']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                  // onChange={(value) => {
                  //   const fieldValue =
                  //     formRef.current.getFieldValue("addPreference");
                  //   if (value && !fieldValue.count) {
                  //     setAddCountFlag(true);
                  //     setAddTotalCountFlag(true);
                  //   } else if (value && fieldValue.count) {
                  //     setAddTotalCountFlag(false);
                  //   } else if (
                  //     !value &&
                  //     !fieldValue.count &&
                  //     fieldValue.totalCount
                  //   ) {
                  //     console.log("hhhhh");
                  //     setAddDayFlag(false);
                  //     setAddCountFlag(false);
                  //   }
                  // }}
                  />
                </FormItem>
                <span>天内被标记</span>
                <FormItem
                  // rules={[{ required: addCountFlag, message: "请输入次数" }]}
                  name={['addPreference', 'count']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                  // onChange={(value) => {
                  //   const fieldValue =
                  //     formRef.current.getFieldValue("addPreference");
                  //   if (value && !fieldValue.day) {
                  //     setAddDayFlag(true);
                  //     setAddTotalCountFlag(true);
                  //   } else if (value && fieldValue.day) {
                  //     setAddTotalCountFlag(false);
                  //   } else if (
                  //     !value &&
                  //     !fieldValue.day &&
                  //     fieldValue.totalCount
                  //   ) {
                  //     setAddDayFlag(false);
                  //     setAddCountFlag(false);
                  //   }
                  // }}
                  />
                </FormItem>
                <span>次</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '24px',
                }}
              >
                <span>同个客户标签累计被标记</span>
                <FormItem
                  // rules={[
                  //   { required: addTotalCountFlag, message: "请输入次数" },
                  // ]}
                  name={['addPreference', 'totalCount']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                  // onChange={(value) => {
                  //   const fieldValue =
                  //     formRef.current.getFieldValue("addPreference");

                  //   if (value && (fieldValue.day || fieldValue.count)) {
                  //     setAddDayFlag(true);
                  //     setAddCountFlag(false);
                  //   } else if (!value && fieldValue.day && fieldValue.count) {
                  //     setAddTotalCountFlag(false);
                  //     setAddDayFlag(true);
                  //     setAddCountFlag(true);
                  //   }
                  // }}
                  />
                </FormItem>
                <span>次</span>
              </div>
            </>
          )}

          <FormItem
            name={['removePreference', 'enable']}
            valuePropName="checked"
            label="移除画像"
            extra="设置客户标签画像移除规则，以下规则满足其中一个即可"
            initialValue={false}
          >
            <Switch
              onChange={(checked) => {
                setIsRemoveChecked(checked);
              }}
            />
          </FormItem>
          {isRemoveChecked && (
            <>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '24px',
                }}
              >
                <span>同个客户标签标记次数少于</span>
                <FormItem
                  name={['removePreference', 'count']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber min={1} max={1000} />
                </FormItem>
                <span>次，且超过 </span>
                <FormItem
                  name={['removePreference', 'day']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber min={1} max={1000} />
                </FormItem>
                <span>天未新增标记</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '24px',
                }}
              >
                <span>同个客户标签累计</span>
                <FormItem
                  name={['removePreference', 'totalDay']}
                  style={{ margin: '0 5px', display: 'inline-block' }}
                >
                  <InputNumber min={1} max={1000} />
                </FormItem>
                <span>天未新增标记</span>
              </div>
            </>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default TagDynamicModal;
