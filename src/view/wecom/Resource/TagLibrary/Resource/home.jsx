/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2022/03/31 11:01
 * @LastEditTime: 2023/11/21 16:29
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/TagLibrary/Resource/home.jsx
 * @Description: '资源标签'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { PlusOutlined } from '@ant-design/icons';
import {
  Form,
  Input,
  Button,
  Table,
  Card,
  Tooltip,
  Tag,
  Popover,
  message,
} from 'antd';
import FormModal from '../comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';
import ResourceModal from './comps/ResourceModal';
import ListOperation from 'components/ListOperation/home';
import FormModalResourceTag from "@wecom/Resource/TagLibrary/comps/FormModalResourceTag"

const FormItem = Form.Item;

const ResourceTag = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [inputValue, setInputValue] = useState('');
  const [formParams, setFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [maxLength, setMaxLength] = useState(15);
  const [resourceParams, setResourceParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '标签组',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '标签',
      width: '250px',
      dataIndex: 'tags',
      key: 'tags',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => {
        const tagList = record.tagList;
        const content = (
          <>
            {tagList?.map((item, idx) => (
              <Tag key={idx} style={{ whiteSpace: 'pre-line' }}>
                {item.name}
              </Tag>
            )) || ''}
          </>
        );
        return (
          <div style={{ textAlign: 'left' }}>
            {tagList?.length > 0 ? (
              <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {
                  <Popover content={content} placement="topLeft">
                    {tagList.map((atem, andex) => (
                      <Tag
                        key={andex}
                        // closable
                        // onClose={(e) => {
                        //   handleCloseTag(e, record, value, atem);
                        // }}
                      >
                        {atem.name}
                      </Tag>
                    ))}
                  </Popover>
                }
              </div>
            ) : (
              ''
            )}
            {tagList?.length > 0 && <br />}
            {/* {record.isAddWords ? ( */}
            {/*   <> */}
            {/*     <Input */}
            {/*       style={{ width: '160px' }} */}
            {/*       placeholder="请输入标签" */}
            {/*       maxLength={maxLength} */}
            {/*       value={inputValue} */}
            {/*       onBlur={(e) => { */}
            {/*         onInputBlur(e, index); */}
            {/*       }} */}
            {/*       onChange={(e) => { */}
            {/*         handleInputValue(e); */}
            {/*       }} */}
            {/*       allowClear */}
            {/*     /> */}
            {/*     <Button */}
            {/*       type="primary" */}
            {/*       onClick={() => handleQueryFilter(record, index)} */}
            {/*       size="small" */}
            {/*       style={{ width: 50, marginLeft: '5px' }} */}
            {/*     > */}
            {/*       确定 */}
            {/*     </Button> */}
            {/*   </> */}
            {/* ) : ( */}
            {/*   <Button */}
            {/*     type="primary" */}
            {/*     icon={<PlusOutlined />} */}
            {/*     size="small" */}
            {/*     onClick={() => { */}
            {/*       handleAddTag(index); */}
            {/*     }} */}
            {/*   > */}
            {/*     新增 */}
            {/*   </Button> */}
            {/* )} */}
          </div>
        );
      },
    },
    {
      title: '标签数量',
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      ellipsis: 'true',
      align: 'center',
      sorter: (a, b) => a.count - b.count,
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record, index), name: "编辑" },
          { onClick: () => handleDelete(record, index), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: 'resource',
        ...formData,
      };
      apiCall('/info/tag/tagGroup/page', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleTagAddOrmodify = (params) => {
    setLoading(true);
    apiCall('/info/tag/tag', 'POST', params)
      .then((res) => {
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleInputValue = (e) => {
    const value = e.target.value;
    let triggerValue = '';
    if (/^[\u4e00-\u9fa5]+$/i.test(value)) {
      triggerValue = value.slice(0, 15);
      setMaxLength(15);
    } else {
      triggerValue = value.slice(0, 30);
      setMaxLength(30);
    }
    setInputValue(triggerValue);
  };

  const handleQueryFilter = (record, index) => {
    const { id, tagList } = record;
    if (inputValue) {
      const tags = tagList.map((item) => item.name);
      if (
        tags.some((item) => item == inputValue) &&
        !new RegExp(/^[ ]+$/).test(inputValue)
      ) {
        message.error('标签已存在！');
        return;
      }
      handleTagAddOrmodify({
        name: inputValue,
        tagGroupId: id,
        type: 'resource',
      });
      setInputValue('');
    } else {
      message.error('请输入标签！');
    }
  };

  const onInputBlur = (e, index) => {
    if (!inputValue) {
      const newDataSource = dataSource;
      newDataSource[index].isAddWords = false;
      setDataSource([...newDataSource]);
    }
  };

  const handleAddTag = (index) => {
    const newDataSource = dataSource;
    if (newDataSource.some((item) => item.isAddWords)) {
      message.warning("已存在新增项，请保存后再新增！");
      return false;
    }
    newDataSource[index].isAddWords = true;
    setDataSource([...newDataSource]);
  };

  const handleCloseTag = (e, record, value, atem) => {
    e.preventDefault();
    apiCall(`/info/tag/tag/${atem.id}`, 'DELETE')
      .then((res) => {
        message.success('删除成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      type: 'resource',
      onOk: () => {
        setFormParams({ visible: false });
        message.success('新增成功！');
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 客户标签动态画像
  const handleResource = () => {
    setResourceParams({
      visible: true,
      onOk: () => {
        setResourceParams({ visible: false });
      },
      onCancel: () => {
        setResourceParams({ visible: false });
      },
    });
  };

  const handleEdit = (record, index) => {
    setFormParams({
      visible: true,
      tagData: record,
      type: 'resource',
      onOk: () => {
        setFormParams({ visible: false });
        message.success('修改成功！');
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
        fetchList();
      },
    });
  };

  const handleDelete = (record, index) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除标签组【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/info/tag/tagGroup/${id}`, 'DELETE')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="ResourceTag">
      <FilterBar bodyStyle={{ padding: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="tagGroupName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="标签组" allowClear />
          </FormItem>
          <FormItem
            name="tagName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="标签" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增标签组
            </Button>
            {/*<Button type="primary" onClick={() => handleResource()}>*/}
            {/*  客户资源偏好动态策略*/}
            {/*</Button>*/}
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModalResourceTag params={formParams} />
      <OperateModal params={operateParams} />
      <ResourceModal params={resourceParams} />
    </div>
  );
};

export default ResourceTag;
