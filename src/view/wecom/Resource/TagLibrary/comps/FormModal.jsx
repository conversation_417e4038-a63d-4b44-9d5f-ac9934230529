/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/31 14:23
 * @LastEditTime: 2023/12/18 10:07
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\TagLibrary\comps\FormModal.jsx
 * @Description: '标签-表单(新增/编辑)'
 */

import React, { useState, useEffect, memo } from 'react';
import { Form, Modal, Input, message, Button, Switch, Popconfirm } from "antd"
import {
  PlusOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { apiCall } from 'common/utils';
import OperateModal from 'components/Modal/OperateModal/index';
import './FormModal.less';

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const FormModal = (props) => {
  const { visible, tagData, onOk, onCancel, type } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [formRef] = Form.useForm();
  const [tagId, setTagId] = useState(null);
  const [tagsList, setTagsList] = useState([]);
  const [maxLength, setMaxLength] = useState(15);
  const [operateParams, setOperateParams] = useState({ visible: false });

  useEffect(() => {
    if (visible) {
      if (tagData) {
        const { id, name, modifyAble, tagList } = tagData;
        formRef.setFieldsValue({ name, modifyAble });
        setTagsList(tagList ?? []);
        setTagId(id);
      }
    }
  }, [visible]);

  const onSureSensit = () => {
    let list = JSON.parse(JSON.stringify(tagsList));
    // if (
    //   list.some((item) => !item.name) ||
    //   list.some((item) => new RegExp('^[ ]+$').test(item.name))
    // ) {
    //   message.warning('标签名称不能为空！');
    //   return false;
    // }
    if (type != 'customer' && list.length > 99) {
      message.error('单个标签组最多添加100个标签！');
      return false;
    }
    list.push({ name: '' });
    setTagsList(list);
  };

  const handleTagListClose = (e, value, idx) => {
    e.preventDefault();
    const list = JSON.parse(JSON.stringify(tagsList));
    if (value.id) {
      apiCall(`/info/tag/tag/${value.id}`, 'DELETE')
        .then((res) => {
          list.splice(idx, 1);
          setTagsList(list);
          message.success('删除成功！');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    } else {
      list.splice(idx, 1);
      setTagsList(list);
      message.success('删除成功！');
    }
  };

  const handleOk = () => {
    formRef.validateFields().then((formData) => {
      for (let i = 0; i < tagsList.length; i++) {
        const value = tagsList[i].name;
        if (!value || new RegExp('^[ ]+$').test(value)) {
          message.warning('标签名称不能为空！');
          return false;
        }
      }

      const newLength = new Set(tagsList.map((item) => item.name)).size;
      const length = tagsList.length;
      if (length > newLength) {
        message.warning('标签名称不能重复！');
        return false;
      }

      setConfirmLoading(true);
      const { name, modifyAble } = formData;
      const data = {
        id: tagId ?? null,
        name,
        modifyAble,
        tagList: tagsList.map((item) => ({
          id: item.id ?? null,
          name: item.name,
        })),
        type: type,
      };
      apiCall('/info/tag/tagGroup', 'POST', data)
        .then((res) => {
          onReset();
          // message.success("修改成功！");
          onOk?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const handleDelet = () => {
    const { name, id } = tagData;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除标签组【${name}】，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/info/tag/tagGroup/${id}`, 'DELETE')
          .then((res) => {
            message.success('删除成功！');
            onReset();
            onOk?.();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            });
          });
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        });
      },
    });
  };

  const handleCancel = () => {
    onReset();
    onCancel?.();
  };

  const onReset = () => {
    setConfirmLoading(false);
    setTagId(null);
    setTagsList([]);
    formRef.resetFields();
  };

  return (
    <Modal
      className="TagLibraryFormModal"
      title={tagData ? '编辑标签组' : '新增标签组'}
      visible={visible}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      footer={
        <div
          style={{
            display: 'flex',
            justifyContent: tagId ? 'space-between' : 'flex-end',
            alignItems: 'center',
          }}
        >
          {tagId && (
            <a onClick={handleDelet} style={{ color: '#d9001b' }}>
              删除标签组
            </a>
          )}
          <div>
            <Button type="primary" onClick={handleCancel}>
              取消
            </Button>
            {
              tagData ? <Popconfirm
                title="编辑标签组不会影响已使用的标签"
                onConfirm={handleOk}
                okText="确定"
                cancelText="取消"
              >
                <Button type="primary" loading={confirmLoading}>
                  确认
                </Button>
              </Popconfirm> :
              <Button type="primary" onClick={handleOk} loading={confirmLoading}>
                确认
              </Button>
            }
          </div>
        </div>
      }
    >
      <Form {...layout} form={formRef}>
        <FormItem
          name="name"
          label="标签组名称"
          rules={[{ required: true, message: '请输入标签组名称（15字内）' }]}
        >
          <Input
            placeholder="请输入标签组名称（15字内）"
            maxLength={15}
            allowClear
          />
        </FormItem>
        {type == 'customer' && (
          <FormItem
            name="modifyAble"
            label="允许员工调整"
            valuePropName="checked"
            initialValue
            extra="允许员工调整时，员工可在移动端对客户取消标签"
          >
            <Switch />
          </FormItem>
        )}
        <FormItem label="标签名称">
          <div>
            {tagsList.map((item, index) => (
              <div key={index} style={{ margin: '10px 0' }}>
                <Input
                  placeholder="请输入标签名称（中文最多15字）"
                  maxLength={maxLength}
                  allowClear
                  style={{ width: '300px' }}
                  value={item.name}
                  onChange={(e) => {
                    const value = e.target.value;
                    let list = JSON.parse(JSON.stringify(tagsList));
                    let triggerValue = '';
                    if (/^[\u4e00-\u9fa5]+$/i.test(value)) {
                      triggerValue = value.slice(0, 15);
                      setMaxLength(15);
                    } else {
                      triggerValue = value.slice(0, 30);
                      setMaxLength(30);
                    }
                    list[index].name = triggerValue;
                    setTagsList(list);
                  }}
                />
                <Popconfirm
                  title="删除后不会影响已使用的标签"
                  onConfirm={(e) => handleTagListClose(e,item, index)}
                  okText="确定"
                  cancelText="取消"
                >
                  <DeleteOutlined className="tag-delete-icon" />
                </Popconfirm>

              </div>
            ))}

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onSureSensit}
            >
              添加
            </Button>
            {type != 'customer' && (
              <span style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.45)' }}>
                <InfoCircleOutlined style={{ margin: '0 6px' }} />
                单个标签组最多添加100个标签
              </span>
            )}
          </div>
        </FormItem>
      </Form>
      <OperateModal params={operateParams} />
    </Modal>
  );
};

export default memo(FormModal);
