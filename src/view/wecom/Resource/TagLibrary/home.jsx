/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022/09/05 10:00
 * @LastEditTime: 2023/03/14 15:01
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\TagLibrary\home.jsx
 * @Description: '标签库'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import Customer from './Customer/home';
import Resource from './Resource/home';
import Collection from './Collection/home';

const { TabPane } = Tabs;

const TagLibrary = (props) => {
  const [tabsIndex, setTabsIndex] = useState('');

  useEffect(() => {
    setTabsIndex(localStorage.getItem('TabsTagLibraryActiveKey') || '1');
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('TabsTagLibraryActiveKey', index);
    setTabsIndex(index);
  };

  return (
    <div className='TagLibrary'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="客户标签" key="1">
            <Customer />
          </TabPane>
          <TabPane tab="资源标签" key="2">
            <Resource />
          </TabPane>
          <TabPane tab="集锦标签" key="3">
            <Collection />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TagLibrary;
