/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/10 10:55
 * @LastEditTime: 2024/01/09 18:03
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\WecomStoreManagement\Decorate\home.jsx
 * @Description: '微店装修'
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { withRouter } from 'react-router-dom';
import { Card, Input, Table, Tooltip, message, Switch, } from 'antd';
import { apiCall } from 'common/utils';
import { MenuOutlined } from '@ant-design/icons';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import SwiperTableModal from '../comps/SwiperTableModal';
import WarningModal from '../comps/WarningModal';
import ListOperation from 'components/ListOperation/home';
import './home.less';

const type = 'DraggableBodyRow';
const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const Decorate = (props) => {
  const [loading, setLoading] = useState(false);
  const [swiperTableParams, setSwiperTableParams] = useState({ visible: false });
  const [warningParams, setWarningParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '拖动排序',
      width: '80px',
      dataIndex: 'sort',
      align: 'center',
      render: (value, record, index) => <MenuOutlined />,
    },
    {
      title: '模块',
      width: '180px',
      dataIndex: 'name',
      key: 'name',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <div className='titleEdit'>
          {
            record.isEdit
              ? <Input
                value={value}
                onChange={(e) => {
                  let newDataSource = JSON.parse(JSON.stringify(dataSource));
                  newDataSource[index].name = e.target.value.replace(/^\s+|\s+$/g, '');
                  setDataSource([...newDataSource]);
                }}
                placeholder="请输入"
                allowClear
              />
              : <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          }
          {record.isEdit
            ? <a onClick={() => handleKeep(record, index)}>保存</a>
            : <a onClick={() => handleEdit(record, index)}>编辑</a>
          }
        </div>
      ),
    },
    {
      title: '显示状态',
      width: '100px',
      dataIndex: 'showState',
      key: 'showState',
      align: 'center',
      render: (value, record, index) => record.type != 6 ? <Switch checkedChildren="显示" unCheckedChildren="隐藏" checked={value} onChange={(checked) => onChangeShowState(checked, record, index)} /> : '',
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      render: (value, record, index) => {
        let opts = [];
        if (record.type == 1) {
          opts.push({ onClick: () => handleSetSwiper(record, index), name: '设置轮播图' });
        }
        if (record.type == 6) {
          opts.push({ onClick: () => handleSetWarning(record, index), name: '设置提示语' });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];
  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      apiCall('/info/informationModuleManage/modify', 'PUT', data).then((res) => {
        message.success('修改成功！');
        setDataSource(
          update(dataSource, {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, dragRow],
            ],
          }),
        );
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource],
  );

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { query } = params;
    const data = {
      ...query,
    };
    apiCall('/info/informationModuleManage/list', 'GET', data).then((res) => {
      setDataSource(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { name } = record;
    if (name == '') {
      message.warning('模块名称不能为空！');
      return false;
    }
    setLoading(true);
    let newDataSource = dataSource;
    const data = newDataSource;
    apiCall('/info/informationModuleManage/modify', 'PUT', data).then((res) => {
      message.success('保存成功！');
      newDataSource[index].isEdit = false;
      setDataSource([...newDataSource]);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeShowState = (checked, record, index) => {
    setLoading(true);
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    newDataSource[index].showState = checked ? 1 : 0;
    const data = newDataSource;
    apiCall('/info/informationModuleManage/modify', 'PUT', data).then((res) => {
      setDataSource([...newDataSource]);
      message.success('修改成功！');
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleSetSwiper = (record, index) => {
    setSwiperTableParams({
      visible: true,
      moduleId: record.id,
      onCancel: () => {
        setSwiperTableParams({ visible: false });
      }
    });
  };

  const handleSetWarning = (record, index) => {
    setWarningParams({
      visible: true,
      onSure: (params) => {
        apiCall('/appemployee/upRiskWarning', 'POST', params).then((res) => {
          message.success('修改成功！');
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
          });
      },
      onCancel: () => {
        setWarningParams({ visible: false });
      }
    });
  };

  return (
    <div className='Decorate'>
      <Card bordered={false} >
        <div className="sample">
          样例展示
          <img src="images/zhanshi.jpg" alt="" />
        </div>
        <DndProvider backend={HTML5Backend}>
          <Table
            loading={loading}
            rowKey="id"
            columns={columns}
            dataSource={dataSource}
            components={components}
            pagination={false}
            scroll={{ x: 400 }}
            onRow={(record, index) => ({
              index,
              moveRow,
            })}
          />
        </DndProvider>
      </Card>
      <SwiperTableModal params={swiperTableParams} />
      <WarningModal params={warningParams} />
    </div>
  );
};

export default withRouter(Decorate);
