.Decorate {
  .sample {
    text-align: center;
    width: 375px;
    line-height: 30px;
    font-size: 16px;
    font-weight: 600;

    img {
      width: 100%;
    }
  }

  .ant-card {

    .ant-card-body {
      padding: 0;
      display: flex;

      .ant-table-wrapper {
        width: 560px;
        margin-left: 40px;

        .titleEdit {
          display: flex;
          justify-content: center;
          align-items: center;

          a {
            width: 60px;
            display: none;
          }

          &:hover {
            a {
              display: block;
            }
          }
        }
      }
    }
  }
}