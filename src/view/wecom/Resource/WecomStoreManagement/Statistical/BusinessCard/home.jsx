/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/10 11:10
 * @LastEditTime: 2024/10/22 09:35
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/WecomStoreManagement/Statistical/BusinessCard/home.jsx
 * @Description: '智慧名片'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import FilterBar from "components/FilterBar/FilterBar";
import {
  Button,
  Form,
  DatePicker,
  Row,
  Col,
  Spin,
  Tooltip,
  Card,
  Table,
  Tabs,
  Avatar,
  TreeSelect,
  Empty,
} from "antd";
import moment from "moment";
import { timeStamp, getDay, disabledAfterDate } from "common/date";
import { apiCall } from "common/utils";
import { Line } from '@ant-design/plots';
import WibotStatistic from 'components/WibotStatistic/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const BusinessCard = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [statisticData, setStatisticData] = useState([
    {
      title: '员工分享人数',
      value: 0,
      suffix: '人'
    },
    {
      title: '员工分享次数',
      value: 0,
      suffix: '次'
    },
    {
      title: '客户访问人数',
      value: 0,
      suffix: '人'
    },
    {
      title: ' 客户访问次数',
      value: 0,
      suffix: '次'
    }
  ]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [staffData, setStaffData] = useState(null);
  const [customerData, setCustomerData] = useState(null);
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState("1");
  const [depOptions, setDepOptions] = useState([]);
  const staffColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工名称",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.employeeAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "员工分享次数",
      width: "160px",
      dataIndex: "employeeShareCount",
      key: "employeeShareCount",
      align: "center",
    },
    {
      title: "客户访问人数",
      width: "160px",
      dataIndex: "customerVisitorCount",
      key: "customerVisitorCount",
      align: "center",
    },
    {
      title: "客户访问次数",
      width: "160px",
      dataIndex: "customerVisitCount",
      key: "customerVisitCount",
      align: "center",
    },
  ];
  const clientColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "180px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "访问员工",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "lastVisitTime",
      key: "lastVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastVisitTime) - timeStamp(b.lastVisitTime),
    },
    {
      title: "客户访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const dateColumns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "日期",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    // {
    //   title: '名片使用员工数',
    //   width: '160px',
    //   dataIndex: 'visitCount',
    //   key: 'visitCount',
    //   align: 'center',
    // },
    {
      title: "员工分享人数",
      width: "160px",
      dataIndex: "employeeSharerCount",
      key: "employeeSharerCount",
      align: "center",
    },
    {
      title: "员工分享次数",
      width: "160px",
      dataIndex: "employeeShareCount",
      key: "employeeShareCount",
      align: "center",
    },
    {
      title: "客户访问次数",
      width: "160px",
      dataIndex: "customerVisitCount",
      key: "customerVisitCount",
      align: "center",
    },
    {
      title: "客户访问人数",
      width: "160px",
      dataIndex: "customerVisitorCount",
      key: "customerVisitorCount",
      align: "center",
    },
  ];

  useEffect(() => {
    getDeptOptions();
    getTabsDataDetails({ tabIdx: "1" });
  }, []);

  const getDeptOptions = async () => {
    const data = {};
    await apiCall("/dept/option", "GET", data)
      .then(async (res) => {
        setDepOptions(res);
        await formRef?.current?.setFieldsValue({
          departmentIds: res[0].id,
          time: [moment(getDay(-30)), moment(getDay(-1))],
        });
        await fetchList();
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const { query } = params;
      const data = {
        type: 15,
        ...query,
        ...formData,
      };

      apiCall("/appemployee/storeStatistic", "GET", data)
        .then((res) => {
          const {
            customerVisitCount,
            customerVisitorCount,
            employeeShareCount,
            employeeSharerCount,
            employeeShareList,
            customerVisitorList,
          } = res;
          // 统计数据
          let newStatisticData = statisticData;
          newStatisticData[0].value = employeeSharerCount;
          newStatisticData[1].value = employeeShareCount;
          newStatisticData[2].value = customerVisitorCount;
          newStatisticData[3].value = customerVisitCount;
          setStatisticData(newStatisticData);
          // 【员工分享】
          setStaffData([
            ...employeeShareList.map((item) => ({
              year: item.localDate,
              value: item.number,
              title: '分享人数'
            })),
            ...employeeShareList.map((item) => ({
              year: item.localDate,
              value: item.count,
              title: '分享次数'
            })),
            ...employeeShareList.map((item) => ({
              year: item.localDate,
              value: item.number,
              title: '分享人数趋势'
            })),
            ...employeeShareList.map((item) => ({
              year: item.localDate,
              value: item.count,
              title: '分享次数趋势'
            })),
          ]);
          // 【客户访问】
          setCustomerData([
            ...customerVisitorList.map((item) => ({
              year: item.localDate,
              value: item.number,
              title: '访问人数'
            })),
            ...customerVisitorList.map((item) => ({
              year: item.localDate,
              value: item.count,
              title: '访问次数'
            })),
            ...customerVisitorList.map((item) => ({
              year: item.localDate,
              value: item.number,
              title: '访问人数趋势'
            })),
            ...customerVisitorList.map((item) => ({
              year: item.localDate,
              value: item.count,
              title: '访问次数趋势'
            })),
          ]);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
    let timer = setTimeout(() => {
      setLoading(false);
      clearTimeout(timer);
    }, 1000);
  };

  const getTabsDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, tabIdx } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      type: 15,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };
    let apiUrl = "";
    switch (tabIdx || tabsDetailsIndex) {
      case "1":
        apiUrl = "/appemployee/storeStatistic/employee";
        break;
      case "2":
        apiUrl = "/appemployee/storeStatistic/customer";
        break;
      case "3":
        apiUrl = "/appemployee/storeStatistic/date";
        break;
      default:
        break;
    }
    apiCall(apiUrl, "GET", data)
      .then((res) => {
        const { records, current, pages, size, total } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleQuery = async () => {
    fetchList();
  };

  const handleChooseTime = async (value) => {
    let time = [];
    switch (value) {
      case "yesterday":
        time = [moment(getDay(-1)), moment(getDay(-1))];
        break;
      case "week":
        time = [moment(getDay(-7)), moment(getDay(-1))];
        break;
      case "month":
        time = [moment(getDay(-30)), moment(getDay(-1))];
        break;
    }
    if (value) {
      await formRef.current.setFieldsValue({ time });
    }
  };

  const onChangeTabsDataDetails = (index) => {
    setDataSource([])
    setTabsDetailsIndex(index);
    getTabsDataDetails({
      tabIdx: index,
      pagination: { current: 1, pageSize: 10 },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    getTabsDataDetails({ pagination });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: "/wecom/customer/details",
      search: `?id=${customerId}`,
    });
  };

  const handleReset = async () => {
    await formRef?.current?.setFieldsValue({
      departmentIds: depOptions[0].id,
      time: [moment(getDay(-30)), moment(getDay(-1))],
    });
    await fetchList();
  };

  const DemoLine = (data) => {
    const config = {
      data: data,
      xField: 'year',
      yField: 'value',
      seriesField: 'title',
      xAxis: {
        type: 'time',
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value;
          }
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <Spin spinning={loading}>
      <div className="BusinessCard-Container">
        <FilterBar bodyStyle={{ padding: 'unset' }}>
          <Form layout={"inline"} ref={formRef}>
            <FormItem name="departmentIds" label="机构">
              <TreeSelect
                style={{ width: 200 }}
                showSearch
                treeNodeFilterProp="name"
                treeData={depOptions}
                fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              />
            </FormItem>
            <FormItem name="time" label="统计时间">
              <RangePicker
                allowClear={false}
                format="YYYY-MM-DD"
                disabledDate={disabledAfterDate}
              />
            </FormItem>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleChooseTime("yesterday")}
            >
              昨日
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleChooseTime("week")}
            >
              近7天
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => handleChooseTime("month")}
            >
              近30天
            </Button>
          </Form>
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => handleQuery()}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置</Button>
        </FilterBar>

        <WibotStatistic
          list={statisticData}
          flex="auto"
        />

        <Row gutter={[16, 0]}>
          <Col xs={24} xl={12}>
            <Card className="ant-chartCard" title="员工分享">
              {staffData && DemoLine(staffData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
          <Col xs={24} xl={12}>
            <Card className="ant-chartCard" title="客户访问">
              {customerData && DemoLine(customerData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col flex="auto">
            <Card title="数据明细">
              <Tabs
                activeKey={tabsDetailsIndex}
                destroyInactiveTabPane
                onChange={onChangeTabsDataDetails}
              >
                <TabPane tab="员工" key="1">
                  <Table
                    rowKey="id"
                    loading={loading}
                    dataSource={dataSource}
                    columns={staffColumns}
                    scroll={{ x: 1300 }}
                    pagination={paginations}
                    onChange={onChangeTable}
                  />
                </TabPane>
                <TabPane tab="客户" key="2">
                  <Table
                    rowKey="id"
                    loading={loading}
                    dataSource={dataSource}
                    columns={clientColumns}
                    scroll={{ x: 1300 }}
                    pagination={paginations}
                    onChange={onChangeTable}
                  />
                </TabPane>
                <TabPane tab="日期" key="3">
                  <Table
                    rowKey="id"
                    loading={loading}
                    dataSource={dataSource}
                    columns={dateColumns}
                    scroll={{ x: 1300 }}
                    pagination={paginations}
                    onChange={onChangeTable}
                  />
                </TabPane>
              </Tabs>
            </Card>
          </Col>

        </Row>
      </div>
    </Spin>
  );
};

export default withRouter(BusinessCard);
