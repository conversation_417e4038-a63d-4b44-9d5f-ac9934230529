/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/10 11:03
 * @LastEditTime: 2023/11/07 10:40
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/WecomStoreManagement/Statistical/home.jsx
 * @Description: '统计数据'
 */

import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
// 模块组件
import BusinessCard from './BusinessCard/home';
import FinancialStore from './FinancialStore/home';
import "./home.less"

const { TabPane } = Tabs;

const Statistical = (props) => {
  const [tabsIndex, setTabsIndex] = useState('1');

  useEffect(() => {
    setTabsIndex('1');
  }, []);

  const onChangeTabs = (index) => {
    setTabsIndex(index);
  };

  return (
    <div className='Statistical-Container'>
      <Tabs type="card" activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
        <TabPane tab="智慧名片" key="1">
          <BusinessCard />
        </TabPane>
        <TabPane tab="金融微店" key="2">
          <FinancialStore />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Statistical;
