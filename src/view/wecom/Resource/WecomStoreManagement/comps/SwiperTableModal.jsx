/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/08/27 14:02
 * @LastEditTime: 2023/11/16 15:02
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/WecomStoreManagement/comps/SwiperTableModal.jsx
 * @Description: '轮播图表格管理对话框'
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Modal,
  Tooltip,
  Button,
  Table,
  Input,
  message,
  Switch,
  Upload,
  Image,
  Typography,
} from 'antd';
import { PlusOutlined, MenuOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import { validURL } from 'common/regular';

import './SwiperTableModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { confirm } = Modal;
const { Paragraph } = Typography;
const type = 'DraggableBodyRow';
const DraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const SwiperTableModal = (props) => {
  const { moduleId } = props.params;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '拖动排序',
      width: '80px',
      dataIndex: 'sort',
      align: 'center',
      render: (value, record, index) => <MenuOutlined />,
    },
    {
      title: '图片',
      width: '160px',
      dataIndex: 'coverFileId',
      key: 'coverFileId',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        let content = null;
        if (value) {
          content = (
            <div className="table-image-box">
              <FileHOC src={value}>
                {(url) => (
                  <Image width={60} src={url} preview={{ src: url }}></Image>
                )}
              </FileHOC>
              <a onClick={() => handleReplaceImage(record, index)}>删 除</a>
            </div>
          );
        } else {
          content = (
            <Upload
              name="file"
              customRequest={(config) => customRequest(config, record, index)}
              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={(info) => onChangeUpload(info, index)}
            >
              <Button
                type="primary"
                icon={<PlusOutlined />}
                loading={record.uploadLoading}
              >
                上 传
              </Button>
            </Upload>
          );
        }
        return content;
      },
    },
    {
      title: '跳转链接',
      width: '180px',
      dataIndex: 'mipLink',
      key: 'mipLink',
      align: 'center',
      render: (value, record, index) => (
        <div className="titleEdit">
          {record.isEdit ? (
            <Input
              value={value}
              onChange={(e) => {
                let newDataSource = dataSource;
                newDataSource[index].mipLink = e.target.value.replace(
                  /^\s+|\s+$/g,
                  ''
                );
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入"
              allowClear
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>
              <Paragraph
                ellipsis={{ rows: 1 }}
                style={{ whiteSpace: 'pre', width: '165px' }}
              >
                {value}
              </Paragraph>
            </Tooltip>
          )}
          {record.isEdit ? (
            <a onClick={() => handleKeep(record, index)}>保存</a>
          ) : (
            <a onClick={() => handleEdit(record, index)}>编辑</a>
          )}
        </div>
      ),
    },
    {
      title: '显示状态',
      width: '100px',
      dataIndex: 'showState',
      key: 'showState',
      align: 'center',
      render: (value, record, index) => (
        <Switch
          checkedChildren="显示"
          unCheckedChildren="隐藏"
          checked={value}
          onChange={(checked) => onChangeShowState(checked, record, index)}
        />
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record, index)}>删除</a>
        </>
      ),
    },
  ];
  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      apiCall('/info/moduleManageBanner/modify', 'PUT', data)
        .then((res) => {
          message.success('修改成功！');
          setDataSource(
            update(dataSource, {
              $splice: [
                [dragIndex, 1],
                [hoverIndex, 0, dragRow],
              ],
            })
          );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource]
  );

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      setLoading(true);
      fetchList();
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { query } = params;
    const data = {
      ...query,
      moduleManageId: moduleId,
    };
    apiCall('/info/moduleManageBanner/list', 'GET', data)
      .then((res) => {
        setDataSource(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = () => {
    if (dataSource.some((item) => !item.coverFileId)) {
      message.warning('已存在新增图片，请上传后再新增！');
      return false;
    }
    setLoading(true);
    const data = {
      sort:
        dataSource.length > 0 ? dataSource[dataSource.length - 1].sort + 1 : 0,
      moduleManageId: 2,
    };
    apiCall('/info/moduleManageBanner/add', 'POST', data)
      .then((res) => {
        message.success('新增成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { mipLink } = record;
    if (mipLink) {
      if (!validURL(mipLink)) {
        message.warning('跳转链接不合法！');
        return;
      }
    }
    setLoading(true);
    let newDataSource = dataSource;
    const data = newDataSource;
    apiCall('/info/moduleManageBanner/modify', 'PUT', data)
      .then((res) => {
        message.success('保存成功！');
        newDataSource[index].isEdit = false;
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeShowState = (checked, record, index) => {
    setLoading(true);
    let newDataSource = JSON.parse(JSON.stringify(dataSource));
    newDataSource[index].showState = checked ? 1 : 0;
    const data = newDataSource;
    apiCall('/info/moduleManageBanner/modify', 'PUT', data)
      .then((res) => {
        message.success('修改成功！');
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record, index) => {
    const { id } = record;
    setLoading(true);
    const data = {
      id: id,
    };
    apiCall('/info/moduleManageBanner/del', 'DELETE', data)
      .then((res) => {
        message.success('删除成功！');
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // setLoading(false);
      });
  };

  const onChangeUpload = (info, index) => {
    if (info.file.status === 'uploading') {
      let newDataSource = dataSource;
      newDataSource[index].uploadLoading = true;
      setDataSource([...newDataSource]);
      return;
    }
  };

  const customRequest = (config, record, index) => {
    const File = config.file;
    const options = {
      quality: 0.9,
    };
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append('file', base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data)
        .then((res) => {
          const { fileId } = res;
          let newDataSource = dataSource;
          newDataSource[index].coverFileId = fileId;
          const data = newDataSource;
          apiCall('/info/moduleManageBanner/modify', 'PUT', data)
            .then((res) => {
              message.success('上传成功！');
              fetchList();
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              // let newDataSource = dataSource;
              // newDataSource[index].uploadLoading = false;
              // setDataSource([...newDataSource]);
            });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => { });
    });
  };

  const handleReplaceImage = (record, index) => {
    confirm({
      title: '您将删除图片，确认继续吗？',
      onOk () {
        let newDataSource = dataSource;
        newDataSource[index].coverFileId = '';
        setDataSource([...newDataSource]);
      },
      onCancel () {
        console.log('Cancel');
      },
    });
  };

  const onOk = () => {
    const isEmpty = dataSource.some((item) => item.mipLink == '');
    const isKeep = dataSource.some((item) => item.isEdit);
    if (isEmpty) {
      message.warning('跳转链接不能为空！');
      return false;
    } else if (isKeep) {
      message.warning('跳转链接未保存！');
      return false;
    }
    onCancel();
  };

  const onCancel = () => {
    const isEmpty = dataSource.some((item) => !item.coverFileId);
    if (isEmpty) {
      message.warning('轮播图不能为空！');
      return false;
    }
    setVisible(false);
    setLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="SwiperTableModal"
      visible={visible}
      width={800}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>
            设置轮播图
            <span style={{ color: '#a9a9a9', fontSize: '12px' }}>
              （建议尺寸688*128px，大小2M内，最多5张）
            </span>
          </div>
          {dataSource.length < 5 ? (
            <Button
              style={{ marginRight: '20px' }}
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleAdd()}
            >
              新增
            </Button>
          ) : (
            ''
          )}
        </div>
      }
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      footer={false}
    >
      <DndProvider backend={HTML5Backend}>
        <Table
          loading={loading}
          rowKey="id"
          columns={columns}
          dataSource={dataSource}
          components={components}
          pagination={false}
          onRow={(record, index) => ({
            index,
            moveRow,
          })}
        />
      </DndProvider>
    </Modal>
  );
};

export default SwiperTableModal;
