/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/14 15:48
 * @LastEditTime: 2022/10/18 11:31
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\Resource\WecomStoreManagement\comps\WarningModal.jsx
 * @Description: '风险提示'
 */
import React, { useEffect, useRef } from 'react';
import { Modal, Form, Input } from 'antd';
import { apiCall } from 'common/utils';
import './WarningModal.less';

const FormItem = Form.Item;
const { TextArea } = Input;

const WarningModal = (props) => {
  const { visible } = props.params;
  const formRef = useRef(null);

  useEffect(() => {
    if (visible) {
      getRiskWarning();
    }
  }, [props]);

  const getRiskWarning = () => {
    apiCall('/appemployee/getRiskWarning', 'GET').then((res) => {
      formRef.current.setFieldsValue({
        content: res
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {

      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      props.params?.onSure?.({ content: formData.content });
      onCancel();
    });
  };

  const onCancel = () => {
    props.params?.onCancel?.();
    formRef.current.resetFields();
  };

  return (
    <Modal
      visible={visible}
      title='风险提示语'
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="WarningModal"
    >
      <Form ref={formRef}>
        <FormItem name="content" className='textArea-mid'>
          <TextArea placeholder="请输入风险提示语" allowClear autoSize={{ minRows: 2, maxRows: 7 }} />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default WarningModal;
