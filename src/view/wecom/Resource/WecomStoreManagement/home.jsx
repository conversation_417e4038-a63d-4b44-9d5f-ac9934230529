/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/06 10:03
 * @LastEditTime: 2023/11/06 16:03
 * @LastEditors: <PERSON>eiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/Resource/WecomStoreManagement/home.jsx
 * @Description: '金融微店'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
// 模块组件
import Statistical from './Statistical/home';
import Decorate from './Decorate/home';
import './home.less';

const { TabPane } = Tabs;

const WecomStoreManagement = (props) => {
  const [tabsIndex, setTabsIndex] = useState('');

  useEffect(() => {
    setTabsIndex(localStorage.getItem('wecomStoreIndex') || '1');
  }, []);

  const onChangeTabs = (index) => {
    localStorage.setItem('wecomStoreIndex', index);
    setTabsIndex(index);
  };

  return (
    <div className='WecomStoreManagement'>
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="数据统计" key="1">
            <Statistical />
          </TabPane>
          <TabPane tab="微店装修" key="2">
            <Decorate />
          </TabPane>
        </Tabs>
      </Card>
      {/* {tabsIndex == '1' && <Statistical />}
      {tabsIndex == '2' && <Decorate />} */}
    </div>
  );
};

export default WecomStoreManagement;
