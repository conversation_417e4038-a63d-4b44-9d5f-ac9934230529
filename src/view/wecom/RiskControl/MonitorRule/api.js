import { apiCall } from "common/utils";

/**
 * 获取风险控制-监控规则列表
 * @param current
 * @param size
 * @param name
 * @param objectType
 * @param enable
 * @param paged
 * @returns {Promise | Promise<unknown>}
 */
export function getRiskControlMonitorRule({
  current,
  size,
  name,
  objectType,
  enable,
  paged,
}) {
  return apiCall("/riskControl/monitorRule", "GET", {
    current,
    size,
    name,
    objectType,
    enable,
    paged,
  });
}

/**
 * 新增风险控制-监控规则
 * @param name
 * @param objectType
 * @param actionType
 * @param enable
 * @param monitorObjectList
 * @returns {Promise | Promise<unknown>}
 */
export function addRiskControlMonitorRule({
  name,
  objectType,
  actionType,
  enable,
  monitorObjectList,
}) {
  return apiCall("/riskControl/monitorRule", "POST", {
    name,
    objectType,
    actionType,
    enable,
    monitorObjectList,
  });
}

/**
 * 更新风险控制-监控规则
 * @param monitorRuleId
 * @param name
 * @param objectType
 * @param actionType
 * @param enable
 * @param monitorObjectList
 * @returns {Promise | Promise<unknown>}
 */
export function updateRiskControlMonitorRule({
  monitorRuleId,
  name,
  objectType,
  actionType,
  enable,
  monitorObjectList,
}) {
  return apiCall(`/riskControl/monitorRule/update/${monitorRuleId}`, "POST", {
    name,
    objectType,
    actionType,
    enable,
    monitorObjectList,
  });
}

/**
 * 查询风险控制-监控规则
 * @param monitorRuleId
 * @returns {Promise | Promise<unknown>}
 */
export function getRiskControlMonitorRuleDetail(monitorRuleId) {
  return apiCall(`/riskControl/monitorRule/${monitorRuleId}`, "GET");
}

/**
 * 删除风险控制-监控规则
 * @param monitorRuleId
 * @returns {Promise | Promise<unknown>}
 */
export function deleteRiskControlMonitorRule(monitorRuleId) {
  return apiCall(`/riskControl/monitorRule/delete/${monitorRuleId}`, "POST");
}

/**
 * 更新风险控制-监控规则-启用状态
 * @param monitorRuleId
 * @param enable
 * @returns {Promise | Promise<unknown>}
 */
export function updateRiskControlMonitorRuleEnable(monitorRuleId, enable) {
  return apiCall(
    `/riskControl/monitorRule/update/enable/${monitorRuleId}`,
    "POST",
    { enable },
  );
}
