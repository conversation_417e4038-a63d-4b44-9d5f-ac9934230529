import {Button, Card, Form, Input, message, Radio, Tooltip, Select} from "antd";
import {BackwardOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import React, {useEffect, useState} from "react";
import {
    addRiskControlMonitorRule,
    getRiskControlMonitorRuleDetail,
    updateRiskControlMonitorRule
} from "@/view/wecom/RiskControl/MonitorRule/api";
import FormItem from "antd/es/form/FormItem";
import {qs2obj} from "common/object";
import {withRouter} from "react-router-dom";
import {clearCache} from "react-router-cache-route";

const objectTypes = [
    {value: "EMPLOYEE", label: "坐席"},
    {value: "CUSTOMER", label: "客户"},
    // {value: 'GROUP', label: '群组'},
    // {value: 'SYSTEM', label: '系统'},
    // {value: 'ROBOT', label: '机器人'},
];

const actionTypes = [
    {
        value: "WARN_AND_BLOCK",
        label: "警告并拦截发送",
        group: "EMPLOYEE",
        tips: "",
    },
    {value: "WARN_EMPLOYEE", label: "仅发警告", group: "EMPLOYEE", tips: ""},
    {
        value: "BLACKLIST",
        label: "拉黑",
        group: "CUSTOMER",
        tips: "审核通过后，系统不再为黑名单客户创建新对话",
    },
    {value: "WARN_CUSTOMER", label: "预警", group: "CUSTOMER", tips: ""},
];
const layout = {
    labelCol: {
        xs: {span: 24},
        sm: {span: 4},
    },
    wrapperCol: {
        xs: {span: 24},
        sm: {span: 8},
    },
};
function MonitorRuleForm(props) {

    const [currentRow, setCurrentRow] = useState(null);
    const [isModalOpen, setModalOpen] = React.useState(false);
    const [modalRef] = Form.useForm(null);
    const [actionsType, setActionsType] = useState([]);

    const objectType = Form.useWatch('objectType', modalRef)
    useEffect(() => {
        console.log('objectType', objectType)
        setActionsType(
            actionTypes.filter(
                (i) => i.group === objectType,
            ),
        );
    }, [objectType]);
    const handleOpenModal = () => {
        setModalOpen(true);
        modalRef.resetFields();
        modalRef.setFieldsValue({
            name: "",
            objectType: objectTypes.find((i) => i.value === "EMPLOYEE").value,
            actionType: actionTypes.find((i) => i.value === "WARN_AND_BLOCK").value,
            word: [],
        });
    };

    const handleCancel = () => {
        setModalOpen(false);
    };
    const handleOk = () => {
        modalRef.validateFields().then((values) => {
            if (currentRow?.id) {
                updateRiskControlMonitorRule({
                    ...values,
                    monitorRuleId: currentRow.id,
                    monitorObjectList: [
                        {
                            type: "SENSITIVE_WORD",
                            sensitiveWordList: values.word,
                        },
                    ],
                })
                    .then(() => {
                        message.success("更新成功");
                    })
                    .finally(() => {
                        setModalOpen(false);
                        fetchList();
                        setCurrentRow(null);
                    });
            } else {
                addRiskControlMonitorRule({
                    ...values,
                    monitorObjectList: [
                        {
                            type: "SENSITIVE_WORD",
                            sensitiveWordList: values.word,
                        },
                    ],
                })
                    .then(() => {
                        message.success("新建成功");
                    })
                    .finally(() => {
                        setModalOpen(false);
                        fetchList();
                    });
            }
        });
    };

    const onChange = (e, value1 = '') => {
        const value = e.target.value;
        const data = actionTypes.filter((i) => i.group === value);
        setActionsType(data);
        modalRef.setFieldValue("actionType", value1 || data[0].value);
    };


    const fetchDetail = (query) => {
        getRiskControlMonitorRuleDetail(query.id)
            .then((data) => {
                modalRef.setFieldsValue({
                    name: data.name,
                    objectType: data.objectType,
                    word: data.monitorObjectList[0]?.sensitiveWordList,
                });
                onChange({target: {value: data.objectType}}, data.actionType)
            })
    }

    const handleGoBack = () => {
        clearCache(); // 清空路由缓存
        props.history.push({
            pathname: "/wecom/monitorRule"
        })
    }
    const handleSubmit = () => {
        modalRef.validateFields().then((values) => {
            console.log(values);
            if (currentRow?.id) {
                updateRiskControlMonitorRule({
                    ...values,
                    monitorRuleId: currentRow.id,
                    monitorObjectList: [
                        {
                            type: "SENSITIVE_WORD",
                            sensitiveWordList: values.word,
                        },
                    ],
                })
                    .then(() => {
                        message.success("更新成功");
                    })
                    .finally(() => {
                        setModalOpen(false);
                        setCurrentRow(null);
                        handleGoBack()
                    });
            } else {
                addRiskControlMonitorRule({
                    ...values,
                    enable: true,
                    monitorObjectList: [
                        {
                            type: "SENSITIVE_WORD",
                            sensitiveWordList: values.word,
                        },
                    ],
                })
                    .then(() => {
                        message.success("新建成功");
                    })
                    .finally(() => {
                        setModalOpen(false);
                        handleGoBack()
                    });
            }
        });
    };

    useEffect(() => {
        const query = qs2obj(props.location.search);
        setCurrentRow(query)
        modalRef.resetFields();
        modalRef.setFieldsValue({
            name: "",
            objectType: objectTypes.find((i) => i.value === "EMPLOYEE").value,
            actionType: actionTypes.find((i) => i.value === "WARN_AND_BLOCK").value,
            word: [],
        });
        if (query.id) {
            fetchDetail(query)
        }
    }, []);
    return <>
        <Card
            extra={
                <Button type="primary" onClick={() => handleGoBack()}>
                    返回
                </Button>
            }
            title={currentRow?.id ? "编辑规则" : "新建规则"}
            bordered={false}
            bodyStyle={{display: 'none'}}
        ></Card>
        <br/>
        <Card
        >
            <Form {...layout} form={modalRef}>
                <FormItem
                    label="规则名称"
                    name="name"
                    rules={[{required: true, message: "请输入规则名称"}]}
                >
                    <Input
                        placeholder="请输入规则名称"
                        showCount
                        maxLength={20}
                        allowClear
                    />
                </FormItem>
                <FormItem
                    label="监控对象"
                    name="objectType"
                    rules={[{required: false, message: "请选择监控对象"}]}
                >
                    <Radio.Group onChange={onChange}>
                        {objectTypes.map((item, index) => {
                            return (
                                <Radio value={item.value} key={index}>
                                    {item.label}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                </FormItem>
                <FormItem
                    label="处理措施"
                    tooltip="当内容符合多个处理措施时，对于坐席，“警告并拦截发送，优先级高于“仅发警告”；对于客户，“拉黑”优先级高于“预警“。"
                    name="actionType"
                    rules={[{required: false, message: "请选择处理措施"}]}
                >
                    <Radio.Group>
                        {actionsType.map((item, index) => {
                            return (
                                <Radio value={item.value} key={index}>
                                    {item.label}
                                    {item.tips && (
                                        <Tooltip title={item.tips}>
                                            <QuestionCircleOutlined/>
                                        </Tooltip>
                                    )}
                                </Radio>
                            );
                        })}
                    </Radio.Group>
                </FormItem>
                <FormItem label="敏感词">
                    <div>
                        <FormItem
                            name="word"
                            rules={[{required: true, message: "请输入敏感词"}]}
                        >
                            <Select
                                mode="tags"
                                tokenSeparators={[',']}
                                style={{ width: '100%' }}
                                placeholder="请输入敏感词"
                            />
                        </FormItem>
                        <div>支持粘贴多个敏感词用“,”分隔开</div>
                    </div>
                </FormItem>


                <FormItem>
                    <div style={{display: 'flex', justifyContent: 'flex-end'}}>
                        <Button type="primary" onClick={handleSubmit}>确定</Button>
                    </div>
                </FormItem>
            </Form>
        </Card>
    </>



}


export default withRouter(MonitorRuleForm);