import {withRouter} from "react-router-dom";
import {Button, Card, Form, Input, message, Modal, Radio, Select, Table, Tooltip,} from "antd";
import ListOperation from "components/ListOperation/home";
import React, {useEffect, useRef, useState} from "react";
import FilterBar from "components/FilterBar/FilterBar";
import {
  addRiskControlMonitorRule,
  deleteRiskControlMonitorRule,
  getRiskControlMonitorRule,
  updateRiskControlMonitorRule,
  updateRiskControlMonitorRuleEnable,
} from "./api";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {WibotModal} from "components/WibotModal";
import history from "common/history";
const FormItem = Form.Item;

const objectTypes = [
  { value: "EMPLOYEE", label: "坐席" },
  { value: "CUSTOMER", label: "客户" },
  // {value: 'GROUP', label: '群组'},
  // {value: 'SYSTEM', label: '系统'},
  // {value: 'ROBOT', label: '机器人'},
];

const actionTypes = [
  {
    value: "WARN_AND_BLOCK",
    label: "警告并拦截发送",
    group: "EMPLOYEE",
    tips: "",
  },
  { value: "WARN_EMPLOYEE", label: "仅发警告", group: "EMPLOYEE", tips: "" },
  {
    value: "BLACKLIST",
    label: "拉黑",
    group: "CUSTOMER",
    tips: "审核通过后，系统不再为黑名单客户创建新对话",
  },
  { value: "WARN_CUSTOMER", label: "预警", group: "CUSTOMER", tips: "" },
];

const MonitorRule = () => {
  const formRef = useRef(null);
  const [loading, setLoading] = React.useState(false);
  const [dataSource, setDataSource] = React.useState([]);
  const [paginations, setPaginations] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      // sorter: (a, b) => a.id - b.id,
    },
    {
      title: "规则名称",
      width: "250px",
      dataIndex: "name",
      key: "name",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        return <div>{record.name}</div>;
      },
    },
    {
      title: "监控对象",
      width: "160px",
      dataIndex: "objectType",
      align: "center",
      render: (value, record, index) =>
        objectTypes.find((i) => i.value === value).label,
    },
    {
      title: "处理措施",
      width: "160px",
      dataIndex: "actionType",
      key: "actionType",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div>{actionTypes.find((i) => i.value === value).label}</div>
      ),
    },
    {
      title: "状态",
      width: "160px",
      dataIndex: "enable",
      key: "enable",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div>{record.enable ? "启用" : "禁用"}</div>
      ),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createEmployeeName",
      key: "createEmployeeName",
      ellipsis: "true",
      align: "center",
      render(v, record) {
        return (
          <>
            <div>{record.createEmployeeName}</div>
            <div>{record.createTime}</div>
          </>
        );
      },
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      // fixed: "right",
      render: (value, record, index) => {
        let opts = [
          // { onClick: () => handleStart(record), name: "启动" },
          // { onClick: () => handleStop(record), name: "停止" },
          // { onClick: () => handleChangeNmber(record), name: "换号" },
          { onClick: () => handleEdit(record), name: "编辑" },
          // {
          //   name: (
          //     <Popconfirm
          //       title="确认删除？"
          //       onConfirm={() => handleRemove(record)}
          //     >
          //       删除
          //     </Popconfirm>
          //   ),
          // },
          {
            onClick: () => handleDisable(record),
            name: record.enable ? "禁用" : "启用",
          },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList(pagination);
  };
  const handleEdit = (record) => {
    setCurrentRow(record);
    history.push({
      pathname: "/wecom/monitorRule/form",
      search: `?id=${record.id}&type=edit`,
    });
  };

  const handleRemove = (record) => {
    deleteRiskControlMonitorRule(record.id).finally(() => {
      fetchList();
    });
  };

  const handleDisable = (record) => {
    WibotModal.open({
      title: (record.enable ? "禁用" : "启用") + "确认",
      children: <div>您将为【{record.name}】进行搡作，确认继续吗？</div>,
      onOk: () => {
        updateRiskControlMonitorRuleEnable(record.id, !record.enable).then(
          (res) => {
            message.success("操作成功");
            fetchList();
          },
        );
      },
      onCancel: () => {
        console.log("Modal Cancel clicked");
      },
    });
  };

  const handleQuery = () => {
    setPaginations({ ...paginations, current: 1 });
    fetchList({ ...paginations, current: 1 });
  };
  const handleReset = () => {
    formRef.current.resetFields();
    setPaginations({ ...paginations, current: 1 });
    fetchList({ ...paginations, current: 1 });
  };
  const fetchList = (pagination = paginations) => {
    setLoading(true);

    formRef.current.validateFields().then((formData) => {
      getRiskControlMonitorRule({
        current: pagination.current,
        size: pagination.pageSize,
        ...formData,
      })
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  useEffect(() => {
    fetchList();
  }, []);
  const [currentRow, setCurrentRow] = useState(null);
  const [isModalOpen, setModalOpen] = React.useState(false);
  const [modalRef] = Form.useForm(null);
  const [actionsType, setActionsType] = useState([]);
  useEffect(() => {
    setActionsType(
      actionTypes.filter(
        (i) => i.group === modalRef.getFieldValue("objectType"),
      ),
    );
  }, [modalRef.getFieldValue("objectType")]);
  const handleOpenModal = () => {
    setModalOpen(true);

    history.push({
      pathname: "/wecom/monitorRule/form"
    });
  };

  const handleCancel = () => {
    setModalOpen(false);
  };
  const handleOk = () => {
    modalRef.validateFields().then((values) => {
      if (currentRow?.id) {
        updateRiskControlMonitorRule({
          ...values,
          monitorRuleId: currentRow.id,
          monitorObjectList: [
            {
              type: "SENSITIVE_WORD",
              sensitiveWordList: values.word.split(","),
            },
          ],
        })
          .then(() => {
            message.success("更新成功");
          })
          .finally(() => {
            setModalOpen(false);
            fetchList();
            setCurrentRow(null);
          });
      } else {
        addRiskControlMonitorRule({
          ...values,
          monitorObjectList: [
            {
              type: "SENSITIVE_WORD",
              sensitiveWordList: values.word.split(","),
            },
          ],
        })
          .then(() => {
            message.success("新建成功");
          })
          .finally(() => {
            setModalOpen(false);
            fetchList();
          });
      }
    });
  };

  const onChange = (e) => {
    const value = e.target.value;
    const data = actionTypes.filter((i) => i.group === value);
    modalRef.setFieldValue("actionType", data[0].value);
    setActionsType(data);
  };
  return (
    <Card bordered={false}>
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name">
            <Input placeholder="规则名称" allowClear />
          </FormItem>
          <FormItem name="objectType">
            <Select placeholder="监控对象" allowClear>
              {objectTypes.map((item, index) => {
                return (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </FormItem>
          <FormItem name="enable">
            <Select placeholder="状态" allowClear>
              <Select.Option value={""}>全部</Select.Option>
              <Select.Option value={"0"}>禁用</Select.Option>
              <Select.Option value={"1"}>启用</Select.Option>
            </Select>
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={handleOpenModal}>
              新建规则
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </Card>
  );
};

export default withRouter(MonitorRule);
