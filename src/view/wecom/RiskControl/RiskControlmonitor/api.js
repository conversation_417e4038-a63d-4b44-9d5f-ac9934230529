import {apiCall} from "common/utils";

/**
 * 获取风险控制-监控规则列表
 * @param current
 * @param size
 * @param name
 * @param objectType
 * @param enable
 * @returns {Promise | Promise<unknown>}
 */
export function getRiskControlMonitorLog({
  actionType,
  agentEmployeeIdStrList,
  auditRemark,
  auditState,
  current,
  customerName,
  deviceEmployeeIdStrList,
  maxTriggerTime,
  minTriggerTime,
  objectType,
  paged,
  ruleName,
  size,
}) {
  return apiCall("/riskControl/riskControlLog", "GET", {
    /**
     * 处理措施
     * WARN_AND_BLOCK :警告并拦截发送 - 对于需要即时阻止并告知用户的行为。
     * WARN_EMPLOYEE :仅发警告（员工） - 仅向员工发送警告，不进行其他操作。
     * BLACKLIST :拉黑 - 将用户或行为加入黑名单。
     * WARN_CUSTOMER :预警（客户） - 向客户发送预警信息，提醒其行为可能有问题。
     */
    actionType,
    /**
     * 参与坐席
     */
    agentEmployeeIdStrList,
    /**
     * 审核意见
     */
    auditRemark,
    /**
     * 审核情况
     * NO_AUDIT_REQUIRED :不需要审核 - 某些操作或数据不需要进行审核。
     * TO_BE_AUDITED :待审核 - 等待审核人员审核的项目。
     * AUDITED :已审核 - 已经通过审核的项目。
     */
    auditState,
    /**
     * 当前页码，默认情况下为第一页，页码从1开始
     */
    current,
    /**
     * 客户昵称
     */
    customerName,
    /**
     * 托管账号
     */
    deviceEmployeeIdStrList,
    maxTriggerTime,
    /**
     * 触发时间
     */
    minTriggerTime,
    /**
     * 监控对象
     * CUSTOMER :CUSTOMER
     * EMPLOYEE :EMPLOYEE
     * GROUP :GROUP
     * SYSTEM :SYSTEM
     * ROBOT :ROBOT
     */
    objectType,
    /**
     * 是否分页
     * 默认为true
     * 是否分页 默认为true
     */
    paged,
    /**
     * 触发规则名称
     */
    ruleName,
    /**
     * 每页的记录数，默认情况下每页十条记录
     */
    size,
  });
}

/**
 * 获取座席下拉
 * @returns {Promise<unknown>}
 */
export function queryAgentOptions() {
  return apiCall("/agentSeat/agentGroup/options", "GET");
}

export function queryDeviceOptions() {
  return apiCall("/device/deviceGroup/listDeviceGroupTree", "GET");
}

export function updateRiskControlMonitorLog({
  riskControlLogId,
  auditState,
  actionType,
  auditResult,
  auditRemark,
}) {
  return apiCall(
    `riskControl/riskControlLog/update/audit/${riskControlLogId}`,
    "POST",
    {
      auditState,
      actionType,
      auditResult,
      auditRemark,
    },
  );
}

export function queryBlackList(data = {}) {
  return apiCall("/riskControl/blacklist", "GET", data);
}

/**
 * 移除黑名单
 * @returns {Promise | Promise<unknown>}
 */
export function removeBlackListById(blacklistId) {
  return apiCall(`/riskControl/blacklist/delete/${blacklistId}`, "POST", {});
}


export function exportTableDataOfRiskControlLog(minTriggerTime, maxTriggerTime) {
  return apiCall('/riskControl/riskControlLog/export', 'GET', {minTriggerTime, maxTriggerTime}, null, {
    isExit: true,
    title: '风控监控数据.xlsx'
  })
}