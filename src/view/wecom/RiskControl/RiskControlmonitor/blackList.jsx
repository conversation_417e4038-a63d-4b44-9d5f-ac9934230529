import {withRouter} from "react-router-dom";
import {Button, Card, Form, Input, Modal, Table} from "antd";
import ListOperation from "components/ListOperation/home";
import React, {useEffect, useRef, useState} from "react";
import FilterBar from "components/FilterBar/FilterBar";
import {queryBlackList, removeBlackListById} from "./api";
import history from "common/history";

const FormItem = Form.Item;

const BlackList = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = React.useState(false);
  const [dataSource, setDataSource] = React.useState([]);
  const [paginations, setPaginations] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1, // sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户昵称", // width: "250px",
      dataIndex: "customerName",
      ellipsis: "true",
      align: "center",
      render: (text, record) => (
        <div>
          <span>{record.customerName}</span>
          <span style={{ color: "#07c160" }}>{record.companyName}</span>
        </div>
      ),
    },
    {
      title: "加入时间", // width: "160px",
      dataIndex: "createTime",
      ellipsis: "true",
      align: "center",
    },

    {
      title: "操作",
      ellipsis: "true",
      width: "220px",
      align: "center", // fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "客户详情" },
          { onClick: () => handleExportModal(record), name: "移除黑名单" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList(pagination);
  };
  const handleEdit = (record) => {
    console.log(`[record]: `, record);
    history.push(`/wecom/customer/details?id=${record.customerId}`);
  };

  const handleQuery = () => {
    fetchList({...paginations, current: 1});
  };
  const handleReset = () => {
    formRef.current.resetFields();
    fetchList({...paginations, current: 1});
  };
  const fetchList = (pagination = paginations) => {
    setLoading(true);

    formRef.current.validateFields().then((formData) => {
      queryBlackList({
        current: pagination.current,
        size: pagination.pageSize,
        ...formData,
      })
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  useEffect(() => {
    formRef.current.setFieldsValue({
      triggerDate: [],
    });
  }, []);

  useEffect(() => {
    fetchList();
  }, [paginations.current]);
  const [currentRow, setCurrentRow] = useState(null);
  const [modalRef] = Form.useForm(null);

  const [exportModal, setExportModal] = useState(false);

  const [exportModalForm] = Form.useForm();
  const handleExportModal = (row) => {
    setCurrentRow(row);
    setExportModal(true);
  };

  const handleExportOk = () => {
    exportModalForm.validateFields().then((data) => {
      removeBlackListById(currentRow.id).then(() => {
        fetchList();
        setExportModal(false);
      });
      // setExportModal(false);
      // console.log(`[data]: `, data);
    });
  };

  return (
      <>
        <Card
            extra={
              <Button type="primary" onClick={() => history.goBack()}>
                返回
              </Button>
            }
            title={"黑名单管理"}
            bordered={false}
            bodyStyle={{display: 'none'}}
        ></Card>
        <br/>
        <Card bordered={false}>

          <FilterBar bodyStyle={{padding: "unset"}}>
            <Form layout={"inline"} ref={formRef}>
              <FormItem name="customerName">
                <Input placeholder="客户昵称" allowClear/>
              </FormItem>
            </Form>
            <div className="flex flex-space-between">
              <div>
                <Button type="primary" onClick={() => handleQuery()}>
                  查询
                </Button>
                <Button onClick={() => handleReset()}>重置筛选</Button>
              </div>
            </div>
          </FilterBar>
          <Card bordered={false} bodyStyle={{padding: "unset"}}>
            <Table
                rowKey="id"
                loading={loading}
                dataSource={dataSource}
                columns={columns}
                scroll={{x: 1300}}
                pagination={paginations}
                onChange={onChangeTable}
            />
          </Card>

          <Modal
              title={"移除确认"}
              open={exportModal}
              onOk={handleExportOk}
              onCancel={() => setExportModal(false)}
          >
            您将为【{currentRow?.customerName}】客户进行移除黑名单操作，确认继续吗？
          </Modal>
        </Card>
      </>

  );
};
export default withRouter(BlackList);
