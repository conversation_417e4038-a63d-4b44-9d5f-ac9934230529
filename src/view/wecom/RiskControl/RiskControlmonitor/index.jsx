import { withRouter } from "react-router-dom"
import {
  <PERSON><PERSON>,
  Card,
  DatePicker,
  Drawer,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Table,
  Tooltip,
  TreeSelect,
} from "antd"
import ListOperation from "components/ListOperation/home"
import React, { useEffect, useRef, useState } from "react"
import FilterBar from "components/FilterBar/FilterBar"
import {
  exportTableDataOfRiskControlLog,
  getRiskControlMonitorLog,
  queryDeviceOptions,
  updateRiskControlMonitorLog,
} from "./api"
import { QuestionCircleOutlined } from "@ant-design/icons"
import history from "common/history"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home"
import ConversationDrawer from "components/ConversationDrawer"
import { getRiskControlMonitorRule } from "../MonitorRule/api"
import moment from "moment"
import { useDict } from "@/hooks/useDict"
import { highlightWord } from "@/utils/highlightWord"
import TypeMessageDrawer from "@/view/wecom/DigitalHuman/AgentSeat/comps/TypeMessage/drawer"
import WibotEditorView from "components/WibotEditorView/home"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const objectTypes = [
  { value: "CUSTOMER", label: "客户" },
  {
    value: "EMPLOYEE",
    label: "坐席",
  }, // {value: 'GROUP', label: '群组'},
  // {value: 'SYSTEM', label: '系统'},
  // {value: 'ROBOT', label: '机器人'},
]

const actionTypes = [
  {
    value: "WARN_AND_BLOCK",
    label: "警告并拦截发送",
    group: "EMPLOYEE",
    tips: "",
  },
  { value: "WARN_EMPLOYEE", label: "仅发警告", group: "EMPLOYEE", tips: "" },
  {
    value: "BLACKLIST",
    label: "拉黑",
    group: "CUSTOMER",
    tips: "审核通过后，系统不再为黑名单客户创建新对话",
  },
  { value: "WARN_CUSTOMER", label: "预警", group: "CUSTOMER", tips: "" },
]

const auditTypes = [
  { label: "无需审核", value: "NO_AUDIT_REQUIRED" },
  {
    label: "待审核",
    value: "TO_BE_AUDITED",
  },
  { label: "已审核", value: "AUDITED" },
]

const MonitorRule = () => {
  const formRef = useRef(null)
  const [loading, setLoading] = React.useState(false)
  const [dataSource, setDataSource] = React.useState([])
  const [paginations, setPaginations] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [deviceEmployeeIdStrList, setDeviceEmployeeIdStrList] = useState([])
  const [agentEmployeeIdStrList, setAgentEmployeeIdStrList] = useState([])
  const eventName = useDict("MONITOR_TYPE")
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1, // sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户昵称", // width: "250px",
      dataIndex: "customerName",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "托管帐号",
      width: "200px",
      dataIndex: "deviceEmployeeName",
      align: "center",
    },
    {
      title: "坐席", // width: "160px",
      dataIndex: "agentEmployeeName",
      key: "actionType",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "触发时间",
      width: "160px",
      dataIndex: "triggerTime",
      // ellipsis: "true",
      align: "center",
    },
    {
      title: "触发规则名称", // width: "160px",
      dataIndex: "ruleName",
      ellipsis: "true",
      width: "160px",
      align: "center",
      render(value) {
        return (
          <Tooltip placement="topLeft" title={value}>
            {value}
          </Tooltip>
        )
      },
    },
    {
      title: "监控对象", // width: "160px",
      dataIndex: "objectType",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div>{objectTypes.find((i) => i.value === value).label}</div>
      ),
    },
    {
      title: "触发事件", // width: "160px",
      dataIndex: "triggerEvent",
      ellipsis: "true",
      align: "center",
      render(value, record, index) {
        return (
          <div>
            {record.hitMonitorObject.type && (
              <Tooltip
                title={record.hitMonitorObject.sensitiveWordList.join(",")}
              >
                {eventName.get(record.hitMonitorObject.type)}:
                {record.hitMonitorObject.sensitiveWordList.join(",")}
              </Tooltip>
            )}
          </div>
        )
      },
    },
    {
      title: "发送内容", // width: "160px",
      dataIndex: "actionType",
      key: "actionType",
      ellipsis: "true",
      align: "center",
      render(value, record) {
        return (
          <Tooltip placement="topLeft" title={record.sendContent.showContent}>
            {record.sendContent.showContent}
          </Tooltip>
        )
      },
    },
    {
      title: "处理措施", // width: "160px",
      dataIndex: "actionType",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div>
          {actionTypes.find((i) => i.value === record.actionType).label}
        </div>
      ),
    },
    {
      title: "审核情况", // width: "160px",
      dataIndex: "auditState",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div>{auditTypes.find((i) => i.value === record.auditState).label}</div>
      ),
    },

    {
      title: "操作",
      ellipsis: "true",
      width: "120px",
      align: "center", // fixed: "right",
      render: (value, record, index) => {
        let opts = [
          // { onClick: () => handleStart(record), name: "启动" },
          // { onClick: () => handleStop(record), name: "停止" },
          // { onClick: () => handleChangeNmber(record), name: "换号" },
          { onClick: () => handleDetail(record, "detail"), name: "详情" },
        ]
        if (
          record.auditState ===
          auditTypes.find((i) => i.value === "TO_BE_AUDITED").value
        ) {
          opts.push({
            onClick: () => handleDetail(record, "audit"),
            name: "审核",
          })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList(pagination)
  }
  const handleEdit = (record) => {
    setCurrentRow(record)
    setModalOpen(true)
    modalRef.setFieldsValue({
      triggerDate: [],
    })
  }

  const handleQuery = () => {
    fetchList({ ...paginations, current: 1 })
  }
  const handleReset = () => {
    formRef.current.resetFields()
    fetchList({ ...paginations, current: 1 })
  }
  const fetchList = (pagination = paginations) => {
    setLoading(true)
    queryDeviceOptions().then((res) => {
      function toOptions(item) {
        item.label = item.title
        item.value = item.key
        item.options = item.children.map((item) => {
          return toOptions(item)
        })
        return item
      }

      const result = res.map((item) => toOptions(item))
      setDeviceEmployeeIdStrList(result)
    })
    formRef.current.validateFields().then((formData) => {
      const [minTriggerTime = null, maxTriggerTime = null] =
        formData.triggerDate || []

      getRiskControlMonitorLog({
        current: pagination.current,
        size: pagination.pageSize,
        minTriggerTime: minTriggerTime
          ? moment(minTriggerTime).format("YYYY-MM-DD 00:00:00")
          : null,
        maxTriggerTime: maxTriggerTime
          ? moment(maxTriggerTime).format("YYYY-MM-DD 23:59:59")
          : null,
        ...formData,
        deviceEmployeeIdStrList:
          formData.deviceEmployeeIdStrList?.join(",") || null,
        agentEmployeeIdStrList:
          formData.agentEmployeeIdStrList?.join(",") || null,
      })
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  useEffect(() => {
    formRef.current.setFieldsValue({
      triggerDate: [],
    })
    fetchList()
  }, [])
  const [currentRow, setCurrentRow] = useState(null)
  const [isModalOpen, setModalOpen] = React.useState(false)
  const [modalRef] = Form.useForm(null)
  const [actionsType, setActionsType] = useState([])
  useEffect(() => {
    setActionsType(
      actionTypes.filter(
        (i) => i.group === modalRef.getFieldValue("objectType")
      )
    )
  }, [modalRef.getFieldValue("objectType")])
  const handleOpenModal = () => {
    setModalOpen(true)
    modalRef.resetFields()
    modalRef.setFieldsValue({
      name: "",
      objectType: objectTypes.find((i) => i.value === "EMPLOYEE").value,
      actionType: actionTypes.find((i) => i.value === "WARN_AND_BLOCK").value,
      word: true,
    })
  }

  const handleCancel = () => {
    setModalOpen(false)
  }
  const handleOk = () => {
    modalRef.validateFields().then((values) => {
      if (currentRow.id) {
        updateRiskControlMonitorRule({
          ...values,
          monitorRuleId: currentRow.id,
          monitorObjectList: [
            {
              type: "SENSITIVE_WORD",
              sensitiveWordList: values.word.split(","),
            },
          ],
        }).finally(() => {
          setModalOpen(false)
          fetchList()
          setCurrentRow(null)
        })
      } else {
        addRiskControlMonitorRule({
          ...values,
          monitorObjectList: [
            {
              type: "SENSITIVE_WORD",
              sensitiveWordList: values.word.split(","),
            },
          ],
        }).finally(() => {
          setModalOpen(false)
          fetchList()
        })
      }
    })
  }

  const [exportModal, setExportModal] = useState(false)

  const [exportModalForm] = Form.useForm()
  const handleExportModal = () => {
    setExportModal(true)
  }

  const handleExportOk = () => {
    exportModalForm.validateFields().then((data) => {
      if (!data.date) {
        return message.error("请选择时间范围")
      }
      const [minTriggerTime = null, maxTriggerTime = null] = data.date

      exportTableDataOfRiskControlLog(
        moment(minTriggerTime).format("YYYY-MM-DD 00:00:00"),
        moment(maxTriggerTime).format("YYYY-MM-DD 23:59:59")
      ).then((res) => {
        message.success("导出成功")
        setExportModal(false)
      })
    })
  }

  const [drawerParams, setDrawerParams] = useState({ visible: false })
  const [currentConversation, setCurrentConversation] = useState()
  const [auditForm] = Form.useForm()
  const [monitorRuleOptions, setMonitorRuleOptions] = useState([])

  useEffect(() => {
    getRiskControlMonitorRule({ paged: false }).then((res) => {
      setMonitorRuleOptions(
        res.records?.map((i) => {
          return {
            label: i.name,
            value: i.name,
          }
        })
      )
    })
  }, [])

  const handleAudit = (data) => {
    auditForm.validateFields().then((form) => {
      if (!form.auditResult) {
        return message.error("请选择审核结果")
      }
      updateRiskControlMonitorLog({ riskControlLogId: data.id, ...form }).then(
        (r) => {
          auditForm.resetFields()
          setDrawerParams({ visible: false })
          fetchList({ current: 1 })
        }
      )
    })
  }
  const handleDetail = (record, type) => {
    console.log(`[record]: `, record)
    const { id, customerName, employeeName } = record
    setCurrentConversation(record)
    setDrawerParams({
      currentConversation,
      id,
      visible: true,
      title: `对话详情:`,
      onCancel: () => {
        setDrawerParams({ visible: false })
        setCurrentConversation(null)
        auditForm.resetFields()
      },
      actions: {
        change(data) {
          setCurrentConversation(data)
        },
      },
      children(data) {
        if (!data) {
          return ""
        }
        const isAudit =
          data.auditState ===
          auditTypes.find((i) => i.value === "TO_BE_AUDITED").value
        const disabled = !isAudit || type === "detail"
        auditForm.setFieldValue("auditResult", data.auditResult)
        auditForm.setFieldValue("auditRemark", data.auditRemark)

        return (
          data && (
            <Drawer
              className="HitDetail-Drawer-Container"
              title={type === "detail" ? `详情` : `审核`}
              placement="right"
              open={true}
              mask={false}
              closable={false}
            >
              <Card title={data.ruleName} extra={data.createTime}>
                <Form form={auditForm} disabled={type === "detail" || !isAudit}>
                  <FormItem label="发送内容">
                    <WibotEditorView
                      html={highlightWord(
                        data.sendContent.showContent,
                        data.hitMonitorObject.sensitiveWordList[0]
                      )}
                    />
                  </FormItem>
                  <FormItem label="处理措施">
                    {actionTypes.find((i) => i.value === data.actionType).label}
                  </FormItem>
                  <FormItem label="审核结果" name="auditResult">
                    <Radio.Group>
                      <Radio value="PASSED">通过</Radio>
                      <Radio value="REJECTED">不通过</Radio>
                    </Radio.Group>
                  </FormItem>
                  <FormItem label="审核意见" name="auditRemark">
                    <Input.TextArea showCount maxLength={100}></Input.TextArea>
                  </FormItem>

                  <FormItem>
                    <Button
                      type="primary"
                      htmlType="submit"
                      style={{ marginRight: 8 }}
                      onClick={() => handleAudit(data)}
                    >
                      提交
                    </Button>
                    <Button
                      htmlType="button"
                      onClick={() => {
                        setDrawerParams({ visible: false })
                        auditForm.resetFields()
                      }}
                    >
                      取消
                    </Button>
                  </FormItem>
                </Form>
              </Card>
            </Drawer>
          )
        )
      },
    })
  }

  const onChange = (e) => {
    const value = e.target.value
    const data = actionTypes.filter((i) => i.group === value)
    setActionsType(data)
  }
  return (
    <Card bordered={false}>
      <FilterBar bodyStyle={{ padding: "unset" }}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="customerName">
            <Input placeholder="客户昵称" allowClear />
          </FormItem>
          <FormItem name="deviceEmployeeIdStrList">
            <TreeSelect
              showSearch
              showArrow
              allowClear
              multiple
              treeDefaultExpandAll
              showCheckedStrategy={TreeSelect.SHOW_ALL}
              placeholder="托管账号"
              maxTagCount="responsive"
              treeData={deviceEmployeeIdStrList}
              fieldNames={{
                label: "title",
                value: "key",
                children: "children",
              }}
              treeNodeFilterProp="title"
            />
          </FormItem>
          <FormItem
            name="agentEmployeeIdStrList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal
              title={`坐席`}
              onlyEmployee
              needExcludeDepFlag={false}
            />
          </FormItem>

          <FormItem name="triggerDate" label="触发时间">
            <RangePicker format={"YYYY-MM-DD"} allowClear />
          </FormItem>

          <FormItem name="ruleName">
            {/*<Input placeholder="触发规则名称" allowClear />*/}
            <Select
              name="ruleName"
              showSearch
              allowClear
              placeholder="触发规则名称"
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={monitorRuleOptions}
            />
          </FormItem>

          <FormItem name="objectType">
            <Select placeholder="监控对象" allowClear>
              {objectTypes.map((item, index) => {
                return (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                )
              })}
            </Select>
          </FormItem>
          <FormItem name="actionType">
            <Select placeholder="处理措施" allowClear>
              {actionTypes.map((item, index) => {
                return (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                )
              })}
            </Select>
          </FormItem>
          <FormItem name="auditState">
            <Select placeholder="审核情况" allowClear>
              {auditTypes.map((item, index) => {
                return (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                )
              })}
            </Select>
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button
              type="primary"
              onClick={() => {
                history.push("/wecom/riskControlmonitor/blackList")
              }}
            >
              黑名单
            </Button>
            <Button type="primary" onClick={handleExportModal}>
              导出
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
        <Modal
          title={currentRow ? "编辑规则" : "新建规则"}
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Form layout={"horizontal"} form={modalRef}>
            <FormItem
              label="规则名称"
              name="name"
              rules={[{ required: true, message: "请输入规则名称" }]}
            >
              <Input
                placeholder="请输入规则名称"
                showCount
                maxLength={20}
                allowClear
              />
            </FormItem>
            <FormItem
              label="监控对象"
              name="objectType"
              rules={[{ required: false, message: "请选择监控对象" }]}
            >
              <Radio.Group onChange={onChange}>
                {objectTypes.map((item, index) => {
                  return (
                    <Radio value={item.value} key={index}>
                      {item.label}
                    </Radio>
                  )
                })}
              </Radio.Group>
            </FormItem>
            <FormItem
              label="处理措施"
              tooltip="当内容符合多个处理措施时，对于坐席，“警告并拦截发送，优先级高于“仅发警告”；对于客户，“拉黑”优先级高于“预警“。"
              name="actionType"
              rules={[{ required: false, message: "请选择处理措施" }]}
            >
              <Radio.Group>
                {actionsType.map((item, index) => {
                  return (
                    <Radio value={item.value} key={index}>
                      {item.label}
                      {item.tips && (
                        <Tooltip title={item.tips}>
                          <QuestionCircleOutlined />
                        </Tooltip>
                      )}
                    </Radio>
                  )
                })}
              </Radio.Group>
            </FormItem>
            <FormItem
              label="敏感词"
              rules={[{ required: true, message: "请输入敏感词" }]}
            >
              <div>
                <FormItem name="word">
                  <Input placeholder="请输入敏感词" allowClear />
                </FormItem>
                <div>支持粘贴多个敏感词用“,”分隔开，回车键确认</div>
              </div>
            </FormItem>
          </Form>
        </Modal>
      </Card>

      <Modal
        title={"风控监控数据"}
        open={exportModal}
        onOk={handleExportOk}
        onCancel={() => setExportModal(false)}
      >
        <Form layout={"horizontal"} form={exportModalForm}>
          <FormItem>
            <div style={{ color: "#ccc" }}>
              根据筛选的时间范围，可选择导出当前列表对应时间内的监控数据
            </div>
          </FormItem>
          <FormItem label="触发时间" name="date" required>
            <RangePicker format={"YYYY-MM-DD"} />
          </FormItem>
        </Form>
      </Modal>
      {/*<TypeMessageDrawer params={drawerParams} />*/}
      <ConversationDrawer params={drawerParams} />
    </Card>
  )
}
export default withRouter(MonitorRule)
