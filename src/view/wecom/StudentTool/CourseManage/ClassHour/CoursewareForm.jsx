/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 14:08
 * @LastEditTime: 2023/11/09 16:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/ClassHour/CoursewareForm.jsx
 * @Description: '课件设计'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin,
  Button,
  Form,
  Input,
  Radio,
  Switch,
  message,
  Upload,
} from 'antd';
import {
  PlusOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { normFile, removeInputEmpty, editorIsEmpty } from 'common/regular';
import {
  compressImage,
  base64ToFile,
  beforeUpload,
  videoBeforeUpload,
} from 'common/image';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import WibotEditor from "components/WibotEditor/home"
import WibotUploadImage from 'components/WibotUploadImage/home';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

const CoursewareForm = (props) => {
  const WibotEditorRef = useRef(null);
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [id, setId] = useState(null);
  const [courseId, setCourseId] = useState(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadVideoLoading, setUploadVideoLoading] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [swichFlag, setSwichFlag] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [videoType, setVideoType] = useState(0);
  const [courseType, setCourseType] = useState(0);
  const [fileData, setFileData] = useState([]);

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ id });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { id } = params;
    await apiCall(`/study/courseItem/${id}`, 'GET')
      .then((res) => {
        const { courseId, coursewareFlag, coursewareSetting, description } = res;
        if (coursewareSetting) {
          setVideoUrl(coursewareSetting.videoFileId);
          setImageUrl(coursewareSetting.cover);
          coursewareSetting.cover = coursewareSetting.cover && [
            coursewareSetting.cover,
          ];
          coursewareSetting.videoFileId = coursewareSetting.videoFileId && [
            coursewareSetting.videoFileId,
          ];
          setVideoType(coursewareSetting.videoFileId ? 0 : 1);
          let timer = setTimeout(() => {
            WibotEditorRef.current.setHtml(coursewareSetting.description)
            clearTimeout(timer)
          }, 300);
        }

        formForm.setFieldsValue({
          ...res,
          coursewareSetting: coursewareSetting && { ...coursewareSetting },
          videoType: coursewareSetting
            ? coursewareSetting.videoFileId
              ? 0
              : 1
            : 0,
        });
        setCourseId(courseId);
        setSwichFlag(coursewareFlag);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const customRequest = (config) => {
    const File = config.file;
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    if (File.type.includes('image/')) {
      // 通过append方法来追加数据
      formData.append('file', File); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data)
        .then((res) => {
          const { fileId, fileUrl } = res;
          setImageUrl(fileUrl);
          formForm.setFieldValue(['coursewareSetting', 'cover'], [fileId]);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setUploadLoading(false);
        });
    } else if (File.type.includes('video/')) {
      // 通过append方法来追加数据
      formData.append('file', File); // 返回压缩后的图片
      const data = formData;
      let fileIds = [];
      apiCall('/file/video', 'POST', data)
        .then((res) => {
          const { fileId, fileUrl } = res;
          setVideoUrl(fileUrl);
          fileIds.push(fileId);
          formForm.setFieldValue(['coursewareSetting', 'videoFileId'], fileIds);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setUploadVideoLoading(false);
        });
    }
  };

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === 'uploading') {
      type ? setUploadVideoLoading(true) : setUploadLoading(true);
      return;
    }
  };

  const handleResetUpload = (e, type = null) => {
    e.preventDefault(); // 阻止浏览器默认行为
    e.stopPropagation(); // 阻止事件冒泡
    if (type) {
      setVideoUrl('');
      formForm.setFieldValue(['coursewareSetting', 'videoFileId'], '');
      return;
    }
    setImageUrl('');
    formForm.setFieldValue(['coursewareSetting', 'cover'], '');
  };

  // 选择文件
  const customFileRequest = (config) => {
    const File = config.file;
    setFileData([
      {
        name: File.name,
        status: 'uploading',
      },
    ]);
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    apiCall('/file/add', 'POST', data)
      .then((res) => {
        const { fileId, fileUrl } = res;
        setFileData([
          {
            name: File.name,
            fileId,
            fileUrl,
            status: 'done',
          },
        ]);
        formForm.setFieldsValue({
          file: fileId,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onRemoveUpload = (file) => {
    setFileData([]);
  };

  // 课件模块
  const onChangeSwitch = (checked) => {
    setSwichFlag(checked);
  };

  // 选择上传视频类型
  const handleVideoType = (e) => {
    setVideoType(e.target.value);
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      if (swichFlag) {
        formData.coursewareSetting.cover =
          formData.coursewareSetting.cover.join(',');
        formData.coursewareSetting.videoFileId = formData.coursewareSetting.videoFileId?.join(',');
      }
      const data = {
        courseId,
        ...formData,
      };
      apiCall(`/study/courseItem/update/${id}`, 'POST', data)
        .then((res) => {
          message.success('保存成功！');
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const uploadVideoButton = (
    <div>
      {uploadVideoLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>
        {uploadVideoLoading ? '上传中' : '上传'}
      </div>
    </div>
  );

  return (
    <div className="CoursewareForm" style={{ paddingBottom: '24px' }}>
      <Spin spinning={loading}>
        <Form {...layout} form={formForm}>
          <h2>课件设计</h2>
          <FormItem
            name="coursewareFlag"
            label="课件模块"
            initialValue={false}
            valuePropName="checked"
            extra="课时必须包含课件或作业，否则课时无法使用"
          >
            <Switch onChange={onChangeSwitch} />
          </FormItem>
          {/* {
            swichFlag && <FormItem label="课件类型" name='courseType' initialValue={0} rules={[{ required: true, message: '请选择课件类型' }]}>
              <Radio.Group onChange={(e) => {
                setCourseType(e.target.value);
              }}>
                <Radio value={0}>视频</Radio>
                <Radio value={1}>文档</Radio>
              </Radio.Group>
            </FormItem>
          } */}
          {swichFlag && (
            <FormItem
              label="课件名称"
              name={['coursewareSetting', 'name']}
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: '请输入课件名称' }]}
            >
              <Input
                placeholder="请输入课件名称（30字内）"
                allowClear
                style={{ width: '240px' }}
              />
            </FormItem>
          )}
          {swichFlag && (
            <FormItem
              name={['coursewareSetting', 'cover']}
              valuePropName="fileList"
              getValueFromEvent={normFile}
              label="课件封面"
              rules={[{ required: true, message: '请上传图片' }]}
              extra="课程进行时展示，建议尺寸16:9，大小限制为2M，最多上传1张"
            >
              <Upload
                name="file"
                customRequest={customRequest}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={beforeUpload}
                onChange={onChangeUpload}
              >
                <WibotUploadImage
                  imageUrl={imageUrl}
                  loading={uploadLoading}
                  onClose={handleResetUpload}
                />
              </Upload>
            </FormItem>
          )}
          {swichFlag && (
            <>
              <FormItem label="课件内容" required>
                <div>
                  {courseType == 0 ? (
                    <>
                      <FormItem
                        name="videoType"
                        initialValue={0}
                        rules={[{ required: true, message: '请选择课件内容' }]}
                      >
                        <Radio.Group onChange={handleVideoType}>
                          <Radio value={0}>视频文件</Radio>
                          <Radio value={1}>视频链接</Radio>
                        </Radio.Group>
                      </FormItem>
                      {videoType == 0 ? (
                        <FormItem
                          name={['coursewareSetting', 'videoFileId']}
                          getValueFromEvent={normFile}
                          rules={[{ required: true, message: '请上传视频' }]}
                          extra="一个课件仅支持上传1个视频，大小限制为500MB"
                        >
                          <Upload
                            name="file"
                            customRequest={customRequest}
                            listType="picture-card"

                            showUploadList={false}
                            beforeUpload={(file) => videoBeforeUpload(file, 500)}
                            onChange={(info) => {
                              onChangeUpload(info, 'video');
                            }}
                          >
                            {videoUrl ? (
                              <div
                                style={{
                                  position: 'relative',
                                  width: '100%',
                                  height: '100%',
                                }}
                              >
                                <FileHOC src={videoUrl}>
                                  {(url) => (
                                    <video
                                      src={url}
                                      style={{ width: '100%', height: '100%' }}
                                    />
                                  )}
                                </FileHOC>
                                {' '}
                                <CloseCircleOutlined
                                  onClick={(e) => {
                                    handleResetUpload(e, 'video');
                                  }}
                                  className="cancel-upload-icon"
                                />
                              </div>
                            ) : (
                              uploadVideoButton
                            )}
                          </Upload>
                        </FormItem>
                      ) : (
                        <FormItem
                          name={['coursewareSetting', 'videoUrl']}
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            {
                              required: true,
                              message: '请输入视频链接',
                              type: 'url',
                            },
                          ]}
                        >
                          <Input
                            placeholder="请填写视频链接，包含http开头"
                            allowClear
                            style={{ width: '240px' }}
                          />
                        </FormItem>
                      )}
                    </>
                  ) : (
                    <FormItem
                      name="file"
                      extra="一个课件仅支持上传1个文档"
                      rules={[{ required: true, message: '请上传文件' }]}
                    >
                      <Upload
                        name="file"
                        fileList={fileData}
                        // listType="picture"
                        customRequest={customFileRequest}
                        beforeUpload={(file) => {
                          const isFile =
                            file.type.includes('pdf') ||
                            file.type.includes('msword') ||
                            file.type.includes(
                              'vnd.openxmlformats-officedocument.wordprocessingml.document'
                            );
                          if (!isFile) {
                            message.error('请上传doc、docx、pdf格式的文档!');
                          }
                          return isFile;
                        }}
                        onRemove={onRemoveUpload}
                      >
                        {fileData.length <= 0 ? (
                          <Button icon={<UploadOutlined />}>选择文件</Button>
                        ) : null}
                      </Upload>
                    </FormItem>
                  )}
                </div>
              </FormItem>
              <FormItem
                label="课件介绍"
                name={['coursewareSetting', 'description']}
                rules={[{ required: true, message: '请输入课件介绍' }]}
              >
                <WibotEditor
                  ref={WibotEditorRef}
                  toolbarConfig={{
                    excludeKeys: [
                      'group-video',
                    ]
                  }}
                  onChangeHtml={(html) => {
                    formForm.setFieldValue(['coursewareSetting', 'description'], editorIsEmpty(html) ? '' : html)
                  }}
                />
              </FormItem>
            </>
          )}
        </Form>
        <div
          style={{
            width: '30%',
            margin: '0 auto',
            display: 'flex',
            justifyContent: 'space-around',
          }}
        >
          <Button type="primary" onClick={() => onSubmit()}>
            保存
          </Button>
        </div>
      </Spin>
    </div>
  );
};
export default withRouter(CoursewareForm);
