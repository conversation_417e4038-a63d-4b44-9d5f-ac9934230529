/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 15:29
 * @LastEditTime: 2025/05/15 15:13
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/ClassHour/TestDesignForm.jsx
 * @Description: '作业设计'
 */

import React, { useState, useEffect } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Form,
  Input,
  Row,
  Col,
  Radio,
  Switch,
  Checkbox,
  Steps,
  Typography,
  Space,
  message,
  Upload,
} from "antd"
import { PlusOutlined, CloseCircleOutlined } from "@ant-design/icons"
import { removeInputEmpty, normFile } from "common/regular"
import { compressImage, base64ToFile, beforeUpload } from "common/image"
import { apiCall } from "common/utils"
import { qs2obj } from "common/object"
import SubjectModal from "../comps/SubjectModal/home"
import WibotUploadImage from "components/WibotUploadImage/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import "./TestDesignForm.less"

const FormItem = Form.Item
const { TextArea } = Input
const { Paragraph } = Typography
const { Step } = Steps

const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}

const TestDesignForm = (props) => {
  const [formForm] = Form.useForm()
  const [id, setId] = useState(null)
  const [examId, setExamId] = useState(null)
  const [loading, setLoading] = useState(false)
  const [imageUrl, setImageUrl] = useState("")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [msgList, setMsgList] = useState([])
  const [previewData, setPreviewData] = useState({})
  const [stepCurrent, setStepCurrent] = useState(0)
  const [swichFlag, setSwichFlag] = useState(false)
  const [subjectFormParams, setSubjectFormParams] = useState({
    visible: false,
  })

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      fetchList({ courseItemId: id })
    }
  }, [])

  const fetchList = async (params = {}) => {
    const { courseItemId } = params
    setLoading(true)
    await apiCall(`/study/courseItem/${courseItemId}`, "GET")
      .then((res) => {
        if (res) {
          const { examId, examVO, examFlag } = res
          setExamId(examId)
          setSwichFlag(examFlag)
          if (examVO) {
            examVO.cover = examVO.cover && [examVO.cover]
            setImageUrl(examVO.cover)
            setMsgList(examVO?.formItemList || [])
          }
          formForm.setFieldsValue({
            ...res,
            saveExamDTO: examVO,
          })
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 步骤切换
  const onChangeSteps = (current) => {
    setStepCurrent(current)
  }

  // 作业模块
  const onChangeSwitch = (checked) => {
    setSwichFlag(checked)
    // liveCodeform.setFieldsValue({
    //   autoCreateGroup: checked ? 1 : 0
    // });
  }

  // 作业封面
  const onChangeUpload = (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true)
      return
    }
  }

  const customRequest = (config) => {
    const File = config.file
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        const { fileId, fileUrl } = res
        setImageUrl(fileUrl)
        const data = previewData
        data.image = fileUrl
        setPreviewData({ ...data })
        formForm.setFieldValue(["saveExamDTO", "cover"], [fileId])
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
      })
  }

  const handleResetUpload = (e) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    setImageUrl("")
    formForm.setFieldValue(["saveExamDTO", "cover"], "")
  }

  // 作业内容-添加题目
  const handleAddMsg = (value) => {
    const data = JSON.parse(JSON.stringify(msgList))
    switch (value) {
      case "RADIO":
        data.push({
          type: "RADIO",
          optionList: [
            {
              answer: false,
            },
            {
              answer: false,
            },
            {
              answer: false,
            },
          ],
        })
        setMsgList(JSON.parse(JSON.stringify(data)))
        break
      case "CHECKBOX":
        data.push({
          type: "CHECKBOX",
          optionList: [
            {
              answer: false,
            },
            {
              answer: false,
            },
            {
              answer: false,
            },
          ],
        })
        setMsgList(JSON.parse(JSON.stringify(data)))
        break
      case "TEXTAREA":
        data.push({
          type: "TEXTAREA",
        })
        setMsgList(JSON.parse(JSON.stringify(data)))
        break
      case "FILL_BLANK":
        data.push({
          type: "FILL_BLANK",
          correctAnswer: [],
        })
        setMsgList(JSON.parse(JSON.stringify(data)))
        break
      case "LIBRARY":
        setSubjectFormParams({
          visible: true,
          onSubmit: (params) => {
            setSubjectFormParams({ visible: false })
            data.push(params.formItem)
            setMsgList(JSON.parse(JSON.stringify(data)))
            formForm.setFieldValue(["saveExamDTO", "formItemList"], data)
          },
          onCancel: () => {
            setSubjectFormParams({ visible: false })
          },
        })
        break
    }
  }

  // 删除题目
  const handleDelMsg = (index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data.splice(index, 1)
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 报送题目改变
  const handleChangeTitle = (e, index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].title = e.target.value
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 报送题目选项改变
  const handleChangeOption = (e, index, andex) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList[andex].name = e.target.value
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 添加题目选项
  const onAdd = (index) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.push({
      answer: false,
    })
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 删除题目选项
  const handleDelItem = (index, andex, type) => {
    const data = JSON.parse(JSON.stringify(msgList))
    if (type == "FILL_BLANK") {
      data[index].correctAnswer.splice(andex, 1)
    } else {
      data[index].optionList.splice(andex, 1)
    }
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 选择答案-单选
  const handleAnswer = (index, andex) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.forEach((a, i) => {
      if (i == andex) {
        a.answer = true
      } else {
        a.answer = false
      }
    })
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 选择答案-多选
  const handleMoreAnswer = (index, andex) => {
    const data = JSON.parse(JSON.stringify(msgList))
    data[index].optionList.forEach((a, i) => {
      if (i == andex) {
        a.answer = !a.answer
      }
    })
    setMsgList(JSON.parse(JSON.stringify(data)))
  }

  // 插入填空
  const handleAddContent = async (index) => {
    const insertHTML = "#________#"
    const inputIndex = document.getElementById(`msgTextInput${index}`) // 获取dom
    const startPos = inputIndex.selectionStart
    const endPos = inputIndex.selectionEnd
    const gapField =
      formForm.getFieldValue([
        "saveExamDTO",
        "formItemList",
        index,
        "textContent",
      ]) ?? ""
    if (startPos === undefined || endPos === undefined) {
      return
    }
    const text =
      gapField.substring(0, startPos) + insertHTML + gapField.substring(endPos)
    formForm.setFieldValue(
      ["saveExamDTO", "formItemList", index, "textContent"],
      text
    )

    const data = JSON.parse(JSON.stringify(msgList))
    data[index].textContent = text
    data[index].correctAnswer.push("")
    setMsgList(JSON.parse(JSON.stringify(data)))

    // react-关键在于给任意一个方法同步
    await inputIndex.focus()
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    )
  }

  // 填空题统计符号数量
  function sum(str, a) {
    let b = str.indexOf(a)
    let num = 0
    while (b !== -1) {
      num++
      b = str.indexOf(a, b + 1)
    }
    return num
  }

  const onSubmit = () => {
    formForm
      .validateFields()
      .then((formData) => {
        if (swichFlag) {
          if (!msgList.length > 0) {
            message.error("请至少添加一个题目！")
            return
          }
          for (let i = 0; i < msgList.length; i++) {
            if (msgList[i].type == "RADIO" || msgList[i].type == "CHECKBOX") {
              if (!msgList[i].optionList.length > 0) {
                message.error("请给题目至少添加一个选项！")
                return
              }
              if (!msgList[i].optionList.some((item) => item.answer)) {
                message.error("请给题目选择答案！")
                return
              }
            } else if (msgList[i].type == "FILL_BLANK") {
              if (msgList[i].textContent.indexOf("#________#") <= -1) {
                message.error("请插入填空！")
                return
              } else if (
                sum(msgList[i].textContent, "#________#") !=
                msgList[i].correctAnswer.length
              ) {
                message.error("插入填空数需与正确答案数一致！")
                return
              }
            }
          }
          formData.saveExamDTO.cover = formData.saveExamDTO.cover.join(",")
          const newMsgList = JSON.parse(JSON.stringify(msgList))
          newMsgList.forEach((item) => {
            if (item.type == "RADIO" || item.type == "CHECKBOX") {
              item.optionList.forEach((atem, andex) => {
                atem.seqNo = andex
              })
            }
          })
          formData.saveExamDTO.formItemList = newMsgList
        }
        setLoading(true)
        const data = {
          examId,
          ...formData,
        }
        apiCall(`/study/courseItem/update/${id}`, "POST", data)
          .then((res) => {
            message.success("保存成功！")
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setLoading(false)
          })
      })
      .catch((err) => {
        message.error("请完善作业步骤一信息！")
      })
  }

  return (
    <div className="TestDesignForm">
      <Spin spinning={loading}>
        <Card bordered={false} className="TestCard">
          <Row>
            <Col xs={24} lg={16}>
              <Form {...layout} form={formForm}>
                <FormItem
                  name="examFlag"
                  label="作业模块"
                  valuePropName="checked"
                  extra="课时必须包含课件或作业，否则课时无法使用"
                >
                  <Switch onChange={onChangeSwitch} />
                </FormItem>
                {swichFlag && (
                  <>
                    <Steps
                      current={stepCurrent}
                      onChange={onChangeSteps}
                      style={{ width: "80%", margin: "0 auto 20px" }}
                    >
                      <Step
                        status={stepCurrent == 0 ? "process" : "wait"}
                        title="步骤一"
                        description="作业设置"
                      />
                      <Step
                        status={stepCurrent == 1 ? "process" : "wait"}
                        title="步骤二"
                        description="作业内容"
                      />
                    </Steps>
                    {
                      <div
                        style={
                          stepCurrent == 0
                            ? { display: "block" }
                            : { display: "none" }
                        }
                      >
                        <FormItem
                          label="作业名称"
                          name={["saveExamDTO", "name"]}
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            { required: true, message: "请输入作业名称" },
                          ]}
                        >
                          <Input
                            placeholder="请输入作业名称（30字内）"
                            allowClear
                            style={{ width: "240px" }}
                            onChange={(e) => {
                              const data = previewData
                              data.name = e.target.value
                              setPreviewData({ ...data })
                            }}
                          />
                        </FormItem>
                        <FormItem
                          label="作业介绍"
                          name={["saveExamDTO", "description"]}
                          getValueFromEvent={(e) => removeInputEmpty(e)}
                          rules={[
                            { required: false, message: "请输入作业介绍" },
                          ]}
                        >
                          <TextArea
                            autoSize={{ minRows: 4, maxRows: 8 }}
                            maxLength={60}
                            placeholder="请输入作业介绍(300字)"
                            allowClear
                            onChange={(e) => {
                              const data = previewData
                              data.description = e.target.value
                              setPreviewData({ ...data })
                            }}
                          />
                        </FormItem>
                        <FormItem
                          name={["saveExamDTO", "cover"]}
                          valuePropName="fileList"
                          getValueFromEvent={normFile}
                          label="作业封面"
                          rules={[{ required: true, message: "请上传图片" }]}
                          extra="建议尺寸750*360，大小限制为2M，最多上传1张"
                        >
                          <Upload
                            name="file"
                            customRequest={customRequest}
                            listType="picture-card"
                            showUploadList={false}
                            beforeUpload={beforeUpload}
                            onChange={onChangeUpload}
                          >
                            <WibotUploadImage
                              imageUrl={imageUrl}
                              loading={uploadLoading}
                              onClose={handleResetUpload}
                            />
                          </Upload>
                        </FormItem>
                      </div>
                    }
                    {
                      <div
                        style={
                          stepCurrent == 1
                            ? { display: "block" }
                            : { display: "none" }
                        }
                      >
                        <h2>作业内容</h2>
                        <div className="submissionInfo">
                          <FormItem
                            wrapperCol={{ xs: { span: 24 }, sm: { span: 24 } }}
                          >
                            <FormItem
                              extra="请按编写需要创建作业题目"
                              style={{ marginBottom: "0px" }}
                            >
                              <div className="btn-flex">
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleAddMsg("RADIO")}
                                >
                                  单选
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleAddMsg("CHECKBOX")}
                                >
                                  多选
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleAddMsg("TEXTAREA")}
                                >
                                  问答
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleAddMsg("FILL_BLANK")}
                                >
                                  填空题
                                </Button>
                                <Button
                                  type="primary"
                                  icon={<PlusOutlined />}
                                  onClick={() => handleAddMsg("LIBRARY")}
                                >
                                  从题库选择
                                </Button>
                              </div>
                            </FormItem>
                            <div className="question">
                              {msgList.map((item, index) => (
                                <div key={index} className="msgItem">
                                  {item.type == "RADIO" && (
                                    <div
                                      className="question_one"
                                      style={{ marginBottom: "20px" }}
                                    >
                                      <span className="num">{index + 1}</span>
                                      <div>
                                        <div className="question-header">
                                          <FormItem
                                            name={[
                                              "saveExamDTO",
                                              "formItemList",
                                              index,
                                              "title",
                                            ]}
                                            getValueFromEvent={(e) =>
                                              removeInputEmpty(e)
                                            }
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入题目名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入题目名称"
                                              maxLength={150}
                                              value={msgList.value}
                                              style={{ width: "305px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeTitle(e, index)
                                              }}
                                            />
                                          </FormItem>
                                          <div className="question_radio tip">
                                            单选
                                          </div>
                                        </div>

                                        <div style={{ marginTop: "10px" }}>
                                          {item.optionList.map(
                                            (atem, andex) => (
                                              <div
                                                key={andex}
                                                className="question_two_item"
                                              >
                                                <Radio
                                                  defaultChecked={false}
                                                  disabled
                                                  style={{ marginTop: "5px" }}
                                                ></Radio>
                                                <FormItem
                                                  name={[
                                                    "saveExamDTO",
                                                    "formItemList",
                                                    index,
                                                    "optionList",
                                                    andex,
                                                    "name",
                                                  ]}
                                                  style={{
                                                    display: "inline-block",
                                                    marginBottom: "0px",
                                                  }}
                                                  rules={[
                                                    {
                                                      required: true,
                                                      message: "请输入选项名称",
                                                    },
                                                  ]}
                                                >
                                                  <Input
                                                    placeholder="请输入选项名称"
                                                    maxLength={150}
                                                    style={{ width: "280px" }}
                                                    allowClear
                                                    onChange={(e) => {
                                                      handleChangeOption(
                                                        e,
                                                        index,
                                                        andex
                                                      )
                                                    }}
                                                  />
                                                </FormItem>
                                                <div
                                                  className={`question_answer ${
                                                    atem.answer && "active"
                                                  }`}
                                                  onClick={() => {
                                                    handleAnswer(index, andex)
                                                  }}
                                                >
                                                  答案
                                                </div>
                                                <CloseCircleOutlined
                                                  className="tagClose"
                                                  onClick={() =>
                                                    handleDelItem(index, andex)
                                                  }
                                                />
                                              </div>
                                            )
                                          )}
                                          <Button
                                            type="primary"
                                            icon={<PlusOutlined />}
                                            onClick={() => onAdd(index)}
                                          >
                                            选项
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                  {item.type == "CHECKBOX" && (
                                    <div
                                      className="question_one"
                                      style={{ marginBottom: "20px" }}
                                    >
                                      <span className="num">{index + 1}</span>
                                      <div>
                                        <div className="question-header">
                                          <FormItem
                                            name={[
                                              "saveExamDTO",
                                              "formItemList",
                                              index,
                                              "title",
                                            ]}
                                            getValueFromEvent={(e) =>
                                              removeInputEmpty(e)
                                            }
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入题目名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入题目名称"
                                              maxLength={150}
                                              value={msgList.value}
                                              style={{ width: "305px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeTitle(e, index)
                                              }}
                                            />
                                          </FormItem>
                                          <div
                                            className="question_radio tip"
                                            style={{ background: "#facd91" }}
                                          >
                                            多选
                                          </div>
                                        </div>
                                        <div style={{ marginTop: "10px" }}>
                                          {item.optionList.map(
                                            (atem, andex) => (
                                              <div
                                                key={andex}
                                                className="question_two_item"
                                              >
                                                <Checkbox
                                                  defaultChecked={false}
                                                  disabled
                                                  style={{
                                                    marginTop: "5px",
                                                    marginRight: "10px",
                                                  }}
                                                ></Checkbox>
                                                <FormItem
                                                  name={[
                                                    "saveExamDTO",
                                                    "formItemList",
                                                    index,
                                                    "optionList",
                                                    andex,
                                                    "name",
                                                  ]}
                                                  style={{
                                                    display: "inline-block",
                                                    marginBottom: "0px",
                                                  }}
                                                  rules={[
                                                    {
                                                      required: true,
                                                      message: "请输入选项名称",
                                                    },
                                                  ]}
                                                >
                                                  <Input
                                                    placeholder="请输入选项名称"
                                                    maxLength={150}
                                                    style={{ width: "280px" }}
                                                    allowClear
                                                    onChange={(e) => {
                                                      handleChangeOption(
                                                        e,
                                                        index,
                                                        andex
                                                      )
                                                    }}
                                                  />
                                                </FormItem>
                                                <div
                                                  className={`question_answer ${
                                                    atem.answer && "active"
                                                  }`}
                                                  onClick={() => {
                                                    handleMoreAnswer(
                                                      index,
                                                      andex
                                                    )
                                                  }}
                                                >
                                                  答案
                                                </div>
                                                <CloseCircleOutlined
                                                  className="tagClose"
                                                  onClick={() =>
                                                    handleDelItem(index, andex)
                                                  }
                                                />
                                              </div>
                                            )
                                          )}
                                          <Button
                                            type="primary"
                                            icon={<PlusOutlined />}
                                            onClick={() => onAdd(index)}
                                          >
                                            选项
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                  {item.type == "TEXTAREA" && (
                                    <div
                                      className="question_one"
                                      style={{ marginBottom: "20px" }}
                                    >
                                      <span className="num">{index + 1}</span>
                                      <div>
                                        <div className="question-header">
                                          <FormItem
                                            name={[
                                              "saveExamDTO",
                                              "formItemList",
                                              index,
                                              "title",
                                            ]}
                                            getValueFromEvent={(e) =>
                                              removeInputEmpty(e)
                                            }
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入题目名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入题目名称"
                                              maxLength={150}
                                              value={msgList.value}
                                              style={{ width: "305px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeTitle(e, index)
                                              }}
                                            />
                                          </FormItem>
                                          <div
                                            className="question_radio tip"
                                            style={{ background: "#ec808d" }}
                                          >
                                            问答
                                          </div>
                                        </div>
                                        <FormItem
                                          name={[
                                            "saveExamDTO",
                                            "formItemList",
                                            index,
                                            "textContent",
                                          ]}
                                        >
                                          <TextArea
                                            placeholder="请输入（300字以内）"
                                            disabled
                                            autoSize={{
                                              minRows: 2,
                                              maxRows: 7,
                                            }}
                                            className="textArea-mid"
                                          />
                                        </FormItem>
                                      </div>
                                    </div>
                                  )}
                                  {item.type == "FILL_BLANK" && (
                                    <div
                                      className="question_one"
                                      style={{ marginBottom: "20px" }}
                                    >
                                      <span className="num">{index + 1}</span>
                                      <div>
                                        <div className="question-header">
                                          <FormItem
                                            name={[
                                              "saveExamDTO",
                                              "formItemList",
                                              index,
                                              "title",
                                            ]}
                                            getValueFromEvent={(e) =>
                                              removeInputEmpty(e)
                                            }
                                            rules={[
                                              {
                                                required: true,
                                                message: "请输入题目名称",
                                              },
                                            ]}
                                          >
                                            <Input
                                              placeholder="请输入题目名称"
                                              maxLength={150}
                                              value={msgList.value}
                                              style={{ width: "305px" }}
                                              allowClear
                                              onChange={(e) => {
                                                handleChangeTitle(e, index)
                                              }}
                                            />
                                          </FormItem>
                                          <div
                                            className="question_radio tip"
                                            style={{
                                              width: "48px",
                                              background: "#eb5945",
                                            }}
                                          >
                                            填空题
                                          </div>
                                        </div>
                                        <FormItem
                                          name={[
                                            "saveExamDTO",
                                            "formItemList",
                                            index,
                                            "textContent",
                                          ]}
                                          extra={
                                            <div>
                                              <Button
                                                type="primary"
                                                icon={<PlusOutlined />}
                                                onClick={() =>
                                                  handleAddContent(index)
                                                }
                                              >
                                                插入填空
                                              </Button>
                                              <div style={{ width: "360px" }}>
                                                注意：请不要在插入填空符“#____#”里输入内容或删除符号，否则该填空符无法生效
                                              </div>
                                            </div>
                                          }
                                          className="textArea-mid"
                                        >
                                          <TextArea
                                            id={`msgTextInput${index}`}
                                            allowClear
                                            autoSize={{
                                              minRows: 2,
                                              maxRows: 7,
                                            }}
                                            onChange={(e) => {
                                              const value = e.target.value
                                              const newValue = value.replace(
                                                /\n/g,
                                                ""
                                              )
                                              formForm.setFieldValue(
                                                [
                                                  "saveExamDTO",
                                                  "formItemList",
                                                  index,
                                                  "textContent",
                                                ],
                                                newValue
                                              )
                                              let newMsgList = JSON.parse(
                                                JSON.stringify(msgList)
                                              )
                                              newMsgList[index].textContent =
                                                newValue
                                              setMsgList(newMsgList)
                                            }}
                                          />
                                        </FormItem>
                                        {item.correctAnswer.map(
                                          (atem, andex) => (
                                            <div
                                              key={andex}
                                              className="question_two_item"
                                            >
                                              <FormItem
                                                name={[
                                                  "saveExamDTO",
                                                  "formItemList",
                                                  index,
                                                  "correctAnswer",
                                                  andex,
                                                ]}
                                                label="正确答案"
                                                style={{
                                                  display: "inline-block",
                                                  marginBottom: "0px",
                                                }}
                                                rules={[
                                                  {
                                                    required: true,
                                                    message: "请输入正确答案",
                                                  },
                                                ]}
                                              >
                                                <Input
                                                  placeholder="请输入正确答案"
                                                  maxLength={150}
                                                  style={{ width: "280px" }}
                                                  allowClear
                                                  onChange={(e) => {
                                                    const data = JSON.parse(
                                                      JSON.stringify(msgList)
                                                    )
                                                    data[index].correctAnswer[
                                                      andex
                                                    ] = e.target.value
                                                    setMsgList(
                                                      JSON.parse(
                                                        JSON.stringify(data)
                                                      )
                                                    )
                                                  }}
                                                />
                                              </FormItem>
                                              <CloseCircleOutlined
                                                className="tagClose"
                                                onClick={() =>
                                                  handleDelItem(
                                                    index,
                                                    andex,
                                                    "FILL_BLANK"
                                                  )
                                                }
                                              />
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  <CloseCircleOutlined
                                    className="msgClose"
                                    onClick={() => handleDelMsg(index)}
                                  />
                                </div>
                              ))}
                            </div>
                          </FormItem>
                        </div>
                      </div>
                    }
                  </>
                )}
              </Form>
              <div style={{ width: "30%", margin: "0 auto", display: "flex" }}>
                {swichFlag ? (
                  <>
                    {stepCurrent == 1 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent - 1)
                        }}
                        style={{ marginRight: "20px" }}
                      >
                        上一步
                      </Button>
                    )}
                    {stepCurrent == 0 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent + 1)
                        }}
                      >
                        下一步
                      </Button>
                    )}
                    {stepCurrent == 1 && (
                      <Button type="primary" onClick={() => onSubmit()}>
                        保存
                      </Button>
                    )}
                  </>
                ) : (
                  <Button type="primary" onClick={() => onSubmit()}>
                    保存
                  </Button>
                )}
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <WibotMobilePreview
                title="课后作业"
                footer={
                  <>
                    <Button type="primary" shape="round">
                      提交
                    </Button>
                  </>
                }
              >
                <div className="cover">
                  {imageUrl && (
                    <FileHOC src={imageUrl}>
                      {(url) => <img src={url} className="header_img" />}
                    </FileHOC>
                  )}
                  <div className="header_box">
                    <Paragraph
                      className="header_title"
                      ellipsis={{ rows: 2, tooltip: false }}
                    >
                      {previewData.name}
                    </Paragraph>
                    <Paragraph
                      className="header_describe"
                      ellipsis={{ rows: 4, tooltip: false }}
                    >
                      {previewData.description}
                    </Paragraph>
                  </div>
                </div>
                <div className="body">
                  {msgList.map((item, index) => (
                    <div key={index} style={{ marginBottom: "10px" }}>
                      {item.type == "CHECKBOX" && (
                        <>
                          <div style={{ marginBottom: "10px" }}>
                            {index + 1}、{item.title ?? "题目名称"}
                            <div
                              className="question_radio"
                              style={{ background: "#facd91" }}
                            >
                              多选
                            </div>
                          </div>
                          <Checkbox.Group>
                            <Space direction="vertical">
                              {item.optionList.map((atem, andex) => (
                                <Checkbox value={`${andex + 1}`} key={andex}>
                                  {atem.name}
                                </Checkbox>
                              ))}
                            </Space>
                          </Checkbox.Group>
                        </>
                      )}
                      {item.type == "RADIO" && (
                        <>
                          <div style={{ marginBottom: "10px" }}>
                            {index + 1}、{item.title ?? "题目名称"}
                            <div className="question_radio">单选</div>
                          </div>
                          <Radio.Group defaultValue={null}>
                            <Space direction="vertical">
                              {item.optionList.map((atem, andex) => (
                                <Radio value={andex} key={andex}>
                                  {atem.name}
                                </Radio>
                              ))}
                            </Space>
                          </Radio.Group>
                        </>
                      )}
                      {item.type == "TEXTAREA" && (
                        <>
                          <div style={{ marginBottom: "10px" }}>
                            {index + 1}、{item.title ?? "题目名称"}
                            <div
                              className="question_radio"
                              style={{ background: "#ec808d" }}
                            >
                              问答
                            </div>
                          </div>
                          <TextArea
                            placeholder="请输入"
                            bordered={false}
                            autoSize={{ minRows: 2, maxRows: 7 }}
                          />
                        </>
                      )}
                      {item.type == "FILL_BLANK" && (
                        <>
                          <div style={{ marginBottom: "10px" }}>
                            {index + 1}、{item.title ?? "题目名称"}
                            <div
                              className="question_radio"
                              style={{
                                width: "48px",
                                background: "#eb5945",
                              }}
                            >
                              填空题
                            </div>
                          </div>
                          <TextArea
                            value={item.textContent}
                            placeholder="请输入"
                            bordered={false}
                            autoSize={{ minRows: 2, maxRows: 7 }}
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </WibotMobilePreview>
            </Col>
          </Row>
        </Card>
      </Spin>
      <SubjectModal params={subjectFormParams} />
    </div>
  )
}
export default withRouter(TestDesignForm)
