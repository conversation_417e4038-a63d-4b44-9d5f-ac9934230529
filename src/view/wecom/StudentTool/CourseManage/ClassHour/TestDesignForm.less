.TestDesignForm {
  background: #f0f2f5;

  .TestCard {
    &.ant-card {
      height: 100%;
      border: unset !important;
    }

    .submissionInfo {
      .btn-flex {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;

        button {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }

      .formBtn {
        .ant-col {
          label {
            &::after {
              content: "";
            }
          }
        }
      }

      .question {
        .question_one {
          display: flex;
          flex-direction: row;
          margin-top: 10px;

          .num {
            width: 22px;
            height: 22px;
            line-height: 22px;
            border-radius: 50%;
            background: #1989fa;
            text-align: center;
            display: block;
            color: #fff;
            margin-right: 10px;
            margin-top: 5px;
          }

          .tagClose {
            cursor: pointer;
            font-size: 20px;
            color: #d9d9d9;
            position: absolute;
            top: 6px;
            right: -45px;
          }

          .question_two_item {
            display: flex;
            flex-direction: row;
            // align-items: center;
            position: relative;
            margin-bottom: 10px;

            .flexItem {
              display: flex;
              flex-direction: row;
              align-items: center;
            }
          }
        }

        .msgItem {
          position: relative;

          .msgClose {
            position: absolute;
            top: 5px;
            right: 0px;
            cursor: pointer;
            font-size: 20px;
            color: #d9d9d9;

            &:hover {
              color: #1989fa;
            }
          }
        }
      }
    }
  }

  .question {
    .question_one {
      display: flex;
      flex-direction: row;
      margin-top: 10px;

      .num {
        width: 22px;
        height: 22px;
        line-height: 22px;
        border-radius: 50%;
        background: #1989fa;
        text-align: center;
        display: block;
        color: #fff;
        margin-right: 10px;
        margin-top: 5px;
      }

      .tagClose {
        cursor: pointer;
        font-size: 20px;
        color: #d9d9d9;
        position: absolute;
        top: 6px;
        right: 0px;
      }

      .question_two_item {
        display: flex;
        flex-direction: row;
        position: relative;

        .question_answer {
          width: 35px;
          height: 20px;
          text-align: center;
          line-height: 18px;
          box-sizing: border-box;
          color: #aaaaaa;
          margin-left: 10px;
          margin-top: 5px;
          border: 1px solid #aaa;
          cursor: pointer;

          &:hover,
          &.active {
            color: #07c160;
            border: 1px solid #07c160;
          }
        }
      }
    }
  }

  .textArea-mid {
    textarea {
      height: 100px !important;
    }
  }

  .question-header {
    display: flex;

    .tip {
      margin-top: 5px;
      margin-left: 10px;
    }
  }

  .question_radio {
    display: inline-block;
    background: #89c1f9;
    width: 35px;
    height: 20px;
    border-radius: 5px;
    text-align: center;
    line-height: 19px;
    box-sizing: border-box;
    color: #fff;
    margin-left: 5px;
  }

  .WibotMobilePreview-Container {
    .cover {
      position: relative;
      min-height: 180px;
      height: 180px;

      .header_img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .header_box {
        position: absolute;
        top: 40px;
        width: 100%;
        text-align: center;
        overflow: hidden;

        .header_title {
          font-size: 18px;
          font-weight: bold;
        }

        .header_describe {
          text-align: left;
          text-indent: 28px;
          white-space: normal;
          word-break: break-all;
        }
      }
    }

    .body {
      text-align: left;
      margin-top: 10px;
    }

    .ant-btn {
      width: 100px;
      margin: 0 auto;
      display: block;
    }
  }
}
