/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 10:30
 * @LastEditTime: 2023/08/31 15:02
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\ClassHour\comps\ClassFormModal\home.jsx
 * @Description: '创建课时'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Spin, Button, Input, Select } from 'antd';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { withRouter } from 'react-router-dom';
import { clearCache } from 'react-router-cache-route';
import './home.less';

const FormItem = Form.Item;

const ClassFormModal = (props) => {
  const { courseId } = props.params;
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        courseId,
        coursewareFlag: false,
        examFlag: false,
        ...formData
      };
      apiCall('/study/courseItem', 'POST', data).then((res) => {
        message.success('新增成功！');
        props.history.push({
          pathname: '/wecom/classHour/form',
          search: `?id=${res.id}&name=${res.name}`
        });
        clearCache();// 清空路由缓存
        onCancel();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={456}
      title="创建课时"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      className="ClassFormModal"
      footer={<div>
        <Button type="primary" onClick={onCancel}>
          取消
        </Button>
        <Button type="primary" onClick={onOk}>
          确认
        </Button>
      </div>}
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem label="课时名称" name="name" getValueFromEvent={(e) => removeInputEmpty(e)} rules={[{ required: true, message: '请输入课时名称' }]}>
            <Input placeholder="请输入课时名称（30字内）" maxLength={30} allowClear />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default withRouter(ClassFormModal);

