/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2022/12/02 11:36
 * @LastEditTime: 2024/10/22 09:35
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/ClassHour/details/home.jsx
 * @Description: '课时详情'
 */

import React, { useEffect, useState, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import { Spin, Button, Card, Row, Col, Space, Empty, Tabs, Form, DatePicker, Select } from 'antd';
import { apiCall } from 'common/utils';
import { getDay } from 'common/date';
import moment from 'moment';
import { qs2obj } from 'common/object';
import LearningRecord from './LearningRecord';
import WorkRecord from './WorkRecord';
import { Line } from '@ant-design/plots';
import './home.less';

const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ClassHourDetails = (props) => {
  const [id, setId] = useState(null);
  const [type, setType] = useState('EMPLOYEE');
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState('1');
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [chartsData, setChartsData] = useState(null);

  useEffect(() => {
    const { id, type } = qs2obj(props.location.search);
    if (id) {
      setType(type);
      setId(id);
      init(id);
    }
  }, []);


  const init = async (id) => {
    await fetchList({ detailId: id });
    await getTabsDataDetails({ detailId: id, tabIdx: '1' });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { detailId, time, tabIdx = '1' } = params;
    const data = {
      id: detailId || id,
      startDate: time && time.length > 0 ? time[0] : moment(getDay(-30)).format('YYYY-MM-DD'),
      endDate: time && time.length > 0 ? time[1] : moment(getDay(-1)).format('YYYY-MM-DD')
    };
    await apiCall('/activity/channel/detail_data', 'GET', data).then((res) => {
      setUserInfo(res);
      setChartsData(res.visitorChart.list)
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabsDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, detailId, tabIdx } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      channelIds: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    let apiUrl = '';
    switch (tabIdx) {
      case '1':
        apiUrl = '/activity/channel/data';
        break;
      case '2':
        apiUrl = '/info/infoResource';
        break;
      case '3':
        apiUrl = '/activity/dynamicCode/page';
        break;
      default:
        break;
    }
    apiCall(apiUrl, 'GET', data)
      .then((res) => {
        const { records, current, pages, size, total } = res;
        setDataSource(records ?? []);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
      case '0':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(value), 'YYYY-MM-DD')];
        fetchList({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(value)).format('YYYY-MM-DD')] });
        break;
      case '-1':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(value), 'YYYY-MM-DD')];
        fetchList({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(value)).format('YYYY-MM-DD')] });
        break;
      case '-7':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        fetchList({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
      case '-15':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        fetchList({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
      case '-30':
        time = [moment(getDay(value), 'YYYY-MM-DD'), moment(getDay(-1), 'YYYY-MM-DD')];
        fetchList({ time: [moment(getDay(value)).format('YYYY-MM-DD'), moment(getDay(-1)).format('YYYY-MM-DD')] });
        break;
    }
    if (value) {
      await formRef.current.setFieldsValue({
        time
      });
    }
  };

  const onChangeTime = async (date, dateString) => {
    fetchList({ time: date ? dateString : null });
    formRef.current.setFieldsValue({
      quickTime: null
    });
  };

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index);
    // getTabsDataDetails({ tabIdx: index, pagination: { current: 1, pageSize: 10 } });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, //倾斜角度
          offset: "20",
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5)
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <div className='ClassHourDetails'>
      <Spin spinning={loading}>
        <Card title="课时详情" extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} bordered={false} bodyStyle={{ display: 'none' }}></Card>
        <br />
        <Row>
          <Col span={24}>
            <Card title={<>基础信息</>}>
              {
                JSON.stringify(userInfo) != '{}' ? <Row className='info' gutter={16}>
                  <Col span={10}>
                    <div style={{ marginRight: '10px' }}>{userInfo.name || '课时名称'}&nbsp;<span className='text'>{100}人学习&nbsp;|&nbsp;课件&nbsp;|&nbsp;作业</span></div>
                    <div className='text'>课件名称：课件名称</div>
                    <div className='text'>作业名称：作业名称作业名称作业名称作业名称</div>
                    <div className='text'>题目数量：10题</div>
                    <Space wrap>更新：{userInfo.updateEmployeeName && <span>{userInfo.updateEmployeeName}&nbsp;&nbsp;</span>}{userInfo.updateTime}</Space>
                    <Space wrap>创建：{userInfo.createEmployeeName && <span>{userInfo.createEmployeeName}&nbsp;&nbsp;</span>}{userInfo.createTime}</Space>
                  </Col>
                  {/* <Col span={7}>
                    <div className='text' style={{ marginBottom: '10px' }}>课件封面：</div>
                    <Image
                      preview={false}
                      width={120}
                      height={100}
                      src="https://test.wizone.work/minio/imageproxy/0x0/wecom-marketing-test/338/0431c5f9-e3be-4e10-9b87-5c667f05ee7d.jpg"
                    />
                  </Col>
                  <Col span={7}>
                    <div className='text' style={{ marginBottom: '10px' }}>课件内容：</div>
                    <video controls poster="https://test.wizone.work/minio/imageproxy/0x0/wecom-marketing-test/338/e7bc2124-9342-403b-a3bd-634b4265b4e4.jpg" src="https://test.wizone.work/minio/wecom-marketing-test/338/d2fe159c-588d-4343-a5ca-3924a950df91.mp4" style={{ width: '160px', height: '100px', objectFit: 'cover' }} />
                  </Col> */}
                </Row> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Card bordered={false} title="数据趋势（学习人数）" >
              <Form layout={'inline'} ref={formRef}>
                <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                  <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                </FormItem>
                <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                  <Select
                    style={{ width: '200px' }}
                    options={[
                      {
                        label: '今天',
                        value: '0'
                      },
                      {
                        label: '昨天',
                        value: '-1'
                      },
                      {
                        label: '最近7天',
                        value: '-7'
                      },
                      {
                        label: '最近15天',
                        value: '-15'
                      },
                      {
                        label: '最近30天',
                        value: '-30'
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {chartsData && DemoLine(chartsData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
        <Card bordered={false} title="数据明细">
          <Tabs activeKey={tabsDetailsIndex} destroyInactiveTabPane onChange={onChangeTabsDataDetails}>
            <TabPane tab="学习记录" key="1">
              <LearningRecord />
            </TabPane>
            <TabPane tab="作业记录" key="2">
              <WorkRecord />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div >
  );
};

export default withRouter(ClassHourDetails);
