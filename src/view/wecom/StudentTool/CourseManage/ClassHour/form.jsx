/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 13:56
 * @LastEditTime: 2022/12/12 17:21
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\ClassHour\form.jsx
 * @Description: '编辑课时'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs, Button } from 'antd';
// 模块组件
import CoursewareForm from './CoursewareForm';
import TestDesign from './TestDesignForm';
import { qs2obj } from 'common/object';

const { TabPane } = Tabs;

const ClassHourForm = (props) => {
  const [tabsIndex, setTabsIndex] = useState('1');
  const [classHourName, setClassHourName] = useState('');

  useEffect(() => {
    const { name } = qs2obj(props.location.search);
    setClassHourName(name);
    // setTabsIndex(localStorage.getItem('tabsClassHourIndex') || '1');
  }, []);

  const onChangeTabs = (index) => {
    // localStorage.setItem('tabsClassHourIndex', index);
    setTabsIndex(index);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='ClassHourForm'>
      <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} title={`编辑课时（${classHourName})`} bordered={false} bodyStyle={{ display: 'none' }}></Card>
      <br />
      <div style={{ background: '#fff', padding: '24px 24px 0 24px' }}>
        <Tabs type="card" activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="课件设计" key="1">
            <CoursewareForm />
          </TabPane>
          <TabPane tab="作业设计" key="2">
            <TestDesign />
          </TabPane>
        </Tabs>
      </div>

    </div>
  );
};

export default ClassHourForm;
