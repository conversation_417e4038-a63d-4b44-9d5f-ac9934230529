/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 11:04
 * @LastEditTime: 2023/11/21 15:32
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/ClassHour/home.jsx
 * @Description: '课时管理'
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
  Typography,
  Select,
  TreeSelect,
  Image,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import { FormOutlined, SaveOutlined, MenuOutlined } from '@ant-design/icons';
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import ClassFormModal from './comps/ClassFormModal/home';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { qs2obj } from 'common/object';
import { clearCache } from 'react-router-cache-route';
import ListOperation from 'components/ListOperation/home';
import './home.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;
const { SHOW_PARENT } = TreeSelect;
const { Option } = Select;

const type = 'DraggableBodyRow';
const DraggableBodyRow = ({
  index,
  moveRow,
  className,
  style,
  ...restProps
}) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName:
          dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const ClassHour = (props) => {
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [classFormParams, seClassFormParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      dataIndex: 'sort',
      align: 'center',
      render: (value, record, index) => (
        <>
          <span style={{ marginRight: '10px' }}>{index + 1}</span>
          <MenuOutlined />
        </>
      ),
    },
    {
      title: '课时名称',
      width: '200px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <div>
          {record.isEdit ? (
            <Input
              value={value}
              onChange={(e) => {
                let newDataSource = dataSource;
                newDataSource[index].name = e.target.value.replace(
                  /^\s+|\s+$/g,
                  ''
                );
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入课程名称"
              allowClear
              style={{ width: '140px' }}
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
          {record.isEdit ? (
            <SaveOutlined
              className="save-icon"
              onClick={() => {
                const data = {
                  name: value,
                };
                apiCall(`/study/courseItem/update/${record.id}`, 'POST', data)
                  .then((res) => {
                    fetchList();
                    message.success('修改成功！');
                  })
                  .catch((err) => {
                    console.log(err);
                  })
                  .finally(() => { });
              }}
            />
          ) : (
            <FormOutlined
              className="edit-icon"
              onClick={() => {
                let newDataSource = dataSource;
                newDataSource[index].isEdit = true;
                setDataSource([...newDataSource]);
              }}
            />
          )}
        </div>
      ),
    },
    {
      title: '课件名称',
      width: '160px',
      dataIndex: 'title',
      key: 'title',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={record.coursewareSetting?.name || ''}>
          {record.coursewareSetting?.name || ''}
        </Tooltip>
      ),
    },
    {
      title: '课件封面',
      width: '160px',
      dataIndex: 'cover',
      key: 'cover',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) =>
        record.coursewareSetting && (
          <FileHOC src={record.coursewareSetting?.cover || 'error'}>
            {(url) => (
              <Image
                width={60}
                height={60}
                src={url}
                fallback="images/fallbackImg.png"
                style={{ objectFit: 'cover' }}
                preview
              />
            )}
          </FileHOC>
        ),
    },
    {
      title: '课件内容',
      width: '160px',
      dataIndex: 'coursewareSetting',
      key: 'coursewareSetting',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) =>
        record.coursewareSetting && (
          <FileHOC src={value?.videoFileId || value?.videoUrl || ''}>
            {(url) => (
              <video
                style={{ maxWidth: '160px', maxHeight: '100px' }}
                controls
                src={url}
              />
            )}
          </FileHOC>
        ),
    },

    {
      title: '是否有作业',
      width: '160px',
      dataIndex: 'examFlag',
      key: 'examFlag',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? '有' : '无'}>{value ? '有' : '无'}</Tooltip>
      ),
    },
    {
      title: '更新人/更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          // { onClick: () => handleDetails(record), name: "详情" },
          { onClick: () => handleCopy(record), name: "复制" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });

      const params = {
        list: data.map((item) => item.id),
        current: paginations.current,
        size: paginations.pageSize,
      };
      apiCall('/study/courseItem/sort', 'POST', params)
        .then((res) => {
          message.success('修改成功！');
          setDataSource(
            update(dataSource, {
              $splice: [
                [dragIndex, 1],
                [hoverIndex, 0, dragRow],
              ],
            })
          );
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource, paginations]
  );

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      fetchList({ courseId: id });
    }
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.updateTime;
      }
      const { courseId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        courseId: courseId ? courseId : id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/study/courseItem', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          courseItemSort(records, current, size);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 排序
  const courseItemSort = (data, current, size) => {
    const params = {
      list: data.map((item) => item.id),
      current,
      size,
    };
    apiCall('/study/courseItem/sort', 'POST', params)
      .then((res) => { })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    seClassFormParams({
      visible: true,
      courseId: id,
      title: '创建课时',
      onCancel: () => {
        seClassFormParams({ visible: false });
      },
    });
  };

  // 编辑
  const handleEdit = (record) => {
    const { id, name } = record;
    props.history.push({
      pathname: '/wecom/classHour/form',
      search: `?id=${id}&name=${name}`,
    });
  };

  // 复制
  const handleCopy = (record) => {
    setLoading(true);
    const { id, name } = record;
    apiCall(`/study/courseItem/copy/${id}`, 'POST')
      .then((res) => {
        props.history.push({
          pathname: '/wecom/classHour/form',
          search: `?id=${id}&name=${name}`,
        });
        clearCache(); // 清空路由缓存
        message.success('课时复制成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 详情
  const handleDetails = (record) => {
    const { id } = record;
    props.history.push({
      pathname: '/wecom/classHour/details',
      search: `?id=${id}`,
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除的课时名称为【${name}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/study/courseItem/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="ClassHour">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="课时名称" allowClear />
          </FormItem>
          <FormItem name="examFlag">
            <Select placeholder="是否有作业" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem
            name="createEmployeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="创建人" allowClear />
          </FormItem>
          <FormItem
            name="updateEmployeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="更新人" allowClear />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              创建课时
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <DndProvider backend={HTML5Backend}>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
            components={components}
            onRow={(record, index) => ({
              index,
              moveRow,
            })}
          />
        </DndProvider>
      </Card>
      <OperateModal params={operateParams} />
      <ClassFormModal params={classFormParams} />
    </div>
  );
};

export default withRouter(ClassHour);
