/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 10:30
 * @LastEditTime: 2023/06/05 17:31
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\comps\CourseFormModal\home.jsx
 * @Description: '新增课程'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import { Modal, Form, message, Spin, Button, Input, Select } from "antd";
import { apiCall } from "common/utils";
import { removeInputEmpty } from "common/regular";
import { clearCache } from "react-router-cache-route";
import "./home.less";

const FormItem = Form.Item;

const CourseFormModal = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [courseTypeOption, setCourseTypeOption] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getCourseTypeOption();
    }
  }, [props]);

  // 获取课程类型
  const getCourseTypeOption = (id) => {
    apiCall("/study/courseType", "GET")
      .then((res) => {
        setCourseTypeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        examFlag: false,
        ...formData,
      };
      apiCall("/study/course", "POST", data)
        .then((res) => {
          message.success("新增成功！");
          props.history.push({
            pathname: "/wecom/courseManage/course",
            search: `?id=${res.id}`,
          });
          // props.params?.onSubmit?.();
          clearCache(); // 清空路由缓存
          onCancel();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={456}
      title="新增课程"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      className="courseModal"
      footer={
        <div>
          <Button type="primary" onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" onClick={onOk}>
            确认
          </Button>
        </div>
      }
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            label="课程名称"
            name="name"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[{ required: true, message: "请输入课程名称" }]}
          >
            <Input
              placeholder="请输入课程名称（30字内）"
              maxLength={30}
              allowClear
            />
          </FormItem>
          <FormItem
            name="courseTypeId"
            label="课程类型"
            rules={[{ required: true, message: "请选择课程类型" }]}
          >
            <Select
              options={courseTypeOption}
              allowClear
              placeholder="请选择课程类型"
              fieldNames={{ label: "name", value: "id" }}
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default withRouter(CourseFormModal);
