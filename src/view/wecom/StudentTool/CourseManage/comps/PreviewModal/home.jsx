/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/07 14:08
 * @LastEditTime: 2022/12/15 10:35
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\comps\PreviewModal\home.jsx
 * @Description: '预览'
 */
import React, { useEffect, memo, } from 'react';
import { Modal, Card, Empty, Collapse } from 'antd';

import './home.less';

const { Panel } = Collapse;

const PreviewModal = (props) => {
  const { visible, courseData, onCancel } = props;

  useEffect(() => {

  }, [visible]);

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <Modal
      title="课程安排"
      width={400}
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      centered
      className="PreviewModal"
    >
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        {
          (courseData && (courseData.courseItemVOList?.length > 0 || courseData.examVO)) ? <div>
            {
              courseData.courseItemVOList?.length > 0 && <Collapse defaultActiveKey={0}>
                {
                  courseData.courseItemVOList?.map((item, index) => (
                    <Panel header={item.name} key={index}>
                      <div className='text'>{(item.coursewareSetting?.name && item.examVO?.name) && '01.'}{item.coursewareSetting?.name}</div>
                      <div className='text'>{(item.coursewareSetting?.name && item.examVO?.name) && '02.'}{item.examVO?.name}</div>
                    </Panel>
                  ))
                }
              </Collapse>
            }
            {
              courseData.examVO && <Collapse defaultActiveKey={0}>
                <Panel header="考试" key={0}>
                  <div>{courseData.examVO.name}</div>
                </Panel>
              </Collapse>
            }
          </div>
            : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        }
      </Card>
    </Modal>
  );
};

export default memo(PreviewModal);
