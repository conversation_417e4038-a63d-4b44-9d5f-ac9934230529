/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2022/12/01 18:22
 * @LastEditTime: 2024/10/22 09:35
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/comps/SubjectModal/home.jsx
 * @Description: '选择试题'
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Button,
  Form,
  Input,
  Pagination,
  Spin,
  Card,
  Typography,
  Tabs,
  Space,
  TreeSelect,
  Row,
  Col,
  Empty,
  Radio,
  Checkbox,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';

import './home.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Paragraph } = Typography;
const { TextArea } = Input;
const { SHOW_PARENT } = TreeSelect;

const SubjectModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [typeOptions, setTypeOptions] = useState([]);
  const [dataSource, setDataSource] = useState([
    {
      type: 'RADIO',
      optionList: [
        {
          isAnswer: true,
        },
        {
          isAnswer: false,
        },
        {
          isAnswer: false,
        },
      ],
    },
  ]);
  const [select, setSelect] = useState({});
  const [tabType, setTabType] = useState('RADIO');

  useEffect(async () => {
    const { visible } = props.params;
    if (visible) {
      setVisible(visible);
      let timer = setTimeout(async () => {
        fetchList();
        clearTimeout(timer);
      }, 300);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      setDataSource([]);
      const { type, pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: type || tabType,
        ...formData,
      };
      const apiUrl = '/study/examItem';
      apiCall(apiUrl, 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: [8, 20, 50, 100],
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
          setDataSource([]);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTabs = (type) => {
    fetchList({ type, pagination: { current: 1, pageSize: 8 } });
    setTabType(type);
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    setSelect({ ...dataSource[index] });
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setTypeOptions([]);
    setDataSource([]);
    setSelect({});
    setTabType('RADIO');
    formRef.current.resetFields();
    props.params?.onSubmit?.(select);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setTypeOptions([]);
    setDataSource([]);
    setSelect({});
    setTabType('RADIO');
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择试题"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="SubjectModal"
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="keyword"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="题目名称、选项名称" allowClear />
          </FormItem>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          <Tabs
            accessKey={tabType}
            destroyInactiveTabPane
            onChange={onChangeTabs}
          >
            <TabPane tab="单选题" key="RADIO">
              {dataSource.length > 0 ? (
                dataSource.map((item, index) => (
                  <div
                    className="subject-card"
                    key={index}
                    onClick={() => handleSelectScript(index)}
                  >
                    <Paragraph
                      strong
                      ellipsis={{ rows: 1 }}
                      style={{ fontSize: '14px', padding: '5px 0px 5px 5px' }}
                    >
                      {item.formItem?.title || '题目名称'}
                    </Paragraph>
                    <div className="subject-content">
                      {item.formItem?.optionList?.map((atem, andex) => (
                        <div className="subject_item" key={andex}>
                          <Radio defaultChecked={false} disabled></Radio>
                          <div className="item_name">{atem.name}</div>
                          {atem.answer && (
                            <div className="question_answer active">答案</div>
                          )}
                        </div>
                      ))}
                    </div>
                    {item.id == select.id ? (
                      <div className="mask">
                        <CheckCircleTwoTone />
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </TabPane>
            <TabPane tab="多选题" key="CHECKBOX">
              {dataSource.length > 0 ? (
                dataSource.map((item, index) => (
                  <div
                    className="subject-card"
                    key={index}
                    onClick={() => handleSelectScript(index)}
                  >
                    <Paragraph
                      strong
                      ellipsis={{ rows: 1 }}
                      style={{ fontSize: '14px', padding: '5px 0px 5px 5px' }}
                    >
                      {item.formItem?.title || '题目名称'}
                    </Paragraph>
                    <div className="subject-content">
                      {item.formItem?.optionList?.map((atem, andex) => (
                        <div className="subject_item" key={andex}>
                          <Checkbox
                            defaultChecked={false}
                            disabled
                            style={{ marginRight: '10px' }}
                          ></Checkbox>
                          <div className="item_name">{atem.name}</div>
                          {atem.answer && (
                            <div className="question_answer active">答案</div>
                          )}
                        </div>
                      ))}
                    </div>
                    {item.id == select.id ? (
                      <div className="mask">
                        <CheckCircleTwoTone />
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </TabPane>
            <TabPane tab="问答题" key="TEXTAREA">
              {dataSource.length > 0 ? (
                dataSource.map((item, index) => (
                  <div
                    className="subject-card"
                    key={index}
                    onClick={() => handleSelectScript(index)}
                  >
                    <Paragraph
                      strong
                      ellipsis={{ rows: 1 }}
                      style={{ fontSize: '14px', padding: '5px 0px 5px 5px' }}
                    >
                      {item.formItem?.title || '题目名称'}
                    </Paragraph>
                    <div className="subject-content">
                      <TextArea
                        placeholder="请输入（300字以内）"
                        disabled
                        autoSize={{ minRows: 2, maxRows: 7 }}
                        className="textArea"
                      />
                    </div>
                    {item.id == select.id ? (
                      <div className="mask">
                        <CheckCircleTwoTone />
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </TabPane>
            <TabPane tab="填空题" key="FILL_BLANK">
              {dataSource.length > 0 ? (
                dataSource.map((item, index) => (
                  <div
                    className="subject-card"
                    key={index}
                    onClick={() => handleSelectScript(index)}
                  >
                    <Paragraph
                      strong
                      ellipsis={{ rows: 1 }}
                      style={{ fontSize: '14px', padding: '5px 0px 5px 5px' }}
                    >
                      {item.formItem?.title || '题目名称'}
                    </Paragraph>
                    <div className="subject-content">
                      <TextArea
                        placeholder="请输入（300字以内）"
                        value={item.formItem?.textContent}
                        disabled
                        autoSize={{ minRows: 2, maxRows: 7 }}
                        className="textArea"
                      />
                      <Space direction="vertical" style={{ marginTop: '10px' }}>
                        {item.formItem?.correctAnswer?.map((atem, andex) => (
                          <div key={andex} className="question_radio_flex">
                            <span>正确答案：</span>
                            <Paragraph
                              ellipsis={{ rows: 1, tooltip: false }}
                              style={{
                                flex: 1,
                                maxWidth: '120px',
                                margin: '0 0 0 5px',
                              }}
                            >
                              {atem}
                            </Paragraph>
                          </div>
                        ))}
                      </Space>
                    </div>
                    {item.id == select.id ? (
                      <div className="mask">
                        <CheckCircleTwoTone />
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </TabPane>
          </Tabs>
          <Pagination {...paginations} onChange={onChangePagination} />
        </Card>
      </Spin>
    </Modal>
  );
};

export default SubjectModal;
