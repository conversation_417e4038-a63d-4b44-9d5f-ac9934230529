.SubjectModal {
  .ant-card {
    .subject-card {
      position: relative;
      cursor: pointer;

      .ant-typography {
        line-height: unset;
        min-height: unset;
        margin-bottom: 0px;
      }

      width: 220px;
      height: auto;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin: 0 34px 10px;
      overflow: hidden;
      display: inline-block;
      box-sizing: border-box;

      .subject-content {
        border-top: 1px solid #ccc;
        padding: 5px;
        height: 95px;
        overflow: auto;
        box-sizing: border-box;

        .subject_item {
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-bottom: 5px;

          .item_name {
            width: 112px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .question_answer {
          width: 35px;
          height: 20px;
          font-size: 12px;
          text-align: center;
          line-height: 18px;
          box-sizing: border-box;
          color: #AAAAAA;
          margin-left: 10px;
          border: 1px solid #aaa;
          cursor: pointer;

          &:hover,
          &.active {
            color: #07c160;
            border: 1px solid #07c160;
          }
        }

        .textArea {
          pointer-events: none;
          height: 80px !important;
        }
        .question_radio_flex{
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 14px;
        }
      }
    }

    .link-card {
      margin: 0 34px 10px;
      min-height: 112px;
      cursor: pointer;
    }

    .picture-card {
      width: 150px;
      height: 228px;
      position: relative;
      display: inline-block;
      margin: 0 36px 10px;
      padding: 10px;
      border: 1px solid #ccc;
      box-shadow: 1px 2px 3px 0px #ccc;
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      vertical-align: middle;
      overflow: hidden;

      div {
        margin: 0;
      }

      video {
        width: 100%;
        height: 170px;
      }

      .ant-image {
        width: 100%;
        height: 170px;
        margin-bottom: 5px;

        .ant-image-img {
          height: 100%;
          object-fit: contain;
        }
      }
    }

    &.select-item {
      overflow: hidden;
    }

    .mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1000;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      -webkit-overflow-scrolling: touch;

      .anticon {
        font-size: 40px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .ant-typography {
      font-size: 12px;
      line-height: 18px;
      min-height: 36px;
    }

    .ant-pagination {
      text-align: right;
    }

    .applet-card {
      position: relative;
      width: 185px;
      height: auto;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin: 0 34px 10px;
      overflow: hidden;
      display: inline-block;
      box-sizing: border-box;

      .applet-card_top {
        padding: 5px 10px 0px 10px;

        .ant-image {
          margin: 5px 0;
        }
      }

      .applet-card_bottom {
        display: flex;
        align-items: center;
        font-size: 12px;
        border-top: 1px solid #f2f2f2;
        padding: 0 10px;
        line-height: 25px;

        img {
          width: 13px;
          height: 13px;
        }
      }

      .noMargin {
        margin-bottom: 0px;
      }
    }
  }
}