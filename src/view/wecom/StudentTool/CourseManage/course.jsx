/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/01 10:56
 * @LastEditTime: 2023/03/03 10:08
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\course.jsx
 * @Description: '编辑课程'
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs, Button } from 'antd';
// 模块组件
import ClassHour from './ClassHour/home';
import TestDesign from './TestDesign/home';

const { TabPane } = Tabs;

const Course = (props) => {
  const [tabsIndex, setTabsIndex] = useState('1');

  useEffect(() => {
    // setTabsIndex(localStorage.getItem('tabsCourseIndex') || '1');
  }, []);

  const onChangeTabs = (index) => {
    // localStorage.setItem('tabsCourseIndex', index);
    setTabsIndex(index);
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  return (
    <div className='Course'>
      <Card extra={<Button type="primary" onClick={() => handleGoBack()}>返回</Button>} title="编辑课程" bordered={false} bodyStyle={{ display: 'none' }}></Card>
      <br />
      <Card bordered={false} >
        <Tabs activeKey={tabsIndex} destroyInactiveTabPane onChange={onChangeTabs} >
          <TabPane tab="课时管理" key="1">
            <ClassHour />
          </TabPane>
          <TabPane tab="考试设计" key="2">
            <TestDesign />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Course;
