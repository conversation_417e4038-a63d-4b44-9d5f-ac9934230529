/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/02 10:46
 * @LastEditTime: 2023/06/08 11:47
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\CourseManage\details\LearningRecord.jsx
 * @Description: '学习记录'
 */

import React, { useEffect, useRef, useState } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const TabDetailsOne = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学员名称",
      width: "220px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? "#07c160" : "#f59a23" }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: "学习营名称",
      width: "160px",
      dataIndex: "createEmployeeName",
      key: "createEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "学习进度",
      width: "160px",
      dataIndex: "createEmployeeName",
      key: "createEmployeeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "开始学习时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "最近学习时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "学习完成时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];

  useEffect(() => {
    // const { id } = qs2obj(props.location.search);
    // fetchList({ dynamicCodeId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.minCreateTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxCreateTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { dynamicCodeId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        dynamicCodeId: dynamicCodeId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/dynamicCodeLog", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsOne">
      <Form layout={"inline"} ref={formRef}>
        <FormItem
          name="fieldName"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="学员名称" allowClear />
        </FormItem>
        <FormItem
          name="fieldName"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="学习营名称" allowClear />
        </FormItem>
        <FormItem name="time" label="学习完成时间">
          <RangePicker />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default TabDetailsOne;
