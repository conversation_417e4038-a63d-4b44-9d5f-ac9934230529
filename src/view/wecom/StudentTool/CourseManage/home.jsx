/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 19:40
 * @LastEditTime: 2023/11/21 15:31
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/CourseManage/home.jsx
 * @Description: '课程管理'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
  Select,
} from "antd";
import FilterBar from "components/FilterBar/FilterBar";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { removeInputEmpty } from "common/regular";
import { FormOutlined, SaveOutlined } from "@ant-design/icons";
import moment from "moment";
import OperateModal from "components/Modal/OperateModal/index";
import TypeFormModal from "./comps/TypeFormModal/home";
import CourseFormModal from "./comps/CourseFormModal/home";
import PreviewModal from "./comps/PreviewModal/home";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import { clearCache } from "react-router-cache-route";
import ListOperation from 'components/ListOperation/home';
import "./home.less";
import {usePageCacheLifeCycle} from "common/hooks";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;

const CourseManage = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [courseTypeOption, setCourseTypeOption] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [courseFormParams, setCourseFormParams] = useState({ visible: false });
  const [previewParams, setPreviewParams] = useState({ visible: false });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "课程名称",
      width: "200px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <div>
          {record.isEdit ? (
            <Input
              value={value}
              onChange={(e) => {
                let newDataSource = dataSource;
                newDataSource[index].name = e.target.value.replace(
                  /^\s+|\s+$/g,
                  ""
                );
                setDataSource([...newDataSource]);
              }}
              placeholder="请输入课程名称"
              allowClear
              style={{ width: "140px" }}
            />
          ) : (
            <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
          )}
          {record.isEdit ? (
            <SaveOutlined
              className="save-icon"
              onClick={() => {
                const data = {
                  name: value,
                };
                apiCall(`/study/course/update/${record.id}`, "POST", data)
                  .then((res) => {
                    fetchList();
                    message.success("修改成功！");
                    //    let newDataSource = dataSource;
                    // newDataSource[index].isEdit = false;
                    // setDataSource([...newDataSource]);
                  })
                  .catch((err) => {
                    console.log(err);

                  })
                  .finally(() => { });
              }}
            />
          ) : (
            <FormOutlined
              className="edit-icon"
              onClick={() => {
                let newDataSource = dataSource;
                newDataSource[index].isEdit = true;
                setDataSource([...newDataSource]);
              }}
            />
          )}
        </div>
      ),
    },
    {
      title: "课程类型",
      width: "200px",
      dataIndex: "courseTypeId",
      key: "courseTypeId",
      align: "center",
      render: (value, record, index) => (
        <div>
          {record.isEdit ? (
            <Select
              style={{ width: "140px" }}
              fieldNames={{ label: "name", value: "id" }}
              placeholder="课程类型"
              value={value}
              allowClear
              options={courseTypeOption}
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
              onChange={(val, option) => {
                let newDataSource = dataSource;
                if (val) {
                  newDataSource[index].courseTypeName = option.name;
                  newDataSource[index].courseTypeId = option.id;
                } else {
                  newDataSource[index].courseTypeName = '';
                  newDataSource[index].courseTypeId = '';
                }
                setDataSource([...newDataSource]);
              }}
            />
          ) : (
            <Tooltip placement="topLeft" title={record.courseTypeName}>
              {record.courseTypeName}
            </Tooltip>
          )}
          {record.isEdit ? (
            <SaveOutlined
              className="save-icon"
              onClick={() => {
                const data = {
                  courseTypeId: value,
                };
                apiCall(`/study/course/update/${record.id}`, "POST", data)
                  .then((res) => {
                    fetchList();
                    message.success("修改成功！");
                  })
                  .catch((err) => {
                    console.log(err);

                  })
                  .finally(() => { });
              }}
            />
          ) : (
            <FormOutlined
              className="edit-icon"
              onClick={() => {
                let newDataSource = dataSource;
                newDataSource[index].isEdit = true;
                setDataSource([...newDataSource]);
              }}
            />
          )}
        </div>
      ),
    },
    {
      title: "课程预览",
      width: "120px",
      align: "center",
      render: (value, record, index) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: "课时数量",
      width: "160px",
      dataIndex: "courseItemCount",
      key: "courseItemCount",
      align: "center",
    },
    {
      title: "是否有考试",
      width: "160px",
      dataIndex: "examFlag",
      key: "examFlag",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value ? "是" : "否"}>{value ? "是" : "否"}</Tooltip>
      ),
    },
    {
      title: "更新人/更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          // { onClick: () => handleDetails(record), name: "详情" },
          { onClick: () => handleCopy(record), name: "复制" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getCourseTypeOption();
    fetchList();
  }, []);

  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    }
  })

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.updateTime;
      }
      formData.createDeptIdList = formData.createDeptIdList?.join(",") || null;
      formData.updateDeptIdList = formData.updateDeptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/study/course", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 获取课程类型
  const getCourseTypeOption = (id) => {
    apiCall("/study/courseType", "GET")
      .then((res) => {
        setCourseTypeOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 课程类型
  const handleType = () => {
    setTypeFormParams({
      visible: true,
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getCourseTypeOption();
      },
    });
  };

  // 新增
  const handleAdd = () => {
    setCourseFormParams({
      visible: true,
      title: "新增课程",
      onCancel: () => {
        setCourseFormParams({ visible: false });
      },
    });
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/courseManage/course",
      search: `?id=${id}`,
    });
  };

  // 复制
  const handleCopy = (record) => {
    setLoading(true);
    const { id } = record;
    apiCall(`/study/course/copy/${id}`, "POST")
      .then((res) => {
        props.history.push({
          pathname: "/wecom/courseManage/course",
          search: `?id=${res[0]}`,
        });
        clearCache(); // 清空路由缓存
        message.success("课程复制成功！");
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handlePreview = (record) => {
    setPreviewParams({
      visible: true,
      courseData: record.courseAllVO,
      onCancel: () => {
        setPreviewParams({ visible: false });
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除的课程名称为【${name}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/study/course/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="CourseManage">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="课程名称" allowClear />
          </FormItem>
          <FormItem name="examFlag">
            <Select placeholder="是否有考试" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="courseTypeId">
            <Select
              placeholder="课程类型"
              fieldNames={{ label: "name", value: "id" }}
              options={courseTypeOption}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem
            name="updateDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="更新人" />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleType()}>
              课程类型
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              新增课程
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <div>课程必须包含课时或考试，否则课程无法使用</div>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <TypeFormModal params={typeFormParams} />
      <CourseFormModal params={courseFormParams} />
      <PreviewModal {...previewParams} />
    </div>
  );
};

export default withRouter(CourseManage);
