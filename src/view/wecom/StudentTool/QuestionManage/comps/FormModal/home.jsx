/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/23 11:09
 * @LastEditTime: 2023/08/25 11:22
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\QuestionManage\comps\FormModal\home.jsx
 * @Description: '新增编辑题目'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Spin, Select, Input, Button } from 'antd';
import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import './home.less';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

const FormModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [id, setId] = useState(null);
  const [examList, setExamList] = useState([]);
  const [examType, setExamType] = useState('RADIO');
  const [gapAnswers, setGapAnswers] = useState([]);

  useEffect(() => {
    const { visible, id, examData = {} } = props.params;
    if (visible) {
      setVisible(true);
      setId(id);
      if (id) {
        setExamType(examData.type);
        let timer = setTimeout(() => {
          formRef.current.setFieldsValue({
            ...examData,
          });
          setExamList(examData?.formItem?.optionList || []);
          setGapAnswers(examData?.formItem?.correctAnswer || []);
          clearTimeout(timer);
        }, 100);
      }
    }
  }, [props.params.visible]);

  // 切换题目类型
  const handleExamType = (val) => {
    setExamType(val);
    const data = JSON.parse(JSON.stringify(examList));
    data.forEach((item, index) => {
      item.answer = false;
    });
    setExamList(data);
  };

  // 添加题目选项
  const handleAddMsg = () => {
    const data = JSON.parse(JSON.stringify(examList));
    const fieldData = formRef.current.getFieldValue();
    fieldData.formItem = fieldData.formItem || {};
    data.push({
      answer: false,
    });
    fieldData.formItem.optionList = data;
    formRef.current.setFieldsValue({
      ...fieldData,
    });
    setExamList(JSON.parse(JSON.stringify(data)));
  };

  // 选择答案
  const handleAnswer = (index) => {
    const data = JSON.parse(JSON.stringify(examList));
    if (examType == 'RADIO') {
      data.forEach((a, i) => {
        if (i == index) {
          a.answer = true;
        } else {
          a.answer = false;
        }
      });
    } else {
      data.forEach((a, i) => {
        if (i == index) {
          a.answer = !a.answer;
        }
      });
    }
    setExamList(JSON.parse(JSON.stringify(data)));
  };

  // 题目选项改变
  const handleChangeOption = (e, index) => {
    const data = JSON.parse(JSON.stringify(examList));
    data[index].name = e.target.value;
    setExamList(JSON.parse(JSON.stringify(data)));
  };

  // 删除题目选项
  const handleDelItem = (index) => {
    const data = JSON.parse(JSON.stringify(examList));
    const fieldData = formRef.current.getFieldValue(['formItem']);
    fieldData.optionList.splice(index, 1);
    data.splice(index, 1);
    setExamList(JSON.parse(JSON.stringify(data)));
    formRef.current.setFieldsValue({
      formItem: fieldData,
    });
  };

  // 插入填空
  const handleAddContent = async () => {
    const insertHTML = '#________#';
    const inputIndex = document.getElementById('msgTextInput'); // 获取dom
    const startPos = inputIndex.selectionStart;
    const endPos = inputIndex.selectionEnd;
    const gapField =
      formRef.current.getFieldValue(['formItem', 'textContent']) ?? '';
    if (startPos === undefined || endPos === undefined) {
      return;
    }
    const text =
      gapField.substring(0, startPos) + insertHTML + gapField.substring(endPos);
    formRef.current.setFieldValue(['formItem', 'textContent'], text);
    // formRef.current.setFieldsValue({
    //   gapContent: text,
    // });
    const data = JSON.parse(JSON.stringify(gapAnswers));
    const fieldData =
      formRef.current.getFieldValue(['formItem', 'correctAnswer']) ?? [];
    data.push('');
    fieldData.push('');
    formRef.current.setFieldValue(['formItem', 'correctAnswer'], fieldData);
    setGapAnswers(data);
    // react-关键在于给任意一个方法同步
    await inputIndex.focus();
    await inputIndex.setSelectionRange(
      endPos + insertHTML.length,
      endPos + insertHTML.length
    );
  };
  // 删除题目正确答案
  const handleGapDelItem = (index) => {
    const data = JSON.parse(JSON.stringify(gapAnswers));
    const fieldData = formRef.current.getFieldValue([
      'formItem',
      'correctAnswer',
    ]);
    console.log(data, fieldData, 'fieldDatafieldData');
    fieldData.splice(index, 1);
    data.splice(index, 1);
    setGapAnswers(JSON.parse(JSON.stringify(data)));
    formRef.current.setFieldValue(['formItem', 'correctAnswer'], fieldData);
  };

  // 统计符号数量
  function sum (str, a) {
    let b = str.indexOf(a);
    let num = 0;
    while (b !== -1) {
      num++;
      b = str.indexOf(a, b + 1);
    }
    return num;
  }

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      if (
        (formData.type == 'RADIO' || formData.type == 'CHECKBOX') &&
        !examList.length > 0
      ) {
        message.error('请添加选项内容！');
        return false;
      }
      if (
        (formData.type == 'RADIO' || formData.type == 'CHECKBOX') &&
        examList.length > 0 &&
        !examList.some((item) => item.answer)
      ) {
        message.error('请选择选项答案！');
        return false;
      }

      const gapContent = formData.formItem.textContent;
      if (
        formData.type == 'FILL_BLANK' &&
        (!gapContent || gapContent.indexOf('#________#') <= -1)
      ) {
        message.error('请插入填空！');
        return false;
      }
      if (
        formData.type == 'FILL_BLANK' &&
        sum(gapContent, '#________#') != gapAnswers.length
      ) {
        message.error('插入填空数需与正确答案数一致！');
        return false;
      }
      if (formData.type == 'RADIO' || formData.type == 'CHECKBOX') {
        formData.formItem.optionList.forEach((item, index) => {
          item.answer = examList[index].answer;
          item.seqNo = index;
        });
      } else if (formData.type == 'TEXTAREA') {
        formData.formItem.textContent = '';
      }
      formData.formItem.type = formData.type;
      setConfirmLoading(true);
      const data = {
        id: id ?? null,
        ...formData,
      };
      apiCall('/study/examItem', 'POST', data)
        .then((res) => {
          message.success(id ? '编辑成功！' : '新增成功！');
          onCancel();
          props.params?.onSubmit?.();
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setId(null);
    setExamList([]);
    setExamType('RADIO');
    setGapAnswers([]);
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={500}
      title={id ? '编辑题目' : '新增题目'}
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      className="QuestionManageModal"
    >
      <Spin spinning={loading}>
        <Form ref={formRef}>
          <FormItem
            name={['formItem', 'title']}
            label="题目名称"
            getValueFromEvent={(e) => removeInputEmpty(e)}
            rules={[
              {
                required: true,
                message: '请输入题目名称',
              },
            ]}
          >
            <Input placeholder="请输入题目名称" allowClear maxLength={150} />
          </FormItem>
          <FormItem
            name="type"
            label="题目类型"
            rules={[
              {
                required: true,
                message: '请选择题目类型',
              },
            ]}
            initialValue={'RADIO'}
          >
            <Select
              placeholder="题目类型"
              allowClear
              onChange={(val) => {
                handleExamType(val);
              }}
            >
              <Option value="RADIO">单选</Option>
              <Option value="CHECKBOX">多选</Option>
              <Option value="TEXTAREA">问答</Option>
              <Option value="FILL_BLANK">填空题</Option>
            </Select>
          </FormItem>
          {examType == 'TEXTAREA' ? (
            <FormItem
              name={['formItem', 'text']}
              label="问答示例"
              required
            >
              <TextArea
                placeholder="请输入（300字以内）"
                disabled
                autoSize={{ minRows: 2, maxRows: 7 }}
                className="textArea-mid"
              />
            </FormItem>
          ) : (
            <FormItem label="选项内容" required>
              {examType == 'FILL_BLANK' ? (
                <div>
                  <FormItem
                    name={['formItem', 'textContent']}
                    // rules={[{ required: true, message: '请输入继承话术（300字）' }]}
                    extra={
                      <div>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddContent()}
                        >
                          插入填空
                        </Button>
                        <div>
                          注意：请不要在插入填空符“#____#”里输入内容或删除符号，否则该填空符无法生效
                        </div>
                      </div>
                    }
                  >
                    <TextArea
                      id="msgTextInput"
                      // value={scriptText}
                      allowClear
                      // maxLength={300}
                      autoSize={{ minRows: 6, maxRows: 10 }}
                      onChange={(e) => {
                        const value = e.target.value;
                        const newValue = value.replace(/\n/g, '');
                        formRef.current.setFieldValue(
                          ['formItem', 'textContent'],
                          newValue
                        );
                      }}
                    />
                  </FormItem>
                  {gapAnswers.map((item, index) => (
                    <div key={index} className="questionItem">
                      <FormItem
                        name={['formItem', 'correctAnswer', index]}
                        style={{
                          display: 'inline-block',
                          marginBottom: '0px',
                        }}
                        // label="正确答案"
                        rules={[{ required: true, message: '请输入正确答案' }]}
                      >
                        <Input
                          placeholder="请输入填空选项正确答案"
                          maxLength={150}
                          style={{ width: '280px' }}
                          allowClear
                        />
                      </FormItem>
                      <CloseCircleOutlined
                        className="tagClose"
                        onClick={() => handleGapDelItem(index)}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => handleAddMsg()}
                  >
                    选项
                  </Button>
                  {examList.length > 0 &&
                    examList.map((item, index) => (
                      <div key={index} className="questionItem">
                        <FormItem
                          name={['formItem', 'optionList', index, 'name']}
                          style={{
                            display: 'inline-block',
                            marginBottom: '0px',
                          }}
                          rules={[
                            { required: true, message: '请输入选项名称' },
                          ]}
                        >
                          <Input
                            placeholder="请输入选项名称"
                            maxLength={150}
                            style={{ width: '280px' }}
                            allowClear
                            onChange={(e) => {
                              handleChangeOption(e, index);
                            }}
                          />
                        </FormItem>
                        <div
                          className={`question_answer ${item.answer && 'active'
                            }`}
                          onClick={() => {
                            handleAnswer(index);
                          }}
                        >
                          答案
                        </div>
                        <CloseCircleOutlined
                          className="tagClose"
                          onClick={() => handleDelItem(index)}
                        />
                      </div>
                    ))}
                </div>
              )}
            </FormItem>
          )}
        </Form>
      </Spin>
    </Modal>
  );
};

export default FormModal;
