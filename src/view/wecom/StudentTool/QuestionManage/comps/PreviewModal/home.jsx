/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/07 14:08
 * @LastEditTime: 2023/08/23 10:19
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\QuestionManage\comps\PreviewModal\home.jsx
 * @Description: '预览弹窗'
 */
import React, { useEffect, memo } from 'react';
import { Modal, Card, Radio, Space, Typography, Checkbox, Input } from 'antd';

import './home.less';

const { Paragraph } = Typography;
const { TextArea } = Input;

const PreviewModal = (props) => {
  const { visible, formItem = {}, onCancel } = props;
  // const saveExamDTO = {
  //   examType: "TEXTAREA",
  //   examList: [
  //     {
  //       name: "选项名称选项名称1",
  //       answer: true,
  //     },
  //     {
  //       name: "选项名称选项名称2",
  //       answer: true,
  //     },
  //     {
  //       name: "选项名称选项名称3",
  //       answer: false,
  //     },
  //   ],
  // };

  useEffect(() => {}, [visible]);

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <Modal
      title="题目预览"
      width={400}
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      centered
      className="PreviewModal"
    >
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        {formItem.type == 'RADIO' && (
          <div>
            <div style={{ marginBottom: '10px' }}>
              <div className="question_radio">单选</div>
              {formItem.title}
            </div>
            <Radio.Group defaultValue={null}>
              <Space direction="vertical">
                {formItem.formItem?.optionList?.map((item, index) => (
                  <div key={index} className="question_radio_flex">
                    <Radio value={index} disabled></Radio>
                    <Paragraph
                      ellipsis={{ rows: 1, tooltip: false }}
                      style={{ maxWidth: '280px', marginBottom: '0px' }}
                    >
                      {item.name}
                    </Paragraph>
                    {item.answer && (
                      <div className="question_answer active">答案</div>
                    )}
                  </div>
                ))}
              </Space>
            </Radio.Group>
          </div>
        )}
        {formItem.type == 'CHECKBOX' && (
          <div>
            <div style={{ marginBottom: '10px' }}>
              <div className="question_radio" style={{ background: '#facd91' }}>
                多选
              </div>
              {formItem.title}
            </div>
            <Radio.Group defaultValue={null}>
              <Space direction="vertical">
                {formItem.formItem?.optionList?.map((item, index) => (
                  <div key={index} className="question_radio_flex">
                    <Checkbox value={index} disabled></Checkbox>
                    <Paragraph
                      ellipsis={{ rows: 1, tooltip: false }}
                      style={{ maxWidth: '280px', margin: '0 0 0 5px' }}
                    >
                      {item.name}
                    </Paragraph>
                    {item.answer && (
                      <div className="question_answer active">答案</div>
                    )}
                  </div>
                ))}
              </Space>
            </Radio.Group>
          </div>
        )}

        {formItem.type == 'TEXTAREA' && (
          <div>
            <div style={{ marginBottom: '10px' }}>
              <div className="question_radio" style={{ background: '#ec808d' }}>
                问答
              </div>
              {formItem.title}
            </div>
            <TextArea
              placeholder="请输入（300字以内）"
              disabled
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
          </div>
        )}

        {formItem.type == 'FILL_BLANK' && (
          <div>
            <div style={{ marginBottom: '10px' }}>
              <div
                className="question_radio"
                style={{ width: '48px', background: '#eb5945' }}
              >
                填空题
              </div>
              {formItem.title}
            </div>
            <TextArea
              placeholder="请输入（300字以内）"
              value={formItem.formItem.textContent}
              disabled
              autoSize={{ minRows: 2, maxRows: 7 }}
              className="textArea-mid"
            />
            <Space direction="vertical" style={{ marginTop: '10px' }}>
              {formItem.formItem?.optionList?.map((item, index) => (
                <div key={index} className="question_radio_flex">
                  <span>正确答案：</span>
                  <Paragraph
                    ellipsis={{ rows: 1, tooltip: false }}
                    style={{ flex: 1, maxWidth: '280px', margin: '0 0 0 5px' }}
                  >
                    {item}
                  </Paragraph>
                </div>
              ))}
            </Space>
          </div>
        )}
      </Card>
    </Modal>
  );
};

export default memo(PreviewModal);
