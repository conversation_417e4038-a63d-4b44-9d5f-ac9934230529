/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/23 10:11
 * @LastEditTime: 2023/11/21 15:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/QuestionManage/home.jsx
 * @Description: '题库管理'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
  Select,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import PreviewModal from './comps/PreviewModal/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import { clearCache } from 'react-router-cache-route';
import FormModal from './comps/FormModal/home';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;

const QuestionManage = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [formParams, setFormParams] = useState({ visible: false });
  const [previewParams, setPreviewParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '题目名称',
      width: '200px',
      dataIndex: 'title',
      key: 'title',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip title={value} placement="topLeft">
          {value}
        </Tooltip>
      ),
    },
    {
      title: '题目类型',
      width: '200px',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip
          title={
            value == 'RADIO'
              ? '单选'
              : value == 'CHECKBOX'
                ? '多选'
                : value == 'TEXTAREA'
                  ? '问答'
                  : '填空题'
          }
        >
          {value == 'RADIO'
            ? '单选'
            : value == 'CHECKBOX'
              ? '多选'
              : value == 'TEXTAREA'
                ? '问答'
                : '填空题'}
        </Tooltip>
      ),
    },
    {
      title: '题目预览',
      width: '120px',
      align: 'center',
      render: (value, record) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: '更新人/更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.updateTime;
      }
      formData.createDeptIdList = formData.createDeptIdList?.join(',') || null;
      formData.updateDeptIdList = formData.updateDeptIdList?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/study/examItem', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    setFormParams({
      visible: true,
      onSubmit: () => {
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id,
      examData: record,
      onSubmit: () => {
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  // 复制
  const handleCopy = (record) => {
    setLoading(true);
    const { id } = record;
    apiCall(`/study/course/copy/${id}`, 'POST')
      .then((res) => {
        props.history.push({
          pathname: '/wecom/courseManage/course',
          search: `?id=${res[0]}`,
        });
        clearCache(); // 清空路由缓存
        message.success('课程复制成功！');
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handlePreview = (record) => {
    setPreviewParams({
      visible: true,
      formItem: record,
      onCancel: () => {
        setPreviewParams({ visible: false });
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { title, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除的题目名称为【${title}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/study/examItem/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="QuestionManage">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="title" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="题目名称" allowClear />
          </FormItem>
          <FormItem name="type">
            <Select placeholder="题目类型" allowClear>
              <Option value="RADIO">单选</Option>
              <Option value="CHECKBOX">多选</Option>
              <Option value="TEXTAREA">问答</Option>
              <Option value="FILL_BLANK">填空题</Option>
            </Select>
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem
            name="updateDeptIdList"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="更新人" />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增题目
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <PreviewModal {...previewParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default withRouter(QuestionManage);
