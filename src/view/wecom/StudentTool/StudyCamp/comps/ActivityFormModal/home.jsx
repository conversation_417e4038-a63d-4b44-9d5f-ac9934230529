/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/29 14:34
 * @LastEditTime: 2023/07/11 10:26
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\ActivityFormModal\home.jsx
 * @Description: '选择活动Model'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Modal,
  Button,
  Form,
  Input,
  Pagination,
  Spin,
  Card,
  Typography,
  Image,
  Row,
  Col,
  Empty,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import {FileHOC} from 'components/FileHOC/FileHOC';
import './home.less';

const FormItem = Form.Item;
const { Paragraph } = Typography;

const ActivityFormModal = (props) => {
  const { activityType, placeholder } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});

  useEffect(async () => {
    const { visible } = props.params;
    if (visible) {
      setVisible(visible);
      setTimeout(() => {
        fetchList();
      }, 100);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      setDataSource([]);
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        type: activityType,
        ...formData,
        studyCampFlag: true,
        studyCampId:
          activityType == 'FORM'
            ? qs2obj(props?.location?.search).id || null
            : null,
      };
      const apiUrl =
        activityType == 'FORM'
          ? '/base/form'
          : '/activity/activity/getActivityByStudyCampRelevance';
      apiCall(apiUrl, 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSizeOptions: [8, 20, 50, 100],
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
          setDataSource([]);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    setSelect({ ...dataSource[index] });
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    props.params?.onSubmit?.(select);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择活动"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="ActivityFormModal"
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="keyWord"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder={placeholder} allowClear />
          </FormItem>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          {dataSource.length > 0 ? (
            dataSource.map((item, index) => (
              <div
                className="link-card"
                key={index}
                onClick={() => handleSelectScript(index)}
              >
                <Paragraph strong ellipsis={{ rows: 2 }}>
                  {activityType == 'FORM' ? item.shareTitle : item.title}
                </Paragraph>
                <Row justify="space-between">
                  <Col span={17}>
                    <Paragraph ellipsis={{ rows: 3 }}>
                      {activityType == 'FORM'
                        ? item.shareDescription
                        : item.description}
                    </Paragraph>
                  </Col>
                  <Col>
                    <FileHOC src={item.shareImage || item.cover || 'error'}>
                      {(url) => (
                        <Image
                          width={54}
                          src={url}
                          fallback="images/fallbackImg.png"
                          preview={false}
                        />
                      )}
                    </FileHOC>
                  </Col>
                </Row>
                {item.id == select.id ? (
                  <div className="mask">
                    <CheckCircleTwoTone />
                  </div>
                ) : (
                  ''
                )}
              </div>
            ))
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
          <Pagination {...paginations} onChange={onChangePagination} />
        </Card>
      </Spin>
    </Modal>
  );
};

export default withRouter(ActivityFormModal);
