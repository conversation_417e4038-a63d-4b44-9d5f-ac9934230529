/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/02 17:58
 * @LastEditTime: 2022/12/05 10:29
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\AuditFormModal\home.jsx
 * @Description: '晒单设置'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Spin, Switch, Radio, Space } from 'antd';
import { apiCall } from 'common/utils';

const FormItem = Form.Item;

const AuditFormModal = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [configInfo, setConfigInfo] = useState(null);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      getInfoData();
    }
  }, [props]);

  const getInfoData = () => {
    setLoading(true);
    apiCall('/globalSetting/getOneByValid', 'GET').then((res) => {
      setConfigInfo(res);
      const { Article = false, MINI_PROGRAM = false, Poster = false, copyWriter = false, Video = false, pageArticle = false } = res.reviewSetting;
      formRef.current.setFieldsValue({
        Article,
        MINI_PROGRAM,
        Poster,
        copyWriter,
        Video,
        pageArticle
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onOk = () => {
    formRef.current.validateFields().then((formData) => {
      setConfirmLoading(true);
      const data = {
        id: configInfo?.id || null,
        reviewSetting: { ...formData }
      };
      apiCall('/globalSetting/addOrModify', 'POST', data).then((res) => {
        message.success('配置成功！');
        props.params?.onSubmit?.();
        onCancel();
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setConfigInfo(null);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={456}
      title="晒单设置"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <p>请根据需要开启需要审核的资源类型，开启后创建时需审核通过后才可以发布及使用，关闭则按不审核处理。</p>

        <Form ref={formRef}>
          <FormItem name="Poster" label="审核开关" valuePropName="checked" >
            <Switch checkedChildren="开" unCheckedChildren="关" />
          </FormItem>

          <FormItem label="推送对象">
            <div>
              <FormItem name="pushObj" initialValue={0} >
                <Radio.Group>
                  <Space direction="vertical">
                    <Radio value={0}>管户员工/负责人</Radio>
                    <Radio value={1}>指定人员</Radio>
                  </Space>
                </Radio.Group>
              </FormItem>
              <FormItem name="emp" >
              </FormItem>
            </div>
          </FormItem>
        </Form>

      </Spin>
    </Modal>
  );
};

export default AuditFormModal;
