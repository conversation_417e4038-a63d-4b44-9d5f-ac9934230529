/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/29 14:34
 * @LastEditTime: 2022/12/12 14:37
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\courseFormModal\home.jsx
 * @Description: '选择课程Model'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Form, Input, Pagination, Spin, Card, Typography, Tabs, Image, TreeSelect, Row, Col, Empty, } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';

import './home.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Paragraph } = Typography;
const { SHOW_PARENT } = TreeSelect;

const CourseFormModal = (props) => {
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 8 });
  const [dataSource, setDataSource] = useState([]);
  const [select, setSelect] = useState({});

  useEffect(async () => {
    const { visible } = props.params;
    if (visible) {
      setVisible(visible);
      setTimeout(() => {
        fetchList();
      }, 100);
    }
  }, [props]);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      setDataSource([]);
      const { pagination } = params;
      const pageInfo = pagination || { current: 1, pageSize: 8 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData
      }
      apiCall('/study/course/allField', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: [8, 20, 50, 100],
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
          setDataSource([]);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangePagination = (page, pageSize) => {
    fetchList({ pagination: { current: page, pageSize: pageSize } });
  };

  const handleSelectScript = (index) => {
    setSelect({ ...dataSource[index] });
  };

  const onOk = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    // setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    props.params?.onSubmit?.(select);
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setPaginations({ current: 1, pageSize: 8 });
    // setDataSource([]);
    setSelect({});
    formRef.current.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={950}
      title="选择课程"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      onCancel={onCancel}
      onOk={onOk}
      className="CourseFormModal"
    >
      <FilterBar bodyStyle={{ padding: 'unset', margin: 'unset' }}>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="keyword" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="课程名称、课时名称" allowClear />
          </FormItem>
          <div className="flex flex-space-between">
            <div>
              <Button type="primary" onClick={() => handleQuery()}>
                查询
              </Button>
              <Button onClick={() => handleReset()}>重置筛选</Button>
            </div>
          </div>
        </Form>
      </FilterBar>
      <Spin spinning={loading}>
        <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
          {
            dataSource.length > 0 ? dataSource.map((item, index) => (
              <div className="course-card" key={index} onClick={() => handleSelectScript(index)}>
                <div className='course-card_top'>
                  <Paragraph ellipsis={{ rows: 1 }} className='noMargin' style={{ minHeight: '0px' }}>
                    {item.name}
                  </Paragraph>
                </div>
                <div className='course-body'>
                  {
                    (item.courseItemVOList?.length > 0 || item.examVO) ? <div>
                      {
                        item.courseItemVOList.map((atem, andex) => (
                          <div key={andex} className="body-item">
                            <div className='body-title'>{atem.name}</div>
                            {

                            }
                            <div className='body-text'>
                              <div>{(atem.coursewareSetting?.name && atem.examVO?.name) && '01.'}{atem.coursewareSetting?.name}</div>
                            </div>
                            <div className='body-text'>
                              <div>{(atem.coursewareSetting?.name && atem.examVO?.name) && '02.'}{atem.examVO?.name}</div>
                            </div>
                          </div>
                        ))
                      }
                      {
                        item.examVO && <div className="body-item">
                          <div className='body-title'>考试</div>
                          <div className='body-text'>{item.examVO.name}</div>
                        </div>
                      }
                    </div>
                      : '暂无数据'
                  }
                </div>
                {
                  item.id == select.id ? <div className="mask">
                    <CheckCircleTwoTone />
                  </div> : ''
                }
              </div>
            )) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
          <Pagination
            {...paginations}
            onChange={onChangePagination}
          />
        </Card>
      </Spin>
    </Modal>
  );
};

export default CourseFormModal;
