.CourseFormModal {
  .ant-card {

    &.select-item {
      overflow: hidden;
    }

    .mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1000;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      -webkit-overflow-scrolling: touch;

      .anticon {
        font-size: 40px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }

    // .ant-typography {
    //   font-size: 12px;
    //   line-height: 18px;
    //   min-height: 36px;
    // }

    .ant-pagination {
      text-align: right;
    }

    .course-card {
      position: relative;
      width: 220px;
      height: auto;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin: 0 34px 10px;
      overflow: hidden;
      display: inline-block;
      box-sizing: border-box;

      .course-card_top {
        padding: 5px 10px 5px 10px;
        border-bottom: 1px solid #c1c1c1;
      }

      .course-body {
        height: 220px;
        padding: 10px 0 10px 10px;
        box-sizing: border-box;
        overflow-y: auto;

        .body-item {
          // border-bottom: 1px solid #ccc;

          &:last-child {
            border-bottom: unset
          }
        }

        .body-title {
          font-size: 13px;
        }

        .body-text {
          font-size: 13px;
          text-indent: 24px;
          color: #AAAAAA;

          .body-text_des {
            text-indent: 40px;
          }
        }
      }

      .noMargin {
        margin-bottom: 0px;
        font-weight: bold;
      }
    }
  }
}