/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/31 17:06
 * @LastEditTime: 2023/09/08 09:56
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\ExportModal\home.jsx
 * @Description: '导出对话框'
 */
import React, { useEffect, useRef, useState } from "react";
import { Tabs, message, Modal, Spin, Button, Image } from "antd";
import { apiCall } from "common/utils";
import moment from "moment";
import "./home.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const ExportModal = (props) => {
  const { studyCampId } = props.params;
  const formRef = useRef(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [TabsActiveKey, setTabsActiveKey] = useState("1");
  const [imageVisible, setImageVisible] = useState(false);
  const ImagePreviewList = [require("images/学习营客户学习情况汇总.png")];

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
    }
  }, [props]);

  const onChangeTabs = (activeKey) => {
    setTabsActiveKey(activeKey);
    formRef.current.resetFields();
  };

  const onOk = () => {
    setConfirmLoading(true);
    const data = {
      studyCampId,
    };
    apiCall("/study/studyCamp/export/studySummary", "POST", data, null, {
      isExit: true,
      title: `学习营数据.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => {
        message.success("导出成功！");
        onCancel();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };

  const onCancel = () => {
    setVisible(false);
    props.params?.onCancel?.();
  };

  return (
    <Modal
      className="StudyCamp-ExportModal"
      visible={visible}
      title="导出学习营数据"
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      footer={[
        <div key={1}>
          <a onClick={() => setImageVisible(true)}>报表示例图</a>
          <FileHOC src={ImagePreviewList[TabsActiveKey - 1]}>
            {(url) => (
              <Image
                style={{ display: "none" }}
                preview={{
                  visible: imageVisible,
                  src: url,
                  onVisibleChange: (value) => {
                    setImageVisible(value);
                  },
                }}
              />
            )}
          </FileHOC>
        </div>,
        <div key={2}>
          <Button key="back" onClick={onCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={onOk}
          >
            导出
          </Button>
        </div>,
      ]}
    >
      <Spin spinning={loading}>
        <Tabs defaultActiveKey={TabsActiveKey} onChange={onChangeTabs}>
          <Tabs.TabPane tab="学习营客户学习情况汇总" key="1">
            <p className="tips">学习营运营期间截至到当前的客户学习情况汇总</p>
          </Tabs.TabPane>
        </Tabs>
      </Spin>
    </Modal>
  );
};

export default ExportModal;
