/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/06 15:16
 * @LastEditTime: 2024/10/22 09:36
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/ExtendModal/home.jsx
 * @Description: '推广'
 */

import React, { useState, useEffect } from "react";
import {
  Modal,
  Tooltip,
  Button,
  Table,
  Select,
  message,
  Image,
  Typography,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { apiCall } from "common/utils";
import { QrCodeBase } from "common/qrcode";
import ListOperation from 'components/ListOperation/home';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { Paragraph } = Typography;

const ExtendModal = (props) => {
  const { visible, studyCampId, onCancel } = props.params;
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [initDataSource, setInitDataSource] = useState([]);
  const [channelOption, setChannelOption] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "获客渠道",
      width: "170px",
      dataIndex: "channelId",
      key: "channelId",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <Select
              style={{ width: "120px" }}
              placeholder="选择渠道"
              fieldNames={{ label: "name", value: "id" }}
              value={value}
              allowClear
              options={channelOption}
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
              onChange={(val, option) => {
                let newDataSource = JSON.parse(JSON.stringify(dataSource));
                if (val) {
                  newDataSource[index].channelId = option.id;
                  newDataSource[index].channelName = option.name;
                } else {
                  newDataSource[index].channelId = "";
                  newDataSource[index].channelName = "";
                }
                setDataSource([...newDataSource]);
              }}
            />
          ) : (
            <Tooltip placement="topLeft" title={record.channelName}>{record.channelName}</Tooltip>
          )}
        </>
      ),
    },
    {
      title: "渠道二维码",
      width: "120px",
      dataIndex: "transitUrl",
      key: "transitUrl",
      align: "center",
      render: (value, record, index) => {
        const image = value ? QrCodeBase({ url: value }) : '';
        return image ? <FileHOC src={image}>
          {(url) => (
            <Image width={60} src={url} preview={url}></Image>
          )}
        </FileHOC> : '-';
      },
    },
    {
      title: "渠道链接",
      width: "160px",
      dataIndex: "transitUrl",
      key: "transitUrl",
      align: "center",
      render: (value, record, index) => (
        value && <Tooltip placement="topLeft" title={value}>
          <Paragraph ellipsis={{ rows: 1 }} copyable>
            {value}
          </Paragraph>
        </Tooltip>
      )
    },
    {
      title: "数据",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          访问人数：{record.visitorCount || 0}
          <br />
          访问次数：{value || 0}
        </div>
      ),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [];
        if (record.isEdit) {
          opts.push({ onClick: () => handleKeep(record, index), name: "保存" });
          opts.push({ onClick: () => handleCancal(record, index), name: "取消" });
        } else {
          opts.push({ onClick: () => handleEdit(record, index), name: "编辑" });
        }
        if (!record.isEdit && record.channelId) {
          if (record.visitorCount && record.visitCount) {
            opts.push({ onClick: () => { message.error("学习营已存在数据，不可删除！") }, name: "删除" });
          } else {
            opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
          }
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    if (visible) {
      getChannelOption();
      getInfoStudyCampChannel();
    }
  }, [visible]);

  // 获客渠道
  const getChannelOption = () => {
    setLoading(true);
    apiCall("/activity/channel/option", "GET")
      .then((res) => {
        setChannelOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getInfoStudyCampChannel = (id) => {
    setLoading(true);
    apiCall(`/study/studyCamp/studyCampChannel?studyCampId=${studyCampId}`, "GET")
      .then((res) => {
        setDataSource(res);
        setInitDataSource(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleAdd = (type = null) => {
    let newDataSource = dataSource;
    newDataSource.unshift({
      channelId: "",
      isEdit: true,
    });
    setDataSource([...newDataSource]);
  };

  const handleEdit = (record, index) => {
    let newDataSource = dataSource;
    newDataSource[index].isEdit = true;
    setDataSource([...newDataSource]);
  };

  const handleKeep = (record, index) => {
    const { id, channelId } = record;
    if (channelId == "") {
      message.warning("渠道名称不能为空！");
      return false;
    }
    setLoading(true);
    const data = {
      id: id ?? null,
      studyCampId: studyCampId,
      channelId: channelId ?? null,
    };
    const apiUrl = "/study/studyCamp/studyCampChannel/saveOrUpdate";
    apiCall(apiUrl, "POST", data)
      .then((res) => {
        message.success("保存成功！");
        getInfoStudyCampChannel();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleDelete = (record) => {
    const { id } = record;
    setLoading(true);
    apiCall(`/study/studyCamp/delete/studyCampChannel/${id}`, "POST")
      .then((res) => {
        message.success("删除成功！");
        let newDataSource = dataSource;
        newDataSource.splice(index, 1);
        setDataSource([...newDataSource]);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCancal = (record, index) => {
    const { id } = record;
    let newDataSource = dataSource;
    if (id) {
      const findData = initDataSource.find(item => item.id == id);
      findData && (newDataSource[index] = { ...findData, isEdit: false });
    } else {
      newDataSource.splice(index, 1);
    }
    setDataSource([...newDataSource]);
  };

  return (
    <Modal
      className="ExtendModal"
      visible={visible}
      width={900}
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          推广渠道
          <Button
            style={{ marginRight: "20px" }}
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleAdd()}
          >
            新增渠道
          </Button>
        </div>
      }
      destroyOnClose
      maskClosable={false}
      afterClose={null}
      centered
      onCancel={() => {
        setLoading(false);
        setDataSource([]);
        onCancel?.();
      }}
      footer={null}
    >
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default ExtendModal;
