/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 15:36
 * @LastEditTime: 2025/07/02 14:27
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/PreviewForm/home.jsx
 * @Description: '表单界面预览'
 */

import React, { useEffect } from "react"
import {
  Carousel,
  Typography,
  Tabs,
  Collapse,
  Button,
  Image,
  Empty,
} from "antd"
import WibotMobilePreview from "components/WibotMobilePreview/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"

const { Paragraph } = Typography
const { Panel } = Collapse

const PreviewForm = (props) => {
  const { previewData } = props

  useEffect(() => {}, [])

  return (
    <>
      <WibotMobilePreview
        title="学习营"
        footer={
          <>
            <Button type="primary" danger shape="round">
              立即报名
            </Button>
          </>
        }
      >
        <Carousel autoplay>
          {previewData.fileList?.map((item, index) => (
            <div className="carousel-item" key={index}>
              <FileHOC src={item.url}>
                {(url) => <Image preview={false} src={url} />}
              </FileHOC>
            </div>
          ))}
        </Carousel>
        {(previewData.name ||
          previewData.description ||
          previewData.signUpTime ||
          previewData.openTime) && (
          <div className="info-card">
            <Paragraph ellipsis={{ rows: 1 }} className="noMargin">
              {previewData.name}
            </Paragraph>
            <Paragraph ellipsis={{ rows: 3 }} className="noMargin text">
              {previewData.description}
            </Paragraph>
            {previewData.signUpTime && (
              <div className="text">
                报名时间 {previewData.signUpTime[0]}~{previewData.signUpTime[1]}
              </div>
            )}
            {previewData.openTime && (
              <div className="text">开营时间 {previewData.openTime}</div>
            )}
          </div>
        )}
        {(previewData.detailsHtml || previewData.courseList?.length > 0) && (
          <div className="info-tabs">
            <Tabs defaultActiveKey="1" centered>
              <Tabs.TabPane tab="营期详情" key="1">
                <div className="tabs-des">
                  <WibotEditorView
                    className="des-text"
                    html={previewData.detailsHtml}
                  />
                </div>
              </Tabs.TabPane>
              <Tabs.TabPane tab="营期安排" key="2">
                <div className="tab-plan">
                  {previewData.courseList &&
                    previewData.courseList?.map((item, index) => (
                      <div className="plan-flex" key={index}>
                        <div style={{ marginRight: "5px" }}>
                          课程{index + 1}
                        </div>
                        <Collapse>
                          <Panel
                            header={
                              <div>
                                <div>{item.name}</div>
                                {item.examLockFlag && (
                                  <div
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      e.preventDefault()
                                    }}
                                  >
                                    <span>解锁时间：</span>
                                    <span>{item.unlockTime}</span>
                                  </div>
                                )}
                              </div>
                            }
                            key={index}
                          >
                            {item.courseItemVOList?.length > 0 ||
                            item.examVO ? (
                              <div>
                                {item.courseItemVOList.length > 0 && (
                                  <Collapse defaultActiveKey={0}>
                                    {item.courseItemVOList.map(
                                      (atem, andex) => (
                                        <Panel
                                          header={atem.name}
                                          key={`${andex}`}
                                        >
                                          <p>
                                            {atem.coursewareSetting?.name &&
                                              atem.examVO?.name &&
                                              "01."}
                                            {atem.coursewareSetting?.name}
                                          </p>
                                          <p>
                                            {atem.coursewareSetting?.name &&
                                              atem.examVO?.name &&
                                              "02."}
                                            {atem.examVO?.name}
                                          </p>
                                        </Panel>
                                      )
                                    )}
                                  </Collapse>
                                )}
                                {item.examVO && (
                                  <Collapse defaultActiveKey={0}>
                                    <Panel header="考试" key={0}>
                                      <div>{item.examVO.name}</div>
                                    </Panel>
                                  </Collapse>
                                )}
                              </div>
                            ) : (
                              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                            )}
                          </Panel>
                        </Collapse>
                      </div>
                    ))}
                </div>
              </Tabs.TabPane>
            </Tabs>
          </div>
        )}
      </WibotMobilePreview>
    </>
  )
}

export default PreviewForm
