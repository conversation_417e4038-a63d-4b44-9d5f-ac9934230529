/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 15:36
 * @LastEditTime: 2025/07/02 14:29
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/PreviewModal/home.jsx
 * @Description: '列表预览弹窗'
 */

import React, { useEffect, useState } from "react"
import {
  Empty,
  Carousel,
  Typography,
  Tabs,
  Collapse,
  Button,
  Image,
} from "antd"
import moment from "moment"
import WibotMobilePreviewModal from "components/WibotMobilePreview/modal"
import { FileHOC } from "components/FileHOC/FileHOC"
import WibotEditorView from "components/WibotEditorView/home"
import "./home.less"

const { Paragraph } = Typography
const { Panel } = Collapse

const PreviewModal = (props) => {
  const { visible, previewData = null, onCancel } = props.params

  useEffect(() => {}, [])

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <WibotMobilePreviewModal
      style={{ background: "#f0f0f0" }}
      className="WibotMobilePreviewModal-study"
      visible={visible}
      title="学习营"
      footer={
        <>
          <Button type="primary" danger shape="round">
            立即报名
          </Button>
        </>
      }
      onCancel={handleCancel}
    >
      {previewData ? (
        <>
          <Carousel autoplay>
            {previewData.imageList?.map((item, index) => (
              <div className="carousel-item" key={index}>
                <FileHOC src={item}>
                  {(url) => <Image preview={false} src={url} />}
                </FileHOC>
              </div>
            ))}
          </Carousel>
          {(previewData.name ||
            previewData.description ||
            previewData.signUpTime ||
            previewData.openTime) && (
            <div className="info-card">
              <Paragraph ellipsis={{ rows: 1 }} className="noMargin">
                {previewData.name}
              </Paragraph>
              <Paragraph ellipsis={{ rows: 3 }} className="noMargin text">
                {previewData.description}
              </Paragraph>
              {previewData.signUpStartTime && (
                <div className="text">
                  报名时间{" "}
                  {moment(previewData.signUpStartTime).format(
                    "YYYY/MM/DD HH:mm"
                  )}
                  ~
                  {moment(previewData.signUpEndTime).format("YYYY/MM/DD HH:mm")}
                </div>
              )}
              {previewData.openTime && (
                <div className="text">开营时间 {previewData.openTime}</div>
              )}
              <div className="text">
                报名人数 {previewData.signUpCount || 0}
              </div>
            </div>
          )}
          <div className="info-tabs">
            <Tabs defaultActiveKey="1" centered>
              <Tabs.TabPane tab="营期详情" key="1">
                <div className="tabs-des">
                  <WibotEditorView
                    className="des-text"
                    html={previewData.detail}
                  />
                </div>
              </Tabs.TabPane>
              <Tabs.TabPane tab="营期安排" key="2">
                <div className="tab-plan">
                  {previewData.courseVOList &&
                    previewData.courseVOList?.map((item, index) => (
                      <div className="plan-flex" key={index}>
                        <div style={{ marginRight: "5px" }}>
                          课程{index + 1}
                        </div>
                        <Collapse>
                          <Panel
                            header={
                              <div>
                                <div>{item.name}</div>
                                {item.examLockFlag && (
                                  <div
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      e.preventDefault()
                                    }}
                                  >
                                    <span>解锁时间：</span>
                                    <span>{item.unlockTime}</span>
                                  </div>
                                )}
                              </div>
                            }
                            key={index}
                          >
                            {item.courseItemVOList?.length > 0 ||
                            item.examVO ? (
                              <div>
                                {item.courseItemVOList.length > 0 && (
                                  <Collapse defaultActiveKey={0}>
                                    {item.courseItemVOList.map(
                                      (atem, andex) => (
                                        <Panel
                                          header={atem.name}
                                          key={`${andex}`}
                                        >
                                          <p>
                                            {atem.coursewareSetting?.name &&
                                              atem.examVO?.name &&
                                              "01."}
                                            {atem.coursewareSetting?.name}
                                          </p>
                                          <p>
                                            {atem.coursewareSetting?.name &&
                                              atem.examVO?.name &&
                                              "02."}
                                            {atem.examVO?.name}
                                          </p>
                                        </Panel>
                                      )
                                    )}
                                  </Collapse>
                                )}
                                {item.examVO && (
                                  <Collapse defaultActiveKey={0}>
                                    <Panel header="考试" key={0}>
                                      <div>{item.examVO.name}</div>
                                    </Panel>
                                  </Collapse>
                                )}
                              </div>
                            ) : (
                              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                            )}
                          </Panel>
                        </Collapse>
                      </div>
                    ))}
                </div>
              </Tabs.TabPane>
            </Tabs>
          </div>
        </>
      ) : null}
    </WibotMobilePreviewModal>
  )
}

export default PreviewModal
