.WibotMobilePreviewModal-study {
  .WibotMobilePreview-Container {
    .carousel-item {
      height: 120px;

      .ant-image {
        width: 100%;
        height: 100%;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }

    .ant-carousel .slick-dots li {
      width: 5px;
      height: 5px;
      border-radius: 50%;
    }

    .ant-carousel .slick-dots li.slick-active {
      background-color: #1890ff;
    }

    .ant-carousel .slick-dots li button {
      height: 5px;
      border-radius: 50%;
    }

    .ant-carousel .slick-dots li.slick-active button {
      background-color: #1890ff !important;
    }

    .info-card {
      background: #fff;
      border-radius: 10px;
      margin: 10px 0;
      padding: 10px;
      text-align: left;

      .noMargin {
        margin-bottom: 0px;
      }
    }

    .info-tabs {
      background: #fff;
      border-radius: 10px;
      padding: 10px;

      .tabs-des {
        position: relative;
        height: 200px;
        text-align: left;
      }

      .ant-tabs-ink-bar {
        background: #d9001b !important;
      }

      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: unset !important;
      }

      .ant-tabs-tab:hover {
        color: unset !important;
      }
    }

    .tab-plan {
      .plan-flex {
        display: flex;
        flex-direction: row;
        font-size: 12px;
        margin-bottom: 10px;
      }

      .ant-collapse {
        flex: 1;
        font-size: 12px;
        text-align: left;
      }
    }

    .ant-btn {
      margin: 0 auto;
      display: block;
    }

    .text {
      font-size: 12px;
      color: #7f7f7f;
    }
  }
}
