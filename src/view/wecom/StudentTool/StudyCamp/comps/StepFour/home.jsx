/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/29 10:15
 * @LastEditTime: 2024/10/22 09:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/StepFour/home.jsx
 * @Description: '步骤四'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import {
  Spin, Button, Form, Switch, Image,
} from 'antd';
import { apiCall } from 'common/utils';
import { qs2obj } from 'common/object';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;

const StudyCampStepFour = forwardRef((props, ref) => {
  const { formForm, processingFlag, shelfState, handlePoster } = props;

  useImperativeHandle(ref, () => ({
    getInitData,
    getPosterId
  }));

  const [loading, setLoading] = useState(false);
  const [switchFlag, setSwitchFlag] = useState(true);
  const [imageUrl, setImageUrl] = useState('');
  const [posterId, setPosterId] = useState(null);
  const [id, setId] = useState(null);

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    if (switchFlag) {
      setLoading(true);
      let apiUrl = null;
      const fieldData = localStorage.getItem('studyCampField') ? JSON.parse(localStorage.getItem('studyCampField')) : {};
      if (id) {
        apiUrl = fieldData.posterId ? `/study/studyCamp/poster?studyCampId=${id}&fastPosterId=${fieldData.posterId}` : `/study/studyCamp/poster?studyCampId=${id}`;
      } else {
        apiUrl = fieldData.posterId ? `/study/studyCamp/poster?fastPosterId=${fieldData.posterId}` : '/study/studyCamp/poster';
      }
      apiCall(apiUrl, 'POST').then((res) => {
        const { image, fastPosterId, id } = res;
        setImageUrl(image);
        setPosterId(fastPosterId);
        setId(id);
        formForm.setFieldsValue({
          fileId: image
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [switchFlag]);

  // 获取详情数据
  const getInitData = (params) => {
    const { enable } = params;
    setSwitchFlag(enable);
  };

  // 获取海报id
  const getPosterId = () => posterId;
  // 结营证书
  const handleCertificateFlag = (checked) => {
    setSwitchFlag(checked);
  };

  return (
    <div className='StudyCampStepFour'>
      <Spin spinning={loading}>
        <h2 className='card-title'>结营奖励</h2>
        <FormItem name={['finishReward', 'enable']} initialValue label="结营证书" valuePropName="checked" required>
          <Switch disabled={processingFlag || shelfState == 'soldOut'} onChange={handleCertificateFlag} />
        </FormItem>
        {/* {
          switchFlag && <FormItem label="证书信息" name={['finishReward', 'certificate']} extra="勾选时会自动在学员证书中展示相关信息">
            <Checkbox.Group options={certificateOptions} />
          </FormItem>
        } */}
        {
          switchFlag && <FormItem name="fileId" label="证书图片" rules={[{ required: true, message: '证书图片不能为空' }]}>
            <div>
              <FileHOC src={imageUrl}>
                {(url) => (
                  <Image
                    width={90}
                    height={90}
                    src={url}
                    fallback="images/fallbackImg.png"
                    style={{ objectFit: 'cover' }}
                    preview
                  />
                )}
              </FileHOC>
              <Button type="primary" style={{ verticalAlign: 'bottom', marginLeft: '10px' }} disabled={shelfState == 'soldOut'} onClick={() => { handlePoster(posterId); }}>维护证书</Button>
            </div>
          </FormItem>
        }

      </Spin>
    </div>
  );
});

export default StudyCampStepFour;
