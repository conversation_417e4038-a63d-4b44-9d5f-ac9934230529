/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/30 10:25
 * @LastEditTime: 2023/03/30 15:49
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\StepOne\comps\PosterPreviewModal.jsx
 * @Description: '海报预览'
 */

import React, { useEffect } from 'react';
import { Row, Col, Image, Typography } from 'antd';
import { AppstoreOutlined } from '@ant-design/icons';
import './PosterPreviewModal.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { Paragraph } = Typography;

const PosterPreviewModal = (props) => {
  const { visible, data = {}, onCancel } = props;

  useEffect(() => {}, [visible]);

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <div className="PosterPreviewModal">
      <div className="PosterPreviewModal_previewWrap">
        <div className="previewWrap_top">
          <FileHOC src={data.imageUrl}>
            {(url) => (
              <Image
                // height={300}
                src={url}
                preview={false}
              ></Image>
            )}
          </FileHOC>
        </div>
        <div className="PosterPreviewModal_content">
          <Paragraph ellipsis={{ rows: 1 }}>{data.name}</Paragraph>
          <Row justify="space-between">
            <Col span={17}>
              <Paragraph
                style={{ fontSize: '12px', lineHeight: '18px' }}
                ellipsis={{ rows: 2 }}
              >
                {data.posterText}
              </Paragraph>
            </Col>
            <Col>
              <AppstoreOutlined style={{fontSize: '45px'}}/>
              {/* <Image
                width={60}
                height="100%"
                src={data.linkImageUrl || "error"}
                fallback="images/fallbackImg.png"
                preview={false}
              /> */}
            </Col>
          </Row>
        </div>
      </div>
      <div className="mask-modal" onClick={handleCancel}></div>
    </div>
  );
};

export default PosterPreviewModal;
