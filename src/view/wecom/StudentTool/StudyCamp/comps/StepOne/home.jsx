/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 15:36
 * @LastEditTime: 2025/05/13 15:15
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/StepOne/home.jsx
 * @Description: '步骤一'
 */

import React, {
  useEffect,
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react"
import {
  Spin,
  Button,
  Form,
  Input,
  Row,
  Col,
  TreeSelect,
  Image,
  Upload,
  DatePicker,
} from "antd"
import moment from "moment"
import { apiCall } from "common/utils"
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import { PlusOutlined, LoadingOutlined } from "@ant-design/icons"
import { normFile, removeInputEmpty, editorIsEmpty } from "common/regular"
import { compressImage, base64ToFile, beforeUpload } from "common/image"
import MaterialModal from "components/Modal/MaterialModal/home"
import PosterPreviewModal from "./comps/PosterPreviewModal"
import WibotEditor from "components/WibotEditor/home"
import WibotUploadImage from "components/WibotUploadImage/home"
// import WibotUpload from "components/WibotUpload/home";
import "./home.less"
import { FileHOC } from "components/FileHOC/FileHOC"
import { formatURL } from "config/index.js"
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { SHOW_PARENT } = TreeSelect
const { TextArea } = Input

const StudyCampStepOne = forwardRef((props, ref) => {
  const {
    formForm,
    processingFlag,
    shelfState,
    handleModifyData,
    handleDetailsHtml,
  } = props

  useImperativeHandle(ref, () => ({
    getInitData,
  }))

  const WibotEditorRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [labelTreeData, setLabelTreeData] = useState([])
  const [fileList, setFileList] = useState([])
  // const fileListRef = useRef()
  const [uploadLoading, setUploadLoading] = useState(false)
  const [uploadLoadingSingle, setUploadLoadingSingle] = useState(false)
  const [uploadShareLoading, setUploadShareLoading] = useState(false)
  const [imageUrl, setImageUrl] = useState("")
  const [linkImageUrl, setLinkImageUrl] = useState("")
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [posterPreviewParams, setPosterPreviewParams] = useState({
    visible: false,
  })
  const [materialModalParams, setMaterialModalParams] = useState({
    visible: false,
  })

  useEffect(() => {
    getTagCategoryTreeTwo()
  }, [])

  useEffect(() => {
    const newFileList = JSON.parse(JSON.stringify(fileList))
    handleModifyData({ fileList: [...newFileList] })
    // fileListRef.current = [...newFileList]
  }, [fileList])

  // 获取详情数据
  const getInitData = (data) => {
    const imageList = data.imageList?.map((item) => ({
      uid: item,
      name: item,
      fileId: item,
      status: "done",
      url: item,
    }))
    setFileList(imageList || [])
    setImageUrl(data.imageUrl)
    setLinkImageUrl(data.shareImage)
    let timer = setTimeout(() => {
      WibotEditorRef.current.setHtml(data.detail ?? "")
      clearTimeout(timer)
    }, 300)
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    setLoading(true)
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res)
        setLabelTreeData(tagTreeData)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const onChangeUpload = (info, type = null) => {
    if (info.file.status === "uploading") {
      if (type == "link") {
        setUploadShareLoading(true)
      } else if (type == "single") {
        setUploadLoadingSingle(true)
      } else {
        setUploadLoading(true)
      }
      return
    }
    if (info.file.status === "removed") {
      let fieldData = localStorage.getItem("imageField")
        ? JSON.parse(localStorage.getItem("imageField"))
        : {}
      setFileList(info.fileList)
      localStorage.setItem(
        "imageField",
        JSON.stringify({
          ...fieldData,
          fileList: info.fileList.map((item) => item.url),
        })
      )
      return
    }
  }

  const customRequest = (config, type = null) => {
    const File = config.file
    let fieldData = localStorage.getItem("imageField")
      ? JSON.parse(localStorage.getItem("imageField"))
      : {}
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData()
    // 通过append方法来追加数据
    formData.append("file", File) // 返回压缩后的图片
    const data = formData
    apiCall("/file/image", "POST", data)
      .then((res) => {
        let { fileId, fileUrl } = res
        fileUrl = formatURL(fileUrl)
        if (type == "single") {
          setImageUrl(fileUrl)
          formForm.setFieldsValue({
            poster: [fileId],
          })
          localStorage.setItem(
            "imageField",
            JSON.stringify({ ...fieldData, imageUrl: fileUrl })
          )
        } else if (type == "link") {
          setLinkImageUrl(fileUrl)
          formForm.setFieldsValue({
            shareImage: [fileId],
          })
          localStorage.setItem(
            "imageField",
            JSON.stringify({ ...fieldData, linkImageUrl: fileUrl })
          )
        } else {
          let newFileList = fileList
          newFileList.push({
            uid: fileId,
            name: File.name,
            fileId: fileId,
            status: "done",
            url: fileUrl,
          })
          setFileList([...newFileList])
          formForm.setFieldsValue({
            imageList: newFileList.map((item) => item.fileId),
          })
          localStorage.setItem(
            "imageField",
            JSON.stringify({
              ...fieldData,
              fileList: newFileList.map((item) => item.url),
            })
          )
        }
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setUploadLoading(false)
        setUploadLoadingSingle(false)
        setUploadShareLoading(false)
      })
  }

  // 海报预览
  const handlePreview = () => {
    const { name, posterText } = formForm.getFieldsValue(["name", "posterText"])
    setPosterPreviewParams({
      visible: true,
      data: {
        name,
        posterText,
        imageUrl,
        linkImageUrl,
      },
      onCancel: () => {
        setPosterPreviewParams({ visible: false })
      },
    })
  }

  // 图片预览
  const onPreviewImage = (file) => {
    setPreviewParams({
      ...file,
      visible: true,
    })
  }

  const handleResetUpload = (e, type = null) => {
    e.preventDefault() // 阻止浏览器默认行为
    e.stopPropagation() // 阻止事件冒泡
    if (type) {
      setLinkImageUrl("")
      formForm.setFieldsValue({
        shareImage: "",
      })
      return false
    }
    setImageUrl("")
    formForm.setFieldsValue({
      poster: "",
    })
  }

  // 从素材库选择海报
  const handleSelectMaterial = () => {
    let fieldData = localStorage.getItem("imageField")
      ? JSON.parse(localStorage.getItem("imageField"))
      : {}
    setMaterialModalParams({
      visible: true,
      tabType: "Poster",
      tabList: ["Poster"],
      multiple: false,
      placeholder: "请输入海报名称",
      onSubmit: (data) => {
        setMaterialModalParams({ visible: false })
        const { fileId, id } = data
        setImageUrl(fileId[0])
        formForm.setFieldsValue({
          poster: [id],
        })
        localStorage.setItem(
          "imageField",
          JSON.stringify({ ...fieldData, imageUrl: fileId[0] })
        )
      },
      onCancel: () => {
        setMaterialModalParams({ visible: false })
      },
    })
  }

  const uploadButton = (
    <div>
      {uploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>{uploadLoading ? "上传中" : "上传"}</div>
    </div>
  )

  return (
    <div className="StudyCampStepOne">
      <Spin spinning={loading}>
        <h2 className="card-title">营期信息</h2>
        <FormItem
          label="学习营名称"
          name="name"
          getValueFromEvent={(e) => removeInputEmpty(e)}
          rules={[{ required: true, message: "请输入学习营名称" }]}
        >
          <Input
            placeholder="请输入学习营名称（30字内）"
            disabled={processingFlag}
            allowClear
            onChange={(e) => {
              handleModifyData({ name: e.target.value })
            }}
          />
        </FormItem>
        <FormItem
          name="openTime"
          label="开营时间"
          rules={[{ required: true, message: "请选择开营时间" }]}
        >
          <DatePicker
            format="YYYY-MM-DD HH:mm"
            showTime={{ defaultValue: moment("00:00:00", "HH:mm") }}
            disabled={processingFlag || shelfState == "soldOut"}
            onChange={(date, dateString) => {
              handleModifyData({
                openTime: date
                  ? moment(dateString).format("YYYY/MM/DD HH:mm")
                  : null,
              })
            }}
            style={{ width: "100%" }}
          />
        </FormItem>
        <FormItem
          name="time"
          label="上下架时间"
          rules={[{ required: true, message: "请选择上下架时间" }]}
          extra="设置上下架时间时请留意报名时间及开营时间"
        >
          <RangePicker
            showTime={{ format: "HH:mm" }}
            format="YYYY-MM-DD HH:mm"
            disabled={processingFlag || shelfState == "soldOut"}
            inputReadOnly
          />
        </FormItem>
        {/* <FormItem */}
        {/*   name="customerTagIdList" */}
        {/*   label="客户标签" */}
        {/*   rules={[{ required: true, message: "请选择客户标签" }]} */}
        {/* > */}
        {/*   <TreeSelect */}
        {/*     treeData={labelTreeData} */}
        {/*     treeCheckable */}
        {/*     treeDefaultExpandedKeys={["1"]} */}
        {/*     allowClear */}
        {/*     showArrow */}
        {/*     showSearch */}
        {/*     treeNodeFilterProp="title" */}
        {/*     maxTagCount="responsive" */}
        {/*     showCheckedStrategy={SHOW_PARENT} */}
        {/*     placeholder="客户标签" */}
        {/*   /> */}
        {/* </FormItem> */}
        <CustomTagSelect
          label="客户标签"
          rules={[{ required: true, message: "请选择客户标签" }]}
          name="tagNameList"
          placeholder="客户标签"
          useForm={formForm}
          existTagNameList={formForm.getFieldValue("tagNameList")}
          labelTreeData={labelTreeData}
        />
        <FormItem
          name="imageList"
          getValueFromEvent={normFile}
          label="学习营封面"
          rules={[{ required: true, message: "请上传图片" }]}
          extra="建议尺寸750*360，大小限制为2M，最多上传5张，第一张为学习营主图"
        >
          <Upload
            name="file"
            customRequest={customRequest}
            listType="picture-card"
            fileList={fileList.map((item) => ({
              ...item,
              url: formatURL(item.url),
            }))}
            beforeUpload={beforeUpload}
            onChange={onChangeUpload}
            onPreview={onPreviewImage}
          >
            {fileList.length >= 5 ? null : uploadButton}
          </Upload>

          {/* <WibotUpload
            fileList={fileList}
            showUploadList={true}
            maxWidth={750}
            maxHeight={360}
            size={2}
            maxCount={5}
            onChange={(params) => {
              const { file, fileList } = params;
              if (file.status === 'removed') {
                setFileList(fileList)
              }
            }}
            onDone={(params) => {
              const { fileUrl, fileId, index } = params;
              let newFileList = JSON.parse(JSON.stringify(fileListRef.current || fileList));
              if (index >= 0) {
                newFileList.splice(index, 1, { status: 'done', url: fileUrl })
              } else {
                newFileList.push({
                  status: 'done',
                  url: fileUrl,
                });
              }
              setFileList([...newFileList])
            }}
          /> */}
        </FormItem>
        <FormItem
          name="poster"
          label="学习营海报"
          rules={[{ required: true, message: "请上传图片" }]}
        >
          <div>
            <div className="posterWrap">
              <Upload
                name="file"
                customRequest={(info) => {
                  customRequest(info, "single")
                }}
                listType="picture-card"
                showUploadList={false}
                beforeUpload={beforeUpload}
                onChange={(info) => {
                  onChangeUpload(info, "single")
                }}
              >
                <WibotUploadImage
                  imageUrl={imageUrl}
                  loading={uploadLoadingSingle}
                  onClose={handleResetUpload}
                />
              </Upload>
              <Button
                className="postButton"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  handleSelectMaterial()
                }}
              >
                选择海报
              </Button>
            </div>
            <div style={{ marginTop: "8px", color: "rgba(0, 0, 0, 0.45)" }}>
              支持上传或选择1张海报，海报建议尺寸750*1334px或9:16，大小限制为2M，最多上传1张
            </div>
            <div style={{ marginTop: "8px" }}>
              海报底部拼接文案：<a onClick={() => handlePreview()}>海报预览</a>
            </div>
            <FormItem
              name="posterText"
              initialValue="新一期的工行理财学习营又来了，丰富有趣的理财知识等你来学习哦！"
              noStyle
            >
              <TextArea
                allowClear
                autoSize={{ minRows: 2, maxRows: 7 }}
                className="textArea-mid"
              />
            </FormItem>
          </div>
        </FormItem>
        <FormItem label="分享设置">
          <div className="link-card" style={{ width: "100%" }}>
            <FormItem
              name="shareTitle"
              getValueFromEvent={(e) => removeInputEmpty(e)}
              rules={[{ required: true, message: "请输入分享链接标题" }]}
            >
              <Input
                placeholder="请输入分享链接标题(30字)"
                maxLength={30}
                allowClear
              />
            </FormItem>
            <Row
              justify="space-between"
              gutter={16}
              style={{ marginTop: "10px" }}
            >
              <Col span={15}>
                <FormItem name="shareDescription">
                  <TextArea
                    placeholder="请输入分享链接描述（30字内）"
                    allowClear
                    showCount
                    maxLength={30}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                    className="textArea-mid"
                  />
                </FormItem>
              </Col>
              <Col span={9}>
                <FormItem
                  name="shareImage"
                  valuePropName="imgUrl"
                  getValueFromEvent={normFile}
                  rules={[{ required: true, message: "请选择图片" }]}
                >
                  <Upload
                    name="file"
                    customRequest={(file) => {
                      customRequest(file, "link")
                    }}
                    listType="picture-card"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={(info) => {
                      onChangeUpload(info, "link")
                    }}
                  >
                    <WibotUploadImage
                      imageUrl={linkImageUrl}
                      loading={uploadShareLoading}
                      onClose={(e) => {
                        handleResetUpload(e, "link")
                      }}
                    />
                  </Upload>
                </FormItem>
              </Col>
            </Row>
          </div>
        </FormItem>
        <FormItem
          name="description"
          label="学习营简介"
          rules={[{ required: true, message: "请输入学习营简介" }]}
        >
          <TextArea
            placeholder="请输入学习营简介（100字内）"
            allowClear
            showCount
            maxLength={100}
            autoSize={{ minRows: 2, maxRows: 7 }}
            className="textArea-mid"
            onChange={(e) => {
              handleModifyData({ description: e.target.value })
            }}
          />
        </FormItem>
        <FormItem
          label="学习营详情"
          name="detail"
          rules={[{ required: true, message: "请输入学习营详情" }]}
        >
          <WibotEditor
            ref={WibotEditorRef}
            toolbarConfig={{
              excludeKeys: ["group-video"],
            }}
            onChangeHtml={(html) => {
              handleDetailsHtml(html)
              formForm.setFieldValue("detail", editorIsEmpty(html) ? "" : html)
            }}
          />
        </FormItem>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
      <MaterialModal params={materialModalParams} />
      {posterPreviewParams.visible && (
        <PosterPreviewModal {...posterPreviewParams} />
      )}
    </div>
  )
})

export default StudyCampStepOne
