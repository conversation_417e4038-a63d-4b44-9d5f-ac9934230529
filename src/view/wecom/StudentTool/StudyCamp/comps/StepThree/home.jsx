/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2022/11/29 10:15
 * @LastEditTime: 2023/06/20 18:02
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\StudentTool\StudyCamp\comps\StepThree\home.jsx
 * @Description: '步骤三'
 */

import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from 'react';
import {
  Spin,
  Button,
  Form,
  Select,
  Switch,
  DatePicker,
} from 'antd';
import moment from 'moment';
import { apiCall } from 'common/utils';
import { PlusOutlined } from '@ant-design/icons';
import LinkCard from 'components/LinkCard/home';
import ActivityFormModal from '../ActivityFormModal/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const StudyCampStepThree = forwardRef((props, ref) => {
  const { formForm, processingFlag, shelfState, handleModifyData } = props;

  useImperativeHandle(ref, () => ({
    getInitData,
  }));

  const [loading, setLoading] = useState(false);
  const [switchFlag, setSwitchFlag] = useState(true);
  const [linkCardData, setLinkCardData] = useState(null);
  const [signLinkCardData, setSignLinkCardData] = useState(null);
  const [shareLinkCardData, setShareLinkCardData] = useState(false);
  const [activityModalParams, setActivityModalParams] = useState({
    visible: false,
  });
  const [signInFlag, setSignInFlag] = useState(false);
  const [shareFlag, setShareFlag] = useState(false);
  const [sopEnable, setSopEnable] = useState(true);
  const [sopOption, setSopOption] = useState([]);

  useEffect(() => {
    getSopOption();
  }, []);

  // 获取SOP组
  const getSopOption = async () => {
    await apiCall('/sopGroup', 'GET')
      .then((res) => {
        setSopOption(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取详情数据
  const getInitData = (data) => {
    const { signUpFlag, signInFlag, shareFlag, formVO, signInVO, shareVO } =
      data;
    setSwitchFlag(signUpFlag);
    setSignInFlag(signInFlag);
    setShareFlag(shareFlag);
    setLinkCardData(formVO);
    setSignLinkCardData(signInVO);
    setShareLinkCardData(shareVO);
  };

  const onChangeSwitch = (val) => {
    setSwitchFlag(val);
  };

  // 选择活动
  const handleAddMsg = (type) => {
    let fieldData = localStorage.getItem('imageField')
      ? JSON.parse(localStorage.getItem('imageField'))
      : {};
    setActivityModalParams({
      visible: true,
      activityType: type,
      placeholder:
        type == 'SIGN'
          ? '签到活动标题、签到活动描述'
          : type == 'SHARE'
            ? '晒单活动标题、晒单活动描述'
            : '报名活动标题、报名活动描述',
      onSubmit: (data) => {
        setActivityModalParams({ visible: false });
        if (type == 'SIGN') {
          data.image = data.cover;
          setSignLinkCardData({ ...data });
          formForm.setFieldsValue({
            signInId: data.id,
          });
          localStorage.setItem(
            'imageField',
            JSON.stringify({ ...fieldData, signInVO: { ...data } })
          );
        } else if (type == 'SHARE') {
          data.image = data.cover;
          setShareLinkCardData({ ...data });
          formForm.setFieldsValue({
            shareId: data.id,
          });
          localStorage.setItem(
            'imageField',
            JSON.stringify({ ...fieldData, shareVO: { ...data } })
          );
        } else {
          setLinkCardData({ ...data });
          formForm.setFieldsValue({
            formId: data.id,
          });
          localStorage.setItem(
            'imageField',
            JSON.stringify({ ...fieldData, formVOSignUp: { ...data } })
          );
        }
      },
      onCancel: () => {
        setActivityModalParams({ visible: false });
      },
    });
  };

  // //签到活动
  // const handleAddSignMsg =()=>{
  //   let fieldData = localStorage.getItem('imageField') ? JSON.parse(localStorage.getItem('imageField')) : {};
  //   setActivityModalParams({
  //     visible: true,
  //     onSubmit: (data) => {
  //       setLinkCardData({ ...data });
  //       setActivityModalParams({ visible: false });
  //       formForm.setFieldsValue({
  //         formId: data.id,
  //         // formVOSignUp: { ...data }
  //       });
  //       localStorage.setItem('imageField', JSON.stringify({ ...fieldData, formVOSignUp: { ...data } }));
  //     },
  //     onCancel: () => {
  //       setActivityModalParams({ visible: false });
  //     }
  //   });
  // }

  return (
    <div className="StudyCampStepThree">
      <Spin spinning={loading}>
        <h2 className="card-title">营期活动</h2>
        <FormItem
          name="sopEnable"
          label="运营脚本"
          initialValue
          valuePropName="checked"
        >
          <Switch
            onChange={(val) => {
              setSopEnable(val);
            }}
            disabled={processingFlag || shelfState == 'soldOut'}
          />
        </FormItem>
        {sopEnable && (
          <FormItem
            name="sopGroupId"
            label="选择脚本"
            rules={[{ required: true, message: '请选择脚本' }]}
            extra="请选择客户群SOP组运营脚本"
          >
            <Select
              placeholder="选择SOP组"
              fieldNames={{ label: 'name', value: 'id' }}
              options={sopOption}
              allowClear
              showSearch
              showArrow
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
        )}
        <FormItem
          name="signUpFlag"
          label="报名活动"
          initialValue
          valuePropName="checked"
        >
          <Switch
            onChange={onChangeSwitch}
            disabled={processingFlag || shelfState == 'soldOut'}
          />
        </FormItem>
        {switchFlag && (
          <FormItem
            name="signUpTime"
            label="报名时间"
            rules={[{ required: true, message: '请选择报名时间' }]}
            extra="报名开始时间必须早于营期开始时间，开营后能继续报名"
          >
            <RangePicker
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              inputReadOnly
              disabled={processingFlag || shelfState == 'soldOut'}
              onChange={(date, dateString) => {
                let time = [];
                if (date) {
                  time[0] = moment(dateString[0]).format('YYYY/MM/DD HH:mm');
                  time[1] = moment(dateString[1]).format('YYYY/MM/DD HH:mm');
                }
                handleModifyData({ signUpTime: date ? time : null });
              }}
            />
          </FormItem>
        )}
        {switchFlag && (
          <FormItem
            name="formId"
            label="报名活动"
            rules={[{ required: true, message: '请关联报名活动' }]}
            extra="请勿选择已存在报名数据的报名活动，否则已报名的客户无法再次报名"
          >
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                disabled={processingFlag || shelfState == 'soldOut'}
                onClick={() => {
                  handleAddMsg('FORM');
                }}
              >
                关联报名活动
              </Button>
              {linkCardData && (
                <div style={{ marginTop: '20px' }}>
                  <LinkCard
                    data={{
                      title: linkCardData.shareTitle,
                      description: linkCardData.shareDescription,
                      image: linkCardData.shareImage,
                    }}
                    isLink={false}
                  />
                </div>
              )}
            </div>
          </FormItem>
        )}
        <FormItem
          name="signInId"
          label="签到活动"
          extra={
            <div>
              {signInFlag && signLinkCardData && (
                <div style={{ marginTop: '20px' }}>
                  <LinkCard data={signLinkCardData} />
                </div>
              )}
            </div>
          }
        >
          <div style={{ display: 'flex' }}>
            <FormItem
              name="signInFlag"
              valuePropName="checked"
              style={{ marginRight: '20px', marginBottom: '0px' }}
            >
              <Switch
                disabled={processingFlag || shelfState == 'soldOut'}
                onChange={(checked) => {
                  setSignInFlag(checked);
                }}
              />
            </FormItem>
            {signInFlag && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                disabled={processingFlag || shelfState == 'soldOut'}
                onClick={() => {
                  handleAddMsg('SIGN');
                }}
              >
                关联签到活动
              </Button>
            )}
          </div>
        </FormItem>
        <FormItem
          name="shareId"
          label="晒单活动"
          extra={
            <div>
              {shareFlag && shareLinkCardData && (
                <div style={{ marginTop: '20px' }}>
                  <LinkCard data={shareLinkCardData} />
                </div>
              )}
            </div>
          }
        >
          <div style={{ display: 'flex' }}>
            <FormItem
              name="shareFlag"
              valuePropName="checked"
              style={{ marginRight: '20px', marginBottom: '0px' }}
            >
              <Switch
                disabled={processingFlag || shelfState == 'soldOut'}
                onChange={(checked) => {
                  setShareFlag(checked);
                }}
              />
            </FormItem>
            {shareFlag && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                disabled={processingFlag || shelfState == 'soldOut'}
                onClick={() => {
                  handleAddMsg('SHARE');
                }}
              >
                关联晒单活动
              </Button>
            )}
          </div>
        </FormItem>
        {/* {
          isBask && <FormItem name="bask" label="晒单标题" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入晒单标题（30字内）" maxLength={30} allowClear />
          </FormItem>
        }
        {
          isBask && <FormItem name="ask" label="晒单要求">
            <TextArea maxLength={300} placeholder="请输入晒单要求（300字内）" allowClear />
          </FormItem>
        } */}
      </Spin>
      <ActivityFormModal params={activityModalParams} />
    </div>
  );
});

export default StudyCampStepThree;
