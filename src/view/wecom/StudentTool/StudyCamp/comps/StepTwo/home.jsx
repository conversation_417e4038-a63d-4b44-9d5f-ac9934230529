/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/29 10:15
 * @LastEditTime: 2023/06/29 14:17
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/StepTwo/home.jsx
 * @Description: '步骤二'
 */

import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import { Spin, Button, Form, Radio, Timeline, DatePicker, Collapse } from 'antd';
import moment from 'moment';
import { PlusOutlined, SaveOutlined, CloseCircleOutlined, FormOutlined } from '@ant-design/icons';
import CourseFormModal from '../CourseFormModal/home';
import './home.less';

const FormItem = Form.Item;
const { Panel } = Collapse;

const StudyCampStepTwo = forwardRef((props, ref) => {
  const { formForm, openDate, processingFlag, shelfState, handleModifyData } = props;

  useImperativeHandle(ref, () => ({
    getInitData,
  }));

  const [loading, setLoading] = useState(false);
  const [radioValue, setRadioValue] = useState('FREE');
  const [courseModalParams, setCourseModalParams] = useState({ visible: false });
  const [courseList, setCourseList] = useState([]);

  useEffect(() => { }, []);

  useEffect(() => {
    handleModifyData({ courseList: courseList });
  }, [courseList]);

  // 获取详情数据
  const getInitData = (data) => {
    console.log(data, 'datadata');
    setRadioValue(data.courseUnlockType);
    let list = data.courseVOList;
    setCourseList(list || []);
  };

  const onChangeRadio = (e) => {
    setRadioValue(e.target.value);
  };

  // 添加课程
  const handleAddMsg = () => {
    let fieldData = localStorage.getItem('imageField') ? JSON.parse(localStorage.getItem('imageField')) : {};
    setCourseModalParams({
      visible: true,
      onSubmit: (data) => {
        const newList = JSON.parse(JSON.stringify(courseList));
        newList.push(data);
        if (openDate) {
          newList.forEach((item, index) => {
            if (!item.unlockTime) {
              item.unlockTime = moment(openDate).add(index, 'days')
                .format('YYYY-MM-DD HH:mm');
            }
          });
        }
        setCourseList(newList);
        setCourseModalParams({ visible: false });
        formForm.setFieldsValue({
          courseIdList: newList.map((item) => item.id),
        });
        localStorage.setItem('imageField', JSON.stringify({ ...fieldData, courseList: newList }));
      },
      onCancel: () => {
        setCourseModalParams({ visible: false });
      }
    });
  };
  // 删除课程
  const handleDelMsg = (index) => {
    let fieldData = localStorage.getItem('imageField') ? JSON.parse(localStorage.getItem('imageField')) : {};
    const newList = JSON.parse(JSON.stringify(courseList));
    newList.splice(index, 1);
    setCourseList(newList);
    if (newList.length == 0) {
      formForm.setFieldsValue({
        courseIdList: []
      });
      localStorage.setItem('imageField', JSON.stringify({ ...fieldData, courseList: [] }));
    } else {
      formForm.setFieldsValue({
        courseIdList: newList.map((item) => item.id)
      });
      localStorage.setItem('imageField', JSON.stringify({ ...fieldData, courseList: newList }));
    }
  };

  // 首个课程的开课时间必须大于或等于开营时间
  const disabledDate = (current) => {
    if (openDate) {
      return current < moment(openDate);
    }
  };

  const disabledTime = (date) => {
    if (openDate) {
      let currentDay = moment(openDate).date();    // 开营时间
      let currentHours = moment(openDate).hours();
      let currentMinutes = moment(openDate).minutes();
      let settingHours = moment(date).hours(); // 设置的时间
      let settingDay = moment(date).date();
      if (date && settingDay === currentDay && settingHours === currentHours) {
        return {
          disabledHours: () => range(0, currentHours - 1),         // 设置为当天现在这小时，禁用该小时，该分钟之前的时间
          disabledMinutes: () => range(0, currentMinutes - 1),
        };
      } else if (date && settingDay === currentDay && settingHours > currentHours) {
        return {
          disabledHours: () => range(0, currentHours - 1),      // 设置为当天现在这小时之后，只禁用当天该小时之前的时间
        };
      } else if (date && settingDay === currentDay && settingHours < currentHours) {
        return {
          disabledHours: () => range(0, currentHours - 1),      // 若先设置了的小时小于当前的，再设置日期为当天，需要禁用当天现在这小时之前的时间和所有的分
          disabledMinutes: () => range(0, 59),
        };
      } else if (date && settingDay > currentDay) {
        return {
          disabledHours: () => [],                     // 设置为当天之后的日期，则不应有任何时间分钟的限制
          disabledMinutes: () => [],
        };
      }
    }
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i <= end; i++) {
      result.push(i);
    }
    return result;
  };

  return (
    <div className='StudyCampStepTwo'>
      <Spin spinning={loading}>
        <h2 className='card-title'>课程安排</h2>
        <FormItem name="courseUnlockType" label="课程解锁方式" initialValue="FREE" rules={[{ required: true, message: '请选择课程解锁方式' }]} extra={
          <div>
            <div>自由模式：学员可随时查看任意课程。</div>
            <div>闯关解锁：按照学习任务由上到下自动解锁下一任务</div>
            <div>按日期解锁：根据日期变动自动解锁，需对每个课程及考试单独进行设置，且时间必填，首个解锁时间需等于开营时间</div>
          </div>
        }>
          <Radio.Group onChange={onChangeRadio} disabled={processingFlag || shelfState == 'soldOut'} >
            <Radio value='FREE'>自由模式</Radio>
            <Radio value='BREAK'>闯关解锁</Radio>
            <Radio value='DATE'>按日期解锁</Radio>
          </Radio.Group>
        </FormItem>
        {/* {
          radioValue == 'BREAK' && <FormItem name="breakConditionType" label="闯关解锁条件" initialValue="FINISH_STUDY" rules={[{ required: true, message: '请选择课程解锁方式' }]}>
            <Radio.Group >
              <Radio value='FINISH_STUDY'>学习完成</Radio>
              <Radio value='PASS_EXAM'>考试通过</Radio>
              <Radio value='FINISH_HOMEWORK'>完成作业</Radio>
            </Radio.Group>
          </FormItem>
        } */}
        <FormItem name="courseIdList" label="课程" wrapperCol={{ xs: { span: 24 }, sm: { span: 18 } }} rules={[{ required: true, message: '请添加课程' }]}>
          <div className='msg-wrap'>
            <Button type="primary" icon={<PlusOutlined />} disabled={processingFlag || shelfState == 'soldOut'} onClick={handleAddMsg}>添加课程</Button>
            {
              courseList.length > 0 && <Timeline className='timeline-box'>
                {
                  courseList.map((item, index) => (
                    <Timeline.Item key={index} dot={<div className='timeline-dot'>
                      {index + 1}
                    </div>}>
                      <Collapse>
                        <Panel header={<div>
                          <div>{item.name}</div>
                          {
                            radioValue == 'DATE' && <div onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                            }}>
                              <span style={{ cursor: 'auto' }}>解锁时间：</span>
                              {
                                item.isEdit ? <DatePicker
                                  disabled={shelfState == 'soldOut'}
                                  format="YYYY-MM-DD HH:mm"
                                  showTime={{ defaultValue: moment('00:00:00', 'HH:mm') }}
                                  value={item.unlockTime ? moment(item.unlockTime, 'YYYY-MM-DD HH:mm') : null}
                                  disabledDate={disabledDate}
                                  disabledTime={disabledTime}
                                  onChange={(date, dateString) => {
                                    let newList = JSON.parse(JSON.stringify(courseList));
                                    newList[index].unlockTime = dateString;
                                    setCourseList(newList);
                                  }}
                                /> : <span style={{ cursor: 'auto' }}>{item.unlockTime || '-'}</span>
                              }
                              {item.isEdit ? (shelfState != 'soldOut' && <SaveOutlined className="save-icon" onClick={(e) => {
                                let newList = JSON.parse(JSON.stringify(courseList));
                                newList[index].isEdit = false;
                                setCourseList(newList);
                              }} />)
                                : (shelfState != 'soldOut' && <FormOutlined className="edit-icon" onClick={(e) => {
                                  let newList = JSON.parse(JSON.stringify(courseList));
                                  newList[index].isEdit = true;
                                  setCourseList(newList);
                                }} />)}
                            </div>
                          }
                        </div>} key={index}>
                          {
                            (item.courseItemVOList?.length > 0 || item.examVO) ? <div>
                              {
                                item.courseItemVOList.length > 0 && <Collapse defaultActiveKey={0}>
                                  {item.courseItemVOList.map((atem, andex) => (
                                    <Panel header={atem.name} key={`${andex}`}>
                                      <p>
                                        {(atem.coursewareSetting?.name && atem.examVO?.name) && '01.'}{atem.coursewareSetting?.name}
                                      </p>
                                      <p>{(atem.coursewareSetting?.name && atem.examVO?.name) && '02.'}{atem.examVO?.name}</p>
                                    </Panel>
                                  ))}
                                </Collapse>
                              }
                              {
                                item.examVO && <Collapse defaultActiveKey={0}>
                                  <Panel header="考试" key={0}>
                                    <div>{item.examVO.name}</div>
                                  </Panel>
                                </Collapse>
                              }
                            </div> : '暂无数据'
                          }

                        </Panel>
                      </Collapse>
                      {shelfState != 'soldOut' && <CloseCircleOutlined className="tagClose" onClick={() => handleDelMsg(index)} />}
                    </Timeline.Item>
                  ))
                }
              </Timeline>
            }
          </div>
        </FormItem>
      </Spin>
      <CourseFormModal params={courseModalParams} />
    </div>
  );
});

export default StudyCampStepTwo;

