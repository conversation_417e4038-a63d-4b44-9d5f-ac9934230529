/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2023/11/15 11:31
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsFive/home.jsx
 * @Description: '数据明细-证书领取记录'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
  Image,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const TabDetailsFive = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学员名称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "领取时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      sorter: (a, b) => timeStamp(a.time) - timeStamp(b.time),
    },
    {
      title: "证书预览",
      width: "160px",
      dataIndex: "rewardPicture",
      key: "rewardPicture",
      align: "center",
      render: (value, record, index) => (
        <FileHOC src={value || "error"}>
          {(url) => (
            <Image
              width={60}
              height={60}
              src={url}
              fallback="images/fallbackImg.png"
              style={{ objectFit: "cover" }}
              preview
            />
          )}
        </FileHOC>
      ),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { detailId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        id: detailId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/study/studyCamp/detail/data/reward", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsFive">
      <Form layout={"inline"} ref={formRef}>
        <FormItem
          name="customerName"
          getValueFromEvent={(e) => removeInputEmpty(e)}
        >
          <Input placeholder="学员名称" allowClear />
        </FormItem>
        <FormItem name="time" label="领取时间">
          <RangePicker />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="customerId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(TabDetailsFive);
