/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2024/10/29 14:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsOne/home.jsx
 * @Description: '数据明细-访问明细'
 */

import React, { useEffect, useRef, useState } from "react";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
  Spin,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const TabDetailsOne = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学员名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "count",
      key: "count",
      align: "center",
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    fetchList({ studyCampId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { studyCampId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        id: studyCampId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/study/studyCamp/detail/data/customer", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const data = {
        id,
        ...formData,
      };
      apiCall(
        "/study/studyCamp/detail/data/customer/export",
        "POST",
        data,
        null,
        {
          isExit: true,
          title: `访问明细.${moment().format("YYYY-MM-DD")}.xlsx`,
        }
      )
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsOne">
      <Spin spinning={loading}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="学员名称" allowClear />
          </FormItem>
          <FormItem name="time" label="访问时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div
          className="flex flex-space-between"
          style={{ marginBottom: "20px" }}
        >
          <div>
            <Button
              type="primary"
              onClick={() => handleQuery()}
              style={{ marginRight: "20px" }}
            >
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
        <Card bordered={false} bodyStyle={{ padding: "unset" }}>
          <Table
            rowKey="customerId"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default TabDetailsOne;
