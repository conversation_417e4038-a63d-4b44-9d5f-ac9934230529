/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/09/01 14:20
 * @LastEditTime: 2023/09/04 17:28
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsRight/comps/ResultModal/home.jsx
 * @Description: '数据明细-考试统计-考试结果对话框'
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Radio,
  Space,
} from 'antd';
import "./home.less"

const FormItem = Form.Item;

const ResultModal = (props) => {
  const { record = {}, visible, onCancel } = props.params;
  const [formItemList, setFormItemList] = useState([])

  useEffect(() => {
    if (visible) {
      const { formItemList } = record.saveExamResultVO;
      if (formItemList.length) {
        const newFormItemList = formItemList.map((item, index) => {
          // 答案
          item.answerList = item.optionList.map((atem, andex) => {
            if (atem.answer) {
              return String.fromCharCode(andex + 65)
            }
          }).filter((s) => s && s.trim())
          // 选择
          item.selectList = item.optionList.map((atem, andex) => {
            if (atem.checked) {
              return String.fromCharCode(andex + 65)
            }
          }).filter((s) => s && s.trim())

          if (item.type == 'FILL_BLANK') {
            return {
              ...item,
              order: index + 1,
              textContentList: item.textContent.split('#').filter(item => item).map((atem, andex) => ({
                text: atem,
                selected: item.customerAnswer[andex] == '________' ? '' : item.customerAnswer[andex]
              }))
            }
          } else {
            return {
              ...item,
              order: index + 1
            }
          }
        })
        setFormItemList(newFormItemList)
      }
    }
  }, [visible]);

  useEffect(() => {
    console.log(formItemList, 'formItemList-formItemList');
    if (formItemList.length) {
      formItemList.forEach((item, index) => {
        if (item.type == 'FILL_BLANK') {
          item.textContentList.forEach((atem, andex) => {
            // 填空错误处理
            document.querySelectorAll('.fillBlank-group .field-area').forEach((btem, bndex) => {
              if (btem.className.includes('fillBlank' + andex)) {
                if (item.customerAnswer.filter((item) => item)[bndex] != item.correctAnswer[bndex]) {
                  btem.style.borderBottom = '1px solid #f01d29';
                  btem.style.color = '#f01d29'
                } else {
                  btem.style.borderBottom = '1px solid #49bb18';
                  btem.style.color = '#49bb18'
                }
              }
            })
          })
        }
      })
    }
  }, [formItemList]);

  const topicTitle = (item, index) => {
    const { type, point } = item;
    let typeName = '';
    switch (type) {
      case 'RADIO':
        typeName = '单选题'
        break;
      case 'CHECKBOX':
        typeName = '多选题'
        break;
      case 'TEXTAREA':
        typeName = '问答题'
        break;
      case 'FILL_BLANK':
        typeName = '填空题'
        break;
    }
    return `${index + 1}.（${typeName}，分值${point}）${item.title}`;
  };

  const logoMark = (item) => {
    const { type, selectList, answerList, textContent, customerAnswer, correctAnswer } = item;
    let state = '0';
    if (type == 'RADIO' || type == 'CHECKBOX') {
      if (selectList.length) {
        (String(selectList) == String(answerList)) ? state = '1' : state = '2'
      }
    } else if (type == 'TEXTAREA') {
      textContent ? state = '1' : state = '0'
    } else if (type == 'FILL_BLANK') {
      if (customerAnswer.filter((item) => item).length) {
        (String(customerAnswer.filter((item) => item)) == String(correctAnswer)) ? state = '1' : state = '2'
      }
    }
    switch (state) {
      case '0':
        return <span className='logoMark'>未作答</span>
      case '1':
        return <span className='logoMark right'>回答正确</span>
      case '2':
        return <span className='logoMark wrong'>回答错误</span>
    }
  }

  return (
    <Modal
      className='ResultModal-Container'
      title={'学员：' + record.customerName}
      visible={visible}
      destroyOnClose
      footer={null}
      onCancel={onCancel}
    >
      <Form className='nonePointer' layout='vertical'>
        {
          formItemList?.map((item, index) => (
            <FormItem
              key={index}
              label={topicTitle(item, index)}
            >
              {
                (item.type == 'RADIO' || item.type == 'CHECKBOX') && <Space style={{ width: '100%' }} direction="vertical">
                  {
                    item.optionList.map((atem, andex) => (
                      <Radio key={andex} checked={atem.checked}>{String.fromCharCode(andex + 65)}.{atem.name}</Radio>
                    ))
                  }
                  <div style={{ padding: '10px', background: '#f3f5f8' }}>
                    <div>学员答案：{item.selectList.join("、")}</div>
                    <div>正确答案：{item.answerList.join("、")}</div>
                    {logoMark(item)}
                  </div>
                </Space>
              }

              {
                item.type == 'TEXTAREA' && <Space style={{ width: '100%' }} direction="vertical">
                  <div style={{ padding: '10px', background: '#f3f5f8' }}>
                    <div> {item.textContent}</div>
                    {logoMark(item)}
                  </div>
                </Space>
              }

              {
                item.type == 'FILL_BLANK' && <Space style={{ width: '100%' }} direction="vertical">
                  <div className='fillBlank-group'>
                    {
                      item.textContentList.map((atem, andex) => (
                        atem.text == '________' ? <span className={`field-area ${'fillBlank' + andex}`} key={andex}>{atem.selected}</span> : <span key={andex}>{atem.text}</span>
                      ))
                    }
                  </div>
                  <div style={{ padding: '10px', background: '#f3f5f8' }}>
                    <div>正确答案：{item.correctAnswer.join("、")}</div>
                    {logoMark(item)}
                  </div>
                </Space>
              }
            </FormItem>
          ))
        }
      </Form>
    </Modal>
  );
};

export default ResultModal;
