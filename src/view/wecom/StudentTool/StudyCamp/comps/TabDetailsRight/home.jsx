/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2023/11/16 14:39
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsRight/home.jsx
 * @Description: '数据明细-考试统计'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
  Select
} from "antd";
import { apiCall } from "common/utils";
import { qs2obj } from "common/object";
import moment from "moment";
import ResultModal from "./comps/ResultModal/home"

const FormItem = Form.Item;

const TabDetailsEight = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [examList, setExamList] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学员",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "考试名称",
      width: "160px",
      dataIndex: "examName",
      key: "examName",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "课程名称",
      width: "160px",
      dataIndex: "courseName",
      key: "courseName",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "答对题目数",
      width: "160px",
      dataIndex: "rightCount",
      key: "rightCount",
      align: "center",
    },
    {
      title: "答错题目数",
      width: "160px",
      dataIndex: "wrongCount",
      key: "wrongCount",
      align: "center",
    },
    {
      title: "题目总数",
      width: "160px",
      dataIndex: "totalCount",
      key: "totalCount",
      align: "center",
    },
    {
      title: "分数",
      width: "160px",
      dataIndex: "point",
      key: "point",
      align: "center",
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleView(record)}>查看</a>
        </>
      ),
    },
  ];
  const [ResultParams, setResultParams] = useState({ visible: false });

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    setId(id);
  }, []);

  useEffect(() => {
    if (id) {
      getExamList();
      fetchList();
    }
  }, [id]);

  // 获取当前学习营的考试列表
  const getExamList = () => {
    setLoading(true)
    const data = {
      studyCampId: id,
    }
    apiCall('/study/studyCamp/detail/data/exam/list', 'GET', data)
      .then((res) => {
        setExamList(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        studyCampId: id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/study/studyCamp/detail/data/exam", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(false);
      const data = {
        studyCampId: id,
        ...formData,
      };
      apiCall('/study/studyCamp/detail/data/exam/export', 'GET', data, null, {
        isExit: true,
        title: `考试统计.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleView = (record) => {
    setResultParams({
      record: record,
      visible: true,
      onCancel: () => {
        setResultParams({ visible: false })
      }
    })
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsEight">
      <Form layout={"inline"} ref={formRef}>
        <FormItem label="选择考试" name="examId">
          <Select
            style={{ width: "200px" }}
            options={examList}
            showSearch
            allowClear
            placeholder="请选择"
            fieldNames={{ label: 'name', value: 'id' }}
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
        <div>
          <Button type="primary" onClick={() => handleExport()}>
            导出列表
          </Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="customerId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={paginations}
          onChange={onChangeTable}
          scroll={{ x: 1300 }}
        />
      </Card>
      <ResultModal params={ResultParams} />
    </div>
  );
};

export default withRouter(TabDetailsEight);
