/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2023/11/17 11:16
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsSeven/home.jsx
 * @Description: '数据明细-课程完成情况'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Table,
  Tooltip,
  Select
} from "antd";
import { apiCall } from "common/utils";
import { qs2obj } from "common/object";
import moment from "moment";

const FormItem = Form.Item;

const TabDetailsSeven = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [courseList, setCourseList] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value) => {
        switch (value) {
          case 'COURSE_WARE':
            value = '视频'
            break;
          case 'EXAM':
            value = '考试'
            break;
          case 'HOMEWORK':
            value = '作业'
            break;
        }
        return <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      },
    },
    {
      title: "所属课程",
      width: "160px",
      dataIndex: "courseName",
      key: "courseName",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "完成人数",
      width: "160px",
      dataIndex: "finishCount",
      key: "finishCount",
      align: "center",
    },
    {
      title: "完成率",
      width: "160px",
      dataIndex: "finishRate",
      key: "finishRate",
      align: "center",
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    setId(id);
  }, []);

  useEffect(() => {
    if (id) {
      getCourseList();
      fetchList();
    }
  }, [id]);

  // 获取当前学习营的课程列表
  const getCourseList = () => {
    setLoading(true)
    const data = {
      studyCampId: id,
    }
    apiCall('/study/studyCamp/detail/data/course/list', 'GET', data)
      .then((res) => {
        setCourseList(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false)
      });
  };

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        studyCampId: id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall("/study/studyCamp/detail/data/course", "GET", data)
        .then((res) => {
          // const { records, current, size, total, pages } = res;
          setDataSource(res);
          // setPaginations({
          //   current: current,
          //   pageSize: size,
          //   total: total,
          //   showQuickJumper: true,
          //   showSizeChanger: true,
          //   showTotal: (total, range) =>
          //     `共 ${total} 条记录 第${current}/${pages}页`,
          // });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(false);
      const data = {
        studyCampId: id,
        ...formData,
      };
      apiCall('/study/studyCamp/detail/data/course/export', 'GET', data, null, {
        isExit: true,
        title: `课程完成情况.${moment().format('YYYY-MM-DD')}.xlsx`,
      })
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsSeven">
      <Form layout={"inline"} ref={formRef}>
        <FormItem label="选择课程" name="courseId">
          <Select
            style={{ width: "200px" }}
            options={courseList}
            showSearch
            allowClear
            placeholder="请选择"
            fieldNames={{ label: 'name', value: 'id' }}
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </FormItem>
      </Form>
      <div className="flex flex-space-between" style={{ marginBottom: "20px" }}>
        <div>
          <Button
            type="primary"
            onClick={() => handleQuery()}
            style={{ marginRight: "20px" }}
          >
            查询
          </Button>
          <Button onClick={() => handleReset()}>重置筛选</Button>
        </div>
        <div>
          <Button type="primary" onClick={() => handleExport()}>
            导出列表
          </Button>
        </div>
      </div>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="courseId"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          onChange={onChangeTable}
          scroll={{ x: 1300 }}
        />
      </Card>
    </div>
  );
};

export default withRouter(TabDetailsSeven);
