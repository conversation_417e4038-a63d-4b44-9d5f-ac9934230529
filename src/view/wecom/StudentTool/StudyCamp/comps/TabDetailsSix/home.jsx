/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2023/11/17 11:16
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsSix/home.jsx
 * @Description: '数据明细-推广渠道'
 */

import React, { useEffect, useState } from 'react';
import { Card, Table, Tooltip, } from 'antd';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { qs2obj } from 'common/object';
import moment from 'moment';

const TabDetailsSix = (props) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '渠道名称',
      width: '160px',
      dataIndex: 'channelName',
      key: 'channelName',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: '访问人数',
      width: '160px',
      dataIndex: 'visitorCount',
      key: 'visitorCount',
      align: 'center',
    },
    {
      title: '访问次数',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
    }
  ];

  useEffect(() => {
    const { id } = qs2obj(window.location.search);
    fetchList({ studyCampId: id });
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { studyCampId, pagination, query } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      studyCampId: studyCampId,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    apiCall('/study/studyCamp/studyCampChannel', 'GET', data).then((res) => {
      setDataSource(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className='TabDetailsSix'>
      <Card bordered={false} bodyStyle={{ padding: 'unset' }}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={false} />
      </Card>
    </div>
  );
};

export default TabDetailsSix;
