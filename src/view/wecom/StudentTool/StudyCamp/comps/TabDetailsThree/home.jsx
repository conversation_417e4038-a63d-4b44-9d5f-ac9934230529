/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2024/11/14 17:17
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsThree/home.jsx
 * @Description: '数据明细-签到记录'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Avatar,
  Spin,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const TabDetailsThree = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "客户名称",
      width: "220px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "首次打卡时间",
      width: "160px",
      dataIndex: "firstSignInTime",
      key: "firstSignInTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstSignInTime) - timeStamp(b.firstSignInTime),
    },
    {
      title: "最近打卡时间",
      width: "160px",
      dataIndex: "lastSignInTime",
      key: "lastSignInTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastSignInTime) - timeStamp(b.lastSignInTime),
    },
    {
      title: "累计打卡天数",
      width: "160px",
      dataIndex: "signInDayCount",
      key: "signInDayCount",
      align: "center",
    },
    // {
    //   title: '缺卡天数',
    //   width: '160px',
    //   dataIndex: 'createEmployeeName',
    //   key: 'createEmployeeName',
    //   align: 'center',
    // },
    // {
    //   title: '助力补卡天数',
    //   width: '160px',
    //   dataIndex: 'createEmployeeName',
    //   key: 'createEmployeeName',
    //   align: 'center',
    // }
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { detailId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        studyCampId: detailId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/study/studyCamp/detail/data/signIn", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  //导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const data = {
        studyCampId: id,
        ...formData,
      };
      apiCall(
        "/study/studyCamp/detail/data/signIn/export",
        "POST",
        data,
        null,
        {
          isExit: true,
          title: `签到记录.${moment().format("YYYY-MM-DD")}.xlsx`,
        }
      )
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsThree">
      <Spin spinning={loading}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="学员名称" allowClear />
          </FormItem>
          <FormItem name="time" label="最近打卡时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div
          className="flex flex-space-between"
          style={{ marginBottom: "20px" }}
        >
          <div>
            <Button
              type="primary"
              onClick={() => handleQuery()}
              style={{ marginRight: "20px" }}
            >
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
        <Card bordered={false} bodyStyle={{ padding: "unset" }}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(TabDetailsThree);
