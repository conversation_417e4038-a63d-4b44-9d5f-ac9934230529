/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 16:57
 * @LastEditTime: 2024/11/14 17:16
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/comps/TabDetailsTwo/home.jsx
 * @Description: '数据明细-学员信息'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  DatePicker,
  Select,
  Avatar,
  Typography,
  Image,
  Spin,
} from "antd";
import { FormOutlined } from "@ant-design/icons";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import { qs2obj } from "common/object";
import moment from "moment";
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import { QrCodeBase } from "common/qrcode";
import { saveAs } from "file-saver";
import "./home.less";
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Paragraph } = Typography;

const TabDetailsTwo = (props) => {
  const formRef = useRef(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [columnsArr, setColumnsArr] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学员名称",
      width: "160px",
      dataIndex: "customerName",
      key: "customerName",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.customerAvatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "120px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "报名邀请员工",
      width: "180px",
      dataIndex: "employeeName",
      key: "employeeName",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.isEdit ? (
            <ETypeTransferModal
              title="选择员工"
              onlyEmployee
              multiple={false}
              onChange={(value, options) => {
                const data = {
                  employeeId: value?.length > 0 ? value[0] : null,
                  id: record.id,
                };
                apiCall("/study/studyCamp/detail/data/student", "POST", data)
                  .then((res) => {
                    fetchList();
                  })
                  .catch((err) => {
                    console.log(err);
                  })
                  .finally(() => { });
              }}
            />
          ) : (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {record.employeeAvatar && (
                <Avatar size={40} src={record.employeeAvatar} />
              )}
              {(value || record.departmentName) && (
                <div
                  style={{
                    marginLeft: "6px",
                    width: "120px",
                    textAlign: "left",
                    whiteSpace: "normal",
                  }}
                >
                  <div>{value}</div>
                  <span>{record.departmentName}</span>
                </div>
              )}
              <FormOutlined
                className="delete-icon"
                onClick={() => {
                  let newDataSource = JSON.parse(JSON.stringify(dataSource));
                  newDataSource[index].isEdit = true;
                  setDataSource(newDataSource);
                }}
              />
            </div>
          )}
        </>
      ),
    },
    {
      title: "首次访问时间",
      width: "160px",
      dataIndex: "firstVisitTime",
      key: "firstVisitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: "最近访问时间",
      width: "160px",
      dataIndex: "visitTime",
      key: "visitTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: "累计访问次数",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
    },
    {
      title: "学习进度",
      width: "160px",
      dataIndex: "studyStatusStr",
      key: "studyStatusStr",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "开始学习时间",
      width: "160px",
      dataIndex: "studyStartTime",
      key: "studyStartTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.studyStartTime) - timeStamp(b.studyStartTime),
    },
    {
      title: "最近学习时间",
      width: "160px",
      dataIndex: "lastStudyTime",
      key: "lastStudyTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastStudyTime) - timeStamp(b.lastStudyTime),
    },
    {
      title: "学习完成时间",
      width: "160px",
      dataIndex: "studyEndTime",
      key: "studyEndTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.studyEndTime) - timeStamp(b.studyEndTime),
    },
    {
      title: "最近签到时间",
      width: "160px",
      dataIndex: "lastCheckInTime",
      key: "lastCheckInTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.lastCheckInTime) - timeStamp(b.lastCheckInTime),
    },
    {
      title: "报名时间",
      width: "160px",
      dataIndex: "signUpTime",
      key: "signUpTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.signUpTime) - timeStamp(b.signUpTime),
    },
    ...columnsArr,
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    fetchList({ detailId: id });
    setId(id);
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }

      const { detailId, pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        id: detailId || id,
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/study/studyCamp/detail/data/student", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res.value;

          const trArr = res.key.map((item, index) => ({
            title: `${item}`,
            width: "160px",
            dataIndex: `th${index + 1}`,
            key: `th${index + 1}`,
            ellipsis: "true",
            align: "center",
            render: (value, record) => {
              let content = "";
              if (record.optionList[index] instanceof Array) {
                content =
                  record.optionList[index].length > 0 &&
                    !record.optionList[index].some((atem, andex) =>
                      atem.includes("http")
                    ) ? (
                    <Tooltip
                      title={<div>{record.optionList[index]?.join("、")}</div>}
                    >
                      <div>{record.optionList[index]?.join("、")}</div>
                    </Tooltip>
                  ) : (
                    record.optionList[index].map((atem, andex) => {
                      if (checkSuffix(atem) == "IMAGE") {
                        return (
                          <FileHOC src={atem || "error"}>
                            {(url) => (
                              <Image
                                key={andex}
                                width={60}
                                height={60}
                                src={url}
                                fallback="images/fallbackImg.png"
                                style={{ objectFit: "cover" }}
                                preview
                              />
                            )}
                          </FileHOC>
                        );
                      } else if (checkSuffix(atem) == "VIDEO") {
                        return (
                          <div key={andex}>
                            <FileHOC src={atem}>
                              {(url) => (
                                <video
                                  style={{ maxWidth: "200px", maxHeight: "100px" }}
                                  controls
                                  src={url}
                                />
                              )}
                            </FileHOC>
                          </div>
                        );
                      } else {
                        return (
                          <a
                            key={andex}
                            onClick={() => {
                              saveAs(atem, unescapeFile(atem));
                            }}
                          >
                            {unescapeFile(atem)}
                          </a>
                        );
                      }
                    })
                  );
              } else {
                content = (
                  <Tooltip placement="topLeft" title={record.optionList[index]}>
                    {record.optionList[index]}
                  </Tooltip>
                );
              }
              return content;
            },
          }));
          setColumnsArr(trArr);

          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 判断文件类型
  const checkSuffix = (url) => {
    const imageRegex = /\.(jpg|png|gif|bmp|jpeg|psd)$/;
    const videoRegex = /\.(avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv|mp4)$/;
    if (imageRegex.test(url.toLowerCase())) {
      return "IMAGE";
    } else if (videoRegex.test(url.toLowerCase())) {
      return "VIDEO";
    } else {
      return "FILE";
    }
  };

  // 编码文件链接中的中文
  const unescapeFile = (url) => {
    const reg = /[\u4e00-\u9fa5]/g;
    if (url.match(reg)) {
      const texts = url.match(reg).join("");
      const index = url.indexOf(texts);
      return url.substring(index, url.length);
    } else {
      const index = url.lastIndexOf("/");
      return url.substring(index + 1, url.length);
    }
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 导出
  const handleExport = () => {
    formRef.current.validateFields().then((formData) => {
      setLoading(true);
      if (formData.time) {
        formData.startDate = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endDate = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const data = {
        id,
        ...formData,
      };
      apiCall(
        "/study/studyCamp/detail/data/student/export",
        "POST",
        data,
        null,
        {
          isExit: true,
          title: `学员信息.${moment().format("YYYY-MM-DD")}.xlsx`,
        }
      )
        .then((res) => { })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="TabDetailsTwo">
      <Spin spinning={loading}>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="customerName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="学员名称" allowClear />
          </FormItem>
          <FormItem name="studyStatus" label="学习进度">
            <Select placeholder="学习进度" allowClear>
              <Option value="NO_OPEN">未学习</Option>
              <Option value="STUDYING">学习中</Option>
              <Option value="END">已完成</Option>
            </Select>
            {/* <SysDictSelect placeholder="学习进度" dataset="WECOM_CODE_TYPE" /> */}
          </FormItem>
          <FormItem name="time" label="学习完成时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div
          className="flex flex-space-between"
          style={{ marginBottom: "20px" }}
        >
          <div>
            <Button
              type="primary"
              onClick={() => handleQuery()}
              style={{ marginRight: "20px" }}
            >
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleExport()}>
              导出
            </Button>
          </div>
        </div>
        <Card bordered={false} bodyStyle={{ padding: "unset" }}>
          <Table
            rowKey="id"
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: "max-content" }}
            pagination={paginations}
            onChange={onChangeTable}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(TabDetailsTwo);
