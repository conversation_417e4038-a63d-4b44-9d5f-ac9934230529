/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/30 15:43
 * @LastEditTime: 2025/05/14 14:20
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/details.jsx
 * @Description: '学习营详情'
 */

import React, { useEffect, useState, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Tooltip,
  Row,
  Col,
  Empty,
  Tag,
  Image,
  Tabs,
  Form,
  DatePicker,
  Select,
  Descriptions,
  Table,
} from "antd"
import { QuestionCircleOutlined } from "@ant-design/icons"
import { apiCall } from "common/utils"
import { getDay } from "common/date"
import moment from "moment"
import { QrCodeBase } from "common/qrcode"
import { saveAs } from "file-saver"
import { qs2obj } from "common/object"
import SysDictLabel from "components/select/SysDictLabel"
import TabDetailsOne from "./comps/TabDetailsOne/home"
import TabDetailsTwo from "./comps/TabDetailsTwo/home"
import TabDetailsThree from "./comps/TabDetailsThree/home"
import TabDetailsFour from "./comps/TabDetailsFour/home"
import TabDetailsFive from "./comps/TabDetailsFive/home"
import TabDetailsSix from "./comps/TabDetailsSix/home"
// import TabDetailsSeven from './comps/TabDetailsSeven/home';
import TabDetailsRight from "./comps/TabDetailsRight/home"
import { DualAxes, Line } from "@ant-design/plots"
import WibotCopyBtn from "components/WibotCopyBtn/home"
import { FileHOC } from "components/FileHOC/FileHOC"
import "./details.less"

const { TabPane } = Tabs
const FormItem = Form.Item
const { RangePicker } = DatePicker

const StudyCampDetails = (props) => {
  const [id, setId] = useState(null)
  const formRef = useRef(null)
  const [loading, setLoading] = useState(false)
  const [userInfo, setUserInfo] = useState({})
  const [dataTrendVO, setDataTrendVO] = useState({})
  const [tabsTrendIndex, setTabsTrendIndex] = useState("STUDY_CUSTOMER_COUNT")
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState("1")
  const [dualAxesData, setDualAxesData] = useState([])
  const [lineData, setLineData] = useState({})
  const [finishRewardVisable, setFinishRewardVisable] = useState(false)
  const [dataSource, setDataSource] = useState([])
  const columns = [
    {
      title: "课程名称",
      width: "160px",
      dataIndex: "courseName",
      key: "courseName",
      align: "center",
      render: (value, record, index) => {
        const obj = {
          children: (
            <Tooltip placement="topLeft" title={value}>
              {value}
            </Tooltip>
          ),
          props: {},
        }
        obj.props.rowSpan = record.num
        return obj
      },
    },
    {
      title: "课件名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "课件类型",
      width: "160px",
      dataIndex: "type",
      key: "type",
      align: "center",
      render: (value) => {
        switch (value) {
          case "COURSE_WARE":
            value = "视频"
            break
          case "EXAM":
            value = "考试"
            break
          case "HOMEWORK":
            value = "作业"
            break
        }
        return (
          <Tooltip placement="topLeft" title={value}>
            {value}
          </Tooltip>
        )
      },
    },
    {
      title: "参与人数",
      width: "160px",
      dataIndex: "joinCount",
      key: "joinCount",
      align: "center",
    },
    {
      title: "完成人数",
      width: "160px",
      dataIndex: "finishCount",
      key: "finishCount",
      align: "center",
    },
    {
      title: "完成率",
      width: "160px",
      dataIndex: "finishRate",
      key: "finishRate",
      align: "center",
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <WibotCopyBtn text={record.url} title="复制链接" />
        </>
      ),
    },
  ]
  const configLine = {
    data: lineData.data,
    height: 300,
    xField: "date",
    yField: "number",
    label: {},
    point: {
      size: 5,
      shape: "diamond",
      style: {
        fill: "white",
        stroke: "#5B8FF9",
        lineWidth: 2,
      },
    },
    tooltip: {
      showMarkers: false,
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: "#000",
          fill: "red",
        },
      },
    },
    interactions: [
      {
        type: "marker-active",
      },
    ],
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: true,
        rotate: -45, //倾斜角度
        offset: "20",
        formatter: function (value) {
          if (value.length > 4) {
            return value.substring(5)
            return `${value.slice(0, 4)}...`
          }
          return value
        },
      },
    },
    meta: {
      number: {
        alias: lineData.title,
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
  }
  const configDualAxes = {
    data: [dualAxesData, dualAxesData],
    xField: "channelName",
    yField: ["visitorCount", "visitCount"],
    height: 300,
    legend: {
      position: "top",
    },
    meta: {
      visitorCount: {
        alias: "访问人数",
      },
      visitCount: {
        alias: "访问次数",
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
    geometryOptions: [
      {
        geometry: "line",
        color: "#5B8FF9",
      },
      {
        geometry: "line",
        color: "#5AD8A6",
      },
    ],
  }

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
    }
  }, [])

  useEffect(() => {
    if (id) {
      init(id)
    }
  }, [id])

  const init = async (id) => {
    await getInfoData({ detailId: id })
    await fetchList()
    await getDataTrendVO(id)
    await getDataTrendChart({ detailId: id, tabIdx: "STUDY_CUSTOMER_COUNT" })
  }

  const getInfoData = async (params = {}) => {
    setLoading(true)
    const { detailId } = params
    const studyCampId = detailId || id
    // const data = {
    //   id: studyCampId,
    // };
    await apiCall(`/study/studyCamp/${studyCampId}`, "GET")
      .then((res) => {
        setUserInfo(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  //数据趋势数据
  const getDataTrendVO = async (studyCampId) => {
    setLoading(true)
    const data = {
      studyCampId,
    }
    await apiCall("/study/studyCamp/detail/dataTrend", "GET", data)
      .then((res) => {
        setDataTrendVO(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  //数据趋势图
  const getDataTrendChart = async (params = {}) => {
    setLoading(true)
    const { detailId, time, tabIdx = null } = params
    const tabType = tabIdx || tabsTrendIndex
    const data = {
      id: detailId || id,
      type: tabType,
      startDate:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format("YYYY-MM-DD"),
      endDate:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format("YYYY-MM-DD"),
    }
    const apiUrl =
      tabType == "CHANNEL_COUNT"
        ? "/study/studyCamp/detail/dataTrend/chart/channel"
        : "/study/studyCamp/detail/dataTrend/chart"
    await apiCall(apiUrl, "GET", data)
      .then((res) => {
        tabType == "CHANNEL_COUNT"
          ? setDualAxesData(res)
          : getTabEcharts(tabType, res.list)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getTabEcharts = (type, data) => {
    let title = ""
    switch (type) {
      case "STUDY_CUSTOMER_COUNT":
        if (data) {
          title = "学习人数"
        }
        break
      case "SIGN_UP_CUSTOMER_COUNT":
        if (data) {
          title = "报名人数"
        }
        break
      case "VISITOR_COUNT":
        if (data) {
          title = "访问人数"
        }
        break
      case "VISIT_COUNT":
        if (data) {
          title = "访问次数"
        }
        break
    }
    setLineData({ data, title })
  }

  //下载活码
  const handleDownloadImg = () => {
    saveAs(QrCodeBase({ url: userInfo.transitUrl }), "活码")
  }

  const onChangeTabsDataTrend = (type) => {
    const time = formRef.current.getFieldValue("time")
    setTabsTrendIndex(type)
    getDataTrendChart({
      tabIdx: type,
      time: time && [
        time[0].format("YYYY-MM-DD"),
        time[1].format("YYYY-MM-DD"),
      ],
    })
  }

  const onChangeQuickTime = async (value) => {
    let time = []
    switch (value) {
      case "0":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ]
        getDataTrendChart({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-1":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(value), "YYYY-MM-DD"),
        ]
        getDataTrendChart({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(value)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-7":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getDataTrendChart({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-15":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getDataTrendChart({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
      case "-30":
        time = [
          moment(getDay(value), "YYYY-MM-DD"),
          moment(getDay(-1), "YYYY-MM-DD"),
        ]
        getDataTrendChart({
          time: [
            moment(getDay(value)).format("YYYY-MM-DD"),
            moment(getDay(-1)).format("YYYY-MM-DD"),
          ],
        })
        break
    }
    if (value) {
      await formRef.current.setFieldsValue({
        time,
      })
    }
  }

  const onChangeTime = async (date, dateString) => {
    getDataTrendChart({ time: date ? dateString : null })
    formRef.current.setFieldsValue({
      quickTime: null,
    })
  }

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index)
  }

  const handleGoBack = () => {
    props.history.go(-1)
  }

  const datas = (data) => {
    let objs = {},
      k,
      arr1 = []
    for (let i = 0, len = data.length; i < len; i++) {
      k = data[i].courseName
      if (objs[k]) {
        objs[k]++
      } else {
        objs[k] = 1
      }
    }
    // 保存结果{el-'元素'，count-出现次数}
    for (let o in objs) {
      for (let i = 0; i < objs[o]; i++) {
        if (i === 0) {
          arr1.push(objs[o])
        } else {
          arr1.push(0)
        }
      }
    }
    arr1.forEach((r, index) => {
      data[index]["num"] = r
      data[index]["key"] = index
    })
    return data
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { pagination, query } = params
    const pageInfo = pagination || { current: 1, pageSize: 10 }
    const data = {
      studyCampId: id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    }
    await apiCall("/study/studyCamp/detail/data/course", "GET", data)
      .then((res) => {
        setDataSource(datas([...res]))
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const handleExport = () => {
    setLoading(false)
    const data = {
      studyCampId: id,
    }
    apiCall("/study/studyCamp/detail/data/course/export", "GET", data, null, {
      isExit: true,
      title: `课程详情.${moment().format("YYYY-MM-DD")}.xlsx`,
    })
      .then((res) => {})
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <div className="StudyCampDetails">
      <Spin spinning={loading}>
        <Card
          title="学习营详情"
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card>
              {JSON.stringify(userInfo) != "{}" ? (
                <Row gutter={16} className="header-box">
                  <Col xs={24} xl={18} className="info">
                    <Descriptions
                      column={1}
                      title={
                        <>
                          {userInfo.name}
                          {userInfo.shelfState && (
                            <Tag
                              style={{
                                borderColor:
                                  userInfo.shelfState == "Added" && "#70b603",
                                color:
                                  userInfo.shelfState == "Added" && "#70b603",
                                marginLeft: "8px",
                              }}
                            >
                              <SysDictLabel
                                dataset="RESOURCE_STATUS"
                                dictkey={userInfo.shelfState}
                              />
                            </Tag>
                          )}
                        </>
                      }
                    >
                      <Descriptions.Item>
                        {userInfo.description}
                      </Descriptions.Item>
                      <Descriptions.Item label="客户标签">
                        <div>
                          {
                            userInfo.tagNameList.map((item, index) => (
                              <Tag key={index}>{item}</Tag>
                            ))
                          }
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label="推广渠道">
                        <div>
                          {userInfo.channelNameList.map((item, index) => (
                            <span key={index}>
                              {item}
                              {index != userInfo.channelNameList?.length - 1 &&
                                "、"}
                            </span>
                          ))}
                        </div>
                      </Descriptions.Item>
                      <Descriptions.Item label="开营时间">
                        {userInfo.openTime}
                      </Descriptions.Item>
                      <Descriptions.Item label="上下架时间">
                        {userInfo.startTime} ～ {userInfo.endTime}
                      </Descriptions.Item>
                      <Descriptions.Item label="课程解锁模式">
                        {userInfo.courseVOList?.length || 0}个课程（
                        <SysDictLabel
                          dataset="Course_Unlock_Type"
                          dictkey={userInfo.courseUnlockType}
                        />
                        {userInfo.breakConditionTyp && "-"}
                        {userInfo.breakConditionType && (
                          <SysDictLabel
                            dataset="Break_Condition_Type"
                            dictkey={userInfo.breakConditionType}
                          />
                        )}
                        ）
                      </Descriptions.Item>
                      <Descriptions.Item label="创建">
                        {userInfo.createEmployeeName} {userInfo.createTime}
                      </Descriptions.Item>
                      <Descriptions.Item label="更新">
                        {userInfo.updateEmployeeName} {userInfo.updateTime}
                      </Descriptions.Item>
                    </Descriptions>
                  </Col>
                  <Col span={6}>
                    <div className="codeBox">
                      <FileHOC src={QrCodeBase({ url: userInfo.transitUrl })}>
                        {(url) => (
                          <Image preview={false} width={120} src={url} />
                        )}
                      </FileHOC>
                      <div className="btn">
                        <a
                          onClick={() => {
                            handleDownloadImg()
                          }}
                        >
                          下载二维码
                        </a>
                      </div>
                      <div className="btn">
                        <WibotCopyBtn
                          text={userInfo.transitUrl}
                          title="复制链接"
                        />
                      </div>
                    </div>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card>
              {JSON.stringify(userInfo) != "{}" ? (
                <div>
                  <Descriptions column={1} title="报名">
                    {userInfo.formVO ? (
                      <>
                        <Descriptions.Item label="时间">
                          {userInfo.signUpStartTime}~{userInfo.signUpEndTime}
                        </Descriptions.Item>
                        <Descriptions.Item>
                          <a
                            onClick={() => {
                              window.open(
                                `/wecom/formList/detail?id=${userInfo.formVO.id}`,
                                "_blank"
                              )
                            }}
                          >
                            {userInfo.formVO.shareTitle}
                          </a>
                        </Descriptions.Item>
                      </>
                    ) : (
                      <Descriptions.Item>未开启报名</Descriptions.Item>
                    )}
                  </Descriptions>

                  <Descriptions column={1} title="签到">
                    {userInfo.signInVO ? (
                      <>
                        <Descriptions.Item>
                          <a
                            onClick={() => {
                              window.open(
                                `/wecom/marketingActivity/detail?id=${userInfo.signInVO.id}&type=SIGN`,
                                "_blank"
                              )
                            }}
                          >
                            {userInfo.signInVO.title}
                          </a>
                        </Descriptions.Item>
                      </>
                    ) : (
                      <Descriptions.Item>未开启签到</Descriptions.Item>
                    )}
                  </Descriptions>

                  <Descriptions column={1} title="晒单">
                    {userInfo.shareVO ? (
                      <>
                        <Descriptions.Item>
                          <a
                            onClick={() => {
                              window.open(
                                `/wecom/marketingActivity/detail?id=${userInfo.shareVO.id}&type=SHARE`,
                                "_blank"
                              )
                            }}
                          >
                            {userInfo.shareVO.title}
                          </a>
                        </Descriptions.Item>
                      </>
                    ) : (
                      <Descriptions.Item>未开启晒单</Descriptions.Item>
                    )}
                  </Descriptions>

                  <Descriptions column={1} title="结营证书">
                    {userInfo.finishReward.enable ? (
                      <>
                        <Descriptions.Item>
                          <a
                            onClick={() => {
                              if (userInfo.finishReward.fastPosterId) {
                                setLoading(true)
                                apiCall(
                                  `/info/posterTemplate/fastPoster/${userInfo.finishReward.fastPosterId}`,
                                  "get"
                                )
                                  .then((res) => {
                                    let newUserInfo = userInfo
                                    newUserInfo.finishReward.picture = res.image
                                    setUserInfo({ ...newUserInfo })
                                    setFinishRewardVisable(true)
                                  })
                                  .catch((err) => {
                                    console.log(err)
                                  })
                                  .finally(() => {
                                    setLoading(false)
                                  })
                              }
                            }}
                          >
                            预览证书
                          </a>
                        </Descriptions.Item>
                        <Descriptions.Item style={{ display: "none" }}>
                          <FileHOC src={userInfo.finishReward.picture}>
                            {(url) => (
                              <Image
                                preview={{
                                  visible: finishRewardVisable,
                                  src: url,
                                  onVisibleChange: (value) => {
                                    setFinishRewardVisable(value)
                                  },
                                }}
                              />
                            )}
                          </FileHOC>
                        </Descriptions.Item>
                      </>
                    ) : (
                      <Descriptions.Item>未开启结营证书</Descriptions.Item>
                    )}
                  </Descriptions>
                </div>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Card
          style={{ marginBottom: "20px" }}
          bordered={false}
          title="课程详情"
          extra={
            <Button type="primary" onClick={() => handleExport()}>
              导出课程
            </Button>
          }
        >
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300, y: 400 }}
            pagination={false}
          />
        </Card>
        <Row>
          <Col span={24}>
            <Card bordered={false} title="数据趋势">
              {JSON.stringify(dataTrendVO) != "{}" ? (
                <Row gutter={16} className="data-screening">
                  <Col span={4}>
                    <span className="num">{dataTrendVO.studyCount || 0}</span>
                    <Tooltip title="存在课程学习数据的学员">
                      <p className="tip">
                        累计学习人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日学习人数：{dataTrendVO.studyCountToday || 0}
                    </p>
                  </Col>
                  <Col span={4}>
                    <span className="num">{dataTrendVO.signUpCount || 0}</span>
                    <Tooltip title="填写报名表单的人数，数据来源于关联学习营的【自定义表单】">
                      <p className="tip">
                        累计报名人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日报名人数：{dataTrendVO.signUpCountToday || 0}
                    </p>
                  </Col>
                  <Col span={4}>
                    <span className="num">{dataTrendVO.signInCount || 0}</span>
                    <Tooltip title="进行签到打卡的人数，数据来源于关联学习营的【活动管理-签到活动】">
                      <p className="tip">
                        累计签到人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日签到人数：{dataTrendVO.signInToday || 0}
                    </p>
                  </Col>
                  <Col span={4}>
                    <span className="num">{dataTrendVO.sharerCount || 0}</span>
                    <Tooltip title="同一学员访问学习营主页多次算一次">
                      <p className="tip">
                        累计晒单人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日晒单人数：{dataTrendVO.sharerCountToday || 0}
                    </p>
                  </Col>
                  <Col span={4}>
                    <span className="num">{dataTrendVO.visitorCount}</span>
                    <Tooltip title="同一用户可访问多次">
                      <p className="tip">
                        累计访问人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问人数：{dataTrendVO.visitorCountToday}
                    </p>
                  </Col>
                  <Col span={4}>
                    <span className="num">{dataTrendVO.visitCount}</span>
                    <Tooltip title="同一学员访问学习营主页多次算多次">
                      <p className="tip">
                        累计访问次数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问次数：{dataTrendVO.visitCountToday}
                    </p>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
              <Tabs
                activeKey={tabsTrendIndex}
                destroyInactiveTabPane
                onChange={onChangeTabsDataTrend}
              >
                <TabPane tab="学习人数" key="STUDY_CUSTOMER_COUNT"></TabPane>
                <TabPane tab="报名人数" key="SIGN_UP_CUSTOMER_COUNT">
                  {" "}
                </TabPane>
                <TabPane tab="签到人数" key="SIGN_COUNT">
                  {" "}
                </TabPane>
                <TabPane tab="晒单人数" key="SHARE_COUNT">
                  {" "}
                </TabPane>
                <TabPane tab="访问人数" key="VISITOR_COUNT">
                  {" "}
                </TabPane>
                <TabPane tab="访问次数" key="VISIT_COUNT">
                  {" "}
                </TabPane>
                <TabPane tab="推广渠道" key="CHANNEL_COUNT">
                  {" "}
                </TabPane>
              </Tabs>
              <Form layout={"inline"} ref={formRef}>
                <FormItem
                  label="统计时间"
                  name="time"
                  initialValue={[moment(getDay(-30)), moment(getDay(-1))]}
                >
                  <RangePicker
                    allowClear={false}
                    format="YYYY-MM-DD"
                    onChange={onChangeTime}
                  />
                </FormItem>
                <FormItem
                  label="快捷时间"
                  name="quickTime"
                  initialValue={"-30"}
                >
                  <Select
                    style={{ width: "200px" }}
                    options={[
                      {
                        label: "今天",
                        value: "0",
                      },
                      {
                        label: "昨天",
                        value: "-1",
                      },
                      {
                        label: "最近7天",
                        value: "-7",
                      },
                      {
                        label: "最近15天",
                        value: "-15",
                      },
                      {
                        label: "最近30天",
                        value: "-30",
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {lineData.data?.length > 0 &&
                tabsTrendIndex != "CHANNEL_COUNT" && <Line {...configLine} />}
              {dualAxesData.length > 0 && tabsTrendIndex == "CHANNEL_COUNT" && (
                <DualAxes {...configDualAxes} />
              )}
              {!dualAxesData.length > 0 &&
                tabsTrendIndex == "CHANNEL_COUNT" && (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
            </Card>
          </Col>
        </Row>
        <Card bordered={false} title="数据明细">
          <Tabs
            activeKey={tabsDetailsIndex}
            destroyInactiveTabPane
            onChange={onChangeTabsDataDetails}
          >
            <TabPane tab="访问明细" key="1">
              <TabDetailsOne />
            </TabPane>
            <TabPane tab="学员信息" key="2">
              <TabDetailsTwo />
            </TabPane>
            <TabPane tab="签到记录" key="3">
              <TabDetailsThree />
            </TabPane>
            <TabPane tab="晒单记录" key="4">
              <TabDetailsFour />
            </TabPane>
            <TabPane tab="证书领取记录" key="5">
              <TabDetailsFive />
            </TabPane>
            <TabPane tab="推广渠道" key="6">
              <TabDetailsSix />
            </TabPane>
            {/* <TabPane tab="课程完成情况" key="7">
              <TabDetailsSeven />
            </TabPane> */}
            <TabPane tab="考试统计" key="8">
              <TabDetailsRight />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  )
}

export default withRouter(StudyCampDetails)
