.StudyCampDetails {

  .ant-row {

    .ant-col {
      margin-bottom: 20px;

      .ant-card {
        height: 100%;
      }
    }
  }

  .info {
    position: relative;

    .text {
      color: #aaa;
    }

    .ant-tag {
      color: #aaa;
    }

    .groupName {
      margin-bottom: 6px;
    }

    .avatar-name {
      .ant-avatar {
        margin-right: 6px;
      }
    }

    .ant-space {
      width: 100%;
      margin-bottom: unset !important;
      color: #aaa;

      .ant-tag {
        margin: 0;
      }
    }


  }

  .codeBox {
    white-space: nowrap;
    text-align: center;

    .btn {
      display: flex;
      justify-content: space-around;
      margin-top: 10px;
    }
  }

  .data-screening {
    background: #f2f2f2;

    .ant-col {
      text-align: center;

      .num {
        font-size: 40px;
        font-weight: bold;
      }

      .tip {
        font-size: 18px;
        color: #aaa;
        cursor: pointer;

        .anticon {
          margin-left: 6px;
        }
      }

      .visit {
        color: #aaa;
        font-size: 14px;
      }
    }
  }

  .plan-box {
    width: 100%;
    height: 200px;
    overflow-y: scroll;

    .plan-flex {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;

      .ant-collapse {
        flex: 1;
      }
    }
  }

  .ant-descriptions {
    margin-bottom: 20px;

    .ant-descriptions-header {
      margin: 0;
    }

    .ant-descriptions-item {
      padding: 0 0 6px;
      margin-bottom: 6px;

      .ant-descriptions-item-label,
      .ant-descriptions-item-content {
        // color: #aaa;
      }
    }
  }
}