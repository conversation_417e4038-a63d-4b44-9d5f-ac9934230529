/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 15:17
 * @LastEditTime: 2025/05/14 14:14
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/form.jsx
 * @Description: '学习营-创建/编辑'
 */

import React, { useEffect, useState, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Spin,
  Button,
  Card,
  Form,
  Row,
  Col,
  Steps,
  Image,
  message,
  Space,
} from "antd"
import { apiCall } from "common/utils"
import moment from "moment"
import { qs2obj } from "common/object"
import PreviewForm from "./comps/PreviewForm/home"
import StudyCampStepOne from "./comps/StepOne/home"
import StudyCampStepTwo from "./comps/StepTwo/home"
import StudyCampStepThree from "./comps/StepThree/home"
import StudyCampStepFour from "./comps/StepFour/home"
import { clearCache } from "react-router-cache-route"
import { FileHOC } from "components/FileHOC/FileHOC"
import { formatURL } from "config/index.js"

const { Step } = Steps
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
}
const StudyCampForm = (props) => {
  const [formForm] = Form.useForm()
  const onRefStepOneFrame = useRef()
  const onRefStepTwoFrame = useRef()
  const onRefStepThreeFrame = useRef()
  const onRefStepFourFrame = useRef()
  const [loading, setLoading] = useState(false)
  const [id, setId] = useState(null)
  const [status, setStatus] = useState(null)
  const [stepCurrent, setStepCurrent] = useState(0)
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [previewData, setPreviewData] = useState({})
  const [detailsHtml, setDetailsHtml] = useState("")
  const [openDate, setOpenDate] = useState(null)
  const [processingFlag, setProcessingFlag] = useState(false)

  useEffect(() => {
    const { id } = qs2obj(props.location.search)
    if (id) {
      setId(id)
      init(id)
    } else {
      getLocalStorageField()
    }
  }, [])

  const init = async (id) => {
    const params = {
      id,
    }
    await fetchList(params)
  }

  const fetchList = async (params = {}) => {
    setLoading(true)
    const { id } = params
    apiCall(`/study/studyCamp/${id}`, "GET")
      .then((res) => {
        const {
          startTime,
          endTime,
          openTime,
          signUpStartTime,
          signUpEndTime,
          finishReward,
          poster,
          imageList,
          courseUnlockType,
          shelfState,
          processingFlag,
          name,
          description,
          courseVOList,
          detail,
          formVO,
          signInVO,
          shareVO,
          signInFlag,
          shareFlag,
          signUpFlag,
          shareImage,
          posterId,
        } = res

        formForm.setFieldsValue({
          ...res,
          imageList: imageList.map((item) => formatURL(item)),
          time: [
            startTime ? moment(startTime) : null,
            endTime ? moment(endTime) : null,
          ],
          openTime: openTime ? moment(openTime) : null,
          signUpTime: [
            signUpStartTime ? moment(signUpStartTime) : null,
            signUpEndTime ? moment(signUpEndTime) : null,
          ],
          fileId: finishReward.picture && [finishReward.picture],
          poster: posterId ? [posterId] : poster && [poster],
          shareImage: shareImage && [shareImage],
          courseIdList: courseVOList.map((item) => item.id),
        })
        setStatus(shelfState)
        setProcessingFlag(processingFlag) // 是否进行中
        if (posterId) {
          getPosterTemplate({ imageList, shareImage, posterId })
        } else {
          onRefStepOneFrame.current.getInitData({
            imageList,
            imageUrl: poster,
            shareImage,
            detail,
          })
        }
        onRefStepTwoFrame.current.getInitData({
          courseUnlockType,
          courseVOList,
          openDate: openTime,
        })
        onRefStepThreeFrame.current.getInitData({
          formVO,
          signUpFlag,
          signInFlag,
          shareFlag,
          signInVO,
          shareVO,
        })
        onRefStepFourFrame.current.getInitData({ enable: finishReward.enable })
        setPreviewData({
          fileList: imageList.map((item) => ({ url: item })),
          name,
          description,
          signUpTime: [signUpStartTime, signUpEndTime],
          openTime,
          courseList: courseVOList,
        })
        setDetailsHtml(detail)
        setOpenDate(openTime)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 获取海报工具图
  const getPosterTemplate = (params) => {
    const { posterId, imageList, shareImage } = params
    setLoading(true)
    apiCall(`/info/posterTemplate/${posterId}`, "GET")
      .then((res) => {
        onRefStepOneFrame.current.getInitData({
          imageList,
          imageUrl: res.image,
          shareImage,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 新增情况下获取维护证书返回后本地存储的数据
  const getLocalStorageField = () => {
    const fieldData = localStorage.getItem("studyCampField")
    const imageField = localStorage.getItem("imageField")
      ? JSON.parse(localStorage.getItem("imageField"))
      : {}
    if (fieldData) {
      const {
        name,
        description,
        detail,
        time,
        signUpTime,
        finishReward,
        signUpFlag,
        signInFlag,
        shareFlag,
        courseUnlockType,
      } = JSON.parse(fieldData)
      let { openTime } = JSON.parse(fieldData)
      if (time) {
        time[0] = moment(time[0])
        time[1] = moment(time[1])
      }
      if (signUpTime) {
        signUpTime[0] = moment(signUpTime[0])
        signUpTime[1] = moment(signUpTime[1])
      }
      if (openTime) {
        openTime = moment(openTime).format("YYYY-MM-DD HH:mm")
      }
      formForm.setFieldsValue({
        ...JSON.parse(fieldData),
        time,
        openTime: openTime && moment(openTime),
        signUpTime,
      })

      if (imageField && JSON.stringify(imageField) != "{}") {
        onRefStepOneFrame.current.getInitData({
          imageList: imageField.fileList,
          imageUrl: imageField.imageUrl,
        })
      }

      onRefStepTwoFrame.current.getInitData({
        courseUnlockType,
        courseVOList: imageField?.courseList ?? [],
        openDate: openTime,
      })
      onRefStepThreeFrame.current.getInitData({
        formVO: imageField?.formVOSignUp,
        signUpFlag,
        signInFlag,
        shareFlag,
        signInVO: imageField?.signInVO,
        shareVO: imageField?.shareVO,
      })
      onRefStepFourFrame.current.getInitData({ enable: finishReward.enable })

      setPreviewData({
        fileList: imageField?.fileList?.map((item) => ({ url: item })) || [],
        name,
        description,
        signUpTime: signUpTime && [
          moment(signUpTime[0]).format("YYYY-MM-DD HH:mm"),
          moment(signUpTime[1]).format("YYYY-MM-DD HH:mm"),
        ],
        openTime,
        courseList: imageField.courseList ?? [],
      })
      setDetailsHtml(detail)
      setOpenDate(openTime)
    }
  }

  const onChangeSteps = (current) => {
    setStepCurrent(current)
  }

  // 拿到各个表单变化需要的数据
  const handleModifyData = (data) => {
    const obj = JSON.parse(JSON.stringify(previewData))
    setPreviewData({ ...obj, ...data })
    if (data.openTime) {
      setOpenDate(data.openTime)
    }
  }

  // 获取富文本详情
  const handleDetailsHtml = (html) => {
    setDetailsHtml(html)
  }

  // 维护证书
  const handlePoster = (posterId) => {
    let fastPosterId = null
    const fieldData = localStorage.getItem("studyCampField")
      ? JSON.parse(localStorage.getItem("studyCampField"))
      : {}
    if (fieldData.posterId) {
      fastPosterId = fieldData.posterId
    } else {
      fastPosterId = posterId
    }
    apiCall(
      `/info/posterTemplate/update/putTask?fastPosterId=${fastPosterId}`,
      "POST"
    )
      .then((res) => {
        const obj = {
          ...formForm.getFieldsValue(),
          posterId: fastPosterId,
        }
        localStorage.setItem("studyCampField", JSON.stringify(obj))
        props.history.push({
          pathname: "/wecom/posterTool/postIframe",
          search: `?id=${fastPosterId}`,
        })
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const onSubmit = (type = null) => {
    if (type == "notIssue") {
      let formData = formForm.getFieldsValue()
      setLoading(true)
      if (formData.openTime) {
        // 开营时间
        formData.openTime = moment(formData.openTime).format("YYYY-MM-DD HH:mm")
      }
      if (formData.time && formData.time[0]) {
        // 上下架时间
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.time
      }
      if (formData.signUpTime && formData.signUpTime[0]) {
        // 报名时间
        formData.signUpStartTime = moment(formData.signUpTime[0]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        formData.signUpEndTime = moment(formData.signUpTime[1]._d).format(
          "YYYY-MM-DD HH:mm"
        )
        delete formData.signUpTime
      }
      const poster = formData.poster && formData.poster[0]
      if (typeof poster == "number") {
        formData.posterId = poster
        delete formData.poster
      } else {
        formData.poster = poster
      }
      formData.shareImage = formData.shareImage?.join(",") || null
      formData.finishReward.picture = formData.fileId && formData.fileId[0]
      formData.finishReward.fastPosterId =
        onRefStepFourFrame.current.getPosterId()
      // 按日期解锁-新增courseIdAndTimeList字段回显
      if (formData.courseUnlockType == "DATE") {
        formData.courseIdAndTimeList = previewData.courseList?.map((item) => ({
          courseId: item.id,
          time: item.unlockTime,
        }))
      }
      const data = {
        id,
        step: 0,
        shelfState: type ? type : status,
        ...formData,
      }
      apiCall("/study/studyCamp/draft", "POST", data)
        .then((res) => {
          setStatus(res.shelfState)
          setId(res.id)
          message.success(id ? "修改成功！" : "新增成功！")
          if (stepCurrent == 3) {
            // clearCache(); // 清空路由缓存
            props.history.push("/wecom/studyCamp")
          }
        })
        .catch((err) => {})
        .finally(() => {
          setLoading(false)
        })
      return false
    }
    formForm
      .validateFields()
      .then((formData) => {
        const newCourseIdList = formData.courseIdList
          ? [...formData.courseIdList]
          : []
        const courseIdListSort = newCourseIdList.sort()
        for (let i = 0; i < courseIdListSort.length; i++) {
          if (courseIdListSort[i] == courseIdListSort[i + 1]) {
            message.error("不能选择相同的课程！")
            return
          }
        }

        if (
          formData.courseUnlockType == "DATE" &&
          previewData.courseList.some((item) => !item.unlockTime)
        ) {
          message.error("课程解锁时间不能为空！")
          return
        }

        setLoading(true)
        if (formData.openTime) {
          // 开营时间
          formData.openTime = moment(formData.openTime).format(
            "YYYY-MM-DD HH:mm"
          )
        }
        if (formData.time) {
          // 上下架时间
          formData.startTime = moment(formData.time[0]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          formData.endTime = moment(formData.time[1]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          delete formData.time
        }
        if (formData.signUpTime) {
          // 报名时间
          formData.signUpStartTime = moment(formData.signUpTime[0]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          formData.signUpEndTime = moment(formData.signUpTime[1]._d).format(
            "YYYY-MM-DD HH:mm"
          )
          delete formData.signUpTime
        }
        const poster = formData.poster && formData.poster[0]
        if (typeof poster == "number") {
          formData.posterId = poster
          delete formData.poster
        } else {
          formData.poster = poster
        }
        formData.shareImage = formData.shareImage.join(",") || null
        // formData.finishReward.picture = formData.fileId;
        formData.finishReward.picture = formData.fileId && formData.fileId[0]
        formData.finishReward.fastPosterId =
          onRefStepFourFrame.current.getPosterId()
        // 按日期解锁-新增courseIdAndTimeList字段回显
        if (formData.courseUnlockType == "DATE") {
          formData.courseIdAndTimeList = previewData.courseList?.map(
            (item) => ({
              courseId: item.id,
              time: item.unlockTime,
            })
          )
        }
        const data = {
          id,
          step: 0,
          shelfState: type ? type : status,
          ...formData,
        }
        const apiUrl = id ? `/study/studyCamp/update/${id}` : "/study/studyCamp"
        apiCall(apiUrl, "POST", data)
          .then((res) => {
            message.success(id ? "修改成功！" : "新增成功！")
            // clearCache(); // 清空路由缓存
            props.history.push("/wecom/studyCamp")
          })
          .catch((err) => {})
          .finally(() => {
            setLoading(false)
          })
      })
      .catch((err) => {
        message.error("请完善表单信息！")
      })
  }

  const handleGoBack = () => {
    props.history.go(-1)
    localStorage.removeItem("studyCampField")
    localStorage.removeItem("imageField")
  }

  return (
    <div className="StudyCampForm">
      <Spin spinning={loading}>
        <Card
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          title={id ? "编辑学习营" : "创建学习营"}
          bordered={false}
          bodyStyle={{ display: "none" }}
        ></Card>
        <br />
        <Card
          bordered={false}
          // style={{ height: "100%" }}
          className="check_in_wrap"
        >
          <Row>
            <Col xs={24} lg={16}>
              <Steps
                current={stepCurrent}
                onChange={onChangeSteps}
                style={{ width: "80%", margin: "0 auto 20px" }}
              >
                <Step
                  status={stepCurrent == 0 ? "process" : "wait"}
                  title="步骤一"
                  description="营期信息"
                />
                <Step
                  status={stepCurrent == 1 ? "process" : "wait"}
                  title="步骤二"
                  description="课程安排"
                />
                <Step
                  status={stepCurrent == 2 ? "process" : "wait"}
                  title="步骤三"
                  description="营期活动"
                />
                <Step
                  status={stepCurrent == 3 ? "process" : "wait"}
                  title="步骤四"
                  description="结营奖励"
                />
              </Steps>
              <Form {...layout} form={formForm}>
                <div
                  style={
                    stepCurrent == 0
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StudyCampStepOne
                    formForm={formForm}
                    ref={onRefStepOneFrame}
                    processingFlag={processingFlag}
                    shelfState={status}
                    handleModifyData={handleModifyData}
                    handleDetailsHtml={handleDetailsHtml}
                  />
                </div>

                <div
                  style={
                    stepCurrent == 1
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StudyCampStepTwo
                    formForm={formForm}
                    openDate={openDate}
                    processingFlag={processingFlag}
                    shelfState={status}
                    ref={onRefStepTwoFrame}
                    handleModifyData={handleModifyData}
                  />
                </div>

                <div
                  style={
                    stepCurrent == 2
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StudyCampStepThree
                    formForm={formForm}
                    processingFlag={processingFlag}
                    shelfState={status}
                    ref={onRefStepThreeFrame}
                    handleModifyData={handleModifyData}
                  />
                </div>

                <div
                  style={
                    stepCurrent == 3
                      ? { display: "block" }
                      : { display: "none" }
                  }
                >
                  <StudyCampStepFour
                    formForm={formForm}
                    processingFlag={processingFlag}
                    shelfState={status}
                    ref={onRefStepFourFrame}
                    handleModifyData={handleModifyData}
                    handlePoster={handlePoster} // 维护证书
                  />
                </div>

                <div style={{ display: "flex", justifyContent: "center" }}>
                  <Space size={40}>
                    {stepCurrent > 0 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent - 1)
                        }}
                      >
                        上一步
                      </Button>
                    )}
                    {stepCurrent < 3 && (
                      <Button
                        type="primary"
                        onClick={() => {
                          setStepCurrent(stepCurrent + 1)
                        }}
                      >
                        下一步
                      </Button>
                    )}
                    {(id && processingFlag) ||
                      (id && status == "soldOut" && stepCurrent == 3 && (
                        <Button type="primary" onClick={() => onSubmit()}>
                          保存
                        </Button>
                      ))}
                    {((id && status == "notIssue") || !id) && (
                      <Button
                        type="primary"
                        onClick={() => onSubmit("notIssue")}
                      >
                        仅保存
                      </Button>
                    )}
                    {((id && status == "soldOut" && stepCurrent == 3) ||
                      (id && status == "notIssue" && stepCurrent == 3) ||
                      (!id && stepCurrent == 3)) && (
                      <Button type="primary" onClick={() => onSubmit("issued")}>
                        保存并发布
                      </Button>
                    )}
                  </Space>
                </div>
              </Form>
            </Col>
            <Col xs={24} lg={8}>
              <PreviewForm
                previewData={{ ...previewData, detailsHtml: detailsHtml }}
              />
            </Col>
          </Row>
        </Card>
      </Spin>
      <FileHOC src={previewParams.url}>
        {(url) => (
          <Image
            style={{ display: "none" }}
            preview={{
              visible: previewParams.visible,
              src: url,
              onVisibleChange: (value) => {
                setPreviewParams({ visible: false })
              },
            }}
          />
        )}
      </FileHOC>
    </div>
  )
}

export default withRouter(StudyCampForm)
