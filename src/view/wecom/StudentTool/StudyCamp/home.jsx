/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 14:19
 * @LastEditTime: 2025/05/14 14:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/StudentTool/StudyCamp/home.jsx
 * @Description: '学习营'
 */

import React, { useState, useEffect, useRef } from "react"
import { withRouter } from "react-router-dom"
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
  Typography,
  Select,
  TreeSelect,
  Switch,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import SysDictLabel from 'components/select/SysDictLabel';
import LinkCard from 'components/LinkCard/home';
import PreviewModal from './comps/PreviewModal/home';
import ExtendModal from './comps/ExtendModal/home';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import ExportModal from './comps/ExportModal/home';
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import {usePageCacheLifeCycle} from "common/hooks";
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
// import './home.less';

const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Paragraph } = Typography
const { SHOW_PARENT } = TreeSelect
const { Option } = Select

const StudyCamp = (props) => {
  const [loading, setLoading] = useState(false)
  const formRef = useRef(null)
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 })
  const [channelOptions, setChannelOptions] = useState([])
  const [operateParams, setOperateParams] = useState({ visible: false })
  const [dataSource, setDataSource] = useState([])
  const [labelTreeData, setLabelTreeData] = useState([])
  const [previewParams, setPreviewParams] = useState({ visible: false })
  const [extendFormParams, setExtendFormParams] = useState({ visible: false })
  const [exportParams, setExportParams] = useState({ visible: false })
  const [sopOption, setSopOption] = useState([])
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "学习营",
      width: "250px",
      dataIndex: "title",
      key: "title",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          <LinkCard
            isLink={false}
            data={{
              title: record.shareTitle,
              description: record.shareDescription,
              image: record.shareImage,
            }}
          />
        </div>
      ),
    },
    {
      title: "营期预览",
      width: "120px",
      align: "center",
      render: (value, record) => (
        <a onClick={() => handlePreview(record)}>预览</a>
      ),
    },
    {
      title: "上下架时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: "上架状态",
      width: "160px",
      dataIndex: "shelfState",
      key: "shelfState",
      align: "center",
      render: (value, record, index) => (
        <>
          {(value == "notIssue" || value == "issued") && (
            <SysDictLabel dataset="RESOURCE_STATUS" dictkey={value} />
          )}
          {(value == "Added" || value == "soldOut") && (
            <Switch
              checkedChildren="已上架"
              unCheckedChildren="已下架"
              checked={value == "Added"}
              onChange={(checked) => {
                onChangeSwitchStatus(checked, record)
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "开营时间",
      width: "160px",
      dataIndex: "openTime",
      key: "openTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.openTime) - timeStamp(b.openTime),
    },
    {
      title: "报名时间",
      width: "160px",
      dataIndex: "time",
      key: "time",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.signUpStartTime}
          <br />至<br />
          {record.signUpEndTime}
        </>
      ),
    },
    {
      title: "关联SOP组",
      width: "160px",
      dataIndex: "sopGroupName",
      key: "sopGroupName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      ),
    },
    {
      title: "学习营数据",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          访问人数：{record.visitorCount}
          <br />
          访问次数：{record.visitCount}
          <br />
          报名人数：{record.signUpCount}
          {/* <br />
          签到人数：{record.visitorCount}
          <br />
          晒单人数：{record.visitorCount} */}
          <br />
          学习人数：{record.studyCount}
        </div>
      ),
    },
    {
      title: "营期内容",
      width: "160px",
      dataIndex: "visitCount",
      key: "visitCount",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          课程数量：{record.courseCount}
          <br />
          结营考试：{record.examFlag ? "有" : "无"}
          <br />
          签到活动：{record.signInFlag ? "有" : "无"}
          <br />
          晒单活动：{record.shareFlag ? "有" : "无"}
          <br />
          报名活动：{record.signUpFlag ? "有" : "无"}
        </div>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '更新人/更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.updateEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleCopy(record, index), name: "复制" },
          { onClick: () => handleDetails(record, index), name: "详情" },
        ]
        if (record.shelfState == "Added") {
          opts.push({
            onClick: () => handleExtend(record, index),
            name: "推广",
          })
        }
        if (record.shelfState == "notIssue") {
          opts.push({
            onClick: () => handleShelves(record, "issued"),
            name: "发布",
          })
        }
        if (record.shelfState == "issued") {
          opts.push({
            onClick: () =>
              handleShelves(record, record.hasAdded ? "soldOut" : "notIssue"),
            name: "取消发布",
          })
        }
        if (record.shelfState == "notIssue" || record.shelfState == "soldOut") {
          opts.push({ onClick: () => handleEdit(record), name: "编辑" })
        }
        if (record.studyCount != 0) {
          opts.push({ onClick: () => handleExport(record), name: "导出" })
        }
        if (record.shelfState == "notIssue") {
          opts.push({ onClick: () => handleDelete(record), name: "删除" })
        } else {
          opts.push({
            onClick: () => {
              message.error("学习营已存在数据，不可删除！")
            },
            name: "删除",
          })
        }
        return <ListOperation opts={opts} />
      },
    },
  ]

  useEffect(() => {
    getInfoChannelOptions()
    getTagCategoryTreeTwo()
    getSopOption()
    fetchList()
  }, [])

  usePageCacheLifeCycle({
    onShow() {
      fetchList()
    },
  })

  const fetchList = (params = {}) => {
    setLoading(true)
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.minCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.createTime
      }
      if (formData.time) {
        formData.minOnShelfTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxOnShelfTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.time
      }
      if (formData.openTime) {
        formData.minOpenTime = moment(formData.openTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxOpenTime = moment(formData.openTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.openTime
      }
      if (formData.signUpTime) {
        formData.minSignUpTime = moment(formData.signUpTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxSignUpTime = moment(formData.signUpTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.signUpTime
      }
      if (formData.updateTime) {
        formData.minUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        )
        formData.maxUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        )
        delete formData.updateTime
      }
      // formData.customerTagIdList =
      //   formData.customerTagIdList?.join(',') || null;
      // formData.createDeptIdList = formData.createDeptIdList?.join(',') || null;
      // formData.updateDeptIdList = formData.updateDeptIdList?.join(',') || null;

      const { pagination, query } = params
      const pageInfo = pagination || { current: 1, pageSize: 10 }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/study/studyCamp/page', 'POST', data)
        .then((res) => {
          const { records, current, size, total, pages } = res
          setDataSource(records)
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          })
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setLoading(false)
        })
    })
  }

  // 获取推广渠道
  const getInfoChannelOptions = () => {
    apiCall("/activity/channel/page", "GET", { paged: false })
      .then((res) => {
        setChannelOptions(res.records)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 获取SOP组
  const getSopOption = async () => {
    await apiCall("/sopGroup", "GET")
      .then((res) => {
        setSopOption(res)
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const handleQuery = () => {
    fetchList()
  }

  const handleReset = () => {
    formRef.current.resetFields()
    fetchList()
  }

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    }
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err)
      })
  }

  // 预览
  const handlePreview = (record) => {
    setPreviewParams({
      visible: true,
      previewData: record,
      onCancel: () => {
        setPreviewParams({ visible: false })
      },
    })
  }

  // 新增
  const handleAdd = () => {
    props.history.push("/wecom/studyCamp/form")
  }

  // 推广
  const handleExtend = (record) => {
    setExtendFormParams({
      visible: true,
      studyCampId: record.id,
      title: "推广渠道",
      onCancel: () => {
        setExtendFormParams({ visible: false })
      },
    })
  }

  // 发布
  const handleShelves = (record, state) => {
    const { id } = record
    apiCall(
      state == "issued"
        ? `/study/studyCamp/issue/${id}`
        : `/study/studyCamp/update/shelfState/${id}?shelfState=${state}`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！")
        fetchList()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  // 编辑
  const handleEdit = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/studyCamp/form",
      search: `?id=${id}`,
    })
  }

  // 导出
  const handleExport = (record) => {
    const { id } = record
    setExportParams({
      visible: true,
      studyCampId: id,
      onCancel: () => {
        setExportParams({ visible: false })
      },
    })
  }

  // 复制
  const handleCopy = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "复制确认",
      content: `您将为学习营名称为【${name}】进行操作，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/study/studyCamp/copy/${id}`, "POST")
          .then((res) => {
            message.success("操作成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  const handleDetails = (record) => {
    const { id } = record
    props.history.push({
      pathname: "/wecom/studyCamp/details",
      search: `?id=${id}`,
    })
  }

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除的学习营名称为【${name}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/study/studyCamp/delete/${id}`, "POST")
          .then((res) => {
            message.success("删除成功！")
            fetchList()
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          })
      },
      onCancel: () => {
        setOperateParams({
          visible: false,
        })
      },
    })
  }

  // 上下架
  const onChangeSwitchStatus = (checked, record) => {
    const { id } = record
    apiCall(
      `/study/studyCamp/update/shelfState/${id}?shelfState=${
        checked ? "Added" : "soldOut"
      }`,
      "POST"
    )
      .then((res) => {
        message.success("修改成功！")
        fetchList()
      })
      .catch((err) => {
        console.log(err)
      })
      .finally(() => {})
  }

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination })
  }

  return (
    <div className="StudyCamp">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="nameOrdesc"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="学习营名称、学习营介绍" allowClear />
          </FormItem>
          <FormItem name="signUpFlag">
            <Select placeholder="是否有报名" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="examFlag">
            <Select placeholder="是否有考试" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="signInFlag">
            <Select placeholder="是否有签到" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <FormItem name="shareFlag">
            <Select placeholder="是否有晒单" allowClear>
              <Option value>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </FormItem>
          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            labelTreeData={labelTreeData}
          />
          {/* <FormItem name="customerTagIdList"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={['customer']} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     treeNodeFilterProp="title" */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <FormItem
            name="updateDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="更新人" />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" />
          </FormItem>
          <FormItem name="channelId">
            <Select
              placeholder="推广渠道"
              fieldNames={{ label: "name", value: "id" }}
              options={channelOptions}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="sopGroupId">
            <Select
              placeholder="选择SOP组"
              fieldNames={{ label: "name", value: "id" }}
              options={sopOption}
              allowClear
              showSearch
              showArrow
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <FormItem name="openTime" label="开营时间">
            <RangePicker />
          </FormItem>
          <FormItem name="signUpTime" label="报名时间">
            <RangePicker />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
          <FormItem name="time" label="上下架时间">
            <RangePicker />
          </FormItem>
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              创建学习营
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <PreviewModal params={previewParams} />
      <ExtendModal params={extendFormParams} />
      {/* 导出对话框 */}
      <ExportModal params={exportParams} />
    </div>
  )
}

export default withRouter(StudyCamp)
