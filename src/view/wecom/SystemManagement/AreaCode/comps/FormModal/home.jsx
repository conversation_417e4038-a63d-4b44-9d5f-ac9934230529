/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/08/03 16:18
 * @LastEditTime: 2023/08/04 09:50
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\SystemManagement\AreaCode\comps\FormModal\home.jsx
 * @Description: '新增编码'
 */

import React, { useState, useEffect, memo } from 'react';
import { Form, Spin, Modal, Input } from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const { visible, id, data, onSubmit, onCancel } = props.params;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      if (data) {
        form.setFieldsValue({
          ...data,
          departmentId: data.departmentId?.split(',') || [],
        });
      }
    }
  }, [visible]);

  const handleOk = () => {
    form.validateFields().then((formData) => {
      const data = {
        id: id ?? null,
        ...formData,
        departmentId: formData.departmentId.join(','),
      };
      onSubmit?.(data);
      form.resetFields();
    });
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel?.();
  };

  return (
    <Modal
      title={id ? '编辑地区编码' : '新增地区编码'}
      visible={visible}
      centered
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Spin spinning={loading}>
        <Form {...layout} form={form}>
          <FormItem
            name="areaCode"
            label="编码"
            rules={[{ required: true, message: '请输入编码' }]}
          >
            <Input placeholder="请输入编码" maxLength={30} allowClear />
          </FormItem>
          <FormItem
            name="areaName"
            label="地区名称"
            rules={[{ required: true, message: '请输入地区名称' }]}
          >
            <Input placeholder="请输入地区名称"  maxLength={30} allowClear />
          </FormItem>
          <FormItem
            label="对应部门"
            name="departmentId"
            rules={[{ required: true, message: '请选择对应部门' }]}
          >
            <ETypeTransferModal
              title="选择部门"
              onlyDepartment
              onlyEmployee={false}
              multiple={false}
            />
          </FormItem>
        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(FormModal);
