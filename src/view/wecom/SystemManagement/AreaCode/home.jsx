/*
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: 2023/08/03 15:57
 * @LastEditTime: 2023/11/21 15:53
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/AreaCode/home.jsx
 * @Description: '地区编码'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import { Button, Card, Form, Input, Table, Tooltip, message } from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal/home';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;

const AreaCode = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [codeFormParams, setCodeFormParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '编码',
      width: '160px',
      dataIndex: 'areaCode',
      key: 'areaCode',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '地区名称',
      width: '160px',
      dataIndex: 'areaName',
      key: 'areaName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '对应部门',
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/base/departmentAreaCode', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleAdd = () => {
    setCodeFormParams({
      visible: true,
      onSubmit: (params) => {
        apiCall('/base/departmentAreaCode', 'POST', params)
          .then((res) => {
            message.success('新增成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setCodeFormParams({ visible: false });
      },
      onCancel: () => {
        setCodeFormParams({ visible: false });
      },
    });
  };

  // 编辑
  const handleEdit = (record) => {
    const { id } = record;
    setCodeFormParams({
      visible: true,
      id,
      data: record,
      onSubmit: (params) => {
        apiCall(`/base/departmentAreaCode/update/${id}`, 'POST', params)
          .then((res) => {
            message.success('编辑成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setCodeFormParams({ visible: false });
      },
      onCancel: () => {
        setCodeFormParams({ visible: false });
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { areaCode, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除编码【${areaCode}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/base/departmentAreaCode/delete/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="AreaCode">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="areaCode"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="编码" allowClear />
          </FormItem>
          <FormItem
            name="areaName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="地区名称" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <FormModal params={codeFormParams} />
    </div>
  );
};

export default withRouter(AreaCode);
