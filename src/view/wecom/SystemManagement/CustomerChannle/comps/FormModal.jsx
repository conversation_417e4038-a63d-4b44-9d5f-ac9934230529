/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2022/04/25 14:50
 * @LastEditTime: 2023/06/05 11:38
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\SystemManagement\CustomerChannle\comps\FormModal.jsx
 * @Description: ''
 */

import React, { useState, useEffect, memo } from "react";
import { Form, Spin, Modal, Input, Tag, Select, TreeSelect } from "antd";
import { apiCall } from "common/utils";
import { recursionTagKeyTreeData, flatten, recursionTagKeyTreeDataTag } from "common/tree"
import "./FormModal.less";
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"
const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const { visible, tagData, isAdd, onOk, onCancel } = props.params;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tagId, setTagId] = useState(null);
  const [customerTag, setCustomerTag] = useState([]);
  const [createMode, setCreateMode] = useState([]);
  const [labelTreeData, setLabelTreeData] = useState([]);
  const [existTagNameList, setExistTagNameList] = useState([])
  useEffect(async () => {
    if (visible) {
      await init(tagData && tagData.id);
      if (tagData) {
        const { id } = tagData;
        form.setFieldsValue({ ...tagData });
        setExistTagNameList(tagData.tagNameList)
        setTagId(id);
      }

    }
  }, [visible]);

  const init = async (id) => {
    await getcreateModeType();
    await getTagCategoryTreeTwo(id);
  };

  const getcreateModeType = async () => {
    setLoading(true);
    await apiCall("/activity/channel/getChannelTypes", "GET")
      .then((res) => {
        setCreateMode(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async (id) => {
    setLoading(true);
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        setLabelTreeData(tagTreeData);
        // 回显选择的标签
        if (id) {
          const allTagList = flatten(tagTreeData);
          const tagIdList = tagData.tagIdList;
          const filterTags = allTagList.filter(
            (item) => tagIdList.indexOf(item.key) != -1
          );
          setCustomerTag(filterTags);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleChange = (value, node) => {
    const data = value.map((item, index) => ({
      id: item,
      title: node[index],
    }));
    setCustomerTag(data);
  };

  const handleClose = (e, item, value = null) => {
    e.preventDefault();
    const data = JSON.parse(JSON.stringify(customerTag));
    const newData = data.filter((i) => i != item);
    setCustomerTag(newData);
  };

  const handleOk = () => {
    form.validateFields().then((formData) => {
      const data = {
        id: tagId ?? null,
        ...formData,
      };
      onOk?.(data);
      setCustomerTag([]);
      form.resetFields();
    });
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel?.();
    setCustomerTag([]);
  };

  return (
    <Modal
      title={isAdd ? "新增渠道" : "编辑渠道"}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确认"
      cancelText="取消"
      className="channel-modal"
    >
      <Spin spinning={loading}>
        <Form {...layout} form={form}>
          <FormItem
            name="name"
            label="渠道名称"
            rules={[{ required: true, message: "请输入渠道名称" }]}
          >
            <Input placeholder="请输入渠道名称" allowClear />
          </FormItem>
          <FormItem
            name="typeId"
            label="渠道类型"
            rules={[{ required: true, message: "请输入渠道名称" }]}
          >
            <Select
              placeholder="请输入渠道类型"
              fieldNames={{ label: "name", value: "id" }}
              options={createMode}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          <CustomTagSelect
            extra={
              <div>
                {customerTag.length > 0
                  ? customerTag.map((item, index) => (
                    <Tag
                      closable
                      style={{ marginTop: "5px" }}
                      key={index}
                      onClose={(e) => handleClose(e, item)}
                    >
                      {item}
                    </Tag>
                  ))
                  : ""}
              </div>
            }
            label="客户标签"
            name="tagNameList"
            placeholder="客户标签"
            useForm={form}
            existTagNameList={existTagNameList}
            labelTreeData={labelTreeData}
          />
          {/* <FormItem */}
          {/*   name="tagIdList" */}
          {/*   label="客户标签" */}
          {/*   extra={ */}
          {/*     <div> */}
          {/*       {customerTag.length > 0 */}
          {/*         ? customerTag.map((item, index) => ( */}
          {/*           <Tag */}
          {/*             closable */}
          {/*             style={{ marginTop: "5px" }} */}
          {/*             key={index} */}
          {/*             onClose={(e) => handleClose(e, item)} */}
          {/*           > */}
          {/*             {item.title} */}
          {/*           </Tag> */}
          {/*         )) */}
          {/*         : ""} */}
          {/*     </div> */}
          {/*   } */}
          {/* > */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={["group58"]} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     treeNodeFilterProp="title" */}
          {/*     onChange={handleChange} */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(FormModal);
