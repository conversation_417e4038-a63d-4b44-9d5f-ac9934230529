/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/10/11 14:02
 * @LastEditTime: 2023/11/24 11:52
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/CustomerChannle/details.jsx
 * @Description: '渠道数据'
 */

import React, { useEffect, useState, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Spin,
  Button,
  Card,
  Table,
  Tooltip,
  Row,
  Col,
  Avatar,
  Empty,
  Switch,
  Image,
  message,
  Tabs,
  Form,
  DatePicker,
  Select,
  Typography,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { timeStamp, getDay } from 'common/date';
import moment from 'moment';
import { QrCodeBase } from 'common/qrcode';
import { qs2obj } from 'common/object';
import LinkCard from 'components/LinkCard/home';
import SysDictLabel from 'components/select/SysDictLabel';
import WibotTableTag from 'components/WibotTableTag/home';
import { Line } from '@ant-design/plots';
import './details.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const { TabPane } = Tabs;
const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const { Paragraph } = Typography;

const ChannelDetails = (props) => {
  const [id, setId] = useState(null);
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [tabsTrendIndex, setTabsTrendIndex] = useState('1');
  const [tabsDetailsIndex, setTabsDetailsIndex] = useState('1');
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [chartsData, setChartsData] = useState(null);
  const visitColumns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '访问人',
      width: '180px',
      dataIndex: 'customerName',
      key: 'customerName',
      align: 'center',
      render: (value, record, index) => {
        const companyName = (
          <span style={{ color: record.type == 1 ? '#07c160' : '#f59a23' }}>
            {record.companyName}
          </span>
        );
        const title = (
          <div>
            {value}
            {companyName}
          </div>
        );
        const content = (
          <div style={{ display: 'flex' }}>
            {record.customerAvatar && (
              <Avatar size={40} src={record.customerAvatar} />
            )}
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              {title}
              <span>{record.realName}</span>
            </div>
          </div>
        );
        return (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        );
      },
    },
    {
      title: '管户员工/负责人',
      width: '180px',
      dataIndex: 'employeeName',
      key: 'employeeName',
      align: 'center',
      render: (value, record, index) => {
        // const companyName = <span style={record.type == '微信用户' ? { color: '#07c160' } : { color: '#f59a23' }}>{record.departmentName}</span>;
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: 'flex' }}>
            {record.employeeAvatar && (
              <Avatar size={40} src={record.employeeAvatar} />
            )}
            <div
              style={{
                marginLeft: '6px',
                width: '120px',
                textAlign: 'left',
                whiteSpace: 'normal',
              }}
            >
              {title}
              <span>{record.departmentName}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ''
        );
      },
    },
    {
      title: '首次访问时间',
      width: '160px',
      dataIndex: 'firstVisitTime',
      key: 'firstVisitTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.firstVisitTime) - timeStamp(b.firstVisitTime),
    },
    {
      title: '访问时间',
      width: '160px',
      dataIndex: 'visitTime',
      key: 'visitTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.visitTime) - timeStamp(b.visitTime),
    },
    {
      title: '累计访问次数',
      width: '160px',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDetails(record)}>客户详情</a>
        </>
      ),
    },
  ];
  const resourceColumns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '资源',
      width: '250px',
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      render: (value, record, index) => (
        <div style={{ textAlign: 'left' }}>
          {(record.type == 'Article' || record.type == 'pageArticle') && (
            <LinkCard data={record} />
          )}
        </div>
      ),
    },
    {
      title: '推荐理由',
      width: '160px',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) =>
        value ? (
          <Tooltip placement="topLeft" title={value}>
            <Paragraph ellipsis={{ rows: 1 }} style={{ whiteSpace: 'pre' }}>
              {value}
            </Paragraph>
          </Tooltip>
        ) : (
          '-'
        ),
    },
    {
      title: '参考话术',
      width: '160px',
      dataIndex: 'script',
      key: 'script',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) =>
        value ? (
          <Tooltip placement="topLeft" title={value}>
            <Paragraph ellipsis={{ rows: 1 }} style={{ whiteSpace: 'pre' }}>
              {value}
            </Paragraph>
          </Tooltip>
        ) : (
          '-'
        ),
    },
    {
      title: '资源类型',
      width: '160px',
      dataIndex: 'type',
      key: 'type',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <SysDictLabel dataset="Resource_Type" dictkey={value} />
      ),
    },
    {
      title: '场景类型',
      width: '160px',
      dataIndex: 'sceneParentName',
      key: 'sceneParentName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => {
        if (record.type == 'Product') {
          return '-';
        } else {
          return (
            <Tooltip placement="topLeft" title={value + `-${record.sceneName}`}>
              {value + `-${record.sceneName}`}
            </Tooltip>
          );
        }
      },
    },
    {
      title: '资源标签',
      width: '160px',
      dataIndex: 'infoTagNames',
      key: 'infoTagNames',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'customerTagNames',
      key: 'customerTagNames',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '数据',
      width: '160px',
      dataIndex: 'visitCount',
      key: 'visitCount',
      align: 'center',
      render: (value, record, index) => (
        <div style={{ textAlign: 'left' }}>
          转发次数：{record.shareCount}
          <br />
          访问次数：{record.visitCount}
          <br />
          访问人数：{record.visitorCount}
        </div>
      ),
    },
    {
      title: '资源状态',
      width: '160px',
      dataIndex: 'resourceStatus',
      key: 'resourceStatus',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '上下架时间',
      width: '160px',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.startTime}
          <br />至<br />
          {record.endTime}
        </>
      ),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.createEmployeeName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];
  const codeColumns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '活码名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '活码二维码',
      width: '180px',
      dataIndex: 'url',
      key: 'url',
      align: 'center',
      render: (value, record, index) => {
        const image = value ? QrCodeBase({ url: value }) : '';
        return <FileHOC src={image}>
          {(url) => <Image width={60} src={url} preview={url}></Image>}
        </FileHOC>;
      },
    },
    {
      title: '访问数据',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => (
        <div style={{ textAlign: 'left' }}>
          访问次数：{record.visitCount}
          <br />
          访问人数：{record.visitorCount}
        </div>
      ),
    },
    {
      title: '客户标签',
      width: '160px',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      align: 'center',
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: '活码类型',
      width: '160px',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (value, record, index) => (
        <SysDictLabel dataset="WECOM_CODE_TYPE" dictkey={value} />
      ),
    },
    {
      title: '活码对象',
      width: '160px',
      dataIndex: 'objectName',
      key: 'objectName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '关联定位',
      width: '160px',
      dataIndex: 'location',
      key: 'location',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '备用客服',
      width: '160px',
      dataIndex: 'reserveEmployeeName',
      key: 'reserveEmployeeName',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '启用状态',
      width: '160px',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (value, record, index) => (
        <Switch
          checkedChildren="已启用"
          unCheckedChildren="已停用"
          checked={value}
          onChange={(checked) => {
            onChangeSwitchStatus(checked, record);
          }}
        />
      ),
    },
    {
      title: '更新时间',
      width: '160px',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: '创建人/创建时间',
      width: '160px',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: (value, record, index) => (
        <>
          {record.creatorName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
  ];

  useEffect(() => {
    const { id } = qs2obj(props.location.search);
    if (id) {
      setId(id);
      init(id);
    }
  }, []);

  const init = async (id) => {
    await fetchList({ detailId: id });
    await getTabsDataDetails({ detailId: id, tabIdx: '1' });
  };

  const fetchList = async (params = {}) => {
    setLoading(true);
    const { detailId, time, tabIdx = '1' } = params;
    const data = {
      id: detailId || id,
      startDate:
        time && time.length > 0
          ? time[0]
          : moment(getDay(-30)).format('YYYY-MM-DD'),
      endDate:
        time && time.length > 0
          ? time[1]
          : moment(getDay(-1)).format('YYYY-MM-DD'),
    };
    await apiCall('/activity/channel/detail_data', 'GET', data)
      .then((res) => {
        setUserInfo(res);
        setChartsData(res.visitorChart.list);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getTabsDataDetails = async (params = {}) => {
    setLoading(true);
    const { pagination, query, detailId, tabIdx } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    const data = {
      channelIds: detailId || id,
      current: pageInfo.current,
      size: pageInfo.pageSize,
      ...query,
    };

    let apiUrl = '';
    switch (tabIdx) {
    case '1':
      apiUrl = '/activity/channel/data';
      break;
    case '2':
      apiUrl = '/info/infoResource';
      break;
    case '3':
      apiUrl = '/activity/dynamicCode/page';
      break;
    default:
      break;
    }
    apiCall(apiUrl, 'GET', data)
      .then((res) => {
        const { records, current, pages, size, total } = res;
        setDataSource(records ?? []);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getMomentMaterialDom = (data) => {
    if (data) {
      if (data.friendCircleType == 'TEXT_LINK') {
        const newData = {
          title: data.linkTitle,
          description: data.linkDescription,
          image: data.linkCoverUrl,
          url: data.link,
        };
        return data.linkTitle ? (
          <LinkCard data={newData} />
        ) : (
          <div>{data.link}</div>
        );
      } else if (data.friendCircleType == 'TEXT_IMAGE') {
        return (
          <Image.PreviewGroup>
            {data.images?.map((item, index) => (
              <div
                key={index}
                style={{
                  display: 'inline-block',
                  width: '60px',
                  margin: '10px 10px 0 0',
                }}
              >
                <FileHOC src={item}>
                  {(url) => (
                    <Image src={url} />
                  )}
                </FileHOC>
              </div>
            ))}{' '}
          </Image.PreviewGroup>
        );
      } else {
        return (
          <FileHOC src={data.videos && data.videos[0]}>
            {(url) => (
              <video
                style={{ width: '100%' }}
                controls
                src={url}
              />
            )}
          </FileHOC>
        );
      }
    } else {
      return '-';
    }
  };

  // 启用状态
  const onChangeSwitchStatus = (checked, record) => {
    const data = {
      id: record.id,
      state: checked,
    };
    apiCall('/activity/dynamicCode/changeStateById', 'PUT', data)
      .then((res) => {
        message.success('修改成功！');
        getTabsDataDetails({ tabIdx: '3' });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleDetails = (record) => {
    const { customerId } = record;
    props.history.push({
      pathname: '/wecom/customer/details',
      search: `?id=${customerId}`,
    });
  };

  const onChangeTabsDataTrend = (index) => {
    const time = formRef.current.getFieldValue('time');
    setTabsTrendIndex(index);
    fetchList({
      tabIdx: index,
      time: time && [
        time[0].format('YYYY-MM-DD'),
        time[1].format('YYYY-MM-DD'),
      ],
    });
  };

  const onChangeQuickTime = async (value) => {
    let time = [];
    switch (value) {
    case '0':
      time = [
        moment(getDay(value), 'YYYY-MM-DD'),
        moment(getDay(value), 'YYYY-MM-DD'),
      ];
      fetchList({
        time: [
          moment(getDay(value)).format('YYYY-MM-DD'),
          moment(getDay(value)).format('YYYY-MM-DD'),
        ],
      });
      break;
    case '-1':
      time = [
        moment(getDay(value), 'YYYY-MM-DD'),
        moment(getDay(value), 'YYYY-MM-DD'),
      ];
      fetchList({
        time: [
          moment(getDay(value)).format('YYYY-MM-DD'),
          moment(getDay(value)).format('YYYY-MM-DD'),
        ],
      });
      break;
    case '-7':
      time = [
        moment(getDay(value), 'YYYY-MM-DD'),
        moment(getDay(-1), 'YYYY-MM-DD'),
      ];
      fetchList({
        time: [
          moment(getDay(value)).format('YYYY-MM-DD'),
          moment(getDay(-1)).format('YYYY-MM-DD'),
        ],
      });
      break;
    case '-15':
      time = [
        moment(getDay(value), 'YYYY-MM-DD'),
        moment(getDay(-1), 'YYYY-MM-DD'),
      ];
      fetchList({
        time: [
          moment(getDay(value)).format('YYYY-MM-DD'),
          moment(getDay(-1)).format('YYYY-MM-DD'),
        ],
      });
      break;
    case '-30':
      time = [
        moment(getDay(value), 'YYYY-MM-DD'),
        moment(getDay(-1), 'YYYY-MM-DD'),
      ];
      fetchList({
        time: [
          moment(getDay(value)).format('YYYY-MM-DD'),
          moment(getDay(-1)).format('YYYY-MM-DD'),
        ],
      });
      break;
    }
    if (value) {
      await formRef.current.setFieldsValue({
        time,
      });
    }
  };

  const onChangeTime = async (date, dateString) => {
    fetchList({ time: date ? dateString : null });
    formRef.current.setFieldsValue({
      quickTime: null,
    });
  };

  const onChangeTabsDataDetails = (index) => {
    setTabsDetailsIndex(index);
    getTabsDataDetails({
      tabIdx: index,
      pagination: { current: 1, pageSize: 10 },
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    getTabsDataDetails({ pagination, tabIdx: tabsDetailsIndex });
  };

  const handleGoBack = () => {
    props.history.go(-1);
  };

  const DemoLine = (data) => {
    const config = {
      data,
      xField: 'date',
      yField: 'number',
      label: {},
      point: {
        size: 5,
        shape: 'diamond',
        style: {
          fill: 'white',
          stroke: '#5B8FF9',
          lineWidth: 2,
        },
      },
      tooltip: {
        showMarkers: false,
      },
      state: {
        active: {
          style: {
            shadowBlur: 4,
            stroke: '#000',
            fill: 'red',
          },
        },
      },
      interactions: [
        {
          type: 'marker-active',
        },
      ],
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          rotate: -45, // 倾斜角度
          offset: '20',
          formatter: function (value) {
            if (value.length > 4) {
              return value.substring(5);
            }
            return value;
          }
        },
      },
      meta: {
        number: {
          alias: '总数',
        },
      },
      slider: {
        start: 0,
        end: 1,
      },
    };
    return <Line {...config} />;
  };

  return (
    <div className="ChannelDetails">
      <Spin spinning={loading}>
        <Card
          title={<>渠道数据（{userInfo.channelName}）</>}
          extra={
            <Button type="primary" onClick={() => handleGoBack()}>
              返回
            </Button>
          }
          bordered={false}
          bodyStyle={{ display: 'none' }}
        ></Card>
        <br />
        <Row gutter={[16, 0]}>
          <Col xs={24} lg={12}>
            <Card title="关联情况" className="data-screening">
              {JSON.stringify(userInfo) != '{}' ? (
                <Row>
                  <Col span={12}>
                    <span className="num">{userInfo.resourceCount || 0}</span>
                    <p className="visit">资源中心</p>
                  </Col>
                  <Col span={12}>
                    <span className="num">{userInfo.codeCount || 0}</span>
                    <p className="visit">渠道活码</p>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="访问数据" className="data-screening">
              {JSON.stringify(userInfo) != '{}' ? (
                <Row>
                  <Col span={12}>
                    <span className="num">{userInfo.visitCount || 0}</span>
                    <Tooltip title="同一用户可访问多次">
                      <p className="tip">
                        累计访问次数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问次数：{userInfo.visitCountToday}
                    </p>
                  </Col>
                  <Col span={12}>
                    <span className="num">{userInfo.visitorCount || 0}</span>
                    <Tooltip title="同一用户访问多次算一次">
                      <p className="tip">
                        累计访问人数
                        <QuestionCircleOutlined />
                      </p>
                    </Tooltip>
                    <p className="visit">
                      今日访问人数：{userInfo.visitorCountToday}
                    </p>
                  </Col>
                </Row>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Card>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Card bordered={false} title="数据趋势">
              <Tabs
                activeKey={tabsTrendIndex}
                destroyInactiveTabPane
                onChange={onChangeTabsDataTrend}
              >
                <TabPane tab="访问人数" key="1"></TabPane>
                <TabPane tab="访问次数" key="2">
                  {' '}
                </TabPane>
              </Tabs>
              <Form layout={'inline'} ref={formRef}>
                <FormItem label="统计时间" name="time" initialValue={[moment(getDay(-30)), moment(getDay(-1))]}>
                  <RangePicker allowClear={false} format="YYYY-MM-DD" onChange={onChangeTime} />
                </FormItem>
                <FormItem label="快捷时间" name="quickTime" initialValue={'-30'}>
                  <Select
                    style={{ width: '200px' }}
                    options={[
                      {
                        label: '今天',
                        value: '0',
                      },
                      {
                        label: '昨天',
                        value: '-1',
                      },
                      {
                        label: '最近7天',
                        value: '-7',
                      },
                      {
                        label: '最近15天',
                        value: '-15',
                      },
                      {
                        label: '最近30天',
                        value: '-30',
                      },
                    ]}
                    onChange={onChangeQuickTime}
                  />
                </FormItem>
              </Form>
              {chartsData && DemoLine(chartsData) || <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </Card>
          </Col>
        </Row>
        <Card bordered={false} title="数据明细">
          <Tabs
            activeKey={tabsDetailsIndex}
            destroyInactiveTabPane
            onChange={onChangeTabsDataDetails}
          >
            <TabPane tab="访问明细" key="1">
              <Table
                rowKey="customerId"
                dataSource={dataSource}
                columns={visitColumns}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
            <TabPane tab="资源中心" key="2">
              <Table
                rowKey="id"
                dataSource={dataSource}
                columns={resourceColumns}
                scroll={{ x: 1300 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
            <TabPane tab="渠道活码" key="3">
              <Table
                rowKey="id"
                dataSource={dataSource}
                columns={codeColumns}
                scroll={{ x: 1800 }}
                pagination={paginations}
                onChange={onChangeTable}
              />
            </TabPane>
          </Tabs>
        </Card>
      </Spin>
    </div>
  );
};

export default withRouter(ChannelDetails);
