/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/13 09:37
 * @LastEditTime: 2023/11/21 15:54
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/CustomerChannle/home.jsx
 * @Description: '获客渠道'
 */

import React, { useEffect, useRef, useState } from "react";
import { withRouter } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  Table,
  Tooltip,
  TreeSelect,
  DatePicker,
  Switch,
  Select,
  message,
} from "antd";
import { removeInputEmpty } from "common/regular";
import { apiCall } from "common/utils";
import { timeStamp } from "common/date";
import moment from "moment";
import FilterBar from "components/FilterBar/FilterBar";
import OperateModal from "components/Modal/OperateModal/index";
import TypeFormModal from "./comps/TypeFormModal";
import FormModal from "./comps/FormModal";
import { recursionTagKeyTreeData, recursionTagKeyTreeDataTag } from "common/tree"
import ETypeTransferModal from "components/TransferModal/EmployeeType/home";
import WibotTableTag from 'components/WibotTableTag/home';
import ListOperation from 'components/ListOperation/home';
import CustomTagSelect from "@/view/wecom/CustomerManagement/Customer/comps/CustomTagSelect"

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;
const { RangePicker } = DatePicker;

const Channel = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [createMode, setCreateMode] = useState([]);
  const [typeFormParams, setTypeFormParams] = useState({ visible: false });
  const [formParams, setFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [labelTreeData, setLabelTreeData] = useState([]);
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "渠道名称",
      width: "160px",
      dataIndex: "name",
      key: "name",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "渠道编码",
      width: "160px",
      dataIndex: "id",
      key: "id",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "渠道类型",
      width: "160px",
      dataIndex: "typeName",
      key: "typeName",
      align: "center",
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: "关联情况",
      width: "160px",
      dataIndex: "codeRefCount",
      key: "codeRefCount",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          渠道活码：{value} 个
          <br />
          关联资源：{record.resourceRefCount} 个
        </div>
      ),
    },
    {
      title: "访问数据",
      width: "160px",
      dataIndex: "statVO",
      key: "statVO",
      align: "center",
      render: (value, record, index) => (
        <div style={{ textAlign: "left" }}>
          访问次数：{record?.visitCount} 次
          <br />
          访问人数：{record?.visitorCount} 人
        </div>
      ),
    },
    {
      title: "客户标签",
      width: "160px",
      dataIndex: "tagNameList",
      key: "tagNameList",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "启用状态",
      width: "160px",
      dataIndex: "state",
      key: "state",
      align: "center",
      render: (value, record, index) => (
        <Switch
          checkedChildren="已启用"
          unCheckedChildren="已停用"
          checked={value}
          onChange={(checked) => {
            onChangeSwitchStatus(checked, record);
          }}
        />
      ),
    },
    {
      title: "更新时间",
      width: "160px",
      dataIndex: "updateTime",
      key: "updateTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.updateTime) - timeStamp(b.updateTime),
    },
    {
      title: "创建人/创建时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      render: (value, record, index) => (
        <>
          {record.creatorName}
          <br />
          {value}
        </>
      ),
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "操作",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleData(record), name: "数据" },
          { onClick: () => handleDelete(record), name: "删除" },
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getcreateModeType();
    getTagCategoryTreeTwo();
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.createTime) {
        formData.startCreateTime = moment(formData.createTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endCreateTime = moment(formData.createTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.createTime;
      }
      if (formData.updateTime) {
        formData.startUpdateTime = moment(formData.updateTime[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endUpdateTime = moment(formData.updateTime[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.updateTime;
      }
      console.log(`[formData]: `, formData)
      if (formData.typeIds) {
         formData.typeIds = String(formData.typeIds).split(",")
      }
      // formData.tagIds = formData.tagIds?.join(",") || null;
      // formData.createDeptIdList = formData.createDeptIdList?.join(",") || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall("/activity/channel/page", "POST", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const getcreateModeType = () => {
    apiCall("/activity/channel/getChannelTypes", "GET")
      .then((res) => {
        setCreateMode(res);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 获取标签树数据
  const getTagCategoryTreeTwo = async () => {
    const data = {
      type: "customer",
    };
    await apiCall("/info/tag/tagGroup/tree", "GET", data)
      .then((res) => {
        const tagTreeData = recursionTagKeyTreeDataTag(res);
        // setLabelTreeData([
        //   {
        //     title: "全选",
        //     value: "customer",
        //     key: "customer",
        //     children: tagTreeData,
        //   },
        // ]);
        setLabelTreeData(tagTreeData);
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const onChangeSwitchStatus = (checked, record) => {
    const data = {
      id: record.id,
      state: checked,
    };
    apiCall("/activity/channel/changeStateById", "PUT", data)
      .then((res) => {
        message.success("修改成功！");
        fetchList();
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      isAdd: true,
      onOk: (params) => {
        apiCall("/activity/channel/add", "POST", params)
          .then((res) => {
            message.success("新增成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };
  const handleType = () => {
    setTypeFormParams({
      visible: true,
      title: "渠道类型",
      type: "CHANNEL",
      onCancel: () => {
        setTypeFormParams({ visible: false });
        getcreateModeType()
      },
    });
  };

  const handleData = (record) => {
    const { id } = record;
    props.history.push({
      pathname: "/wecom/customerChannle/details",
      search: `?id=${id}`,
    });
  };

  const handleEdit = (record) => {
    setFormParams({
      visible: true,
      isAdd: false,
      tagData: record,
      onOk: (params) => {
        apiCall("/activity/channel/modify", "PUT", params)
          .then((res) => {
            message.success("修改成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: "删除确认",
      content: `您将删除活码【${name}】，删除后活码立即失效，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id,
        };
        apiCall("/activity/channel/del", "DELETE", data)
          .then((res) => {
            message.success("删除成功！");
            fetchList();
          })
          .catch((err) => {
            console.log(err);

          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="Channel">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="渠道名称" allowClear />
          </FormItem>
          <FormItem
            name="createDeptIdList"
            style={{ minWidth: "unset", maxWidth: "200px", marginRight: "0px" }}
          >
            <ETypeTransferModal title="创建人" needDisableDepFlag={false} />
          </FormItem>
          <FormItem name="typeIds">
            <Select
              placeholder="渠道类型"
              fieldNames={{ label: "name", value: "id" }}
              options={createMode}
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? "").toLowerCase().includes(input.toLowerCase())
              }
            />
          </FormItem>
          {/* <FormItem name="tagIds"> */}
          {/*   <TreeSelect */}
          {/*     treeData={labelTreeData} */}
          {/*     treeCheckable */}
          {/*     treeDefaultExpandedKeys={["customer"]} */}
          {/*     allowClear */}
          {/*     showArrow */}
          {/*     showSearch */}
          {/*     maxTagCount="responsive" */}
          {/*     showCheckedStrategy={SHOW_PARENT} */}
          {/*     treeNodeFilterProp="title" */}
          {/*     placeholder="客户标签" */}
          {/*   /> */}
          {/* </FormItem> */}
          <CustomTagSelect
            creatable
            name="tagNameList"
            placeholder="客户标签"
            useRefForm={formRef}
            labelTreeData={labelTreeData}
          />
          <FormItem name="createTime" label="创建时间">
            <RangePicker />
          </FormItem>
          <FormItem name="updateTime" label="更新时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleType()}>
              渠道类型
            </Button>
            <Button type="primary" onClick={() => handleAdd()}>
              创建渠道
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <TypeFormModal params={typeFormParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default withRouter(Channel);
