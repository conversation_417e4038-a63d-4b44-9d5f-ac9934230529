/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/03/31 14:23
 * @LastEditTime: 2025/07/15 15:55
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/EmployeeTag/comps/FormModal.jsx
 * @Description: '员工标签-表单(新增/编辑)'
 */

import React, { useState, useEffect, memo } from 'react';
import { Form, Modal, Input } from 'antd';
import { apiCall } from 'common/utils';
import './FormModal.less';

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 },
};

const FormModal = (props) => {
  const { visible, tagData, onOk, onCancel } = props.params;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [formRef] = Form.useForm();
  const [maxLength, setMaxLength] = useState(15);

  useEffect(() => {
    if (visible) {
      if (tagData) {
        const { name } = tagData;
        formRef.setFieldsValue({ 'name': name });
      }
    }
  }, [visible]);

  const handleOk = () => {
    formRef.validateFields().then((formData) => {
      setConfirmLoading(true);
      const { id = null } = tagData || {};
      const { name } = formData;
      let apiUrl = ""
      if (id) {
        apiUrl = `/employee_tag/modify`
      } else {
        apiUrl = "/employee_tag/add"
      }
      const data = {
        id: id,
        name: name,
      };
      apiCall(apiUrl, "POST", data)
        .then((res) => {
          onReset()
          onOk?.()
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setConfirmLoading(false)
        })
    });
  };

  const handleCancel = () => {
    onReset();
    onCancel?.();
  };

  const onReset = () => {
    setConfirmLoading(false);
    formRef.resetFields();
  };


  return (
    <Modal
      className='EmployeeTag-FormModal'
      title={tagData ? '编辑标签' : '新增标签'}
      visible={visible}
      maskClosable={false}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <Form {...layout} form={formRef}>
        <FormItem name="name" label="标签名称" rules={[{ required: true, message: '请输入标签名称' }]}>
          <Input
            placeholder="请输入标签名称（中文最多15字）"
            allowClear
            maxLength={maxLength}
            onChange={(e) => {
              const value = e.target.value;
              let triggerValue = '';
              if (/^[\u4e00-\u9fa5]+$/i.test(value)) {
                triggerValue = value.slice(0, 15);
                setMaxLength(15);
              } else {
                triggerValue = value.slice(0, 30);
                setMaxLength(30);
              }
              formRef.setFieldsValue({
                name: triggerValue,
              });
            }}
          />
        </FormItem>
      </Form>
    </Modal>
  );
};

export default memo(FormModal);
