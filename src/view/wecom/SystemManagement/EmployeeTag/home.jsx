/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/03/13 09:33
 * @LastEditTime: 2024/10/21 16:34
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/EmployeeTag/home.jsx
 * @Description: '员工标签'
 */

import React, { useState, useEffect, useRef } from 'react';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { Form, Input, Button, Table, Card, Tooltip, message } from 'antd';
import FormModal from './comps/FormModal';
import OperateModal from 'components/Modal/OperateModal/index';
import ListOperation from 'components/ListOperation/home';

const FormItem = Form.Item;

const EmployeeTag = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [formParams, setFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '标签',
      width: '300px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      ellipsis: 'true',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '使用人数',
      width: '160px',
      dataIndex: 'useCount',
      key: 'useCount',
      ellipsis: 'true',
      align: 'center',
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" }
        ];
        if (record.type == 1) {
          opts.push({ onClick: () => handleDelete(record, index), name: "删除" });
        }
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall('/employee_tag/page', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      type: 'resource',
      onOk: () => {
        setFormParams({ visible: false });
        message.success('新增成功！');
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  const handleEdit = (record, index) => {
    setFormParams({
      visible: true,
      tagData: record,
      type: 'resource',
      onOk: () => {
        setFormParams({ visible: false });
        message.success('修改成功！');
        fetchList();
      },
      onCancel: () => {
        setFormParams({ visible: false });
      },
    });
  };

  const handleDelete = (record, index) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `删除标签【${name}】会同步删除员工的标签，确认继续吗？`,
      onSubmit: () => {
        apiCall(`/employee_tag/del?id=${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="EmployeeTag">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="标签名称" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增标签
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <FormModal params={formParams} />
      <OperateModal params={operateParams} />
    </div>
  );
};

export default EmployeeTag;
