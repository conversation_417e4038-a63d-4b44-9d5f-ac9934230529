/*
 * @Author: <PERSON>eiw
 * @Date: 2023/12/07 09:55
 * @LastEditTime: 2024/09/09 09:29
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/IcbcConfig/home.jsx
 * @Description: '工行配置'
 */


import React, { useState, useEffect } from 'react';
import { Button, Card, Form, message, Space, Spin, Radio, } from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import {isV1, isV2, versionFnMap} from "config";

const layout = {
  // labelCol: {
  //   xs: { span: 24 },
  //   sm: { span: 4 },
  // },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const IcbcConfig = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getGlobalSetting();
  }, []);

  const getGlobalSetting = () => {
    setLoading(true);
    apiCall('/globalSetting/icbc_config', 'GET',).then((res) => {
      formForm.setFieldsValue(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      setLoading(true);
      const data = {
        ...formData,
      };
      apiCall(`/globalSetting/icbc_config`, 'POST', data).then((res) => {
        message.success('配置成功！');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  const tips = `
  1.同一企微账号一天内添加客户数量超过500个时，自动发送预警通知给指定的通知对象。
  ${isV2() ? `2.同一企微账号添加客户总数量抵达1.8w时，自动发送预警通知给指定的通知对象。`: ''}
  `

  return (
    <div className='IcbcConfig-Container'>
      <Card bordered={false} >
        <Spin spinning={loading}>
          <Form form={formForm} {...layout}>
            <Form.Item label="群发任务允许通知目标范围" name="batchTaskType" initialValue={'ONLY_DEVICE'} required={false}>
              <Radio.Group>
                <Radio value="ALL"> 全部员工 </Radio>
                <Radio value="ONLY_DEVICE"> 仅托管账号 </Radio>
              </Radio.Group>
            </Form.Item>

            {
              isV1() ?
                  <Form.Item
                      label="客户补认证的通知对象"
                      name="authNotifyList"
                      rules={[{required: false, message: '请选择通知对象'}]}
                  >
                    <ETypeTransferModal title="通知对象" onlyEmployee multiple/>
                  </Form.Item> :
                  null
            }

            <Form.Item
              label="客户量超限预警通知"
              name="customerOverloadNotifyList"
              rules={[{ required: false, message: '请选择通知对象' }]}
              tooltip={tips}
            >
              <ETypeTransferModal title="通知对象" onlyEmployee multiple />
            </Form.Item>
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Spin>
      </Card>
    </div>
  );
};

export default IcbcConfig;
