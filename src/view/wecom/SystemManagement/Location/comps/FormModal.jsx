/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/25 14:50
 * @LastEditTime: 2024/10/21 16:22
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/Location/comps/FormModal.jsx
 * @Description: '定位配置-表单(新增/编辑)'
 */

import React, { memo, useEffect, useRef, useState } from 'react';
import { Form, Input, Modal, Spin, TreeSelect, Upload } from 'antd';
import { base64ToFile, beforeUpload, compressImage } from 'common/image';
import { normFile, removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import TMap from 'components/Map/TMap/home';
import BMap from 'components/Map/BMap/home';
import WibotUploadImage from 'components/WibotUploadImage/home';

const FormItem = Form.Item;
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 },
};

const FormModal = (props) => {
  const formRef = useRef(null);
  const onRefTmap = useRef();
  const [id, setId] = useState(null);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [depOptions, setDepOptions] = useState([]);
  const [isInit, setIsInit] = useState(null);

  useEffect(async () => {
    const { visible, formData = null } = props.params;
    setVisible(visible);
    if (visible) {
      setLoading(true);
      await getLocationDisableIdList();
      // let centerLatLng = formData.latitude && formData.longitude ? { lat: formData.latitude, lng: formData.longitude } : null;
      // onRefTmap.current.initTMapLoader(centerLatLng).then((res) => { });
      if (formData) {
        const { logo } = formData;
        await formRef?.current?.setFieldsValue({
          ...formData,
          fileId: logo ? [logo] : [],
          centerLatLng: {
            lng: formData.longitude,
            lat: formData.latitude,
            address: formData.formattedAddress,
          }
        });
        setImageUrl(logo ?? '');
        setIsInit(false);
      } else {
        setIsInit(true);
      }
    }
  }, [props]);

  const getLocationDisableIdList = async () => {
    setLoading(true);
    await apiCall('/base/location/disableIdList', 'GET',).then((res) => {
      getDepOptions(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getDepOptions = async (filterArr) => {
    setLoading(true);
    await apiCall('/dept/option', 'GET',).then((res) => {
      setDepOptions(recursionTreeData(res, filterArr));
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const recursionTreeData = (treeData, filterArr) => {
    const newTreeData = [];
    let obj = {};
    treeData.forEach((item) => {
      const tmp = { ...item };
      if (tmp.childList) {
        // 内部调用即递归
        tmp.childList = recursionTreeData(tmp.childList, filterArr);
        obj = {
          ...tmp,
          disabled: filterArr.indexOf(tmp.id) > -1
        };
      }
      newTreeData.push(obj);
    });
    return newTreeData;
  };

  const onChangeUpload = (info) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
  };

  const customRequest = (config) => {
    const File = config.file;
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    apiCall('/file/image', 'POST', data).then((res) => {
      const { fileId, fileUrl } = res;
      setImageUrl(fileUrl);
      formRef.current.setFieldsValue({
        fileId: [fileId]
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setUploadLoading(false);
      });
  };

  const handleResetUpload = (e) => {
    e.preventDefault();// 阻止浏览器默认行为
    e.stopPropagation();// 阻止事件冒泡
    setImageUrl('');
  };

  const onOk = () => {
    // const LatLng = onRefTmap.current.getLatLng();
    formRef.current.validateFields().then((formData) => {
      props.params?.onOk?.({
        ...formData,
        latitude: formData.centerLatLng.lat,
        longitude: formData.centerLatLng.lng,
        formattedAddress: formData.centerLatLng.address,
      });
      onCancel();
    });
  };

  const onCancel = () => {
    setLoading(false);
    setId(null);
    setConfirmLoading(false);
    setUploadLoading(false);
    setImageUrl('');
    setIsInit(null);
    // formRef.current.resetFields();
    // onRefTmap.current.destroyMap();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={800}
      title={id ? '编辑定位' : '创建定位'}
      maskClosable={false}
      centered
      afterClose={null}
      destroyOnClose
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <Form {...layout} ref={formRef}>

          <FormItem name="name" label="定位名称" getValueFromEvent={(e) => removeInputEmpty(e)} rules={[{ required: true, message: '请输入定位名称（30字以内）' }]}>
            <Input placeholder="请输入定位名称" allowClear maxLength={30} />
          </FormItem>

          <FormItem name="departmentId" label="关联组织" rules={[{ required: true, message: '请输入关联组织' }]}>
            <TreeSelect
              treeData={depOptions}
              treeDefaultExpandAll
              allowClear
              showArrow
              showSearch
              fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              treeNodeFilterProp='name'
              maxTagCount="responsive"
              placeholder='关联组织'
            />
          </FormItem>

          <FormItem name="fileId" valuePropName="fileList" getValueFromEvent={normFile} label="定位Logo"
            extra="大小限制为2M，最多上传1张" >
            <Upload
              name="file"
              customRequest={customRequest}
              listType="picture-card"

              showUploadList={false}
              beforeUpload={beforeUpload}
              onChange={onChangeUpload}
            >
              <WibotUploadImage
                imageUrl={imageUrl}
                loading={uploadLoading}
                onClose={handleResetUpload}
              />
            </Upload>
          </FormItem>

          <FormItem name="phone" label="联系方式" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="请输入联系方式" allowClear maxLength={11} />
          </FormItem>

          <FormItem name="address" label="详细地址" getValueFromEvent={(e) => removeInputEmpty(e)} >
            <Input placeholder="请输入详细地址" allowClear />
          </FormItem>

          {/* <FormItem label="选择定位" name="centerLatLng" rules={[{ required: false, message: '请选择定位' }]}>
            <TMap ref={onRefTmap} />
          </FormItem> */}

          <FormItem label="选择定位" name="centerLatLng" rules={[{ required: false, message: '请选择定位' }]}>
            <BMap isInit={isInit} onChange={(center) => { }} />
          </FormItem>

        </Form>
      </Spin>
    </Modal>
  );
};

export default memo(FormModal);
