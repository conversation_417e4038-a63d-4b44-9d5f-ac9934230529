/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/26 17:22
 * @LastEditTime: 2024/10/21 16:22
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/Location/home.jsx
 * @Description: '企业定位配置'
 */

import React, { useEffect, useRef, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { Button, Card, Form, Input, Table, Tooltip, Image, TreeSelect, message } from 'antd';
import { removeInputEmpty } from 'common/regular';
import { apiCall } from 'common/utils';
import FilterBar from 'components/FilterBar/FilterBar';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal';
import ListOperation from 'components/ListOperation/home';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { SHOW_PARENT } = TreeSelect;

const Location = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [dataSource, setDataSource] = useState([]);
  const [depOptions, setDepOptions] = useState([]);
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [formParams, setFormParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '定位名称',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '定位LOGO',
      width: '160px',
      dataIndex: 'logo',
      key: 'logo',
      align: 'center',
      render: (value, record, index) => <FileHOC src={value ?? ''}>
        {(url) => (
          <Image width={60} src={url} preview={url}></Image>
        )}
      </FileHOC>,
    },
    {
      title: '关联组织',
      width: '160px',
      dataIndex: 'departmentName',
      key: 'departmentName',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '联系方式',
      width: '160px',
      dataIndex: 'phone',
      key: 'phone',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '详细地址',
      width: '160px',
      dataIndex: 'address',
      key: 'address',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  useEffect(() => {
    getdepOptions();
    fetchList();
  }, []);

  const getdepOptions = () => {
    setLoading(true);
    apiCall('/dept/option', 'GET',).then((res) => {
      setDepOptions(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };


  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      formData.departmentIds = formData.departmentIds?.join(',') || null;

      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };

      apiCall('/base/location/page', 'GET', data).then((res) => {
        const { records, current, size, total, pages } = res;
        setDataSource(records);
        setPaginations({
          current: current,
          pageSize: size,
          total: total,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) => `共 ${total} 条记录 第${current}/${pages}页`,
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  const handleAdd = () => {
    setFormParams({
      visible: true,
      isAdd: true,
      onOk: (params) => {
        const { departmentId, address, name, phone, fileId, longitude, latitude, formattedAddress } = params;
        const data = {
          address,
          name,
          phone,
          logo: fileId && fileId[0],
          longitude,
          latitude,
          formattedAddress,
          departmentId
        };
        apiCall('/base/location/add', 'POST', data).then((res) => {
          message.success('新增成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);

          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  const handleEdit = (record) => {
    setFormParams({
      visible: true,
      formData: record,
      onOk: (params) => {
        const { departmentId, address, name, phone, fileId, longitude, latitude, formattedAddress } = params;
        const data = {
          id: record.id,
          address,
          name,
          phone,
          logo: fileId && fileId[0],
          longitude,
          latitude,
          formattedAddress,
          departmentId
        };
        apiCall('/base/location/modify', 'PUT', data).then((res) => {
          message.success('修改成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除定位【${name}】，确认继续吗？`,
      onSubmit: () => {
        const data = {
          id
        };
        apiCall('/base/location/del', 'DELETE', data).then((res) => {
          message.success('删除成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className='Location'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="name" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="定位名称" allowClear />
          </FormItem>
          <FormItem name="departmentIds">
            <TreeSelect
              treeData={depOptions}
              treeCheckable
              treeDefaultExpandedKeys={depOptions.length > 0 ? [depOptions[0].id] : []}
              allowClear
              showArrow
              showSearch
              fieldNames={{ label: 'name', value: 'id', children: 'childList' }}
              treeNodeFilterProp='name'
              maxTagCount="responsive"
              showCheckedStrategy={SHOW_PARENT}
              placeholder='组织架构'
            />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>
              新增定位
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table rowKey="id" loading={loading} dataSource={dataSource} columns={columns} scroll={{ x: 1300 }}
          pagination={paginations} onChange={onChangeTable} />
      </Card>
      <OperateModal params={operateParams} />
      <FormModal params={formParams} />
    </div>
  );
};

export default withRouter(Location);
