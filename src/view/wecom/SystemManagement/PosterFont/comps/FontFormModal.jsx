/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/04/20 09:24
 * @LastEditTime: 2023/04/26 11:35
 * @LastEditors: Lixiaoyan
 * @FilePath: \weebot_cloud_webfront\src\view\wecom\SystemManagement\PosterFont\comps\FontFormModal.jsx
 * @Description: '上传字体文件弹窗'
 */

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, message, Spin, Button, Upload } from 'antd';
import { apiCall } from 'common/utils';
import { UploadOutlined } from '@ant-design/icons';
import { clearCache } from 'react-router-cache-route';

const FormItem = Form.Item;

const FontFormModal = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [courseTypeOption, setCourseTypeOption] = useState([]);
  const [fileData, setFileData] = useState([]);

  useEffect(() => {
    const { visible } = props.params;
    if (visible) {
      setVisible(true);
      // getCourseTypeOption();
    }
  }, [props]);

  // 获取课程类型
  const getCourseTypeOption = (id) => {
    apiCall('/study/courseType', 'GET')
      .then((res) => {
        const options = res.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        setCourseTypeOption(options);
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  // 选择文件
  const customFileRequest = (config) => {
    const File = config.file;
    setFileData([
      {
        name: File.name,
        status: 'uploading',
      },
    ]);
    // 通过FormData构造函数创建一个空对象
    const formData = new FormData();
    // 通过append方法来追加数据
    formData.append('file', File); // 返回压缩后的图片
    const data = formData;
    setFileData([
      {
        name: File.name,
        fileUrl: data,
        status: 'done',
      },
    ]);
    formForm.setFieldsValue({
      file: File.name,
    });
  };
  const onRemoveUpload = (file) => {
    setFileData([]);
  };

  const onOk = () => {
    formForm.validateFields().then((formData) => {
      setConfirmLoading(true);
      apiCall('/info/posterFont/addFont', 'POST', fileData[0].fileUrl)
        .then((res) => {
          message.success('新增成功！');
          props.params?.onSubmit?.();
          onCancel();
        })
        .catch((err) => {
          console.log(err);

        })
        .finally(() => {
          setConfirmLoading(false);
        });
    });
  };

  const onCancel = () => {
    setVisible(false);
    setLoading(false);
    setConfirmLoading(false);
    setFileData([]);
    formForm.resetFields();
    props.params?.onCancel?.();
  };

  return (
    <Modal
      visible={visible}
      width={600}
      title="上传字体文件"
      maskClosable={false}
      afterClose={null}
      destroyOnClose
      centered
      confirmLoading={confirmLoading}
      onCancel={onCancel}
      onOk={onOk}
      className="courseModal"
    >
      <Spin spinning={loading}>
        <Form form={formForm}>
          <FormItem
            label="字体文件压缩包"
            name="file"
            extra="请选择一个zip格式的字体文件，单次上传不超过100M"
            rules={[{ required: true, message: '请上传文件' }]}
          >
            <Upload
              name="file"
              fileList={fileData}
              customRequest={customFileRequest}
              beforeUpload={(file) => {
                console.log(file, 'filefilefile');
                const isFile = file.type.includes('zip');
                if (!isFile) {
                  message.error('请上传zip格式的字体文件!');
                }
                return isFile;
              }}
              // onChange={onChangeUpload}
              onRemove={onRemoveUpload}
            >
              {fileData.length <= 0 ? (
                <Button icon={<UploadOutlined />}>选择文件</Button>
              ) : null}
            </Upload>
          </FormItem>
          <div style={{ color: '#aaaaaa', fontSize: '13px' }}>
            <div>
              1.
              一套字体的上传需同时上传字体名称相同的2种字体格式（WOFF、TFF）才能生效
            </div>
            <div>
              2.
              请将一套或多套相同名称的2种格式的字体文件压缩在同个文件夹中，压缩格式为zip，请勿加密
            </div>
            <div>
              3.
              由于海报编辑时会实时下载字体，可能会导致占用的网络带宽较多，建议上传小文件的字体
            </div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default FontFormModal;
