/*
 * @Author: <PERSON><PERSON>oyan
 * @Date: 2023/04/19 17:14
 * @LastEditTime: 2023/11/21 15:56
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/PosterFont/home.jsx
 * @Description: '海报字体管理'
 */

import React, { useState, useEffect, useRef } from 'react';
import { withRouter } from 'react-router-dom';
import {
  Button,
  Card,
  Form,
  Input,
  DatePicker,
  Table,
  Tooltip,
  message,
} from 'antd';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { timeStamp } from 'common/date';
import { removeInputEmpty } from 'common/regular';
import moment from 'moment';
import OperateModal from 'components/Modal/OperateModal/index';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import FontFormModal from './comps/FontFormModal';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const PosterFont = (props) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const [fontFormParams, setFontFormParams] = useState({ visible: false });
  const columns = [
    {
      title: '序号',
      width: '80px',
      align: 'center',
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: '字体名称',
      width: '160px',
      dataIndex: 'fontName',
      key: 'fontName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '上传时间',
      width: '160px',
      dataIndex: 'uploadTime',
      key: 'uploadTime',
      align: 'center',
      sorter: (a, b) => timeStamp(a.uploadTime) - timeStamp(b.uploadTime),
    },
    {
      title: '操作人',
      width: '160px',
      dataIndex: 'createEmployeeName',
      key: 'createEmployeeName',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => (
        <Tooltip placement="topLeft" title={value}>{value}</Tooltip>
      ),
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDelete(record)}>删除</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          'YYYY-MM-DD 00:00:00'
        );
        formData.endTime = moment(formData.time[1]._d).format(
          'YYYY-MM-DD 23:59:59'
        );
        delete formData.time;
      }
      formData.createEmployeeId = formData.createEmployeeId?.join(',') || null;
      const { pagination, query } = params;
      const pageInfo = pagination || { current: 1, pageSize: 10 };
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...query,
        ...formData,
      };
      apiCall('/info/posterFont/page', 'GET', data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 新增
  const handleFont = () => {
    setFontFormParams({
      visible: true,
      onSubmit: () => {
        setFontFormParams({ visible: false });
        fetchList();
      },
      onCancel: () => {
        setFontFormParams({ visible: false });
      },
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { fontName, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `您将删除的字体名称为【${fontName}】，确认继续？`,
      onSubmit: () => {
        apiCall(`/info/posterFont/del/${id}`, 'POST')
          .then((res) => {
            message.success('删除成功！');
            fetchList();
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="PosterFont">
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem
            name="fontName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="字体名称" allowClear />
          </FormItem>
          <FormItem
            name="createEmployeeId"
            style={{ minWidth: 'unset', maxWidth: '200px', marginRight: '0px' }}
          >
            <ETypeTransferModal title="操作人" onlyEmployee />
          </FormItem>
          <FormItem name="time" label="上传时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleFont()}>
              上传字体
            </Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
      <OperateModal params={operateParams} />
      <FontFormModal params={fontFormParams} />
      {/* <TypeFormModal params={typeFormParams} />
      <CourseFormModal params={courseFormParams} />
      <PreviewModal {...previewParams} /> */}
    </div>
  );
};

export default withRouter(PosterFont);
