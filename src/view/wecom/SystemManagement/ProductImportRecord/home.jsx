/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/31 15:51
 * @LastEditTime: 2023/11/15 11:37
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/ProductImportRecord/home.jsx
 * @Description: '产品导入记录'
 */

import React, { useState, useEffect, useRef } from "react";
import { withRouter } from "react-router-dom";
import FilterBar from "components/FilterBar/FilterBar";
import moment from "moment";
import { timeStamp } from "common/date";
import { apiCall } from "common/utils";
import { removeInputEmpty } from "common/regular";
import {
  Form,
  Input,
  Button,
  Table,
  Card,
  Tooltip,
  Avatar,
  DatePicker,
} from "antd";
import WibotTableTag from 'components/WibotTableTag/home';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;

const ProductImportRecord = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [paginations, setPaginations] = useState({ current: 1, pageSize: 10 });
  const columns = [
    {
      title: "序号",
      width: "80px",
      align: "center",
      render: (text, record, index) => index + 1,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "导入人",
      width: "160px",
      dataIndex: "employeeName",
      key: "employeeName",
      ellipsis: "true",
      align: "center",
      render: (value, record, index) => {
        const title = <div>{value}</div>;
        const content = (
          <div style={{ display: "flex" }}>
            <Avatar size={40} src={record.avatar} />
            <div
              style={{
                marginLeft: "6px",
                width: "140px",
                textAlign: "left",
                whiteSpace: "normal",
              }}
            >
              {title}
              <span>{record.departmentName || "-"}</span>
            </div>
          </div>
        );
        return value ? (
          <Tooltip title={title} placement="topLeft">
            {content}
          </Tooltip>
        ) : (
          ""
        );
      },
    },
    {
      title: "导入时间",
      width: "160px",
      dataIndex: "createTime",
      key: "createTime",
      align: "center",
      sorter: (a, b) => timeStamp(a.createTime) - timeStamp(b.createTime),
    },
    {
      title: "导入成功产品数",
      width: "160px",
      dataIndex: "succeedCount",
      key: "succeedCount",
      ellipsis: "true",
      align: "center",
    },
    {
      title: "导入成功产品名称",
      width: "160px",
      dataIndex: "productNames",
      key: "productNames",
      align: "center",
      render: (value, record, index) => <WibotTableTag tagList={value} />,
    },
    {
      title: "原文件下载",
      width: "120px",
      align: "center",
      fixed: "right",
      render: (value, record, index) => (
        <>
          <a onClick={() => handleDownload(record, index)}>下载</a>
        </>
      ),
    },
  ];

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      if (formData.time) {
        formData.startTime = moment(formData.time[0]._d).format(
          "YYYY-MM-DD 00:00:00"
        );
        formData.endTime = moment(formData.time[1]._d).format(
          "YYYY-MM-DD 23:59:59"
        );
        delete formData.time;
      }
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        ...formData,
      };
      apiCall("/info/infoResource/listProductRecord", "GET", data)
        .then((res) => {
          const { records, current, size, total, pages } = res;
          setDataSource(records);
          setPaginations({
            current: current,
            pageSize: size,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `共 ${total} 条记录 第${current}/${pages}页`,
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 下载
  const handleDownload = (record, index) => {
    const { fileId } = record;
    location.href = fileId;
  };

  const onChangeTable = (pagination, filters, sorter) => {
    fetchList({ pagination });
  };

  return (
    <div className="sceneSetting">
      <FilterBar>
        <Form layout={"inline"} ref={formRef}>
          <FormItem
            name="employeeName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="导入人" allowClear />
          </FormItem>
          <FormItem
            name="productName"
            getValueFromEvent={(e) => removeInputEmpty(e)}
          >
            <Input placeholder="导入产品名称" allowClear />
          </FormItem>
          <FormItem name="time" label="导入时间">
            <RangePicker />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} bodyStyle={{ padding: "unset" }}>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 1300 }}
          pagination={paginations}
          onChange={onChangeTable}
        />
      </Card>
    </div>
  );
};

export default withRouter(ProductImportRecord);
