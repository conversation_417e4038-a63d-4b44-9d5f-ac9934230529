/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/04 09:59
 * @LastEditTime: 2023/09/21 11:05
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/SceneSetting/comps/FormModal.jsx
 * @Description: '场景-编辑'
 */
import React, { useState, memo, useEffect } from 'react';
import { Form, Input, Modal, Button, Upload, Row, Col, message, Tag } from 'antd';
import { compressImage, base64ToFile, beforeUpload } from 'common/image';
import { FormOutlined } from '@ant-design/icons';
import { apiCall } from 'common/utils';
import { removeInputEmpty, normFile } from 'common/regular';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import DragSort from 'components/DragSort/home';
import WibotUploadImage from 'components/WibotUploadImage/home';
import './FormModal.less';

const FormItem = Form.Item;

const FormModal = (props) => {
  const { visible, id, onOk, onCancel } = props.params;
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [typeList, setTypeList] = useState([]);
  const [imageUrl, setImageUrl] = useState('');
  const [isTagEdit, setIsTagEdit] = useState(false);
  const [editIndex, setEditIndex] = useState(0);

  useEffect(() => {
    if (visible) {
      if (id) {
        getByIdData(id);
      }
    }
  }, [visible]);

  const getByIdData = (id) => {
    apiCall(`/info/scene/${id}`, 'GET').then((res) => {
      const { image, childList } = res;
      setImageUrl(image);
      setTypeList(childList ?? []);
      formForm.setFieldsValue({
        ...res,
        image: image && [image] || [],
        subname: childList?.length > 0 ? childList[0] : ''
      });
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  const customRequest = (config) => {
    const File = config.file;
    const options = {
      width: 500,
      height: 500,
      quality: 0.9,
    };
    compressImage(File, options).then((result) => {
      // 通过FormData构造函数创建一个空对象
      const formData = new FormData();
      // 通过append方法来追加数据
      formData.append('file', base64ToFile(result, File.name)); // 返回压缩后的图片
      const data = formData;
      apiCall('/file/image', 'POST', data).then((res) => {
        const { fileId, fileUrl } = res;
        setImageUrl(fileUrl);
        formForm.setFieldsValue({
          image: [fileId]
        });
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
          setConfirmLoading(false);
        });
    });
  };

  const handleChange = (info) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      setConfirmLoading(true);
      return;
    }
  };

  const handleResetUpload = (e) => {
    e.preventDefault();// 阻止浏览器默认行为
    e.stopPropagation();// 阻止事件冒泡
    setImageUrl('');
    formForm.setFieldsValue({
      image: ''
    });
  };

  // 确认新增添加场景小类
  const onSureSensit = () => {
    formForm.validateFields(['tagWord']).then((formData) => {
      const { tagWord } = formData;
      const newTypeList = typeList;
      if (!!tagWord && !new RegExp(/^[ ]+$/).test(tagWord) && newTypeList.every((item) => item.name != tagWord)) {
        newTypeList.push({ name: tagWord });
      } else if (newTypeList.some((item) => item.name == tagWord)) {
        message.error('场景小类已存在！');
      }
      setTypeList([...newTypeList]);
      formForm.setFieldsValue({
        tagWord: '',
      });
    });
  };

  // 确认修改场景小类
  const onSureEdit = () => {
    formForm.validateFields(['tagWord']).then((formData) => {
      const { tagWord } = formData;
      const newTypeList = typeList;
      if (!!tagWord && !new RegExp(/^[ ]+$/).test(tagWord) && newTypeList.every((item) => item.name != tagWord)) {
        newTypeList[editIndex].name = tagWord;
      } else if (newTypeList[editIndex].name != tagWord && newTypeList.some((item) => item.name == tagWord)) {
        message.error('场景小类已存在！');
      }
      setTypeList([...newTypeList]);
      setIsTagEdit(false);
      formForm.setFieldsValue({
        tagWord: '',
      });
    });

  };

  // 取消修改场景小类
  const onCancelEdit = () => {
    setIsTagEdit(false);
    formForm.setFieldsValue({
      tagWord: '',
    });
  };

  // 编辑场景小类
  const handleEditTag = (value, index) => {
    setIsTagEdit(true);
    setEditIndex(index);
    formForm.setFieldsValue({
      subname: value.name,
      tagWord: value.name,
    });
  };

  // 场景小类输入框改变
  const handleChangeTagWord = (e) => {
    formForm.setFieldsValue({
      subname: e.target.value ? e.target.value : 'subname',
    });
  };

  // 删除场景小类
  const handleTagListClose = (e, value) => {
    e.preventDefault();
    const data = typeList.filter((item) => item.name !== value.name);
    setTypeList(data);
    if (!data.length > 0) {
      formForm.setFieldsValue({
        subname: '',
      });
    }
  };

  // 拖拽排序
  const changePosition = (dragIndex, hoverIndex) => {
    const data = [...typeList];
    const temp = data[dragIndex];
    // 交换位置
    data[dragIndex] = data[hoverIndex];
    data[hoverIndex] = temp;
    setTypeList(data);
  };

  // 提交
  const handleSubmit = () => {
    formForm.validateFields(['name', 'image', 'description']).then((formData) => {
      if (!typeList.length > 0) {
        message.error('请添加场景小类！');
        return;
      }
      formData.image = formData.image[0];
      formData.childList = typeList;
      onOk?.(formData);
      handleCancel();
    });
  };

  const handleCancel = () => {
    formForm.resetFields();
    setImageUrl('');
    setTypeList([]);
    setIsTagEdit(false);
    setLoading(false);
    setConfirmLoading(false);
    onCancel?.();
  };

  return (
    <Modal visible={visible}
      title={id ? '编辑场景' : '新增场景'}
      maskClosable={false}
      afterClose={null}
      onOk={handleSubmit}
      onCancel={handleCancel}
      destroyOnClose
      confirmLoading={confirmLoading}
      width={800}
      centered
      className='scene-modal'
    >
      <Form labelCol={{ span: 8 }}
        wrapperCol={{ span: 18 }} form={formForm}>

        <div className='row-modal-wrap'>
          <div className='col-tree-left'>
            <FormItem name="name" label="场景大类名称" getValueFromEvent={(e) => removeInputEmpty(e)} rules={
              [{ required: true, message: '请输入场景大类名称（5字内）', }]}
            >
              <Input placeholder="请输入场景大类名称（5字内）" maxLength={5} allowClear />
            </FormItem>
            <FormItem name="image" label="场景大类图片" valuePropName="fileList" getValueFromEvent={normFile} rules={[{ required: true, message: '请上传图片', }]}
              extra="PNG格式，透明底，100*100">
              <Upload
                name="file"
                customRequest={customRequest}
                listType="picture-card"

                showUploadList={false}
                beforeUpload={beforeUpload}
                onChange={handleChange}
              >
                <WibotUploadImage
                  imageUrl={imageUrl}
                  loading={loading}
                  onClose={handleResetUpload}
                />
              </Upload>
            </FormItem>
            <FormItem name="description" label="场景大类介绍">
              <Input.TextArea placeholder="请输入场景大类介绍（100字）" allowClear showCount maxLength={100} autoSize={{ minRows: 2, maxRows: 4 }} />
            </FormItem>
          </div>
          <div className='col-tree-right'>
            <FormItem label="场景小类"
              labelCol={{ span: 6 }}
              name="subname" rules={[{ required: true, message: '', }]} style={{ marginBottom: '0px' }}
              extra={!isTagEdit ? <div>
                <DndProvider backend={HTML5Backend}>
                  {
                    typeList.map((item, index) =>
                      <DragSort key={index} id={item.id} index={index} changePosition={changePosition}>
                        {
                          <>
                            <Tag
                              closable
                              onClose={(e) => { handleTagListClose(e, item); }}
                              style={{ marginRight: '5px' }}
                            >
                              {item.name}
                            </Tag>
                            <FormOutlined className='tag-edit-icon' onClick={() => { handleEditTag(item, index); }} />
                          </>
                        }
                      </DragSort>
                    )
                  }
                </DndProvider>
              </div> : `原名称：${typeList[editIndex]?.name}`}>
              <Row style={{ width: '315px' }}>
                <Col span={14}>
                  <FormItem name="tagWord" style={{ margin: 'unset' }} rules={[{ required: true, message: '请输入场景小类名称（5字内）', }]} getValueFromEvent={(e) => removeInputEmpty(e)}
                  >
                    <Input placeholder="请输入场景小类名称" maxLength={5} allowClear onPressEnter={isTagEdit ? onSureEdit : onSureSensit} onChange={handleChangeTagWord} />
                  </FormItem>
                </Col>
                <Col span={10}>
                  {
                    isTagEdit ? <>
                      <Button type="primary" onClick={onSureEdit} style={{ width: '55px', padding: '4px 10px', marginRight: '5px' }}>保存</Button>
                      <Button type="primary" onClick={onCancelEdit} style={{ width: '55px', padding: '4px 10px' }}>取消</Button>
                    </>
                      : <Button type="primary" onClick={onSureSensit} style={{ width: '55px', padding: '4px 10px' }}>添加</Button>
                  }
                </Col>
              </Row>
            </FormItem>
          </div>
        </div>
      </Form>
    </Modal>
  );
};
export default memo(FormModal);
