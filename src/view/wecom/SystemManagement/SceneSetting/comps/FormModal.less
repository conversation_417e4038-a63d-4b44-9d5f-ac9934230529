.scene-modal {
  .row-modal-wrap {
    display: flex;
    flex-direction: row;

    .col-tree-left {
      flex: 1;
      // border: 1px solid #e5e5e5;
      border-left: unset;
      border-right: unset;
      border-bottom: unset;
      margin-right: 25px;
    }

    .col-tree-right {
      flex: 1;
      padding: 10px;
      padding-top: 0px;
      border: 1px solid #e5e5e5;
      border-right: unset;
      border-bottom: unset;
      border-top: unset;

      .tag-item {
        display: inline-block;
        margin-top: 5px;
        margin-right: 8px;
      }

      .tag-edit-icon {
        &:hover {
          color: #5d8dd4;
        }
      }

      .ant-form-item-with-help .ant-form-item-explain {
        width: 200px;
      }

      .ant-form-item-extra {
        position: absolute;
        top: 50px;
      }
    }

    .ant-input-textarea {
      textarea {
        height: 100px !important;
      }
    }
  }
}