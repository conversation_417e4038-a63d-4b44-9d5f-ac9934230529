/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/08/04 09:45
 * @LastEditTime: 2023/11/21 16:31
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/SceneSetting/home.jsx
 * @Description: '场景类型'
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { withRouter } from 'react-router-dom';
import FilterBar from 'components/FilterBar/FilterBar';
import { apiCall } from 'common/utils';
import { removeInputEmpty } from 'common/regular';
import { PlusOutlined, MenuOutlined } from '@ant-design/icons';
import { Form, Input, Button, Table, Card, Tooltip, Image, Tag, Popover, message, Typography } from 'antd';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import OperateModal from 'components/Modal/OperateModal/index';
import FormModal from './comps/FormModal';
import ListOperation from 'components/ListOperation/home';
import './home.less';
import { FileHOC } from 'components/FileHOC/FileHOC';

const FormItem = Form.Item;
const { Paragraph } = Typography;

const type = 'DraggableBodyRow';
const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef();
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};
      if (dragIndex === index) {
        return {};
      }
      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward',
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    },
  });
  const [, drag] = useDrag({
    type,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  drop(drag(ref));

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{ cursor: 'move', ...style }}
      {...restProps}
    />
  );
};

const SceneSetting = (props) => {
  const formRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [formParams, setFormParams] = useState({ visible: false });
  const [operateParams, setOperateParams] = useState({ visible: false });
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '拖动排序',
      width: '80px',
      dataIndex: 'sort',
      align: 'center',
      render: (value, record, index) => <>
        <MenuOutlined />
        <span style={{ marginLeft: '10px' }}>{index + 1}</span>
      </>,
    },
    {
      title: '场景大类',
      width: '160px',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      render: (value, record, index) => {
        const content = <div style={{ display: 'flex', alignItems: 'center' }}>
          <Paragraph ellipsis={{ rows: 1, tooltip: false }}>
            <FileHOC src={record.image || 'error'}>
              {(url) => (
                <Image
                  width={60}
                  src={url}
                  fallback="images/fallbackImg.png"
                  preview
                />
              )}
            </FileHOC>
            <span style={{ marginLeft: '10px' }}>{value}</span></Paragraph>
        </div>;
        return (
          content
        );
      }
    },
    {
      title: '场景大类介绍',
      width: '160px',
      dataIndex: 'description',
      key: 'description',
      // ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}><span style={{ whiteSpace: 'pre-line' }}>{value || '-'}</span></Tooltip >,
    },
    {
      title: '场景小类',
      width: '160px',
      dataIndex: 'childList',
      key: 'childList',
      align: 'center',
      render: (value, record, index) => {
        const content = <>
          {
            value?.map((item, idx) => <Tag style={{ whiteSpace: 'pre-line' }} key={idx}>{item.name}</Tag>) || ''
          }
        </>;
        return <div style={{ textAlign: 'left' }}>
          {value?.length > 0 ? <Popover content={content} placement="topLeft">
            <div style={{ marginBottom: '10px' }}>
              {
                value.map((atem, andex) => <Tag key={andex} closable onClose={(e) => { handleCloseTag(e, record, value, atem); }} style={{ marginBottom: '5px' }}>{atem.name}</Tag>)
              }
            </div>
          </Popover> : ''}
          {record.isAddWords ? <>
            <Input style={{ width: '160px' }} placeholder="请输入场景小类名称" maxLength={5} onBlur={(e) => { onInputBlur(e, index); }} onChange={(e) => { handleInputValue(e); }}
              allowClear />
            <Button
              type="primary"
              onClick={() => handleQueryFilter(record, index)}
              size="small"
              style={{ width: 50, marginLeft: '5px' }}
            >
              确定
            </Button>
          </> : <Button type="primary" icon={<PlusOutlined />} size="small" onClick={() => { handleAddTag(index); }}>新增</Button>}
        </div>;
      },
    },
    {
      title: '场景小类数量',
      width: '160px',
      dataIndex: 'childrenLength',
      key: 'childrenLength',
      ellipsis: 'true',
      align: 'center',
      render: (value, record, index) => <Tooltip placement="topLeft" title={value}>{value}</Tooltip>,
      sorter: (a, b) => a.childrenLength - b.childrenLength
    },
    {
      title: '操作',
      width: '120px',
      align: 'center',
      fixed: 'right',
      render: (value, record, index) => {
        let opts = [
          { onClick: () => handleEdit(record), name: "编辑" },
          { onClick: () => handleDelete(record), name: "删除" }
        ];
        return <ListOperation opts={opts} />;
      },
    },
  ];

  const components = {
    body: {
      row: DraggableBodyRow,
    },
  };
  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      setLoading(true);
      const dragRow = dataSource[dragIndex];
      const data = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow],
        ],
      });
      const params = data.map((item) => item.id);
      apiCall('/info/scene/sort/update', 'PUT', params).then((res) => {
        message.success('修改成功！');
        setDataSource(
          update(dataSource, {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, dragRow],
            ],
          }),
        );
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [dataSource],
  );

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = (params = {}) => {
    setLoading(true);
    const { pagination } = params;
    const pageInfo = pagination || { current: 1, pageSize: 10 };
    formRef.current.validateFields().then((formData) => {
      const data = {
        current: pageInfo.current,
        size: pageInfo.pageSize,
        paged: false,
        ...formData
      };
      apiCall('/info/scene', 'GET', data).then((res) => {
        const { records } = res;
        setDataSource(records);
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const handleQuery = () => {
    fetchList();
  };

  const handleReset = () => {
    formRef.current.resetFields();
    fetchList();
  };

  // 添加场景小类
  const handleInputValue = (e) => {
    setInputValue(e.target.value);
  };

  const handleQueryFilter = (record, index) => {
    const { id, childList } = record;
    if (inputValue) {
      const nameList = childList?.map((item) => item.name);
      if (nameList.some((item) => item == inputValue) && !new RegExp(/^[ ]+$/).test(inputValue)) {
        message.error('场景小类已存在！');
        return;
      }
      handleSceneAddOrmodify({
        name: inputValue,
        parentId: id
      });
      setInputValue('');
    } else {
      message.error('请输入场景小类名称！');
    }
  };

  const handleSceneAddOrmodify = (params) => {
    setLoading(true);
    apiCall('/info/scene', 'POST', params).then((res) => {
      fetchList();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onInputBlur = (e, index) => {
    if (!inputValue) {
      const newDataSource = dataSource;
      newDataSource[index].isAddWords = false;
      setDataSource([...newDataSource]);
    }
  };

  const handleAddTag = (index) => {
    const newDataSource = dataSource;
    newDataSource[index].isAddWords = true;
    setDataSource([...newDataSource]);
  };

  // 删除场景小类
  const handleCloseTag = (e, record, value, atem) => {
    e.preventDefault();
    console.log(atem);
    apiCall(`/info/scene/${atem.id}`, 'DELETE').then((res) => {
      message.success('删除成功！');
      fetchList();
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
      });
  };

  // 新增
  const handleAdd = () => {
    setFormParams({
      visible: true,
      onOk: (params) => {
        apiCall('/info/scene', 'POST', params).then((res) => {
          message.success('新增成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  // 修改
  const handleEdit = (record, index) => {
    const { id } = record;
    setFormParams({
      visible: true,
      id,
      onOk: (params) => {
        apiCall(`/info/scene/${id}`, 'PUT', params).then((res) => {
          message.success('修改成功！');
          fetchList();
        })
          .catch((err) => {
          });
        setFormParams({ visible: false });
      },
      onCancel: () => {
        setFormParams({ visible: false });
      }
    });
  };

  // 删除
  const handleDelete = (record) => {
    const { name, id } = record;
    setOperateParams({
      visible: true,
      title: '删除确认',
      content: `确认删除场景大类【${name}】？`,
      onSubmit: () => {
        apiCall(`/info/scene/${id}`, 'DELETE').then((res) => {
          message.success('删除成功！');
          fetchList();
        })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            setOperateParams({
              visible: false,
            })
          });
      }, onCancel: () => {
        setOperateParams({
          visible: false,
        })
      }
    });
  };

  return (
    <div className='sceneSetting'>
      <FilterBar>
        <Form layout={'inline'} ref={formRef}>
          <FormItem name="searchStr" getValueFromEvent={(e) => removeInputEmpty(e)}>
            <Input placeholder="场景名称、场景大类介绍" allowClear />
          </FormItem>
        </Form>
        <div className="flex flex-space-between">
          <div>
            <Button type="primary" onClick={() => handleQuery()}>
              查询
            </Button>
            <Button onClick={() => handleReset()}>重置筛选</Button>
          </div>
          <div>
            <Button type="primary" onClick={() => handleAdd()}>新增场景</Button>
          </div>
        </div>
      </FilterBar>
      <Card bordered={false} >
        <DndProvider backend={HTML5Backend}>
          <Table
            rowKey="id"
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 1300 }}
            pagination={false}
            components={components}
            onRow={(record, index) => ({
              index,
              moveRow,
            })}
          />
        </DndProvider>
      </Card>
      <FormModal params={formParams} />
      <OperateModal params={operateParams} />
    </div>
  );
};

export default withRouter(SceneSetting);
