/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/04/06 10:03
 * @LastEditTime: 2024/10/31 13:59
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/SideBarSetting/home.jsx
 * @Description: '企微配置'
 */

import React, { useState, useEffect } from 'react';
import { Button, Card, Form, Input, Steps, message, Space, Spin } from 'antd';
import { apiCall } from 'common/utils';
import ETypeTransferModal from 'components/TransferModal/EmployeeType/home';
import WibotCopyBtn from 'components/WibotCopyBtn/home';
import WibotMaterialCenter from 'components/WibotMaterialCenter/home';

const FormItem = Form.Item;
const { Step } = Steps;
const layout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

import './home.less';

const SideBarSetting = (props) => {
  const [formForm1] = Form.useForm();
  const [formForm2] = Form.useForm();
  const [listLoading, setListLoading] = useState(false);
  const [formLoading1, setFormLoading1] = useState(false);
  const [formLoading2, setFormLoading2] = useState(false);
  const [list, setList] = useState([]);
  const [authUrl, setAuthUrl] = useState("");

  useEffect(() => {
    getAuthUrl();
    getAuthWhiteList();
    getwxcpConfig();
    getSideBarInfo();
  }, []);

  const getAuthUrl = () => {
    apiCall('/globalSetting/authUrl', 'GET',).then((res) => {
      setAuthUrl(res)
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => { });
  };

  const getAuthWhiteList = () => {
    setFormLoading1(true);
    apiCall('/globalSetting/authWhiteList', 'GET',).then((res) => {
      formForm1.setFieldValue('authWhitelist', res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setFormLoading1(false);
      });
  };

  const getwxcpConfig = () => {
    setFormLoading2(true);
    apiCall('/globalSetting/wxcp_config', 'GET',).then((res) => {
      const { accountOfflineNotifyList } = res;
      formForm2.setFieldValue('accountOfflineNotifyList', accountOfflineNotifyList);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setFormLoading2(false);
      });
  };

  const getSideBarInfo = () => {
    setListLoading(true);
    apiCall('/base/sideBar', 'GET',).then((res) => {
      setList(res);
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setListLoading(false);
      });
  };

  const onSubmit1 = () => {
    formForm1.validateFields().then((formData) => {
      setFormLoading1(true);
      const data = {
        ...formData,
      };
      apiCall(`/globalSetting/authWhiteList`, 'POST', data).then((res) => {
        message.success('配置成功！');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setFormLoading1(false);
        });
    });
  }

  const onSubmit2 = () => {
    formForm2.validateFields().then((formData) => {
      setFormLoading2(true);
      const data = {
        ...formData,
      };
      apiCall(`/globalSetting/wxcp_config`, 'POST', data).then((res) => {
        message.success('配置成功！');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setFormLoading2(false);
        });
    });
  }

  return (
    <div className='SideBarSetting-Container'>
      <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
        <Card title="二次验证配置" bordered={false} >
          <Spin spinning={formLoading1}>
            <Form {...layout} className='listFormBox'>
              <FormItem label="验证地址">
                <>
                  <Input value={authUrl} readOnly />
                  <WibotCopyBtn text={authUrl} title={<Button type="primary">复制</Button>} />
                </>
              </FormItem>
            </Form>

            <Form form={formForm1} {...layout} >
              <FormItem
                label="白名单"
                name="authWhitelist"
                rules={[{ required: false, message: '请选择白名单' }]}
              >
                <ETypeTransferModal title="白名单" />
              </FormItem>

              {
                process.env.NODE_ENV === 'development' ? <FormItem label="素材列表">
                  <WibotMaterialCenter formRef={formForm1} />
                </FormItem> : ''
              }
            </Form>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Space size={40}>
                <Button type="primary" onClick={() => onSubmit1()}>保存</Button>
              </Space>
            </div>
          </Spin>
        </Card>

        <Card title="应用通知配置" bordered={false} >
          <Spin spinning={formLoading2}>
            <Form form={formForm2}  >
              <FormItem
                label="集约管控账号离线通知"
                name="accountOfflineNotifyList"
              >
                <ETypeTransferModal title="选择员工" onlyEmployee />
              </FormItem>
            </Form>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Space size={40}>
                <Button type="primary" onClick={() => onSubmit2()}>保存</Button>
              </Space>
            </div>
          </Spin>
        </Card>

        <Card title="企微侧边栏菜单配置" bordered={false} loading={listLoading}>
          <Steps>
            <Step status="process" title="步骤一" description="打开企业微信后台，依次选择【客户与上下游>聊天工具>聊天工具栏管理】" />
            <Step status="process" title="步骤二" description="点击【+配置应用页面】，选择本应用" />
            <Step status="process" title="步骤三" description="在弹窗中选择本应用，点击下一步；新弹窗页面内容选择自定义，随后填写页面名称及粘贴链接即可。" />
          </Steps>
          <h3 >配置链接</h3>
          <Form {...layout} className='listFormBox'>
            {
              list.map((item, index) =>
                <FormItem key={index} label={item.name} name={item.name} >
                  <>
                    <Input value={item.url} readOnly />
                    <WibotCopyBtn text={item.url} title={<Button type="primary">复制</Button>} />
                  </>
                </FormItem>
              )
            }
          </Form>
        </Card>
      </Space>
    </div>
  );
};

export default SideBarSetting;
