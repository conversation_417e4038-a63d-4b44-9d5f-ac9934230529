/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023/12/07 09:55
 * @LastEditTime: 2024/09/09 09:29
 * @LastEditors: Janaeiw
 * @FilePath: /weebot_cloud_webfront/src/view/wecom/SystemManagement/IcbcConfig/home.jsx
 * @Description: '工行配置'
 */

import moment from "moment";
import React, {useEffect, useState} from 'react';
import {Button, Card, Form, message, Space, Spin, Switch, TimePicker,} from 'antd';
import {apiCall} from 'common/utils';

const layout = {
  // labelCol: {
  //   xs: { span: 24 },
  //   sm: { span: 4 },
  // },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const deviceConfig = (props) => {
  const [formForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [enable, setEnable] = useState(false)

  useEffect(() => {
    getGlobalSetting();
  }, []);

  const getGlobalSetting = () => {
    setLoading(true);
    apiCall('/globalSetting/device_config', 'GET',).then((res) => {
      console.log(`[res]: `, res)
      const {enable, startTime, endTime} = res
      console.log(`[moment(startTime)]: `, moment(startTime, 'HH:mm'))
      formForm.setFieldsValue({
        enable,
        time: startTime && endTime ? [moment(startTime, 'HH:mm'), moment(endTime, 'HH:mm')] : [],
      });
      if (enable) {
        setEnable(true)
      }
    })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onSubmit = () => {
    formForm.validateFields().then((formData) => {
      const data = {
        enable: formData.enable,
      }
      if (formData.time?.length) {
        data.startTime = formData.time[0]?.format('HH:mm')
        data.endTime = formData.time[1]?.format('HH:mm')
      }
      if (data.enable) {
        if (!data.startTime || !data.endTime) {
          return message.error('请选择时间范围')
        }
      }
      console.log(`[data]: `, data)
      setLoading(true);
      apiCall(`/globalSetting/device_config`, 'POST', data).then((res) => {
        message.success('配置成功！');
      })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }

  return (
    <div className=''>
      <Card bordered={false} >
        <Spin spinning={loading}>
          <Form form={formForm}  {...layout}>
            <Form.Item label={'定时重启企业微信应用配置'} name="enable" valuePropName="checked" tooltip="仅启用状态下，每日在指定时间段内对集约账号上的企微应用进行重启操作">
              <Switch onChange={setEnable}/>
            </Form.Item>

            {
                enable && (
                    <Form.Item
                        required
                        label="每日重启时间段"
                        name="time"
                        rules={[{required: false, type: 'array', message: '请选择时间范围'}]}
                    >
                      <TimePicker.RangePicker format={'HH:mm'}/>
                    </Form.Item>
                )
            }
          </Form>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space size={40}>
              <Button type="primary" onClick={() => onSubmit()}>保存</Button>
            </Space>
          </div>
        </Spin>
      </Card>
    </div>
  );
};

export default deviceConfig;
