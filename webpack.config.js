const autoprefixer = require("autoprefixer")
const path = require("path")
const webpack = require("webpack")
const htmlWebpackPlugin = require("html-webpack-plugin")
const CopyWebpackPlugin = require("copy-webpack-plugin")
const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer")
const srcPath = path.join(__dirname, "/src")
const DotenvWebpackPlugin = require("dotenv-webpack")
const webpackHashManifestPlugin = require("webpack-hash-manifest-plugin")
module.exports = (env, argv) => {
  let dotenvConfig = {
    path: "./.env." + env,
    defaults: "./.env",
  }
  require(`dotenv-defaults`).config(dotenvConfig)

  console.log(`当前环境信息：
  NODE_ENV(模式名称)=${process.env.NODE_ENV}
  APP_ENV(环境名称)=${process.env.APP_ENV}
  PUBLIC_PATH(应用部署的基础路径)=${process.env.PUBLIC_PATH}
  USE_EXTERNAL_RESOURCES(是否引用外部资源)=${process.env.USE_EXTERNAL_RESOURCES}
  USE_SOURCEMAP(是否启用代码映射source-map)=${process.env.USE_SOURCEMAP}
  PROXY_TARGET_URL(代理目标地址)=${process.env.PROXY_TARGET_URL}
  `)

  let externals = {}
  let cdnScripts = []
  if (process.env.USE_EXTERNAL_RESOURCES === "true") {
    externals = {
      react: "React",
      "react-dom": "ReactDOM",
    }
    cdnScripts = [
      // 配置cdn资源
      "https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/react/17.0.2/umd/react.development.js",
      "https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/react-dom/17.0.2/umd/react-dom.development.js",
    ]
  }

  let plugins = [
    new webpack.ProvidePlugin({
      React: "react",
    }),
    new htmlWebpackPlugin({
      filename: "index.html",
      template: path.join(__dirname, "src/index.ejs"),
      hash: true, // true | false。如果是true，会给所有包含的script和css添加一个唯一的webpack编译hash值。这对于缓存清除非常有用。
      inject: true, // | 'head' | 'body' | false  ,注入所有的资源到特定的 template 或者 templateContent 中，如果设置为 true 或者 body，所有的 javascript 资源将被放置到 body 元素的底部，'head' 将放置到 head 元素中。
      chunks: ["vendor", "app"], // 使用chunks 需要指定entry 入口文件中的哪一个模块
      cdn: {
        js: cdnScripts,
      },
      minify: {
        collapseWhitespace: true, //开启html压缩
      },
    }),
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, "./src/theme"),
        to: "theme",
        ignore: [".*"],
      },
      {
        from: path.resolve(__dirname, "./src/WebDemo"),
        to: "WebDemo",
        ignore: [".*"],
      },
      {
        from: path.resolve(__dirname, "./src/ImgCompress"),
        to: "ImgCompress",
        ignore: [".*"],
      },
      {
        from: path.resolve(__dirname, "./src/audio"),
        to: "audio",
        ignore: [".*"],
      },
    ]),
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
    new DotenvWebpackPlugin(dotenvConfig),
    new webpack.HotModuleReplacementPlugin(),
  ]

  if (process.env.NODE_ENV === "production") {
    plugins.push(
      new BundleAnalyzerPlugin({
        analyzerMode: "static",
      })
    )
    plugins.push(new webpackHashManifestPlugin())
  }

  return {
    entry: {
      app: ["@babel/polyfill", "./src/app.jsx"],
    },

    optimization: {
      splitChunks: {
        chunks: "all", // 分割所有 chunks
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: "vendor",
            chunks: "all",
          },
          default: {
            minChunks: 2, // 模块至少被不同的 chunk 引用两次才会被拆分
            priority: -20, // 默认缓存组优先级较低
            reuseExistingChunk: true, // 如果一个模块已经被打包过了，则重用
          },
        },
      },
    },

    output: {
      path: path.join(__dirname, "dist"),
      filename: "assets/[name].js",
      publicPath: process.env.PUBLIC_PATH,
      chunkFilename: "assets/[name].js?[chunkhash:10]",
    },

    devtool:
      process.env.USE_SOURCEMAP === "true"
        ? "eval-cheap-module-source-map"
        : "none",

    devServer: {
      hot: true,
      open: process.env.NODE_ENV === "development",
      inline: true,
      contentBase: "src/",
      historyApiFallback: {
        index: process.env.PUBLIC_PATH,
      },
      disableHostCheck: true,
      publicPath: process.env.PUBLIC_PATH,
      proxy: {
        "/std/wibot/webapi": {
          target: process.env.PROXY_TARGET_URL,
          changeOrigin: true,
          pathRewrite: {
            "^/std/wibot/webapi": "",
          },
        },
      },
    },

    resolve: {
      modules: ["node_modules", path.join(__dirname, "../node_modules")],
      extensions: [".web.js", ".mjs", ".js", ".json", ".web.jsx", ".jsx"],
      alias: {
        "@": `${srcPath}`,
        actions: `${srcPath}/actions/`,
        assets: `${srcPath}/assets/`,
        audio: `${srcPath}/audio/`,
        common: `${srcPath}/common/`,
        components: `${srcPath}/components/`,
        config: `${srcPath}/config/`,
        HOC: `${srcPath}/HOC/`,
        images: `${srcPath}/images/`,
        stores: `${srcPath}/stores/`,
        style: `${srcPath}/style/`,
        theme: `${srcPath}/theme/`,
        "@wecom": `${srcPath}/view/wecom`,
        "@pharmacy": `${srcPath}/view/pharmacy`,
        "@comworkbench": `${srcPath}/view/comworkbench`,
      },
    },

    module: {
      strictExportPresence: true,
      rules: [
        {
          oneOf: [
            {
              test: /\.(js|jsx)$/,
              include: path.resolve(__dirname, "src"), // 仅处理 src 目录下的文件
              use: [
                // 将 thread-loader 放在数组第一个位置
                "thread-loader",
                // 其他 loader，例如 babel-loader
                {
                  loader: "babel-loader",
                  options: {
                    presets: [
                      "@babel/preset-env", // 用于将 ES6+ 代码转换为 ES5
                      "@babel/preset-react", // 用于支持 React JSX
                    ],
                    plugins: [
                      // 根据需要添加 Babel 插件
                      ["@babel/plugin-proposal-decorators", { legacy: true }],
                      "@babel/plugin-proposal-class-properties",
                      "@babel/plugin-proposal-optional-chaining",
                      "@babel/plugin-syntax-dynamic-import",
                      "@babel/plugin-proposal-export-default-from",
                      "@babel/plugin-proposal-export-namespace-from",
                      "@babel/plugin-proposal-nullish-coalescing-operator",
                      "@babel/plugin-transform-runtime",
                    ],
                    cacheDirectory: true,
                  },
                },
              ],
            },
            {
              test: /\.less$/,
              use: [
                "thread-loader",
                "style-loader",
                "css-loader",
                {
                  loader: "postcss-loader",
                  options: {
                    // Necessary for external CSS imports to work
                    // https://github.com/facebookincubator/create-react-app/issues/2677
                    ident: "postcss",
                    plugins: () => [
                      require("postcss-flexbugs-fixes"),
                      autoprefixer({
                        browsers: [
                          ">1%",
                          "last 4 versions",
                          "Firefox ESR",
                          "not ie < 9", // React doesn't support IE8 anyway
                        ],
                        flexbox: "no-2009",
                      }),
                    ],
                  },
                },
                {
                  loader: "less-loader",
                  options: {
                    modifyVars: {},
                    javascriptEnabled: true,
                  },
                },
              ],
            },
            {
              test: /\.(jpg|png)$/,
              use: [
                "thread-loader",
                {
                  loader: "url-loader?limit=8192",
                  options: {
                    outputPath: "images/",
                  },
                },
              ],
            },
            {
              test: /\.css$/,
              use: [
                "thread-loader",
                "style-loader",
                {
                  loader: "css-loader",
                  options: {
                    modules: true,
                    importLoaders: 1,
                    localIdentName: "[local]___[hash:base64:5]",
                  },
                },
                {
                  loader: "postcss-loader",
                  options: {
                    ident: "postcss",
                    plugins: () => [
                      require("postcss-flexbugs-fixes"),
                      autoprefixer({
                        browsers: [
                          ">1%",
                          "last 4 versions",
                          "Firefox ESR",
                          "not ie < 9", // React doesn't support IE8 anyway
                        ],
                        flexbox: "no-2009",
                      }),
                    ],
                  },
                },
              ],
            },
            {
              test: /\.(woff(2)?|ttf|eot|svg)(\?v=\d+\.\d+\.\d+)?$/,
              use: [
                {
                  loader: "file-loader",
                  options: {
                    name: "[name].[ext]",
                    outputPath: "fonts/",
                  },
                },
              ],
            },
            {
              test: /\.md$/,
              use: [
                {
                  loader: "raw-loader",
                },
              ],
            },
          ],
        },
        // ** STOP ** Are you adding a new loader?
        // Make sure to add the new loader(s) before the "file" loader.
      ],
    },

    externals: externals,

    plugins: plugins,

    node: {
      fs: "empty",
    },
  }
}
